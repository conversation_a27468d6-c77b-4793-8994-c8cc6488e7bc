import { ApiService } from './api.service';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ChartService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  postChartPercolation(params: any, queryParams: any = {}) {
    const url = `/charts/percolation-instruments`;
    return this.api.post<any>(url, params, queryParams, 'client', true);
  }

  getAbsoluteVariation(params: any) {
    const url = `/charts/absolute-variation`;
    return this.api.get<any>(url, params, false, 'client');
  }

  getChartInc(id: string, params: any) {
    const url = `/charts/inclinometers/${id}`;
    return this.api.get<any>(url, params, false, 'client');
  }

  getChartSettlementGauge(id: string, params: any) {
    const url = `/charts/settlement-gauge/${id}`;
    return this.api.get<any>(url, params, false, 'client');
  }

  getChartSurfaceLandmarkOrPrism(id: string, params: any) {
    const url = `/charts/surface-landmark-or-prism/${id}`;
    return this.api.get<any>(url, params, false, 'client');
  }

  getChartRl(params: any) {
    const url = `/charts/linimetric-ruler`;
    return this.api.get<any>(url, params, false, 'client');
  }

  getChartPluv(id: string, params: any) {
    const url = `/charts/climate-instruments/${id}`;
    return this.api.get<any>(url, params, false, 'client');
  }

  getChartStabilityAnalysis(params: any) {
    const url = `/charts/stability-analysis`;
    return this.api.get<any>(url, params, false, 'client');
  }
}
