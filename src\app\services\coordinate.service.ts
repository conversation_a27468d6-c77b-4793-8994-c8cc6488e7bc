import { Injectable } from '@angular/core';

import { CoordinateConversionsService } from './api/coordinateConversions';

import { Observable, of, Subject } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CoordinateService {
  coordinateSubject: Subject<any> = new Subject();

  constructor(private coordinateConversionsService: CoordinateConversionsService) {}

  coordinatesConversion(data, type: string = ''): Observable<any> {
    type = type !== '' ? type + '_' : '';
    const datum = data.get('datum').value;

    if (data.get(type + 'coordinate_format').value == 2) {
      //to-Geodetic
      const zone_letter = data.get(type + 'zone_letter').value;
      const zone_number = data.get(type + 'zone_number').value;
      const northing = data.get(type + 'northing').value;
      const easting = data.get(type + 'easting').value;

      if (
        (zone_letter || zone_letter === 0) &&
        (zone_number || zone_number === 0) &&
        (northing || northing === 0) &&
        (easting || easting === 0) &&
        (datum || datum === 0)
      ) {
        const params: any = {
          utm: {
            zone_number: zone_number,
            zone_letter: zone_letter,
            northing: northing,
            easting: easting
          },
          datum: datum
        };
        return this.coordinateConversionsService.postCoordinateGeodetic(params).pipe(
          map((res: any) => {
            return {
              type: 'Geodetic',
              latitude: res.latitude,
              longitude: res.longitude
            };
          }),
          catchError((error) => {
            return of({
              hasError: true,
              error: error
            });
          })
        );
      }
    } else {
      //to-UTM
      const latitude = data.get(type + 'latitude').value;
      const longitude = data.get(type + 'longitude').value;

      if ((latitude || latitude === 0) && (longitude || longitude === 0) && (datum || datum === 0)) {
        const params: any = {
          decimal_geodetic: {
            latitude: latitude,
            longitude: longitude
          },
          datum: datum
        };
        return this.coordinateConversionsService.postCoordinateUTM(params).pipe(
          map((res: any) => {
            return {
              type: 'UTM',
              zone_letter: res.zone_letter,
              zone_number: res.zone_number,
              northing: res.northing,
              easting: res.easting
            };
          }),
          catchError((error) => {
            return of({
              hasError: true,
              error: error
            });
          })
        );
      }
    }
    return of(null);
  }

  convertCoordinateToSirgas(coordinateSetting): Observable<any> {
    if (coordinateSetting.datum !== 2) {
      let params = {
        decimal_geodetic: {
          latitude: coordinateSetting.coordinate_systems.decimal_geodetic.latitude,
          longitude: coordinateSetting.coordinate_systems.decimal_geodetic.longitude
        },
        input_datum: coordinateSetting.datum,
        output_datum: 2 //Sirgas2000
      };
      return this.coordinateConversionsService.postCoordinateDatum(params).pipe(
        map((res: any) => {
          return res;
        }),
        catchError((error) => {
          return of({
            hasError: true,
            error: error
          });
        })
      );
    }
    return of({
      latitude: coordinateSetting.coordinate_systems.decimal_geodetic.latitude,
      longitude: coordinateSetting.coordinate_systems.decimal_geodetic.longitude
    });
  }
}
