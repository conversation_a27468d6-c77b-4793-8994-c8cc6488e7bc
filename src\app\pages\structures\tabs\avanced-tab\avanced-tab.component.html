<!-- Layout 2 -->
<form [formGroup]="formAvancedTab">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <div class="col-md-6 mt-2">
              <input
                class="form-check-input me-1"
                type="checkbox"
                formControlName="has_auto_update"
                value=""
                checked
                (change)="
                  activeValidate(formAvancedTab.get('has_auto_update').value)
                "
              />
              <label class="form-label"> Atualização Automática </label>
            </div>
            <!-- Protocol -->
            <div class="col-md-6">
              <label class="form-label">Protocolo de transferência</label>
              <div>
                <select class="form-select" formControlName="protocol">
                  <option value="">Selecione...</option>
                  <option
                    *ngFor="let item of transferProtocol"
                    [ngValue]="item.value"
                  >
                    {{ item.protocol }}
                  </option>
                </select>
              </div>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formAvancedTab.get('protocol').valid &&
                  formAvancedTab.get('protocol').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
          </div>

          <div class="row mt-2">
            <!-- Last data fetch -->
            <div class="col-md-6">
              <label class="form-label">Última atualização de leituras</label>
              <input
                type="datetime-local"
                class="form-control"
                formControlName="last_data_fetch"
              />
            </div>
            <!-- Last generated package -->
            <div class="col-md-6">
              <label class="form-label">Último pacote gerado</label>
              <input
                type="datetime-local"
                class="form-control"
                formControlName="last_generated_package"
              />
            </div>
          </div>

          <div class="row mt-2">
            <!-- Frequency to fetch data -->
            <div class="col-md-6">
              <label class="form-label"
                >Frequência automática de leituras</label
              >
              <input
                type="time"
                min="00:15"
                class="form-control"
                formControlName="frequency_to_fetch_data"
                step="1"
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formAvancedTab.get('frequency_to_fetch_data').valid &&
                  formAvancedTab.get('frequency_to_fetch_data').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
            <!-- Frequency to generate packages -->
            <div class="col-md-6">
              <label class="form-label"
                >Frequência automática geração de pacotes</label
              >
              <input
                type="time"
                min="00:15"
                class="form-control"
                formControlName="frequency_to_generate_packages"
                step="1"
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formAvancedTab.get('frequency_to_generate_packages').valid &&
                  formAvancedTab.get('frequency_to_generate_packages').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
