<form [formGroup]="formChart">
  <div class="row g-3 mt-1">
    <!-- Instrumento -->
    <div class="col-md-3">
      <label class="form-label">Instrumento:</label>
      <ng-multiselect-dropdown
        [settings]="instrumentsSettings"
        [data]="instruments"
        formControlName="instrument"
        (onSelect)="changeInstrument($event, 'select')"
        (onDeSelect)="changeInstrument($event, 'deselect')"
        [placeholder]="'Selecione...'"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Data e hora inicial -->
    <div class="col-md-3">
      <label class="form-label">Data e hora inicial:</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="start_date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('start_date').valid &&
          formChart.get('start_date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Data e hora final -->
    <div class="col-md-3">
      <label class="form-label">Data e hora final:</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="end_date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('end_date').valid && formChart.get('end_date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Eixo Y -->
    <div class="col-md-3">
      <label class="form-label">Eixo Y:</label>
      <select
        class="form-select"
        formControlName="info_yAxis"
        (change)="changeYAxis()"
      >
        <option value="">Selecione...</option>
        <option *ngFor="let item of infoYAxis" [ngValue]="item.id">
          {{ item.name }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('info_yAxis').valid &&
          formChart.get('info_yAxis').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>
  <!-- Grafico -->
  <div class="row mt-3" *ngIf="formChart.valid">
    <div class="col-md-4">
      <app-button
        [class]="'btn-logisoil-green me-2 text-nowrap'"
        [customBtn]="true"
        [icon]="'fa fa-line-chart'"
        [label]="'Gerar gráfico'"
        (click)="getChart()"
      ></app-button>
    </div>
  </div>
  <!-- Alerta -->
  <div class="row mt-3">
    <div class="col-md-12">
      <div
        class="alert alert-warning"
        role="alert"
        *ngIf="messageReturn.status"
      >
        {{ messageReturn.text }}
      </div>
    </div>
  </div>
  <div class="row mt-2 mb-3" *ngIf="formChart.valid">
    <div class="col-md-2" *ngIf="chart.options">
      <label class="form-label"
        ><label class="form-label"
          >Tamanho: {{ formChart.controls['chart_height'].value }} px</label
        >
        <input
          type="range"
          class="range"
          #chartHeight
          min="300"
          max="1600"
          step="10"
          formControlName="chart_height"
          (input)="setHeight(chartHeight.value)"
        />
      </label>
    </div>
    <div class="col-md-12" *ngIf="chart.options">
      <app-e-charts
        [dataChart]="chart"
        [height]="formChart.controls['chart_height'].value"
      ></app-e-charts>
    </div>
  </div>
  <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [click]="goBack.bind(this)"
    ></app-button>
  </div>
</form>
