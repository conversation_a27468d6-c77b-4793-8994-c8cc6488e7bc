import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { DxfViewer } from 'dxf-viewer';
import * as Three from 'three';
import { DomSanitizer } from '@angular/platform-browser';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-dxf-viewer-v2',
  templateUrl: './dxf-viewer-v2.component.html',
  styleUrls: ['./dxf-viewer-v2.component.scss']
})
export class DxfViewerV2Component implements OnInit, OnChanges {
  @Input() public fileDxf: any = null;
  @Input() public show: boolean = false;
  @Input() public idCanvas: string = 'canvasContainer';

  public layers = null;
  public dxfViewer = null;
  public dropdownOpen = false;

  constructor(private sanitizer: DomSanitizer, private ngxSpinnerService: NgxSpinnerService) {}

  ngOnInit(): void {}

  /**
   * Monitora mudanças nas propriedades do componente.
   * Quando o valor de 'fileDxf' é atualizado e não é nulo,
   * exibe o spinner e processa o arquivo DXF com um pequeno atraso.
   *
   * @param {SimpleChanges} changes - Contém as mudanças nas propriedades observáveis.
   */
  ngOnChanges(changes: SimpleChanges) {
    if (changes.fileDxf.currentValue != null) {
      this.show = true;
      this.ngxSpinnerService.show();

      setTimeout(() => {
        this.ngxSpinnerService.hide();
        this.handleFileInput(this.fileDxf);
      }, 50);
    } else {
      this.show = false;
    }
  }

  /**
   * Alterna a visibilidade de uma camada específica no visualizador DXF com base no evento de clique.
   *
   * @param {any} layer - Objeto que representa a camada a ser exibida ou ocultada.
   * @param {Event} $event - Evento de clique que determina o estado de exibição da camada.
   */
  _ToggleLayer(layer, $event) {
    this.dxfViewer.ShowLayer(layer.name, $event.target.checked);
  }

  /**
   * Converte um valor numérico em uma cor CSS hexadecimal, preenchendo com zeros à esquerda
   * para garantir que o valor tenha 6 caracteres.
   *
   * @param {number} value - O valor numérico a ser convertido em uma cor hexadecimal.
   * @returns {string} Cor em formato hexadecimal.
   */
  _GetCssColor(value) {
    let s = value.toString(16);
    while (s.length < 6) {
      s = '0' + s;
    }
    return '#' + s;
  }

  /**
   * Retorna a classe de ícone apropriada para uma camada com base no nome da camada.
   * Camadas 'external' e 'material' recebem o ícone de polígono, e outras recebem o ícone de quadrado.
   *
   * @param {string} layerName - Nome da camada para a qual a classe de ícone será definida.
   * @returns {string} Classe de ícone Font Awesome apropriada para a camada.
   */
  _GetIconClass(layerName: string): string {
    if (layerName && (layerName.toLowerCase() === 'external' || layerName.toLowerCase() === 'material')) {
      return 'fa-solid fa-draw-polygon';
    }
    return 'fa-solid fa-square';
  }

  /**
   * Processa e exibe o arquivo DXF usando o visualizador DXF.
   * Configura o visualizador com parâmetros personalizados, carrega o arquivo DXF e define as camadas iniciais.
   *
   * @param {File} file - O arquivo DXF a ser exibido no visualizador.
   */
  async handleFileInput(file) {
    const canvasRef = document.getElementById(this.idCanvas);

    const dxfViewer = new DxfViewer(canvasRef, {
      canvasWidth: 500,
      canvasHeight: 500,
      autoResize: true,
      clearColor: new Three.Color('#fff'),
      clearAlpha: 1,
      canvasAlpha: true,
      canvasPremultipliedAlpha: true,
      antialias: true,
      colorCorrection: true,
      blackWhiteInversion: true,
      pointSize: 2,
      sceneOptions: {
        arcTessellationAngle: (10 / 180) * Math.PI,
        minArcTessellationSubdivisions: 8,
        wireframeMesh: false,
        suppressPaperSpace: false,
        textOptions: {
          curveSubdivision: 2,
          fallbackChar: '\uFFFD?'
        }
      },
      retainParsedDxf: true,
      preserveDrawingBuffer: true,
      fileEncoding: 'utf-8'
    });

    await dxfViewer.Load({
      url: URL.createObjectURL(file),
      fonts: ['OpenSans-Regular.ttf', 'Roboto-LightItalic.ttf'],

      progressCbk: (phase, size, total) => {},
      workerFactory: null
    });

    const layersIterable = dxfViewer.GetLayers();
    const layersArray = Array.from(layersIterable);

    const filteredLayers = layersArray.filter((layer) => layer.name !== '0');
    const sortedLayers = filteredLayers.sort((a, b) => {
      const isInteger = (name) => /^\d+$/.test(name);
      const getPriority = (layer) => {
        if (layer.name.startsWith(' ') || isInteger(layer.name)) return 1;
        if (layer.name.toLowerCase() === 'external') return 2;
        if (layer.name.toLowerCase() === 'material') return 3;
        return 4;
      };

      const priorityA = getPriority(a);
      const priorityB = getPriority(b);

      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      if (priorityA === 1 && priorityB === 1) {
        const isANumber = !isNaN(Number(a.name));
        const isBNumber = !isNaN(Number(b.name));

        if (isANumber && isBNumber) {
          return Number(a.name) - Number(b.name);
        } else if (isANumber) {
          return -1;
        } else if (isBNumber) {
          return 1;
        }
      }

      return a.name.localeCompare(b.name);
    });

    this.layers = sortedLayers;
    this.dxfViewer = dxfViewer;

    this.dxfViewer.ShowLayer('Superfície de ruptura', false);
    this.dxfViewer.ShowLayer('Freática', false);
  }

  //Alterna o estado de exibição do dropdown, invertendo o valor atual de `dropdownOpen`
  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }
}
