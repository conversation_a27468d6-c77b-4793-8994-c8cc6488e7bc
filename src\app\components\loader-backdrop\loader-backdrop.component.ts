import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';

@Component({
  selector: 'app-loader-backdrop',
  templateUrl: './loader-backdrop.component.html',
  styleUrls: ['./loader-backdrop.component.scss']
})
export class LoaderBackdropComponent implements OnInit {
  @ViewChild('videoPlayer') videoPlayer: ElementRef<HTMLVideoElement>;

  showLoader: boolean = true;

  constructor() {}

  ngOnInit(): void {
    // Verifica no sessionStorage se o vídeo já foi exibido
    const videoWatched = sessionStorage.getItem('videoWatched');

    if (videoWatched) {
      this.showLoader = false; // Não exibe o vídeo novamente
    }
  }

  ngAfterViewInit(): void {
    if (this.showLoader) {
      const videoElement = this.videoPlayer.nativeElement;

      // Garante que o vídeo seja silenciado para autoplay
      videoElement.muted = true;

      // Tenta iniciar a reprodução
      videoElement.play().catch((err) => {
        console.error('Falha ao reproduzir o vídeo automaticamente:', err);
      });
    }
  }

  onVideoLoaded(): void {}

  onVideoEnded(): void {
    // Salva no sessionStorage que o vídeo foi exibido
    sessionStorage.setItem('videoWatched', 'true');
    this.showLoader = false;
  }
}
