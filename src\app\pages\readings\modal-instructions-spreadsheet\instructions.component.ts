import { Component } from '@angular/core';

@Component({
  selector: 'app-inapz-instructions',
  templateUrl: './inapz-instructions.html'
})
export class InaPzInstructionsComponent {}

@Component({
  selector: 'app-inc-conv-instructions',
  templateUrl: './inc-conv-instructions.html'
})
export class IncConvInstructionsComponent {}

@Component({
  selector: 'app-ipi-instructions',
  templateUrl: './ipi-instructions.html'
})
export class IpiInstructionsComponent {}

@Component({
  selector: 'app-ms-pr-instructions',
  templateUrl: './ms-pr-instructions.html'
})
export class MsPrInstructionsComponent {}

@Component({
  selector: 'app-mr-instructions',
  templateUrl: './mr-instructions.html'
})
export class MrInstructionsComponent {}

@Component({
  selector: 'app-geo-instructions',
  templateUrl: './geo-instructions.html'
})
export class GeoInstructionsComponent {}

@Component({
  selector: 'app-rl-instructions',
  templateUrl: './rl-instructions.html'
})
export class RlInstructionsComponent {}
