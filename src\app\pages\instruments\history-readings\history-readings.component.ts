import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { MessagePadroes } from 'src/app/constants/message.constants';
import { SecurityLevel, Axis } from 'src/app/constants/instruments.constants';

import { UserService } from 'src/app/services/user.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';

import * as moment from 'moment';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-history-readings',
  templateUrl: './history-readings.component.html',
  styleUrls: ['./history-readings.component.scss']
})
export class HistoryReadingsComponent implements OnInit {
  public instrumentId: any = null;

  public profile: any = null;
  public permissaoUsuario: any = null;

  public messageReturn: any = [{ text: '', status: false }];

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public type: number = 0;

  public tableHeader: any = [
    {
      label: 'Data da leitura',
      width: '20%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['reading_value_date']
    },
    {
      label: 'Ponto de Medição',
      width: '20%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['measurement']
    },
    {
      label: 'Deslocamento',
      width: '20%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['displaced_axis']
    },
    {
      label: 'Nível de segurança',
      width: '40%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['security_level']
    },
    {
      label: 'Legenda',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['color'],
      type: 'color'
    }
  ];

  public tableData: any = [];

  constructor(private activatedRoute: ActivatedRoute, private instrumentsServiceApi: InstrumentsServiceApi, private userService: UserService) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.getInstrument(this.activatedRoute.snapshot.params.instrumentId);
  }

  getInstrument(instrumentId: string = '') {
    this.instrumentsServiceApi.getInstrumentsById(instrumentId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.type = dados.type;

      //Instrumentos que possuem ponto de medição
      if (![3, 4, 5, 8].includes(this.type)) {
        let idx = fn.findIndexInArrayofObject(this.tableHeader, 'label', 'Ponto de Medição');
        if (idx > -1 && idx < this.tableHeader.length) {
          this.tableHeader.splice(idx, 1);
        }
      }

      //Instrumentos que possuem deslocamento
      if (![6, 7].includes(this.type)) {
        let idx = fn.findIndexInArrayofObject(this.tableHeader, 'label', 'Deslocamento');
        if (idx > -1 && idx < this.tableHeader.length) {
          this.tableHeader.splice(idx, 1);
        }
      }

      this.getHistoryReadings(instrumentId);
    });
  }

  getHistoryReadings(instrumentId: string) {
    this.messageReturn.text = '';
    this.messageReturn.status = false;

    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    this.instrumentsServiceApi.getSecurityLevelAlerts(instrumentId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados) {
        this.tableData = dados ? dados.data : [];
        this.collectionSize = dados.total_items_count;
        this.formatDataHistory();
      } else {
        this.tableData = [];
        this.collectionSize = 0;
        this.messageReturn.text = MessagePadroes.NoAlert;
        this.messageReturn.status = true;
      }
    });
  }

  formatDataHistory() {
    this.tableData = this.tableData.map((item: any) => {
      let securityLevel = fn.findIndexInArrayofObject(SecurityLevel, 'value', item.security_level, 'value', true);

      let displaced_axis = { label: '' };
      if ([6, 7].includes(this.type)) {
        displaced_axis = fn.findIndexInArrayofObject(Axis, 'value', item.displaced_axis, 'value', true);
      }

      let itemData = {
        reading_value_date: moment(item.reading_value_date).format('DD/MM/YYYY HH:mm:ss'),
        measurement: item.measurement != null ? item.measurement : '',
        displaced_axis: displaced_axis.label,
        security_level: securityLevel.label,
        color: securityLevel.color
      };
      return itemData;
    });
  }

  // Metodo que recebe a pagina selecionada
  loadPage(selectPage: number): void {
    this.page = selectPage;
    this.getHistoryReadings(this.activatedRoute.snapshot.params.instrumentId);
  }
}
