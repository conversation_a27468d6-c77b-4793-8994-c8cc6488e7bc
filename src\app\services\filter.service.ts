import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class FilterService {
  private filter: any = { filters: {}, filtersHierarchy: {}, filtersBody: {} };
  private previousRoute: any = '';

  constructor(private router: Router) {}

  setFilters(filters: any, filtersHierarchy: any = {}, filtersBody: any = {}) {
    this.previousRoute = this.router.url;
    this.filter = { filters: filters, filtersHierarchy: filtersHierarchy, filtersBody: filtersBody };
  }

  getFilters() {
    if (this.previousRoute != this.router.url) {
      this.previousRoute = this.router.url;
      this.filter = { filters: {}, filtersHierarchy: {}, filtersBody: {} };
    }

    return this.filter;
  }

  resetFilters() {
    this.filter = { filters: {}, filtersHierarchy: {}, filtersBody: {} };
    this.previousRoute = '';
  }
}
