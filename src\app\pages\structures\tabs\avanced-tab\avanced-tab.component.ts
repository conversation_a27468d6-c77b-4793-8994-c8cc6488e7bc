import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Component, Input, OnInit, ViewChild } from '@angular/core';

import { transferProtocol as transferProtocolEnum } from 'src/app/constants/structure.constants';
import { accessLevel as accessLevelPermission } from 'src/app/constants/permissions.constants';

import { DataService } from 'src/app/services/data.service';

import * as moment from 'moment';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-avanced-tab',
  templateUrl: './avanced-tab.component.html',
  styleUrls: ['./avanced-tab.component.scss']
})
export class AvancedTabComponent implements OnInit {
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  public formAvancedTab: FormGroup = new FormGroup({
    has_auto_update: new FormControl(false),
    protocol: new FormControl('', [Validators.required]),
    last_data_fetch: new FormControl(''), //Campo bloqueado
    last_generated_package: new FormControl(''), //Campo bloqueado
    frequency_to_fetch_data: new FormControl(null, [Validators.required]),
    frequency_to_generate_packages: new FormControl(null, [Validators.required])
  });

  public transferProtocol: any = transferProtocolEnum;
  public func = fn;

  constructor(private dataService: DataService) {}

  /**
   * Lifecycle hook que é executado após a criação do componente.
   * Inicia a validação de acesso com base no perfil do usuário.
   */
  ngOnInit(): void {
    this.validateAccess();
  }

  /**
   * Valida as permissões de acesso ao formulário com base no perfil do usuário.
   * Desabilita ou habilita campos conforme o nível de permissão e modo de visualização.
   *
   * @param role - (Opcional) Nível de permissão a ser validado. Padrão é 0.
   */
  validateAccess(role: number = 0): any {
    if (this.profile.description !== accessLevelPermission.SuperSuporte && this.profile.description !== accessLevelPermission.Suporte) {
      this.formAvancedTab.disable();
      this.formAvancedTab.get('frequency_to_fetch_data').enable();
      this.formAvancedTab.get('frequency_to_generate_packages').enable();
    }
    if (this.view) {
      this.formAvancedTab.disable();
    }
    this.formAvancedTab.get('last_data_fetch').disable();
    this.formAvancedTab.get('last_generated_package').disable();
    this.activeValidate(false);
  }

  /**
   * Popula o formulário `formAvancedTab` com os dados recebidos.
   * Formata campos de data para o formato ISO com hora fixa `23:59:59`.
   *
   * @param dataTab - Objeto com os dados que serão atribuídos ao formulário.
   */
  setData(dataTab: any = []) {
    for (var index in dataTab) {
      switch (index) {
        case 'last_data_fetch':
        case 'last_generated_package':
          this.formAvancedTab.get(index).setValue(moment(dataTab[index]).format('YYYY-MM-DDT23:59:59'));
          break;
        default:
          this.formAvancedTab.get(index).setValue(dataTab[index]);
          break;
      }
    }
  }

  /**
   * Recupera os valores atuais dos controles do formulário `formAvancedTab`.
   *
   * @returns Um objeto contendo chave e valor de todos os controles do formulário.
   */
  getData() {
    return Object.entries(this.formAvancedTab.controls).reduce((dataTab, [key, control]) => {
      dataTab[key] = control.value;
      return dataTab;
    }, {});
  }

  /**
   * Ativa ou desativa as validações nos campos obrigatórios do formulário.
   *
   * @param active - Se `true`, define os validadores `required`. Se `false`, remove os validadores e limpa erros.
   */
  activeValidate(active: any) {
    if (active) {
      this.formAvancedTab.get('protocol').setValidators([Validators.required]);
      this.formAvancedTab.get('frequency_to_fetch_data').setValidators([Validators.required]);
      this.formAvancedTab.get('frequency_to_generate_packages').setValidators([Validators.required]);

      this.formAvancedTab.get('protocol').updateValueAndValidity();
      this.formAvancedTab.get('frequency_to_fetch_data').updateValueAndValidity();
      this.formAvancedTab.get('frequency_to_generate_packages').updateValueAndValidity();
    } else {
      this.formAvancedTab.controls['protocol'].setErrors(null);
      this.formAvancedTab.controls['protocol'].clearValidators();

      this.formAvancedTab.controls['frequency_to_fetch_data'].setErrors(null);
      this.formAvancedTab.controls['frequency_to_fetch_data'].clearValidators();

      this.formAvancedTab.controls['frequency_to_generate_packages'].setErrors(null);
      this.formAvancedTab.controls['frequency_to_generate_packages'].clearValidators();
    }
  }
}
