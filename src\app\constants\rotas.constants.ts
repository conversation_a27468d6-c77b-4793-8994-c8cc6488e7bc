import { environment } from 'src/environments/environment';

enum Rotas {
  Administrador = '/administrator',
  Notificações = '/alerts',
  Auth = 'auth',
  Atividades = '/activities',
  CadastrarCliente = 'create',
  CadastrarEstrutura = 'create',
  CadastrarInstrumento = 'create',
  CadastrarLeitura = 'create',
  CadastrarMaterial = 'create',
  CadastrarSecao = 'create',
  CadastrarUnidade = 'create',
  CadastrarUsuario = 'create',
  Clientes = '/clients',
  ConsultarSecoes = ':instrumentId/consult',
  CriarAgendamento = 'createNewSchedule',
  CriarSimulador = 'simulations/createSimulation',
  Dashboard = '/dashboard',
  DownloadDXF = '/stabilityAnalysisId/downloadDXF',
  DownloadPNG = '/stabilityAnalysisId/downloadPNG',
  DownloadZIP = '/stabilityAnalysisId/downloadZIP',
  EditarAgendamentoDeRelatorio = ':reportId/edit',
  EditarCliente = ':clientId/edit',
  EditarEstrutura = ':structureId/edit',
  EditarInstrumento = ':instrumentId/edit',
  EditarLeitura = ':readingId/edit',
  EditarMaterial = ':staticMaterialId/edit',
  EditarPlanoDeAcao = 'action-plan/:actionPlanId/edit',
  EditarSecao = ':sectionId/edit',
  EditarSimulacao = 'simulations/:simulationId/editSimulation',
  EditarUnidade = ':unitId/edit',
  EditarUsuario = ':userId/edit',
  EmitirRelatório = 'sendReport',
  Estabilidade = '/stability',
  Estruturas = '/structures',
  FichaFIE = 'inspection-sheet/fie',
  FichaEoR = 'inspection-sheet/eor',
  FichaFIR = 'inspection-sheet/fir',
  FichaRISR = 'inspection-sheet/risr',
  FichaEmBranco = 'inspection-sheet/embranco',
  GraficoInstrumento = ':instrumentId/chart',
  HistoricoAtividades = '/history',
  HistoricoEstrutura = ':structureId/history',
  HistoricoInstrumentacao = ':instrumentId/history',
  HistoricoLeituras = ':readingId/history',
  HistoricoMaterial = ':staticMaterialId/history',
  HistoricoNotificacoes = '/notificationsHistory',
  HistoricoSecao = ':sectionId/history',
  Home = '/',
  Imagens = '/galleryImages',
  ImagensEstrutura = ':structureId/images',
  Inspecoes = '/inspections',
  Instrumentacao = '/instruments',
  Leituras = '/readings',
  Materiais = '/materials',
  MapaInstrumentacao = ':instrumentId/map',
  PlanoDeAcao = 'action-plan/newActionPlan',
  Parametros = '/parameters',
  Profile = '/profile',
  Relatorios = '/reports',
  ResultadoSimulacao = 'simulations/:simulationId/resultSimulation',
  Secoes = '/sections',
  SolicitarRevisao = '/revisions',
  Unidades = '/units',
  Usuarios = '/users',
  VisualizarUnidade = ':unitId/view',
  VisualizarEstrutura = ':structureId/view',
  VisualizarInstrumento = ':instrumentId/view',
  VisualizarLeitura = ':readingId/view',
  VisualizarMaterial = ':staticMaterialId/view',
  VisualizarSecao = ':sectionId/view',
  VisualizarUsuario = ':userId/view',
  VisualizarFicha = 'inspection-sheet/:inspectionSheetId/view',
  VisualizarOcorrencia = 'inspection-sheet/occurrence/:occurrenceId/view',
  VisualizarPlanoDeAcao = 'action-plan/:actionPlanId/view',
  config = 'config', // Não remover
  //Minidashboard Instrumentos
  AdicionarInstrumento = 'addInstrument',
  EdicaoInstrumentoMassa = 'editingBatch',
  GruposInstrumentos = 'groupInstruments',
  GraficarDados = 'graphData',
  BaixarInformacoes = 'downloadInformations',
  CadastroInstrumentoPlanilha = 'insertBySpreadsheet',
  Mapa = 'viewMap',
  //Minidashboard Estabilidade
  ImagensInstrumentacao = ':instrumentId/images',
  GraficosEstabilidade = 'stabilityCharts',
  FatoresDeSeguranca = 'stabilityAnalysis',
  MapaEstabilidade = 'stabilityMap',
  Simulador = 'simulations'
}

enum Telas {
  Administrador = 'Administrador',
  Notificações = 'Notificações',
  Atividades = 'Atividades',
  Clientes = 'Clientes',
  Dashboard = 'Dashboard',
  Documentacao = 'Documentação',
  Estabilidade = 'Estabilidade',
  Estruturas = 'Estruturas',
  HistoricoAtividades = 'Histórico de Atividades',
  Home = 'Home',
  Imagens = 'Imagens',
  Inspecoes = 'Inspeções',
  Instrumentacao = 'Instrumentação',
  Investigacoes = 'Investigação',
  Leituras = 'Leituras',
  Materiais = 'Materiais',
  Parametros = 'Parâmetros',
  Profile = 'Perfil ',
  Relatorios = 'Relatórios',
  Secoes = 'Seções',
  SolicitarRevisao = 'Solicitar Revisão',
  Unidades = 'Unidades',
  Usuarios = 'Usuários',
  HistoricoNotificacoes = 'Histórico de Notificações',
  //Minidashboard
  Imagem = 'Imagem',
  Secao = 'Seção',
  Tabela = 'Tabela',
  //Minidashboard Instrumentos
  AdicionarInstrumento = 'Adicionar Instrumento',
  GruposInstrumentos = 'Grupo de Instrumentos',
  EdicaoInstrumentoMassa = 'Edição Instrumentos em Massa',
  GraficarDados = 'Graficar Dados',
  BaixarInformacoes = 'Baixar Informações',
  CadastroInstrumentoPlanilha = 'Cadastro de Instrumento via Planilha',
  MapaInstrumentacao = 'Mapa',
  //Minidashboard Estabilidade
  GraficosEstabilidade = 'Gráficos',
  FatoresDeSeguranca = 'Listagem',
  MapaEstabilidade = 'Mapa',
  Pacotes = 'Pacotes',
  Simulador = 'Simulador'
}

enum Icones {
  Administrador = 'fa fa-solid fa-user-gear',
  Notificações = 'alertas.svg',
  Atividades = 'atividades.svg',
  Clientes = 'fa fa-icon fas fa-building',
  Compartilhar = 'fa fa-share-alt',
  CriarAgendamento = 'fa fa-calendar-plus-o',
  Dashboard = 'dashboard.svg',
  DownloadDXF = 'fa fa-file',
  DownloadPNG = 'fa fa-file-image-o',
  DownloadZIP = 'fa fa-file-archive-o',
  Documentacao = 'documentacao.svg',
  EmitirRelatório = 'fa fa-file-text-o',
  Estabilidade = 'estabilidade.svg',
  Estruturas = 'estruturas.svg',
  FixarSimulacao = 'fa fa-thumb-tack',
  Historico = 'fa fa-history',
  HistoricoNotificacoes = 'alertas.svg',
  Home = 'home.svg',
  Imagens = 'imagens.svg',
  Inspecoes = 'inspecoes.svg',
  Instrumentacao = 'instrumentacao.svg',
  Investigacoes = 'investigacoes.svg',
  Leituras = 'leituras.svg',
  MapaLocalizacao = 'fa fa-map-marker',
  Materiais = 'materiais.svg',
  Parametros = 'parametros.svg',
  Profile = 'fa fa-icon fas fa-id-card',
  Relatorios = 'relatorios.svg',
  Renomear = 'fa fa-pencil-square-o',
  Secoes = 'secoes.svg',
  SolicitarRevisao = 'solicitar_revisao.svg',
  Unidades = 'unidades.svg',
  Usuarios = 'fa fa-icon fas fa-users',
  ValidarPlanoDeAcao = 'fa fa-check',
  //Acoes
  Create = 'fa fa-plus-circle',
  Edit = 'fa fa-pencil-square',
  Delete = 'fa fa-trash', //Minidashboard Tela de materias
  View = 'fa fa-eye',
  //Minidashboard
  Grafico = 'fa fa-area-chart',
  Imagem = 'fa fa-picture-o',
  Mapa = 'fas fa-map-marked',
  Secao = 'secoes.svg',
  Tabela = 'fa fa-table',
  //Menu profile
  MeuCadastro = 'fa fa-id-card',
  Empresa = 'fas fa-building',
  TermoServico = 'fas fa-file-text-o',
  //Minidashboard Instrumentos
  InserirLeituras = 'fa fa-thin fa-clipboard-list',
  AdicionarInstrumento = 'adicionar-instrumentos.svg',
  GruposInstrumentos = 'fa fa-thin fa-object-group',
  EdicaoInstrumentoMassa = 'fa fa-pencil-square-o',
  GraficarDados = 'graficar-dados.svg',
  BaixarInformacoes = 'fa fa-download',
  CadastroInstrumentoPlanilha = 'fa fa-file-excel-o',
  MapaInstrumentacao = 'fa fa-map-marker',
  //Minidashboard Estabilidade
  Pacotes = 'box-seam.svg',
  Simulador = 'fa fa-calculator',
  GraficosEstabilidade = 'fa fa-area-chart',
  MapaEstabilidade = 'fas fa-map-marked',
  FatoresDeSeguranca = 'dashboard.svg'
}

const Acoes = {
  create: {
    titulo: 'Cadastro',
    icone: Icones.Create
  },
  edit: {
    titulo: 'Edição',
    icone: Icones.Edit
  },
  view: {
    titulo: 'Visualização',
    icone: Icones.View
  },
  delete: {
    titulo: 'Excluir Material', //Tela Materiais
    icone: Icones.Delete
  },
  editingBatch: {
    titulo: 'Edição Instrumentos em massa',
    icone: Icones.EdicaoInstrumentoMassa
  },
  groupInstruments: {
    titulo: 'Grupo de Instrumentos',
    icone: Icones.GruposInstrumentos
  },
  history: {
    titulo: 'Histórico',
    icone: Icones.Historico
  },
  insertReadings: {
    titulo: 'Inserir Leituras',
    icone: Icones.InserirLeituras
  },
  map: {
    titulo: 'Mapa',
    icone: Icones.MapaInstrumentacao
  },
  insertBySpreadsheet: {
    titulo: 'Cadastro de instrumento via planilha',
    icone: Icones.CadastroInstrumentoPlanilha
  },
  consult: {
    titulo: 'Consultar Seções',
    icone: Icones.Secoes
  },
  chart: {
    titulo: 'Gráfico',
    icone: Icones.Grafico
  },
  images: {
    titulo: 'Imagens',
    icone: Icones.Imagens
  },
  stabilityMap: {
    titulo: 'Mapa',
    icone: Icones.Mapa
  },
  viewMap: {
    titulo: 'Mapa',
    icone: Icones.MapaInstrumentacao
  },
  sendReport: {
    titulo: 'Emitir Relatório',
    icone: Icones.EmitirRelatório
  },
  createNewSchedule: {
    titulo: 'Criar Agendamento',
    icone: Icones.CriarAgendamento
  },
  stabilityAnalysis: {
    titulo: 'Listagem',
    icone: Icones.FatoresDeSeguranca
  },
  stabilityImages: {
    titulo: 'Imagens',
    icone: Icones.Imagens
  },
  stabilityCharts: {
    titulo: 'Gráficos',
    icone: Icones.GraficosEstabilidade
  },
  downloadDXF: {
    titulo: 'Download DXF',
    icone: Icones.DownloadDXF
  },
  downloadPNG: {
    titulo: 'Download PNG',
    icone: Icones.DownloadPNG
  },
  downloadZIP: {
    titulo: 'Download .zip',
    icone: Icones.DownloadZIP
  },
  simulations: {
    titulo: 'Simulador',
    icone: Icones.Simulador
  },
  createSimulation: {
    titulo: 'Simulador',
    icone: 'fa fa-calculator',
    subTitulos: [{ titulo: 'Criar simulação', icone: Icones.Create, routerLink: '/stability/simulations/createSimulation' }]
  },
  resultSimulation: {
    titulo: 'Simulador',
    icone: 'fa fa-calculator',
    subTitulos: [{ titulo: 'Resultados da simulação', icone: Icones.Simulador, routerLink: '/stability/simulations/:simulationId/resultSimulation' }]
  },
  historySection: {
    titulo: 'Histórico',
    icone: Icones.Historico
  },
  historyMaterials: {
    titulo: 'Histórico',
    icone: Icones.Historico
  },
  fie: {
    titulo: 'Ficha de inspeção - FIE',
    icone: ''
  },
  eor: {
    titulo: 'Ficha de inspeção - EoR',
    icone: ''
  },
  risr: {
    titulo: 'Ficha de inspeção de Segurança Regular - RISR',
    icone: ''
  },
  fir: {
    titulo: 'Ficha de inspeção - FIR',
    icone: ''
  },
  embranco: {
    titulo: 'Ficha em branco',
    icone: ''
  },
  newActionPlan: {
    titulo: 'Planos de Ação',
    icone: 'fa fa-tasks',
    subTitulos: [{ titulo: 'Novo Plano de Ação', icone: Icones.Create, routerLink: '/inspections/action-plan/newActionPlan' }]
  }
};

const MenuPrincipal = [
  {
    Titulo: Telas.Home,
    Rota: Rotas.Home,
    Ativo: environment.MenuAtivo.Home,
    Icone: Icones.Home,
    Show: true
  },
  {
    Titulo: Telas.Dashboard,
    Rota: Rotas.Dashboard,
    Ativo: environment.MenuAtivo.Dashboard,
    Icone: Icones.Dashboard,
    Show: true
  },
  {
    Titulo: Telas.Secoes,
    Rota: Rotas.Secoes,
    Ativo: environment.MenuAtivo.Secoes,
    Icone: Icones.Secoes,
    Show: true
  },
  {
    Titulo: Telas.Materiais,
    Rota: Rotas.Materiais,
    Ativo: environment.MenuAtivo.Materiais,
    Icone: Icones.Materiais,
    Show: true
  },
  {
    Titulo: Telas.Instrumentacao,
    Rota: Rotas.Instrumentacao,
    Ativo: environment.MenuAtivo.Instrumentacao,
    Icone: Icones.Instrumentacao,
    Show: true
  },
  {
    Titulo: Telas.Leituras,
    Rota: Rotas.Leituras,
    Ativo: environment.MenuAtivo.Leituras,
    Icone: Icones.Leituras,
    Show: true
  },
  {
    Titulo: Telas.Estabilidade,
    Rota: Rotas.Estabilidade,
    Ativo: environment.MenuAtivo.Estabilidade,
    Icone: Icones.Estabilidade,
    Show: true
  },
  {
    Titulo: Telas.Inspecoes,
    Rota: Rotas.Inspecoes,
    Ativo: environment.MenuAtivo.Inspecoes,
    Icone: Icones.Inspecoes,
    Show: true
  },
  {
    Titulo: Telas.Imagens,
    Rota: Rotas.Imagens,
    Ativo: environment.MenuAtivo.Imagens,
    Icone: Icones.Imagens,
    Show: true
  },
  {
    Titulo: Telas.Relatorios,
    Rota: Rotas.Relatorios,
    Ativo: environment.MenuAtivo.Relatorios,
    Icone: Icones.Relatorios,
    Show: true
  },
  {
    Titulo: Telas.Notificações,
    Rota: Rotas.Notificações,
    Ativo: environment.MenuAtivo.Notificações,
    Icone: Icones.Notificações,
    Show: true
  },
  {
    Titulo: Telas.Parametros,
    Rota: Rotas.Parametros,
    Ativo: environment.MenuAtivo.Parametros,
    Icone: Icones.Parametros,
    Show: false
  },
  {
    Titulo: Telas.Atividades,
    Rota: Rotas.Atividades,
    Ativo: environment.MenuAtivo.Atividades,
    Icone: Icones.Atividades,
    Show: false
  },
  {
    Titulo: Telas.SolicitarRevisao,
    Rota: Rotas.SolicitarRevisao,
    Ativo: environment.MenuAtivo.SolicitarRevisao,
    Icone: Icones.SolicitarRevisao,
    Show: false
  },
  {
    Titulo: Telas.HistoricoNotificacoes,
    Rota: Rotas.HistoricoNotificacoes,
    Ativo: environment.MenuAtivo.HistoricoNotificacoes,
    Icone: Icones.HistoricoNotificacoes,
    Show: false
  }
];

const MenuAdmin = [
  {
    Titulo: Telas.Usuarios,
    Rota: Rotas.Usuarios,
    Ativo: environment.MenuAtivo.Usuarios,
    Icone: Icones.Usuarios,
    Show: true
  },
  {
    Titulo: Telas.HistoricoAtividades,
    Rota: Rotas.HistoricoAtividades,
    Ativo: environment.MenuAtivo.HistoricoAtividades,
    Icone: Icones.Historico,
    Show: false
  },
  {
    Titulo: Telas.Clientes,
    Rota: Rotas.Clientes,
    Ativo: environment.MenuAtivo.Clientes,
    Icone: Icones.Clientes,
    Show: true
  },
  {
    Titulo: Telas.Unidades,
    Rota: Rotas.Unidades,
    Ativo: environment.MenuAtivo.Unidades,
    Icone: Icones.Unidades,
    Show: true
  },
  {
    Titulo: Telas.Estruturas,
    Rota: Rotas.Estruturas,
    Ativo: environment.MenuAtivo.Estruturas,
    Icone: Icones.Estruturas,
    Show: true
  }
];

enum Profile {
  MeuCadastro = 'Meu Cadastro',
  Empresa = 'Cliente(Alterar)',
  AlterarSenha = 'Alterar Senha',
  TermoServico = 'Termos de serviço',
  Logout = 'Logout'
}

const MenuProfile = [
  {
    Titulo: Profile.MeuCadastro,
    Icone: Icones.MeuCadastro,
    Click: 'meuCadastro'
  },
  {
    Titulo: Profile.Empresa,
    Icone: Icones.Empresa,
    Click: null
  },
  {
    Titulo: Profile.TermoServico,
    Icone: Icones.TermoServico,
    Click: 'termoServico'
  }
];

const userLogout = {
  label: '',
  icone: '',
  itens: [
    {
      tipo: Profile.Logout,
      selecionado: false,
      categoriaAtiva: false,
      icone: {
        tipo: 'fa',
        nome: 'fas fa-sign-out'
      },
      subs: [],
      click: 'logout'
    }
  ]
};

enum DashboardUnit {
  Editar = 'Editar',
  Visualizar = 'Visualizar'
}

const MiniDashboardUnit = [
  {
    Titulo: DashboardUnit.Editar,
    Icone: Icones.Edit,
    Click: 'edit'
  },
  {
    Titulo: DashboardUnit.Visualizar,
    Icone: Icones.View,
    Click: 'view'
  }
];

enum DashboardStructure {
  Editar = 'Editar',
  Visualizar = 'Visualizar',
  HistoricoEstrutura = 'Histórico Estrutura',
  ConsultarInstrumentos = 'Consultar Instrumentos',
  ConsultarSecoes = 'Consultar Seções',
  Imagem = 'Imagem'
}

const MiniDashboardStructure = [
  {
    Titulo: DashboardStructure.Editar,
    Icone: Icones.Edit,
    Click: 'edit'
  },
  {
    Titulo: DashboardStructure.Visualizar,
    Icone: Icones.View,
    Click: 'view'
  },
  {
    Titulo: DashboardStructure.HistoricoEstrutura,
    Icone: Icones.Historico,
    Click: 'history'
  },
  {
    Titulo: DashboardStructure.ConsultarInstrumentos,
    Icone: Icones.Instrumentacao,
    Click: 'instrument'
  },
  {
    Titulo: DashboardStructure.ConsultarSecoes,
    Icone: Icones.Secoes,
    Click: 'section'
  },
  {
    Titulo: 'Exibir no Mini Dashboard',
    Icone: null,
    Click: null,
    Separador: true
  },
  {
    Titulo: DashboardStructure.Imagem,
    Icone: Icones.Imagem,
    Click: 'images'
  }
];

enum DashboardSections {
  Editar = 'Editar',
  Visualizar = 'Visualizar',
  HistoricoSecao = 'Histórico Seção'
}

const MiniDashboardSections = [
  {
    Titulo: DashboardSections.Editar,
    Icone: Icones.Edit,
    Click: 'edit'
  },
  {
    Titulo: DashboardSections.Visualizar,
    Icone: Icones.View,
    Click: 'view'
  },
  {
    Titulo: DashboardSections.HistoricoSecao,
    Icone: Icones.Historico,
    Click: 'history'
  }
];

enum DashboardInstrument {
  Editar = 'Editar',
  Visualizar = 'Visualizar',
  HistoricoInstrumentacao = 'Histórico Instrumentação',
  ConsultarSecoes = 'Consultar Seções',
  GraficoInstrumentacao = 'Gráfico',
  MapaInstrumentacao = 'Ver Localização',
  Mapa = 'Mapa',
  Imagem = 'Imagem'
}

const MiniDashboardInstrument = [
  {
    Titulo: DashboardInstrument.Editar,
    Icone: Icones.Edit,
    Click: 'edit'
  },
  {
    Titulo: DashboardInstrument.Visualizar,
    Icone: Icones.View,
    Click: 'view'
  },
  {
    Titulo: DashboardInstrument.HistoricoInstrumentacao,
    Icone: Icones.Historico,
    Click: 'history'
  },
  {
    Titulo: DashboardInstrument.ConsultarSecoes,
    Icone: Icones.Secoes,
    Click: 'consult'
  },
  {
    Titulo: 'Exibir no Mini Dashboard',
    Icone: null,
    Click: null,
    Separador: true
  },
  {
    Titulo: DashboardInstrument.GraficoInstrumentacao,
    Icone: Icones.Grafico,
    Click: 'chart'
  },
  {
    Titulo: DashboardInstrument.Mapa,
    Icone: Icones.Mapa,
    Click: 'viewMap'
  },
  {
    Titulo: DashboardInstrument.MapaInstrumentacao,
    Icone: Icones.MapaInstrumentacao,
    Click: 'map'
  },
  {
    Titulo: DashboardInstrument.Imagem,
    Icone: Icones.Imagem,
    Click: 'images'
  }
];

const MenuInstrumentacao = [
  {
    Titulo: Telas.GruposInstrumentos,
    Rota: Rotas.GruposInstrumentos,
    Ativo: environment.MenuAtivo.GruposInstrumentos,
    Icone: Icones.GruposInstrumentos,
    Show: true
  },
  {
    Titulo: Telas.BaixarInformacoes,
    Rota: Rotas.BaixarInformacoes,
    Ativo: environment.MenuAtivo.BaixarInformacoes,
    Icone: Icones.BaixarInformacoes,
    Event: 'downloadInformations',
    Show: true
  },
  {
    Titulo: Telas.EdicaoInstrumentoMassa,
    Rota: Rotas.EdicaoInstrumentoMassa,
    Ativo: environment.MenuAtivo.EdicaoInstrumentoMassa,
    Icone: Icones.EdicaoInstrumentoMassa,
    Show: true
  },
  {
    Titulo: Telas.CadastroInstrumentoPlanilha,
    Rota: Rotas.CadastroInstrumentoPlanilha,
    Ativo: environment.MenuAtivo.CadastroInstrumentoPlanilha,
    Icone: Icones.CadastroInstrumentoPlanilha,
    Event: 'insertInstrumentBySpreadsheet',
    Show: true
  },
  {
    Titulo: Telas.MapaInstrumentacao,
    Rota: Rotas.Mapa,
    Ativo: environment.MenuAtivo.MapaInstrumentacao,
    Icone: Icones.Mapa,
    Show: true
  }
];

enum DashboardStaticMaterials {
  Editar = 'Editar',
  Visualizar = 'Visualizar',
  HistoricoMaterial = 'Histórico Material'
}

const MiniDashboardStaticMaterials = [
  {
    Titulo: DashboardStaticMaterials.Editar,
    Icone: Icones.Edit,
    Click: 'edit'
  },
  {
    Titulo: DashboardStaticMaterials.Visualizar,
    Icone: Icones.View,
    Click: 'view'
  },
  {
    Titulo: DashboardStaticMaterials.HistoricoMaterial,
    Icone: Icones.Historico,
    Click: 'history'
  }
];

const MenuEstabilidade = [
  {
    Titulo: Telas.FatoresDeSeguranca,
    Rota: Rotas.FatoresDeSeguranca,
    Ativo: environment.MenuAtivo.FatoresDeSeguranca,
    Icone: Icones.FatoresDeSeguranca,
    Show: true
  },
  {
    Titulo: Telas.GraficosEstabilidade,
    Rota: Rotas.GraficosEstabilidade,
    Ativo: environment.MenuAtivo.GraficosEstabilidade,
    Icone: Icones.GraficosEstabilidade,
    Show: true
  },
  {
    Titulo: Telas.MapaEstabilidade,
    Rota: Rotas.MapaEstabilidade,
    Ativo: environment.MenuAtivo.MapaEstabilidade,
    Icone: Icones.MapaEstabilidade,
    Show: true
  },
  {
    Titulo: Telas.Simulador,
    Rota: Rotas.Simulador,
    Ativo: environment.MenuAtivo.Simulador,
    Icone: Icones.Simulador,
    Show: true
  }
];

enum DashboardReports {
  Editar = 'Editar',
  Excluir = 'Excluir'
}

const MiniDashboardReports = [
  {
    Titulo: DashboardReports.Editar,
    Icone: Icones.Edit,
    Click: 'edit'
  },
  {
    Titulo: DashboardReports.Excluir,
    Icone: Icones.Delete,
    Click: 'delete'
  }
];

enum DashboardStability {
  DownloadDXF = 'Download DXF',
  DownloadPNG = 'Download PNG',
  DownloadZIP = 'Download .zip'
}

const MiniDashboardStability = [
  {
    Titulo: DashboardStability.DownloadDXF,
    Icone: Icones.DownloadDXF,
    Click: 'downloadDXF'
  },
  {
    Titulo: DashboardStability.DownloadPNG,
    Icone: Icones.DownloadPNG,
    Click: 'downloadPNG'
  },
  {
    Titulo: DashboardStability.DownloadZIP,
    Icone: Icones.DownloadZIP,
    Click: 'downloadZIP'
  }
];

enum DashboardSimulations {
  ResultadoSimulacao = 'Resultados da simulação',
  FixarSimulacao = 'Fixar simulação',
  Excluir = 'Excluir',
  Renomear = 'Renomear simulação',
  Compartilhar = 'Compartilhar simulação'
}

const MiniDashboardSimulations = [
  {
    Titulo: DashboardSimulations.ResultadoSimulacao,
    Icone: Icones.Estabilidade,
    Click: 'result'
  },
  {
    Titulo: DashboardSimulations.Compartilhar,
    Icone: Icones.Compartilhar,
    Click: 'share'
  },
  {
    Titulo: DashboardSimulations.FixarSimulacao,
    Icone: Icones.FixarSimulacao,
    Click: 'fixed'
  },
  {
    Titulo: DashboardSimulations.Renomear,
    Icone: Icones.Renomear,
    Click: 'rename'
  },
  {
    Titulo: DashboardSimulations.Excluir,
    Icone: Icones.Delete,
    Click: 'delete'
  }
];

enum DashboardInspectionSheet {
  Editar = 'Editar',
  Excluir = 'Excluir',
  Visualizar = 'Visualizar',
  Historico = 'Histórico'
}

const MiniDashboardInspectionSheet = [
  {
    Titulo: DashboardInspectionSheet.Editar,
    Icone: Icones.Edit,
    Click: 'edit'
  },
  {
    Titulo: DashboardInspectionSheet.Excluir,
    Icone: Icones.Delete,
    Click: 'delete'
  },
  {
    Titulo: DashboardInspectionSheet.Visualizar,
    Icone: Icones.View,
    Click: 'view'
  },
  {
    Titulo: DashboardInspectionSheet.Historico,
    Icone: Icones.Historico,
    Click: 'history'
  }
];

export {
  Rotas,
  Telas,
  Icones,
  Profile,
  Acoes,
  MenuPrincipal,
  MenuAdmin,
  MenuInstrumentacao,
  MenuProfile,
  MenuEstabilidade,
  userLogout,
  MiniDashboardUnit,
  MiniDashboardStructure,
  MiniDashboardSections,
  MiniDashboardInstrument,
  MiniDashboardStaticMaterials,
  MiniDashboardReports,
  MiniDashboardStability,
  MiniDashboardSimulations,
  MiniDashboardInspectionSheet
};
