import { Component, EventEmitter, OnInit, Input, Output } from '@angular/core';
import { moveItemInArray, CdkDragDrop } from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-drag-and-drop',
  templateUrl: './drag-and-drop.component.html',
  styleUrls: ['./drag-and-drop.component.scss']
})
export class DragAndDropComponent implements OnInit {
  @Input() public dataList: any = [];
  @Input() public orderField: string = '';
  @Input() public textField: string = '';
  @Input() public dragDrop: boolean = false;
  @Input() public class: string = 'drag-list';

  @Output() public sendClickEvent = new EventEmitter();

  constructor() {}

  ngOnInit(): void {}

  onDrop(event: CdkDragDrop<string[]>, key: string = '', increment = 1) {
    moveItemInArray(this.dataList, event.previousIndex, event.currentIndex);
    this.dataList.forEach((item, index) => {
      item[key] = index + increment;
    });
  }

  clickRowEvent(action: any = null) {
    this.sendClickEvent.emit(action);
  }
}
