<div class="col-md-12">
  <form [formGroup]="formGeneralizedHoekBrown">
    <div class="row">
      <label class="form-label" style="font-style: italic">Fórmula:</label>
      <div class="col-md-3">
        <img
          src="assets/images/static-materials/generalized-hoek-brown.png"
          class="img-fluid img-thumbnail"
          style="max-height: 80px; width: auto"
        />
      </div>
    </div>
    <div class="row mt-1">
      <!-- Ucs instact -->
      <div class="col-md-3">
        <label class="form-label">UCS Intacto</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="ucs_intact"
            min="0"
            step="0.0000001"
            (keypress)="
              func.controlNumber(
                $event,
                formGeneralizedHoekBrown.get('ucs_intact'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formGeneralizedHoekBrown.get('ucs_intact')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formGeneralizedHoekBrown.get('ucs_intact').valid &&
            formGeneralizedHoekBrown.get('ucs_intact').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Type cohesion -->
      <div class="col-md-3">
        <label class="form-label">Definir resistência utilizando:</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="dropdownSettings"
          [data]="strengthDefinition"
          formControlName="strength_definition"
          (onSelect)="itemEvent($event, 'select')"
          (onDeSelect)="itemEvent($event, 'deselect')"
          [disabled]="
            formGeneralizedHoekBrown.controls['strength_definition'].disabled
          "
        ></ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formGeneralizedHoekBrown.get('strength_definition').valid &&
            formGeneralizedHoekBrown.get('strength_definition').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <div class="row mt-1" *ngIf="seletectedStrengthDefinition == 1">
      <!-- GSI -->
      <div class="col-md-3">
        <label class="form-label">GSI</label>
        <input
          type="text"
          class="form-control"
          formControlName="gsi"
          min="0"
          step="0.0000001"
          (keypress)="
            func.controlNumber(
              $event,
              formGeneralizedHoekBrown.get('gsi'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber($event, formGeneralizedHoekBrown.get('gsi'))
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formGeneralizedHoekBrown.get('gsi').valid &&
            formGeneralizedHoekBrown.get('gsi').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- mi -->
      <div class="col-md-3">
        <label class="form-label">mi</label>
        <input
          type="text"
          class="form-control"
          formControlName="mi"
          min="0"
          step="0.0000001"
          (keypress)="
            func.controlNumber(
              $event,
              formGeneralizedHoekBrown.get('mi'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber($event, formGeneralizedHoekBrown.get('mi'))
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formGeneralizedHoekBrown.get('mi').valid &&
            formGeneralizedHoekBrown.get('mi').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- D -->
      <div class="col-md-3">
        <label class="form-label">D</label>
        <input
          type="text"
          class="form-control"
          formControlName="d"
          min="0"
          step="0.0000001"
          (keypress)="
            func.controlNumber(
              $event,
              formGeneralizedHoekBrown.get('d'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber($event, formGeneralizedHoekBrown.get('d'))
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formGeneralizedHoekBrown.get('d').valid &&
            formGeneralizedHoekBrown.get('d').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <div class="row mt-1" *ngIf="seletectedStrengthDefinition == 2">
      <!-- Mb -->
      <div class="col-md-3">
        <label class="form-label">Mb</label>
        <input
          type="text"
          class="form-control"
          formControlName="mb"
          min="0"
          step="0.0000001"
          (keypress)="
            func.controlNumber(
              $event,
              formGeneralizedHoekBrown.get('mb'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber($event, formGeneralizedHoekBrown.get('mb'))
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formGeneralizedHoekBrown.get('gsi').valid &&
            formGeneralizedHoekBrown.get('gsi').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- s -->
      <div class="col-md-3">
        <label class="form-label">s</label>
        <input
          type="text"
          class="form-control"
          formControlName="s"
          min="0"
          step="0.0000001"
          (keypress)="
            func.controlNumber(
              $event,
              formGeneralizedHoekBrown.get('s'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber($event, formGeneralizedHoekBrown.get('s'))
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formGeneralizedHoekBrown.get('s').valid &&
            formGeneralizedHoekBrown.get('s').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- a -->
      <div class="col-md-3">
        <label class="form-label">a</label>
        <input
          type="text"
          class="form-control"
          formControlName="a"
          min="0"
          step="0.0000001"
          (keypress)="
            func.controlNumber(
              $event,
              formGeneralizedHoekBrown.get('a'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber($event, formGeneralizedHoekBrown.get('a'))
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formGeneralizedHoekBrown.get('a').valid &&
            formGeneralizedHoekBrown.get('a').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
  </form>
</div>
