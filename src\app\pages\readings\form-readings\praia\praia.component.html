<form [formGroup]="formReading" class="mb-3">
  <!-- instrument -->
  <div class="col-md-2">
    <label class="form-label">Seção</label>
    <select
      class="form-select"
      formControlName="section"
      (change)="changeSection(formReading.controls['section'].value)"
    >
      <option value="" *ngIf="formReading.controls['section'].value == ''">
        Selecione...
      </option>
      <option
        *ngFor="let sectionItem of sectionsList"
        [ngValue]="sectionItem.id"
      >
        {{ sectionItem.identifier }}
      </option>
    </select>
    <small
      class="form-text text-danger"
      *ngIf="
        !formReading.get('section').valid &&
        formReading.get('section').touched &&
        !formReading.get('section').disabled
      "
      >Campo Obrigatório.</small
    >
  </div>
  <!-- date -->
  <div class="col-md-2 ms-3">
    <label class="form-label">Data e hora</label>
    <input type="datetime-local" class="form-control" formControlName="date" />
    <small
      class="form-text text-danger"
      *ngIf="!formReading.get('date').valid && formReading.get('date').touched"
      >Campo Obrigatório.</small
    >
  </div>
  <!-- length -->
  <div class="col-md-2 ms-3">
    <label class="form-label">Comprimento</label>
    <div class="input-group">
      <input
        type="text"
        class="form-control"
        formControlName="length"
        (blur)="func.formatType($event)"
        (focus)="func.formatType($event)"
        (keypress)="func.controlNumber($event, null, 'positiveDecimal')"
        (keyup)="func.controlNumber($event, formReading.get('length'))"
        appDisableScroll
      /><span class="input-group-text">{{ units[0] }}</span>
    </div>
    <small
      class="form-text text-danger"
      *ngIf="
        !formReading.get('length').valid && formReading.get('length').touched
      "
      >Campo Obrigatório.</small
    >
  </div>
</form>
