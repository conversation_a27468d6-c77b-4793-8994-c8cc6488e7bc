import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Axis } from 'src/app/constants/instruments.constants';
import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-security-levels',
  templateUrl: './security-levels.component.html',
  styleUrls: ['./security-levels.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SecurityLevelsComponent implements OnInit, OnChanges {
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public editingBatch: boolean = false;
  @Input() public disabled: boolean = false;
  @Input() public typeInstrument: any = null;

  public ctrlSecurityLevels: boolean = false;

  public formSecurityLevels: FormGroup = new FormGroup({
    id: new FormControl(''),
    attention: new FormControl(''),
    alert: new FormControl(''),
    emergency: new FormControl(''),
    abrupt_variation_between_readings: new FormControl(''),
    maximum_daily_rainfall: new FormControl(''), //Pluviômetro e Pluviógrafo
    rain_intensity: new FormControl(''), //Pluviômetro e Pluviógrafo
    axis: new FormControl(''),

    id_1: new FormControl(''),
    attention_1: new FormControl(''),
    alert_1: new FormControl(''),
    emergency_1: new FormControl(''),
    abrupt_variation_between_readings_1: new FormControl(''),
    maximum_daily_rainfall_1: new FormControl(null), //Pluviômetro e Pluviógrafo
    rain_intensity_1: new FormControl(null), //Pluviômetro e Pluviógrafo
    axis_1: new FormControl(1),

    id_2: new FormControl(''),
    attention_2: new FormControl(''),
    alert_2: new FormControl(''),
    emergency_2: new FormControl(''),
    abrupt_variation_between_readings_2: new FormControl(''),
    maximum_daily_rainfall_2: new FormControl(null), //Pluviômetro e Pluviógrafo
    rain_intensity_2: new FormControl(null), //Pluviômetro e Pluviógrafo
    axis_2: new FormControl(2),

    id_3: new FormControl(''),
    attention_3: new FormControl(''),
    alert_3: new FormControl(''),
    emergency_3: new FormControl(''),
    abrupt_variation_between_readings_3: new FormControl(''),
    maximum_daily_rainfall_3: new FormControl(null), //Pluviômetro e Pluviógrafo
    rain_intensity_3: new FormControl(null), //Pluviômetro e Pluviógrafo
    axis_3: new FormControl(3),

    id_4: new FormControl(''),
    attention_4: new FormControl(''),
    alert_4: new FormControl(''),
    emergency_4: new FormControl(''),
    abrupt_variation_between_readings_4: new FormControl(''),
    maximum_daily_rainfall_4: new FormControl(null), //Pluviômetro e Pluviógrafo
    rain_intensity_4: new FormControl(null), //Pluviômetro e Pluviógrafo
    axis_4: new FormControl(4),

    id_5: new FormControl(''),
    attention_5: new FormControl(''),
    alert_5: new FormControl(''),
    emergency_5: new FormControl(''),
    abrupt_variation_between_readings_5: new FormControl(''),
    maximum_daily_rainfall_5: new FormControl(null), //Pluviômetro e Pluviógrafo
    rain_intensity_5: new FormControl(null), //Pluviômetro e Pluviógrafo
    axis_5: new FormControl(5),

    id_6: new FormControl(''),
    attention_6: new FormControl(''),
    alert_6: new FormControl(''),
    emergency_6: new FormControl(''),
    abrupt_variation_between_readings_6: new FormControl(''),
    maximum_daily_rainfall_6: new FormControl(null), //Pluviômetro e Pluviógrafo
    rain_intensity_6: new FormControl(null), //Pluviômetro e Pluviógrafo
    axis_6: new FormControl(6)
  });

  public axis = Axis;
  public _current: any = {};
  public func = fn;

  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
    if (changes.disabled && changes.disabled.currentValue != null) {
      changes.disabled.currentValue ? this.formSecurityLevels.disable() : this.formSecurityLevels.enable();
    }
  }

  splitData($dados) {
    let dadosSL = null;

    if (Array.isArray($dados)) {
      dadosSL = $dados[0];
    } else {
      dadosSL = $dados;
    }

    if (!fn.isEmpty(dadosSL)) {
      if (this.typeInstrument != 6 && this.typeInstrument != 7) {
        this.formSecurityLevels.controls['id'].setValue(dadosSL.id);
        this.formSecurityLevels.controls['attention'].setValue(dadosSL.attention);
        this.formSecurityLevels.controls['alert'].setValue(dadosSL.alert);
        this.formSecurityLevels.controls['emergency'].setValue(dadosSL.emergency);
        this.formSecurityLevels.controls['abrupt_variation_between_readings'].setValue(dadosSL.abrupt_variation_between_readings);
        this.formSecurityLevels.controls['maximum_daily_rainfall'].setValue(dadosSL.maximum_daily_rainfall);
        this.formSecurityLevels.controls['rain_intensity'].setValue(dadosSL.rain_intensity);
        this.formSecurityLevels.controls['axis'].setValue(dadosSL.axis);

        this.ctrlSecurityLevels = true;

        if ($dados.hasOwnProperty('_current')) {
          let dadosCurrentSL = null;

          if (Array.isArray($dados._current)) {
            dadosCurrentSL = $dados._current[0];
          } else {
            dadosCurrentSL = $dados._current;
          }

          this._current['attention'] = dadosSL.attention != dadosCurrentSL.attention ? dadosCurrentSL.attention : null;
          this._current['alert'] = dadosSL.alert != dadosCurrentSL.alert ? dadosCurrentSL.alert : null;
          this._current['emergency'] = dadosSL.emergency != dadosCurrentSL.emergency ? dadosCurrentSL.emergency : null;
          this._current['abrupt_variation_between_readings'] =
            dadosSL.abrupt_variation_between_readings != dadosCurrentSL.abrupt_variation_between_readings
              ? dadosCurrentSL.abrupt_variation_between_readings
              : null;
          this._current['maximum_daily_rainfall'] =
            dadosSL.maximum_daily_rainfall != dadosCurrentSL.maximum_daily_rainfall ? dadosCurrentSL.maximum_daily_rainfall : null;
          this._current['rain_intensity'] = dadosSL.rain_intensity != dadosCurrentSL.rain_intensity ? dadosCurrentSL.rain_intensity : null;
          this._current['axis'] = dadosSL.axis != dadosCurrentSL.axis ? dadosCurrentSL.axis : null;
        }
      } else {
        for (let i = 1; i <= 6; i++) {
          dadosSL = $dados[i - 1];
          this.formSecurityLevels.controls['id_' + i].setValue(dadosSL.id);
          this.formSecurityLevels.controls['attention_' + i].setValue(dadosSL.attention);
          this.formSecurityLevels.controls['alert_' + i].setValue(dadosSL.alert);
          this.formSecurityLevels.controls['emergency_' + i].setValue(dadosSL.emergency);
          this.formSecurityLevels.controls['abrupt_variation_between_readings_' + i].setValue(dadosSL.abrupt_variation_between_readings);
          this.formSecurityLevels.controls['maximum_daily_rainfall_' + i].setValue(dadosSL.maximum_daily_rainfall);
          this.formSecurityLevels.controls['rain_intensity_' + i].setValue(dadosSL.rain_intensity);
          this.formSecurityLevels.controls['axis_' + i].setValue(dadosSL.axis);

          const axis = dadosSL.axis;

          if ($dados.hasOwnProperty('_current')) {
            const dadosCurrentSL = fn.findIndexInArrayofObject($dados._current, 'axis', axis, 'axis', true);

            this._current['attention_' + axis] = dadosSL.attention != dadosCurrentSL.attention ? dadosCurrentSL.attention : null;
            this._current['alert_' + axis] = dadosSL.alert != dadosCurrentSL.alert ? dadosCurrentSL.alert : null;
            this._current['emergency_' + axis] = dadosSL.emergency != dadosCurrentSL.emergency ? dadosCurrentSL.emergency : null;
            this._current['abrupt_variation_between_readings_' + axis] =
              dadosSL.abrupt_variation_between_readings != dadosCurrentSL.abrupt_variation_between_readings
                ? dadosCurrentSL.abrupt_variation_between_readings
                : null;
            this._current['maximum_daily_rainfall_' + axis] =
              dadosSL.maximum_daily_rainfall != dadosCurrentSL.maximum_daily_rainfall ? dadosCurrentSL.maximum_daily_rainfall : null;
            this._current['rain_intensity_' + axis] = dadosSL.rain_intensity != dadosCurrentSL.rain_intensity ? dadosCurrentSL.rain_intensity : null;
            this._current['axis_' + axis] = axis;
          }
        }
      }
    }

    if (this.view) {
      this.formSecurityLevels.disable();
    }
  }
}
