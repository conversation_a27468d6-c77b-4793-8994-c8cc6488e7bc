import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { IDropdownSettings } from 'ng-multiselect-dropdown';

import { UsersService as UsersServiceApi } from 'src/app/services/api/users.service';
import { UserService } from 'src/app/services/user.service';

@Component({
  selector: 'app-modal-share-simulation',
  templateUrl: './modal-share-simulation.component.html',
  styleUrls: ['./modal-share-simulation.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalShareSimulationComponent implements OnInit, OnChanges {
  @ViewChild('modalShareSimulation') modalShareSimulation: ElementRef;

  @Input() public simulationItem: any = '';
  @Output() public sendClickEvent = new EventEmitter();

  public formShareSimulation: FormGroup = new FormGroup({
    Name: new FormControl({ value: '', disabled: true }),
    UserId: new FormControl([])
  });

  public controls: any = [];

  public messagesError: any = null;

  public profile: any = null;

  public users: any = [];
  public usersSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  constructor(private modalService: NgbModal, private usersServiceApi: UsersServiceApi, private userService: UserService) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile(false);
    this.controls = this.formShareSimulation.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.simulationItem.currentValue != null) {
      this.getUser();
      this.controls['Name'].setValue(changes.simulationItem.currentValue.name);
    }
  }

  openModal() {
    this.resetForm();
    this.modalService.open(this.modalShareSimulation, { size: 'md' });
  }

  getUser() {
    const params = {
      StructureId: this.simulationItem.sections[0].structure_id,
      Roles: Array.from({ length: this.profile.level }, (_, i) => this.profile.level - i),
      Active: true
    };

    this.usersServiceApi.getUsersList(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.users = dados;
      this.users = this.users.map((item: any) => {
        return { id: item.id, name: item.username };
      });
    });
  }

  resetForm() {
    this.controls['UserId'].setValue([]);
  }

  getForm(action = 'shareUser') {
    this.sendClickEvent.emit({ action: action, form: this.formShareSimulation.controls });
  }
}
