.occurrences-container {
  background-color: #ffffff;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  border-radius: 5px;
  padding: 10px;
  font-family: averta-bold;

  .form-control.readonly {
    background-color: #f5f5f5;
    color: #6c757d;
    cursor: not-allowed;
    pointer-events: none;
  }

  .form-label,
  .form-check-label {
    color: #34b575;
    font-family: averta-bold;
    font-size: 0.875em;
  }

  .form-control {
    border-color: #d4d2d2;
    font-size: 0.875em;
  }

  .form-select {
    font-size: 0.875em !important;
    line-height: 1.52857143 !important;
  }

  .table-wrapper {
    overflow-x: auto;
    margin-bottom: 20px;
  }

  .list-status {
    font-size: 0.875em;
  }

  .status-preto {
    background-color: black;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-cinza {
    background-color: #cccccc;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-verde {
    background-color: green;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-amarelo {
    background-color: gold;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-vermelho {
    background-color: red;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: inline-block;
  }

  .status-vermelho-piscante {
    background-color: red;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: inline-block;
    animation: blink 1s infinite;
  }

  .blink {
    animation: blink 1s infinite;
  }

  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }

  .maps.show {
    animation: showMap 1s ease 0s 1 normal forwards;
  }

  .text-muted {
    font-style: italic;
    color: #6c757d;
  }
}
