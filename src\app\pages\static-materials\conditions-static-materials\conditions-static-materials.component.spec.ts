import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ConditionsStaticMaterialsComponent } from './conditions-static-materials.component';

describe('ConditionsStaticMaterialsComponent', () => {
  let component: ConditionsStaticMaterialsComponent;
  let fixture: ComponentFixture<ConditionsStaticMaterialsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ConditionsStaticMaterialsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ConditionsStaticMaterialsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
