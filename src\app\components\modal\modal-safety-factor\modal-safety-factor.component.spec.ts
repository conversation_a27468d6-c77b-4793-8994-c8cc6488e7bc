import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalSafetyFactorComponent } from './modal-safety-factor.component';

describe('ModalSafetyFactorComponent', () => {
  let component: ModalSafetyFactorComponent;
  let fixture: ComponentFixture<ModalSafetyFactorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalSafetyFactorComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalSafetyFactorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
