const NotificationThemeGroup = [
  { value: 1, label: 'Instrumentação Cadastrada' },
  { value: 2, label: 'Instrumentação Atualizada' },
  { value: 3, label: 'Instrumentação' },
  { value: 4, label: 'Instrumentação' },
  { value: 5, label: 'Instrumentação' },
  { value: 6, label: 'Leitura Cadastrada' },
  { value: 7, label: 'Leitura Atualizada' },
  { value: 8, label: 'Leitura Excluída' },
  { value: 9, label: 'Carta de Controle' },
  { value: 10, label: 'Criação de Fator de Segurança' },
  { value: 11, label: 'Fator de segurança acima dos níveis tolerados' },
  { value: 12, label: 'Inspeção em atraso' },
  { value: 13, label: 'Licença Logisoil' },
  { value: 14, label: 'Gradiente Hidráulico' },
  { value: 15, label: 'Análise de Estabilidade' },
  { value: 16, label: 'Nova Análise de Estabilidade' },
  { value: 17, label: 'Análise de Estabilidade Atualizada' },
  { value: 18, label: 'Análise de Estabilidade - FS abaixo do permitido' },
  { value: 19, label: 'Análise de Estabilidade' },
  { value: 20, label: 'Leitura Automatizada' },
  { value: 21, label: 'Estrutura Atualizada' },
  { value: 22, label: 'Estrutura Inativada' }
];

const NotificationPeriod = [
  { value: 7, label: 'Última semana' },
  { value: 15, label: 'Últimos 15 dias' },
  { value: 30, label: 'Último mês' },
  { value: 1, label: 'Tudo' },
  { value: 0, label: 'Personalizado' }
];

enum NotificationTheme {
  InstrumentCreated = 1,
  InstrumentUpdated = 2,
  InstrumentDeleted = 3,
  InstrumentDamaged = 4,
  InstrumentRepaired = 5,
  ReadingCreated = 6,
  ReadingUpdated = 7,
  ReadingDeleted = 8,
  ControlLetterNewRegistry = 9,
  SecurityLevelCreated = 10,
  SecurityLevelAboveTolerance = 11,
  OverdueInspection = 12,
  License = 13,
  DrainageAlert = 14,
  StabilityAnalysis = 15,
  StabilityAnalysisCreated = 16,
  StabilityAnalysisUpdated = 17,
  StabilityAnalysisSafetyFactorBelowToleratedLevels = 18,
  StabilityAnalysisWithWarnings = 19,
  AutomatedReading = 20,
  StructureUpdated = 21,
  StructureDeleted = 22
}

export { NotificationThemeGroup, NotificationTheme, NotificationPeriod };
