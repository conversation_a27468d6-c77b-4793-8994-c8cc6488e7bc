<div
  class="table-responsive-md mt-4"
  *ngIf="inspectionSheetType === 1 || inspectionSheetType === 3"
>
  <form [formGroup]="currentSituationForm">
    <table class="table table-bordered table-hover align-middle">
      <thead class="table-light">
        <tr>
          <th style="width: 30%">Identificação da Anomalia</th>
          <th style="width: 15%">Situação</th>
          <th style="width: 10%">Pontuação</th>
          <th style="width: 45%">Observações</th>
        </tr>
      </thead>
      <tbody formArrayName="situations">
        <tr
          *ngFor="let row of situations.controls; let i = index"
          [formGroupName]="i"
        >
          <!-- Identificador -->
          <td>{{ row.get('Identifier')?.value }}</td>

          <!-- Rótulo da Situação -->
          <td>
            <div *ngIf="!view; else readModeSituationLabel">
              <select
                class="form-select"
                formControlName="ActionResultClassification"
              >
                <option
                  *ngFor="let option of actionResultClassification"
                  [value]="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
            </div>
            <ng-template #readModeSituationLabel>
              {{
                getSituationLabel(row.get('ActionResultClassification')?.value)
              }}
            </ng-template>
          </td>

          <!-- Seleção de Pontuação -->
          <td>
            <div *ngIf="!view; else readModeAnomalyScore">
              <select
                class="form-select"
                formControlName="AnomalyScore"
                (change)="onChange('AnomalyScore', i)"
              >
                <option
                  *ngFor="let option of getAnomalyScoreOptions(i)"
                  [value]="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>
            <ng-template #readModeAnomalyScore>
              {{ row.get('AnomalyScore')?.value }}
            </ng-template>
          </td>

          <!-- Nota -->
          <td>
            <div *ngIf="!view; else readModeNote">
              <textarea
                rows="2"
                class="form-control"
                formControlName="Note"
                (blur)="onBlur('Note', i)"
              ></textarea>
            </div>
            <ng-template #readModeNote>
              {{ row.get('Note')?.value }}
            </ng-template>
          </td>
        </tr>
      </tbody>
    </table>
  </form>
</div>

<!-- Debug -->
<!-- <div class="mt-4">
  <h5>Debug - Dados do Formulário:</h5>
  <pre>{{ currentSituationForm.value | json }}</pre>
</div> -->
