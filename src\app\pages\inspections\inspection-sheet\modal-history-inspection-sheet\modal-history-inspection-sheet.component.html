<ng-template #modalHistoryInspectionSheet let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">Histórico</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Fechar"
      (click)="d('Cross click')"
    ></button>
  </div>
  <div class="modal-body">
    <app-table
      *ngIf="tableData.length > 0"
      [tableHeader]="tableHeader"
      [tableData]="tableData"
    ></app-table>
  </div>
  <!-- Paginação -->
  <div class="row mt-3" *ngIf="tableData.length > 0">
    <app-paginator
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event)"
    ></app-paginator>
  </div>

  <!-- Botões -->
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-gray'"
      [label]="'Fechar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>

<ngx-spinner
  bdColor="rgba(51,51,51,0.1)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
