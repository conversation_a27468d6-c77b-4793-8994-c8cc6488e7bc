<form [formGroup]="formReading" class="mb-3">
  <!-- instrument -->
  <div class="col-md-2">
    <label class="form-label">Instrumento</label>
    <select
      class="form-select"
      formControlName="instrument"
      (change)="changeInstrument(formReading.controls['instrument'].value)"
    >
      <option value="" *ngIf="formReading.controls['instrument'].value == ''">
        Selecione...
      </option>
      <option
        *ngFor="let instrumentItem of instrumentsList"
        [ngValue]="instrumentItem.id"
      >
        {{ instrumentItem.identifier }}
      </option>
    </select>
    <small
      class="form-text text-danger"
      *ngIf="
        !formReading.get('instrument').valid &&
        formReading.get('instrument').touched &&
        !formReading.get('instrument').disabled
      "
      >Campo Obrigatório.</small
    >
  </div>
  <!-- date -->
  <div class="col-md-2 ms-3">
    <label class="form-label">Data e hora</label>
    <input type="datetime-local" class="form-control" formControlName="date" />
    <small
      class="form-text text-danger"
      *ngIf="!formReading.get('date').valid && formReading.get('date').touched"
      >Campo Obrigatório.</small
    >
  </div>
  <!-- pluviometria -->
  <div class="col-md-2 ms-3">
    <label class="form-label">Pluviometria</label>
    <div class="input-group">
      <input
        type="text"
        class="form-control"
        formControlName="pluviometry"
        (blur)="func.formatType($event)"
        (focus)="func.formatType($event)"
        (keypress)="func.controlNumber($event, null, 'notE')"
        (keyup)="func.controlNumber($event, formReading.get('pluviometry'))"
      /><span class="input-group-text">{{ units[0] }}</span>
    </div>
    <small
      class="form-text text-danger"
      *ngIf="
        !formReading.get('pluviometry').valid &&
        formReading.get('pluviometry').touched
      "
      >Campo Obrigatório.</small
    >
  </div>
</form>
