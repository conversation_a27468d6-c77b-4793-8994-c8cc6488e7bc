<div class="list-content py-1" id="view-inspection-sheet" *ngIf="load !== null">
  <ng-container *ngIf="load && dados; else noData">
    <!-- Header -->
    <div class="row mt-1 mb-3">
      <div class="col-md-3 d-flex justify-content-start">
        <img
          src="/assets/images/logoLogisoil.png"
          alt="Logisoil"
          title="Logisoil"
          class="logo-inspection"
        />
      </div>
      <div
        class="col-md-6 d-flex justify-content-center align-items-center fs-5 fw-bold"
      >
        Ficha de Inspeção {{ dados?.search_identifier }}
      </div>
      <div class="col-md-3 d-flex justify-content-end">
        <img
          *ngIf="!dados?.client?.logo?.content"
          src="/assets/ico/WL_logo_walm.svg"
          height="50"
        />
        <img
          *ngIf="dados?.client?.logo?.content"
          [src]="dados?.client?.logo?.content"
          height="50"
        />
      </div>
    </div>

    <!-- Dados cadastrais -->
    <div class="row mt-2 px-1">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Dados cadastrais
      </div>
    </div>

    <div class="row">
      <div class="col-md-4">
        <label class="title d-block">Empreendedor:</label>
        <span class="content d-block">{{ dados?.client?.name }}</span>
      </div>
      <div class="col-md-4">
        <label class="title d-block">Nome da barragem:</label>
        <span class="content d-block">{{ dados?.client_unit?.name }}</span>
      </div>
      <div class="col-md-4">
        <label class="title d-block">Coordenadas:</label>
        <span class="content d-block">{{
          dados?.general_data?.coordinate
        }}</span>
      </div>
    </div>

    <!-- Datas, estrutura, status -->
    <div class="row">
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="title d-block">Data de cadastro da ficha:</label>
        <span class="content d-block">{{
          dados?.general_data?.created_date
        }}</span>
      </div>
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="title d-block">Cadastrado por:</label>
        <span class="content d-block">{{
          dados?.general_data?.created_by
        }}</span>
      </div>
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="title d-block">Estrutura:</label>
        <span class="content d-block">{{ dados?.structure?.name }}</span>
      </div>
    </div>

    <!-- Datas inspeção -->
    <div class="row">
      <div class="col-md-4">
        <label class="title d-block">{{ startDateForm.label }}:</label>
        <span class="content d-block">{{
          dados?.general_data?.start_date
        }}</span>
      </div>
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 4
        "
      >
        <label class="title d-block">Fim da inspeção:</label>
        <span class="content d-block">{{ dados?.general_data?.end_date }}</span>
      </div>
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="title d-block">Executado por:</label>
        <span class="content d-block">{{ dados?.executed_by }}</span>
      </div>
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="title d-block">Status da ficha:</label>
        <span class="content d-block">{{ dados?.general_data?.status }}</span>
      </div>
    </div>

    <!-- Identificação do avaliador (FIE) -->
    <h6 class="mt-4 fw-bold" *ngIf="inspectionSheetType === 3">
      Identificação do avaliador
    </h6>
    <hr *ngIf="inspectionSheetType === 3" />
    <div class="row" *ngIf="inspectionSheetType === 3">
      <div class="col-md-4">
        <label class="title d-block">Nome:</label>
        <span class="content d-block">{{ dados?.evaluator_name }}</span>
      </div>
      <div class="col-md-4">
        <label class="title d-block">Cargo:</label>
        <span class="content d-block">{{ dados?.evaluator_position }}</span>
      </div>
      <div class="col-md-2">
        <label class="title d-block">CREA nº:</label>
        <span class="content d-block">{{ dados?.evaluator_crea }}</span>
      </div>
      <div class="col-md-2">
        <label class="title d-block">ART nº:</label>
        <span class="content d-block">{{ dados?.evaluator_art }}</span>
      </div>
    </div>

    <!-- Responsáveis (EOR) -->
    <div class="row" *ngIf="inspectionSheetType === 2">
      <div
        *ngFor="let responsible of dados?.responsibles; let i = index"
        class="row mb-3"
      >
        <div class="col-md-6" style="margin: 0">
          <label class="title d-block" *ngIf="i === 0">Responsáveis:</label>
          <span class="content d-block">{{ responsible.responsible }}</span>
        </div>
        <div class="col-md-6" style="margin: 0">
          <label class="title d-block" *ngIf="i === 0">Empresa:</label>
          <span class="content d-block">{{
            responsible.is_internal_representative
              ? 'Walm'
              : dados?.client?.name
          }}</span>
        </div>
      </div>
    </div>

    <!-- Componentes da ficha -->
    <div class="row mt-4 px-1" *ngIf="dados?.previous_situation">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Situação pretérita
      </div>
    </div>
    <app-previous-situation
      *ngIf="dados?.previous_situation"
      [inspectionSheetType]="inspectionSheetType"
      [view]="true"
    ></app-previous-situation>

    <div class="row mt-4 px-1" *ngIf="dados?.areas">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Aspectos observados
      </div>
    </div>
    <app-aspects-observed
      *ngIf="dados?.areas"
      [inspectionSheetType]="inspectionSheetType"
      [status]="dados?.status"
      [locked]="dados?.locked"
      [view]="true"
    ></app-aspects-observed>

    <div class="row mt-4 px-1">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Estado de conservação
      </div>
    </div>
    <app-conservation-status
      [inspectionSheetType]="inspectionSheetType"
      [status]="dados?.status"
      [locked]="dados?.locked"
      [view]="true"
    ></app-conservation-status>

    <div class="row mt-4 px-1" *ngIf="dados?.notes?.length > 0">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Observações gerais
      </div>
    </div>
    <app-general-observations
      *ngIf="dados?.notes?.length > 0"
      [inspectionSheetType]="inspectionSheetType"
      [status]="dados?.status"
      [locked]="dados?.locked"
      [view]="true"
    ></app-general-observations>

    <div class="row mt-4 px-1" *ngIf="dados?.identified_anomalies?.length > 0">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Ações executadas
      </div>
    </div>
    <app-actions-executed
      *ngIf="dados?.identified_anomalies?.length > 0"
      [inspectionSheetType]="inspectionSheetType"
      [status]="dados?.status"
      [locked]="dados?.locked"
      [view]="true"
    ></app-actions-executed>

    <div class="row mt-4 px-1" *ngIf="dados?.identified_anomalies?.length > 0">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Situação atual
      </div>
    </div>
    <app-current-situation
      *ngIf="dados?.identified_anomalies?.length > 0"
      [inspectionSheetType]="inspectionSheetType"
      [status]="dados?.status"
      [locked]="dados?.locked"
      [view]="true"
    ></app-current-situation>
  </ng-container>

  <!-- Caso não tenha dados (204 ou vazio) -->
  <ng-template #noData>
    <div class="alert alert-warning mt-3" role="alert">
      Ficha de inspeção não encontrada.
    </div>
  </ng-template>

  <!-- Botão voltar -->
  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/inspections']"
    ></app-button>
  </div>
</div>
