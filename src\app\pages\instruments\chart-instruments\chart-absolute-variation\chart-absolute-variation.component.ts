import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';

import { navBar } from 'src/app/constants/chart.constants';
import { MessageInputInvalid } from 'src/app/constants/message.constants';

import { ChartService as ChartServiceApi } from 'src/app/services/api/chart.service';
import { FileLoaderService } from 'src/app/services/file-loader.service';
import { Markers } from 'src/app/constants/chart.constants';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-chart-absolute-variation',
  templateUrl: './chart-absolute-variation.component.html',
  styleUrls: ['./chart-absolute-variation.component.scss']
})
export class ChartAbsoluteVariationComponent implements OnInit {
  @Input() public instrumentInfo: any = null;

  public formChart: FormGroup = new FormGroup({
    instrument: new FormControl([]),
    period: new FormControl(''),
    color0: new FormControl('#000000'),
    dry_marker: new FormControl('x'),
    dry_marker_length: new FormControl(1),
    chart_height: new FormControl(300)
  });

  public navbar: any = null;
  public controls: any = null;

  public data: any = null;
  public yAxisDescription: any = '';

  public xAxis: any = [];
  public yAxis: any = [];

  public chart: any = {};

  public chartSeries: any = [];
  public chartSeriesType: string = 'line';
  public chartLegends: any = [];

  public chartLegendsTop: number = 50;
  public chartLegendsBottom: number = 0;

  public markers = Markers;

  public min: number = null;
  public max: number = null;

  public messageReturn: any = { text: '', status: false };

  public func = fn;

  constructor(private chartServiceApi: ChartServiceApi, private fileLoaderService: FileLoaderService) {}

  /**
   * Método de inicialização do componente.
   * Configura controles, carrega marcadores e obtém os dados do gráfico.
   */
  ngOnInit(): void {
    this.controls = this.formChart.controls;
    this.navbar = fn.constToObject(navBar);
    this.navbar = fn.objToArray(this.navbar);
    this.controls['instrument'].setValue([this.instrumentInfo]);
    this.loadMarkers();
    this.getChart();
  }

  /**
   * Altera o período selecionado e redefine as configurações do gráfico.
   * @param {any} period - Período selecionado.
   */
  changePeriod(period) {
    this.controls['period'].setValue(period);
    this.resetConfigurations();
  }

  //Obtém os dados do gráfico com base nos parâmetros selecionados.
  getChart() {
    this.messageReturn.text = '';
    this.messageReturn.status = false;

    let params = {};
    params['InstrumentId'] = this.controls['instrument'].value.map((object) => object.id)[0];

    if (this.controls['period'].value != '') {
      params['period'] = this.controls['period'].value;
    }

    this.chartServiceApi.getAbsoluteVariation(params).subscribe((resp) => {
      let dados: any = resp;

      if (dados) {
        dados = dados.body === undefined ? dados : dados.body;
        this.formatData(dados);
      } else {
        this.messageReturn.text = MessageInputInvalid.NoChart;
        this.messageReturn.status = true;

        setTimeout(() => {
          this.messageReturn.status = false;
        }, 4000);

        this.resetConfigurations();
      }
    });
  }

  /**
   * Constrói o gráfico a partir dos dados fornecidos.
   * @param {any} data - Dados do gráfico.
   */
  constructChart(data) {
    this.constructXAxis(data);
  }

  /**
   * Constrói o eixo X do gráfico com base nas datas dos dados fornecidos.
   * @param {any} data - Dados do gráfico.
   */
  constructXAxis(data) {
    let dates = [];

    //Instrument
    for (const key in data['instruments']) {
      if (data['instruments'].hasOwnProperty(key)) {
        dates = data['instruments'][key].map((item) => moment(item.date).format('DD/MM/YYYY HH:mm:ss'));
        this.xAxis.push(...dates);
      }
    }

    //Níveis de segurança
    for (const key in data['security-levels']) {
      if (data['security-levels'].hasOwnProperty(key)) {
        dates = data['security-levels'][key].map((item) => moment(item.date).format('DD/MM/YYYY HH:mm:ss'));
        this.xAxis.push(...dates);
      }
    }

    const orderedDates = this.xAxis
      .map((dateTimeString) => {
        // Convertendo o formato brasileiro para o formato americano
        const [datePart, timePart] = dateTimeString.split(' ');
        const [dia, mes, ano] = datePart.split('/').map(Number);
        const [hora, minuto, segundo] = timePart.split(':').map(Number);
        return new Date(ano, mes - 1, dia, hora, minuto, segundo);
      })
      .sort((a, b) => a - b)
      .map((date) => date.toLocaleString('pt-BR').replace(',', '')); // Usando toLocaleString para considerar a data e a hora

    this.xAxis = orderedDates;
    this.xAxis = this.uniqueArray(this.xAxis);

    this.constructSeries(data);
  }

  /**
   * Constrói as séries de dados do gráfico.
   * @param {any} data - Dados do gráfico.
   */
  constructSeries(data) {
    const datesObject = {};

    for (const date of this.xAxis) {
      datesObject[date] = null;
    }

    let series = {};
    this.chartSeries = [];
    this.chartLegends = [];

    this.controls['chart_height'].setValue(Math.floor(this.chartLegendsTop) + this.controls['chart_height'].value);
    this.yAxisDescription = 'colocar o titulo no .ts';

    series['Cota topo'] = { ...datesObject };
    series['Cota base'] = { ...datesObject };
    series['Cota'] = { ...datesObject };

    const securityLevels = Object.keys(data['security-levels']);
    securityLevels.forEach((securityLevel) => {
      series[securityLevel] = { ...datesObject };
    });

    for (const key in data['instruments']) {
      data['instruments'][key].forEach((element) => {
        let date = moment(element.date).format('DD/MM/YYYY HH:mm:ss');
        const quota = element.quota;
        const top_quota = element.top_quota;
        const base_quota = element.base_quota;
        if (element.dry) {
          let markerDry = fn.findIndexInArrayofObject(this.markers, 'marker', this.controls['dry_marker'].value, 'marker', true);
          let config = {};
          config['value'] =
            series.hasOwnProperty('Cota') && series['Cota'].hasOwnProperty(date) ? (typeof quota == 'string' ? parseFloat(quota) : quota) : null;
          config['symbol'] = `image://${this.getSvgWithReplacedValue(markerDry.text, this.controls['color0'].value)}`;
          config['symbolSize'] = parseInt(this.controls['dry_marker_length'].value) * 120;
          series['Cota'][date] = config;
        } else {
          series['Cota'][date] =
            series.hasOwnProperty('Cota') && series['Cota'].hasOwnProperty(date) ? (typeof quota == 'string' ? parseFloat(quota) : quota) : null;
        }

        series['Cota topo'][date] =
          series.hasOwnProperty('Cota topo') && series['Cota topo'].hasOwnProperty(date)
            ? typeof top_quota == 'string'
              ? parseFloat(top_quota)
              : top_quota
            : null;
        series['Cota base'][date] =
          series.hasOwnProperty('Cota base') && series['Cota base'].hasOwnProperty(date)
            ? typeof base_quota == 'string'
              ? parseFloat(base_quota)
              : base_quota
            : null;
      });
    }

    const itemSeries = {
      name: 'Cota',
      type: 'line',
      data: Object.values(series['Cota']),
      connectNulls: true
    };
    this.chartSeries.push(itemSeries);
    this.chartLegends.push('Cota');
    this.defineMinMax(itemSeries.data);

    const itemSeriesCT = {
      name: 'Cota topo',
      type: 'line',
      data: Object.values(series['Cota topo']),
      connectNulls: true
    };
    this.chartSeries.push(itemSeriesCT);
    this.chartLegends.push('Cota topo');
    this.defineMinMax(itemSeriesCT.data);

    const itemSeriesCB = {
      name: 'Cota base',
      type: 'line',
      data: Object.values(series['Cota base']),
      connectNulls: true
    };
    this.chartSeries.push(itemSeriesCB);
    this.chartLegends.push('Cota base');
    this.defineMinMax(itemSeriesCB.data);

    for (const key in data['security-levels']) {
      data['security-levels'][key].forEach((element) => {
        let date = moment(element.date).format('DD/MM/YYYY HH:mm:ss');
        const value = element.value;
        series[key][date] = series.hasOwnProperty(key) && series[key].hasOwnProperty(date) ? (typeof value == 'string' ? parseFloat(value) : value) : null;
      });

      const itemSeries = {
        name: key,
        type: 'line',
        data: Object.values(series[key]),
        connectNulls: true,
        itemStyle: {
          color: data['security-levels'][key][0].color
        }
      };
      this.chartLegends.push(key);
      this.chartSeries.push(itemSeries);
      this.defineMinMax(itemSeries.data);
    }
    this.constructYAxis();
  }

  //Constrói o eixo Y do gráfico com base na descrição e nos dados das séries.
  constructYAxis() {
    this.yAxis = [];

    let itemYAxis = {
      name: this.yAxisDescription.label,
      type: 'value',
      axisLine: {
        show: true
      },
      nameRotate: 90,
      nameLocation: 'center',
      nameGap: 55,
      nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
      alignTicks: true,
      axisLabel: {
        formatter: function (value, index) {
          return value.toFixed(1);
        }
      },
      show: true,
      interval: Math.ceil((this.max - this.min) / 25),
      min: this.min,
      max: this.max
    };

    this.yAxis.push(itemYAxis);

    this.generateChart();
  }

  /**
   * Define os valores mínimos e máximos dos dados da série para o eixo Y.
   * @param {any} array - Dados da série.
   * @param {number|null} index - Índice da série, se aplicável.
   */
  defineMinMax(array: any, index = null) {
    array = array.filter((item) => item !== null);

    array = array.map((item) => {
      if (item != null) {
        return typeof item == 'number' ? item : item.value;
      }
    });

    const min = Math.min(...array);
    const max = Math.max(...array);
    let previous = min - (min % 10);
    let next = max + (10 - (max % 10));

    this.min = this.min == null ? previous : this.min;
    this.min = Math.min(this.min, previous);
    previous = this.min;

    this.max = this.max == null ? next : this.max;
    this.max = Math.max(this.max, next);
    next = this.max;
  }

  /**
   * Remove elementos repetidos de um array.
   * @param {any[]} array - Array de datas.
   * @returns {any[]} - Array com elementos únicos.
   */
  uniqueArray(array) {
    const uniqueArray = [];
    const seeDates = {};

    for (const date of array) {
      if (!seeDates[date]) {
        uniqueArray.push(date);
        seeDates[date] = true;
      }
    }

    return uniqueArray;
  }

  //Carrega os marcadores para o gráfico a partir dos arquivos SVG.
  loadMarkers() {
    this.markers.forEach((marker, index) => {
      this.loadFileContent('assets/markers/', marker.icon, index);
    });
  }

  /**
   * Carrega o conteúdo de um arquivo a partir de um caminho específico.
   * @param {string} path - Caminho para o arquivo.
   * @param {string} fileName - Nome do arquivo.
   * @param {number} index - Índice do marcador, opcional.
   */
  loadFileContent(path: string, fileName: string, index = 0) {
    this.fileLoaderService.loadFile(path, fileName).subscribe(
      (data) => {
        // this.markers[index].text = data;
      },
      (error) => {
        console.error('Erro ao carregar o arquivo:', error);
      }
    );
  }

  /**
   * Converte o arquivo SVG para texto e substitui suas cores.
   * @param {string} svg - Conteúdo SVG.
   * @param {string} color - Cor para substituição, padrão é preto.
   * @returns {string} - SVG convertido para base64.
   */
  getSvgWithReplacedValue(svg, color = '#000000') {
    svg = this.replaceMultipleOccurrences(svg, ['rgb(0,0,0)', 'rgb(101,101,101)'], [color, color]);
    const svgBase64 = btoa(svg);
    return `data:image/svg+xml;base64,${svgBase64}`;
  }

  /**
   * Substitui todas as ocorrências de uma string por outra em um texto.
   * @param {string} text - Texto original.
   * @param {string[]} oldValues - Array de valores a serem substituídos.
   * @param {string[]} newValues - Array de novos valores para substituição.
   * @returns {string} - Texto com as substituições aplicadas.
   */
  replaceMultipleOccurrences(text, oldValues, newValues) {
    if (oldValues.length !== newValues.length) {
      throw new Error('Os arrays devem ter o mesmo comprimento.');
    }

    let newText = text;
    for (let i = 0; i < oldValues.length; i++) {
      const oldValue = oldValues[i];
      const newValue = newValues[i];
      newText = newText.split(oldValue).join(newValue);
    }

    return newText;
  }

  /**
   * Gera uma cor hexadecimal aleatória.
   * @returns {string} - Cor hexadecimal.
   */
  randomHexColor() {
    const randomColorComponent = () => {
      const component = Math.floor(Math.random() * 256); //Valor aleatorio entre 0  e 255
      return component.toString(16).padStart(2, '0'); //Converte para hexadecimal e completa com zero se necessario
    };

    const r = randomColorComponent();
    const g = randomColorComponent();
    const b = randomColorComponent();

    return `#${r}${g}${b}`;
  }

  /**
   * Formata os dados recebidos e os prepara para a construção do gráfico.
   * @param {any} $dados - Dados recebidos para formatação.
   */
  formatData($dados) {
    let dados: any = {
      instruments: {},
      'security-levels': {},
      statistics: {}
    };

    dados.instruments[$dados.instrument_identifier] = [];
    dados['security-levels']['Nível de Atenção'] = [];
    dados['security-levels']['Nível de Alerta'] = [];
    dados['security-levels']['Nível de Emergência'] = [];

    $dados.readings.forEach((reading, index) => {
      dados.instruments[$dados.instrument_identifier][index] = {
        quota: reading.quota,
        top_quota: reading.top_quota,
        base_quota: reading.base_quota,
        dry: reading.dry ? 1 : 0,
        date: reading.date
      };

      dados['security-levels']['Nível de Atenção'].push({
        value: reading.attention_level,
        color: '#ffc107',
        date: reading.date
      });
      dados['security-levels']['Nível de Alerta'].push({
        value: reading.alert_level,
        color: '#fd7e14',
        date: reading.date
      });
      dados['security-levels']['Nível de Emergência'].push({
        value: reading.emergency_level,
        color: '#dc3545',
        date: reading.date
      });
    });

    dados['statistics']['absolute_variation'] = {
      value: $dados.statistics.absolute_variation.toFixed(2),
      color: $dados.statistics.absolute_variation == 0 ? 'orange' : $dados.statistics.absolute_variation > 0 ? '#34b575' : 'red'
    };

    dados['statistics']['maximum'] =
      ($dados.statistics.maximum.dry ? 'Seco -> ' : '') +
      $dados.statistics.maximum.quota +
      ' m (' +
      moment($dados.statistics.maximum.date).format('DD/MM/YYYY') +
      ')';
    dados['statistics']['minimum'] =
      ($dados.statistics.minimum.dry ? 'Seco -> ' : '') +
      $dados.statistics.minimum.quota +
      ' m (' +
      moment($dados.statistics.minimum.date).format('DD/MM/YYYY') +
      ')';
    dados['statistics']['average'] = $dados.statistics.average.toFixed(2) + ' m';
    dados['statistics']['standard_deviation'] = $dados.statistics.standard_deviation.toFixed(2) + ' m';
    dados['statistics']['last_reading'] =
      ($dados.statistics.last_reading.dry ? 'Seco -> ' : '') +
      $dados.statistics.last_reading.quota +
      ' m (' +
      moment($dados.statistics.last_reading.date).format('DD/MM/YYYY HH:MM:SS') +
      ')';

    this.data = dados;
    this.constructChart(dados);
  }

  //Gera e configura o gráfico usando as opções definidas.
  generateChart() {
    this.chart['options'] = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegends,
        icon: 'rect',
        left: 'center',
        top: 'bottom'
      },
      grid: {
        containLabel: true,
        top: this.chartLegendsTop,
        left: 50,
        right: 50,
        height: this.controls['chart_height'].value
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        interval: 0.1,
        boundaryGap: false,
        data: this.xAxis,
        axisLabel: {
          interval: Math.floor(this.xAxis.length / 35), // Define o intervalo para exibir todos os valores do eixo X
          rotate: 60
        }
      },
      yAxis: this.yAxis,
      series: this.chartSeries
    };
  }

  //Redefine as configurações do gráfico e carrega os dados novamente.
  resetConfigurations() {
    this.yAxisDescription = '';
    this.xAxis = [];
    this.yAxis = [];
    this.chartSeries = [];
    this.chartLegends = [];
    this.chartLegendsTop = 50;
    this.chartLegendsBottom = 0;
    this.min = null;
    this.max = null;

    this.controls['chart_height'].setValue(300);
    this.getChart();
  }
}
