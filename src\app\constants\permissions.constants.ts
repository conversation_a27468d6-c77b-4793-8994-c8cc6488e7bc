import { Rotas } from './rotas.constants';

enum accessLevel {
  SuperSuporte = 'super-support',
  SuperAdministrador = 'super-administrator',
  Administrator = 'administrator',
  Operador = 'operator',
  Suporte = 'support',
  Auditor = 'auditor'
}

enum actions {
  Cadastrar = 'create',
  CriarSimulador = 'createSimulation', //Tela de Estabilidade
  ConsultarSecoes = 'consult',
  Editar = 'edit',
  EditarSimulacao = 'editSimulation', //Tela de Estabilidade
  Excluir = 'delete',
  FatoresDeSeguranca = 'stabilityAnalysis', //Tela de Estabilidade
  Grafico = 'chart',
  GraficosEstabilidade = 'stabilityCharts', //Tela de Estabilidade
  Imagens = 'images',
  ImagensEstabilidade = 'stabilityImages', //Tela de Estabilidade
  FichaDeInspecaoFIE = 'fie', //Inspeções
  FichaDeInspecaoFIR = 'fir', //Inspeções
  FichaDeInspecaoEOR = 'eor', //Inspeções
  FichaDeInspecaoRISR = 'risr', //Inspeções
  FichaDeInspecaoEmBranco = 'embranco', //Inspeções
  HistoricoEstrutura = 'history',
  HistoricoInstrumentacao = 'history',
  HistoricoLeituras = 'history',
  HistoricoMaterial = 'history',
  HistoricoSecao = 'history',
  Layers = 'layers', // Tela de Estrutura
  Listar = 'list',
  MapaEstabilidade = 'map',
  MapaInstrumentacao = 'map',
  NovoPlanoDeAcao = 'newActionPlan', //Inspeções
  Pacotes = 'packages', //Tela de Estabilidade
  ResultadoSimulacao = 'resultSimulation', //Tela de Estabilidade
  Simulador = 'stabilitySimulations', //Tela de Estabilidade
  Visualizar = 'view',
  Validar = 'validate' //Inspeções - Planos de ação
}

const specialActions = {
  [Rotas.MapaEstabilidade]: 'map',
  [Rotas.FatoresDeSeguranca]: 'stabilityAnalysis',
  [Rotas.GraficosEstabilidade]: 'stabilityCharts',
  [Rotas.Simulador]: 'stabilitySimulations',
  [Rotas.CriarSimulador]: 'createSimulation',
  [Rotas.EditarSimulacao]: 'editSimulation',
  [Rotas.ResultadoSimulacao]: 'resultSimulation'
};

const acl = {
  [accessLevel.SuperSuporte]: {
    [Rotas.Notificações]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Clientes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Estabilidade]: {
      [actions.Listar]: true,
      [actions.Pacotes]: true,
      [actions.MapaEstabilidade]: true,
      [actions.FatoresDeSeguranca]: true,
      [actions.ImagensEstabilidade]: true,
      [actions.GraficosEstabilidade]: true,
      [actions.Simulador]: true,
      [actions.CriarSimulador]: true,
      [actions.EditarSimulacao]: true,
      [actions.ResultadoSimulacao]: true
    },
    [Rotas.Estruturas]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.Imagens]: true,
      [actions.Layers]: true,
      [actions.HistoricoEstrutura]: true,
      viewResponsibles: true,
      editResponsibles: true,
      viewDatasheet: true,
      editDatasheet: true
    },
    [Rotas.GruposInstrumentos]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true
    },
    [Rotas.HistoricoNotificacoes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Imagens]: {
      [actions.Listar]: true
    },
    [Rotas.Inspecoes]: {
      [actions.Cadastrar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.FichaDeInspecaoFIE]: true,
      [actions.FichaDeInspecaoFIR]: true,
      [actions.FichaDeInspecaoEOR]: true,
      [actions.FichaDeInspecaoRISR]: true,
      [actions.FichaDeInspecaoEmBranco]: true,
      [actions.NovoPlanoDeAcao]: true,
      [actions.Editar]: true,
      [actions.Validar]: true,
      [actions.Excluir]: true
    },
    [Rotas.Instrumentacao]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoInstrumentacao]: true,
      [actions.MapaInstrumentacao]: true,
      [actions.ConsultarSecoes]: true,
      [actions.Grafico]: true,
      [actions.Imagens]: true
    },
    [Rotas.Leituras]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoLeituras]: true
    },
    [Rotas.Materiais]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Excluir]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoMaterial]: true
    },
    [Rotas.Relatorios]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Secoes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoSecao]: true
    },
    [Rotas.Unidades]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },
    [Rotas.Usuarios]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },

    Nivel: 0
  },
  [accessLevel.SuperAdministrador]: {
    [Rotas.Notificações]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Clientes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false
    },
    [Rotas.Estabilidade]: {
      [actions.Listar]: true,
      [actions.Pacotes]: true,
      [actions.MapaEstabilidade]: true,
      [actions.FatoresDeSeguranca]: true,
      [actions.ImagensEstabilidade]: true,
      [actions.GraficosEstabilidade]: true,
      [actions.Simulador]: true,
      [actions.EditarSimulacao]: true,
      [actions.ResultadoSimulacao]: true
    },
    [Rotas.Estruturas]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.Imagens]: true,
      [actions.Layers]: false,
      [actions.HistoricoEstrutura]: true,
      viewResponsibles: true,
      editResponsibles: true,
      viewDatasheet: true,
      editDatasheet: true
    },
    [Rotas.GruposInstrumentos]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true
    },
    [Rotas.HistoricoNotificacoes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Imagens]: {
      [actions.Listar]: true
    },
    [Rotas.Inspecoes]: {
      [actions.Cadastrar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.FichaDeInspecaoFIE]: true,
      [actions.FichaDeInspecaoFIR]: true,
      [actions.FichaDeInspecaoEOR]: true,
      [actions.FichaDeInspecaoRISR]: true,
      [actions.FichaDeInspecaoEmBranco]: true,
      [actions.NovoPlanoDeAcao]: true,
      [actions.Editar]: true,
      [actions.Validar]: true,
      [actions.Excluir]: true
    },
    [Rotas.Instrumentacao]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoInstrumentacao]: true,
      [actions.MapaInstrumentacao]: true,
      [actions.ConsultarSecoes]: true,
      [actions.Grafico]: true,
      [actions.Imagens]: true
    },
    [Rotas.Leituras]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoLeituras]: true
    },
    [Rotas.Materiais]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Excluir]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoMaterial]: true
    },
    [Rotas.Relatorios]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Secoes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoSecao]: true
    },
    [Rotas.Unidades]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },
    [Rotas.Usuarios]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },

    Nivel: 1
  },
  [accessLevel.Administrator]: {
    [Rotas.Notificações]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Clientes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false
    },
    [Rotas.Estabilidade]: {
      [actions.Listar]: true,
      [actions.Pacotes]: true,
      [actions.MapaEstabilidade]: true,
      [actions.FatoresDeSeguranca]: true,
      [actions.ImagensEstabilidade]: true,
      [actions.GraficosEstabilidade]: true,
      [actions.Simulador]: true,
      [actions.EditarSimulacao]: true,
      [actions.ResultadoSimulacao]: true
    },
    [Rotas.Estruturas]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.Imagens]: true,
      [actions.Layers]: false,
      [actions.HistoricoEstrutura]: true,
      viewResponsibles: true,
      editResponsibles: true,
      viewDatasheet: true,
      editDatasheet: true
    },
    [Rotas.GruposInstrumentos]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true
    },
    [Rotas.HistoricoNotificacoes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Imagens]: {
      [actions.Listar]: true
    },
    [Rotas.Inspecoes]: {
      [actions.Cadastrar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.FichaDeInspecaoFIE]: true,
      [actions.FichaDeInspecaoFIR]: true,
      [actions.FichaDeInspecaoEOR]: true,
      [actions.FichaDeInspecaoRISR]: true,
      [actions.FichaDeInspecaoEmBranco]: true,
      [actions.NovoPlanoDeAcao]: true,
      [actions.Editar]: true,
      [actions.Validar]: true,
      [actions.Excluir]: true
    },
    [Rotas.Instrumentacao]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoInstrumentacao]: true,
      [actions.MapaInstrumentacao]: true,
      [actions.ConsultarSecoes]: true,
      [actions.Grafico]: true,
      [actions.Imagens]: true
    },
    [Rotas.Leituras]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoLeituras]: true
    },
    [Rotas.Materiais]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Excluir]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoMaterial]: true
    },
    [Rotas.Relatorios]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Secoes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoSecao]: true
    },
    [Rotas.Unidades]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },
    [Rotas.Usuarios]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },
    Nivel: 1
  },
  [accessLevel.Operador]: {
    [Rotas.Notificações]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Clientes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false
    },
    [Rotas.Estabilidade]: {
      [actions.Listar]: true,
      [actions.Pacotes]: true,
      [actions.MapaEstabilidade]: true,
      [actions.FatoresDeSeguranca]: true,
      [actions.ImagensEstabilidade]: true,
      [actions.GraficosEstabilidade]: true,
      [actions.Simulador]: true,
      [actions.EditarSimulacao]: true,
      [actions.ResultadoSimulacao]: true
    },
    [Rotas.Estruturas]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.Layers]: false,
      [actions.HistoricoEstrutura]: true,
      viewResponsibles: true,
      editResponsibles: false,
      viewDatasheet: true,
      editDatasheet: false
    },
    [Rotas.GruposInstrumentos]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true
    },
    [Rotas.HistoricoNotificacoes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Imagens]: {
      [actions.Listar]: true
    },
    [Rotas.Inspecoes]: {
      [actions.Cadastrar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.FichaDeInspecaoFIE]: true,
      [actions.FichaDeInspecaoFIR]: true,
      [actions.FichaDeInspecaoEOR]: true,
      [actions.FichaDeInspecaoRISR]: true,
      [actions.FichaDeInspecaoEmBranco]: true,
      [actions.NovoPlanoDeAcao]: true,
      [actions.Editar]: true,
      [actions.Validar]: true,
      [actions.Excluir]: true
    },
    [Rotas.Instrumentacao]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoInstrumentacao]: true,
      [actions.MapaInstrumentacao]: true,
      [actions.ConsultarSecoes]: true,
      [actions.Grafico]: true
    },
    [Rotas.Leituras]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoLeituras]: true
    },
    [Rotas.Materiais]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Excluir]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoMaterial]: true
    },
    [Rotas.Relatorios]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Secoes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoSecao]: true
    },
    [Rotas.Unidades]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },
    [Rotas.Usuarios]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false,
      [actions.Visualizar]: false
    },
    Nivel: 1
  },
  [accessLevel.Suporte]: {
    [Rotas.Notificações]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Clientes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false
    },
    [Rotas.Estabilidade]: {
      [actions.Listar]: true,
      [actions.Pacotes]: true,
      [actions.MapaEstabilidade]: true,
      [actions.FatoresDeSeguranca]: true,
      [actions.ImagensEstabilidade]: true,
      [actions.GraficosEstabilidade]: true,
      [actions.Simulador]: true,
      [actions.CriarSimulador]: true,
      [actions.EditarSimulacao]: true,
      [actions.ResultadoSimulacao]: true
    },
    [Rotas.Estruturas]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.Imagens]: true,
      [actions.Layers]: true,
      [actions.HistoricoEstrutura]: true,
      viewResponsibles: true,
      editResponsibles: true,
      viewDatasheet: true,
      editDatasheet: true
    },
    [Rotas.GruposInstrumentos]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true
    },
    [Rotas.HistoricoNotificacoes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Imagens]: {
      [actions.Listar]: true
    },
    [Rotas.Inspecoes]: {
      [actions.Cadastrar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.FichaDeInspecaoFIE]: true,
      [actions.FichaDeInspecaoFIR]: true,
      [actions.FichaDeInspecaoEOR]: true,
      [actions.FichaDeInspecaoRISR]: true,
      [actions.FichaDeInspecaoEmBranco]: true,
      [actions.NovoPlanoDeAcao]: true,
      [actions.Editar]: true,
      [actions.Validar]: true,
      [actions.Excluir]: true
    },
    [Rotas.Instrumentacao]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoInstrumentacao]: true,
      [actions.MapaInstrumentacao]: true,
      [actions.ConsultarSecoes]: true,
      [actions.Grafico]: true
    },
    [Rotas.Leituras]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoLeituras]: true
    },
    [Rotas.Materiais]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Excluir]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoMaterial]: true
    },
    [Rotas.Relatorios]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Secoes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoSecao]: true
    },
    [Rotas.Unidades]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },
    [Rotas.Usuarios]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false,
      [actions.Visualizar]: false
    },
    Nivel: 1
  },
  [accessLevel.Auditor]: {
    [Rotas.Notificações]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false
    },
    [Rotas.Clientes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false
    },
    [Rotas.Estabilidade]: {
      [actions.Listar]: true,
      [actions.Pacotes]: true,
      [actions.MapaEstabilidade]: true,
      [actions.FatoresDeSeguranca]: true,
      [actions.GraficosEstabilidade]: true,
      [actions.Simulador]: false,
      [actions.EditarSimulacao]: false,
      [actions.ResultadoSimulacao]: true
    },
    [Rotas.Estruturas]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.Layers]: false,
      [actions.HistoricoEstrutura]: false,
      viewResponsibles: false,
      editResponsibles: false,
      viewDatasheet: false,
      editDatasheet: false
    },
    [Rotas.HistoricoNotificacoes]: {
      [actions.Cadastrar]: true,
      [actions.Editar]: true,
      [actions.Listar]: true
    },
    [Rotas.Imagens]: {
      [actions.Listar]: true
    },
    [Rotas.Inspecoes]: {
      [actions.Cadastrar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.FichaDeInspecaoFIE]: false,
      [actions.FichaDeInspecaoFIR]: false,
      [actions.FichaDeInspecaoEOR]: false,
      [actions.FichaDeInspecaoRISR]: false,
      [actions.FichaDeInspecaoEmBranco]: false,
      [actions.NovoPlanoDeAcao]: false,
      [actions.Editar]: false,
      [actions.Validar]: false,
      [actions.Excluir]: false
    },
    [Rotas.Instrumentacao]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoInstrumentacao]: false,
      [actions.MapaInstrumentacao]: true,
      [actions.ConsultarSecoes]: true,
      [actions.Grafico]: true
    },
    [Rotas.Leituras]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoLeituras]: true
    },
    [Rotas.Materiais]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Excluir]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoMaterial]: false
    },
    [Rotas.Relatorios]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true
    },
    [Rotas.Secoes]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true,
      [actions.HistoricoSecao]: false
    },
    [Rotas.Unidades]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: true,
      [actions.Visualizar]: true
    },
    [Rotas.Usuarios]: {
      [actions.Cadastrar]: false,
      [actions.Editar]: false,
      [actions.Listar]: false,
      [actions.Visualizar]: false
    },
    Nivel: 1
  }
};

enum tabs {
  Geral = 'general-tab',
  Avancado = 'avanced-tab',
  Estabilidade = 'stability-tab',
  ConfiguracoesSlide2 = 'configurations-tab',
  FichaTecnica = 'datasheet-tab',
  Responsaveis = 'responsible-tab',
  Inspecoes = 'inspections-tab'
}

export enum ReportActions {
  Cadastrar = 'create',
  Editar = 'edit',
  Excluir = 'delete',
  Listar = 'listar'
}

export { acl, specialActions, accessLevel, tabs };
