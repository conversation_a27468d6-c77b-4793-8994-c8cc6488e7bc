// Modelo constitutivo
const constitutiveModel = [
  { name: '<PERSON><PERSON><PERSON>Coulomb', id: '1' },
  { name: 'Undrained', id: '2' },
  { name: 'No Strength (i.e water)', id: '3' },
  { name: 'Infinite Strength', id: '4' },
  { name: 'Shear/Normal Function', id: '5' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', id: '6' },
  { name: 'Generalized <PERSON><PERSON><PERSON><PERSON>', id: '7' },
  { name: 'Vertical Stress Ratio', id: '8' },
  { name: 'SHANSEP', id: '9' }
];

// Tipo de coesao
const typeCohesion = [
  { name: 'Constant', id: '1' },
  { name: 'F(Depth from Top of Layer)', id: '2' },
  { name: 'F(Depth from Horizontal Datum)', id: '3' },
  { name: 'F(Distance to Slope)', id: '4' }
];

//Definir resistencia usando
const strengthDefinition = [
  { name: 'G<PERSON>, mi, D', id: '1' },
  { name: 'mb, s, a', id: '2' }
];

//Tipo de historico de tensoes
const stressHistoryType = [
  { name: 'Overconsolidation Ratio', id: '1' },
  { name: 'Preconsolidation Pressure', id: '2' }
];

const stressHistoryMethod = [
  {
    1: [
      { name: 'Constante (kPa)', id: '1' },
      { name: 'Pontos por profundidade e RSA', id: '2' },
      { name: 'Pontos por elevação e RSA', id: '3' }
    ],
    2: [
      { name: 'Constante (kPa)', id: '1' },
      { name: 'Pontos por profundidade e RSA', id: '2' },
      { name: 'Pontos por elevação e PC', id: '3' }
    ]
  }
];

const preconsolidationPressure = [
  { name: 'Constante (kPa)', id: '1' },
  { name: 'Pontos por profundidade e RSA', id: '2' },
  { name: 'Pontos por elevação e PC', id: '3' }
];

const waterSurface = [
  { name: 'Nenhuma', id: '1' },
  { name: 'Linha Freática', id: '2' },
  { name: 'Linha Piezométrica 1', id: '3' }
];

const hu = [
  { name: 'Custom', id: '1' },
  { name: 'Auto', id: '2' }
];

export { constitutiveModel, typeCohesion, strengthDefinition, stressHistoryType, stressHistoryMethod, preconsolidationPressure, waterSurface, hu };
