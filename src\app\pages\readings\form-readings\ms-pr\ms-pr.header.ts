const msPrTableHeader = [
  {
    label: 'Selecionar',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['select'],
    type: 'check'
  },
  {
    label: 'ID',
    width: '60px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['reading_search_identifier']
  },
  {
    label: 'Instrumento',
    width: '50%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['identifier']
  },
  {
    label: 'Data e hora',
    width: '50%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['date_format']
  },
  {
    label: 'Referência',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['is_referential_reading']
  },
  {
    label: 'Datum',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['datum']
  },
  {
    label: 'Coordenada E (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['east_coordinate']
  },
  {
    label: 'Coordenada N (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['north_coordinate']
  },
  {
    label: 'Cota (m)',
    width: '100px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['quota']
  },
  {
    label: 'Deslocamento E (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['east_displacement']
  },
  {
    label: 'Deslocamento N (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['north_displacement']
  },
  {
    label: 'Deslocamento Z (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['z_displacement']
  },
  {
    label: 'Deslocamento planimétrico total (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['total_planimetric_displacement']
  },
  {
    label: 'Deslocamento A (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['a_displacement']
  },
  {
    label: 'Deslocamento B (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['b_displacement']
  },
  {
    label: 'Ações',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['actionCustom']
  }
];

export { msPrTableHeader };
