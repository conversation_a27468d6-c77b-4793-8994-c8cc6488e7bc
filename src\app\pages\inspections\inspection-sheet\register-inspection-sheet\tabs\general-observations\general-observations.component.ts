import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, QueryList, SimpleChanges, ViewChildren } from '@angular/core';
import { FormBuilder, FormGroup, FormArray } from '@angular/forms';
import { ChangeDetectorRef } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-general-observations',
  templateUrl: './general-observations.component.html',
  styleUrls: ['./general-observations.component.scss']
})
export class GeneralObservationsComponent implements OnInit, OnChanges {
  @ViewChildren('fileInput') fileInputs!: QueryList<ElementRef>;
  @Input() public inspectionSheetType: number = null;
  @Input() public status: number = null;
  @Input() public locked: boolean = false;
  @Input() public view: boolean = false;
  @Output() public formChanged = new EventEmitter<any>();

  notesForm: FormGroup = this.fb.group({
    notes: this.fb.array([]) // Array para as notas dinâmicas
  });

  public fileSelected: boolean = false; // Controla a exibição dos botões
  public fileNameView: string = ''; // Nome do arquivo selecionado
  public fileUrl: string = ''; // URL para download do arquivo

  constructor(private fb: FormBuilder, private cdr: ChangeDetectorRef, private sanitizer: DomSanitizer) {}

  ngOnInit(): void {}

  /**
   * Detecta e processa mudanças nos inputs vinculados ao componente.
   * @param {SimpleChanges} changes - Contém as mudanças detectadas nos inputs do componente.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.status || changes.locked) {
      this.toggleForm();
    }
  }

  /**
   * Obtém o FormArray de notas.
   * @returns {FormArray} - Lista de notas cadastradas.
   */
  get notes(): FormArray {
    return this.notesForm.get('notes') as FormArray;
  }

  /**
   * Adiciona uma nova nota ao formulário.
   */
  addNote(): void {
    if (this.notesForm.disabled) {
      return;
    }
    this.notes.push(
      this.fb.group({
        id: [null], // ID da nota (caso exista)
        note: [''], // Texto da nota
        file: this.fb.group({
          base64: [null], // Arquivo em base64
          name: [null] // Nome do arquivo
        })
      })
    );
  }

  /**
   * Remove uma nota do formulário pelo índice especificado.
   * @param {number} index - Índice da nota a ser removida.
   */
  removeNote(index: number): void {
    if (this.notesForm.disabled) {
      return;
    }
    this.notes.removeAt(index);
    this.formChanged.emit();
    if (this.notes.length === 0) {
      this.addNote();
    }
  }

  /**
   * Processa a seleção de um arquivo e converte para Base64.
   * @param {Event} event - Evento gerado pela seleção de arquivo no input.
   * @param {number} index - Índice da nota associada ao arquivo.
   */
  onFileSelected(event: Event, index: number): void {
    if (this.notesForm.disabled) {
      return;
    }
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      const reader = new FileReader();

      reader.onload = () => {
        const base64 = reader.result as string;
        const noteGroup = this.notes.at(index);
        const safeUrl = this.prepareDownloadUrl(base64);
        noteGroup.get('file')?.patchValue({ base64: safeUrl, name: file.name });
        this.triggerSave();
      };

      reader.readAsDataURL(file);
    }
  }

  /**
   * Configura os dados recebidos do componente pai no formulário de notas.
   * @param {any} dados - Dados das notas.
   */
  setData(dados: any): void {
    const notesData = dados.notes || [];
    this.notes.clear(); // Limpa o FormArray antes de popular

    if (notesData.length === 0) {
      this.addNote();
    }

    notesData.forEach((note: any) => {
      let base64Value = note.file?.base64 || null;

      if (base64Value && !base64Value.startsWith('data:')) {
        const decode = fn.base64Extension(base64Value.slice(0, 100));
        if (decode.mimeType) {
          base64Value = `data:${decode.mimeType};base64,${base64Value}`;
        }
      }

      const safeBase64 = base64Value ? this.sanitizer.bypassSecurityTrustResourceUrl(base64Value) : null;

      this.notes.push(
        this.fb.group({
          id: [note.id], // ID da nota
          note: [note.note], // Texto da nota
          file: this.fb.group({
            base64: [safeBase64], // Base64 seguro com prefixo correto
            name: [note.file?.name || null] // Nome do arquivo
          })
        })
      );
    });

    this.cdr.detectChanges(); // Força a detecção de mudanças
    this.toggleForm();
  }

  /**
   * Remove o arquivo anexado a uma nota específica.
   * @param {number} index - Índice da nota a ser modificada.
   */
  removeFile(index: number): void {
    if (this.notesForm.disabled) {
      return;
    }
    const noteGroup = this.notes.at(index);
    noteGroup.get('file')?.reset(); // Remove os dados do arquivo

    // Localiza e limpa o valor do input file correspondente
    const fileInput = this.fileInputs.toArray()[index]; // Obtém a referência do input pelo índice
    if (fileInput) {
      fileInput.nativeElement.value = ''; // Limpa o texto do input
    }
    this.triggerSave();
  }

  /**
   * Converte uma string Base64 em uma URL segura para download.
   * @param {string} base64 - String Base64 a ser convertida.
   * @returns {SafeResourceUrl} - URL segura para exibição do arquivo.
   */
  prepareDownloadUrl(base64: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(base64);
  }

  /**
   * Verifica se um arquivo é um PDF.
   * @param {string | null} fileName - Nome do arquivo.
   * @returns {boolean} - Retorna `true` se o arquivo for um PDF, caso contrário, `false`.
   */
  isPdf(fileName: string | null): boolean {
    return fileName?.toLowerCase().endsWith('.pdf') || false;
  }

  /**
   * Abre um arquivo codificado em Base64 em uma nova aba do navegador.
   * @param {any} base64 - String Base64 do arquivo.
   */
  openFile(base64: any): void {
    fn.openInNewTab(base64, this.sanitizer);
  }

  /**
   * Dispara o evento de salvamento ao remover o foco de um campo do formulário.
   * @param {string} controlName - Nome do campo modificado.
   * @param {number} [index] - Índice da nota no FormArray, se aplicável.
   */
  onBlur(controlName: string, index?: number): void {
    if (this.notesForm.disabled) {
      return;
    }

    const control = this.notes.at(index).get(controlName);
    if (control?.dirty) {
      this.triggerSave();
    }
  }

  /**
   * Emite o evento de salvamento quando alterações são detectadas no formulário.
   */
  triggerSave(): void {
    this.formChanged.emit();
  }

  /**
   * Alterna entre habilitar ou desabilitar o formulário de notas.
   */
  toggleForm() {
    const isLocked = [2, 3].includes(this.status) || this.locked;
    if (isLocked) {
      this.notesForm.disable();
    } else {
      this.notesForm.enable();
    }
  }
}
