<ng-template #modalProfile let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title" id="modal-profile-title">
      <i class="fas fa-id-card"></i>
      Meu <PERSON>
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <div class="modal-body">
    <ul>
      <li><strong>Usuário: </strong>{{ user.username }}</li>
      <li><strong>E-mail: </strong>{{ user.email }}</li>
      <li><strong>Idioma: </strong>{{ user.locale.label }}</li>
      <li><strong>Permissão: </strong>{{ user.role }}</li>
    </ul>
  </div>
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-green'"
      [label]="'Alterar <PERSON>ha'"
      [icon]="'fa fa-unlock-alt'"
      (click)="alterarSenha()"
      data-bs-target="modalAlterarSenha"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-blue'"
      [label]="'Editar Cadastro'"
      [icon]="'fa fa-pencil'"
      (click)="editProfile()"
      data-bs-target="modalEditProfile"
      ><small id="numeroNotificacoes"></small>
    </app-button>
    <app-button
      [class]="'btn-logisoil-gray'"
      [label]="'Fechar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>

<!-- Modal Editar Cadastro -->
<ng-template #modalEditProfile let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">
      <span class="custom-icon"> <i class="fas fa-id-card"></i> </span>
      Editar Cadastro
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <form [formGroup]="formProfile" (ngSubmit)="alterarProfile()">
    <div class="modal-body">
      <div class="alert alert-success mt-0" role="alert" *ngIf="message.status">
        {{ message.text }}
      </div>
      <div class="col-md-12">
        <label>Usuário:</label>
        <input type="text" formControlName="username" class="form-control" />
      </div>
      <div class="col-md-12 mt-1">
        <label>Endereço de e-mail:</label>
        <input
          type="text"
          formControlName="email_address"
          class="form-control"
        />
      </div>
      <div class="col-md-12 mt-1">
        <label>Nome:</label>
        <input type="text" formControlName="first_name" class="form-control" />
      </div>
      <div class="col-md-12 mt-1">
        <label>Sobrenome:</label>
        <input type="text" formControlName="surname" class="form-control" />
      </div>
      <div class="col-md-12 mt-1">
        <label>Idioma:</label>
        <select class="form-select" formControlName="locale">
          <option value="">Selecione...</option>
          <ng-container *ngFor="let item of locales">
            <option [value]="item.id">
              {{ item.label }}
            </option>
          </ng-container>
        </select>
      </div>
    </div>

    <div class="modal-footer">
      <app-button
        [class]="'btn-logisoil-green'"
        [label]="'Salvar'"
        [icon]="'fa fa-floppy-o'"
        [type]="false"
      >
      </app-button>
      <app-button
        [class]="'btn-logisoil-red'"
        [label]="'Cancelar'"
        (click)="c('Close click')"
      >
      </app-button>
    </div>
  </form>
</ng-template>
<!-- Modal Editar Cadastro -->

<!-- Modal Alterar Senha -->
<ng-template #modalAlterarSenha let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">
      <span class="custom-icon"> <i class="fas fa-unlock-alt"></i> </span>
      Alterar Senha
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>

  <div class="modal-body">
    <div class="alert alert-success mt-4" role="alert" *ngIf="message.status">
      {{ message.text }}
    </div>
    Você receberá um e-mail para criação de nova senha. <br />
    Para prosseguir, clique em "Confirmar".
  </div>

  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-green'"
      [label]="'Confirmar'"
      [icon]="'fa fa-check'"
      [type]="false"
      (click)="enviarEmail()"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-red'"
      [label]="'Cancelar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>
<!-- Modal Alterar Senha -->
