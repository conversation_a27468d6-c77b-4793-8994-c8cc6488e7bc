import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-mohr-coloumb',
  templateUrl: './mohr-coloumb.component.html',
  styleUrls: ['./mohr-coloumb.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class MohrColoumbComponent implements OnInit, OnChanges {
  @Input() public data: any = null;
  @Input() public view: boolean = false;

  public formMohrColoumb: FormGroup = new FormGroup({
    cohesion: new FormControl('', [Validators.required]),
    friction_angle: new FormControl('', [Validators.required]),
    tensile_strength: new FormControl({ value: null, disabled: true }),
    is_tensile_strength: new FormControl(false)
  });

  public message: any = [{ text: '', status: false }];

  public func = fn;

  constructor(public formService: FormService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  splitData($dados) {
    this.formMohrColoumb.controls['cohesion'].setValue($dados.cohesion);
    this.formMohrColoumb.controls['friction_angle'].setValue($dados.friction_angle);

    if ($dados.tensile_strength != null) {
      this.formMohrColoumb.controls['is_tensile_strength'].setValue(true);
      this.formMohrColoumb.controls['tensile_strength'].setValue($dados.tensile_strength);
      this.formService.checkboxControlValidate(this.formMohrColoumb, 'tensile_strength');
    }

    if (this.view) {
      this.formMohrColoumb.disable();
    }
  }

  validate() {
    let formValid = this.formService.validateForm(this.formMohrColoumb);

    if (!formValid) {
      this.formMohrColoumb.markAllAsTouched();
    }

    return formValid;
  }
}
