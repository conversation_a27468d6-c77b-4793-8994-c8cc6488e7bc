import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup } from '@angular/forms';

import { MessageCadastro, MessagePadroes, ModalConfirm } from 'src/app/constants/message.constants';

import { ReportActions } from 'src/app/constants/permissions.constants';
import { TypeReports as SubjectType } from 'src/app/constants/reports.constants';
import { ReportsService as ReportsServiceApi } from 'src/app/services/api/reports.service';
import { UserService } from 'src/app/services/user.service';

import { NgxSpinnerService } from 'ngx-spinner';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-list-reports',
  templateUrl: './list-reports.component.html',
  styleUrls: ['./list-reports.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListReportsComponent implements OnInit {
  @ViewChild('modalConfirm') ModalConfirm: any;

  public formFilter: FormGroup = new FormGroup({
    SubjectType: new FormControl(0)
  });

  public subjectType: any = SubjectType;

  public controls: any = [];

  public tableHeader: any = [
    {
      label: 'ID',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Título',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['title']
    },
    {
      label: 'Frequência',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['frequency']
    },
    {
      label: 'Dias a analisar',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['days_to_analyze']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['miniDashboard']
    }
  ];

  public tableData: any = [];

  public reportRequest: any = {
    structure: {
      id: null
    },
    title: null,
    responsible_name: null,
    destination_emails: [null],
    subject_type: null,
    periodicity_type: null,
    days_to_analyze: null,
    daily_report_parameters: {
      daily_periodicity: null
    },
    weekly_report_parameters: {
      weekly_emission_days: [null]
    },
    monthly_report_parameter: {
      monthly_emission_day: null,
      monthly_periodicity: null
    }
  };

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public modalData: any = {};
  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  public func = fn;

  can(action: ReportActions): boolean {
    return this.permissaoUsuario?.[action] !== false;
  }

  constructor(
    private router: Router,
    private ngxSpinnerService: NgxSpinnerService,
    private reportsServiceApi: ReportsServiceApi,
    private userService: UserService
  ) {}

  /**
   * Método de ciclo de vida do Angular chamado ao inicializar o componente.
   *
   * - Exibe o spinner.
   * - Carrega o perfil e permissões do usuário.
   * - Inicializa os controles do formulário de filtro.
   * - Executa a busca inicial dos relatórios agendados.
   */
  ngOnInit(): void {
    this.ngxSpinnerService.show();

    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    if (!this.can(ReportActions.Listar)) {
      this.message = {
        text: 'Você não tem permissão para acessar esta tela.',
        status: true,
        class: 'alert-danger'
      };
      this.ngxSpinnerService.hide();
      return;
    }

    this.controls = this.formFilter.controls;
    this.searchReports();
  }

  /**
   * Realiza a requisição para obter a lista de relatórios agendados com base nos filtros informados.
   *
   * - Exibe o spinner durante o carregamento.
   * - Atualiza a tabela e a quantidade total de registros.
   * - Exibe mensagem de "nenhum registro encontrado" se necessário.
   *
   * @param params Parâmetros de filtro para consulta dos relatórios.
   */
  getReportsList(params) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.reportsServiceApi.getReportsList(params).subscribe(
      (resp) => {
        const dados: any = resp;

        if (dados.status == 200) {
          this.tableData = dados.body.data ? dados.body.data : dados.body ? dados.body : [];
          this.collectionSize = dados.body.total_items_count;
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * Executa a busca de relatórios com base nos valores atuais do formulário de filtros.
   * Envia os dados para o método `getReportsList`.
   */
  searchReports() {
    const params = {
      SubjectType: this.controls['SubjectType'].value
    };

    this.getReportsList(params);
  }

  /**
   * Trata os eventos de clique nas linhas da tabela.
   * Redireciona para edição, abre modal de confirmação para exclusão ou confirma exclusão.
   *
   * @param $event Objeto contendo ação e dados da linha clicada.
   */
  clickRowEvent($event: any = null) {
    const actionLabels: any = {
      edit: 'editar',
      delete: 'excluir'
    };

    const phraseEndings: any = {
      edit: 'este agendamento de relatório.',
      delete: 'este agendamento de relatório.'
    };

    const isRestrictedAction = ['edit', 'delete'].includes($event.action);
    const hasPermission = this.can($event.action);

    if (isRestrictedAction && !hasPermission) {
      this.message = {
        text: `Você não tem permissão para ${actionLabels[$event.action]} ${phraseEndings[$event.action]}`,
        status: true,
        class: 'alert-danger'
      };
      setTimeout(() => (this.message.status = false), 4000);
      return;
    }

    switch ($event.action) {
      case 'edit':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/edit']);
        break;
      case 'delete':
        this.modalTitle = 'Excluir Agendamento de Relatório';
        this.modalMessage = ModalConfirm.ConfirmarOperacao;
        this.modalInstruction = null;
        this.modalConfig.iconHeader = null;
        this.modalConfig.action = 'confirmDelete';
        this.modalData = { id: $event.id };
        this.ModalConfirm.openModal();
        break;
      case 'confirmDelete':
        this.deleteReportScheduling($event.data.id);
        break;
    }
  }

  /**
   * Executa a exclusão de um agendamento de relatório com base no ID fornecido.
   * Após exclusão, atualiza a tabela e exibe mensagem de sucesso.
   *
   * @param reportId ID do relatório a ser excluído.
   */
  deleteReportScheduling(reportId: string) {
    this.ngxSpinnerService.show();

    this.reportsServiceApi.deleteReports(reportId).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.DeleteReport;
      this.message.status = true;
      this.message.class = 'alert-success';
      this.searchReports();

      setTimeout(() => {
        this.message.status = false;
      }, 4000);

      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Redireciona o usuário para a rota principal (home).
   */
  goBack() {
    this.router.navigate(['/']);
  }
}
