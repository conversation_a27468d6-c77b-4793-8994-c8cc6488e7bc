import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ClickOutsideModule } from 'ng4-click-outside';
import { ColorSketchModule } from 'ngx-color/sketch';

import { LogisoilDirectivesModule } from 'src/app/shared/logisoil-directives.module';
import { SectionsRoutingModule } from './sections-routing.module';
import { SharedModule } from '@components/shared.module';

import { ListSectionsComponent } from './list-sections/list-sections.component';
import { RegisterSectionComponent } from './register-section/register-section.component';

// Tela de Cadastro de Secoes
import { GeneralTabComponent } from './tabs/general-tab/general-tab.component';
import { ReviewTabComponent } from './tabs/review-tab/review-tab.component';
import { HistorySectionComponent } from './history-section/history-section.component';

@NgModule({
  declarations: [ListSectionsComponent, RegisterSectionComponent, GeneralTabComponent, ReviewTabComponent, HistorySectionComponent],
  imports: [
    CommonModule,
    ClickOutsideModule,
    ColorSketchModule,
    FormsModule,
    LogisoilDirectivesModule,
    NgbModule,
    ReactiveFormsModule,
    SharedModule,
    NgMultiSelectDropDownModule.forRoot(),
    SectionsRoutingModule
  ]
})
export class SectionsModule {}
