import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup } from '@angular/forms';

import { MessagePadroes } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { Subtypes } from 'src/app/constants/instruments.constants';

import { ImagesService as ImagesServiceApi } from 'src/app/services/api/image.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { UserService } from 'src/app/services/user.service';

import { NgxSpinnerService } from 'ngx-spinner';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-gallery-images',
  templateUrl: './gallery-images.component.html',
  styleUrls: ['./gallery-images.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class GalleryImagesComponent implements OnInit {
  @ViewChild('hierarchy') hierarchy: any;

  public formFilter: FormGroup = new FormGroup({
    ClientId: new FormControl([]),
    ClientUnitId: new FormControl([]),
    StructureId: new FormControl([]),
    SectionId: new FormControl([]),
    InstrumentId: new FormControl([]),
    Subtype: new FormControl('')
  });

  public instruments: any = [];
  public instrumentsBySubtype: any = [];
  public sections: any = [];
  public subTypes: any = [];
  public selectedSection: any = [];

  public instrumentsSettings = MultiSelectDefault.Instruments;
  public sectionSettings = MultiSelectDefault.Sections;
  public viewSettings = MultiSelectDefault.View;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public selectedSections: any = {};

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public imagesItens: any = [];
  public message: any = { text: '', status: false, class: 'alert-success' };

  public ctrlSearch: boolean = false;

  public func = fn;

  constructor(
    private imagesServiceApi: ImagesServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private sectionsServiceApi: SectionsServiceApi,
    private userService: UserService
  ) {}

  /**
   * Método de inicialização do componente.
   * Carrega o perfil do usuário, permissões e configurações iniciais.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.sectionSettings.singleSelection = true;
    this.instrumentsSettings.singleSelection = true;
    this.subTypes = fn.enumToArraySimple(Subtypes);
  }

  ngAfterViewInit() {
    // Chama o método managerFilters ao inicializar o componente para monitorar mudanças nos filtros
    this.hierarchy.formHierarchy.statusChanges.subscribe(() => {
      this.managerFilters();
    });
  }

  /**
   * Obtém as seções relacionadas a uma estrutura selecionada.
   * @param {any} structure - Estrutura selecionada.
   * @param {string} action - Ação de seleção ou desseleção.
   */
  getSections(structure, action: string = 'select') {
    if (action === 'select') {
      this.ctrlSearch = true;
      this.sectionsServiceApi.getSectionList({ structureId: structure.id, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.sections = dados;
      });
    } else {
      this.sections = [];
      this.instruments = [];
      this.instrumentsBySubtype = [];

      this.formFilter.controls['SectionId'].setValue([]);
      this.formFilter.controls['InstrumentId'].setValue([]);
    }
  }

  /**
   * Obtém os instrumentos relacionados às seções selecionadas.
   * @param {any} sections - Seções selecionadas.
   * @param {string} action - Ação de seleção ou desseleção.
   */
  getInstruments(sections: any, action: string = '') {
    this.ngxSpinnerService.show();

    switch (action) {
      case 'select':
        sections = this.formFilter.controls['SectionId'].value;
        break;
      case 'deselect':
        sections = this.formFilter.controls['SectionId'].value;
        break;
    }

    let uniqueArray = [];

    if (sections.length == 0) {
      this.instruments = [];
      this.instrumentsBySubtype = [];
      this.formFilter.controls['InstrumentId'].setValue([]);
      this.ngxSpinnerService.hide();
    } else {
      this.selectedSections = [];
      sections.forEach((section) => {
        this.sectionsServiceApi.getSectionByIdInstrument(section.id).subscribe((resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;
          dados.instruments.map((instrument) => {
            this.selectedSections[instrument.id] = instrument;
          });

          Object.keys(this.selectedSections).map((key) => {
            uniqueArray = uniqueArray.concat({
              id: this.selectedSections[key].id,
              identifier: this.selectedSections[key].identifier,
              type: this.selectedSections[key].type
            });
          });

          this.instruments = fn.uniqueArray(uniqueArray);
          this.filterSubtypes();
          this.removeNonExistingItems();
        });
      });
      this.ngxSpinnerService.hide();
    }
  }

  //Remove itens inexistentes da lista de instrumentos selecionados.
  removeNonExistingItems() {
    const filteredArray = this.formFilter.controls['InstrumentId'].value.filter((item) => this.instruments.some((option) => option.id === item.id));
    this.formFilter.controls['InstrumentId'].setValue(filteredArray);
  }

  //Filtra os instrumentos com base nos subtipos selecionados.
  filterSubtypes() {
    this.formFilter.controls['InstrumentId'].setValue([]);

    let types = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13];
    switch (parseInt(this.formFilter.controls['Subtype'].value)) {
      case 1:
        types = [1, 2, 3, 10];
        break;
      case 2:
        types = [4, 5, 6, 7, 8];
        break;
      case 3:
        types = [9];
        break;
      case 4:
        types = [12, 13];
        break;
    }
    this.instrumentsBySubtype = this.instruments.filter((instrument) => {
      return types.includes(instrument['type']);
    });
  }

  //Pesquisa imagens com base nos filtros aplicados e parâmetros hierárquicos.
  searchImages() {
    let controls = this.formFilter.controls;
    let filterHierarchy = this.hierarchy.getFilters();
    let params = {
      Entities: 0,
      'Filters.Clients': filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : null,
      'Filters.ClientUnits': filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : null,
      'Filters.Structures': filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].id : null
    };

    if (controls['SectionId'].value.length > 0) {
      params['Filters.Sections'] = controls['SectionId'].value[0].id;
    }

    if (controls['InstrumentId'].value.length > 0) {
      params['Filters.Instruments'] = controls['InstrumentId'].value[0].id;
      params.Entities = 1;
    }

    this.getImages(params);
  }

  /**
   * Obtém as imagens correspondentes aos parâmetros de pesquisa fornecidos.
   * @param {any} params - Parâmetros de pesquisa para obter as imagens.
   */
  getImages(params) {
    this.ngxSpinnerService.show();

    this.message.status = false;
    this.imagesItens = [];

    this.imagesServiceApi.getImages(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.imagesItens = dados.images;

      if (this.imagesItens.length === 0) {
        this.message.text = MessagePadroes.NoImage;
        this.message.status = true;
        this.message.class = 'alert-warning';

        setTimeout(() => {
          this.message.class = '';
          this.message.status = false;
          this.message.class = 'alert-success';
        }, 4000);
      }

      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Gerencia os eventos hierárquicos (clientes, unidades, estruturas).
   * @param {any} $event - Evento disparado pela hierarquia.
   */
  getEventHierarchy($event) {
    switch ($event.type) {
      case 'units':
        this.sections = [];
        this.formFilter.controls['SectionId'].setValue([]);
        this.formFilter.controls['InstrumentId'].setValue([]);
        break;
      case 'structures':
        this.sections = [];
        this.formFilter.controls['SectionId'].setValue([]);
        this.formFilter.controls['InstrumentId'].setValue([]);
        if ($event.element != null) {
          this.getSections($event.element, $event.action);
        }
        break;
    }
    if ($event.action == 'deselect') {
      this.ctrlSearch = false;
      this.sections = [];
      this.instruments = [];
      this.instrumentsBySubtype = [];
      this.formFilter.controls['SectionId'].setValue([]);
      this.formFilter.controls['InstrumentId'].setValue([]);
      this.formFilter.controls['Subtype'].setValue('');
    }
  }

  managerFilters() {
    const filterHierarchy = this.hierarchy.getFilters();

    // Verifica se Cliente, Unidade e Estrutura estão preenchidos
    if (filterHierarchy.clients.length > 0 && filterHierarchy.units.length > 0 && filterHierarchy.structures.length > 0) {
      this.searchImages();
    }
  }

  //Reseta todos os filtros aplicados e limpa as listas de seções, instrumentos e imagens.
  resetFilter() {
    this.hierarchy.resetFilters();

    this.sections = [];
    this.instruments = [];
    this.instrumentsBySubtype = [];

    this.imagesItens = [];

    this.formFilter.controls['ClientId'].setValue([]);
    this.formFilter.controls['ClientUnitId'].setValue([]);
    this.formFilter.controls['StructureId'].setValue([]);
    this.formFilter.controls['SectionId'].setValue([]);
    this.formFilter.controls['InstrumentId'].setValue([]);
    this.formFilter.controls['Subtype'].setValue('');
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  //Navega de volta para a página inicial
  goBack() {
    this.router.navigate(['/']);
  }
}
