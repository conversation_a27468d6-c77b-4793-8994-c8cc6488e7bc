import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';

import {
  ActionPlanArea,
  ActionPlanStatus,
  ActionPlanSeverity,
  ActionPlanUrgency,
  ActionPlanTendency,
  InspectionSheetType
} from 'src/app/constants/inspections.constants';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import { MessagePadroes } from 'src/app/constants/message.constants';

@Component({
  selector: 'app-register-action-plan',
  templateUrl: './register-action-plan.component.html',
  styleUrls: ['./register-action-plan.component.scss']
})
export class RegisterActionPlanComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('modalConfirmAttachment') modalConfirmAttachment: any;

  formActionPlan: FormGroup = this.fb.group({
    occurrence_id: '',
    occurrence_description: [{ value: '', disabled: true }],
    inspection_sheet_type: [{ value: '', disabled: true }],
    area: ['', Validators.required],
    recommendation: ['', [Validators.maxLength(512)]],
    expiration_date: ['', Validators.required],
    status: ['', Validators.required],
    severity: [1, Validators.required],
    urgency: [1, Validators.required],
    tendency: [1, Validators.required]
  });

  public areaList = ActionPlanArea;
  public statusList = ActionPlanStatus;

  public severityList = ActionPlanSeverity;
  public urgencyList = ActionPlanUrgency;
  public tendencyList = ActionPlanTendency;
  public inspectionSheetType = InspectionSheetType;

  public showAttachmentsSection = false;
  public selectedFiles: File[] = [];

  public attachments: any[] = [];
  public history: any[] = [];

  public edit: boolean = false;
  public view: boolean = false;
  public occurrenceId: string = '';

  public structureId: string = '';
  public actionPlanId: string | null = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public charCounts: { [key: string]: number } = {};

  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {};
  public modalData: any = {};

  //Histórico
  public tableData: Array<{ [key: string]: any }> = [];
  public tableHeader: any = [
    {
      label: 'ID',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Data',
      width: '180px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Tipo de entidade',
      width: '180px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['inspection_sheet_type']
    },
    {
      label: 'Ação',
      width: '70%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    },
    {
      label: 'Usuário',
      width: '30%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    }
  ];

  constructor(
    private fb: FormBuilder,
    private inspectionSheetServiceApi: InspectionSheetServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private activatedRoute: ActivatedRoute,
    private toastr: ToastrService
  ) {}

  /**
   * Método de inicialização do componente.
   *
   * - Verifica se há `actionPlanId` na rota para modo de edição e carrega os dados do plano de ação.
   * - Verifica se há `occurrenceId` nos queryParams para carregar dados da ocorrência.
   * - Armazena o `structureId` vindo dos queryParams para uso futuro.
   */
  ngOnInit(): void {
    if (this.activatedRoute.snapshot.params.actionPlanId) {
      this.edit = true;
      this.actionPlanId = this.activatedRoute.snapshot.params.actionPlanId;
      this.getActionPlanById(this.actionPlanId);
    }

    this.occurrenceId = this.activatedRoute.snapshot?.queryParams?.occurrenceId ?? '';

    if (this.occurrenceId != '') {
      this.getOccurrenceById(this.occurrenceId);
    }

    this.structureId = this.activatedRoute.snapshot?.queryParams?.structureId ?? '';
  }

  /**
   * Carrega os dados do plano de ação para edição.
   * @param {string} id - ID do plano de ação.
   */
  getActionPlanById(actionPlanId: string): void {
    this.ngxSpinnerService.show();
    this.inspectionSheetServiceApi.getActionPlanAttachments;
    this.inspectionSheetServiceApi.getActionPlanById(actionPlanId).subscribe({
      next: (resp) => {
        const data = (resp as any)?.body ?? resp;

        // Se status for 1 (Concluída), entra em modo visualização
        this.view = data.status === 1;
        if (this.view) {
          this.formActionPlan.disable();
        }

        this.formActionPlan.patchValue({
          area: data.area,
          recommendation: data.recommendation,
          expiration_date: moment(data.expiration_date).format('YYYY-MM-DD'),
          status: data.status,
          severity: data.severity,
          urgency: data.urgency,
          tendency: data.tendency,
          occurrence_id: data.occurrence?.id ?? '',
          occurrence_description: data.occurrence?.aspect_description ?? '',
          inspection_sheet_type: data.occurrence?.inspection_sheet_type
        });

        this.charCounts['recommendation'] = data.recommendation.length;

        // Buscar anexos separadamente
        this.getActionPlanAttachments(actionPlanId);

        this.showAttachmentsSection = true;

        this.getActionPlanHistory();

        this.ngxSpinnerService.hide();
      },
      error: () => {
        this.ngxSpinnerService.hide();
        this.toastr.error('Erro ao carregar plano de ação.');
      }
    });
  }

  /**
   * Carrega os dados da ocorrência pelo seu identificador e preenche o formulário.
   *
   * @param occurrenceId Identificador da ocorrência.
   */
  getOccurrenceById(occurrenceId: string): void {
    this.inspectionSheetServiceApi.getInspectionSheetOccurrenceById(occurrenceId).subscribe({
      next: (resp) => {
        const data = (resp as any)?.body ?? resp;
        this.formActionPlan.patchValue({
          occurrence_id: this.occurrenceId ?? '',
          occurrence_description: data.aspect?.description ?? '',
          inspection_sheet_type: data.inspection_sheet_type
        });

        this.structureId = data.structure_id;
      },
      error: () => {
        this.ngxSpinnerService.hide();
        this.toastr.error('Erro ao carregar ocorrência.');
      }
    });
  }

  /**
   * Recupera os anexos associados a um plano de ação.
   *
   * - Faz uma requisição ao endpoint de anexos.
   * - Converte os arquivos recebidos para base64 com MIME, se necessário.
   * - Atualiza a lista local `attachments` com os dados formatados.
   * - Exibe uma mensagem de aviso caso ocorra erro na requisição.
   *
   * @param {string} actionPlanId - ID do plano de ação para buscar os anexos.
   */
  private getActionPlanAttachments(actionPlanId: string): void {
    this.inspectionSheetServiceApi.getActionPlanAttachments(actionPlanId).subscribe({
      next: (resp) => {
        const files = (resp as any)?.attachments ?? [];

        this.attachments = files
          .map((item: any) => {
            const fileName = item.file?.name;
            const base64Value = item.file?.base64;
            const id = item.id;

            if (!base64Value || !fileName) return null;

            let base64WithMime = base64Value;
            if (!base64Value.startsWith('data:')) {
              const decode = fn.base64Extension(base64Value.slice(0, 100));
              base64WithMime = decode.mimeType ? `data:${decode.mimeType};base64,${base64Value}` : `data:application/octet-stream;base64,${base64Value}`;
            }

            return {
              id,
              name: fileName,
              base64: base64WithMime
            };
          })
          .filter((file: any) => file !== null);
      },
      error: () => {
        this.toastr.warning('Não foi possível carregar os anexos deste plano de ação.');
      }
    });
  }

  /**
   * Busca o histórico de alterações do plano de ação atual.
   */
  getActionPlanHistory() {
    this.ngxSpinnerService.show();

    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    this.inspectionSheetServiceApi.getActionPlanHistory(this.actionPlanId, params).subscribe({
      next: (resp: any) => {
        if (resp?.status === 200) {
          const data = (resp as any)?.body ?? resp;
          this.tableData = data.data ? this.formatActionPlanHistory(data.data) : [];
          this.collectionSize = data?.total_items_count || 0;
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.message.text = MessagePadroes.NoRegister;
          this.message.status = true;
          this.message.class = 'alert-warning';
        }
        this.ngxSpinnerService.hide();
      },
      error: () => {
        this.ngxSpinnerService.hide();
        this.toastr.error('Erro ao carregar histórico.');
      }
    });
  }

  /**
   * Formata os dados recebidos do histórico de planos de ação para exibição na tabela.
   *
   * @param data Lista de alterações do plano de ação.
   * @returns Lista formatada para a tabela.
   */
  formatActionPlanHistory(data: any[]): any[] {
    return data.map((item, index) => {
      return {
        search_identifier: item.search_identifier,
        created_date: item.created_date ? moment(item.created_date).format('DD/MM/YYYY HH:mm:ss') : '-',
        changes: item.changes,
        inspection_sheet_type: item.inspection_sheet_type,
        user: `${item.modified_by.first_name} ${item.modified_by.surname}`
      };
    });
  }

  /**
   * Salva o plano de ação atual.
   * Decide entre criar um novo ou atualizar um existente com base no modo de edição (`edit`).
   */
  onSave(): void {
    if (this.view) return;

    if (this.formActionPlan.invalid) {
      this.formActionPlan.markAllAsTouched();
      return;
    }

    if (this.edit) {
      this.updateActionPlan();
    } else {
      this.createActionPlan();
    }
  }

  /**
   * Cria um novo plano de ação usando os dados preenchidos no formulário.
   */
  createActionPlan(): void {
    const payload = {
      structure_id: this.structureId,
      occurrence_id: this.formActionPlan.value.occurrence_id,
      inspection_sheet_id: this.formActionPlan.value.inspection_sheet_id,
      area: Number(this.formActionPlan.value.area),
      recommendation: this.formActionPlan.value.recommendation,
      expiration_date: this.formActionPlan.value.expiration_date,
      status: Number(this.formActionPlan.value.status),
      severity: Number(this.formActionPlan.value.severity),
      urgency: Number(this.formActionPlan.value.urgency),
      tendency: Number(this.formActionPlan.value.tendency),
      attachments: this.attachments.map((file) => {
        const base64 = this.extractBase64(file.base64);

        // Se o anexo já tem ID (veio do back), mantém o id no envio
        if (file.id && !file.id.toString().startsWith('novo-')) {
          return {
            id: file.id,
            file: {
              base64: base64,
              name: file.name
            }
          };
        }

        // Se for um anexo novo, manda sem id
        return {
          file: {
            base64: base64,
            name: file.name
          }
        };
      })
    };

    this.inspectionSheetServiceApi.postActionPlans(payload).subscribe({
      next: (resp: any) => {
        if (resp?.status === 200) {
          const data = (resp as any)?.body ?? resp;
          this.actionPlanId = data;
          this.toastr.success('Plano de ação criado.');
          this.showAttachmentsSection = true;
          this.getActionPlanHistory();
        }
      },
      error: (error) => {
        const backendMessage = Array.isArray(error?.error) ? error.error[0]?.message : 'Erro ao criar plano de ação.';

        this.message = {
          text: backendMessage,
          status: true,
          class: 'alert-danger'
        };

        setTimeout(() => {
          this.message.status = false;
        }, 6000);
      }
    });
  }

  /**
   * Atualiza um plano de ação existente usando os dados preenchidos no formulário.
   */
  updateActionPlan(): void {
    const payload = {
      id: this.actionPlanId,
      area: Number(this.formActionPlan.value.area),
      recommendation: this.formActionPlan.value.recommendation,
      expiration_date: this.formActionPlan.value.expiration_date,
      status: Number(this.formActionPlan.value.status),
      severity: Number(this.formActionPlan.value.severity),
      urgency: Number(this.formActionPlan.value.urgency),
      tendency: Number(this.formActionPlan.value.tendency),
      attachments: this.attachments.map((file) => {
        const base64 = this.extractBase64(file.base64);

        // Se o anexo já tem ID (veio do back), mantém o id no envio
        if (file.id && !file.id.toString().startsWith('novo-')) {
          return {
            id: file.id,
            file: {
              base64: base64,
              name: file.name
            }
          };
        }

        // Se for um anexo novo, manda sem id
        return {
          file: {
            base64: base64,
            name: file.name
          }
        };
      })
    };

    this.inspectionSheetServiceApi.putActionPlans(this.actionPlanId!, payload).subscribe({
      next: () => {
        this.toastr.success('Plano de ação atualizado.');
        this.showAttachmentsSection = true;
      },
      error: (error) => {
        this.message = {
          text: error?.error?.[0]?.message ?? 'Erro ao atualizar plano de ação.',
          status: true,
          class: 'alert-danger'
        };

        setTimeout(() => {
          this.message.status = false;
        }, 6000);
      }
    });
  }

  /**
   * Manipula a seleção de arquivos realizada pelo usuário.
   *
   * - Converte a lista de arquivos selecionados em um array e armazena em `selectedFiles`.
   * - Se nenhum arquivo for selecionado, limpa a lista.
   *
   * @param {Event} event - Evento de alteração do input de arquivos.
   */
  onFilesSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) {
      this.selectedFiles = [];
      return;
    }

    this.selectedFiles = Array.from(input.files);
  }

  /**
   * Envia todos os arquivos selecionados para o backend.
   *
   * - Cria um objeto `FormData` contendo os arquivos selecionados.
   * - Envia via `POST` para o endpoint de anexos do plano de ação.
   * - Exibe mensagens de sucesso ou erro conforme o resultado da operação.
   * - Limpa a lista de arquivos e reseta o input após o envio.
   * - Atualiza a listagem de anexos após envio bem-sucedido.
   */
  sendAllAttachments(): void {
    if (!this.selectedFiles.length || !this.actionPlanId) return;

    this.ngxSpinnerService.show();

    const formData = new FormData();
    this.selectedFiles.forEach((file) => {
      formData.append('files', file); // plural!
    });

    this.inspectionSheetServiceApi.postActionPlansAttachments(this.actionPlanId, formData).subscribe({
      next: (resp: any) => {
        this.message = {
          text: `Arquivo(s) anexado(s) com sucesso.`,
          status: true,
          class: 'alert-success'
        };

        this.selectedFiles = [];
        this.fileInput.nativeElement.value = '';

        // Atualiza a tabela com os arquivos mais recentes
        this.getActionPlanAttachments(this.actionPlanId);

        setTimeout(() => (this.message.status = false), 4000);
        this.ngxSpinnerService.hide();
      },
      error: () => {
        this.message = {
          text: `Erro ao enviar os arquivos.`,
          status: true,
          class: 'alert-danger'
        };

        setTimeout(() => (this.message.status = false), 6000);
        this.ngxSpinnerService.hide();
      }
    });
  }

  /**
   * Remove um anexo do plano de ação via API.
   * @param attachmentId ID do anexo a ser removido
   * @param name Nome do anexo (para exibir na mensagem)
   */
  removeAttachment(attachmentId: string, name: string): void {
    if (this.view || !this.actionPlanId || !attachmentId) return;

    this.ngxSpinnerService.show();

    this.inspectionSheetServiceApi.deleteActionPlanAttachments(this.actionPlanId, attachmentId).subscribe({
      next: () => {
        this.message = {
          text: `Arquivo "${name}" removido com sucesso.`,
          status: true,
          class: 'alert-success'
        };

        this.getActionPlanAttachments(this.actionPlanId); // Atualiza os anexos após remoção
        this.ngxSpinnerService.hide();

        setTimeout(() => (this.message.status = false), 4000);
      },
      error: () => {
        this.message = {
          text: `Erro ao remover o arquivo "${name}".`,
          status: true,
          class: 'alert-danger'
        };

        this.ngxSpinnerService.hide();
        setTimeout(() => (this.message.status = false), 4000);
      }
    });
  }

  openModalDeleteAttachment(id: string, name: string): void {
    this.modalTitle = 'Confirmar exclusão do anexo';
    this.modalMessage = `Deseja realmente excluir o arquivo "${name}"?`;
    this.modalInstruction = 'Esta ação não poderá ser desfeita.';
    this.modalConfig = { iconHeader: null, action: 'deleteAttachment' };
    this.modalData = { id, name };
    this.modalConfirmAttachment.openModal();
  }

  confirmDeleteAttachment(event: any): void {
    if (event?.action === 'deleteAttachment') {
      this.removeAttachment(event.data.id, event.data.name);
    }
  }

  /**
   * Atualiza todo o plano de ação (não apenas anexos) no backend.
   */
  updateAttachments(refreshAfterUpdate = false): void {
    if (!this.actionPlanId) return; // Segurança extra

    const payload = {
      id: this.actionPlanId,
      area: Number(this.formActionPlan.value.area),
      recommendation: this.formActionPlan.value.recommendation,
      expiration_date: this.formActionPlan.value.expiration_date,
      status: Number(this.formActionPlan.value.status),
      severity: Number(this.formActionPlan.value.severity),
      urgency: Number(this.formActionPlan.value.urgency),
      tendency: Number(this.formActionPlan.value.tendency),
      attachments: this.attachments.map((file) => {
        const base64 = this.extractBase64(file.base64);

        if (file.id && !file.id.toString().startsWith('novo-')) {
          return {
            id: file.id,
            file: {
              base64: base64,
              name: file.name
            }
          };
        }

        return {
          file: {
            base64: base64,
            name: file.name
          }
        };
      })
    };

    this.inspectionSheetServiceApi.putActionPlans(this.actionPlanId, payload).subscribe({
      next: () => {
        this.toastr.success('Anexos atualizados com sucesso.');
        if (refreshAfterUpdate) {
          this.getActionPlanById(this.actionPlanId!); // Atualiza os dados na tela, se quiser
        }
      },
      error: (error) => {
        const backendMessage = Array.isArray(error?.error) ? error.error[0]?.message : 'Erro ao atualizar plano de ação.';

        this.message = {
          text: backendMessage,
          status: true,
          class: 'alert-danger'
        };

        setTimeout(() => {
          this.message.status = false;
        }, 6000);
      }
    });
  }

  /**
   * Extrai o conteúdo puro base64 de uma URL Data.
   * @param {string} dataUrl - URL com prefixo data.
   * @returns {string} Base64 sem prefixo.
   */
  private extractBase64(dataUrl: string): string {
    return dataUrl.split(',')[1];
  }

  // Atualiza o contador do campo específico
  onValueChange(event: any, field: string): void {
    this.charCounts[field] = event.target.value.length;
  }

  /**
   * Carrega a página selecionada e realiza a busca de análises de estabilidade.
   * @param {number} selectPage - Página selecionada.
   */
  loadPage(selectPage: number) {
    this.page = selectPage;
    this.getActionPlanHistory();
  }
}
