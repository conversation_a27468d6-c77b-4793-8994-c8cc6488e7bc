import { Component, OnInit, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';

import { filter } from 'rxjs';
import { UserService } from 'src/app/services/user.service';

@Component({
  selector: 'app-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class TableComponent implements OnInit {
  @Input() public messageReturn: any = [{ text: '', status: true }];
  @Input() public tableHeader: any = [];
  @Input() public tableSubheader: any = [];
  @Input() public tableData: any = [];
  @Input() public permissaoUsuario: any = [];

  @Input() public menuMiniDashboard: any = {};
  @Input() public actionCustom: any = [];
  @Input() public extraHeader: any = [];
  @Input() public eventRow: boolean = false;
  @Input() public detail: boolean = false;

  @Input() public filterHeader: any = null;
  @Input() public getActionsFn: ((row: any) => any[]) | null = null; //Inspeções - Planos de ação

  @Output() public sendToggleStatus = new EventEmitter();
  @Output() public sendEmailPassword = new EventEmitter();
  @Output() public sendClickRowEvent = new EventEmitter();

  public routerLink: string = '/';
  public ctrlActions: boolean = true;
  public levelUser: number = 7;

  constructor(private router: Router, private userService: UserService) {
    router.events.pipe(filter((event: any) => event instanceof NavigationEnd)).subscribe((event) => {
      this.routerLink = event.url;
      if (this.routerLink == '/users') {
        this.levelUser = userService.getProfile().level;
      }
    });
  }

  /**
   * Método de ciclo de vida do Angular chamado na inicialização do componente.
   * Armazena a URL atual da rota no atributo `routerLink`.
   */
  ngOnInit(): void {
    this.routerLink = this.router.url;
  }

  /**
   * Emite o evento `sendToggleStatus` com os dados recebidos.
   * Usado para alternar o status de um item na tabela.
   *
   * @param $event Dados do status que foi alternado.
   */
  toggleStatus($event: any) {
    this.sendToggleStatus.emit($event);
  }

  /**
   * Emite o evento `sendEmailPassword` com os dados necessários.
   * Utilizado para acionar o envio de senha por e-mail.
   *
   * @param $event Dados do usuário para envio da senha.
   */
  emailPassword($event: any) {
    this.sendEmailPassword.emit($event);
  }

  /**
   * Emite o evento `sendClickRowEvent` quando uma linha da tabela é clicada.
   * Combina `action` e `params` em um único objeto emitido.
   *
   * @param action Ação disparada.
   * @param params Parâmetros adicionais da linha.
   */
  clickRowEvent(action: any = null, params: any = null) {
    this.sendClickRowEvent.emit({ ...action, ...params });
  }

  /**
   * Emite o evento `sendClickRowEvent` ao clicar no cabeçalho de uma tabela com ação.
   *
   * @param $event Objeto contendo `action` e `params`.
   */
  clickHeaderEvent($event) {
    this.sendClickRowEvent.emit({ action: $event.action, params: $event.params });
  }

  /**
   * Impede que o evento de clique se propague para elementos pai.
   *
   * @param $event Evento DOM do tipo clique.
   */
  stopPropagation($event: Event) {
    $event.stopPropagation();
  }

  /**
   * Verifica se o valor fornecido é um array.
   *
   * @param input Valor a ser verificado.
   * @returns `true` se for um array, caso contrário `false`.
   */
  isArray(input: any): boolean {
    return Array.isArray(input);
  }

  /**
   * Garante que a variável `detail` seja tratada como um array.
   *
   * @param detail Detalhe que pode ser um item único ou um array.
   * @returns Array contendo os detalhes.
   */
  getDetailList(detail: any): any[] {
    return Array.isArray(detail) ? detail : [detail];
  }

  allActionsFalse(row: any, data: any, actionCustom: any[]): boolean {
    // Verifica se TODOS os actionCustom que têm 'condition'
    // possuem o data[condition.item] como false
    if (!row.showDashWhenNoAction) return false; // só mostra traço se a flag no row estiver true

    const allFalse = actionCustom.every(
      (item) => (item.hasOwnProperty('condition') ? !data[item.condition.item] : true) // ignora items que não tem condition (ou seja, não bloqueia por eles)
    );

    return allFalse;
  }

  getCellType(row, column, data) {
    if (column === 'active') {
      return { type: 'active', class: 'fit exposed select-cell' };
    }

    if (column === 'action') {
      return { type: 'action', class: 'd-flex justify-content-around' };
    }

    if (column === 'miniDashboard') {
      return { type: 'miniDashboard', class: 'd-flex justify-content-around' };
    }

    if (column === 'actionCustom') {
      return {
        type: 'actionCustom',
        class: row.hasOwnProperty('class') ? row.class : 'd-flex justify-content-center'
      };
    }

    if (row.hasOwnProperty('extra')) {
      return {
        type: 'extra',
        class: {
          'd-flex justify-content-center align-items-center': true,
          'only-icon': data.extra && data.extra[column] && !data.extra?.show_label
        }
      };
    }

    const type = row['type'];

    // Função auxiliar para verificar se type inclui o valor
    const hasType = (value) => {
      if (Array.isArray(type)) {
        return type.includes(value);
      } else {
        return type === value;
      }
    };

    if (hasType('color')) {
      return { type: 'type-color', class: '' };
    }

    if (hasType('event')) {
      return { type: 'type-event', class: 'pointer' };
    }

    if (hasType('check')) {
      return { type: 'type-check', class: '' };
    }

    if (hasType('button')) {
      return { type: 'type-button', class: '' };
    }

    if (hasType('button_item')) {
      return { type: 'type-button_item', class: 'd-flex justify-content-center' };
    }

    if (hasType('link')) {
      return { type: 'type-link', class: '' };
    }

    if (hasType('details')) {
      return { type: 'type-details', class: '' };
    }

    if (hasType('gut')) {
      return { type: 'type-gut', class: 'text-center' };
    }

    return { type: 'default', class: '' };
  }
}
