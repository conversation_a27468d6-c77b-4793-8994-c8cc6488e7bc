<div class="list-content">
  <form [formGroup]="formRegisterReadings">
    <div class="row mt-2 mb-2">
      <!-- Cliente -->
      <div class="col-md-3" *ngIf="!edit && (spreadsheet || !view)">
        <label class="form-label">Cliente</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="clientSettings"
          [data]="clients"
          (onSelect)="getUnits($event, 'select')"
          (onDeSelect)="getUnits($event, 'deselect')"
          formControlName="client"
        >
        </ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formRegisterReadings.get('client').valid &&
            formRegisterReadings.get('client').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Unidade -->
      <div class="col-md-3" *ngIf="!edit && (spreadsheet || !view)">
        <label class="form-label">Unidade</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="unitSettings"
          [data]="units"
          (onSelect)="getStructures($event, 'select')"
          (onDeSelect)="getStructures($event, 'deselect')"
          formControlName="client_unit"
        >
        </ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formRegisterReadings.get('client_unit').valid &&
            formRegisterReadings.get('client_unit').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Estrutura -->
      <div class="col-md-3" *ngIf="!edit && (spreadsheet || !view)">
        <label class="form-label">Estrutura</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="structureSettings"
          [data]="structures"
          (onSelect)="
            getListSelect(
              formRegisterReadings.get('type_instrument').value[0],
              'select'
            )
          "
          formControlName="structure"
        >
        </ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formRegisterReadings.get('structure').valid &&
            formRegisterReadings.get('structure').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Tipo de instrumento -->
      <div
        class="col-md-3"
        *ngIf="
          (formRegisterReadings.controls['structure'].value &&
            formRegisterReadings.controls['structure'].value.length > 0) ||
          edit ||
          view
        "
      >
        <label class="form-label">{{
          beachLength ? 'Comprimento de praia' : 'Tipo de Instrumento'
        }}</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="instrumentSettings"
          [data]="typeInstrumentsList"
          formControlName="type_instrument"
          (onSelect)="getListSelect($event, 'select')"
          (onDeSelect)="getListSelect($event, 'deselect')"
          [disabled]="this.view"
        >
        </ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formRegisterReadings.get('type_instrument').valid &&
            formRegisterReadings.get('type_instrument').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
  </form>

  <div
    class="col-md-12 alert"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status && instruments.length == 0 && sections.length == 0"
    [innerHTML]="message.text"
  ></div>

  <div
    class="row mt-2 mb-2"
    *ngIf="instruments.length > 0 || sections.length > 0"
  >
    <div class="col-md-12">
      <div class="reading-container">
        <div class="reading-header d-flex align-items-center">
          {{ title }} -
          {{ formRegisterReadings.controls['type_instrument'].value[0].name }}
        </div>
        <div class="row p-3">
          <form [formGroup]="formUnitsReadings">
            <!-- Medidas para entrada de dados -->
            <div class="row" *ngIf="mesasureUnits.length > 0 && !view">
              <div class="col-md-4" *ngIf="mesasureUnits[0]">
                <label class="form-label">{{ mesasureUnits[0].label }}</label>
                <select
                  class="form-select"
                  formControlName="unit_0"
                  (change)="changeUnit()"
                >
                  <option
                    *ngFor="let unit of mesasureUnits[0].unit"
                    [ngValue]="unit.id"
                  >
                    {{ unit.name }}
                  </option>
                </select>
              </div>
              <div class="col-md-4" *ngIf="mesasureUnits[1]">
                <label class="form-label">{{ mesasureUnits[1].label }}</label>
                <select
                  class="form-select"
                  formControlName="unit_1"
                  (change)="changeUnit()"
                >
                  <option
                    *ngFor="let unit of mesasureUnits[1].unit"
                    [ngValue]="unit.id"
                  >
                    {{ unit.name }}
                  </option>
                </select>
              </div>
            </div>
            <hr *ngIf="!edit && !view" />
            <!-- Planilha -->
            <div class="row mt-2" *ngIf="!edit && (spreadsheet || !view)">
              <span>
                <strong
                  >Importe os dados da leitura ou preencha os campos
                  manualmente.</strong
                >
              </span>
              <!-- Formato -->
              <div class="col-md-2">
                <label class="form-label">Formato do arquivo:</label>
                <ng-multiselect-dropdown
                  [placeholder]="'Selecione...'"
                  [settings]="singleSettingsModal"
                  [data]="fileFormats"
                  formControlName="file_format"
                >
                </ng-multiselect-dropdown>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formUnitsReadings.get('file_format').valid &&
                    formUnitsReadings.get('file_format').touched
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Template -->
              <div
                class="col-md-auto d-flex align-items-center"
                style="margin-top: 5px"
              >
                <app-button
                  [class]="'btn-logisoil-blue'"
                  [label]="'Baixar planilha modelo'"
                  [icon]="'fa fa-download'"
                  [type]="true"
                  (click)="getDownloadTemplate()"
                >
                </app-button>
              </div>
              <!-- Import -->
              <div class="col-md-4">
                <label class="form-label"
                  >Importar planilha
                  <em class="form-text"
                    >(Tamanho máximo permitido: 10MB)
                  </em></label
                >
                <input
                  type="file"
                  class="form-control"
                  accept=".csv, .xlsx"
                  (change)="uploadFile($event)"
                />
                <small
                  ><i
                    class="fa fa-exclamation-circle me-1"
                    aria-hidden="true"
                  ></i
                  ><a
                    class="instructions"
                    (click)="ModalInstructionsSpreadsheet.openModal()"
                    >Instruções de preenchimento</a
                  >
                </small>
              </div>
              <div
                class="col-md-auto d-flex align-items-center"
                style="margin-top: 5px"
              >
                <app-button
                  [class]="'btn-logisoil-blue'"
                  [customBtn]="true"
                  [label]="'Enviar arquivo'"
                  [icon]="'fa fa-upload'"
                  *ngIf="fileValid"
                  (click)="getReadingsFileUpload()"
                ></app-button>
              </div>
            </div>
            <hr *ngIf="!edit && (spreadsheet || !view)" />
            <!-- Atribuir data e horário -->
            <div class="row mt-3" *ngIf="canShowDatetimeButton">
              <div class="col-md-3">
                <input
                  type="datetime-local"
                  class="form-control"
                  formControlName="attribute_datetime"
                />
              </div>
              <div class="col-md-3">
                <app-button
                  [class]="'btn-logisoil-blue me-2'"
                  [label]="'Atribuir data e horário'"
                  (click)="setDatetime()"
                ></app-button>
              </div>
            </div>
            <!-- Recalcular análises de estabilidade da seção X -->
            <div
              class="row mt-3"
              *ngIf="
                [1, 2, 3].includes(typeInstrument.id) &&
                !view &&
                !spreadsheet &&
                edit
              "
            >
              <div>
                <input
                  type="checkbox"
                  class="form-check-input me-2"
                  value=""
                  formControlName="should_recalculate_stability"
                />
                <label class="form-label"
                  >Recalcular análises de estabilidade da seção
                  {{
                    formUnitsReadings.controls['instrument_sections'].value.name
                  }}</label
                >
              </div>
            </div>
          </form>
          <div style="display: grid" *ngIf="ctrlListFormReadings">
            <div [ngClass]="!view ? 'mt-2' : ''">
              <ng-template
                ngFor
                let-formReadingsItem
                [ngForOf]="listFormReadings"
                let-i="index"
              >
                <app-form-readings
                  #formReadingsRef
                  [typeInstrument]="typeInstrument"
                  [instruments]="
                    spreadsheet
                      ? formReadingsItem.instruments
                      : !edit && !view
                      ? instruments
                      : formReadingsItem.instruments
                  "
                  [sections]="
                    spreadsheet
                      ? formReadingsItem.sections
                      : !edit && !view
                      ? sections
                      : formReadingsItem.sections
                  "
                  [unitField]="unitField"
                  [uidForm]="formReadingsItem.uidForm"
                  (sendRemoveForm)="removeForm($event)"
                  [datetime]="datetime"
                  [structure]="selectStructure ?? null"
                  [data]="formReadingsItem.data"
                  [edit]="edit"
                  [view]="view"
                  [spreadsheet]="spreadsheet"
                ></app-form-readings>
              </ng-template>
            </div>
          </div>
        </div>
        <div class="reading-footer mb-3">
          <!-- Alertas -->
          <div class="row mt-2">
            <div
              class="col-md-12 alert"
              [ngClass]="message.class"
              role="alert"
              *ngIf="message.status"
              [innerHTML]="message.text"
            ></div>
            <app-alert
              class="mt-2"
              [class]="'alert-danger'"
              [messages]="messagesError"
            ></app-alert>
            <app-alert
              class="mt-2"
              [class]="messageUpload.class"
              [messages]="messageUpload.msg"
              *ngIf="messageUpload.status"
              (sendClickEvent)="clickEvent($event)"
            ></app-alert>
          </div>
          <hr />
          <div class="row">
            <div class="col-md-12 d-flex align-items-end justify-content-end">
              <app-button
                [class]="'btn-logisoil-red'"
                label="Cancelar"
                [type]="true"
                class="me-1"
                (click)="removeAllForm()"
                *ngIf="spreadsheet || !view"
              >
              </app-button>
              <app-button
                *ngIf="!beachLength && !edit && !view"
                [class]="'btn-logisoil-blue'"
                [icon]="'fa fa-file-text-o'"
                label="Adicionar leitura"
                [type]="true"
                class="me-1"
                (click)="addForm('button')"
              >
              </app-button>
              <app-button
                *ngIf="spreadsheet || !view"
                [class]="'btn-logisoil-green'"
                [icon]="'fa fa-thin fa-floppy-disk'"
                label="Salvar"
                [type]="true"
                (click)="validate()"
              >
              </app-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-12 mt-2 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/readings']"
    ></app-button>
  </div>
</div>

<!-- Instruções cadastro planilha -->
<app-modal-instructions-spreadsheet
  #modalInstructionsSpreadsheet
  [typeInstrument]="typeInstrument"
></app-modal-instructions-spreadsheet>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
