import { Component, OnInit, ViewChild } from '@angular/core';
import { ContentComponent } from '@components/content/content.component';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss']
})
export class MainComponent implements OnInit {
  @ViewChild(ContentComponent) contentComponent: ContentComponent;

  constructor() {}

  ngOnInit(): void {}

  setStructure($event) {
    this.contentComponent.setStructure($event);
    this.contentComponent.setFilter();
  }
}
