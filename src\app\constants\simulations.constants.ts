const Conditions = [
  { value: 1, label: 'Drenada' },
  { value: 2, label: 'Não drenada' },
  { value: 3, label: 'Pseudo estática' }
];

const AnalysisType = [{ value: 1, label: 'Estática' }];

const CalculationMethods = [
  { name: '<PERSON> simplified', id: '1' },
  { name: 'Corps of engineers 1', id: '2' },
  { name: 'Corps of engineers 2', id: '3' },
  { name: 'GLE/Morgenstern-Price', id: '4' },
  { name: 'Janbu simplified', id: '5' },
  { name: '<PERSON><PERSON> corrected', id: '6' },
  { name: '<PERSON> karafiath', id: '7' },
  { name: 'Ordinary or fellenius', id: '8' },
  { name: '<PERSON>', id: '9' },
  { name: '<PERSON>rma', id: '10' }
];

const SurfaceType = [
  { value: 1, label: 'Circular' },
  { value: 2, label: 'Não circular' }
];

const SearchMethod = {
  surface_type_circular: {
    name: 'Circular',
    id: '1',
    search: [
      {
        method: 'Auto Refine Search C',
        value: '1',
        fields: ['divisions_along_slope', 'circles_per_division', 'number_of_iterations', 'divisions_next_iteration']
      },
      { method: 'Grid Search', value: '2', fields: ['radius_increment'] },
      { method: 'Slope Search', value: '3', fields: ['number_of_surfaces'] }
    ]
  },
  surface_type_non_circular: {
    name: 'Não Circular',
    id: '2',
    search: [
      {
        method: 'Auto Refine Search NC',
        value: '1',
        fields: ['divisions_along_slope', 'surfaces_per_division', 'number_of_iterations', 'divisions_next_iteration', 'number_of_vertices_along_surface']
      },
      {
        method: 'Cuckoo Search',
        value: '2',
        fields: ['maximum_iterations', 'number_of_nests', 'number_of_vertices_along_surface']
      },
      {
        method: 'Simulated Annealing',
        value: '3',
        fields: [
          'initial_number_of_surface_vertices',
          'initial_number_of_iterations',
          'maximum_number_of_steps',
          'tolerance_for_stopping_criterion',
          'number_of_factors_safety_compared_before_stopping'
        ]
      },
      {
        method: 'Particle Swarm Search',
        value: '4',
        fields: ['initial_number_of_surface_vertices', 'maximum_iterations', 'number_of_particles']
      },
      { method: 'Block Search', value: '5', fields: ['number_of_surfaces'] },
      { method: 'Path Search', value: '6', fields: ['number_of_surfaces'] }
    ]
  }
};

//Freática-Piezométrica = Phreatic-Piezometric
const PhreaticPiezometric = [
  { value: 1, label: 'Específica' },
  { value: 2, label: 'Estatística' },
  { value: 3, label: 'Variação' },
  { value: 4, label: 'Manual' }
];

//Período
const Period = [
  { value: 2, label: 'Últimos 6 meses' },
  { value: 3, label: 'Último ano' },
  { value: 4, label: 'Últimos 2 anos' },
  { value: 5, label: 'Últimos 3 anos' },
  { value: 6, label: 'Últimos 5 anos' },
  { value: 1, label: 'Tudo' }
];

//Referência Leituras
const ReferenceReadings = [
  { value: 1, label: 'Específica' },
  { value: 2, label: 'Máximo' },
  { value: 3, label: 'Média' },
  { value: 4, label: 'Mediana' },
  { value: 5, label: 'Mínimo' },
  { value: 6, label: 'Último' }
];

//Ref. N.A. montante
const UpstreamWaterLevelReference = [
  { value: 1, label: 'Específica' },
  { value: 2, label: 'Máximo' },
  { value: 3, label: 'Média' },
  { value: 4, label: 'Mediana' },
  { value: 5, label: 'Mínimo' },
  { value: 6, label: 'Último' }
];

//Ref. N.A. jusante
const DownstreamWaterLevelReference = [
  { value: 1, label: 'Específica' },
  { value: 2, label: 'Máximo' },
  { value: 3, label: 'Média' },
  { value: 4, label: 'Mediana' },
  { value: 5, label: 'Mínimo' },
  { value: 6, label: 'Último' }
];

//Ref. Comprimento de Praia
const BeachLengthReference = [
  { value: 1, label: 'Específica' },
  { value: 2, label: 'Máximo' },
  { value: 3, label: 'Média' },
  { value: 4, label: 'Mediana' },
  { value: 5, label: 'Mínimo' },
  { value: 6, label: 'Último' }
];

const SimulatorFields = [
  {
    // Tipo de condição: Drenada / Não drenada - Freática/Piezométrica: Específica
    // Campo: NA montante, NA jusante
    conditions: [1, 2],
    phreatic_piezometric: 1,
    fields: ['UpstreamWaterLevelReference', 'UpstreamWaterLevel', 'DownstreamWaterLevelReference', 'DownstreamWaterLevel']
  },
  {
    // Tipo de condição: Drenada / Não Drenada - Freática/Piezométrica: Estatística
    // Campos:  Referência leituras, Período, Depois de, Antes de,  Ref. N.A. montante, N.A. montante, Ref. N.A. jusante, N.A. jusante
    conditions: [1, 2],
    phreatic_piezometric: 2,
    fields: [
      'ReferenceReadings',
      'Period',
      'AfterDate',
      'BeforeDate',
      'UpstreamWaterLevelReference',
      'UpstreamWaterLevel',
      'DownstreamWaterLevelReference',
      'DownstreamWaterLevel'
    ]
  },
  {
    // Tipo de condição: Drenada / Não Drenada - Freática/Piezométrica: Variação
    // Campos: Referência leituras, Período, Variação freática (m), Depois de, Antes de, Variação freática (m)
    // Campos: Ref. N.A. montante, N.A. montante, Ref. N.A. jusante, N.A. jusante
    conditions: [1, 2],
    phreatic_piezometric: 3,
    fields: [
      'ReferenceReadings',
      'Period',
      'PhreaticVariation',
      'AfterDate',
      'BeforeDate',
      'UpstreamWaterLevelReference',
      'UpstreamWaterLevel',
      'DownstreamWaterLevelReference',
      'DownstreamWaterLevel'
    ]
  },
  {
    // Tipo de condição: Pseudo estática - Freática/Piezométrica: Específica
    // Campos: Coeficiente Sísmico Horizontal, Coeficiente Sísmico Vertical, N.A. montante (m), N.A. jusante
    // Sismo critico
    conditions: [3],
    phreatic_piezometric: 1,
    fields: [
      'SeismicCoefficientHorizontal',
      'SeismicCoefficientVertical',
      'UpstreamWaterLevelReference',
      'UpstreamWaterLevel',
      'DownstreamWaterLevelReference',
      'DownstreamWaterLevel'
    ]
  },
  {
    // Tipo de condição: Pseudo estática - Freática/Piezométrica: Estatística
    // Campos: Referência leituras, Período, Depois de, Antes de, Coeficiente Sísmico Horizontal, Coeficiente Sísmico Vertical
    // Campos:  Ref. N.A. montante, N.A. montante, Ref. N.A. jusante, N.A. jusante
    conditions: [3],
    phreatic_piezometric: 2,
    fields: [
      'ReferenceReadings',
      'Period',
      'AfterDate',
      'BeforeDate',
      'SeismicCoefficientHorizontal',
      'SeismicCoefficientVertical',
      'UpstreamWaterLevelReference',
      'UpstreamWaterLevel',
      'DownstreamWaterLevelReference',
      'DownstreamWaterLevel'
    ]
  },
  {
    // Tipo de condição: Pseudo estática - Freática/Piezométrica: Variação
    // Campos: Referência leituras, Período, Depois de, Antes de, Variação freática (m), Coeficiente Sísmico Horizontal, Coeficiente Sísmico Vertical
    // Campos: Ref. N.A. montante, N.A. montante, Ref. N.A. jusante, N.A. jusante
    conditions: [3],
    phreatic_piezometric: 3,
    fields: [
      'ReferenceReadings',
      'Period',
      'PhreaticVariation',
      'AfterDate',
      'BeforeDate',
      'SeismicCoefficientHorizontal',
      'SeismicCoefficientVertical',
      'UpstreamWaterLevelReference',
      'UpstreamWaterLevel',
      'DownstreamWaterLevelReference',
      'DownstreamWaterLevel'
    ]
  }
];

const PhreaticPiezometricFields = [
  {
    phreatic_piezometric: 1,
    description: 'Específica',
    reference: 'water_table_configuration',
    fields: [
      {
        form: 'UpstreamWaterLevelReference',
        label: 'Ref. N.A. montante',
        reference: 'upstream_linimetric_ruler_statistical_measure',
        constant: 'UpstreamWaterLevelReference'
      },
      { form: 'UpstreamWaterLevel', label: 'N.A. montante (m)', reference: 'upstream_linimetric_ruler_quota', constant: null },
      {
        form: 'DownstreamWaterLevelReference',
        label: 'Ref. N.A. jusante',
        reference: 'downstream_linimetric_ruler_statistical_measure',
        constant: 'DownstreamWaterLevelReference'
      },
      { form: 'DownstreamWaterLevel', label: 'N.A. jusante (m)', reference: 'downstream_linimetric_ruler_quota', constant: null }
    ]
  },
  {
    phreatic_piezometric: 2,
    description: 'Estatística',
    reference: 'water_table_configuration',
    fields: [
      { form: 'ReferenceReadings', label: 'Referência leituras', reference: 'reading_statistical_measure', constant: 'ReferenceReadings' },
      { form: 'Period', label: 'Período', reference: '', constant: null },
      { form: 'AfterDate', label: 'Antes de', reference: 'start_date', constant: null },
      { form: 'BeforeDate', label: 'Depois de', reference: 'end_date', constant: null },
      {
        form: 'UpstreamWaterLevelReference',
        label: 'Ref. N.A. montante',
        reference: 'upstream_linimetric_ruler_statistical_measure',
        constant: 'UpstreamWaterLevelReference'
      },
      { form: 'UpstreamWaterLevel', label: 'N.A. montante (m)', reference: 'upstream_linimetric_ruler_quota', constant: null },
      {
        form: 'DownstreamWaterLevelReference',
        label: 'Ref. N.A. jusante',
        reference: 'downstream_linimetric_ruler_statistical_measure',
        constant: 'DownstreamWaterLevelReference'
      },
      { form: 'DownstreamWaterLevel', label: 'N.A. jusante (m)', reference: 'downstream_linimetric_ruler_quota', constant: null }
    ]
  },
  {
    phreatic_piezometric: 3,
    description: 'Variação',
    reference: 'water_table_configuration',
    fields: [
      { form: 'ReferenceReadings', label: 'Referência leituras', reference: 'reading_statistical_measure', constant: 'ReferenceReadings' },
      { form: 'Period', label: 'Período', reference: '', constant: null },
      { form: 'AfterDate', label: 'Antes de', reference: 'start_date', constant: null },
      { form: 'BeforeDate', label: 'Depois de', reference: 'end_date', constant: null },
      {
        form: 'UpstreamWaterLevelReference',
        label: 'Ref. N.A. montante',
        reference: 'upstream_linimetric_ruler_statistical_measure',
        constant: 'UpstreamWaterLevelReference'
      },
      { form: 'UpstreamWaterLevel', label: 'N.A. montante (m)', reference: 'upstream_linimetric_ruler_quota', constant: null },
      {
        form: 'DownstreamWaterLevelReference',
        label: 'Ref. N.A. jusante',
        reference: 'downstream_linimetric_ruler_statistical_measure',
        constant: 'DownstreamWaterLevelReference'
      },
      { form: 'DownstreamWaterLevel', label: 'N.A. jusante (m)', reference: 'downstream_linimetric_ruler_quota', constant: null },
      { form: 'PhreaticVariation', label: 'Variação freática', reference: 'water_table_variation' }
    ]
  }
];

const ConditionFields = [
  {
    condition: 1,
    description: 'Drenada',
    reference: 'should_evaluate_drained_condition',
    fields: []
  },
  {
    condition: 2,
    description: 'Não drenada',
    reference: 'should_evaluate_undrained_condition',
    fields: []
  },
  {
    condition: 3,
    description: 'Pseudo estática',
    reference: 'should_evaluate_pseudo_static_condition',
    fields: [
      { form: 'SeismicCoefficientHorizontal', label: 'Coeficiente sísmico horizontal', reference: 'seismic_coefficient.horizontal', constant: null },
      { form: 'SeismicCoefficientVertical', label: 'Coeficiente sísmico vertical', reference: 'seismic_coefficient.vertical', constant: null }
    ]
  }
];

const SectionFields = [
  { form: 'MinimumDrainedDepth', label: 'Prof. mín. drenada (m)', reference: 'minimum_drained_depth', constant: null },
  { form: 'BeachLengthReference', label: 'Referência praia', reference: 'beach_length_statistical_measure', constant: 'BeachLengthReference' },
  { form: 'MinimumUndrainedDepth', label: 'Prof. mín. não drenada (m)', reference: 'minimum_undrained_depth', constant: null },
  { form: 'BeachLength', label: 'Comp. praia (m)', reference: 'beach_length', constant: null },
  { form: 'MinimumPseudoStaticDepth', label: 'Prof. mín. pseudo-estática (m)', reference: 'minimum_pseudo_static_depth', constant: null }
];

const SearchMethodsFieldsLabels = {
  divisions_along_slope: {
    name: 'Divisões ao longo do talude:'
  },
  circles_per_division: {
    name: 'Círculos por divisão:'
  },
  number_of_iterations: {
    name: 'Número de iterações:'
  },
  divisions_next_iteration: {
    name: 'Divisões para próxima iteração (%):'
  },
  surfaces_per_division: {
    name: 'Superfícíes por divisão:'
  },
  number_of_vertices_along_surface: {
    name: 'Número de vértices ao longo da superfíce:'
  },
  radius_increment: {
    name: 'Incremento de raio:'
  },
  number_of_surfaces: {
    name: 'Número de superfícies:'
  },
  initial_number_of_surface_vertices: {
    name: 'Número inicial de vértices das cunhas:'
  },
  maximum_iterations: {
    name: 'Máximo de iterações:'
  },
  number_of_nests: {
    name: 'Número de ninhos:'
  },
  initial_number_of_iterations: {
    name: 'Número inicial de iterações:'
  },
  maximum_number_of_steps: {
    name: 'Número máximo de etapas:'
  },
  number_of_factors_safety_compared_before_stopping: {
    name: 'Número de fatores de segurança comparados antes de parar:'
  },
  tolerance_for_stopping_criterion: {
    name: 'Tolerância para critério de parada:'
  },
  number_of_particles: {
    name: 'Número de partículas:'
  }
};

export {
  AnalysisType,
  Conditions,
  CalculationMethods,
  Period,
  PhreaticPiezometric,
  UpstreamWaterLevelReference,
  DownstreamWaterLevelReference,
  BeachLengthReference,
  ReferenceReadings,
  SurfaceType,
  SearchMethod,
  SimulatorFields,
  PhreaticPiezometricFields,
  ConditionFields,
  SectionFields,
  SearchMethodsFieldsLabels
};
