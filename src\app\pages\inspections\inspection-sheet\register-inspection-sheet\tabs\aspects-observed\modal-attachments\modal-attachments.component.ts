import { Component, ElementRef, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { ModalMapComponent } from '@components/modal/modal-map/modal-map.component';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-modal-attachments',
  templateUrl: './modal-attachments.component.html',
  styleUrls: ['./modal-attachments.component.scss'],
  providers: [NgbActiveModal]
})
export class ModalAttachmentsComponent implements OnInit {
  @ViewChild('modalAttachments') modalAttachments!: ElementRef; // Referência ao modal
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  @Output() saveAttachments = new EventEmitter();

  @Input() occurrenceAttachments: FormArray; // Recebe o FormArray de anexos do componente pai
  @Input() hierarchy: any;

  @Input() public status: number = null;
  @Input() public locked: boolean = false;
  @Input() public view: boolean = false;

  public newAttachmentForm!: FormGroup; // Formulário para novo anexo
  public occurrenceAttachmentsForm!: FormGroup; // Formulário principal que encapsula os anexos

  constructor(private fb: FormBuilder, private modalService: NgbModal, public activeModal: NgbActiveModal) {}

  /**
   * Inicializa o componente ao ser carregado.
   */
  ngOnInit(): void {
    this.initializeNewAttachmentForm();
  }

  /**
   * Detecta e processa mudanças nos inputs vinculados ao componente.
   * @param {SimpleChanges} changes - Contém as mudanças detectadas nos inputs do componente.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['occurrenceAttachments']) {
      const current = changes['occurrenceAttachments'].currentValue;
      if (current && current instanceof FormArray) {
        current.controls.forEach((control, index) => {
          const fileControl = control.get('file');
          if (fileControl) {
            const base64Value = fileControl.value.base64;
            const fileName = fileControl.value.name; // Preserve o nome do arquivo

            if (!base64Value) {
              return;
            }

            if (!base64Value.startsWith('data:image/')) {
              const decode = fn.base64Extension(base64Value.slice(0, 100));
              if (decode.mimeType) {
                const newBase64Value = `data:${decode.mimeType};base64,${base64Value}`;

                fileControl.patchValue({
                  base64: newBase64Value,
                  name: fileName // Preserva o valor do nome
                });
              }
            }
          }
        });
        this.occurrenceAttachmentsForm = this.fb.group({
          occurrence_attachments: current
        });
      }
    }
  }

  /**
   * Inicializa o formulário para anexos.
   */
  initializeNewAttachmentForm(): void {
    this.newAttachmentForm = this.fb.group({
      file: this.fb.group({
        base64: ['', Validators.required],
        name: ['', Validators.required]
      }),
      northing: [null],
      easting: [null]
    });
  }

  /**
   * Obtém o FormArray de anexos da ocorrência.
   * @returns {FormArray} - O array de anexos.
   */
  get attachments(): FormArray {
    return this.occurrenceAttachmentsForm.get('occurrence_attachments') as FormArray;
  }

  /**
   * Remove um anexo pelo índice especificado.
   * @param {number} index - Índice do anexo a ser removido.
   */
  removeAttachment(index: number): void {
    this.attachments.removeAt(index);
  }

  /**
   * Adiciona um novo anexo ao formulário.
   */
  addAttachment(): void {
    if (this.newAttachmentForm.valid) {
      const northing = this.newAttachmentForm.get('northing')?.value;
      const easting = this.newAttachmentForm.get('easting')?.value;
      const file = this.newAttachmentForm.get('file.name')?.value;

      if (!northing || !easting || !file) {
        return;
      }

      const newAttachment = this.fb.group(this.newAttachmentForm.value);
      this.attachments.push(newAttachment);

      this.reset();
    } else {
      //console.warn('Formulário inválido.');
    }
  }

  /**
   * Reseta o formulário de novo anexo.
   */
  reset() {
    // Reseta o formulário
    this.newAttachmentForm.reset();
    this.newAttachmentForm.get('file')?.patchValue({
      base64: '',
      name: ''
    });

    const fileInput = document.querySelector('#fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = ''; // Reset apenas no input de arquivo
    }
  }

  /**
   * Processa a seleção de um arquivo e converte para Base64.
   * @param {Event} event - Evento gerado pela seleção de arquivo no input.
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      const reader = new FileReader();

      reader.onload = () => {
        this.newAttachmentForm.get('file')?.patchValue({
          base64: reader.result,
          name: file.name // Atualiza o nome do arquivo no formulário
        });
      };

      reader.readAsDataURL(file);
    }
  }

  /**
   * Exibe o mapa em um modal para seleção de coordenadas.
   * @param {any} $event - Dados passados para o modal.
   */
  showMap($event) {
    const modalRef = this.modalService.open(ModalMapComponent, { size: 'xl' });
    modalRef.componentInstance.title = 'da Estrutura ' + this.hierarchy.structure.name;
    modalRef.componentInstance.coordinates = this.hierarchy.structure.coordinate_setting;
    modalRef.componentInstance.data = $event;
    modalRef.componentInstance.sendClickEvent.subscribe(($event) => this.clickEvent($event));
  }

  /**
   * Processa eventos emitidos pelo modal de mapa.
   * @param {any} $event - Evento contendo os dados das coordenadas selecionadas.
   */
  clickEvent($event) {
    if ($event.type === 'coordinates') {
      // Certifique-se de usar comparação estrita
      this.newAttachmentForm.patchValue({
        easting: $event.coordinates.easting,
        northing: $event.coordinates.northing
      });
    }
  }

  /**
   * Salva os anexos e emite o FormArray atualizado para o componente pai.
   */
  save() {
    if (this.occurrenceAttachmentsForm.valid) {
      this.attachments.controls.forEach((control) => {
        const fileControl = control.get('file');
        if (fileControl) {
          const base64Value = fileControl.get('base64')?.value;
          if (base64Value?.startsWith('data:image/jpeg;base64,')) {
            fileControl.patchValue({
              base64: base64Value.replace('data:image/jpeg;base64,', '')
            });
          } else if (base64Value?.startsWith('data:image/png;base64,')) {
            fileControl.patchValue({
              base64: base64Value.replace('data:image/png;base64,', '')
            });
          }
        }
      });
      // Emitir o FormArray atualizado para o componente pai
      this.saveAttachments.emit(this.occurrenceAttachmentsForm.get('occurrence_attachments') as FormArray);
      this.modalService.dismissAll();
      // this.activeModal.close(); // Fecha o modal
    }
  }

  /**
   * Abre o modal de anexos.
   */
  openModal() {
    this.modalService.open(this.modalAttachments, { size: 'lg' });
  }
}
