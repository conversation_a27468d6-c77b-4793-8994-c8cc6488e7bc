.logo-inspection {
  width: 200px; /* Define a largura fixa */
  height: 38px; /* Mantém a altura proporcional */
  display: block; /* Remove espaçamentos extras */
  max-width: 100%; /* Evita problemas em telas menores */
}

.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.card-header {
  background-color: #34b575;
  color: #ffffff;
  font-size: 0.875em;
}

label.title {
  font-size: 0.875em;
  font-weight: 800;
  margin-bottom: 4px;
}

span.content {
  font-size: 0.875em;
}

div[class^='col-'] {
  margin: 6px 0;
  /* Suas regras de estilo aqui */
}

.subheader {
  background-color: rgba(3, 37, 97, 1);
  color: #ffffff;
  height: 32px;
}

/* table > thead > tr > th,
.header-title {
  background: rgba(3, 37, 97, 0.1);
  font-size: 0.875em !important;
  text-align: center;
  color: #032561;
  border: 1px solid #ffffff;
  font-weight: bold;
}

.header-title {
  font-size: 1em !important;
  font-weight: bold;
}

.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #ffffff;
}

tbody {
  font-size: 0.875em !important;
  text-align: center;
  border: 1px solid #d4d2d2;
  color: #032561;
  background-color: rgba(212, 210, 210, 0.1);
}
 */
