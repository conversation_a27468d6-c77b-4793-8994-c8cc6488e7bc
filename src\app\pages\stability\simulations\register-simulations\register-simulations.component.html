<div class="list-content">
  <form [formGroup]="formSimulation">
    <div class="row mt-2">
      <!-- Filtro Cliente, Unidade, Estrutura -->
      <app-hierarchy
        #hierarchy
        [elements]="elements"
        class="col-md-12"
        (sendEventHierarchy)="getEventHierarchy($event)"
      ></app-hierarchy>
    </div>
    <div class="row mt-2">
      <!-- Seção -->
      <div class="col-md-4">
        <label class="form-label">Seção</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione'"
          [settings]="sectionSettings"
          [data]="sections"
          formControlName="SectionId"
        ></ng-multiselect-dropdown>

        <!-- Mensagens relacionadas à validação da seção selecionada -->
        <div
          class="alert alert-danger mt-2"
          *ngIf="messageNoDxf.status"
          [innerHTML]="messageNoDxf.text"
        ></div>

        <div
          class="alert alert-danger mt-2"
          *ngIf="messageNoInstrument.status"
          [innerHTML]="messageNoInstrument.text"
        ></div>

        <small
          class="form-text text-danger"
          *ngIf="
            !formSimulation.get('SectionId').valid &&
            formSimulation.get('SectionId').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>

    <!-- Parâmetros circulares e não circulares -->
    <div
      class="row mt-3"
      *ngIf="formSimulation.get('SectionId').value.length > 0"
    >
      <span class="mb-2">
        <i class="fa fa-exclamation-circle me-2"></i>
        Selecione pelo menos um tipo de superfície.
      </span>

      <!-- Circular -->
      <div class="col-md-6">
        <div class="simulation-surface">
          <div class="section-title d-flex align-items-center">
            <input
              class="form-check-input me-2"
              type="checkbox"
              formControlName="SurfaceTypeCircular"
            />
            Tipo de Superfície: Circular
          </div>
          <div class="row">
            <!-- Método de cálculo -->
            <div class="col-md-6">
              <label class="form-label">Método de cálculo</label>
              <ng-multiselect-dropdown
                [placeholder]="'Selecione'"
                [settings]="dropdownSettings"
                [data]="calculationMethods.Circular"
                formControlName="CalculationMethodsCircular"
                [disabled]="
                  formSimulation.get('CalculationMethodsCircular').disabled
                "
                [ngClass]="{
                  'disabled-dropdown': formSimulation.get(
                    'CalculationMethodsCircular'
                  ).disabled
                }"
              ></ng-multiselect-dropdown>
              <small
                class="form-text text-danger"
                *ngIf="
                  formSimulation.get('SurfaceTypeCircular').value &&
                  !formSimulation.get('CalculationMethodsCircular').valid &&
                  formSimulation.get('CalculationMethodsCircular').touched
                "
                >Campo Obrigatório.</small
              >
            </div>

            <!-- Método de busca -->
            <div class="col-md-6">
              <label class="form-label">Método de busca</label>
              <div class="d-flex align-items-end">
                <select
                  class="form-select"
                  formControlName="SearchMethodCircular"
                >
                  <option value="">Selecione</option>
                  <option
                    *ngFor="let method of searchMethod.Circular"
                    [value]="method.value"
                  >
                    {{ method.method }}
                  </option>
                </select>
                <app-button
                  [class]="'btn-logisoil-search-method'"
                  [icon]="'fa fa-cog'"
                  [type]="false"
                  style="margin-bottom: 4px"
                  (click)="
                    formSimulation.get('SurfaceTypeCircular').value != '' &&
                      ModalSearchMethodCircular.openModal()
                  "
                ></app-button>
              </div>
              <small
                class="form-text text-danger"
                *ngIf="
                  formSimulation.get('SurfaceTypeCircular').value &&
                  !formSimulation.get('SearchMethodCircular').valid &&
                  formSimulation.get('SearchMethodCircular').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Não Circular -->
      <div class="col-md-6">
        <div class="simulation-surface">
          <div class="section-title d-flex align-items-center">
            <input
              class="form-check-input me-2"
              type="checkbox"
              formControlName="SurfaceTypeNonCircular"
            />
            Tipo de Superfície: Não Circular
          </div>
          <div class="row">
            <!-- Método de cálculo -->
            <div class="col-md-6">
              <label class="form-label">Método de cálculo</label>
              <ng-multiselect-dropdown
                [placeholder]="'Selecione'"
                [settings]="dropdownSettings"
                [data]="calculationMethods.NonCircular"
                formControlName="CalculationMethodsNonCircular"
                [disabled]="
                  formSimulation.get('CalculationMethodsNonCircular').disabled
                "
                [ngClass]="{
                  'disabled-dropdown': formSimulation.get(
                    'CalculationMethodsNonCircular'
                  ).disabled
                }"
              ></ng-multiselect-dropdown>
              <small
                class="form-text text-danger"
                *ngIf="
                  formSimulation.get('SurfaceTypeNonCircular').value &&
                  !formSimulation.get('CalculationMethodsNonCircular').valid &&
                  formSimulation.get('CalculationMethodsNonCircular').touched
                "
                >Campo Obrigatório.</small
              >
            </div>

            <!-- Método de busca -->
            <div class="col-md-6">
              <label class="form-label">Método de busca</label>
              <div class="d-flex align-items-end">
                <select
                  class="form-select"
                  formControlName="SearchMethodNonCircular"
                >
                  <option value="">Selecione</option>
                  <option
                    *ngFor="let method of searchMethod.NonCircular"
                    [value]="method.value"
                  >
                    {{ method.method }}
                  </option>
                </select>
                <app-button
                  [class]="'btn-logisoil-search-method'"
                  [icon]="'fa fa-cog'"
                  [type]="false"
                  style="margin-bottom: 4px"
                  (click)="
                    formSimulation.get('SurfaceTypeNonCircular').value != '' &&
                      ModalSearchMethodNonCircular.openModal()
                  "
                ></app-button>
              </div>
              <small
                class="form-text text-danger"
                *ngIf="
                  formSimulation.get('SurfaceTypeNonCircular').value &&
                  !formSimulation.get('SearchMethodNonCircular').valid &&
                  formSimulation.get('SearchMethodNonCircular').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Validação das superfícies -->
      <small
        class="form-text text-danger"
        *ngIf="
          formSimulation.get('SurfaceTypeCircular').value === false &&
          formSimulation.get('SurfaceTypeCircular').touched &&
          formSimulation.get('SurfaceTypeNonCircular').value === false &&
          formSimulation.get('SurfaceTypeNonCircular').touched
        "
        >Selecione pelo menos um tipo de superfície</small
      >
    </div>

    <!-- Condição e Freática/Piezométrica -->
    <div
      class="row mt-2"
      *ngIf="formSimulation.get('SectionId').value.length > 0"
    >
      <!-- Condição -->
      <div class="col-md-6">
        <label class="form-label">Condição</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione'"
          [settings]="conditionSettings"
          [data]="conditions"
          formControlName="Conditions"
        ></ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formSimulation.get('Conditions').valid &&
            formSimulation.get('Conditions').touched
          "
          >Campo Obrigatório.</small
        >
        <div *ngIf="ctrlSism">
          <input
            class="form-check-input me-1"
            type="checkbox"
            formControlName="SafetyFactorTargetCheck"
          />
          <label class="form-label">Sismo crítico</label>
        </div>
      </div>
      <!-- Freática/Piezométrica -->
      <div class="col-md-3">
        <label class="form-label">Freática/Piezométrica</label>
        <select class="form-select" formControlName="PhreaticPiezometric">
          <option value="">Selecione</option>
          <ng-template ngFor let-item [ngForOf]="phreaticPiezometric">
            <option [ngValue]="item.value">
              {{ item.label }}
            </option>
          </ng-template>
        </select>
        <small
          class="form-text text-danger"
          *ngIf="
            !formSimulation.get('PhreaticPiezometric').valid &&
            formSimulation.get('PhreaticPiezometric').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- FS Alvo -->
      <div
        class="col-md-3"
        *ngIf="formSimulation.get('SafetyFactorTargetCheck').value === true"
      >
        <label class="form-label">FS alvo</label>
        <input
          type="number"
          min="-9999999999999"
          max="9999999999999"
          class="form-control"
          formControlName="SafetyFactorTarget"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber($event, formSimulation.get('SafetyFactorTarget'))
          "
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSimulation.get('SafetyFactorTarget').valid &&
            formSimulation.get('SafetyFactorTarget').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <div
      formArrayName="SectionForm"
      *ngIf="
        formSimulation.get('SectionId').value.length > 0 &&
        formSimulation.get('PhreaticPiezometric')?.value
      "
    >
      <div
        *ngFor="let section of sectionForm.controls; let i = index"
        [formGroupName]="i"
      >
        <hr />

        <div class="row mt-2">
          <!-- Seção -->
          <div class="col-md-4">
            <label class="form-label">Seção</label>
            <input
              type="text"
              class="form-control"
              formControlName="MinimumSectionName"
            />
          </div>

          <!-- Revisão atual da Seção -->
          <div class="col-md-4">
            <label class="form-label">Revisão</label>
            <ng-multiselect-dropdown
              [placeholder]="'Selecione'"
              [settings]="sectionReviewsSettings"
              [data]="sectionsReviews[section.get('MinimumSectionId').value]"
              formControlName="SectionReviewId"
              (onSelect)="onReviewChange(i)"
              (onDeSelect)="onReviewChange(i)"
            ></ng-multiselect-dropdown>
            <small
              class="form-text text-danger"
              *ngIf="
                section.get('SectionReviewId')?.value[0]?.id !=
                sectionsReviewsLatest[section.get('MinimumSectionId')?.value]
                  ?.id
              "
            >
              A revisão selecionada não é a revisão mais atual.
            </small>
          </div>

          <!-- Etapa de obra -->
          <div
            class="col-md-4"
            *ngIf="
              sectionConstructionStages[section.get('MinimumSectionId')?.value]
                ?.length > 0
            "
          >
            <label class="form-label">Etapa de obra</label>
            <select class="form-select" formControlName="ConstructionStageId">
              <option
                *ngIf="
                  sectionConstructionStages[
                    section.get('MinimumSectionId')?.value
                  ]?.length > 1
                "
                value=""
              >
                Selecione
              </option>
              <option
                *ngFor="
                  let stage of sectionConstructionStages[
                    section.get('MinimumSectionId')?.value
                  ]
                "
                [value]="stage.id"
              >
                {{ stage.stage }}
              </option>
            </select>
          </div>
        </div>

        <div class="row mt-2">
          <!-- Prof. mín. drenada (m) -->
          <div class="col-md-3" *ngIf="section.get('MinimumDrainedDepth')">
            <label class="form-label">Prof. mín. drenada (m)</label>
            <input
              type="text"
              min="0"
              decimalplaces="0.00001"
              step="1"
              class="form-control"
              formControlName="MinimumDrainedDepth"
              (keypress)="
                func.controlNumber(
                  $event,
                  section.get('MinimumDrainedDepth'),
                  'positiveDecimalDot'
                )
              "
              (keyup)="
                func.controlNumber($event, section.get('MinimumDrainedDepth'))
              "
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !section.get('MinimumDrainedDepth').valid &&
                section.get('MinimumDrainedDepth').touched
              "
            >
              Campo Obrigatório.
            </small>
          </div>

          <!--  Prof. mín. não drenada (m)-->
          <div class="col-md-3" *ngIf="section.get('MinimumUndrainedDepth')">
            <label class="form-label">Prof. mín. não drenada (m)</label>
            <input
              type="text"
              min="0"
              decimalplaces="0.00001"
              step="1"
              class="form-control"
              formControlName="MinimumUndrainedDepth"
              (keypress)="
                func.controlNumber(
                  $event,
                  section.get('MinimumUndrainedDepth'),
                  'positiveDecimalDot'
                )
              "
              (keyup)="
                func.controlNumber($event, section.get('MinimumUndrainedDepth'))
              "
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !section.get('MinimumUndrainedDepth').valid &&
                section.get('MinimumUndrainedDepth').touched
              "
            >
              Campo Obrigatório.
            </small>
          </div>

          <!-- Prof. mín. pseudo-estática (m) -->
          <div class="col-md-3" *ngIf="section.get('MinimumPseudoStaticDepth')">
            <label class="form-label">Prof. mín. pseudo-estática (m)</label>
            <input
              type="text"
              min="0"
              decimalplaces="0.00001"
              step="1"
              class="form-control"
              formControlName="MinimumPseudoStaticDepth"
              (keypress)="
                func.controlNumber(
                  $event,
                  section.get('MinimumPseudoStaticDepth'),
                  'positiveDecimalDot'
                )
              "
              (keyup)="
                func.controlNumber(
                  $event,
                  section.get('MinimumPseudoStaticDepth')
                )
              "
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !section.get('MinimumPseudoStaticDepth').valid &&
                section.get('MinimumPseudoStaticDepth').touched
              "
            >
              Campo Obrigatório.
            </small>
          </div>
        </div>
        <!-- Praia -->
        <div class="row mt-2">
          <!-- Referência praia -->
          <div class="col-md-3" *ngIf="section.get('BeachLengthReference')">
            <label class="form-label">Referência praia</label>
            <select class="form-select" formControlName="BeachLengthReference">
              <ng-template ngFor let-item [ngForOf]="beachLengthReference">
                <option [ngValue]="item.value">{{ item.label }}</option>
              </ng-template>
            </select>
            <small
              class="form-text text-danger"
              *ngIf="
                !section.get('BeachLengthReference').valid &&
                section.get('BeachLengthReference').touched
              "
            >
              Campo Obrigatório.
            </small>
          </div>
          <!-- Comp. praia (m) -->
          <div class="col-md-3" *ngIf="section.get('BeachLength')">
            <label class="form-label">Comp. praia (m)</label>
            <input
              type="number"
              min="-9999999999999"
              max="9999999999999"
              class="form-control"
              formControlName="BeachLength"
              (keypress)="func.controlNumber($event, null, 'notE')"
              (keyup)="func.controlNumber($event, section.get('BeachLength'))"
              [attr.placeholder]="section.get('BeachLength')['placeholder']"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !section.get('BeachLength').valid &&
                section.get('BeachLength').touched &&
                section.get('BeachLength').enabled
              "
            >
              Campo Obrigatório.
            </small>
          </div>
        </div>

        <div
          class="col-md-12 mt-3 alert"
          *ngIf="
            sectionsMap[section.get('MinimumSectionId').value]?.message?.status
          "
          [ngClass]="
            sectionsMap[section.get('MinimumSectionId').value].message.class
          "
          role="alert"
        >
          {{ sectionsMap[section.get('MinimumSectionId').value].message.text }}
        </div>
      </div>
    </div>
    <!-- Formulário -->
    <div
      class="row mt-3"
      *ngIf="
        formSimulation.get('SectionId')?.value?.length > 0 &&
        showFields &&
        formSimulation.get('PhreaticPiezometric')?.value !== 4 &&
        !disabledSimulation
      "
    >
      <div class="col-md-12">
        <div class="simulation-readings">
          <div class="section-title">{{ titleCard }}</div>
          <div class="row">
            <!-- Referência leituras -->
            <div class="col-md-3" *ngIf="showFieldsItems.ReferenceReadings">
              <label class="form-label">Referência leituras</label>
              <select class="form-select" formControlName="ReferenceReadings">
                <option value="">Selecione</option>
                <ng-template ngFor let-item [ngForOf]="referenceReadings">
                  <option [ngValue]="item.value">{{ item.label }}</option>
                </ng-template>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('ReferenceReadings').valid &&
                  formSimulation.get('ReferenceReadings').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Período -->
            <div class="col-md-3" *ngIf="showFieldsItems.Period">
              <label class="form-label">Período</label>
              <select class="form-select" formControlName="Period">
                <ng-template ngFor let-item [ngForOf]="period">
                  <option [ngValue]="item.value">{{ item.label }}</option>
                </ng-template>
                <option value="0">Personalizado</option>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('Period').valid &&
                  formSimulation.get('Period').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Depois de -->
            <div
              class="col-md-3"
              *ngIf="
                showFieldsItems.AfterDate &&
                formSimulation.get('Period').value != 1
              "
            >
              <label class="form-label">Depois de</label>
              <input
                type="date"
                class="form-control"
                formControlName="AfterDate"
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('AfterDate').valid &&
                  formSimulation.get('AfterDate').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Antes de -->
            <div
              class="col-md-3"
              *ngIf="
                showFieldsItems.BeforeDate &&
                formSimulation.get('Period').value != 1
              "
            >
              <label class="form-label">Antes de</label>
              <input
                type="date"
                class="form-control"
                formControlName="BeforeDate"
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('BeforeDate').valid &&
                  formSimulation.get('BeforeDate').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Variação freática (m) -->
            <div class="col-md-3" *ngIf="showFieldsItems.PhreaticVariation">
              <label class="form-label">Variação freática (m)</label>
              <input
                type="number"
                min="-9999999999999"
                max="9999999999999"
                class="form-control"
                formControlName="PhreaticVariation"
                (keypress)="func.controlNumber($event, null, 'notE')"
                (keyup)="
                  func.controlNumber(
                    $event,
                    formSimulation.get('PhreaticVariation')
                  )
                "
                appDisableScroll
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('PhreaticVariation').valid &&
                  formSimulation.get('PhreaticVariation').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Coeficiente Sísmico Horizontal -->
            <div
              class="col-md-3"
              *ngIf="showFieldsItems.SeismicCoefficientHorizontal"
            >
              <label class="form-label">Coeficiente Sísmico Horizontal</label>
              <input
                type="number"
                class="form-control"
                formControlName="SeismicCoefficientHorizontal"
                (keypress)="func.controlNumber($event, null, 'notE')"
                (keyup)="
                  func.controlNumber(
                    $event,
                    formSimulation.get('SeismicCoefficientHorizontal')
                  )
                "
                appDisableScroll
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('SeismicCoefficientHorizontal').valid &&
                  formSimulation.get('SeismicCoefficientHorizontal').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Coeficiente Sísmico Vertical -->
            <div
              class="col-md-3"
              *ngIf="showFieldsItems.SeismicCoefficientVertical"
            >
              <label class="form-label">Coeficiente Sísmico Vertical</label>
              <input
                type="number"
                class="form-control"
                formControlName="SeismicCoefficientVertical"
                (keypress)="func.controlNumber($event, null, 'notE')"
                (keyup)="
                  func.controlNumber(
                    $event,
                    formSimulation.get('SeismicCoefficientVertical')
                  )
                "
                appDisableScroll
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('SeismicCoefficientVertical').valid &&
                  formSimulation.get('SeismicCoefficientVertical').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Ref. N.A. montante -->
            <div
              class="col-md-3"
              *ngIf="showFieldsItems.UpstreamWaterLevelReference"
            >
              <label class="form-label">Ref. N.A. montante</label>
              <select
                class="form-select"
                formControlName="UpstreamWaterLevelReference"
                [disabled]="
                  !formSimulation.get('UpstreamWaterLevelReference').enabled
                "
              >
                <ng-template
                  ngFor
                  let-item
                  [ngForOf]="upstreamWaterLevelReference"
                >
                  <option [ngValue]="item.value">{{ item.label }}</option>
                </ng-template>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('UpstreamWaterLevelReference').valid &&
                  formSimulation.get('UpstreamWaterLevelReference').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- N.A. montante (m) -->
            <div class="col-md-3" *ngIf="showFieldsItems.UpstreamWaterLevel">
              <label class="form-label">N.A. montante (m)</label>
              <input
                type="number"
                class="form-control"
                formControlName="UpstreamWaterLevel"
                [disabled]="!formSimulation.get('UpstreamWaterLevel').enabled"
                (keypress)="func.controlNumber($event, null, 'notE')"
                (keyup)="
                  func.controlNumber(
                    $event,
                    formSimulation.get('UpstreamWaterLevel')
                  )
                "
                [placeholder]="
                  formSimulation.get('UpstreamWaterLevel')['placeholder']
                "
                appDisableScroll
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('UpstreamWaterLevel').valid &&
                  formSimulation.get('UpstreamWaterLevel').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Ref. N.A. jusante -->
            <div
              class="col-md-3"
              *ngIf="showFieldsItems.DownstreamWaterLevelReference"
            >
              <label class="form-label">Ref. N.A. jusante</label>
              <select
                class="form-select"
                formControlName="DownstreamWaterLevelReference"
                [disabled]="
                  !formSimulation.get('DownstreamWaterLevelReference').enabled
                "
              >
                <ng-template
                  ngFor
                  let-item
                  [ngForOf]="downstreamWaterLevelReference"
                >
                  <option [ngValue]="item.value">{{ item.label }}</option>
                </ng-template>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('DownstreamWaterLevelReference').valid &&
                  formSimulation.get('DownstreamWaterLevelReference').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- N.A. jusante (m) -->
            <div class="col-md-3" *ngIf="showFieldsItems.DownstreamWaterLevel">
              <label class="form-label">N.A. jusante (m)</label>
              <input
                type="number"
                class="form-control"
                formControlName="DownstreamWaterLevel"
                [disabled]="!formSimulation.get('DownstreamWaterLevel').enabled"
                (keypress)="func.controlNumber($event, null, 'notE')"
                (keyup)="
                  func.controlNumber(
                    $event,
                    formSimulation.get('DownstreamWaterLevel')
                  )
                "
                [placeholder]="
                  formSimulation.get('DownstreamWaterLevel')['placeholder']
                "
                appDisableScroll
              />
              <small
                class="form-text text-danger"
                *ngIf="
                  !formSimulation.get('DownstreamWaterLevel').valid &&
                  formSimulation.get('DownstreamWaterLevel').touched
                "
              >
                Campo Obrigatório.
              </small>
            </div>

            <!-- Marcar para desconsiderar instrumentos avariados -->
            <div
              class="form-check me-1 ms-3 mt-2"
              *ngIf="showFieldsItems.ReferenceReadings"
            >
              <input
                class="form-check-input"
                type="checkbox"
                value=""
                formControlName="IgnoreDamagedInstruments"
              />
              <label class="form-label">
                Marcar para desconsiderar instrumentos avariados
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>

  <!-- Simulação específica -->
  <form [formGroup]="formSimulationSections">
    <div
      class="mt-3 row mb-3"
      formArrayName="SectionFormItems"
      *ngIf="
        formSimulation.get('SectionId').value.length > 0 &&
        showFields &&
        formSimulation.get('PhreaticPiezometric').value === 1
      "
    >
      <div
        *ngFor="let section of sectionFormItems.controls; let i = index"
        [formGroupName]="i"
        class="col-md-12"
      >
        <div class="simulation-section" *ngIf="!disabledSimulation">
          <div class="section-title">
            Simulação específica - {{ section.get('SectionName').value }}
          </div>
          <div *ngIf="getInstruments(section).controls.length > 0">
            <span>
              <i class="fa fa-exclamation-circle me-2"></i>
              Preencha abaixo as leituras da instrumentação correspondente à
              seção selecionada.
            </span>
            <div class="row mt-2">
              <label class="form-label">Tipo dos dados</label>
              <div class="col-md-2">
                <input
                  class="form-control"
                  type="text"
                  value="Cota (m)"
                  disabled
                  readonly
                />
              </div>
              <div class="col-md-4">
                <app-button
                  [class]="'btn-logisoil-blue'"
                  [icon]="'fa fa-arrow-down'"
                  [type]="false"
                  [label]="'Preencher com as últimas leituras'"
                  class="me-1"
                  (click)="fillLatestReadings(i)"
                ></app-button>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 list-instruments">
                <table class="table mt-2 table-borderless table-hover table-sm">
                  <thead>
                    <tr>
                      <th scope="col">Instrumento</th>
                      <th scope="col">Leitura</th>
                      <th style="text-align: center" scope="col">Seco?</th>
                    </tr>
                  </thead>
                  <tbody formArrayName="SectionFormItemsInstruments">
                    <tr
                      *ngFor="
                        let instrument of getInstruments(section).controls;
                        let j = index
                      "
                      [formGroupName]="j"
                    >
                      <td>
                        {{ instrument.get('instrument_identifier').value }}
                      </td>
                      <td>
                        <input
                          type="text"
                          class="form-control"
                          formControlName="quota"
                          placeholder="Cota (m)"
                          style="border-width: 1px; width: 100%"
                          (blur)="instrument.get('quota').markAsTouched()"
                          (focus)="instrument.get('quota').markAsUntouched()"
                        />
                        <small
                          class="form-text text-danger"
                          *ngIf="
                            instrument.get('quota').invalid &&
                            instrument.get('quota').touched &&
                            !instrument.get('dry').value
                          "
                        >
                          Campo Obrigatório.
                        </small>
                      </td>
                      <td style="text-align: center">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          formControlName="dry"
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Alerta -->
          <div
            class="alert alert-warning"
            role="alert"
            *ngIf="getInstruments(section).controls.length === 0"
          >
            {{ messageNotInstruments[section.get('SectionId').value] }}
          </div>
        </div>
      </div>
    </div>
  </form>

  <form [formGroup]="formSimulation">
    <div
      class="row mt-2"
      *ngIf="
        formSimulation.get('SectionId').value.length > 0 &&
        formSimulation.get('Conditions').value != '' &&
        formSimulation.get('PhreaticPiezometric').value != '' &&
        !disabledSimulation
      "
    >
      <div class="col-md-12 d-flex justify-content-end">
        <div class="me-2" style="width: 210px">
          <label class="form-label">Nome da simulação:</label>
          <input
            type="text"
            formControlName="Name"
            class="form-control"
            maxlength="80"
          />
        </div>
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-calculator'"
          [label]="'Simular'"
          [type]="false"
          (click)="validateForms()"
          style="margin-top: 30px"
        >
        </app-button>
      </div>
    </div>
  </form>

  <!-- Mensagens de alerta -->
  <div
    class="alert mt-3"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status"
    [innerHTML]="message.text"
  ></div>

  <div
    class="alert mt-3"
    [ngClass]="messageNoDxf.class"
    role="alert"
    *ngIf="messageNoDxf.status"
    [innerHTML]="messageNoDxf.text"
  ></div>

  <div
    class="alert mt-3"
    [ngClass]="messageNoInstrument.class"
    role="alert"
    *ngIf="messageNoInstrument.status"
    [innerHTML]="messageNoInstrument.text"
  ></div>

  <div class="row mt-2">
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <!-- Botões -->
  <div class="mt-3 mb-3 col-md-12 d-flex justify-content-end">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/stability/simulations']"
    ></app-button>
  </div>
</div>

<!-- Modal Método de busca Circular -->
<app-modal-search-method
  #modalSearchMethodCircular
  [title]="titleModal"
  [surfaceType]="'1'"
  [searchMethod]="formSimulation.get('SearchMethodCircular').value"
  [slide2Configuration]="dados?.structure?.slide2_configuration"
></app-modal-search-method>

<!-- Modal Método de busca Não Circular -->
<app-modal-search-method
  #modalSearchMethodNonCircular
  [title]="titleModal"
  [surfaceType]="'2'"
  [searchMethod]="formSimulation.get('SearchMethodNonCircular').value"
  [slide2Configuration]="dados?.structure?.slide2_configuration"
></app-modal-search-method>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
