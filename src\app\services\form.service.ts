import { Injectable } from '@angular/core';
import { FormGroup, Validators, FormArray, FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FormService {
  private formSubscriptions: Subscription[] = [];

  /**
   * Valida se todos os campos de um `FormGroup` estão válidos e não estão desabilitados.
   *
   * @param form O formulário a ser validado.
   * @param option (Não utilizado no corpo atual. Reservado para futuras opções de validação.)
   * @returns `true` se todos os campos válidos e habilitados forem válidos, senão `false`.
   */

  validateForm(form: FormGroup, option = 'notDisable') {
    const valid = Object.keys(form.controls).every((key) => {
      if (!form.controls[key].valid && !form.controls[key].disabled) {
        return false;
      } else {
        return true;
      }
    });
    return valid;
  }

  /**
   * Valida apenas os campos listados de um `FormGroup`, ignorando os que não fazem parte de `fieldList`.
   *
   * @param form O formulário a ser validado.
   * @param fieldList Lista de nomes de campos que devem ser validados.
   * @returns `true` se todos os campos listados e habilitados forem válidos, senão `false`.
   */

  validateFormList(form: FormGroup, fieldList: any = []) {
    const valid = Object.keys(form.controls).every((key) => {
      if (!form.controls[key].valid && !form.controls[key].disabled && fieldList.includes(key)) {
        return false;
      } else {
        return true;
      }
    });
    return valid;
  }

  /**
   * Ativa ou desativa a validação de um campo com base no valor de um checkbox relacionado (`is_{type}`).
   *
   * @param form O `FormGroup` contendo os campos.
   * @param type Nome base do campo (ex: 'tensile_strength' → depende de 'is_tensile_strength').
   */

  checkboxControlValidate(form: FormGroup, type: string = '') {
    if (form.controls['is_' + type].value) {
      form.controls[type].setValidators([Validators.required]);
      form.controls[type].updateValueAndValidity();
      form.controls[type].enable();
    } else {
      form.controls[type].setErrors(null);
      form.controls[type].clearValidators();
      form.controls[type].disable();
      form.controls[type].markAsUntouched();
    }
  }

  /**
   * Aplica validação condicional a um campo com base em uma dependência lógica entre campos.
   *
   * @param form O `FormGroup` de referência.
   * @param dependencyField Nome do campo que define a dependência.
   * @param field Nome do campo a ser validado condicionalmente.
   * @param condition Condição booleana para aplicar ou remover validação.
   */

  dependencyControlValidate(form: FormGroup, dependencyField: any, field: string, condition: boolean = true) {
    if (condition) {
      form.controls[field].setValidators([Validators.required]);
      form.controls[field].updateValueAndValidity();
      form.controls[field].enable();
    } else {
      form.controls[field].setErrors(null);
      form.controls[field].clearValidators();
      form.controls[field].disable();
      form.controls[field].markAllAsTouched();
    }
  }

  /**
   * Habilita ou desabilita uma lista de campos dentro de um `FormGroup`.
   *
   * @param form O `FormGroup` de origem.
   * @param fieldList Lista de nomes de campos a serem alterados.
   * @param action `true` para habilitar, `false` para desabilitar os campos.
   */

  toggleFormList(form: FormGroup, fieldList: any = [], action: boolean = true) {
    Object.keys(form.controls).map((key) => {
      if (fieldList.includes(key)) {
        if (action) {
          form.controls[key].enable();
        } else {
          form.controls[key].disable();
        }
      }
    });
  }

  /**
   * Aplica ou remove validação `Validators.required` a um ou mais campos.
   *
   * @param form O `FormGroup` de referência.
   * @param fields Campo único ou lista de campos a serem validados.
   * @param enable `true` para ativar validação e habilitar o campo, `false` para desabilitar e remover validação.
   */

  controlValidate(form: FormGroup, fields: any, enable = true) {
    if (typeof fields == 'string') {
      fields = [fields];
    }
    fields.forEach((field) => {
      if (enable) {
        form.controls[field].setValidators([Validators.required]);
        form.controls[field].updateValueAndValidity();
        form.controls[field].enable();
      } else {
        form.controls[field].setErrors(null);
        form.controls[field].clearValidators();
        form.controls[field].disable();
        form.controls[field].markAllAsTouched();
      }
    });
  }

  /**
   * Habilita ou desabilita todos os controles dentro de um `FormArray`.
   *
   * @param formArray O array de controles de formulário.
   * @param enable `true` para habilitar todos os controles, `false` para desabilitar.
   */

  toggleCheckbox(formArray: FormArray, enable: boolean) {
    formArray.controls.forEach((control) => {
      if (enable) {
        control.enable();
      } else {
        control.disable();
      }
    });
  }

  /**
   * Monitora todos os controles de um FormGroup, incluindo controles dentro de FormArrays.
   * @param {FormGroup} form - O FormGroup a ser monitorado.
   * @param {Function} onControlChange - Callback chamado quando um controle é alterado.
   */
  monitorAllFormControls(form: FormGroup, onControlChange?: (controlName: string, value: any) => void): void {
    Object.keys(form.controls).forEach((controlName) => {
      const control = form.get(controlName);

      if (control instanceof FormArray) {
        control.controls.forEach((group, index) => {
          if (group instanceof FormGroup) {
            this.monitorFormGroupControls(group, onControlChange, `${controlName}[${index}]`);
          }
        });
      } else if (control instanceof FormGroup) {
        this.monitorFormGroupControls(control, onControlChange, controlName);
      } else if (control instanceof FormControl) {
        this.subscribeToControl(control, controlName, onControlChange);
      }
    });
  }

  /**
   * Monitora os controles dentro de um grupo de formulário.
   * @param {FormGroup} group - O grupo de formulário a ser monitorado.
   * @param {Function} onControlChange - Callback chamado quando um controle é alterado.
   * @param {string} parentPath - Caminho do controle pai.
   */
  monitorFormGroupControls(group: FormGroup, onControlChange?: (controlName: string, value: any) => void, parentPath: string = ''): void {
    Object.keys(group.controls).forEach((controlName) => {
      const control = group.get(controlName);
      const fullPath = parentPath ? `${parentPath}.${controlName}` : controlName;

      if (control instanceof FormControl) {
        this.subscribeToControl(control, fullPath, onControlChange);
      }
    });
  }

  /**
   * Inscreve-se nas mudanças de um controle específico e executa um callback.
   * @param {FormControl} control - O controle a ser monitorado.
   * @param {string} controlName - O nome do controle (ou caminho completo).
   * @param {Function} onControlChange - Callback chamado quando o controle é alterado.
   */
  subscribeToControl(control: FormControl, controlName: string, onControlChange?: (controlName: string, value: any) => void): void {
    const subscription = control.valueChanges.subscribe((value) => {
      if (onControlChange) {
        onControlChange(controlName, value); // Chama o callback no componente
      }
    });

    this.formSubscriptions.push(subscription);
  }

  /**
   * Cancela todas as assinaturas associadas ao formulário.
   */
  formDestroySubscribe(): void {
    this.formSubscriptions.forEach((sub) => sub.unsubscribe());
    this.formSubscriptions = [];
  }
}
