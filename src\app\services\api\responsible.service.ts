import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class ResponsibleService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  // Cadastro de Responsáveis
  postResponsible(params: any) {
    const url = '/responsibles';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna os responsáveis para uso em filtro
  getResponsible(params: any) {
    const url = '/responsibles/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getResponsibleList(params: any = {}) {
    const url = '/responsibles';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca o responsável por ID
  getResponsibleById(id: string) {
    const url = `/responsibles/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putResponsible(id: string, params: any) {
    const url = `/responsibles/${id}`;
    return this.api.put<any>(url, params, 'client');
  }
}
