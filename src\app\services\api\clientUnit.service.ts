import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class ClientUnitService {
  constructor(private api: ApiService) {}

  // Cadastro de unidades
  postClientUnits(params: any) {
    const url = '/client-units';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna a lista de unidades para uso em filtro
  getClientUnits(params: any) {
    const url = '/client-units/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca a unidade por ID
  getClientUnitsById(id: string) {
    const url = `/client-units/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  getClientUnitsId(params: any = {}) {
    const url = `/client-units`;
    return this.api.get<any>(url, params, false, 'client');
  }

  // Atualizará todos os dados da unidade
  putClientUnits(id: string, params: any) {
    const url = `/client-units/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  // Exclui o registro da unidade no banco de dados
  deleteClientUnits(id: string) {
    const url = `/client-units/${id}`;
    return this.api.delete<any>(url, 'client');
  }

  patchClientUnits(id: string, params: any) {
    const url = `/client-units/${id}`;
    return this.api.patch<any>(url, params, 'client');
  }
}
