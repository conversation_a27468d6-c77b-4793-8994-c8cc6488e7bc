const ChartLine = [
  { id: 'solid', value: 'Cont<PERSON><PERSON>' },
  { id: 'dotted', value: 'Pont<PERSON><PERSON><PERSON>' },
  { id: 'dashed', value: '<PERSON>ja<PERSON>' }
];

const XAxisRotate = [
  { value: 0, label: 'Horizontal' },
  { value: 90, label: 'Vertical' },
  { value: 30, label: '30 graus' },
  { value: 45, label: '45 graus' },
  { value: 60, label: '60 graus' }
];

const YAxisLabel = [
  { id: 0, name: 'instrument', label: 'Cota (m)', position: 'left', offset: 20, nameRotate: 90, nameLocation: 'center', nameGap: 45, show: true },
  { id: 1, name: 'reservoir', label: 'NA Reservatório (m)', position: 'left', offset: 90, nameRotate: 90, nameLocation: 'center', nameGap: 45, show: false },
  { id: 2, name: 'rainfall', label: 'Pluviometria (mm)', position: 'right', offset: 20, nameRotate: 270, nameLocation: 'center', nameGap: 45, show: false }
];

const Markers = [
  { marker: 'circle', label: '<PERSON><PERSON><PERSON><PERSON>', icon: 'circle.svg', text: '', img: '/assets/markers/circle.png' },
  { marker: 'cross', label: 'Cruz', icon: 'cross.svg', text: '', img: '/assets/markers/cross.png' },
  { marker: 'x', label: 'X', icon: 'x.svg', text: '', img: '/assets/markers/x.png' },
  { marker: 'triangle', label: 'Triângulo', icon: 'triangle.svg', text: '', img: '/assets/markers/triangle.png' },
  { marker: 'rectangle', label: 'Retângulo', icon: 'rectangle.svg', text: '', img: '/assets/markers/rectangle.png' }
];

const Periods = [
  { value: 1, label: '1 mês' },
  { value: 3, label: '3 meses' },
  { value: 6, label: '6 meses' },
  { value: 12, label: '1 ano' },
  { value: 24, label: '2 anos' },
  { value: 60, label: '5 anos' },
  { value: 0, label: 'Tudo' }
];

const PeriodsInaPz = [
  { value: 1, label: '1 mês' },
  { value: 2, label: '3 meses' },
  { value: 3, label: '6 meses' },
  { value: 4, label: '1 ano' },
  { value: 5, label: '2 anos' },
  { value: 6, label: '5 anos' },
  { value: 7, label: 'Tudo' }
];

const PeriodsPluv = [
  { value: 1, label: 'Diário', show: true },
  { value: 2, label: 'Semanal', show: true },
  { value: 3, label: 'Mensal', show: true },
  { value: 4, label: 'Anual', show: true }
];

const Aggregation = [
  { value: 1, label: 'Máximo', show: true },
  { value: 2, label: 'Acumulado', show: true }
];

//Medidor de Recalque
const InfoYAxis = [
  { id: 1, name: 'Recalque Absoluto  (mm)' },
  { id: 2, name: 'Recalque Relativo  (mm)' },
  { id: 3, name: 'Cota (m)' }
];

//Marco superficial e Prisma
const YAxisDisplacement = [
  { value: 1, label: 'Deslocamento E (mm)' },
  { value: 2, label: 'Deslocamento N (mm)' },
  { value: 3, label: 'Deslocamento A (mm)' },
  { value: 4, label: 'Deslocamento B (mm)' },
  { value: 5, label: 'Deslocamento Z (mm)' },
  { value: 6, label: 'Deslocamento Planimétrico Total (mm)' }
];

//Variação absoluta
const navBar = {
  1: {
    label: '1 mês',
    month: 1
  },
  2: {
    label: '3 meses',
    month: 2
  },
  3: {
    label: '6 meses',
    month: 3
  },
  4: {
    label: '1 ano',
    month: 4
  },
  5: {
    label: '2 anos',
    month: 5
  },
  6: {
    label: '5 anos',
    month: 6
  },
  7: {
    label: 'Tudo',
    month: 7
  }
};

export { ChartLine, XAxisRotate, YAxisLabel, Markers, Periods, PeriodsInaPz, PeriodsPluv, Aggregation, InfoYAxis, YAxisDisplacement, navBar };
