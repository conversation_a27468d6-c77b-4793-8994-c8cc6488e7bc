import { Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DomSanitizer } from '@angular/platform-browser';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-modal-view-dxf',
  templateUrl: './modal-view-dxf.component.html',
  styleUrls: ['./modal-view-dxf.component.scss']
})
export class ModalViewDxfComponent implements OnInit, OnChanges {
  @ViewChild('modalViewDxf') ModalViewDxf: ElementRef;
  @Input() public title: string = '';
  @Input() public dxfInfo: any = null;

  public fileDxf: any = null;
  public fileContent: string = '';
  public fileName: string = '';
  public fileContentDownload: any = '';

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Estrutura',
      width: '180px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['structure_name']
    },
    {
      label: 'Seção',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['section_name']
    },
    {
      label: 'Condição',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['soil_condition_type']
    },
    {
      label: 'Tipo de Superfície',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['surface_type']
    },
    {
      label: 'Método de Cálculo',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['calculation_method']
    },
    {
      label: 'Fator de Segurança',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['value']
    }
  ];

  constructor(private modalService: NgbModal, private sanitizer: DomSanitizer) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.dxfInfo && changes.dxfInfo.currentValue != null) {
      this.displaySummaryWithDxf(changes.dxfInfo.currentValue);
    }
  }

  openModal() {
    this.modalService.open(this.ModalViewDxf, { fullscreen: true });
  }

  displaySummaryWithDxf($data) {
    this.tableData = [];
    const { dxf, ...info } = $data;

    if (Object.keys(info).length !== 0) {
      this.tableData.push(info);
    }

    this.loadDrawing(dxf);
  }

  loadDrawing(drawing) {
    if (drawing != null) {
      this.fileContent = drawing.base64;
      this.fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + this.fileContent);
      this.fileName = drawing.name;
      this.fileDxf = fn.base64ToFile(this.fileContent, this.fileName);
    }
  }
}
