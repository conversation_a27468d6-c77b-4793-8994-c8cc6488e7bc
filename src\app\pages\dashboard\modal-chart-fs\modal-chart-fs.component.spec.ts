import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalChartFsComponent } from './modal-chart-fs.component';

describe('ModalChartFsComponent', () => {
  let component: ModalChartFsComponent;
  let fixture: ComponentFixture<ModalChartFsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalChartFsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalChartFsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
