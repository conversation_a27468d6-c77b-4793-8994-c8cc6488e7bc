import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { MessagePadroes } from 'src/app/constants/message.constants';
import * as moment from 'moment';

@Component({
  selector: 'app-modal-history-inspection-sheet',
  templateUrl: './modal-history-inspection-sheet.component.html',
  styleUrls: ['./modal-history-inspection-sheet.component.scss']
})
export class ModalHistoryInspectionSheetComponent implements OnInit, OnChanges {
  @ViewChild('modalHistoryInspectionSheet') modalHistoryInspectionSheet: ElementRef;
  @Input() public data: any = {};
  @Output() public sendClickEvent = new EventEmitter();

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'ID',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['id']
    },
    {
      label: 'Data',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Usuário',
      width: '20%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['modified_by']
    },
    {
      label: 'Detalhes',
      width: '80%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    }
  ];

  constructor(private modalService: NgbModal, private inspectionSheetServiceApi: InspectionSheetServiceApi, private ngxSpinnerService: NgxSpinnerService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes?.data && changes?.data?.currentValue && changes?.data?.currentValue.id) {
      this.getInspectionSheetHistory();
    }
  }

  openModal() {
    this.modalService.open(this.modalHistoryInspectionSheet, { size: 'xl' });
  }

  getInspectionSheetHistory() {
    this.ngxSpinnerService.show();

    this.message.text = '';
    this.message.status = false;

    const inspectionSheetId = this.data.id;

    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    this.inspectionSheetServiceApi.getInspectionSheetHistory(inspectionSheetId, params).subscribe(
      (resp) => {
        const dados: any = resp;
        if (dados.status == 200) {
          // Formata os dados antes de atribuí-los à tabela
          this.tableData = dados.body.data ? this.formatData(dados.body.data) : [];
          this.collectionSize = dados.body.total_items_count;
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.message.text = MessagePadroes.NoRegister;
          this.message.status = true;
          this.message.class = 'alert-success';

          setTimeout(() => {
            this.message.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide(); // Garantir ocultação aqui
      },
      (error) => {
        console.error(error);
        this.ngxSpinnerService.hide(); // Garantir ocultação no caso de erro
      }
    );
  }

  formatData(data: any[]) {
    return data.map((item) => ({
      id: item.id,
      created_date: moment(item.created_dat).format('DD/MM/YYYY HH:mm:ss'),
      modified_by: `${item.modified_by.first_name} ${item.modified_by.surname}`,
      changes: item.changes
    }));
  }

  clickRowEvent(action: any = null) {
    this.sendClickEvent.emit(action);
  }

  loadPage(selectPage: number) {
    this.page = selectPage;
    this.getInspectionSheetHistory();
  }
}
