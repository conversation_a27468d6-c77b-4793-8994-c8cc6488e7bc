<ng-template #modalDownloadListInstrument let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title" id="modal-profile-title">
      <i class="fa fa-info-circle"></i>
      Instruções para baixar a planilha:
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click'); resetForm()"
    ></button>
  </div>
  <form
    [formGroup]="formDownloadInformation"
    (ngSubmit)="clickRowEvent('sendDownloadInformations')"
  >
    <div class="modal-body">
      <p>
        <i class="fa fa-arrow-right me-1" aria-hidden="true"></i> Selecione o
        formato desejado do arquivo: <strong>.xlsx</strong> ou
        <strong> .csv</strong>.
      </p>
      <p>
        <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
        Para cada tipo de instrumento selecionado, um
        <strong> arquivo</strong> será criado.
      </p>
      <p>
        <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
        Após selecionar o(s) tipo(s) de instrumento(s), clique no botão
        <strong> Baixar</strong>.
      </p>
      <div class="row">
        <div class="col-md-12">
          <label class="form-label">Formato do arquivo:</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="singleSettingsModal"
            [data]="fileFormats"
            formControlName="file_format"
          >
          </ng-multiselect-dropdown>
          <small
            class="form-text text-danger"
            *ngIf="
              !formDownloadInformation.get('file_format').valid &&
              formDownloadInformation.get('file_format').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <div class="col-md-12 mt-2">
          <label class="form-label">Tipo(s) de instrumento(s):</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="multipleSettingsModal"
            [data]="typeInstruments"
            formControlName="type_instrument"
            (onSelect)="messagesError = []; labelAlert = ''"
            (onSelectAll)="messagesError = []; labelAlert = ''"
            (onDeSelect)="messagesError = []; labelAlert = ''"
            (onDeSelectAll)="messagesError = []; labelAlert = ''"
          >
          </ng-multiselect-dropdown>
          <small
            class="form-text text-danger"
            *ngIf="
              !formDownloadInformation.get('type_instrument').valid &&
              formDownloadInformation.get('type_instrument').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
      </div>

      <app-alert
        [class]="'alert-warning mt-3'"
        [messages]="messagesError"
        [label]="labelAlert"
      ></app-alert>
    </div>

    <!-- Botões -->
    <div class="modal-footer">
      <app-button
        [class]="'btn-logisoil-green'"
        [label]="'Baixar'"
        [icon]="'fa fa-download'"
        [type]="false"
        [disabled]="!formDownloadInformation.valid"
      >
      </app-button>
      <app-button
        [class]="'btn-logisoil-red'"
        [label]="'Cancelar'"
        (click)="c('Close click'); resetForm()"
      >
      </app-button>
    </div>
  </form>
</ng-template>
