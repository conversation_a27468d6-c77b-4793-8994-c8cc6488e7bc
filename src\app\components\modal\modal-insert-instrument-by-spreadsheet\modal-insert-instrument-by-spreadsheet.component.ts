import { Component, ElementRef, EventEmitter, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { Datum, MultiSelectDefault } from 'src/app/constants/app.constants';
import { FileFormat, typeInstruments } from 'src/app/constants/instruments.constants';

import { ClientService } from 'src/app/services/api/client.service';
import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { StructuresService } from 'src/app/services/api/structure.service';

import { UserService } from 'src/app/services/user.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-modal-insert-instrument-by-spreadsheet',
  templateUrl: './modal-insert-instrument-by-spreadsheet.component.html',
  styleUrls: ['./modal-insert-instrument-by-spreadsheet.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalInsertInstrumentBySpreadsheetComponent implements OnInit {
  @ViewChild('modalInsertInstrumentBySpreadsheet') ModalInsertInstrumentBySpreadsheet: ElementRef;
  @Output() public sendClickEvent = new EventEmitter();

  public formInsertInstrumentBySpreadsheet: FormGroup = new FormGroup({
    client: new FormControl([], [Validators.required]),
    client_unit: new FormControl([], [Validators.required]),
    structure: new FormControl([], [Validators.required]),
    datum: new FormControl([], [Validators.required]),
    file_format: new FormControl([], [Validators.required]),
    type_instrument: new FormControl([], [Validators.required]),
    rows: new FormControl(''),
    includeInactive: new FormControl(false) // Adiciona controle para o checkbox
  });

  public clientSettings = MultiSelectDefault.Single;
  public structureSettings = MultiSelectDefault.Single;
  public unitSettings = MultiSelectDefault.Single;

  public clients: any = [];
  public structures: any = [];
  public units: any = [];

  public datum: any = Datum;

  public singleSettingsModal = null;
  public typeInstrumentSettingsModal = null;
  public datumSettingsModal = null;

  public fileFormats = [];
  public typeInstruments = [];

  public messagesError = [];
  public labelAlert = '';

  public profile: any = null;
  public permissaoUsuario: any = null;

  public func = fn;

  constructor(
    private clientService: ClientService,
    private clientUnitService: ClientUnitService,
    private modalService: NgbModal,
    private structuresService: StructuresService,
    private userService: UserService
  ) {}

  /**
   * Método executado ao inicializar o componente.
   * Configura o perfil do usuário, permissões e inicializa as configurações de dropdown e outros dados do formulário.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;

    this.singleSettingsModal = {
      singleSelection: true,
      idField: 'value',
      textField: 'label',
      selectAllText: 'Selecionar',
      unSelectAllText: 'Desmarcar seleção',
      searchPlaceholderText: 'Pesquisar...',
      itemsShowLimit: 5,
      allowSearchFilter: true,
      enableCheckAll: false,
      closeDropDownOnSelection: true,
      noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
      noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
    };

    this.datumSettingsModal = {
      singleSelection: true,
      idField: 'id',
      textField: 'value',
      selectAllText: 'Selecionar',
      unSelectAllText: 'Desmarcar seleção',
      searchPlaceholderText: 'Pesquisar...',
      itemsShowLimit: 5,
      allowSearchFilter: true,
      enableCheckAll: false,
      closeDropDownOnSelection: true,
      noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
      noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
    };

    this.typeInstrumentSettingsModal = {
      singleSelection: true,
      idField: 'id',
      textField: 'name',
      selectAllText: 'Selecionar',
      unSelectAllText: 'Desmarcar seleção',
      searchPlaceholderText: 'Pesquisar...',
      itemsShowLimit: 5,
      allowSearchFilter: true,
      enableCheckAll: true,
      closeDropDownOnSelection: true,
      noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
      noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
    };

    this.fileFormats = FileFormat;
    this.typeInstruments = typeInstruments;

    this.getClients();

    this.loadFilter(this.formInsertInstrumentBySpreadsheet, 'client', 'client_unit', 'structure', false);
  }

  /**
   * Busca e carrega a lista de clientes ativos, resetando os filtros atuais de unidade e estrutura.
   * Se houver apenas um cliente, seleciona-o automaticamente.
   */
  getClients() {
    this.clients = [];
    this.units = [];
    this.structures = [];

    this.formInsertInstrumentBySpreadsheet.get('client').setValue('');
    this.formInsertInstrumentBySpreadsheet.get('client_unit').setValue('');
    this.formInsertInstrumentBySpreadsheet.get('structure').setValue('');

    this.clientService.getClientsList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;

      if (this.clients.length == 1) {
        this.formInsertInstrumentBySpreadsheet.get('client').setValue(this.clients);
        this.getUnits(this.clients[0]);
      }
    });
  }

  /**
   * Carrega a lista de unidades de um cliente com base no ID do cliente e atualiza os filtros de unidade e estrutura.
   * Se houver apenas uma unidade, seleciona-a automaticamente.
   * @param {any} client - O cliente selecionado.
   * @param {string} [action='select'] - A ação a ser executada (selecionar ou desmarcar).
   */
  getUnits(client, action: string = 'select') {
    this.units = [];
    this.structures = [];

    this.formInsertInstrumentBySpreadsheet.get('client_unit').setValue('');
    this.formInsertInstrumentBySpreadsheet.get('structure').setValue('');

    if (action === 'select') {
      this.clientUnitService.getClientUnitsId({ clientId: client.id, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.units = dados;

        if (this.units.length == 1) {
          this.formInsertInstrumentBySpreadsheet.get('client_unit').setValue(this.units);
          this.getStructures(this.units[0]);
        }
      });
    }
  }

  /**
   * Carrega a lista de estruturas de uma unidade de cliente com base no ID da unidade e atualiza o filtro de estrutura.
   * Se houver apenas uma estrutura, seleciona-a automaticamente.
   * @param {any} clientUnit - A unidade do cliente selecionada.
   * @param {string} [action='select'] - A ação a ser executada (selecionar ou desmarcar).
   */
  getStructures(clientUnit, action: string = 'select') {
    this.structures = [];

    this.formInsertInstrumentBySpreadsheet.get('structure').setValue('');

    if (action === 'select') {
      this.structuresService.getStructureList({ clientUnitId: clientUnit.id, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.structures = dados;

        if (this.structures.length == 1) {
          this.formInsertInstrumentBySpreadsheet.get('structure').setValue(this.structures);
        }
      });
    }
  }

  //Abre o modal para inserção de instrumentos por planilha.
  openModal() {
    this.modalService.open(this.ModalInsertInstrumentBySpreadsheet, { size: 'xl' });
  }

  //Fecha todos os modais abertos.
  closeModal() {
    this.modalService.dismissAll();
  }

  /**
   * Emite o evento de clique na linha da tabela, enviando a ação realizada.
   * @param {any} action - A ação realizada no clique da linha.
   */
  clickRowEvent(action: any = null) {
    this.sendClickEvent.emit(action);
  }

  /**
   * Carrega os filtros salvos no `localStorage` e preenche os campos do formulário com base nos dados disponíveis.
   * Verifica se há valores salvos para ClientId, ClientUnitId, e StructureId, e preenche os campos correspondentes no formulário.
   * Se os valores existirem, também busca as unidades e estruturas associadas.
   *
   * @param {FormGroup} $form - O formulário que será preenchido com os filtros.
   * @param {string} client - O nome do campo de cliente no formulário.
   * @param {string} unit - O nome do campo de unidade no formulário.
   * @param {string} structure - O nome do campo de estrutura no formulário.
   */
  loadFilter($form, client, unit, structure, onlyId = false) {
    const savedFilters = localStorage.getItem('filterHierarchy');
    if (savedFilters) {
      const filterHierarchy = JSON.parse(savedFilters);

      // Verificar se existe ClientId, ClientUnitId, e StructureId e preenchê-los
      if (filterHierarchy.ClientId) {
        const configClient = {
          value: onlyId ? filterHierarchy.ClientId.id : [{ id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }],
          param: onlyId ? filterHierarchy.ClientId.id : { id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }
        };
        $form.get(client)?.setValue(configClient.value);
        this.getUnits(configClient.param);
      }

      if (filterHierarchy.ClientUnitId) {
        const configClientUnit = {
          value: onlyId ? filterHierarchy.ClientUnitId.id : [{ id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }],
          param: onlyId ? filterHierarchy.ClientUnitId.id : { id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }
        };
        $form.get(unit)?.setValue(configClientUnit.value);
        this.getStructures(configClientUnit.param);
      }

      if (filterHierarchy.StructureId) {
        const configStructure = {
          value: onlyId ? filterHierarchy.StructureId.id : [{ id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }],
          param: onlyId ? filterHierarchy.StructureId.id : { id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }
        };
        $form.get(structure)?.setValue(configStructure.value);
      }
    }
  }
}
