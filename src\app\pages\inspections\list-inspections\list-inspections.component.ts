import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { UserService } from 'src/app/services/user.service';
import { InspectionSheetStatus, InspectionSheetType } from 'src/app/constants/inspections.constants';

@Component({
  selector: 'app-list-inspections',
  templateUrl: './list-inspections.component.html',
  styleUrls: ['./list-inspections.component.scss']
})
export class ListInspectionsComponent implements OnInit {
  public activeTab = 'fichasDeInspecao';

  public inspectionSheetStatus = InspectionSheetStatus;
  public inspectionSheetType = InspectionSheetType;

  public selectedStatus: string | number = 'all';
  public selectedOrigin: string | number = 'all';

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private userService: UserService) {}

  /**
   * Método de inicialização que é chamado após a construção do componente.
   * Inicializa as configurações e dados necessários para o funcionamento do componente.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    if (this.activatedRoute.snapshot?.queryParams?.tab) {
      this.activeTab = this.activatedRoute.snapshot?.queryParams?.tab;
    }
  }

  changeTab(tab: string): void {
    this.activeTab = tab;
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  //Navega para a página inicial
  goBack() {
    this.router.navigate(['/']);
  }
}
