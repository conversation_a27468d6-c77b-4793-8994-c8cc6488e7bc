<div class="col-md-12">
  <form [formGroup]="formMeasure">
    <div class="card" [ngClass]="!formValid ? '' : 'not-valid'">
      <div class="card-header form-control-bg">
        {{ nameMeasure }}
      </div>
      <div class="card-body">
        <app-button
          [class]="
            'btn-logisoil-remove-item-outside color-white position-absolute top-0 end-0'
          "
          [icon]="'fa fa-trash'"
          (click)="removeMe()"
          *ngIf="index > 0 && !view && !edit"
        >
        </app-button>
        <div *ngIf="edit || view">
          <ng-template #tipContentStatus>
            <span
              *ngIf="formMeasure.controls['active'].value"
              class="form-switch-label"
              >Ativo</span
            >
            <span
              *ngIf="!formMeasure.controls['active'].value"
              class="form-switch-label"
              >Inativo</span
            >
          </ng-template>
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              role="switch"
              formControlName="active"
              [ngbTooltip]="tipContentStatus"
              [disabled]="formMeasure.controls['active'].disabled"
              (click)="activeMe(formMeasure.controls['active'].value)"
            />
          </div>
        </div>
        <div class="row">
          <!-- Identifier -->
          <div class="col-md-3">
            <label class="form-label">Identificador</label>
            <input
              type="text"
              class="form-control"
              formControlName="identifier"
              autocomplete="off"
              maxlength="32"
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['identifier'] != null"
              >{{ _current['identifier'] }}<br
            /></small>
            <small
              class="form-text text-danger"
              *ngIf="
                !formMeasure.get('identifier').valid &&
                formMeasure.get('identifier').touched
              "
              >Campo Obrigatório.</small
            >
          </div>
          <!-- Nome alternativo (Opcional) -->
          <div class="col-md-3">
            <label class="form-label">Nome alternativo</label>
            <input
              type="text"
              class="form-control"
              formControlName="alternative_name"
              autocomplete="off"
              maxlength="32"
              placeholder="Opcional"
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['alternative_name'] != null"
              >{{ _current['alternative_name'] }}<br
            /></small>
          </div>
          <!-- Cota -->
          <div class="col-md-3">
            <label class="form-label">{{
              typeMeasure === 'pressure_cell'
                ? 'Cota célula de pressão (m)'
                : typeMeasure === 'measure_point'
                ? 'Profundidade (m)'
                : 'Cota (m)'
            }}</label>
            <input
              type="text"
              class="form-control"
              formControlName="quota"
              autocomplete="off"
              min="-9999999999999"
              max="9999999999999"
              maxlength="9999999999999"
              (keypress)="func.controlNumber($event, null, 'notE')"
              (keyup)="func.controlNumber($event, formMeasure.get('quota'))"
              (blur)="
                func.formatType($event);
                calcQuota(formMeasure.controls['quota'].value)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['quota'] != null"
              >{{ _current['quota'] }}<br
            /></small>
            <small
              class="form-text text-danger"
              *ngIf="
                !formMeasure.get('quota').valid &&
                formMeasure.get('quota').touched
              "
              >Campo Obrigatório.</small
            >
          </div>
          <!-- Cota do ponto do inclinômetro (campo informativo) -->
          <div class="col-md-3" *ngIf="typeMeasure === 'measure_point'">
            <label class="form-label">Cota do ponto do inclinômetro (m)</label>
            <input
              type="text"
              class="form-control"
              formControlName="elevation"
              [disabled]="true"
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['elevation'] != null"
              >{{ _current['elevation'] }}<br
            /></small>
          </div>
          <!-- Comprimento (campo informativo) -->
          <div class="col-md-3 mt-1" *ngIf="typeMeasure === 'measure_point'">
            <label class="form-label">Comprimento (m)</label>
            <input
              type="text"
              class="form-control"
              formControlName="length"
              [disabled]="true"
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['length'] != null"
              >{{ _current['length'] }}<br
            /></small>
          </div>
          <!-- Anéis Magnéticos e Inclinometros -->
          <div
            class="col-md-3 mt-1"
            *ngIf="
              typeMeasure === 'magnetic_ring' || typeMeasure === 'measure_point'
            "
          >
            <label class="form-label">Litotipo</label>
            <input
              type="text"
              class="form-control"
              formControlName="lithotype"
              autocomplete="off"
              maxlength="32"
              placeholder="Opcional"
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['lithotype'] != null"
              >{{ _current['lithotype'] }}<br
            /></small>
          </div>
          <!--  Anéis Magnéticos -->
          <div class="col-md-3 mt-1" *ngIf="typeMeasure === 'magnetic_ring'">
            <label class="form-label">Limite (cm)</label>
            <input
              type="text"
              class="form-control"
              formControlName="limit"
              autocomplete="off"
              min="-9999999999999"
              max="9999999999999"
              maxlength="9999999999999"
              (keypress)="func.controlNumber($event, null, 'notE')"
              (keyup)="func.controlNumber($event, formMeasure.get('limit'))"
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['limit'] != null"
              >{{ _current['limit'] }}<br
            /></small>
            <small
              class="form-text text-danger"
              *ngIf="
                !formMeasure.get('limit').valid &&
                formMeasure.get('limit').touched
              "
              >Campo Obrigatório.</small
            >
          </div>
          <!--  Anéis Magnéticos -->
          <div class="col-md-3 mt-1" *ngIf="typeMeasure === 'magnetic_ring'">
            <label class="form-label">Δ ref. (m)</label>
            <input
              type="text"
              class="form-control"
              formControlName="delta_ref"
              autocomplete="off"
              min="-9999999999999"
              max="9999999999999"
              maxlength="9999999999999"
              (keypress)="func.controlNumber($event, null, 'notE')"
              (keyup)="func.controlNumber($event, formMeasure.get('delta_ref'))"
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="is_referencial"
                value=""
                formControlName="is_referencial"
                (change)="setIsReferencial()"
              />
              <label class="form-label" for="is_referencial">
                Marcar como referencial
              </label>
            </div>
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['delta_ref'] != null"
              >{{ _current['delta_ref'] }}<br
            /></small>
          </div>
        </div>
        <!-- Níveis de segurança -->
        <div class="row mt-3">
          <app-security-levels
            [data]="datasecurityLevels"
            [edit]="edit"
            [view]="view"
            [disabled]="!formMeasure.controls['active'].value || view"
          ></app-security-levels>
        </div>
      </div>
    </div>
  </form>
</div>
