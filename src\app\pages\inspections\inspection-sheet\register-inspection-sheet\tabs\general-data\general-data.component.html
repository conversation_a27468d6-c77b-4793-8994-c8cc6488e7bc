<!--
inspectionSheetType: 1 -  RISR
inspectionSheetType: 2 -  EoR
inspectionSheetType: 3 -  FIE
inspectionSheetType: 4 -  FIR
-->

<div class="row mt-2">
  <form [formGroup]="generalDataForm">
    <div
      class="row mb-3"
      *ngIf="
        inspectionSheetType === 1 ||
        inspectionSheetType === 2 ||
        inspectionSheetType === 3 ||
        inspectionSheetType === 4
      "
    >
      <div class="col-md-4">
        <label class="form-label">Empreendedor:</label>
        <input
          id="entrepreneur"
          formControlName="Client"
          type="text"
          class="form-control"
          (blur)="onBlur('Client')"
        />
      </div>

      <div class="col-md-4">
        <label class="form-label">Nome da barragem:</label>
        <input
          formControlName="ClientUnit"
          type="text"
          class="form-control"
          (blur)="onBlur('ClientUnit')"
        />
      </div>

      <div class="col-md-4">
        <label class="form-label">Coordenadas:</label>
        <input
          formControlName="StructureCoordinateSetting"
          type="text"
          class="form-control"
          (blur)="onBlur('StructureCoordinateSetting')"
        />
      </div>
    </div>

    <div class="row mb-3">
      <!-- Data de Cadastro da Ficha (sempre bloqueada) -->
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="form-label">Data de cadastro da ficha:</label>
        <input
          formControlName="CreatedDate"
          type="datetime-local"
          class="form-control"
          readonly
          (blur)="onBlur('CreatedDate')"
        />
      </div>

      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="form-label">Cadastrado por:</label>
        <input
          formControlName="CreatedBy"
          type="text"
          class="form-control"
          (blur)="onBlur('CreatedBy')"
        />
      </div>

      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="form-label">Estrutura:</label>
        <input
          formControlName="Structure"
          type="text"
          class="form-control"
          (blur)="onBlur('Structure')"
        />
      </div>
    </div>

    <div class="row">
      <!-- FIE - Data da Vistoria | EOR, FIR, RISR - Início da inspeção  -->
      <div class="col-md-4">
        <label class="form-label">{{ startDateForm.label }}:</label>
        <input
          formControlName="StartDate"
          type="datetime-local"
          class="form-control"
          (change)="onChange('StartDate')"
        />
      </div>
      <!-- EOR, FIR, RISR - Fim da Inspeção (exceto FIE) -->
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 4
        "
      >
        <label class="form-label">Fim da inspeção:</label>
        <input
          formControlName="EndDate"
          type="datetime-local"
          class="form-control"
          (change)="onChange('EndDate')"
        />
      </div>

      <!-- FIE, FIR, RISR - (exceto EOR) -->
      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="form-label">Executado por:</label>
        <input
          formControlName="ExecutedBy"
          type="text"
          class="form-control"
          (blur)="onBlur('ExecutedBy')"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !generalDataForm.get('ExecutedBy').valid &&
            generalDataForm.get('ExecutedBy').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div
        class="col-md-4"
        *ngIf="
          inspectionSheetType === 1 ||
          inspectionSheetType === 2 ||
          inspectionSheetType === 3 ||
          inspectionSheetType === 4
        "
      >
        <label class="form-label">Status da ficha:</label>
        <input
          formControlName="Status"
          type="text"
          class="form-control"
          readonly
          (blur)="onBlur('Status')"
        />
      </div>
    </div>
    <small
      class="form-text text-danger"
      *ngIf="!generalDataForm.get('StartDate').valid && status === 1 && !locked"
      >{{ startDateForm.small }}</small
    >
    <!-- FIE -->
    <h6 class="mt-4" *ngIf="inspectionSheetType === 3">
      Identificação do avaliador
    </h6>
    <hr *ngIf="inspectionSheetType === 3" />
    <!-- FIE -->
    <div class="row mb-3" *ngIf="inspectionSheetType === 3">
      <div class="col-md-6">
        <label class="form-label">Nome:</label>
        <input
          formControlName="EvaluatorName"
          type="text"
          class="form-control"
          (blur)="onBlur('EvaluatorName')"
        />
      </div>
      <div class="col-md-6">
        <label class="form-label">Cargo:</label>
        <input
          formControlName="EvaluatorPosition"
          type="text"
          class="form-control"
          (blur)="onBlur('EvaluatorPosition')"
        />
      </div>
    </div>
    <!-- FIE -->
    <div class="row mb-3" *ngIf="inspectionSheetType === 3">
      <div class="col-md-3">
        <label class="form-label">CREA nº:</label>
        <input
          formControlName="EvaluatorCrea"
          type="text"
          class="form-control"
          (blur)="onBlur('EvaluatorCrea')"
        />
      </div>
      <div class="col-md-3">
        <label class="form-label">ART nº:</label>
        <input
          formControlName="EvaluatorArt"
          type="text"
          class="form-control"
          (blur)="onBlur('EvaluatorArt')"
        />
      </div>
    </div>

    <div
      class="row"
      *ngIf="inspectionSheetType === 2"
      formArrayName="Responsibles"
    >
      <div
        *ngFor="let responsible of Responsibles.controls; let i = index"
        [formGroupName]="i"
        class="row mb-3"
      >
        <!-- Responsáveis -->
        <div class="col-md-6">
          <label class="form-label">Responsáveis:</label>
          <input
            formControlName="Responsible"
            type="text"
            class="form-control"
            placeholder="Digite o nome do responsável"
            (blur)="onBlur('Responsibles', i)"
          />
        </div>

        <!-- Externos /Internos -->
        <div
          class="col-md-6 d-flex align-items-start"
          style="padding-top: 36px"
        >
          <label class="form-label me-3 mb-0">Empresa?</label>
          <div class="form-check form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              formControlName="IsInternalResponsible"
              [value]="true"
              (change)="onChange('Responsibles', i)"
            />
            <label class="form-check-label">Walm</label>
          </div>
          <div class="form-check form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              formControlName="IsInternalResponsible"
              [value]="false"
              (change)="onChange('Responsibles', i)"
            />
            <label class="form-check-label">{{
              externalResponsibleLabel
            }}</label>
          </div>
          <app-button
            class="btn-sm btn-logisoil-red ms-2"
            icon="fa fa-trash"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            title="Excluir responsável"
            (click)="removeResponsible(i)"
            *ngIf="Responsibles.controls.length > 1"
          ></app-button>
        </div>
      </div>

      <!-- Botão Adicionar responsável -->
      <div class="text-start mt-2">
        <app-button
          class="btn-logisoil-green"
          label="Adicionar responsável"
          icon="fas fa-plus-circle"
          [type]="false"
          (click)="addResponsible()"
        ></app-button>
      </div>
    </div>
  </form>
</div>
