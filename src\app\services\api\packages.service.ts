import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class PackagesService {
  constructor(private api: ApiService) {}
  // Geração de pacotes
  postPackages(params) {
    const url = `/packages`;
    return this.api.post<any>(url, params, {}, 'user');
  }

  // Retorna os pacotes gerados para uso em filtro
  getPackagesSearch(params: any) {
    const url = `/packages/search`;
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca o pacote por ID
  getPackagesById(id: string) {
    const url = `/packages/${id}`;
    return this.api.get<any>(url, {}, false, 'client');
  }

  // Ignora o pacote no banco de dados
  deletePackages(params: any, id: string) {
    const url = `/packages/${id}`;
    return this.api.delete<any>(url, 'client', params);
  }

  // Calcular e Forçar cáclulo
  postPackagesCalculate(params: any, id: string) {
    const url = `/packages/${id}/calculate`;
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Atualiza revisões e etapas de obra do pacote
  patchPackages(params: any, id: string) {
    const url = `/packages/${id}`;
    return this.api.patch<any>(url, params, 'client');
  }
}
