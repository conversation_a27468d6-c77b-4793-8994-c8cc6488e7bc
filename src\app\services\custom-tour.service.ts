import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CustomTourService {
  constructor(private http: HttpClient) {}

  // Carrega os passos do tour de um arquivo JSON
  loadTourSteps(url: string): Observable<any[]> {
    return this.http.get<any[]>(url);
  }

  // Inicializa e começa o tour, pode ser reutilizado em vários componentes
  startTour(
    tourService,
    tourGuideUrl: string,
    callback?: (param: any) => void,
    customSteps?: any[] // Novo parâmetro para customizações
  ): void {
    this.loadTourSteps(tourGuideUrl).subscribe(
      (steps) => {
        // Substitui conteúdo com base nos customSteps fornecidos
        const mergedSteps = steps.map((step) => {
          // Verifica se existe uma configuração personalizada para o anchorId
          const customStep = customSteps?.find((custom) => custom.anchorId === step.anchorId);
          if (customStep) {
            // Retorna o passo original com as propriedades personalizadas sobrescritas
            return { ...step, ...customStep };
          }
          return step; // Retorna o passo original caso não haja customização
        });

        // Filtra apenas passos com âncoras válidas
        const validSteps = mergedSteps.filter((step) => !!document.querySelector(`[tourAnchor="${step.anchorId}"]`));

        if (validSteps.length > 0) {
          tourService.initialize(validSteps, { enableBackdrop: true });

          // Configura lógica para pular passos inexistentes durante o tour
          this.handleStepShow(tourService, callback);

          tourService.start();
        } else {
          console.warn('Nenhum passo válido foi encontrado para o tour.');
        }
      },
      (error) => {
        console.error('Erro ao carregar o arquivo de etapas do tour:', error);
      }
    );
  }

  // Lógica para pular passos inexistentes durante o tour
  private handleStepShow(tourService, callback: (param: any) => void = () => {}): void {
    tourService.stepShow$.subscribe((step) => {
      // Se houver um callback, chama o método no componente passando o passo atual
      if (callback) {
        callback(step);
      }
      const anchorExists = !!document.querySelector(`[tourAnchor="${step.anchorId}"]`);
      if (!anchorExists) {
        console.warn(`Anchor "${step.anchorId}" não encontrado. Pulando passo.`);
        tourService.next(); // Pula para o próximo passo
      }
    });
  }
}
