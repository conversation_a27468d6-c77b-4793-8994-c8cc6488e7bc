import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';

import { DataService } from 'src/app/services/data.service';
import { FilterService } from 'src/app/services/filter.service';
import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { StatusService } from 'src/app/services/status.service';
import { UserService } from 'src/app/services/user.service';

import { coordinateFormat, Datum, MultiSelectDefault } from 'src/app/constants/app.constants';
import {
  ActionPlan,
  OccurrenceDateFilter,
  InspectionSheetType,
  OccurrenceStatus,
  OccurrenceDatePeriod,
  OccurrenceActionPlanStatusList
} from 'src/app/constants/inspections.constants';

import { GoogleMapsComponent } from '@components/google-maps/google-maps.component';
import { MessagePadroes } from 'src/app/constants/message.constants';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { SharedService } from 'src/app/services/shared.service';

@Component({
  selector: 'app-occurrences',
  templateUrl: './occurrences.component.html',
  styleUrls: ['./occurrences.component.scss']
})
export class OccurrencesComponent implements OnInit {
  @ViewChild(GoogleMapsComponent) googleMaps: GoogleMapsComponent;

  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('ModalAttachmentsTrailComponent', { static: false }) ModalAttachmentsTrailComponent;
  @ViewChild('modalConfirm') ModalConfirm: any;
  @ViewChild('modalOccurrenceDetails') ModalOccurrenceDetails: any;

  inspectionSheetOccurrencesForm: FormGroup = this.fb.group({
    ClientId: [''],
    ClientUnitId: [''],
    StructureId: [''],
    StartDate: [''],
    EndDate: [''],
    Period: [''],
    Status: [''],
    InspectionSheetType: [''],
    WithActionPlan: [''],
    DateFilter: [''],
    DaysRemaining: [''],
    OutputDatum: ['']
  });

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public filterSearch: any = {};
  public filterParams: any = {};
  public filter: any = {
    StartDate: null,
    EndDate: null,
    AllDates: false,
    Period: 8,
    DaysRemaining: 0,
    Status: 'all',
    InspectionSheetType: 'all',
    SearchIdentifier: '',
    Datum: 1,
    CoordinateFormat: 2,
    OutputDatum: ''
  };

  public dateFilter = OccurrenceDateFilter;
  public datePeriod = OccurrenceDatePeriod;
  public disableDateFields: boolean = true;

  public inspectionSheetType = InspectionSheetType;
  public occurrenceStatus = OccurrenceStatus;
  public actionPlan = ActionPlan;
  public statusLegendList = OccurrenceActionPlanStatusList;

  public OutputDatum: any[] = Datum;
  public coordinateFormat: any[] = coordinateFormat;

  public selectedDateFilter: string | number = 'noFilter';
  public selectedStatus: string | number = 'all';
  public selectedOrigin: string | number = 'all';
  public selectedActionPlan: string | number = '';

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public func = fn;

  // Mapa
  public showMaps: boolean = false;
  public dataMapsStructure = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ]
  };

  public tableData: Array<{ [key: string]: any }> = [];
  public tableHeader: any = [
    {
      label: 'ID',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier'],
      type: 'link'
    },
    {
      label: 'Ficha',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['inspection_sheet_search_identifier'],
      type: 'link'
    },
    {
      label: 'Status',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['status'],
      extra: true
    },
    {
      label: 'Descrição',
      width: '140px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['aspect_description']
    },
    {
      label: 'Origem',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['inspection_sheet_type']
    },
    {
      label: 'Coordenadas',
      width: '160px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['coordinates']
    },
    {
      label: 'Planos de ação',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['action_plan_condition'],
      type: ['button'],
      condition_config: {
        true: {
          class: 'btn-logisoil-green',
          icon: 'fa fa-file-text-o fa-lg',
          label: '',
          type: true,
          option: 'editActionPlan',
          title: 'Ir para o Plano de Ação'
        },
        false: {
          class: 'btn-logisoil-red',
          icon: 'fa fa-file-text-o fa-lg',
          label: '',
          type: true,
          option: 'generateActionPlan',
          title: 'Gerar Plano de Ação'
        }
      },
      config: {}
    },
    {
      label: 'Detalhes',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['occurrence_details'],
      type: 'details'
    }
  ];

  public selectedColumns = this.tableHeader;
  public viewSettings = MultiSelectDefault.View;

  public clickEventsubscription: Subscription;

  public showAttachmentsModal = false;
  public attachmentsTrail: any[] = [];
  public selectedOccurrenceSearchIdentifier!: number;
  public occurrenceAttachments: any[] = [];

  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  public modalData: any = {};
  public modalId: any = null;

  public configModal: any = null;
  public titleModal: string = '';

  constructor(
    private dataService: DataService,
    private fb: FormBuilder,
    private filterService: FilterService,
    private inspectionSheetServiceApi: InspectionSheetServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private statusService: StatusService,
    private userService: UserService,
    private sharedService: SharedService,
    private router: Router
  ) {
    this.clickEventsubscription = this.sharedService.getClickEvent().subscribe((event) => {
      this.eventMap(event);
    });
  }

  /**
   * Executado ao inicializar o componente.
   * Obtém informações do perfil do usuário e permissões associadas.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros.
   */
  ngAfterViewInit(): void {
    this.ngxSpinnerService.show();
    setTimeout(() => {
      // Aplica filtros e carrega os dados automaticamente
      if (this.filter) {
        this.searchOccurrences();
      }
      this.ngxSpinnerService.hide(); // Oculta o spinner após os dados serem carregados
    }, 1000);
  }

  /**
   * Extrai os filtros de hierarquia da estrutura selecionada.
   * @returns {any} - Objeto contendo os filtros de hierarquia.
   */
  extractHierarchyFilters(): any {
    const hierarchyFilters = this.hierarchy.getFilters();

    return {
      ClientId: hierarchyFilters?.clients?.[0]?.id || '',
      ClientUnitId: hierarchyFilters?.units?.[0]?.id || '',
      StructureId: hierarchyFilters?.structures?.[0]?.id || ''
    };
  }

  /**
   * Aplica os filtros salvos à pesquisa da ocorrência.
   * @param {any} filters - Filtros a serem aplicados.
   */
  private applySavedFilters(filters: any): void {
    this.filterSearch = filters;
    this.page = filters.Page || 1;
    this.pageSize = filters.PageSize || 10;

    this.filter = {
      SearchIdentifier: filters.SearchIdentifier || '',
      StartDate: filters.StartDate || '',
      EndDate: filters.EndDate || '',
      Status: filters.Status || '',
      InspectionSheetType: filters.InspectionSheetType || '',
      ...this.extractHierarchyFilters() // Adiciona os filtros de hierarquia
    };
  }

  /**
   * Calcula o período de datas com base em um valor numérico e atualiza os campos `StartDate` e `EndDate` do filtro.
   *
   * @param {number} value - Valor selecionado que representa o intervalo de tempo:
   *   - 1 = Último mês
   *   - 2 = Últimos 3 meses
   *   - 3 = Últimos 6 meses
   *   - 4 = Últimos 12 meses
   *   - 5 = Últimos 24 meses
   *   - 6 = Últimos 60 meses
   *   - 8 = Período personalizado (libera campos)
   * @param {boolean} [reset=true] - Se `true`, limpa os campos antes de calcular (útil para mudanças de seleção).
   */
  calculatePeriod(value: number, reset = true): void {
    const today = moment();
    this.disableDateFields = parseInt(value.toString()) !== 8;

    if (reset) {
      this.filter.StartDate = '';
      this.filter.EndDate = '';
    }

    if ([1, 2, 3, 4, 5, 6].includes(parseInt(value.toString()))) {
      // Define EndDate como hoje
      this.filter.EndDate = today.format('YYYY-MM-DD');

      // Subtrai a quantidade de meses com base no valor selecionado
      let monthsToSubtract = 0;
      switch (parseInt(value.toString())) {
        case 1:
          monthsToSubtract = 1;
          break;
        case 2:
          monthsToSubtract = 3;
          break;
        case 3:
          monthsToSubtract = 6;
          break;
        case 4:
          monthsToSubtract = 12;
          break;
        case 5:
          monthsToSubtract = 24;
          break;
        case 6:
          monthsToSubtract = 60;
          break;
      }
      this.filter.StartDate = today.clone().subtract(monthsToSubtract, 'months').format('YYYY-MM-DD');

      // Desabilita campos se estiver usando [(ngModel)] com disable no HTML
      // this.disableDateFields = true; // use uma variável no HTML para controlar o disable
    } else if (parseInt(value.toString()) === 8) {
      // Personalizado: libera os campos pro usuário
      this.filter.StartDate = '';
      this.filter.EndDate = '';
      // this.disableDateFields = false;
    }
  }

  /**
   * Obtém a lista de ocorrências a partir dos parâmetros fornecidos.
   * @param {any} params - Parâmetros da consulta.
   */
  getOccurrenceList(params: any): void {
    this.ngxSpinnerService.show();

    // Limpa mensagens e dados anteriores
    this.message.text = '';
    this.message.status = false;

    this.inspectionSheetServiceApi.getInspectionSheetOccurrences(params).subscribe(
      (resp) => {
        const dados: any = resp;
        if (dados?.status === 200) {
          this.tableData = dados.body?.data ? this.formatOccurrenceData(dados.body.data) : [];
          this.collectionSize = dados.body?.total_items_count || 0;
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.message.text = MessagePadroes.NoRegister;
          this.message.status = true;
          this.message.class = 'alert-warning';

          // Atualiza o mapa se estiver visível
          if (this.showMaps) {
            this.mapStructure();
          }

          setTimeout(() => {
            this.message.status = false;
          }, 4000);
        }

        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.error('Erro ao buscar ocorrências:', error);
        this.message.status = true;
        this.message.class = 'alert-danger';
        this.tableData = [];
        this.collectionSize = 0;
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Realiza a busca das ocorrências aplicando os filtros selecionados.
   */
  searchOccurrences(): void {
    this.filterParams = {
      Status: this.selectedStatus !== 'all' ? this.selectedStatus : '',
      InspectionSheetType: this.selectedOrigin !== 'all' ? this.selectedOrigin : '',
      WithActionPlan: this.selectedActionPlan !== '' ? this.selectedActionPlan : '',
      OutputDatum: this.filter.Datum || '',
      CoordinateFormat: this.filter.CoordinateFormat || '',
      ...this.extractHierarchyFilters(),
      Page: this.page,
      PageSize: this.pageSize
    };

    // Lógica de datas
    if (this.selectedDateFilter === 'noFilter' || this.filter.AllDates) {
      // Não adiciona StartDate, EndDate ou DaysRemaining
    } else {
      this.filterParams.DateFilter = this.selectedDateFilter;

      if (this.selectedDateFilter == 3) {
        // Filtro por dias restantes
        this.filterParams.DaysRemaining = this.filter.DaysRemaining || '';
      } else {
        // Filtro por período
        this.filterParams.StartDate = this.filter.StartDate || '';
        this.filterParams.EndDate = this.filter.EndDate || '';
      }
    }

    this.filterService.setFilters(this.filterParams, this.hierarchy.getFilters());

    // Busca ocorrências
    this.getOccurrenceList(this.filterParams);

    // Atualiza o mapa se visível
    if (this.showMaps) {
      this.mapStructure();
    }
  }

  /**
   * Formata os dados brutos das ocorrências para exibição na tabela.
   * @param {any[]} data - Lista de ocorrências.
   * @returns {any[]} - Lista formatada.
   */
  formatOccurrenceData(data: any[]): any[] {
    return data.map((item) => {
      const formattedStatus = this.statusService.formatStatus(item.occurrence_and_action_plan_status);

      // Define o formato das coordenadas
      let coordinates = '-';
      if (this.filter.CoordinateFormat == 1) {
        // Geodésico (Latitude, Longitude)
        const lat = this.formatCoordinate(item.latitude_sirgas2000);
        const lng = this.formatCoordinate(item.longitude_sirgas2000);
        coordinates = `${lat} graus, ${lng} graus`;
      } else {
        // UTM (Northing, Easting)
        const north = this.formatCoordinate(item.northing);
        const east = this.formatCoordinate(item.easting);
        coordinates = `N ${north} (m), E ${east} (m)`;
      }

      let map = { latitude: item.latitude_sirgas2000, longitude: item.longitude_sirgas2000 };

      return {
        id: item.id,
        search_identifier: item.search_identifier,
        inspection_sheet_id: item.inspection_sheet_id,
        inspection_sheet_search_identifier: item.inspection_sheet_search_identifier,
        inspection_sheet_type: InspectionSheetType.find((type) => type.value === item.inspection_sheet_type)?.label || 'Desconhecido',
        status: formattedStatus.status,
        extra: formattedStatus.extra,
        color: formattedStatus.color,
        aspect_description: item.aspect?.description || '-',
        coordinates,
        maps: map,
        name: item.search_identifier,
        action_plan_id: item.action_plan_id,
        action_plan_condition: item.action_plan_id ? true : false,
        structure_id: item.structure.id
      };
    });
  }

  /**
   * Formata uma coordenada para 3 casas decimais, se for número válido.
   * @param {any} value - Valor da coordenada.
   * @returns {string} - Coordenada formatada.
   */
  private formatCoordinate(value: any): string {
    const num = Number(value);
    return isNaN(num) ? '-' : num.toFixed(3);
  }

  /**
   * Retorna a classe de status de acordo com o status da ocorrência.
   * @param status - Código do status
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'HasNoActionPlan':
        return 'status-preto';
      case 'Completed':
        return 'status-cinza';
      case 'PendingMoreThanThreeDays':
        return 'status-verde';
      case 'PendingThreeDaysOrLess':
        return 'status-amarelo';
      case 'Overdue':
        return 'status-vermelho';
      case 'OverdueAndRecurring':
        return 'status-vermelho-piscante';
      default:
        return 'status-desconhecido';
    }
  }

  /**
   * Carrega a estrutura selecionada do `localStorage`, obtém sua coordenada e atualiza o mapa.
   *
   * - Verifica se existe o item `filterHierarchy` no armazenamento local.
   * - Extrai o `ClientId` e `StructureId` e faz a requisição da coordenada da estrutura.
   * - Atualiza o centro do mapa e adiciona um marcador correspondente à estrutura.
   * - Caso encontre a coordenada, envia os dados atualizados ao mapa usando `sendDataMap('markers')`.
   */
  loadMapFromStorage(): void {
    const filterHierarchy = this.extractHierarchyFilters();

    if (filterHierarchy) {
      const clientId = filterHierarchy.ClientId;
      const structureId = filterHierarchy.StructureId;

      if (clientId && structureId) {
        this.dataService.getStructureCoordinate(clientId, [structureId]).subscribe((coordinate) => {
          if (coordinate.length > 0) {
            const lat = coordinate[0].decimal_geodetic.latitude;
            const lng = coordinate[0].decimal_geodetic.longitude;

            // Limpa marcadores
            this.dataMapsStructure.markers = [];

            // Adiciona novo marcador
            this.dataMapsStructure.markers.push({
              position: { lat, lng },
              title: 'Estrutura',
              options: {}
            });

            // Atualiza centro
            this.dataMapsStructure.center = { lat, lng };

            // Envia para o mapa
            this.sendDataMap('markers');
          }
        });
      } else {
        this.resetMap();
      }
    }
  }

  /**
   * Carrega e centraliza o mapa com base na estrutura selecionada na hierarquia atual.
   *
   * - Primeiro, reinicia o estado do mapa chamando `resetMap()`.
   * - Extrai os filtros de hierarquia (Cliente e Estrutura).
   * - Caso `StructureId` ou `ClientId` não estejam definidos, chama `loadMapFromStorage()` como fallback.
   * - Se ambos estiverem disponíveis:
   *   - Busca as coordenadas da estrutura através do `dataService.getStructureCoordinate`.
   *   - Atualiza o centro do mapa (`dataMapsStructure.center`) e insere um novo marcador.
   *   - Envia os dados atualizados ao mapa via `sendDataMap('markers')`.
   * - Inclui logs informativos para facilitar o debug.
   */

  mapStructure(): void {
    this.resetMap();

    const filters = this.extractHierarchyFilters();
    const clientId = filters.ClientId;
    const structureId = filters.StructureId;

    if (!structureId || !clientId) {
      this.loadMapFromStorage();
      return;
    }

    this.dataService.getStructureCoordinate(clientId, [structureId]).subscribe((coordinate) => {
      if (coordinate.length > 0) {
        const lat = coordinate[0].decimal_geodetic.latitude;
        const lng = coordinate[0].decimal_geodetic.longitude;

        this.resetMap();

        this.dataMapsStructure.markers.push({
          position: { lat, lng },
          title: 'Estrutura',
          options: {}
        });

        this.dataMapsStructure.center = { lat, lng };

        this.sendDataMap('markers');
      }
    });
  }

  /**
   * Alterna a exibição do mapa.
   *
   * - Inverte o estado da variável `showMaps`.
   * - Quando o mapa é exibido (`showMaps` = true), carrega as coordenadas da estrutura armazenadas no localStorage,
   *   chamando o método `loadMapFromStorage()`.
   */
  toggleMap(): void {
    this.showMaps = !this.showMaps;
    if (this.showMaps) {
      this.loadMapFromStorage();
    }
  }

  /**
   * Envia dados para atualização do mapa.
   *
   * - Verifica se o mapa foi inicializado antes de definir os dados.
   * - Caso o mapa não esteja pronto, aguarda 500ms e tenta novamente.
   *
   * @param option - Opção de atualização para o mapa (ex.: `'markers'`).
   */
  sendDataMap(option: string): void {
    if (this.googleMaps) {
      this.googleMaps.setDataMap(this.dataMapsStructure, option, true);
    }
  }

  /**
   * Plota as ocorrências no mapa com marcadores personalizados.
   *
   * Para cada ocorrência recebida:
   * - Cria um marcador SVG com cor de preenchimento baseada em `occurrence.color`.
   * - Define a posição do marcador com as coordenadas `latitude` e `longitude`.
   * - Adiciona um `infoWindowMarker` contendo:
   *   - Identificador e status da ocorrência.
   *   - Dois botões: "Consultar" e "Exibir anexos", com eventos configurados.
   * - Centraliza o mapa na última ocorrência processada.
   *
   * Após processar todas as ocorrências, envia os marcadores para renderização no mapa com `setDataMap`.
   *
   * @param {$occurrences} Array de objetos de ocorrência contendo dados de geolocalização e status.
   */
  plotOccurruence($occurrences) {
    if (this.dataMapsStructure.markers.length > 0) {
      this.dataMapsStructure.markers = [this.dataMapsStructure.markers[0]];
    }

    $occurrences.forEach((occurrence) => {
      let strokeColor = 'white';
      let fillColor = occurrence.color;
      let svgMarker = {
        path: 'M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0',
        fillColor: fillColor,
        fillOpacity: 1.0,
        strokeWeight: 1.5,
        strokeColor: strokeColor,
        rotation: 0,
        scale: 0.3,
        anchor: new google.maps.Point(0, 0)
      };
      let marker = {
        position: {
          lat: occurrence.maps.latitude,
          lng: occurrence.maps.longitude
        },
        title: 'Ocorrência ' + occurrence.search_identifier,
        options: {},
        icon: svgMarker,
        id: 'mk-' + 'Ocorrência ' + occurrence.search_identifier,
        zIndex: -999
      };
      let infoWindowMarker = {
        content: '',
        ariaLabel: 'Ocorrência ' + occurrence.search_identifier,
        id: 'Ocorrência ' + occurrence.search_identifier,
        data: occurrence,
        classTitle: 'text-center',
        subtitle: occurrence.status,
        classGroup: 'd-flex justify-content-around mb-2',
        contentConfig: [
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-blue me-1',
              icon: '',
              label: 'Consultar',
              type: true,
              eventClick: true,
              event: 'consultOccurrence',
              id: 'iw-button-' + fn.hashCode(occurrence.search_identifier)
            }
          },
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-green',
              icon: 'fa fa-paperclip',
              title: 'Exibir anexos',
              'data-bs-toggle': 'tooltip',
              type: true,
              eventClick: true,
              event: 'openAttachmentsModal',
              id: 'iw-button-' + fn.hashCode(occurrence.search_identifier)
            }
          }
        ]
      };

      marker['infoWindowMarker'] = infoWindowMarker;
      this.dataMapsStructure.markers.push(marker);
      this.dataMapsStructure.center = { lat: occurrence.maps.latitude, lng: occurrence.maps.longitude };
    });

    this.googleMaps.setDataMap(this.dataMapsStructure, 'markersMultiple', false);
  }

  /**
   * Reseta o mapa para o estado inicial padrão (centro e marcador fixo).
   */
  resetMap(): void {
    this.dataMapsStructure = {
      height: '500px',
      width: '100%',
      zoom: 16,
      center: { lat: -17.930178, lng: -43.7908453 },
      options: {
        mapTypeId: 'satellite',
        zoomControl: true,
        scrollwheel: true,
        disableDoubleClickZoom: true,
        maxZoom: 22,
        minZoom: 1
      },
      markers: [
        {
          position: {
            lat: -17.930178,
            lng: -43.7908453
          },
          title: '',
          options: {}
        }
      ]
    };

    this.sendDataMap('markers');
  }

  /**
   * Redefine todos os filtros aplicados na tela de ocorrências e reinicia a listagem.
   */
  resetFilter(): void {
    // Zera hierarquia (Cliente, Unidade, Estrutura)
    this.hierarchy.resetFilters();

    // Reseta filtros manuais
    this.filter = {
      StartDate: '',
      EndDate: '',
      Period: 8,
      DaysRemaining: 0,
      Status: 'all',
      InspectionSheetType: 'all',
      SearchIdentifier: '',
      Datum: 1,
      CoordinateFormat: 2,
      OutputDatum: ''
    };

    // Reseta params
    this.filterParams = {};
    this.filterSearch = {};

    // Reseta filtros salvos no serviço
    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());

    // Limpa o mapa se estiver sendo exibido
    this.resetMap(); // agora com center e markers padrão

    // Reinicia listagem
    this.managerFilters(true);
  }

  /**
   * Gerencia os filtros da pesquisa, aplicando filtros salvos ou executando nova busca.
   * @param {boolean} [triggerSearch=false] - Indica se deve executar a busca imediatamente.
   */
  managerFilters(triggerSearch: boolean = false): void {
    if (triggerSearch) {
      this.searchOccurrences();
    } else {
      const data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchOccurrences();
      } else {
        this.applySavedFilters(data.filters);

        // Configura os filtros de hierarquia, se disponíveis
        if (Object.keys(data.filtersHierarchy).length !== 0) {
          this.hierarchy.setClients(data.filtersHierarchy.clients);
          this.hierarchy.setUnits(data.filtersHierarchy.units);
          this.hierarchy.setStructures(data.filtersHierarchy.structures);
        }
      }
    }
  }

  /**
   * Gerencia eventos relacionados à hierarquia de unidades, estruturas e instrumentos.
   * @param {any} $event - O evento disparado pela hierarquia.
   */
  getEventHierarchy($event) {
    this.loadMapFromStorage();
  }

  /**
   * Alterna a exibição de colunas da tabela de acordo com o evento de seleção ou deseleção.
   * @param {any} $event - Evento acionado ao selecionar ou deselecionar colunas.
   * @param {string} type - Tipo de ação realizada (selecionar, deselecionar, selecionar todas, etc).
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Método para tratar eventos de clique nas linhas da tabela.
   * Redireciona para a página apropriada com base na ação do evento.
   * @param {any} $event - O evento de clique na linha.
   */
  clickRowEvent($event: any = null): void {
    const action = $event?.action ?? $event?.data?.context ?? null;
    const row = this.tableData.find((item) => item.id === $event.id);
    const occurrenceId = row.id;
    let url = '';

    switch (action) {
      case 'link':
        if ($event?.column == 'search_identifier') {
          this.googleMaps.clearMarkersAttachments();
          this.showMaps = true;
          this.plotOccurruence([row]);
        }

        if ($event?.column == 'inspection_sheet_search_identifier') {
          const inspectionSheetId = row.inspection_sheet_id;
          const url = this.router.serializeUrl(this.router.createUrlTree([`/inspections/inspection-sheet/${inspectionSheetId}/view`]));
          window.open(url, '_blank');
        }
        break;

      case 'confirm':
        this.onRemoveMarkers();
        break;

      case 'openDetails':
        url = this.router.serializeUrl(this.router.createUrlTree([`/inspections/inspection-sheet/occurrence/${occurrenceId}/view`]));
        window.open(url, '_blank');
        break;

      case 'generateActionPlan':
        this.router.navigate(['inspections/action-plan/newActionPlan'], { queryParams: { occurrenceId: occurrenceId } });
        break;

      case 'editActionPlan':
        this.router.navigate([`inspections/action-plan/${row.action_plan_id}/edit`], { queryParams: { structureId: row.structure_id } });
        break;
    }
  }

  /**
   * Manipula eventos provenientes do mapa, como clique em ocorrências ou anexos.
   *
   * - `consultOccurrence`: Abre uma nova aba com a visualização da ocorrência.
   * - `openAttachmentsModal`: Abre o modal de trilha de anexos para a ocorrência clicada.
   *
   * @param $event Evento emitido pelo mapa, contendo tipo e dados da ocorrência.
   */
  eventMap($event) {
    switch ($event.type) {
      case 'consultOccurrence':
        const occurrenceId = $event.data.data.id;
        const url = this.router.serializeUrl(this.router.createUrlTree([`/inspections/inspection-sheet/occurrence/${occurrenceId}/view`]));
        window.open(url, '_blank');
        break;

      case 'openAttachmentsModal':
        this.selectedOccurrenceSearchIdentifier = $event.data.data.id;
        this.selectedOccurrenceSearchIdentifier = $event.data.data.search_identifier;
        this.loadTrailAttachments($event.data.data.id);
        break;
    }
  }

  /**
   * Trata eventos emitidos por modais de confirmação.
   * Executa ações específicas com base na propriedade `action` do evento recebido.
   *
   * @param $event Objeto contendo a ação a ser executada, ex: { action: 'removeMarkers' }.
   */
  clickEvent($event: any = null) {
    if ($event.action === 'removeMarkers') {
      this.onRemoveMarkers();
    }

    if ($event.action === 'removePhotos') {
      this.onRemovePhotos();
    }
  }

  /**
   * Busca o histórico de ocorrências relacionadas para a ocorrência informada
   * e prepara os dados para exibição no modal de trilha de anexos.
   *
   * @param {string} occurrenceId - ID da ocorrência selecionada no mapa.
   */
  loadTrailAttachments(occurrenceId: string): void {
    this.ngxSpinnerService.show();
    this.inspectionSheetServiceApi.getInspectionSheetOccurrenceById(occurrenceId).subscribe({
      next: (response: any) => {
        const previous = response.previous_occurrences || [];

        this.attachmentsTrail = previous.map((item: any) => ({
          search_identifier: item.search_identifier,
          inspection_sheet_id: item.inspection_sheet_search_identifier,
          created_date: moment(item.created_date).format('DD/MM/YYYY'),
          occurrence_id: item.id,
          attachments_count: item.occurrence_attachments?.length || 0,
          select: false
        }));

        this.occurrenceAttachments = previous.map((item: any) => ({
          search_identifier: item.search_identifier,
          occurrence_attachments: item.occurrence_attachments
        }));

        const currentOccurrence = {
          search_identifier: response.search_identifier,
          inspection_sheet_id: response.inspection_sheet_search_identifier,
          created_date: moment(response.created_date).format('DD/MM/YYYY'),
          occurrence_id: response.id,
          attachments_count: response.occurrence_attachments?.length || 0,
          select: false
        };

        const currentOccurrenceAttachments = {
          search_identifier: response.search_identifier,
          occurrence_attachments: response.occurrence_attachments
        };

        this.attachmentsTrail.unshift(currentOccurrence);
        this.occurrenceAttachments.unshift(currentOccurrenceAttachments);

        this.showAttachmentsModal = true;
        this.ngxSpinnerService.hide();
      },
      error: (err) => {
        this.ngxSpinnerService.hide();
        console.error('Erro ao buscar dados da ocorrência:', err);
      }
    });
  }

  /**
   * Recebe os IDs das ocorrências selecionadas no modal e inicia a lógica
   * para exibição dos anexos dessas ocorrências no mapa.
   *
   * @param {number[]} selectedIds - Lista de IDs das ocorrências selecionadas.
   */
  handleAddToMap(selectedIds: number[]): void {
    this.showAttachmentsModal = false;
    const filtered = this.occurrenceAttachments.filter((item) => selectedIds.includes(item.search_identifier));

    this.googleMaps.clearMarkersAttachments();
    this.googleMaps.addAttachmentMarkers(filtered);
  }

  /**
   * Configura e abre o modal de confirmação para remover todos os marcadores do mapa.
   */
  confirmRemoveMarkers(): void {
    this.modalTitle = 'Remover marcadores';
    this.modalMessage = 'Deseja remover todos os marcadores do mapa?';
    this.modalInstruction = 'Esta ação irá limpar todos os marcadores visíveis no mapa.';
    this.modalConfig.iconHeader = 'fa fa-trash-o';
    this.modalConfig.action = 'removeMarkers';
    this.modalId = 'removeMarkers';
    this.ModalConfirm.openModal();
  }

  /**
   * Abre o modal de confirmação para remoção de todas as fotos (marcadores de anexos) do mapa.
   * Configura título, mensagem, instrução e ícone de exclusão.
   */
  confirmRemovePhotos(): void {
    this.modalTitle = 'Remover fotos';
    this.modalMessage = 'Deseja remover todos as fotos do mapa?';
    this.modalInstruction = 'Esta ação irá limpar todos as fotos visíveis no mapa.';
    this.modalConfig.iconHeader = 'fa fa-trash-o';
    this.modalConfig.action = 'removePhotos';
    this.modalId = 'removePhotos';
    this.ModalConfirm.openModal();
  }

  /**
   * Remove todos os marcadores normais (ex: seções, instrumentos, ocorrências) do mapa.
   */
  onRemoveMarkers(): void {
    this.googleMaps.clearMarkers();
  }

  /**
   * Remove todos os marcadores de anexos (fotos) do mapa.
   */
  onRemovePhotos(): void {
    this.googleMaps.clearMarkersAttachments();
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }

    this.searchOccurrences();
  }
}
