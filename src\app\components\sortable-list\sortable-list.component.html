<div class="input-group mb-1" *ngIf="filter">
  <button class="btn" type="button">
    <i class="fa fa-search"></i>
  </button>
  <input
    type="text"
    class="form-control mb-1"
    placeholder="Procurar..."
    [(ngModel)]="searchItem"
  />
</div>

<ul
  class="list-group"
  cdkDropList
  (cdkDropListDropped)="onDrop($event, orderField, 0)"
>
  <ng-template ngFor let-item [ngForOf]="dataList" let-i="index">
    <li
      class="list-group-item"
      cdkDrag
      cdkDragLockAxis="y"
      [cdkDragDisabled]="dragDrop"
      *ngIf="
        !filter ||
        searchItem == '' ||
        item[textField].toLowerCase().includes(searchItem.toLowerCase())
      "
    >
      <div class="drag-handle">
        <ng-container>
          <input
            class="form-check-input me-3"
            type="checkbox"
            *ngIf="checkbox"
            #itemCheckbox
            (change)="checkedItem(itemCheckbox, i)"
            [checked]="item.selected"
            [disabled]="dragDrop"
          />
          <app-button
            [class]="'btn-logisoil-editItem mini'"
            [icon]="'fa fa-thin fa-pencil'"
            class="me-1"
            *ngIf="edit"
            (click)="editItem(item)"
          >
          </app-button>

          {{ item[orderField] + 1 }} - {{ item[textField] }}
        </ng-container>
      </div>
    </li>
  </ng-template>
</ul>
