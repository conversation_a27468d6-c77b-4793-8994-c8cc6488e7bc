<ng-template #modalComponents let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">{{ title }}</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <div class="px-2 py-2">
    <app-images
      *ngIf="component == 'app-images' && config && config.imagesItens"
      [imagesItens]="config.imagesItens"
      [message]="config.message"
      [uploadActive]="config.uploadActive"
      #appImages
    ></app-images>
  </div>
</ng-template>
