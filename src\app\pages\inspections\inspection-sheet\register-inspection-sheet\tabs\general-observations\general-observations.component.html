<form [formGroup]="notesForm">
  <!-- Notas <PERSON> -->
  <div formArrayName="notes">
    <ng-container
      *ngFor="let note of notes.controls; let i = index"
      [formGroupName]="i"
    >
      <div class="row mt-3">
        <div class="col-md-12">
          <label *ngIf="!view" class="form-label">Observação:</label>
          <div
            class="d-flex align-items-start"
            *ngIf="!view; else readModeNote"
          >
            <textarea
              class="form-control"
              rows="5"
              formControlName="note"
              placeholder="Digite sua observação aqui..."
              (blur)="onBlur('note', i)"
            ></textarea>
            <app-button
              class="btn-sm btn-logisoil-red ms-2"
              icon="fa fa-trash"
              data-bs-toggle="tooltip"
              data-bs-placement="top"
              title="Excluir observação"
              (click)="removeNote(i)"
            ></app-button>
          </div>
          <ng-template #readModeNote>
            <div class="content">{{ note.get('note')?.value }}</div>
          </ng-template>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-md-6 d-flex align-items-start">
          <!-- Upload de arquivo -->
          <ng-container
            *ngIf="!view && !note.get('file.name')?.value; else downloadLink"
          >
            <input
              type="file"
              class="form-control"
              (change)="onFileSelected($event, i)"
              id="fileInput_{{ i }}"
              #fileInput
            />
          </ng-container>
          <!-- Link para download -->
          <ng-template #downloadLink>
            <div class="d-flex flex-column">
              <span class="file-name">{{ note.get('file.name')?.value }}</span>
              <div
                class="mt-2 d-flex align-items-center"
                *ngIf="note.get('file.base64')?.value"
              >
                <a
                  [href]="note.get('file.base64')?.value"
                  download="{{ note.get('file.name')?.value }}"
                >
                  <app-button
                    class="btn-logisoil-blue"
                    label="Baixar arquivo"
                    icon="fa fa-download"
                  ></app-button>
                </a>

                <app-button
                  class="btn-logisoil-blue ms-2"
                  label="Visualizar arquivo"
                  icon="fa fa-eye"
                  (click)="openFile(note.get('file.base64')?.value)"
                ></app-button>
                <app-button
                  *ngIf="!view"
                  class="btn-logisoil-red ms-2"
                  label="Remover"
                  (click)="removeFile(i)"
                ></app-button>
              </div>
            </div>
          </ng-template>
        </div>
      </div>
    </ng-container>
  </div>

  <!-- Botão Adicionar Nota -->
  <div class="col-md-12 d-flex justify-content-end mt-1" *ngIf="!view">
    <app-button
      class="btn-logisoil-green"
      label="Adicionar observação"
      icon="fas fa-plus-circle"
      (click)="addNote()"
    ></app-button>
  </div>
</form>
