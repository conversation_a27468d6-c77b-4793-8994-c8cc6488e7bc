.logo-inspection {
  width: 200px; /* Define a largura fixa */
  height: 38px; /* Mantém a altura proporcional */
  display: block; /* Remove espaçamentos extras */
  max-width: 100%; /* Evita problemas em telas menores */
}

.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.card-header {
  background-color: #34b575;
  color: #ffffff;
  font-size: 0.875em;
}

label.title {
  font-size: 0.875em;
  font-weight: 800;
  margin-bottom: 4px;
}

span.content {
  font-size: 0.875em;
}

div[class^='col-'] {
  margin: 6px 0;
  /* Suas regras de estilo aqui */
}

.subheader {
  background-color: rgba(3, 37, 97, 1);
  color: #ffffff;
  height: 32px;
}

table > thead > tr > th,
.header-title {
  background: rgba(3, 37, 97, 0.1);
  font-size: 0.875em !important;
  text-align: center;
  color: #032561;
  border: 1px solid #ffffff;
  font-weight: bold;
}

.header-title {
  font-size: 1em !important;
  font-weight: bold;
}

.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #ffffff;
}

tbody {
  font-size: 0.875em !important;
  text-align: center;
  border: 1px solid #d4d2d2;
  color: #032561;
  background-color: rgba(212, 210, 210, 0.1);
}

// ---------------------------------------------
.timeline {
  position: relative;
  padding: 10px 0;
  margin: 0 auto;
}

.timeline:after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background-color: #dee2e6;
}

.timeline-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  position: relative;
}

/* 🔘 Bolinha da timeline */
.timeline-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #6c757d;
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

/* 📆 Data - estreitando distância lateral */
.timeline-time.left-time {
  width: 46%;
  padding-right: 5px;
  text-align: right;
}

.timeline-time.right-time {
  width: 46%;
  padding-left: 5px;
  text-align: left;
}

.timeline-time {
  font-size: 1rem;
  color: #212529;
}

.timeline-time small {
  display: block;
  font-size: 0.85rem;
  color: #6c757d;
}

/* 🧾 Cards */
.timeline-content {
  width: 46%;
  padding: 0;
}

.timeline-content.left {
  text-align: right;
  display: flex;
  justify-content: flex-end;
}

.timeline-content.right {
  text-align: left;
  display: flex;
  justify-content: flex-start;
}

.timeline-content .card {
  width: 100%;
  max-width: 360px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  margin: 0;
}

.timeline-content .card-body {
  padding: 0.75rem;
  font-size: 0.95rem;
}

.timeline-content .card-body .fa {
  font-size: 0.9rem;
  color: #0d6efd;
}

/* 🔲 Ícones quadrados */
.square-icon {
  width: 14px;
  height: 14px;
  background-color: #6c757d;
  display: inline-block;
  border-radius: 3px;
}

/* 🖼️ Anexos */
.timeline-content .img-thumbnail {
  border-radius: 6px;
  transition: transform 0.2s ease-in-out;
  object-fit: cover;
}

.timeline-content .img-thumbnail:hover {
  transform: scale(1.05);
}

/* 📱 Responsivo */
@media (max-width: 768px) {
  .timeline:after {
    left: 28px;
  }

  .timeline-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .timeline-dot {
    left: 28px;
  }

  .timeline-time,
  .timeline-content {
    width: 100%;
    padding: 0;
    margin-left: 56px;
    text-align: left;
  }

  .timeline-content .card {
    max-width: 100%;
  }
}

.timeline-dot::before,
.timeline-dot::after {
  content: '';
  position: absolute;
  top: 6px; /* Meio da bolinha */
  width: 30px;
  height: 1px;
  border-top: 1px dashed #6c757d;
}

.timeline-dot::before {
  right: 100%;
  margin-right: 4px;
}

.timeline-dot::after {
  left: 100%;
  margin-left: 4px;
}

//---------
