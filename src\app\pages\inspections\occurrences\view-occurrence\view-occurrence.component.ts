import { AfterViewInit, Component, Input, OnChanges, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize, map, Observable, of, switchMap } from 'rxjs';

import { OccurrenceResponse, OccurrenceActionPlanDeadlineStatus } from 'src/app/constants/inspections.constants';

import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';
import { DataService } from 'src/app/services/data.service';
import { StatusService } from 'src/app/services/status.service';

import { GoogleMapsComponent } from '@components/google-maps/google-maps.component';

import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import { MessagePadroes } from 'src/app/constants/message.constants';

@Component({
  selector: 'app-view-occurrence',
  templateUrl: './view-occurrence.component.html',
  styleUrls: ['./view-occurrence.component.scss']
})
export class ViewOccurrenceComponent implements OnInit {
  @ViewChild('mapOccurrence', { static: false }) mapOccurrence: GoogleMapsComponent;

  public dados: any = null;
  public message: any = { text: '', status: false, class: 'alert-success' };
  public load: boolean | null = null;

  public dataMapsOccurrence = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  public occurrenceAttachmentsSanitized: {
    file: { name: string; base64: SafeResourceUrl };
    northing: number;
    easting: number;
  }[] = [];

  public historicalOccurrences: any[] = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private dataService: DataService,
    private sanitizer: DomSanitizer,
    private ngxSpinnerService: NgxSpinnerService,
    private inspectionSheetServiceApi: InspectionSheetServiceApi,
    private router: Router,
    private statusService: StatusService
  ) {}

  /**
   * Método executado na inicialização do componente.
   *
   * - Obtém os parâmetros da rota para capturar o `occurrenceId` e `clienteId`.
   * - Reinicia os dados da tela (`dados`, `occurrenceAttachmentsSanitized`, `historicalOccurrences`).
   * - Chama o método `getInspectionSheetOccurrenceById` para buscar os dados da ocorrência.
   * - Ao receber os dados:
   *   - Sanitiza os anexos da ocorrência.
   *   - Processa ocorrências anteriores (`previous_occurrences`) para exibir histórico.
   *   - Chama `callMapOccurrence` para posicionar o marcador no mapa com base nas coordenadas da ocorrência.
   *   - Define a flag `load` como `true` ao final.
   */
  ngOnInit(): void {
    this.activatedRoute.params
      .pipe(
        switchMap((params) => {
          const occurrenceId = params['occurrenceId'];
          const clientId = this.activatedRoute.snapshot.queryParams['clienteId'] ?? '';

          this.dados = null;
          this.occurrenceAttachmentsSanitized = [];
          this.historicalOccurrences = [];

          return this.getInspectionSheetOccurrenceById(occurrenceId, clientId);
        })
      )
      .subscribe((resp) => {
        if (!resp || resp?.status === 204 || !resp?.body) {
          this.load = false;
        } else {
          this.dados = resp.body;

          if (this.dados?.occurrence_attachments?.length) {
            this.occurrenceAttachmentsSanitized = this.dados.occurrence_attachments.map((item) => {
              let base64Value = item.file?.base64 || '';
              if (base64Value && !base64Value.startsWith('data:')) {
                const decode = fn.base64Extension(base64Value.slice(0, 100));
                if (decode.mimeType) {
                  base64Value = `data:${decode.mimeType};base64,${base64Value}`;
                }
              }
              const safeBase64 = base64Value ? this.sanitizer.bypassSecurityTrustResourceUrl(base64Value) : null;
              return {
                ...item,
                file: {
                  name: item.file.name,
                  base64: safeBase64
                }
              };
            });
          }

          this.historicalOccurrences = this.processPreviousOccurrences(this.dados?.previous_occurrences || []);

          if (this.dados?.latitude_sirgas2000 && this.dados?.longitude_sirgas2000) {
            setTimeout(() => {
              this.callMapOccurrence(this.dados);
            }, 100);
          }

          this.load = true;
        }
      });
  }

  /**
   * Busca os dados de uma ocorrência vinculada a uma ficha de inspeção, dado o `occurrenceId`.
   *
   * - Exibe o spinner de carregamento antes da requisição.
   * - Faz a chamada para a API `getInspectionSheetOccurrenceById`.
   * - Se houver resposta, formata os dados com `formatInspectionSheetOccurrenceData` (incluindo o logo do cliente).
   * - Caso não haja dados, retorna um `Observable` nulo sem quebrar o fluxo.
   * - Oculta o spinner ao final do processo (independente de sucesso ou erro).
   *
   * @param occurrenceId - ID da ocorrência que será buscada.
   * @param clientId - ID do cliente, utilizado para buscar o logo da empresa.
   * @returns Observable com os dados formatados da ocorrência ou `null` se não houver retorno da API.
   */
  getInspectionSheetOccurrenceById(occurrenceId: string, clientId: string): Observable<any> {
    this.ngxSpinnerService.show();

    return this.inspectionSheetServiceApi.getInspectionSheetOccurrenceById(occurrenceId).pipe(
      switchMap((resp: any) => {
        if (!resp || resp?.status === 204 || !resp?.body) {
          return of(null);
        }

        // Se veio body normal
        return this.formatInspectionSheetOccurrenceData(resp, clientId);
      }),
      finalize(() => {
        this.ngxSpinnerService.hide();
      })
    );
  }

  /**
   * Formata os dados recebidos de uma ocorrência para exibição na tela.
   *
   * - Carrega o logo do cliente via `loadLogo`.
   * - Formata as datas de início e fim da ficha de inspeção.
   * - Converte o valor numérico de `response` e `occurrence_and_action_plan_status` em seus respectivos rótulos (labels).
   * - Formata a data de criação da ocorrência.
   * - Retorna um objeto com os dados originais da ocorrência, enriquecidos com os dados formatados e o logo do cliente.
   *
   * @param {any} dados - Dados brutos da ocorrência retornados pela API.
   * @param {string} clientId - Identificador do cliente, utilizado para buscar o logo.
   * @returns {Observable<any>} - Observable contendo os dados da ocorrência formatados para uso no front-end.
   */
  formatInspectionSheetOccurrenceData(dados: any, clientId: string): Observable<any> {
    return this.loadLogo(clientId).pipe(
      map((logo) => {
        const startDate = dados.inspection_sheet_start_date ? moment(dados.inspection_sheet_start_date).format('DD/MM/YYYY HH:mm:ss') : '';
        const endDate = dados.inspection_sheet_end_date ? moment(dados.inspection_sheet_end_date).format('DD/MM/YYYY HH:mm:ss') : '';
        return {
          ...dados,
          response: OccurrenceResponse.find((item) => item.value === dados.response)?.label || '',
          occurrence_and_action_plan_status:
            OccurrenceActionPlanDeadlineStatus.find((item) => item.value === dados.occurrence_and_action_plan_status)?.label || '',
          period: `${startDate} ${endDate}`,
          created_date: dados.created_date ? moment(dados.created_date).format('DD/MM/YYYY HH:mm:ss') : '',
          client: {
            logo
          }
        };
      })
    );
  }

  /**
   * Carrega o logo do cliente com base no `clientId` informado.
   *
   * - Consulta o endpoint via `dataService.getClientLogo`.
   * - Se o logo for retornado em base64, detecta o MIME type para montar o `data URI` correto.
   * - Garante que o retorno esteja no formato compatível com uso em `src` de imagens (`data:image/png;base64,...`).
   * - Se `clientId` estiver vazio, retorna um Observable nulo.
   *
   * @param {string} clientId - Identificador do cliente usado para buscar o logo.
   * @returns {Observable<{ content: string; name: string } | null>} - Observable contendo o logo em formato `data URI` e o nome do arquivo, ou `null` se não houver logo.
   */
  loadLogo(clientId: string): Observable<{ content: string; name: string } | null> {
    if (clientId != '') {
      return this.dataService.getClientLogo(clientId).pipe(
        map((logoData) => {
          if (logoData.logo && logoData.logo.base64) {
            if (logoData.logo.base64 && !logoData.logo.base64.startsWith('data:')) {
              const decode = fn.base64Extension(logoData.logo.base64.slice(0, 100));
              if (decode.mimeType) {
                return {
                  content: `data:${decode.mimeType};base64,${logoData.logo.base64}`,
                  name: logoData.logo.name
                };
              }
            }
          }
          return null;
        })
      );
    }
    return new Observable((observer) => {
      observer.next(null);
      observer.complete();
    });
  }

  /**
   * Atualiza a posição do marcador no mapa de ocorrência com base nas coordenadas
   * `northing` e `easting` da ocorrência informada.
   *
   * Após atualizar os dados no objeto `dataMapsOccurrence`, dispara o envio dessas
   * informações para o mapa chamando `sendDataMap`.
   *
   * @param dados - Objeto contendo as coordenadas da ocorrência (`northing` e `easting`).
   */
  callMapOccurrence(dados) {
    if (!dados || !dados.occurrence_and_action_plan_status) {
      return;
    }

    const formattedStatus = this.statusService.formatStatus(dados.occurrence_and_action_plan_status);
    let strokeColor = 'white';
    let fillColor = formattedStatus?.color ?? 'white';
    let svgMarker = {
      path: 'M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0',
      fillColor: fillColor,
      fillOpacity: 1.0,
      strokeWeight: 1.5,
      strokeColor: strokeColor,
      rotation: 0,
      scale: 0.3,
      anchor: new google.maps.Point(0, 0)
    };

    if (dados?.latitude_sirgas2000 && dados?.longitude_sirgas2000) {
      this.dataMapsOccurrence.center.lat = dados.latitude_sirgas2000;
      this.dataMapsOccurrence.center.lng = dados.longitude_sirgas2000;
      this.dataMapsOccurrence.markers[0].position.lat = dados.latitude_sirgas2000;
      this.dataMapsOccurrence.markers[0].position.lng = dados.longitude_sirgas2000;
      this.dataMapsOccurrence.markers[0]['icon'] = svgMarker;
      this.sendDataMap('markersMultiple', false, 'occurrence');
    }
  }

  /**
   * Envia dados atualizados para o componente de mapa específico.
   *
   * Atualmente implementado apenas para o mapa de ocorrência (`map == 'occurrence'`).
   *
   * @param option - Identificador da ação (ex: 'markersMultiple', 'polylinesMultiple').
   * @param clear - Indica se o mapa deve ser limpo antes da atualização (padrão: `true`).
   * @param map - Identificador do mapa a ser atualizado (ex: 'occurrence').
   */
  sendDataMap(option, clear = true, map = '') {
    if (map == 'occurrence') {
      this.mapOccurrence.setDataMap(this.dataMapsOccurrence, option, clear);
    }
  }

  /**
   * Abre um arquivo base64 em uma nova aba do navegador.
   *
   * Utiliza a função auxiliar `openInNewTab` da `fn`, combinada ao `DomSanitizer`
   * para garantir a segurança ao exibir o conteúdo.
   *
   * @param base64 - String em base64 do arquivo a ser aberto.
   */
  openFile(base64: any): void {
    fn.openInNewTab(base64, this.sanitizer);
  }

  /**
   * Navega até a visualização da ficha de inspeção especificada.
   *
   * Evita o comportamento padrão do clique (caso esteja vinculado a um `<a>`)
   * e redireciona para a rota de visualização da ficha de inspeção.
   *
   * @param inspectionSheetId - ID da ficha de inspeção a ser aberta.
   * @param $event - Evento do clique, utilizado para prevenir o comportamento padrão.
   */
  openInspectionSheet(inspectionSheetId, $event): void {
    $event.preventDefault(); // impede o comportamento padrão do link
    this.router.navigate([`inspections/inspection-sheet/${inspectionSheetId}/view`]);
  }

  /**
   * Processa as ocorrências anteriores e formata os dados para exibição.
   *
   * - Ordena pela data de criação (mais antiga primeiro)
   * - Formata anexos com base64 seguro para visualização
   * - Formata data de criação
   */
  processPreviousOccurrences(previous: any[]): any[] {
    return [...previous]
      .sort((a, b) => new Date(a.created_date).getTime() - new Date(b.created_date).getTime())
      .map((item) => {
        const attachments = (item.occurrence_attachments || []).map((att) => {
          let base64Value = att.file?.base64 || '';
          if (base64Value && !base64Value.startsWith('data:')) {
            const decode = fn.base64Extension(base64Value.slice(0, 100));
            if (decode.mimeType) {
              base64Value = `data:${decode.mimeType};base64,${base64Value}`;
            }
          }
          return {
            ...att,
            file: {
              ...att.file,
              base64: this.sanitizer.bypassSecurityTrustResourceUrl(base64Value)
            }
          };
        });

        return {
          ...item,
          occurrence_attachments: attachments,
          created_date: item.created_date ? moment(item.created_date).toDate() : null
        };
      });
  }

  /**
   * Navega até a visualização de uma ocorrência histórica.
   * @param occurrenceId ID da ocorrência.
   * @param $event Evento de clique para evitar redirecionamento padrão.
   */
  openOccurrence(occurrenceId: string, $event: Event): void {
    $event.preventDefault();
    this.router.navigate([`inspections/inspection-sheet/occurrence/${occurrenceId}/view`]);
  }
}
