<div class="list-content">
  <div class="alert alert-success mt-3" role="alert" *ngIf="message.status">
    {{ message.text }}
  </div>

  <div
    class="alert alert-warning mt-2"
    role="alert"
    *ngIf="messageReturn.status"
  >
    {{ messageReturn.text }}
  </div>

  <div class="row mt-2">
    <!-- Mensagens de erro -->
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <div class="row mt-1">
    <div class="col-md-12" *ngIf="!messageReturn.status">
      <label class="form-label"
        >O instrumento {{ instrumentIdentifier }} está vinculado à(s)
        seguinte(s) seção(ões):</label
      >
    </div>
    <div class="col-md-6">
      <!-- Tabela -->
      <app-table
        *ngIf="tableData.length > 0"
        [messageReturn]="messageReturn"
        [tableHeader]="tableHeader"
        [tableData]="tableData"
        [actionCustom]="actionCustom"
        [permissaoUsuario]="permissaoUsuario"
        (sendClickRowEvent)="clickRowEvent($event)"
      >
      </app-table>
    </div>

    <div class="col-md-12">
      <app-dxf-viewer [fileDxf]="fileDxf"></app-dxf-viewer>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12 mt-2 d-flex justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [click]="goBack.bind(this)"
      ></app-button>
    </div>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
