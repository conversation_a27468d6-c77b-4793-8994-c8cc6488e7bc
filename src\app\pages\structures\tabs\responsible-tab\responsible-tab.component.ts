import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MessageCadastro } from 'src/app/constants/message.constants';

import { ClientService } from 'src/app/services/api/client.service';
import { StructuresService } from 'src/app/services/api/structure.service';
import { ResponsibleService } from 'src/app/services/api/responsible.service';
import { RoleService } from 'src/app/services/api/role.service';
import { PositionService } from 'src/app/services/api/position.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-responsible-tab',
  templateUrl: './responsible-tab.component.html',
  styleUrls: ['./responsible-tab.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ResponsibleTabComponent implements OnInit {
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public permissaoUsuario: any = {};

  public formStructureResponsible: FormGroup = new FormGroup({
    name: new FormControl('', [Validators.required])
  });

  public formResponsible: FormGroup = new FormGroup({
    id: new FormControl(null),
    name: new FormControl('', [Validators.required]),
    email_address: new FormControl('', [Validators.required]),
    position: new FormControl('', [Validators.required]),
    role: new FormControl('', [Validators.required]),
    cpf: new FormControl(''),
    professional_record: new FormControl('')
  });

  public formRole: FormGroup = new FormGroup({
    name: new FormControl('', [Validators.required])
  });

  public formPosition: FormGroup = new FormGroup({
    name: new FormControl('', [Validators.required])
  });

  tableHeader: any = [
    {
      label: 'Nome',
      width: '100%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'E-mail',
      width: '200px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['email_address']
    },
    {
      label: 'Função',
      width: '140px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['function']
    },
    {
      label: 'Cargo',
      width: '140px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['position_name']
    },
    {
      label: 'CPF',
      width: '160px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['cpf']
    },
    {
      label: 'Registro Profissional',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['professional_record']
    },
    {
      label: 'Ações',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom']
    }
  ];

  public actionCustom: any = [
    {
      class: 'btn-logisoil-remove-item',
      icon: 'fa fa-thin fa-trash-can',
      label: '',
      title: 'Remover',
      type: 'true',
      option: 'remove'
    }
  ];

  public tableData: any = [];
  public labelActions: boolean = true;
  public selectedColumns = this.tableHeader;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public filterParams: any = {};

  public filter: any = {
    Name: '',
    EmailAdress: '',
    Role: '',
    Position: '',
    Cpf: '',
    ProfessionalRecord: ''
  };

  public clients: any = [];
  public roles: any = [];
  public positions: any = [];

  public responsible: any = {
    id: null,
    name: null,
    email_address: null,
    position: { id: null },
    role: { id: null },
    cpf: null,
    professional_record: null
  };

  public ctrlResponsible: boolean = false;
  public editResponsible: boolean = false;

  public messageResponsible: any = [{ text: '', status: false }];
  public messagesErrorResponsible: any = null;

  public messageRole: any = [{ text: '', status: false }];
  public messagesErrorRole: any = null;

  public messagePosition: any = [{ text: '', status: false }];
  public messagesErrorPosition: any = null;

  public responsibleRequest: any = {};
  public responsibleList: any = [];

  public roleRequest: any = {};
  public roleList: any = [];

  public positionRequest: any = {};
  public positionList: any = [];

  public client: any = {
    id: null,
    name: null
  };

  public role: any = {
    id: null,
    name: null
  };
  public ctrlRole: boolean = false;
  public editRole: boolean = false;

  public position: any = {
    id: null,
    name: null
  };
  public ctrlPosition: boolean = false;
  public editPosition: boolean = false;

  public ctrlVinculation: boolean = false;

  constructor(
    private clientService: ClientService,
    private structuresService: StructuresService,
    private responsibleService: ResponsibleService,
    private roleService: RoleService,
    private positionService: PositionService
  ) {}

  /**
   * Lifecycle hook executado após a criação do componente.
   * Carrega cliente, responsáveis e, se permitido, cargos e funções.
   * Valida o acesso ao formulário.
   */
  ngOnInit(): void {
    this.getClient();

    if (this.can('viewResponsibles')) {
      this.getResponsible();
    }

    if (this.can('editResponsibles')) {
      this.getRoles();
      this.getPositions();
    }

    this.validateAccess();
  }

  /**
   * Verifica se o usuário possui permissão para a ação especificada.
   *
   * @param action - Nome da permissão.
   * @returns `true` se permitido, senão `false`.
   */
  can(action: string): boolean {
    return this.permissaoUsuario?.[action] === true;
  }

  /**
   * Obtém a lista de responsáveis e preenche o formulário se um valor padrão for fornecido.
   *
   * @param defaultItem - (Opcional) Valor para setar no campo 'name'.
   */
  getResponsible(defaultItem: any = null) {
    this.responsibleService.getResponsibleList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.responsibleList = dados;
      if (defaultItem !== null) {
        this.formResponsible.get('name').setValue(defaultItem);
      }
    });
  }

  /**
   * Obtém a lista de clientes disponíveis.
   */
  getClient() {
    this.clientService.getClientsList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;
    });
  }

  /**
   * Obtém a lista de funções (roles) disponíveis.
   */
  getRoles() {
    this.roleService.getRolesList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.roleList = dados;
    });
  }

  /**
   * Obtém a lista de cargos (positions) disponíveis.
   */
  getPositions() {
    this.positionService.getPositionsList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.positionList = dados;
    });
  }

  /**
   * Busca um responsável pelo ID e popula o formulário para adição ou edição.
   *
   * @param responsibleId - ID do responsável.
   * @param type - Tipo de ação: 'add' ou 'edit'. Padrão 'add'.
   */
  getResponsibleById(responsibleId: string, type: string = 'add') {
    this.responsibleService.getResponsibleById(responsibleId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (type === 'add') {
        this.formStructureResponsible.get('name').setValue('');
        this.formatTableData('responsible', dados);
      }

      if (type == 'edit') {
        this.editResponsible = true;
        this.formResponsible.get('id').setValue(dados.id);
        this.formResponsible.get('name').setValue(dados.name);
        this.formResponsible.get('email_address').setValue(dados.email_address);
        this.formResponsible.get('position').setValue(dados.position.id);
        this.formResponsible.get('role').setValue(dados.role.id);
        this.formResponsible.get('cpf').setValue(dados.cpf);
        this.formResponsible.get('professional_record').setValue(dados.professional_record);
        this.ctrlResponsible = true;
      }
    });
  }

  /**
   * Envia os dados do responsável para cadastro via POST.
   * Exibe feedback e atualiza a lista ou seleciona o responsável se estiver vinculando.
   */
  registerResponsible() {
    this.responsibleService.postResponsible(this.responsibleRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageResponsible.text = MessageCadastro.SucessoCadastro;
        this.messageResponsible.status = true;
        this.formResponsible.reset();
        this.ctrlResponsible = false;

        setTimeout(() => {
          this.messageResponsible.status = false;
        }, 4000);

        this.getResponsible(dados);
        if (this.ctrlVinculation) {
          this.selectResponsible(dados);
          this.ctrlVinculation = false;
        }
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorResponsible = error.error;
          setTimeout(() => {
            this.messagesErrorResponsible = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Seleciona um responsável pelo ID e reseta o formulário.
   *
   * @param responsibleId - ID do responsável.
   */
  selectResponsible(responsibleId) {
    if (responsibleId != '') {
      this.getResponsibleById(responsibleId);
    }
    this.ctrlResponsible = false;
    this.resetForm('responsible');
  }

  /**
   * Envia os dados atualizados do responsável via PUT.
   * Atualiza visualmente a tabela e exibe feedback de sucesso ou erro.
   */
  editResponsibleForm() {
    this.responsibleService.putResponsible(this.responsibleRequest.id, this.responsibleRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageResponsible.text = MessageCadastro.EdicaoCadastro;
        this.messageResponsible.status = true;
        this.formResponsible.reset();
        this.checkUpdateTable(dados);

        setTimeout(() => {
          this.messageResponsible.text = '';
          this.messageResponsible.status = false;
        }, 4000);

        this.getResponsible(dados);
        this.ctrlResponsible = false;

        if (this.ctrlVinculation) {
          this.selectResponsible(dados);
          this.ctrlVinculation = false;
        }
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorResponsible = error.error;
          setTimeout(() => {
            this.messagesErrorResponsible = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Envia os dados de uma nova função via POST e atualiza o formulário.
   */
  registerRole() {
    this.roleService.postRoles(this.roleRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageRole.text = MessageCadastro.SucessoCadastro;
        this.messageRole.status = true;
        this.formRole.reset();
        this.ctrlRole = false;

        setTimeout(() => {
          this.messageRole.status = false;
        }, 4000);

        this.getRoles();
        this.formResponsible.get('role').setValue(dados);
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorRole = error.error;
          setTimeout(() => {
            this.messagesErrorRole = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Envia os dados de um novo cargo via POST e atualiza o formulário.
   */
  registerPosition() {
    this.positionService.postPositions(this.positionRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messagePosition.text = MessageCadastro.SucessoCadastro;
        this.messagePosition.status = true;
        this.formPosition.reset();
        this.ctrlPosition = false;
        setTimeout(() => {
          this.messagePosition.status = false;
        }, 4000);
        this.getPositions();
        this.formResponsible.get('position').setValue(dados);
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorPosition = error.error;
          setTimeout(() => {
            this.messagesErrorPosition = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Valida e formata os dados com base no tipo informado.
   *
   * @param type - Tipo de dado: 'responsible', 'role' ou 'position'.
   */
  validate(type: string = '') {
    this.formatData(type);
  }

  /**
   * Formata os dados dos formulários e chama o método correspondente de cadastro ou edição.
   *
   * @param type - Tipo: 'responsible', 'role', 'position'.
   */
  formatData(type: string = '') {
    if (type === 'responsible') {
      this.responsibleRequest = this.responsible;
      this.responsibleRequest.id = this.formResponsible.get('id').value;
      this.responsibleRequest.name = this.formResponsible.get('name').value;
      this.responsibleRequest.email_address = this.formResponsible.get('email_address').value;
      this.responsibleRequest.position.id = this.formResponsible.get('position').value;
      this.responsibleRequest.role.id = this.formResponsible.get('role').value;
      this.responsibleRequest.cpf = this.formResponsible.get('cpf').value;
      this.responsibleRequest.professional_record = this.formResponsible.get('professional_record').value;

      if (!this.editResponsible) {
        delete this.responsibleRequest.id;
        this.registerResponsible();
      } else {
        this.editResponsibleForm();
      }
    }

    if (type === 'role') {
      this.roleRequest = this.role;
      this.roleRequest.name = this.formRole.get('name').value;

      if (!this.editRole) {
        delete this.roleRequest.id;
        this.registerRole();
      }
    }

    if (type === 'position') {
      this.positionRequest = this.position;
      this.positionRequest.name = this.formPosition.get('name').value;

      if (!this.editPosition) {
        delete this.positionRequest.id;
        this.registerPosition();
      }
    }
  }

  /**
   * Adiciona um novo responsável à tabela, evitando duplicidade.
   *
   * @param option - Apenas 'responsible' neste contexto.
   * @param data - Objeto com os dados do responsável.
   */
  formatTableData(option: string, data: any = []) {
    if (option == 'responsible') {
      let idx = fn.findIndexInArrayofObject(this.tableData, 'id', data.id);
      if (idx === -1) {
        data.function = data.role.name;
        data.position_name = data.position.name;
        this.tableData.push(data);
      }
    }
  }

  /**
   * Atualiza a tabela local com os dados do responsável editado.
   *
   * @param id - ID do responsável atualizado.
   */
  checkUpdateTable(id: string): void {
    let idx = fn.findIndexInArrayofObject(this.tableData, 'id', id);
    if (idx !== -1) {
      this.tableData[idx].id = this.responsibleRequest.id;
      this.tableData[idx].name = this.responsibleRequest.name;
      this.tableData[idx].email_address = this.responsibleRequest.email_address;
      this.tableData[idx].cpf = this.responsibleRequest.cpf;
      this.tableData[idx].professional_record = this.responsibleRequest.professional_record;

      let idxRole = fn.findIndexInArrayofObject(this.roleList, 'id', this.responsibleRequest.role.id);

      if (idxRole !== -1) {
        this.tableData[idx].function = this.roleList[idxRole].name;
      }

      let idxPosition = fn.findIndexInArrayofObject(this.positionList, 'id', this.responsibleRequest.position.id);

      if (idxPosition !== -1) {
        this.tableData[idx].position_name = this.positionList[idxPosition].name;
      }
    }

    this.responsibleRequest = [];
  }

  /**
   * Reseta o formulário de responsável, função ou cargo.
   *
   * @param type - Tipo do formulário: 'responsible', 'role' ou 'position'.
   * @param option - Se `true`, mantém o controle de abertura ativado.
   */
  resetForm(type: string = '', option: boolean = false) {
    if (type === 'responsible') {
      this.ctrlResponsible = false;
      if (option) {
        this.ctrlResponsible = true;
      }
      this.editResponsible = false;
      this.formResponsible.reset();
      this.formResponsible.get('id').setValue(null);
      this.formResponsible.get('name').setValue('');
      this.formResponsible.get('email_address').setValue('');
      this.formResponsible.get('position').setValue('');
      this.formResponsible.get('role').setValue('');
      this.formResponsible.get('cpf').setValue('');
      this.formResponsible.get('professional_record').setValue('');
    }

    if (type === 'role') {
      this.ctrlRole = false;
      if (option) {
        this.ctrlRole = true;
      }
    }

    if (type === 'position') {
      this.ctrlPosition = false;
      if (option) {
        this.ctrlPosition = true;
      }
    }
  }

  /**
   * Trata eventos de clique em linhas da tabela (ex: remoção).
   *
   * @param $event - Evento com ação e ID.
   */
  clickRowEvent($event: any = null) {
    switch ($event.action) {
      case 'remove':
        let idx = fn.findIndexInArrayofObject(this.tableData, 'id', $event.id);
        if (idx !== -1) {
          this.tableData.splice(idx, 1);
        }
        break;
      default:
        break;
    }
  }

  /**
   * Valida o acesso ao formulário com base no perfil e modo visualização.
   *
   * @param role - (Opcional) Nível de permissão.
   */
  validateAccess(role: number = 0): any {
    if (this.view) {
      this.formStructureResponsible.disable();
      this.formResponsible.disable();
    }
  }

  /**
   * Popula a tabela de responsáveis com os dados recebidos.
   * Remove a última coluna do header se estiver em modo visualização.
   *
   * @param dataTab - Objeto com a propriedade `responsibles`.
   */
  setData(dataTab: any = []) {
    if (this.view) {
      this.tableHeader.pop();
    }
    dataTab.responsibles.map((responsible: any) => {
      this.formatTableData('responsible', responsible);
    });
  }

  /**
   * Retorna os dados dos responsáveis formatados para envio.
   *
   * @returns Objeto contendo a lista `responsibles`.
   */
  getData() {
    let dataTab = {};
    dataTab['responsibles'] = this.tableData.map((item: any) => {
      delete item.position_name;
      return item;
    });

    return dataTab;
  }
}
