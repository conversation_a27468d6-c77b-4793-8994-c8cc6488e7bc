import { Component, OnInit, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-history-tabs-reading',
  templateUrl: './history-tabs-reading.component.html',
  styleUrls: ['./history-tabs-reading.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HistoryTabsReadingComponent implements OnInit {
  public readingTabConfig: any = { styleColor: false, active: true };

  constructor() {}

  ngOnInit(): void {}

  selectTab(option: any = '') {
    switch (option) {
      case 'reading':
        this.readingTabConfig.active = true;
        break;
      default:
        break;
    }
  }
}
