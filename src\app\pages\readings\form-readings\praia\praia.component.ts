import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';

import { fieldsReading } from 'src/app/constants/readings.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-praia',
  templateUrl: './praia.component.html',
  styleUrls: ['./praia.component.scss']
})
export class PraiaComponent implements OnInit, OnChanges {
  @Input() public sectionsList: any = [];
  @Input() public index: number = null;
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public units: any = null;
  @Input() public typeInstrument: any = null;
  @Input() public datetime: any = null;

  @Output() public setSection = new EventEmitter();

  public formReading: FormGroup = new FormGroup({
    section: new FormControl('', [Validators.required]),
    date: new FormControl({ value: '', disabled: true }, [Validators.required]),
    length: new FormControl({ value: '', disabled: true }, [Validators.required]),
    //Para edicao
    id: new FormControl({ value: '', disabled: true })
  });

  public controls: any = null;

  public fieldsReading = fieldsReading;

  public message: any = [{ text: '', status: false }];
  public messagesError: any = null;

  public func = fn;

  constructor(private formService: FormService) {}

  ngOnInit(): void {
    this.controls = this.formReading.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReading.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }

    if (changes.datetime && changes.datetime.currentValue != null) {
      this.controls['date'].setValue(this.datetime);
    }
  }

  changeSection(section) {
    this.setSection.emit(section);
  }

  splitData($dados) {
    if (!this.edit && !this.view) {
      this.controls['section'].enable();
    } else {
      this.controls['section'].disable();
    }

    this.formService.toggleFormList(this.formReading, this.fieldsReading[this.typeInstrument.id]);

    this.formReading.controls['section'].setValue($dados.section.id);

    if ($dados.edit) {
      this.controls['id'].setValue($dados.edit.id);

      let date = $dados.edit.date.split('.');
      this.controls['date'].setValue(date[0]);

      this.controls['length'].setValue($dados.edit.length);
    }

    if (this.view) {
      this.formReading.disable();
    }
  }
}
