import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';

import { InsertInspectionSheet } from 'src/app/constants/inspections.constants';

import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';

@Component({
  selector: 'app-modal-insert-inspection-sheet',
  templateUrl: './modal-insert-inspection-sheet.component.html',
  styleUrls: ['./modal-insert-inspection-sheet.component.scss']
})
export class ModalInsertInspectionSheetComponent implements OnInit {
  @Input() public structureId: string = '';
  @Output() public sendClickEvent = new EventEmitter();
  @ViewChild('modalInsertInspectionSheet') modalInsertInspectionSheet: ElementRef;

  public modalRef: any = NgbModalRef;

  public insertInspectionSheet = InsertInspectionSheet;
  public selectedOption: any = 3;

  public messagesError: any = null;

  constructor(private inspectionSheetServiceApi: InspectionSheetServiceApi, private modalService: NgbModal, private router: Router) {}

  ngOnInit(): void {}

  openModal() {
    this.modalRef = this.modalService.open(this.modalInsertInspectionSheet);
  }

  clickRowEvent(action: any = null) {
    this.sendClickEvent.emit(action);
  }

  create(inspectionSheetId: string) {
    if (this.selectedOption !== '') {
      // Feche o modal antes de redirecionar
      if (this.modalRef) {
        this.modalRef.close();
      }

      switch (parseInt(this.selectedOption)) {
        case 1:
          this.router.navigate(['inspections/inspection-sheet/risr'], { queryParams: { inspectionSheetId: inspectionSheetId } });
          break;
        case 2:
          this.router.navigate(['inspections/inspection-sheet/eor'], { queryParams: { inspectionSheetId: inspectionSheetId } });
          break;
        case 3:
          this.router.navigate(['inspections/inspection-sheet/fie'], { queryParams: { inspectionSheetId: inspectionSheetId } });
          break;
        case 4:
          this.router.navigate(['inspections/inspection-sheet/fir'], { queryParams: { inspectionSheetId: inspectionSheetId } });
          break;
      }
    }
  }

  InsertInspectionSheet() {
    const params = {
      structure_id: this.structureId,
      type: this.selectedOption
    };

    this.inspectionSheetServiceApi.postInspectionSheet(params).subscribe(
      (resp) => {
        const inspectionSheetId: any = resp;
        this.create(inspectionSheetId);
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
      }
    );
  }
}
