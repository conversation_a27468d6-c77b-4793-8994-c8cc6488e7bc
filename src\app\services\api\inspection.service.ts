import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class InspectionService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  // Cadastro de Área
  postAreas(params: any) {
    const url = '/areas';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna as áreas para uso em filtro
  getAreas(params: any) {
    const url = '/areas/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getAreasList(params: any = {}) {
    const url = '/areas';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca a área por ID
  getAreasById(id: string) {
    const url = `/areas/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putAreas(id: string, params: any) {
    const url = `/areas/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  // Cadastro de Aspectos
  postAspects(params: any) {
    const url = '/aspects';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna os aspectos para uso em filtro
  getAspects(params: any) {
    const url = '/aspects/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getAspectsList(params: any = {}) {
    const url = '/aspects';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca o aspecto por ID
  getAspectsById(id: string) {
    const url = `/aspects/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putAspects(id: string, params: any) {
    const url = `/aspects/${id}`;
    return this.api.put<any>(url, params, 'client');
  }
}
