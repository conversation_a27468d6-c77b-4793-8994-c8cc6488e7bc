.container {
  display: grid;
  grid-template-columns: 40px 2.3fr;
  grid-template-rows: 2fr 0.1fr;
  gap: 0px 0px;
  grid-template-areas: 'label-y chart' 'label-y label-x';
  width: 100%;
  height: 100%;
}

.label-x {
  grid-area: label-x;
}

.chart {
  grid-area: chart;
}

.label-y {
  grid-area: label-y;
  transform-origin: 0 0;
  transform: rotate(270deg);
  white-space: nowrap;
  align-self: end;
}
