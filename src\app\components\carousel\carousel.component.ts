import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-carousel',
  templateUrl: './carousel.component.html',
  styleUrls: ['./carousel.component.scss']
})
export class CarouselComponent implements OnInit {
  @Input() images: any = [];
  @Input() showCarousel: boolean = true;
  @Input() selectedImage: number = null;

  @Output() public sendClose = new EventEmitter();

  constructor() {}

  ngOnInit(): void {}

  closeCarousel() {
    this.sendClose.emit();
  }
}
