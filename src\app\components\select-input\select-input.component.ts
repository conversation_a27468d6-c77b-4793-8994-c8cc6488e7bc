import { Component, Input, OnChanges, OnInit, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { FormControl, FormGroup, Validators, AbstractControl } from '@angular/forms';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-select-input',
  templateUrl: './select-input.component.html',
  styleUrls: ['./select-input.component.scss']
})
export class SelectInputComponent implements OnInit, OnChanges {
  @Input() formControlItem!: AbstractControl;
  @Input() selectOptions: any = [];
  @Input() idField: any = null;
  @Input() textField: any = null;
  @Input() options: any = null;
  @Input() view: boolean = false;

  @Output() public onAdd = new EventEmitter();
  @Output() public onEdit = new EventEmitter();

  public formSelect: FormGroup = new FormGroup({
    itemSelect: new FormControl(),
    itemInput: new FormControl('')
  });

  public controls: any = null;
  public ctrlItem: boolean = false;
  public action: string = '';
  public selectedItem: number = null;

  constructor() {}

  ngOnInit(): void {
    this.controls = this.formSelect.controls;
    this.controls['itemSelect'] = this.formControlItem;
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.controls = this.formSelect.controls;
    this.controls['itemSelect'] = this.formControlItem;
  }

  addItem() {
    if (this.controls['itemSelect'].enabled) {
      this.ctrlItem = true;
      this.action = 'add';
    }
  }

  updateItem() {
    this.ctrlItem = true;
    this.action = 'edit';
    const idItem = fn.findIndexByValue(this.selectOptions, this.controls['itemSelect'].value, this.idField);
    this.selectedItem = this.selectOptions[idItem];
    this.controls['itemInput'].setValue(this.selectedItem[this.textField]);
  }

  saveItem(index: number = null) {
    if (this.action == 'add') {
      this.onAdd.emit(this.controls['itemInput'].value);
      this.controls['itemInput'].setValue('');
    } else if (this.action == 'edit') {
      let item = {};
      item[this.idField] = this.selectedItem[this.idField];
      item[this.textField] = this.controls['itemInput'].value;
      this.onEdit.emit(item);
      this.controls['itemInput'].setValue('');
    }
    this.ctrlItem = false;
  }

  cancelItem() {
    this.ctrlItem = false;
    this.controls['itemInput'].setValue('');
    this.selectedItem = null;
  }
}
