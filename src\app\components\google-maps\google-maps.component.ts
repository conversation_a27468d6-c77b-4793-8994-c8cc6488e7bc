import {
  AfterViewInit,
  ApplicationRef,
  Component,
  ComponentFactoryResolver,
  ComponentRef,
  ElementRef,
  Injector,
  Input,
  NgZone,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import { ButtonComponent } from '@components/button/button.component';
import { MapInfoComponent } from '@components/map-info/map-info.component';
import { Subscription } from 'rxjs';
import { SharedService } from 'src/app/services/shared.service';

import * as toGeoJSON from '@tmcw/togeojson';
import { DOMParser } from '@xmldom/xmldom';

@Component({
  selector: 'app-google-maps',
  templateUrl: './google-maps.component.html',
  styleUrls: ['./google-maps.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class GoogleMapsComponent implements AfterViewInit {
  @Input() mapType: 'default' | 'selection' = 'default';
  @Input() id: any = 'map'; //Valor padrão para a referência local dinâmica
  @Input() height: string | null = null;
  @Input() discount: number = 0;
  @Input() isFlex: boolean = false;

  @ViewChild('mapWrapper', { static: false }) mapElement!: ElementRef;

  public center: google.maps.LatLng;
  public newMap: google.maps.Map;

  public markers: google.maps.Marker[] = [];
  public polylines: google.maps.Polyline[] = [];
  public kmlLayers: google.maps.KmlLayer[] = [];
  public geoDatas: google.maps.Data[] = [];
  public polygons: any[] = [];
  public markerImage: google.maps.MarkerShape;

  public markersAttachments: google.maps.Marker[] = [];
  public infoWindowsAttachments: google.maps.InfoWindow[] = [];

  public infoWindow: google.maps.InfoWindow = null;
  public infoWindows: google.maps.InfoWindow[] = [];
  public infoWindowsMarker: google.maps.InfoWindow[] = [];
  public infoWindowsPolyline: google.maps.InfoWindow[] = [];
  public infoWindowsKmlLayers: google.maps.InfoWindow[] = [];
  public infoWindowsGeoDatas: google.maps.InfoWindow[] = [];
  public infoWindowsPolygons: google.maps.InfoWindow[] = [];

  public document: any = null;

  public compRefButton: ComponentRef<ButtonComponent>;
  public compRefMapInfo: ComponentRef<MapInfoComponent>;

  constructor(
    public appRef: ApplicationRef,
    private elRef: ElementRef,
    private injector: Injector,
    public resolver: ComponentFactoryResolver,
    public sharedService: SharedService,
    private zone: NgZone
  ) {}

  /**
   * Método do ciclo de vida Angular chamado após a inicialização da view.
   * Inicializa o mapa e adiciona controles.
   */
  ngAfterViewInit() {
    this.initMap();
  }

  initMap() {
    setTimeout(() => {
      this.initializeMap();
      this.addCenterControl();

      if (this.mapType == 'default') {
        this.markerMap();
      } else {
        this.markOnClick();
      }
    }, 50); // Pequeno atraso para garantir que o layout foi completamente renderizado

    if (!this.height) {
      const gridItem = this.elRef.nativeElement.closest('.grid-item') as HTMLElement;
      if (gridItem) {
        const gridItemHeight = gridItem.clientHeight;
        const mapContent = this.elRef.nativeElement.querySelector('.map-content') as HTMLElement;
        if (mapContent?.style) {
          mapContent.style.height = `${gridItemHeight - this.discount}px`;
        }
      }
    }
  }

  /**
   * Define os dados do mapa com base nos parâmetros fornecidos.
   *
   * @param dataMap Os dados do mapa a serem definidos.
   * @param option A opção para definir o tipo de dados do mapa.
   * @param clear Indica se os dados existentes devem ser limpos.
   * @param center Indica se o mapa deve ser centralizado nos novos dados.
   */
  // setDataMap(dataMap: any, option: string = '', clear = true, center = true) {
  //   this.setDataMapSrv(dataMap, option, clear, center);
  // }
  setDataMap(dataMap: any, option: string = '', clear = true, center = true) {
    if (!this.newMap) {
      this.initializeMap(true, { dataMap: dataMap, option: option, clear: clear, center: center });
    } else {
      this.setDataMapSrv(dataMap, option, clear, center);
    }
  }

  /**
   * Abre uma janela de informação (InfoWindow) para os marcadores especificados.
   *
   * @param arrayMarkersId Array de IDs dos marcadores para os quais abrir a InfoWindow.
   * @param prefix Prefixo para identificar os marcadores.
   * @param close Indica se as janelas de informações existentes devem ser fechadas antes de abrir as novas.
   */
  openInfoWindowFromMarkerId(arrayMarkersId, prefix: any = '', close: boolean = true) {
    this.openInfoWindowFromMarkerIdSrv(arrayMarkersId, prefix, close);
  }

  /**
   * Limpa todos os elementos do mapa, incluindo marcadores, polilinhas, camadas KML, dados GeoJSON e polígonos.
   */
  clearMap() {
    this.clearMapSrv();
  }

  /**
   * Inicializa o mapa do Google Maps com as opções padrão.
   */
  initializeMap(init: boolean = false, $params: any = {}) {
    const LatLng = new google.maps.LatLng(-17.930178, -43.7908453);
    this.center = LatLng;

    const mapOptions: google.maps.MapOptions = {
      center: LatLng,
      zoom: 16,
      fullscreenControl: true,
      mapTypeControl: true,
      streetViewControl: false,
      scrollwheel: true,
      mapTypeId: 'satellite',
      mapId: this.mapElement.nativeElement.id
    };

    this.newMap = new google.maps.Map(this.mapElement.nativeElement, mapOptions);

    this.newMap.addListener('zoom_changed', () => {
      this.eventClick({
        type: 'mapZoomChanged',
        data: this.newMap.getZoom(),
        params: { mapId: this.mapElement.nativeElement.id }
      });
    });

    // Adicione um ouvinte de evento para o evento tilesloaded
    this.newMap.addListener('idle', () => {
      this.eventClick({
        type: 'idle',
        data: null,
        params: { mapId: this.mapElement.nativeElement.id }
      });
    });

    if (init) {
      this.setDataMapSrv($params.dataMap, $params.option, $params.clear, $params.center);
    }
  }

  /**
   * Adiciona um controle personalizado ao centro do mapa.
   */
  addCenterControl() {
    const centerControlDiv = document.createElement('div');
    const centerControl = this.createCenterControl(this.newMap);
    centerControlDiv.appendChild(centerControl);

    this.newMap.controls[google.maps.ControlPosition.TOP_CENTER].push(centerControlDiv);
  }

  /**
   * Adiciona um marcador ao centro do mapa.
   */
  markerMap() {
    //Markers
    const marker = new google.maps.Marker({
      position: this.center,
      title: '',
      map: this.newMap
    });

    this.markers.push(marker);
  }

  /**
   * Permite marcar a posição no mapa ao clicar.
   * @param center A posição inicial para o marcador.
   */
  markOnClick(center = null) {
    this.infoWindow = new google.maps.InfoWindow({
      content: 'Selecione as coordenadas do epicentro no mapa',
      position: center
    });
    this.infoWindow.open(this.newMap);

    this.newMap.addListener('click', ({ latLng }) => {
      this.showCoordinates(latLng);
      this.eventClick({ type: 'click', data: latLng });
    });
  }

  /**
   * Mostra as coordenadas de uma posição clicada no mapa em uma janela de informação.
   * @param latLng As coordenadas da posição clicada.
   */
  showCoordinates(latLng: google.maps.LatLng) {
    // Close the current InfoWindow.
    this.infoWindow.close();

    //Create a new InfoWindow
    this.infoWindow = new google.maps.InfoWindow({
      position: latLng
    });
    this.infoWindow.setContent(JSON.stringify(latLng.toJSON(), null, 2));
    this.infoWindow.open(this.newMap);
  }

  /**
   * Cria um controle personalizado para centralizar o mapa.
   * @param map O mapa no qual o controle será adicionado.
   * @returns O botão de controle para centralizar o mapa.
   */
  createCenterControl(map) {
    const controlButton = document.createElement('button');

    // Set CSS for the control.
    controlButton.style.backgroundColor = '#fff';
    controlButton.style.border = '2px solid #fff';
    controlButton.style.borderRadius = '3px';
    controlButton.style.boxShadow = '0 2px 6px rgba(0,0,0,.3)';
    controlButton.style.color = 'rgb(25,25,25)';
    controlButton.style.cursor = 'pointer';
    controlButton.style.fontFamily = 'Roboto,Arial,sans-serif';
    controlButton.style.fontSize = '16px';
    controlButton.style.lineHeight = '38px';
    controlButton.style.margin = '8px 0 22px';
    controlButton.style.padding = '0 5px';
    controlButton.style.textAlign = 'center';

    controlButton.textContent = 'Centralizar';
    controlButton.title = 'Clique para recentralizar o mapa';
    controlButton.type = 'button';

    // Setup the click event listeners: simply set the map.
    controlButton.addEventListener('click', () => {
      map.setCenter(this.center);
    });

    return controlButton;
  }

  /**
   * Define os dados do mapa com base nos parâmetros fornecidos pelo serviço.
   *
   * @param dataMap Os dados do mapa a serem definidos.
   * @param option A opção para definir o tipo de dados do mapa.
   * @param clear Indica se os dados existentes devem ser limpos.
   * @param center Indica se o mapa deve ser centralizado nos novos dados.
   */
  setDataMapSrv(dataMap: any, option: string = '', clear = true, center = true) {
    if (center && this.newMap != undefined) {
      const LatLng = new google.maps.LatLng(dataMap.center.lat, dataMap.center.lng);
      this.center = LatLng;
      this.newMap.setCenter(LatLng);
      this.newMap.setZoom(dataMap.zoom);
    }

    switch (option) {
      case 'markers':
        this.newMarker(dataMap);
        break;
      case 'markersMultiple':
        this.newMarkerMultiple(dataMap);
        break;
      case 'polylines':
        this.newPolylines(dataMap, clear);
        break;
      case 'polylinesMultiple':
        this.newPolylinesMultiple(dataMap, clear);
        break;
      case 'clearPolylinesMultiple':
      case 'clearPolylines':
        this.clearPolylines();
        this.clearinfoWindowsPolyline();
        break;
      case 'clearMarkers':
        this.clearMarkers();
        break;
      case 'clearKmlsMultiple':
        this.clearKmls();
        break;
      case 'clearGeoDataMultiple':
        this.clearGeoDatas();
        break;
      case 'selection':
        this.newMarker(dataMap, true, true);
        break;
      case 'zoom':
        this.newMap.setZoom(dataMap.zoom);
        break;
      case 'kmls':
        this.newKmlLayer(dataMap, clear);
        break;
      case 'geoData':
        this.newGeoData(dataMap, clear);
        break;
      case 'polygonsMultiple':
        this.newPolygonsMultiple(dataMap, clear);
        break;
      // 💡 Você pode adicionar mais opções aqui futuramente
      default:
        // Aviso removido para não poluir o console
        break;
    }
  }

  /**
   * Adiciona um novo marcador ao mapa.
   *
   * @param dataMap Os dados do mapa que contêm os detalhes do marcador.
   * @param clear Indica se os marcadores existentes devem ser limpos.
   * @param selection Indica se o modo de seleção deve ser ativado.
   */
  newMarker(dataMap, clear = true, selection = false) {
    // Markers
    this.clearMarkers();
    const marker = new google.maps.Marker({
      position: this.center,
      title: dataMap.markers[0].title,
      map: this.newMap
    });
    this.markers.push(marker);
    if (selection) {
      this.markOnClick(this.center);
    }
  }

  /**
   * Adiciona múltiplos marcadores ao mapa.
   *
   * @param dataMap Os dados do mapa que contêm os detalhes dos marcadores.
   * @param clear Indica se os marcadores existentes devem ser limpos.
   */
  newMarkerMultiple(dataMap, clear = true) {
    this.markers.forEach((markerItem, index) => {
      this.markers[index].setMap(null);
    });

    this.markers = [];
    this.infoWindowsMarker = [];

    dataMap.markers.forEach((markerItem, index) => {
      if (index == 0 && dataMap.showPin === false) {
        dataMap.markers[index].visible = false;
      }

      let marker = new google.maps.Marker(dataMap.markers[index]);

      if (markerItem.hasOwnProperty('infoWindowMarker')) {
        let infoWindowMarker = new google.maps.InfoWindow({
          content: markerItem.infoWindowMarker.content,
          ariaLabel: markerItem.infoWindowMarker.ariaLabel
        });

        //Verifica se precisa adiconar um component no conteudo do infoWindow
        let extraParams = {
          id: markerItem.infoWindowMarker.id,
          data: markerItem.infoWindowMarker.data,
          classGroup: markerItem.infoWindowMarker.classGroup ? markerItem.infoWindowMarker.classGroup : '',
          classTitle: markerItem.infoWindowMarker.classTitle ? markerItem.infoWindowMarker.classTitle : '',
          subtitle: markerItem.infoWindowMarker?.subtitle ?? null
        };

        this.zone.run(() => this.setComponent(marker, infoWindowMarker, markerItem.infoWindowMarker.contentConfig, extraParams /*$event*/));
        marker.addListener('click', ($event) => {
          infoWindowMarker.open({
            anchor: marker
          });
          this.eventClick({ type: 'mapInstrumentInfoWindow', data: extraParams });
        });

        infoWindowMarker.addListener('closeclick', ($event) => {
          this.eventClick({ type: 'mapInstrumentInfoWindowClose', data: extraParams });
        });
        this.infoWindowsMarker.push(infoWindowMarker);
      }

      this.markers.push(marker);
      this.markers[this.markers.length - 1].setMap(this.newMap);
    });
    this.eventClick({ type: 'loadMarkers', data: null, params: { mapId: this.mapElement.nativeElement.id } });
  }

  addAttachmentMarkers(attachmentsData: any[]): void {
    attachmentsData.forEach((item) => {
      const { occurrence_attachments, search_identifier } = item;

      occurrence_attachments.forEach((anexo) => {
        const lat = anexo.latitude_sirgas2000;
        const lng = anexo.longitude_sirgas2000;

        if (lat && lng) {
          const marker = new google.maps.Marker({
            position: { lat, lng },
            title: anexo.file.name,
            icon: {
              url: 'assets/ico/ico-maps/picture.png',
              scaledSize: new google.maps.Size(28, 28) // ou 32x32, se preferir maior
            },
            map: this.newMap
          });

          const imageBase64 = anexo.file.base64.startsWith('data:') ? anexo.file.base64 : `data:image/jpeg;base64,${anexo.file.base64}`;

          const infoWindow = new google.maps.InfoWindow({
            content: `
              <div style="text-align:center">
                <strong>${anexo.file.name}</strong><br />
                <img src="${imageBase64}" alt="${anexo.file.name}" style="max-width:200px; max-height:200px; margin-top:6px;" />
              </div>
            `
          });

          marker.addListener('click', () => {
            this.infoWindowsAttachments.forEach((iw) => iw.close());
            infoWindow.open(this.newMap, marker);
          });

          this.markersAttachments.push(marker);
          this.infoWindowsAttachments.push(infoWindow);
        }
      });
    });
  }

  /**
   * Adiciona uma nova polilinha ao mapa.
   *
   * @param dataMap Os dados do mapa que contêm os detalhes da polilinha.
   * @param clear Indica se as polilinhas existentes devem ser limpas.
   */
  newPolylines(dataMap, clear = true) {
    if (this.polylines.length > 0 && clear) {
      this.polylines[0].setMap(null);
      this.polylines.shift();
    }
    let polyline = new google.maps.Polyline(dataMap.polylines[0]);
    this.polylines.push(polyline);
    this.polylines[this.polylines.length - 1].setMap(this.newMap);
  }

  /**
   * Adiciona múltiplas polilinhas ao mapa.
   *
   * @param dataMap Os dados do mapa que contêm os detalhes das polilinhas.
   * @param clear Indica se as polilinhas existentes devem ser limpas.
   */
  newPolylinesMultiple(dataMap, clear = true) {
    this.polylines.forEach((polylineItem, index) => {
      this.polylines[index].setMap(null);
    });

    this.infoWindows.forEach((infoWindowItem, index) => {
      this.infoWindows[index].close();
      this.infoWindows[index] = null;
    });

    this.infoWindowsPolyline.forEach((infoWindowItem, index) => {
      this.infoWindowsPolyline[index].close();
      this.infoWindowsPolyline[index] = null;
    });

    this.polylines = [];
    this.infoWindows = [];
    this.infoWindowsPolyline = [];

    dataMap.polylines.forEach((polylineItem, index) => {
      let polyline = new google.maps.Polyline(dataMap.polylines[index]);

      if (!polylineItem.hasOwnProperty('infoWindowPolyline')) {
        let infoWindow = new google.maps.InfoWindow({
          content: dataMap.polylines[index].name,
          position: dataMap.polylines[index].path[0] //coordenada montante upstream
        });
        this.infoWindows.push(infoWindow);
        this.infoWindows[this.infoWindows.length - 1].open(this.newMap);
      } else {
        //if (polylineItem.infoWindowPolyline.length > 0) {
        let infoWindowPolyline = new google.maps.InfoWindow({
          content: polylineItem.infoWindowPolyline.content,
          position: dataMap.polylines[index].path[0]
        });
        let extraParams = {
          id: polylineItem.infoWindowPolyline.id,
          data: polylineItem.infoWindowPolyline.data,
          classGroup: polylineItem.infoWindowPolyline.classGroup ? polylineItem.infoWindowPolyline.classGroup : '',
          classTitle: polylineItem.infoWindowPolyline.classTitle ? polylineItem.infoWindowPolyline.classTitle : ''
        };
        this.zone.run(() => this.setComponent(polyline, infoWindowPolyline, polylineItem.infoWindowPolyline.contentConfig, extraParams));

        polyline.addListener('click', ($event) => {
          infoWindowPolyline.open({
            anchor: polyline
          });
          this.eventClick({ type: 'mapPolylineInfoWindow', data: extraParams });
        });

        infoWindowPolyline.addListener('closeclick', ($event) => {
          this.eventClick({ type: 'mapPolylineInfoWindowClose', data: extraParams });
        });

        // Faz a infoWindow vir aberta (não deletar)
        // this.infoWindows[this.infoWindows.length - 1].open(this.newMap);

        this.infoWindowsPolyline.push(infoWindowPolyline);
        if (dataMap.showSectionInfoWindow) {
          this.infoWindowsPolyline[this.infoWindowsPolyline.length - 1].open(this.newMap);
        }
        //}
      }

      this.polylines.push(polyline);
      this.polylines[this.polylines.length - 1].setMap(this.newMap);
    });
  }

  /**
   * Adiciona múltiplos polígonos ao mapa.
   *
   * @param dataMap Os dados do mapa que contêm os detalhes dos polígonos.
   * @param clear Indica se os polígonos existentes devem ser limpos.
   */
  newPolygonsMultiple(dataMap, clear = true) {
    this.polygons.forEach((polygonItem, index) => {
      this.polygons[index].setMap(null);
    });

    this.infoWindows.forEach((infoWindowItem, index) => {
      this.infoWindows[index].close();
      this.infoWindows[index] = null;
    });

    this.infoWindowsPolygons.forEach((infoWindowItem, index) => {
      this.infoWindowsPolygons[index].close();
      this.infoWindowsPolygons[index] = null;
    });

    this.polygons = [];
    this.infoWindows = [];
    this.infoWindowsPolygons = [];

    dataMap.polygons.forEach((polygonItem, index) => {
      let polygon: any = null;
      switch (polygonItem.type) {
        case 'CIRCLE':
          polygon = new google.maps.Circle(dataMap.polygons[index]);
          break;
      }

      if (!polygonItem.hasOwnProperty('infoWindowPolygon')) {
        let infoWindow = new google.maps.InfoWindow({
          content: dataMap.polygons[index].name,
          position: dataMap.polygons[index].path[0] //coordenada montante upstream
        });
        this.infoWindows.push(infoWindow);
        this.infoWindows[this.infoWindows.length - 1].open(this.newMap);
      } else {
        if (polygonItem.infoWindowPolygon.length > 0) {
          let infoWindowPolygon = new google.maps.InfoWindow({
            content: polygonItem.infoWindowPolygon.content,
            position: dataMap.polygons[index].center
          });
          let extraParams = {
            id: polygonItem.infoWindowPolygon.id,
            data: polygonItem.infoWindowPolygon.data,
            classGroup: polygonItem.infoWindowPolygon.classGroup ? polygonItem.infoWindowPolygon.classGroup : '',
            classTitle: polygonItem.infoWindowPolygon.classTitle ? polygonItem.infoWindowPolygon.classTitle : ''
          };
          this.zone.run(() => this.setComponent(polygon, infoWindowPolygon, polygonItem.infoWindowPolygon.contentConfig, extraParams));

          polygon.addListener('click', ($event) => {
            infoWindowPolygon.open({
              anchor: polygon
            });
            this.eventClick({ type: 'mapPolygonInfoWindow', data: extraParams });
          });

          infoWindowPolygon.addListener('closeclick', ($event) => {
            this.eventClick({ type: 'mapPolygonInfoWindowClose', data: extraParams });
          });

          this.infoWindows.push(infoWindowPolygon);
          //faz a infowindow vir aberto
          //this.infoWindows[this.infoWindows.length - 1].open(this.newMap);

          this.infoWindowsPolygons.push(infoWindowPolygon);
        }
      }

      this.polygons.push(polygon);
      this.polygons[this.polygons.length - 1].setMap(this.newMap);
    });
  }

  /**
   * Adiciona uma nova camada KML ao mapa.
   *
   * @param dataMap Os dados do mapa que contêm os detalhes da camada KML.
   * @param clear Indica se as camadas KML existentes devem ser limpas.
   */
  newKmlLayer(dataMap, clear = true) {
    this.kmlLayers.forEach((kmlLayer, index) => {
      this.kmlLayers[index].setMap(null);
    });

    this.kmlLayers = [];
    this.infoWindowsKmlLayers = [];

    dataMap.kmlLayers.forEach((kmlLayerItem, index) => {
      let kmlLayer = new google.maps.KmlLayer(dataMap.kmlLayers[index]);

      if (kmlLayerItem.hasOwnProperty('infoWindowKmlLayer')) {
        let infoWindowsKmlLayer = new google.maps.InfoWindow({
          content: kmlLayerItem.infoWindowKmlLayer.content,
          ariaLabel: kmlLayerItem.infoWindowKmlLayer.ariaLabel
        });

        kmlLayer.addListener('click', ($event) => {
          infoWindowsKmlLayer.setPosition($event.latLng);
          infoWindowsKmlLayer.open(this.newMap);
          this.eventClick({ type: 'mapkmlLayer', data: {} });
        });

        infoWindowsKmlLayer.addListener('closeclick', ($event) => {
          this.eventClick({ type: 'mapkmlLayerInfoWindowClose', data: {} });
        });
        this.infoWindowsKmlLayers.push(infoWindowsKmlLayer);
      }

      this.kmlLayers.push(kmlLayer);
      this.kmlLayers[this.kmlLayers.length - 1].setMap(this.newMap);
    });
  }

  /**
   * Adiciona novos dados GeoJSON ao mapa.
   *
   * @param dataMap Os dados do mapa que contêm os detalhes dos dados GeoJSON.
   * @param clear Indica se os dados GeoJSON existentes devem ser limpos.
   */
  newGeoData(dataMap, clear = true) {
    this.geoDatas.forEach((geoData, index) => {
      this.geoDatas[index].setMap(null);
    });

    this.geoDatas = [];
    this.infoWindowsGeoDatas = [];

    dataMap.geoDatas.forEach((geoDataItem, index) => {
      let geoData = new google.maps.Data();
      geoData.addGeoJson(dataMap.geoDatas[index].geoJson);

      let featureStyle = {
        fillColor: '#9e4f00',
        fillOpacity: 0.7,
        strokeWeight: 0
      };

      geoData.setStyle(featureStyle);

      if (geoDataItem.hasOwnProperty('infoWindowGeoData')) {
        let infoWindowGeoData = new google.maps.InfoWindow({
          content: geoDataItem.infoWindowGeoData.content,
          ariaLabel: geoDataItem.infoWindowGeoData.ariaLabel
        });
        geoData.addListener('click', ($event) => {
          infoWindowGeoData.setPosition($event.latLng);
          infoWindowGeoData.open(this.newMap);
          this.eventClick({ type: 'mapGeoData', data: {} });
        });
        infoWindowGeoData.addListener('closeclick', ($event) => {
          this.eventClick({ type: 'mapGeoDataInfoWindowClose', data: {} });
        });
        this.infoWindowsGeoDatas.push(infoWindowGeoData);
      }

      this.geoDatas.push(geoData);
      this.geoDatas[this.geoDatas.length - 1].setMap(this.newMap);
    });
  }

  /**
   * Define os componentes na janela de informação (InfoWindow) do mapa.
   *
   * @param elementMap O elemento do mapa associado à InfoWindow.
   * @param infoWindow A InfoWindow a ser configurada.
   * @param configs A configuração dos componentes da InfoWindow.
   * @param extraParams Parâmetros extras para configuração dos componentes.
   * @param $type O tipo de elemento (marcador, polilinha, etc).
   */
  setComponent(elementMap, infoWindow, configs: any = {}, extraParams: any = {}, $type = 'marker') {
    if (configs && configs.length > 0) {
      let element = [];

      let divRoot = document.createElement('DIV');
      divRoot.setAttribute('id', 'div-iw-root');

      let divTitle = document.createElement('DIV');
      divTitle.classList.add('iw-title');
      divTitle.innerHTML = $type == 'marker' ? extraParams.id : extraParams.data.name;

      if (extraParams.classTitle) {
        divTitle.setAttribute('class', `${extraParams.classTitle}`);
      }

      divRoot.appendChild(divTitle);

      let hr = document.createElement('HR');
      hr.classList.add('iw-hr');
      divRoot.appendChild(hr);

      if (extraParams.subtitle) {
        let divSubTitle = document.createElement('DIV');
        divSubTitle.classList.add('iw-subtitle'); // você pode estilizar como quiser no SCSS
        divSubTitle.innerHTML = `Status: ${extraParams?.subtitle ?? '---'}`;
        divRoot.appendChild(divSubTitle);
      }

      let divGroup = document.createElement('DIV');
      divGroup.setAttribute('class', `group-i ${extraParams.classGroup}`);

      configs.forEach((config, index) => {
        if (config && config.hasOwnProperty('component')) {
          if (config.component === 'app-button') {
            let compFactoryButton = this.resolver.resolveComponentFactory(ButtonComponent);
            this.compRefButton = compFactoryButton.create(this.injector);

            Object.keys(config.attrs).forEach((key) => {
              switch (key) {
                case 'eventClick':
                  this.compRefButton.instance.click = () => {
                    this.eventClick({ type: config.attrs.event, data: extraParams });
                  };
                  break;
                case 'event':
                  //nada a fazer
                  break;
                default:
                  this.compRefButton.instance[key] = config.attrs[key];
                  break;
              }
            });
            this.appRef.attachView(this.compRefButton.hostView);

            element.push(document.createElement('div'));
            element[element.length - 1].setAttribute('id', 'div-iw-' + index + '-' + extraParams.id);
            if (config.classItem) {
              element[element.length - 1].setAttribute('class', `${config.classItem}`);
            }
            element[element.length - 1].appendChild(this.compRefButton.location.nativeElement);
            if (config.hasOwnProperty('show')) {
              if (config.show) {
                divGroup.appendChild(element[element.length - 1]);
              } else {
              }
            } else {
              divGroup.appendChild(element[element.length - 1]);
            }
          }
          if (config.component === 'app-map-info') {
            let compFactoryMapInfo = this.resolver.resolveComponentFactory(MapInfoComponent);
            this.compRefMapInfo = compFactoryMapInfo.create(this.injector);

            Object.keys(config.attrs).forEach((key) => {
              switch (key) {
                default:
                  this.compRefMapInfo.instance[key] = config.attrs[key];
                  break;
              }
            });
            this.appRef.attachView(this.compRefMapInfo.hostView);

            element.push(document.createElement('div'));
            element[element.length - 1].setAttribute('id', 'div-iw-' + index + '-' + extraParams.id);
            if (config.classItem) {
              element[element.length - 1].setAttribute('class', `${config.classItem}`);
            }
            element[element.length - 1].appendChild(this.compRefMapInfo.location.nativeElement);
            if (config.hasOwnProperty('show')) {
              if (config.show) {
                divGroup.appendChild(element[element.length - 1]);
              } else {
              }
            } else {
              divGroup.appendChild(element[element.length - 1]);
            }
          }
        }
      });
      divRoot.appendChild(divGroup);
      infoWindow.setContent(divRoot);
    }
  }

  /**
   * Abre as janelas de informação (InfoWindows) para os marcadores com os IDs fornecidos.
   *
   * @param arrayMarkersId Array de IDs dos marcadores para os quais abrir as InfoWindows.
   * @param prefix Prefixo para identificar os marcadores.
   * @param close Indica se as InfoWindows existentes devem ser fechadas antes de abrir as novas.
   */
  openInfoWindowFromMarkerIdSrv(arrayMarkersId: any, prefix: any = '', close: boolean = true) {
    if (close) {
      this.infoWindowsMarker.forEach((infoWindowMarker: any) => {
        infoWindowMarker.close();
      });
    }

    this.markers.forEach((marker: any) => {
      if (arrayMarkersId.includes(marker.id)) {
        this.infoWindowsMarker.forEach((infoWindowMarker: any) => {
          if (prefix + infoWindowMarker.ariaLabel == marker.id) {
            infoWindowMarker.open({ anchor: marker });
          }
        });
      }
    });
  }

  /**
   * Limpa todos os marcadores do mapa.
   */
  clearMarkers() {
    this.markers.forEach((markerItem, index) => {
      if (index >= 0 && index < this.markers.length && this.markers[index] != null) {
        this.markers[index].setMap(null);
      }
    });
    this.markers.length = 0;
    this.markers = [];
  }

  clearMarkersAttachments(): void {
    this.markersAttachments.forEach((markerItem, index) => {
      if (index >= 0 && index < this.markersAttachments.length && this.markersAttachments[index] != null) {
        this.markersAttachments[index].setMap(null);
      }
    });
    this.markersAttachments.length = 0;
    this.markersAttachments = [];

    this.infoWindowsAttachments.forEach((infoWindowItem, index) => {
      if (index >= 0 && index < this.infoWindowsAttachments.length && this.infoWindowsAttachments[index] != null) {
        this.infoWindowsAttachments[index].close();
        this.infoWindowsAttachments[index] = null;
      }
    });
    this.infoWindowsAttachments.length = 0;
    this.infoWindowsAttachments = [];
  }

  /**
   * Limpa todas as polilinhas do mapa.
   */
  clearPolylines() {
    this.polylines.forEach((polylineItem, index) => {
      if (index >= 0 && index < this.polylines.length && this.polylines[index] != null) {
        this.polylines[index].setMap(null);
      }
    });
    this.polylines.length = 0;
    this.polylines = [];
  }

  /**
   * Limpa todos os polígonos do mapa.
   */
  clearPolygons() {
    this.polygons.forEach((polygonItem, index) => {
      if (index >= 0 && index < this.polygons.length && this.polygons[index] != null) {
        this.polygons[index].setMap(null);
      }
    });
    this.polygons.length = 0;
    this.polygons = [];
  }

  /**
   * Limpa todas as camadas KML do mapa.
   */
  clearKmls() {
    this.kmlLayers.forEach((kmlItem, index) => {
      if (index >= 0 && index < this.kmlLayers.length && this.kmlLayers[index] != null) {
        this.kmlLayers[index].setMap(null);
      }
    });
    this.kmlLayers.length = 0;
    this.kmlLayers = [];
  }

  /**
   * Limpa todos os dados GeoJSON do mapa.
   */
  clearGeoDatas() {
    this.geoDatas.forEach((geoDataItem, index) => {
      if (index >= 0 && index < this.geoDatas.length && this.geoDatas[index] != null) {
        this.geoDatas[index].setMap(null);
      }
    });
    this.geoDatas.length = 0;
    this.geoDatas = [];
  }

  /**
   * Limpa todas as janelas de informação (InfoWindows) do mapa.
   */
  clearInfoWindow() {
    this.infoWindows.forEach((infoWindowItem, index) => {
      if (index >= 0 && index < this.infoWindows.length && this.infoWindows[index] != null) {
        this.infoWindows[index].close();
        this.infoWindows[index] = null;
      }
    });
    this.infoWindows.length = 0;
    this.infoWindows = [];
  }

  /**
   * Limpa todas as janelas de informação (InfoWindows) das polilinhas no mapa.
   */
  clearinfoWindowsPolyline() {
    this.infoWindowsPolyline.forEach((infoWindowPolylineItem, index) => {
      if (index >= 0 && index < this.infoWindowsPolyline.length && this.infoWindowsPolyline[index] != null) {
        this.infoWindowsPolyline[index].close();
        this.infoWindowsPolyline[index] = null;
      }
    });
    this.infoWindowsPolyline.length = 0;
    this.infoWindowsPolyline = [];
  }

  /**
   * Limpa todas as janelas de informação (InfoWindows) das camadas KML no mapa.
   */
  clearinfoWindowsKmlLayers() {
    this.infoWindowsKmlLayers.forEach((infoWindowsKmlLayersItem, index) => {
      if (index >= 0 && index < this.infoWindowsKmlLayers.length && this.infoWindowsKmlLayers[index] != null) {
        this.infoWindowsKmlLayers[index].close();
        this.infoWindowsKmlLayers[index] = null;
      }
    });
    this.infoWindowsPolyline.length = 0;
    this.infoWindowsPolyline = [];
  }

  /**
   * Limpa todas as janelas de informação (InfoWindows) dos dados GeoJSON no mapa.
   */
  clearinfoWindowsGeoDatas() {
    if (this.infoWindowsGeoDatas != null) {
      this.infoWindowsGeoDatas.forEach((infoWindowsGeoDatasItem, index) => {
        if (index >= 0 && index < this.infoWindowsGeoDatas.length && this.infoWindowsGeoDatas[index] != null) {
          this.infoWindowsGeoDatas[index].close();
          this.infoWindowsGeoDatas[index] = null;
        }
      });
      this.infoWindowsPolyline.length = 0;
      this.infoWindowsPolyline = [];
    }
  }

  /**
   * Limpa todos os elementos do mapa, incluindo marcadores, polilinhas, camadas KML, dados GeoJSON e janelas de informação.
   * @param {boolean} resetCenter - Indica se o centro do mapa deve ser redefinido para a posição inicial.
   */
  clearMapSrv(resetCenter = true) {
    this.clearMarkers();
    this.clearPolylines();
    this.clearKmls();
    this.clearGeoDatas();
    this.clearInfoWindow();
    this.clearinfoWindowsPolyline();
    this.clearinfoWindowsKmlLayers();
    this.clearinfoWindowsGeoDatas();

    if (resetCenter) {
      const LatLng = new google.maps.LatLng(-17.930178, -43.7908453);
      this.center = LatLng;

      this.newMap.setCenter(new google.maps.LatLng(-17.930178, -43.7908453));
      this.newMap.setZoom(16);
      this.markerMap();
    }
  }

  /**
   * Envia um evento de clique compartilhado para o serviço.
   * @param {any} params - Parâmetros do evento de clique.
   */
  eventClick(params) {
    this.sharedService.sendClickEvent(params);
  }

  /**
   * Converte dados KML em formato base64 para GeoJSON.
   * @param {any} base64KmlData - Dados KML em formato base64.
   * @returns {Promise<any>} - Uma promessa que resolve para os dados em formato GeoJSON.
   */
  convertKmlToGeoJson(base64KmlData: any): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.blobParaString(base64KmlData)
        .then((result) => {
          const kmlData = result;
          const kmlDocument = new DOMParser().parseFromString(kmlData, 'text/xml');
          const geoJson = toGeoJSON.kml(kmlDocument);
          resolve(geoJson); // Retorna o GeoJSON
        })
        .catch((error) => {
          console.error(error);
          reject(error);
        });
    });
  }

  /**
   * Converte um Blob para uma string.
   * @param {Blob} blob - O Blob a ser convertido.
   * @returns {Promise<string>} - Uma promessa que resolve para a string convertida.
   */
  blobParaString(blob: Blob): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const dataUrl = reader.result as string;
        const base64 = dataUrl.split(',')[1];
        const text = atob(base64);
        resolve(text);
      };
      reader.onerror = () => {
        reject('Erro ao converter Blob para string');
      };
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Força o redesenho do mapa para corrigir problemas de renderização
   */
  triggerResize(): void {
    if (this.newMap) {
      google.maps.event.trigger(this.newMap, 'resize');

      // Reaplica o centro após o resize
      if (this.center) {
        this.newMap.setCenter(this.center);
      }
    }
  }
}
