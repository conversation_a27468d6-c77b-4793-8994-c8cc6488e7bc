import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';

import { DataService } from 'src/app/services/data.service';
import { UserService } from 'src/app/services/user.service';
import { MapsService as MapsServiceApi } from 'src/app/services/api/maps.service';

import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';

import { GoogleMapsComponent } from 'src/app/components/google-maps/google-maps.component';

import { SharedService } from 'src/app/services/shared.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { Subscription } from 'rxjs';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-maps-stability',
  templateUrl: './maps-stability.component.html',
  styleUrls: ['./maps-stability.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class MapsStabilityComponent implements OnInit {
  @ViewChild('mapStability', { static: false }) mapStability: GoogleMapsComponent;
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalAnalysis') ModalAnalysis: any;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public dataMapsStability = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  public centerDefault = { lat: -17.930178, lng: -43.7908453 };
  public coordinateSections: any = [];

  public message: any = [{ text: '', status: false, class: 'alert-success' }];
  public messagesError: any = null;

  public ctrlBtnFilter: boolean = false;
  public ctrlColors: boolean = false;

  public clickEventsubscription: Subscription;

  public showColorPicker = {
    ina: false,
    pz: false
  };

  public selectedColor = {
    ina: '#0000FF',
    pz: '#FF0000'
  };

  public stability_map_configuration_id: string = null;
  public structure_id: string = null;

  public dataMap: any = null;

  public configModal: any = null;
  public titleModal: string = '';

  public func = fn;

  constructor(
    private dataService: DataService,
    private userService: UserService,
    private mapsServiceApi: MapsServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private sharedService: SharedService
  ) {
    this.clickEventsubscription = this.sharedService.getClickEvent().subscribe((event) => {
      this.eventMap(event);
    });
  }

  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;
  }

  generateMap() {
    this.ngxSpinnerService.show();

    let filterHierarchy = this.hierarchy.getFilters();
    this.mapsStructure(filterHierarchy.clients[0].id, filterHierarchy.structures[0].id);
  }

  postStabilityMapConfiguration() {
    this.ngxSpinnerService.show();

    const params = {
      id: this.stability_map_configuration_id,
      structure_id: this.structure_id,
      piezometer_color: this.selectedColor.pz,
      water_level_indicator_color: this.selectedColor.ina
    };

    this.dataMap.structure_map_data.stability_map_configuration['water_level_indicator_color'] = this.selectedColor.ina;
    this.dataMap.structure_map_data.stability_map_configuration['piezometer_color'] = this.selectedColor.pz;

    this.mapsServiceApi.postMapsStabilityConfiguration(params).subscribe(
      (resp) => {
        this.stability_map_configuration_id = resp.toString();
        this.message.text = MessageCadastro.AlteracaoCor;
        this.message.status = true;
        this.message.class = 'alert-success';
        this.managerMap();

        setTimeout(() => {
          this.message = { text: '', status: false, class: 'alert-success' };
        }, 4000);
        this.ngxSpinnerService.hide(); // Ocultar o spinner após o sucesso
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
          this.ngxSpinnerService.hide(); // Ocultar o spinner em caso de erro
        }
      }
    );
  }

  mapsStructure(clientId, structureId) {
    this.dataService.getStructureCoordinate(clientId, [structureId]).subscribe(
      (coordinate) => {
        if (coordinate.length > 0) {
          this.dataMapsStability.center.lat = coordinate[0].decimal_geodetic.latitude;
          this.dataMapsStability.center.lng = coordinate[0].decimal_geodetic.longitude;
          this.dataMapsStability.markers[0].position.lat = coordinate[0].decimal_geodetic.latitude;
          this.dataMapsStability.markers[0].position.lng = coordinate[0].decimal_geodetic.longitude;
          this.sendDataMap('markers');
          this.getMapsStability(structureId);
        }
      },
      (error) => {
        console.error('Erro ao obter estrutura:', error);
        this.ngxSpinnerService.hide(); // Ocultar spinner em caso de erro
      }
    );
  }

  getMapsStability(structureId: string = null) {
    this.ngxSpinnerService.show();
    this.stability_map_configuration_id = null;
    this.structure_id = null;

    this.mapsServiceApi.getMapsStabilityById(structureId).subscribe(
      (resp) => {
        let dados: any = resp;
        if (dados) {
          dados = dados.body === undefined ? dados : dados.body;
          this.stability_map_configuration_id =
            dados.structure_map_data.stability_map_configuration == null ? null : dados.structure_map_data.stability_map_configuration.id;
          this.structure_id = structureId;
          this.dataMap = dados;
          this.managerMap();
        } else {
          this.message.text = MessagePadroes.NoMapData;
          this.message.status = true;
          this.message.class = 'alert-warning';
          setTimeout(() => {
            this.message = { text: '', status: false, class: 'alert-success' };
          }, 4000);
        }
        this.ngxSpinnerService.hide(); // Ocultar spinner após o carregamento
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
          this.ngxSpinnerService.hide(); // Ocultar spinner em caso de erro
        }
      }
    );
  }

  managerMap() {
    const $dados = this.dataMap;
    if ($dados.structure_map_data.stability_map_configuration != null) {
      this.selectedColor.ina = $dados.structure_map_data.stability_map_configuration.water_level_indicator_color;
      this.selectedColor.pz = $dados.structure_map_data.stability_map_configuration.piezometer_color;
    } else {
      this.dataMap.structure_map_data.stability_map_configuration = {};
    }
    this.ctrlColors = true;
    this.plotSections($dados.structure_map_data.sections);
    this.plotInstruments($dados.instruments);
  }

  plotSections($sections) {
    $sections.forEach((section) => {
      let polyline = {
        path: [],
        strokeColor: section.map_line_setting.color,
        strokeOpacity: section.map_line_setting.type == 1 ? 0 : 1,
        strokeWeight: section.map_line_setting.width,
        icons: [
          {
            icon: {
              path: 'M 0,-1 0,1',
              strokeOpacity: section.map_line_setting.type == 1 ? 1 : 0,
              scale: 4,
              strokeWeight: section.map_line_setting.width
            },
            offset: '0',
            repeat: '20px'
          }
        ],
        id: section.id,
        name: section.name
      };

      polyline.path.push({
        lat: section.coordinates.upstream_coordinate_setting.coordinate_systems.decimal_geodetic.latitude,
        lng: section.coordinates.upstream_coordinate_setting.coordinate_systems.decimal_geodetic.longitude
      });
      if (section.coordinates.midpoint_coordinate_setting) {
        polyline.path.push({
          lat: section.coordinates.midpoint_coordinate_setting.coordinate_systems.decimal_geodetic.latitude,
          lng: section.coordinates.midpoint_coordinate_setting.coordinate_systems.decimal_geodetic.longitude
        });
      }
      polyline.path.push({
        lat: section.coordinates.downstream_coordinate_setting.coordinate_systems.decimal_geodetic.latitude,
        lng: section.coordinates.downstream_coordinate_setting.coordinate_systems.decimal_geodetic.longitude
      });

      let infoWindowPolyline = {
        content: '',
        ariaLabel: section.name,
        id: section.name,
        data: section,
        classTitle: 'd-flex justify-content-center',
        contentConfig: [
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-blue',
              icon: '',
              label: 'Ver Análise',
              type: true,
              eventClick: true,
              event: 'analysis',
              id: 'iw-button-pl' + fn.hashCode(section.name)
            },
            classItem: 'd-flex justify-content-center'
          }
        ]
      };

      polyline['infoWindowPolyline'] = infoWindowPolyline;

      //Adiciona a seção para exibir no mapa
      this.dataMapsStability.polylines.push(polyline);
    });
    this.sendDataMap('polylinesMultiple', false);
  }

  plotInstruments($instruments) {
    $instruments.forEach((instrument) => {
      let strokeColor = 'white';
      let fillColor = instrument.type == 1 ? this.selectedColor.ina : this.selectedColor.pz;
      let svgMarker = {
        path: 'M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0',
        fillColor: fillColor,
        fillOpacity: 1.0,
        strokeWeight: 1.5,
        strokeColor: strokeColor,
        rotation: 0,
        scale: 0.3,
        anchor: new google.maps.Point(0, 0)
      };
      let marker = {
        position: {
          lat: instrument.decimal_geodetic_coordinate.latitude,
          lng: instrument.decimal_geodetic_coordinate.longitude
        },
        title: instrument.identifier,
        options: {},
        icon: svgMarker,
        id: 'mk-' + instrument.identifier,
        zIndex: -999
      };
      let infoWindowMarker = {
        content: instrument.identifier,
        ariaLabel: instrument.identifier,
        id: instrument.identifier,
        data: instrument,
        contentConfig: []
      };

      marker['infoWindowMarker'] = infoWindowMarker;
      this.dataMapsStability.markers.push(marker);
    });
    this.sendDataMap('markersMultiple', true);
  }

  sendDataMap(option, clear = true) {
    this.mapStability.setDataMap(this.dataMapsStability, option, clear);
  }

  resetFilters() {
    this.ctrlBtnFilter = false;
    this.ctrlColors = false;
    this.hierarchy.resetFilters();
    this.mapStability.clearMap();
  }

  eventMap($event) {
    switch ($event.type) {
      case 'analysis':
        this.configModal = $event.data.data;
        this.titleModal = $event.data.data.name;
        this.ModalAnalysis.openModal();
        break;
    }
  }

  getEventHierarchy($event) {
    switch ($event.type) {
      case 'structures':
        this.ctrlBtnFilter = $event.action === 'select' ? true : false;
        this.generateMap();
        break;
    }
    if ($event.action === 'deselect') {
      this.ctrlBtnFilter = false;
      this.ctrlColors = false;
      this.mapStability.clearMap();
    }
  }

  onClickedOutside(element: string, property: string = '') {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker[property] = false;
        break;
    }
  }

  // Método para trocar as cores dos instrumentos (INA e PZ)
  changeComplete($event, property: string = '') {
    this.ngxSpinnerService.show(); // Exibir o spinner
    setTimeout(() => {
      this.selectedColor[property] = $event.color.hex;
      this.ngxSpinnerService.hide(); // Ocultar o spinner após o processo
    }, 300); // Simulação de tempo para a troca de cor
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }
}
