import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class NotificationsService {
  [x: string]: any;
  constructor(private api: ApiService) { }

  //Obtém as Notificações
  getNotifications(params: any) {
    const url = `/notifications`;
    return this.api.get<any>(url, params, false, 'user');
  }

  //Marca uma lista de Notificação como lidas
  putReadNotifications(params: any) {
    const url = `/notifications/read`;
    return this.api.put<any>(url, params, 'user');
  }

  //Marca todas as Notificações como lidas
  putAllReadNotifications(params: any) {
    const url = `/notifications/read/all`;
    return this.api.put<any>(url, params, 'user');
  }

  //Obtém as configurações de notificação do usuário
  getUserNotificationConfigurations() {
    const url = `/notifications/configuration`;
    return this.api.get<any>(url, null, false, 'user');
  }

  //Altera as configurações de notificação do usuário
  postUserNotificationConfigurations(params: any) {
    const url = `/notifications/configuration`;
    return this.api.post<any>(url, params, {}, 'user');
  }
  

}