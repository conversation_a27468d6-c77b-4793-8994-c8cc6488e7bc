import { Injectable } from '@angular/core';
import * as _ from 'lodash';
import { FormService } from './form.service';
import * as moment from 'moment';

@Injectable()
export class StaticMaterialsService {
  public edit: boolean = false;
  public view: boolean = false;

  public componentDrained: any = null;
  public componentUndrained: any = null;
  public componentPseudo: any = null;

  public formDrainedlCoditionValid: any = null;
  public formUndrainedConditionValid: any = null;
  public formPseudoConditionValid: any = null;

  public constitutiveModelElement: any = {
    drained: null,
    undrained: null,
    pseudo: null
  };

  public constitutiveModelDrained: any = null;
  public constitutiveModelUndrained: any = null;
  public constitutiveModelPseudo: any = null;

  public formDrainedlValid: any = null;
  public formUndrainedValid: any = null;
  public formPseudoValid: any = null;

  public staticMaterialsRequest: any = {
    structure: {
      id: null
    },
    name: null,
    drained_static_material_value: null,
    undrained_static_material_value: null,
    pseudo_static_static_material_value: null,
    description: null,
    start_date: null
  };

  public constitutiveModelRequest: any = {
    color: null,
    natural_specific_weight: null,
    saturated_specific_weight: null,
    constitutive_model: null,
    cohesion: null,
    friction_angle: null,
    tensile_strength: null,
    cohesion_type: null,
    cohesion_variation: null,
    maximum: null,
    datum: null,
    allow_sliding_along_boundary: null,
    ucs_intact: null,
    m: null,
    s: null,
    strength_definition: null,
    gsi: null,
    mi: null,
    d: null,
    mb: null,
    a: null,
    resistance_ratio: null,
    maximum_shear_strength: null,
    minimum_shear_strength: null,
    stress_history_type: null,
    stress_history_method: null,
    constant: null,
    hu: null,
    custom_hu_value: null,
    use_drained_resistance_over_water_surface: false,
    water_surface: null,
    point_values: []
  };

  constructor(private formService: FormService) {}

  /**
   * Valida as condições dos formulários e abas (drained, undrained, pseudo) e chama a função de juntar dados se válido.
   * @param {any} elementoTab - O elemento que contém as abas e o formulário.
   * @param {string} tabConditions - As condições das abas (drained, undrained, pseudo).
   * @param {string} formGroup - O nome do FormGroup que está sendo validado.
   * @param {boolean} edit - Indica se o formulário está em modo de edição.
   * @param {boolean} view - Indica se o formulário está em modo de visualização.
   * @returns {boolean|void} - Retorna false se a validação falhar ou chama a função de junção de dados se for bem-sucedida.
   */
  validate(elementoTab, tabConditions, formGroup, edit, view) {
    this.edit = edit;
    this.view = view;

    //resetando validação
    elementoTab[tabConditions].drainedTabConfig.styleColor = false;
    elementoTab[tabConditions].undrainedTabConfig.styleColor = false;
    elementoTab[tabConditions].pseudoTabConfig.styleColor = false;
    elementoTab[tabConditions].ctrlMsgTab = null;

    let formValid = this.formService.validateForm(elementoTab[formGroup]);
    if (elementoTab.filterHierarchy) {
      let hierarchyValid = elementoTab.filterHierarchy.structures && elementoTab.filterHierarchy.structures.length > 0 ? true : false;
    }
    this.componentDrained = elementoTab[tabConditions].drainedRef.toArray()[0];
    this.componentUndrained = elementoTab[tabConditions].undrainedRef.toArray()[0];
    this.componentPseudo = elementoTab[tabConditions].pseudoRef.toArray()[0];

    this.formDrainedlCoditionValid = this.validateFormConstitutiveModel(this.componentDrained, 'drained');
    this.formUndrainedConditionValid = this.validateFormConstitutiveModel(this.componentUndrained, 'undrained');
    this.formPseudoConditionValid = this.validateFormConstitutiveModel(this.componentPseudo, 'pseudo');

    this.constitutiveModelDrained = this.componentDrained['formConditionStaticMaterials'].controls['constitutive_model'].value.length;
    this.constitutiveModelUndrained = this.componentUndrained['formConditionStaticMaterials'].controls['constitutive_model'].value.length;
    this.constitutiveModelPseudo = this.componentPseudo['formConditionStaticMaterials'].controls['constitutive_model'].value.length;

    this.formDrainedlValid = this.formService.validateForm(this.componentDrained.formConditionStaticMaterials);
    this.formUndrainedValid = this.formService.validateForm(this.componentUndrained.formConditionStaticMaterials);
    this.formPseudoValid = this.formService.validateForm(this.componentPseudo.formConditionStaticMaterials);

    elementoTab[tabConditions].ctrlMsgTab = this.constitutiveModelDrained + this.constitutiveModelUndrained + this.constitutiveModelPseudo;
    const drainedValid =
      this.constitutiveModelDrained === 1 && this.formDrainedlCoditionValid && this.formDrainedlValid
        ? true
        : this.constitutiveModelDrained === 0
        ? true
        : false;
    const undrainedValid =
      this.constitutiveModelUndrained === 1 && this.formUndrainedConditionValid && this.formUndrainedValid
        ? true
        : this.constitutiveModelUndrained === 0
        ? true
        : false;
    const pseudoValid =
      this.constitutiveModelPseudo === 1 && this.formPseudoConditionValid && this.formPseudoValid ? true : this.constitutiveModelPseudo === 0 ? true : false;

    if (drainedValid && undrainedValid && pseudoValid && formValid) {
      return this.joinData(elementoTab, tabConditions, formGroup);
    } else {
      //   //validar as abas
      if (!drainedValid) {
        this.componentDrained['formConditionStaticMaterials'].markAllAsTouched();
        elementoTab[tabConditions].drainedTabConfig.styleColor = !this.formDrainedlCoditionValid;
      }
      if (!undrainedValid) {
        this.componentUndrained['formConditionStaticMaterials'].markAllAsTouched();
        elementoTab[tabConditions].undrainedTabConfig.styleColor = !this.formUndrainedConditionValid;
      }
      if (!pseudoValid) {
        this.componentPseudo['formConditionStaticMaterials'].markAllAsTouched();
        elementoTab[tabConditions].pseudoTabConfig.styleColor = !this.formPseudoConditionValid;
      }
      elementoTab[formGroup].markAllAsTouched();
      return false;
    }
  }

  /**
   * Valida o modelo constitutivo selecionado em um componente específico (drained, undrained, pseudo).
   * @param {any} component - O componente que contém o formulário do modelo constitutivo.
   * @param {string} condition - A condição (drained, undrained, pseudo) que está sendo validada.
   * @returns {boolean} - Retorna true se a validação for bem-sucedida ou false caso contrário.
   */
  validateFormConstitutiveModel(component, condition) {
    if (component.formConditionStaticMaterials.controls['constitutive_model'].value.length > 0) {
      this.constitutiveModelElement[condition] = this.getConstitutiveModelElement(component);
    }

    if (this.constitutiveModelElement[condition] !== null) {
      return this.constitutiveModelElement[condition].validate();
    } else {
      return true;
    }
  }

  /**
   * Obtém o elemento de modelo constitutivo com base no componente fornecido.
   * @param {any} component - O componente do qual extrair o modelo constitutivo.
   * @returns {any} Retorna o elemento de modelo constitutivo.
   */
  getConstitutiveModelElement(component: any) {
    let element = component.selectedConstitutiveModel;
    let constitutiveModelElement = null;

    switch (element.toString()) {
      case '1': //mohrColoumbRef
        constitutiveModelElement = component.mohrColoumbRef.toArray()[0];
        break;
      case '2': //undrainedRef
        constitutiveModelElement = component.undrainedRef.toArray()[0];
        break;
      case '3': //noStrength
        break;
      case '4': //infiniteStrengthRef
        constitutiveModelElement = component.infiniteStrengthRef.toArray()[0];
        break;
      case '5': //shearNormalFunctionRef
        constitutiveModelElement = component.shearNormalFunctionRef.toArray()[0];
        break;
      case '6': //hoekBrownRef
        constitutiveModelElement = component.hoekBrownRef.toArray()[0];
        break;
      case '7': //generalizedHoekBrownRef
        constitutiveModelElement = component.generalizedHoekBrownRef.toArray()[0];
        break;
      case '8': //verticalStressRatioRef
        constitutiveModelElement = component.verticalStressRatioRef.toArray()[0];
        break;
      case '9': //shansepRef
        constitutiveModelElement = component.shansepRef.toArray()[0];
        break;
      default:
        constitutiveModelElement = null;
        break;
    }
    return constitutiveModelElement;
  }

  /**
   * Junta os dados do formulário e envia a solicitação para criar ou editar o material estático.
   * Dependendo se está em modo de edição ou não, decide entre registrar um novo material ou editar um existente.
   */
  joinData(elementoTab, tabConditions, formGroup) {
    let data = this.staticMaterialsRequest;

    data.name = elementoTab[formGroup].controls['name'].value;
    if (tabConditions === 'tabConditions') {
      data.structure.id = elementoTab.filterHierarchy.structures[0].id;
      delete data.description;
    } else if (tabConditions === 'tabConditionsReview') {
      data.structure.id = elementoTab[formGroup].controls['structures'].value[0].id;
      data.description = elementoTab[formGroup].controls['description'].value;
      data.start_date = moment(elementoTab[formGroup].controls['start_date'].value).format('YYYY-MM-DDT00:00:00');
    }

    //drained
    if (this.constitutiveModelDrained > 0) {
      data.drained_static_material_value = this.joinDataConstitutiveModel('drained');
    }

    //undrained
    if (this.constitutiveModelUndrained > 0) {
      data.undrained_static_material_value = this.joinDataConstitutiveModel('undrained');
    }

    //pseudo
    if (this.constitutiveModelPseudo > 0) {
      data.pseudo_static_static_material_value = this.joinDataConstitutiveModel('pseudo');
    }
    return data;
  }

  /**
   * Junta os dados específicos do modelo constitutivo com base na condição fornecida (ex: 'drained', 'undrained', 'pseudo').
   * @param {string} constitutiveModel - A condição (modelo constitutivo) a ser processada.
   * @returns {any} Retorna os dados processados do modelo constitutivo.
   */
  joinDataConstitutiveModel(constitutiveModel) {
    let dataConstituive = _.cloneDeep(this.constitutiveModelRequest);
    let constituiveComponent = null;
    let constitutiveForm = null;

    switch (constitutiveModel) {
      case 'drained':
        constituiveComponent = this.componentDrained;
        constitutiveForm = this.componentDrained['formConditionStaticMaterials'];
        break;
      case 'undrained':
        constituiveComponent = this.componentUndrained;
        constitutiveForm = this.componentUndrained['formConditionStaticMaterials'];
        break;
      case 'pseudo':
        constituiveComponent = this.componentPseudo;
        constitutiveForm = this.componentPseudo['formConditionStaticMaterials'];
        break;
    }

    dataConstituive.color = constitutiveForm.controls['color'].value;
    dataConstituive.natural_specific_weight = constitutiveForm.controls['natural_specific_weight'].value;

    if (constitutiveForm.controls['is_saturated_specific_weight'].value) {
      dataConstituive.saturated_specific_weight = constitutiveForm.controls['saturated_specific_weight'].value;
    }
    dataConstituive.constitutive_model = parseInt(constitutiveForm.controls['constitutive_model'].value[0].id);

    //Parametros freaticos
    if (constitutiveForm.controls['water_surface'].value.length > 0) {
      dataConstituive.water_surface = parseInt(constitutiveForm.controls['water_surface'].value[0].id);
    }

    if (constitutiveForm.controls['hu'].value.length > 0) {
      dataConstituive.hu = parseInt(constitutiveForm.controls['hu'].value[0].id);
      if (parseInt(constitutiveForm.controls['hu'].value[0].id) == 1) {
        dataConstituive.custom_hu_value = constitutiveForm.controls['custom_hu_value'].value;
      }
    }

    dataConstituive.use_drained_resistance_over_water_surface = constitutiveForm.controls['use_drained_resistance_over_water_surface'].value;

    this.constitutiveModelElement['drained'] = this.getConstitutiveModelElement(constituiveComponent);

    let idConstitutiveModel =
      typeof constituiveComponent.selectedConstitutiveModel == 'number'
        ? constituiveComponent.selectedConstitutiveModel.toString()
        : constituiveComponent.selectedConstitutiveModel;

    switch (idConstitutiveModel) {
      case '1': //mohrColoumbRef
        dataConstituive.cohesion = this.constitutiveModelElement[constitutiveModel].formMohrColoumb.controls['cohesion'].value;
        dataConstituive.friction_angle = this.constitutiveModelElement[constitutiveModel].formMohrColoumb.controls['friction_angle'].value;
        if (this.constitutiveModelElement[constitutiveModel].formMohrColoumb.controls['is_tensile_strength'].value) {
          dataConstituive.tensile_strength = this.constitutiveModelElement[constitutiveModel].formMohrColoumb.controls['tensile_strength'].value;
        }
        break;
      case '2': //undrainedRef
        dataConstituive.cohesion_type = parseInt(this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion_type'].value[0].id);
        //Tipo de coesao
        switch (this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion_type'].value[0].id) {
          case '1': //Constant
            dataConstituive.cohesion = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion'].value;
            if (this.constitutiveModelElement[constitutiveModel].formUndrained.controls['is_tensile_strength'].value) {
              dataConstituive.tensile_strength = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['tensile_strength'].value;
            }
            break;
          case '2': //F(Depth from Top of Layer)
            dataConstituive.cohesion = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion_top'].value;
            dataConstituive.cohesion_variation = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion_variation'].value;
            if (this.constitutiveModelElement[constitutiveModel].formUndrained.controls['is_f_tensile_strength'].value) {
              dataConstituive.tensile_strength = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['f_tensile_strength'].value;
            }
            if (this.constitutiveModelElement[constitutiveModel].formUndrained.controls['is_maximum'].value) {
              dataConstituive.maximum = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['maximum'].value;
            }
            break;
          case '3': //F(Depth from Horizontal Datum)
            dataConstituive.cohesion = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion_datum'].value;
            dataConstituive.cohesion_variation = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion_variation_datum'].value;
            dataConstituive.datum = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['datum'].value;
            if (this.constitutiveModelElement[constitutiveModel].formUndrained.controls['is_datum_tensile_strength'].value) {
              dataConstituive.tensile_strength = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['datum_tensile_strength'].value;
            }
            if (this.constitutiveModelElement[constitutiveModel].formUndrained.controls['is_datum_maximum'].value) {
              dataConstituive.maximum = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['datum_maximum'].value;
            }
            break;
          case '4': //F(Distance to Slope)
            dataConstituive.cohesion = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion_top_slope'].value;
            dataConstituive.cohesion_variation = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['cohesion_variation_slope'].value;
            if (this.constitutiveModelElement[constitutiveModel].formUndrained.controls['is_slope_tensile_strength'].value) {
              dataConstituive.tensile_strength = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['slope_tensile_strength'].value;
            }
            if (this.constitutiveModelElement[constitutiveModel].formUndrained.controls['is_maximum_slope'].value) {
              dataConstituive.maximum = this.constitutiveModelElement[constitutiveModel].formUndrained.controls['maximum_slope'].value;
            }
            break;
        }
        break;
      case '3': //noStrength
        break;
      case '4': //infiniteStrengthRef
        dataConstituive.allow_sliding_along_boundary =
          this.constitutiveModelElement[constitutiveModel].formInfiniteStrength.controls['is_allow_sliding_along_boundary'].value;
        break;
      case '5': //shearNormalFunctionRef
        dataConstituive.point_values = this.constitutiveModelElement[constitutiveModel].getPoints();
        break;
      case '6': //hoekBrownRef
        dataConstituive.ucs_intact = this.constitutiveModelElement[constitutiveModel].formHoekBrown.controls['ucs_intact'].value;
        dataConstituive.m = this.constitutiveModelElement[constitutiveModel].formHoekBrown.controls['m'].value;
        dataConstituive.s = this.constitutiveModelElement[constitutiveModel].formHoekBrown.controls['s'].value;
        break;
      case '7': //generalizedHoekBrownRef
        dataConstituive.ucs_intact = this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['ucs_intact'].value;
        dataConstituive.strength_definition = parseInt(
          this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['strength_definition'].value[0].id
        );
        switch (this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['strength_definition'].value[0].id) {
          case '1': //gsi,mi,d
            dataConstituive.gsi = this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['gsi'].value;
            dataConstituive.mi = this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['mi'].value;
            dataConstituive.d = this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['d'].value;
            break;
          case '2': //mb,s,a
            dataConstituive.mb = this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['mb'].value;
            dataConstituive.s = this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['s'].value;
            dataConstituive.a = this.constitutiveModelElement[constitutiveModel].formGeneralizedHoekBrown.controls['a'].value;
            break;
        }
        break;
      case '8': //verticalStressRatioRef
        dataConstituive.resistance_ratio = this.constitutiveModelElement[constitutiveModel].formVerticalStressRatio.controls['resistance_ratio'].value;
        dataConstituive.minimum_shear_strength =
          this.constitutiveModelElement[constitutiveModel].formVerticalStressRatio.controls['minimum_shear_strength'].value;
        if (this.constitutiveModelElement[constitutiveModel].formVerticalStressRatio.controls['is_maximum_shear_strength'].value) {
          dataConstituive.maximum_shear_strength =
            this.constitutiveModelElement[constitutiveModel].formVerticalStressRatio.controls['maximum_shear_strength'].value;
        }
        if (this.constitutiveModelElement[constitutiveModel].formVerticalStressRatio.controls['is_tensile_strength'].value) {
          dataConstituive.tensile_strength = this.constitutiveModelElement[constitutiveModel].formVerticalStressRatio.controls['tensile_strength'].value;
        }
        break;
      case '9': //shansepRef
        dataConstituive.point_values = this.constitutiveModelElement[constitutiveModel].getPoints();
        dataConstituive.a = this.constitutiveModelElement[constitutiveModel].formShansep.controls['a'].value;
        dataConstituive.s = this.constitutiveModelElement[constitutiveModel].formShansep.controls['s'].value;
        dataConstituive.m = this.constitutiveModelElement[constitutiveModel].formShansep.controls['m'].value;
        if (this.constitutiveModelElement[constitutiveModel].formShansep.controls['is_maximum_shear_strength'].value) {
          dataConstituive.maximum_shear_strength = this.constitutiveModelElement[constitutiveModel].formShansep.controls['maximum_shear_strength'].value;
        }
        if (this.constitutiveModelElement[constitutiveModel].formShansep.controls['is_tensile_strength'].value) {
          dataConstituive.tensile_strength = this.constitutiveModelElement[constitutiveModel].formShansep.controls['tensile_strength'].value;
        }
        dataConstituive.stress_history_type = parseInt(
          this.constitutiveModelElement[constitutiveModel].formShansep.controls['stress_history_type'].value[0].id
        );
        dataConstituive.stress_history_method = parseInt(
          this.constitutiveModelElement[constitutiveModel].formShansep.controls['stress_history_method'].value[0].id
        );
        break;
      default:
        break;
    }

    if (this.edit) {
      dataConstituive['id'] = constitutiveForm.controls['id'].value;
    }

    return dataConstituive;
  }
}
