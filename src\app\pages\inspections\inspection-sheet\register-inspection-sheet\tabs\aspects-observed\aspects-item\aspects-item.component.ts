import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MessagePadroes } from 'src/app/constants/message.constants';
import { Router } from '@angular/router';

@Component({
  selector: 'app-aspects-item',
  templateUrl: './aspects-item.component.html',
  styleUrls: ['./aspects-item.component.scss']
})
export class AspectsItemComponent implements OnInit, OnChanges {
  @ViewChild('modalAttachments') ModalAttachments: any;

  @Input() formGroup: FormGroup;
  @Input() index: string;
  @Input() hierarchy: any;
  @Input() item: any = null;
  @Input() occurrencesLinkable: any = [];

  @Input() public status: number = null;
  @Input() public locked: boolean = false;
  @Input() public view: boolean = false;

  @Output() addOccurrence = new EventEmitter<void>();
  @Output() addOccurrenceAttachment = new EventEmitter<void>();
  @Output() removeOccurrence = new EventEmitter<number>();

  @Output() showMap = new EventEmitter();

  @Output() updateOccurrence = new EventEmitter();

  @Output() formChanged = new EventEmitter<any>();

  public modalAttachmentsProps = {
    occurrenceAttachments: null
  };

  private currentOccurrenceIndex: number;

  public messagePadroes = MessagePadroes;

  public linkErrorFlags: boolean[] = [];

  constructor(private modalService: NgbModal, private router: Router, private fb: FormBuilder) {}

  /**
   * Lifecycle hook chamado ao inicializar o componente.
   */
  ngOnInit(): void {
    this.linkErrorFlags = Array(this.occurrences.length).fill(false);

    const occurrencesArray = this.formGroup.get('occurrences') as FormArray;

    // Observa alterações no campo `response` de cada ocorrência
    occurrencesArray.controls.forEach((occurrence, index) => {
      occurrence.get('response')?.valueChanges.subscribe(() => {
        // Força a reavaliação ao mudar o valor do campo
        this.canLinkOccurrence(this.formGroup.get('id')?.value, index);
      });
    });
  }

  /**
   * Lifecycle hook chamado quando mudanças ocorrem nos inputs vinculados ao componente.
   * @param {SimpleChanges} changes - Mudanças detectadas nos inputs do componente.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.status || changes.locked) {
      const isLocked = this.locked || [2, 3].includes(this.status);

      if (isLocked) {
        this.formGroup.disable(); // Desabilitar todo o formulário
        if (!this.locked) {
          this.enableLatitudeLongitude(); // Habilitar latitude e longitude somente se NÃO estiver locked
        }
      } else {
        this.formGroup.enable(); // Habilitar todo o formulário
      }
    }
  }

  /**
   * Habilita os campos de latitude e longitude.
   */
  private enableLatitudeLongitude(): void {
    const occurrencesArray = this.formGroup.get('occurrences') as FormArray;

    occurrencesArray.controls.forEach((occurrence) => {
      occurrence.get('northing')?.enable(); // Habilitar northing
      occurrence.get('easting')?.enable(); // Habilitar easting
    });
  }

  /**
   * Sincroniza os flags de erro de vínculo com as ocorrências.
   */
  private syncLinkErrorFlagsWithOccurrences(): void {
    const occurrencesArray = this.formGroup.get('occurrences') as FormArray;
    this.linkErrorFlags = Array(occurrencesArray.length).fill(false);
  }

  /**
   * Retorna o FormArray de ocorrências.
   */
  get occurrences(): FormArray {
    return this.formGroup.get('occurrences') as FormArray;
  }

  /**
   * Adiciona uma nova ocorrência.
   */
  onAddOccurrence(): void {
    if ([2, 3].includes(this.status)) {
      return; // Bloqueia a ação se o status for 2 ou 3
    }
    this.updateOccurrence.emit({ action: 'addOccurrence' });
  }

  /**
   * Remove uma ocorrência pelo índice fornecido.
   * @param {number} index - Índice da ocorrência a ser removida.
   */
  onRemoveOccurrence(index: number): void {
    if ([2, 3].includes(this.status)) {
      return; // Bloqueia a ação se o status for 2 ou 3
    }
    this.updateOccurrence.emit({ action: 'removeOccurrence', index: index });
  }

  /**
   * Emite um evento para exibir o mapa.
   * @param {any} occurrence - Ocorrência selecionada.
   * @param {number} occurrenceIndex - Índice da ocorrência.
   */
  callMap(occurrence, occurrenceIndex) {
    this.showMap.emit({ ...this.item, occurrence: occurrence, occurrenceIndex: occurrenceIndex });
  }

  /**
   * Abre o modal de anexos para uma ocorrência específica.
   * @param {any} modalRef - Referência do modal.
   * @param {number} occurrenceIndex - Índice da ocorrência.
   */
  openModal(modalRef: any, occurrenceIndex: number): void {
    if (modalRef === 'attachments') {
      this.currentOccurrenceIndex = occurrenceIndex; // Salve o índice atual

      const occurrenceControl = this.occurrences.at(occurrenceIndex) as FormGroup;

      // Obtém o FormArray de occurrence_attachments
      const occurrenceAttachments = occurrenceControl.get('occurrence_attachments') as FormArray;

      if (!occurrenceAttachments || !(occurrenceAttachments instanceof FormArray)) {
        console.error('occurrence_attachments is not a valid FormArray');
        return;
      }

      if ((occurrenceAttachments?.value?.length > 0 && this.view) || !this.view) {
        // Passa o FormArray para o modal
        this.modalAttachmentsProps.occurrenceAttachments = occurrenceAttachments;

        // Abre o modal
        this.ModalAttachments.openModal();
      }
    }
  }

  /**
   * Atualiza os anexos de uma ocorrência.
   * @param {FormArray} updatedAttachments - Anexos atualizados.
   */
  onSaveAttachments(updatedAttachments: FormArray): void {
    // Atualizar occurrence_attachments na ocorrência correspondente
    const occurrenceControl = this.occurrences.at(this.currentOccurrenceIndex) as FormGroup;

    // Aqui está o pulo do gato: clona os controles pra quebrar a referência
    const clonedArray = new FormArray(updatedAttachments.controls.map((ctrl) => this.fb.group(ctrl.value)));
    occurrenceControl.setControl('occurrence_attachments', clonedArray);

    this.updateOccurrence.emit({ action: 'addOccurrenceAttachment' });
  }

  /**
   * Verifica se um aspecto está vinculado.
   * @param {string} aspectId - ID do aspecto.
   * @returns {boolean} - Retorna verdadeiro se o aspecto estiver vinculado.
   */
  isAspectLinked(aspectId: string): boolean {
    // Verifica se existe uma ocorrência vinculada ao aspecto com o ID fornecido
    return this.occurrencesLinkable.some((occurrence) => occurrence.aspect.id === aspectId);
  }

  /**
   * Vincula uma ocorrência a um aspecto.
   * @param {string} aspectId - ID do aspecto.
   * @param {number} index - Índice da ocorrência.
   */
  linkOccurrence(aspectId: string, index: number): void {
    if ([2, 3].includes(this.status)) {
      return; // Bloqueia a ação se o status for 2 ou 3
    }

    if (!this.canLinkOccurrence(aspectId, index)) {
      console.warn(`Condição de vínculo não atendida para aspectId: ${aspectId}, index: ${index}`);
      this.linkErrorFlags[index] = true;
      return;
    }

    const linkedOccurrence = this.occurrencesLinkable.find((occurrence) => occurrence.aspect.id === aspectId);

    if (!linkedOccurrence) {
      console.error(`Nenhuma ocorrência vinculável encontrada para aspectId: ${aspectId}`);
      this.linkErrorFlags[index] = true;
      return;
    }

    const occurrencesArray = this.formGroup.get('occurrences') as FormArray;
    const occurrenceControl = occurrencesArray.at(index);

    if (!occurrenceControl) {
      console.error(`Controle da ocorrência não encontrado no índice: ${index}`);
      this.linkErrorFlags[index] = true;
      return;
    }

    occurrenceControl.patchValue({
      linked_to: { id: linkedOccurrence.id, search_identifier: linkedOccurrence.search_identifier }
    });

    this.linkErrorFlags[index] = false; // Nenhum erro ocorreu para esta ocorrência
    this.updateOccurrence.emit({ action: 'addOccurrenceLinked' });
  }

  /**
   * Verifica se uma ocorrência está vinculada.
   * @param {number} index - Índice da ocorrência.
   * @returns {boolean} - Retorna verdadeiro se a ocorrência estiver vinculada.
   */
  isOccurrenceLinked(index: number): boolean {
    const occurrencesArray = this.formGroup.get('occurrences') as FormArray;
    const occurrenceControl = occurrencesArray.at(index);

    // Verifica se o campo `linked_to.id` já está preenchido
    return occurrenceControl?.get('linked_to')?.value?.id ? true : false;
  }

  /**
   * Retorna o identificador da ocorrência vinculada.
   * @param {number} index - Índice da ocorrência.
   * @returns {any} - Identificador da ocorrência vinculada.
   */
  getOccurrenceLink(index: number): any {
    const occurrencesArray = this.formGroup.get('occurrences') as FormArray;
    const occurrenceControl = occurrencesArray.at(index);

    // Retorna o valor completo do campo `linked_to`, ou null se não existir
    return occurrenceControl?.get('linked_to')?.value.search_identifier || null;
  }

  /**
   * Verifica se uma ocorrência pode ser vinculada a um aspecto.
   * @param {string} aspectId - ID do aspecto.
   * @param {number} index - Índice da ocorrência.
   * @returns {boolean} - Retorna verdadeiro se a ocorrência puder ser vinculada.
   */
  canLinkOccurrence(aspectId: string, index: number): boolean {
    // Verifica se o aspecto está vinculado
    if (!this.isAspectLinked(aspectId)) {
      return false;
    }

    // Verifica se a ocorrência já está vinculada
    if (this.isOccurrenceLinked(index)) {
      return false;
    }

    // Verifica se o response do formulário é igual ao response do vínculo
    const occurrencesArray = this.formGroup.get('occurrences') as FormArray;
    const occurrenceControl = occurrencesArray.at(index);
    const responseValue = occurrenceControl.get('response')?.value;

    const linkedOccurrence = this.occurrencesLinkable.find((occurrence) => occurrence.aspect.id === aspectId);

    if (!linkedOccurrence) {
      return false;
    }

    return linkedOccurrence.response === responseValue;
  }

  /**
   * Navega para a ocorrência vinculada.
   * @param {number} index - Índice da ocorrência.
   * @param {Event} event - Evento do clique.
   */
  openOccurrence(index: number, event: Event): void {
    event.preventDefault(); // Impede a navegação padrão do link
    const occurrencesArray = this.formGroup.get('occurrences') as FormArray;
    const occurrenceControl = occurrencesArray.at(index);
    const ocurrence = occurrenceControl?.get('linked_to')?.value;
    this.router.navigate([`inspections/inspection-sheet/occurrence/${ocurrence.id}/view`], { queryParams: { clienteId: this.hierarchy.client.id } });
  }

  /**
   * Emite um evento ao detectar uma alteração no formulário.
   * @param {string} controlName - Nome do campo alterado.
   * @param {number} [index] - Índice da ocorrência, se aplicável.
   */
  onChange(controlName: string, index?: number): void {
    const control = this.occurrences.at(index).get(controlName);
    if (control?.dirty) {
      this.triggerSave();
    }
  }

  /**
   * Emite um evento ao detectar a saída do foco (blur) em um campo do formulário.
   * @param {string} controlName - Nome do campo que perdeu o foco.
   * @param {number} [index] - Índice da ocorrência, se aplicável.
   */
  onBlur(controlName: string, index?: number): void {
    const control = this.occurrences.at(index).get(controlName);
    if (control?.dirty) {
      this.triggerSave();
    }
  }

  /**
   * Emite um evento ao detectar um blur em um campo do formulário.
   */
  triggerSave(): void {
    this.formChanged.emit();
  }
}
