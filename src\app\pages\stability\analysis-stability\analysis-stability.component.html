<div class="list-content">
  <!-- Banner de notificações -->
  <app-alert
    *ngIf="showNotificationBanner"
    [class]="'alert-warning'"
    class="mt-3"
    [messages]="bannerNotifications"
    [showCloseButton]="true"
    [onClose]="handleCloseNotificationBanner.bind(this)"
  ></app-alert>

  <form [formGroup]="formStabilityAnalysis">
    <!-- Filtro Cliente, Unidade, Estrutura -->
    <div class="row mt-2">
      <div class="col-md-3">
        <label class="form-label">ID</label>
        <input
          formControlName="SearchIdentifier"
          type="number"
          step="1"
          min="1"
          class="form-control"
          autocomplete="off"
          placeholder="ID"
        />
      </div>
      <app-hierarchy
        #hierarchy
        [elements]="elements"
        class="col-md-9"
        (sendEventHierarchy)="getEventHierarchy($event)"
      ></app-hierarchy>
      <!-- Seção -->
      <div class="col-md-3">
        <label class="form-label">Seção</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="sectionSettings"
          [data]="sections"
          formControlName="SectionId"
        >
        </ng-multiselect-dropdown>
      </div>
      <!-- Data-->
      <div class="col-md-3">
        <label class="form-label">Data e hora inicial</label>
        <input
          type="datetime-local"
          class="form-control"
          formControlName="StartDate"
        />
      </div>
      <div class="col-md-3">
        <label class="form-label">Data e hora final</label>
        <input
          type="datetime-local"
          class="form-control"
          formControlName="EndDate"
        />
      </div>
      <div class="col-md-3 d-flex align-items-end">
        <app-button
          [class]="'btn-logisoil-blue'"
          [icon]="'fa fa-search'"
          [label]="'Buscar'"
          class="me-1"
          (click)="managerFilters(true)"
        ></app-button>
        <app-button
          [class]="'btn-logisoil-gray'"
          [icon]="'fa fa-eraser'"
          [label]="'Limpar'"
          (click)="resetFilter()"
        ></app-button>
      </div>
    </div>
  </form>
  <div class="row mt-3">
    <div class="col-md-3">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>
  </div>

  <!-- Mensagem de alerta -->
  <div
    class="alert mt-3"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status"
    [innerHTML]="message.text"
  ></div>
  <app-alert
    class="mt-3"
    [class]="'alert-danger'"
    [messages]="messagesError"
  ></app-alert>

  <!-- Tabela -->
  <div class="row mt-3" *ngIf="tableData.length > 0">
    <div class="col-md-12">
      <app-table
        [messageReturn]="message"
        [tableHeader]="tableHeader"
        [tableData]="tableData"
        [permissaoUsuario]="permissaoUsuario"
        [eventRow]="true"
        (sendClickRowEvent)="clickRowEvent($event)"
        [menuMiniDashboard]="'miniDashboardStability'"
      >
      </app-table>
    </div>
  </div>

  <!-- Paginação -->
  <div class="row mt-3" *ngIf="tableData.length > 0">
    <app-paginator
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event)"
      [enableItemPerPage]="true"
    ></app-paginator>
  </div>

  <!-- Voltar -->
  <div class="col-md-12 mt-3 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/stability']"
    ></app-button>
  </div>
</div>

<!-- Visualizar DXF -->
<app-modal-view-dxf
  [title]="'DXF'"
  #modalViewDxf
  [dxfInfo]="dxfInfo"
></app-modal-view-dxf>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
