import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { DataService } from 'src/app/services/data.service';
import { FarolService } from 'src/app/services/farol.service';
import { FilterService } from 'src/app/services/filter.service';
import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { UserService } from 'src/app/services/user.service';

import {
  ActionPlanArea,
  ActionPlanDateFilter,
  ActionPlanStatus,
  ActionPlanSeverity,
  ActionPlanTendency,
  ActionPlanUrgency,
  InspectionSheetType,
  OccurrenceDatePeriod
} from 'src/app/constants/inspections.constants';
import { <PERSON><PERSON>adastro, MessagePadroes, ModalConfirm } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-action-plans',
  templateUrl: './action-plans.component.html',
  styleUrls: ['./action-plans.component.scss']
})
export class ActionPlansComponent implements OnInit {
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalConfirm') ModalConfirm: any;

  inspectionSheetOccurrencesForm: FormGroup = this.fb.group({
    ClientId: [''],
    ClientUnitId: [''],
    StructureId: [''],
    StartDate: [''],
    EndDate: [''],
    Period: [''],
    Status: [''],
    InspectionSheetType: [''],
    DateFilter: ['']
  });

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public actionPlanStatus = ActionPlanStatus;
  public area = ActionPlanArea;
  public actionPlanSeverity = ActionPlanSeverity;
  public actionPlanTendency = ActionPlanTendency;
  public actionPlanUrgency = ActionPlanUrgency;

  public selectedOrigin: string | number = 'all';
  public selectedArea: string | number = 'all';
  public selectedActionPlanStatus: string | number = 'all';

  public structureId: string = '';
  public selectedId: string | null = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public func = fn;

  public filterSearch: any = {};
  public filterParams: any = {};
  public filter: any = {
    StartDate: null,
    EndDate: null,
    AllDates: false,
    Period: '',
    DaysRemaining: 0,
    Status: 'all',
    InspectionSheetType: 'all',
    SearchIdentifier: ''
  };

  public dateFilter = ActionPlanDateFilter;
  public datePeriod = OccurrenceDatePeriod;
  public disableDateFields: boolean = true;

  public inspectionSheetType = InspectionSheetType;

  public selectedDateFilter: string | number = '';
  public selectedStatus: string | number = 'all';

  public tableData: Array<{ [key: string]: any }> = [];
  public tableHeader: any = [
    {
      label: 'ID',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Ações',
      width: '160px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['recommendation']
    },
    {
      label: 'Ficha',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['inspection_sheet_search_identifier'],
      type: 'link'
    },
    {
      label: 'Ocorrência/Registro',
      width: '140px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['occurrence_search_identifier'],
      type: 'link'
    },
    {
      label: 'Farol',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['farol'],
      extra: true
    },
    {
      label: 'GUT',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['gut'],
      type: 'gut'
    },
    {
      label: 'Origem',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['inspection_sheet_type']
    },
    {
      label: 'Área',
      width: '100px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['area']
    },
    {
      label: 'Data de Abertura',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Data de Conclusão',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['completion_date']
    },
    {
      label: 'Status',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['status']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['miniDashboard']
    }
  ];

  public selectedColumns = this.tableHeader;
  public viewSettings = MultiSelectDefault.View;

  public clickEventsubscription: Subscription;

  public modalData: any = {};
  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  constructor(
    private route: ActivatedRoute,
    private dataService: DataService,
    private farolService: FarolService,
    private fb: FormBuilder,
    private filterService: FilterService,
    private inspectionSheetServiceApi: InspectionSheetServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private toastr: ToastrService,
    private userService: UserService
  ) {}

  /**
   * Executado ao inicializar o componente.
   * Obtém informações do perfil do usuário e permissões associadas.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.route.queryParams.subscribe((params) => {
      this.selectedId = params['selectedId'] || null;
    });
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros.
   */
  ngAfterViewInit(): void {
    this.ngxSpinnerService.show();
    setTimeout(() => {
      // Aplica filtros e carrega os dados automaticamente
      if (this.filter) {
        this.searchActionPlans();
      }
      this.ngxSpinnerService.hide(); // Oculta o spinner após os dados serem carregados
    }, 1000);
  }

  /**
   * Extrai os filtros de hierarquia da estrutura selecionada.
   * @returns {any} - Objeto contendo os filtros de hierarquia.
   */
  extractHierarchyFilters(): any {
    const hierarchyFilters = this.hierarchy.getFilters();

    return {
      ClientId: hierarchyFilters?.clients?.[0]?.id || '',
      ClientUnitId: hierarchyFilters?.units?.[0]?.id || '',
      StructureId: hierarchyFilters?.structures?.[0]?.id || ''
    };
  }

  /**
   * Obtém a lista de planos de ação a partir dos parâmetros fornecidos.
   * @param {any} params - Parâmetros da consulta.
   */
  getActionPlanList(params: any): void {
    this.ngxSpinnerService.show();

    this.message.text = '';
    this.message.status = false;

    this.inspectionSheetServiceApi.getActionPlans(params).subscribe(
      (resp) => {
        const dados: any = resp;
        if (dados?.status === 200) {
          this.tableData = dados.body?.data ? this.formatActionPlanData(dados.body.data) : [];
          this.collectionSize = dados.body?.total_items_count || 0;
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.message.text = MessagePadroes.NoRegister;
          this.message.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.message.status = false;
          }, 4000);
        }

        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.error('Erro ao buscar planos de ação:', error);
        this.message.status = true;
        this.message.class = 'alert-danger';
        this.tableData = [];
        this.collectionSize = 0;
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Realiza a busca dos planos de ação aplicando os filtros selecionados.
   */
  searchActionPlans(): void {
    this.filterParams = {
      Status: this.selectedActionPlanStatus !== 'all' ? this.selectedActionPlanStatus : '',
      InspectionSheetType: this.selectedOrigin !== 'all' ? this.selectedOrigin : '',
      Area: this.selectedArea !== 'all' ? this.selectedArea : '',
      ...this.extractHierarchyFilters(),
      Page: this.page,
      PageSize: this.pageSize
    };

    // Lógica de datas
    if (this.selectedDateFilter !== '' && this.selectedDateFilter !== 'noFilter') {
      this.filterParams.DateFilter = this.selectedDateFilter;

      if (this.selectedDateFilter == 3) {
        this.filterParams.DaysRemaining = this.filter.DaysRemaining || '';
      } else {
        this.filterParams.StartDate = this.filter.StartDate || '';
        this.filterParams.EndDate = this.filter.EndDate || '';
      }
    }

    this.filterService.setFilters(this.filterParams, this.hierarchy.getFilters());

    // Busca planos de ação
    this.getActionPlanList(this.filterParams);
  }

  /**
   * Reseta todos os filtros aplicados na tela de Planos de Ação,
   * incluindo hierarquia, filtros manuais e dados exibidos na tabela.
   */
  resetFilter(): void {
    // Zera hierarquia (Cliente, Unidade, Estrutura)
    this.hierarchy.resetFilters();

    // Reseta filtros manuais
    this.filter = {
      StartDate: '',
      EndDate: '',
      Period: '',
      DaysRemaining: 0,
      Status: 'all',
      InspectionSheetType: 'all',
      SearchIdentifier: ''
    };

    // Reseta controle interno de filtros
    this.selectedDateFilter = '';
    this.selectedOrigin = 'all';
    this.selectedArea = 'all';
    this.selectedActionPlanStatus = 'all';
    this.selectedColumns = [...this.tableHeader]; // Reseta seleção de colunas
    this.disableDateFields = true;

    // Garante que todas as colunas fiquem visíveis
    this.tableHeader.forEach((col) => (col.show = true));

    // Reseta parâmetros e filtros salvos no serviço
    this.filterParams = {};
    this.filterSearch = {};
    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());

    // Limpa mensagens
    this.message.status = false;

    // Reseta paginação
    this.page = 1;

    // Executa nova busca com filtros zerados
    this.searchActionPlans();
  }

  /**
   * Formata os dados brutos dos planos de ação para exibição na tabela.
   * @param {any[]} data - Lista de planos de ação.
   * @returns {any[]} - Lista formatada.
   */
  formatActionPlanData(data: any[]): any[] {
    return data.map((item) => {
      return {
        id: item.id,
        search_identifier: item.search_identifier,
        recommendation: item.recommendation || '-',
        inspection_sheet_search_identifier: item.inspection_sheet_search_identifier,
        inspection_sheet_id: item.inspection_sheet_id,
        inspection_sheet_type: InspectionSheetType.find((type) => type.value === item.inspection_sheet_type)?.label || 'Desconhecido',
        occurrence_search_identifier: item.occurrence_search_identifier,
        occurrence_id: item.occurrence_id,
        area: ActionPlanArea.find((area) => area.value === item.area)?.label || 'Desconhecida',
        created_date: item.created_date ? moment(item.created_date).format('DD/MM/YYYY') : '-',
        created_by: item.created_by,
        completion_date: item.completion_date ? moment(item.completion_date).format('DD/MM/YYYY') : '-',
        gut: item.gut,
        status: ActionPlanStatus.find((status) => status.value === item.status)?.label || 'Desconhecido',
        extra: {
          farol: this.farolService.generateFarol(item.gut),
          gut: this.generateGutBadge(item.gut)
        },
        structure_id: item.structure_id
      };
    });
  }

  /**
   * Gera o HTML de um badge para exibir o valor do GUT (Gravidade, Urgência e Tendência).
   *
   * @param gut Valor do GUT.
   * @returns HTML string formatada com o badge de GUT.
   */
  generateGutBadge(gut: number): string {
    if (gut === null || gut === undefined) return '-';

    return `
    <span class="badge-gut" title="GUT = Gravidade + Urgência + Tendência. Quanto maior, mais crítica a situação.">
      ${gut}
    </span>
  `;
  }

  /**
   * Calcula o período de datas com base em um valor numérico e atualiza os campos `StartDate` e `EndDate` do filtro.
   *
   * @param {number} value - Valor selecionado que representa o intervalo de tempo:
   *   - 1 = Último mês
   *   - 2 = Últimos 3 meses
   *   - 3 = Últimos 6 meses
   *   - 4 = Últimos 12 meses
   *   - 5 = Últimos 24 meses
   *   - 6 = Últimos 60 meses
   *   - 8 = Período personalizado (libera campos)
   * @param {boolean} [reset=true] - Se `true`, limpa os campos antes de calcular (útil para mudanças de seleção).
   */
  calculatePeriod(value: number, reset = true): void {
    const today = moment();
    this.disableDateFields = parseInt(value.toString()) !== 8;

    if (reset) {
      this.filter.StartDate = '';
      this.filter.EndDate = '';
    }

    if ([1, 2, 3, 4, 5, 6].includes(parseInt(value.toString()))) {
      // Define EndDate como hoje
      this.filter.EndDate = today.format('YYYY-MM-DD');

      // Subtrai a quantidade de meses com base no valor selecionado
      let monthsToSubtract = 0;
      switch (parseInt(value.toString())) {
        case 1:
          monthsToSubtract = 1;
          break;
        case 2:
          monthsToSubtract = 3;
          break;
        case 3:
          monthsToSubtract = 6;
          break;
        case 4:
          monthsToSubtract = 12;
          break;
        case 5:
          monthsToSubtract = 24;
          break;
        case 6:
          monthsToSubtract = 60;
          break;
      }
      this.filter.StartDate = today.clone().subtract(monthsToSubtract, 'months').format('YYYY-MM-DD');

      // Desabilita campos se estiver usando [(ngModel)] com disable no HTML
      // this.disableDateFields = true; // use uma variável no HTML para controlar o disable
    } else if (parseInt(value.toString()) === 8) {
      // Personalizado: libera os campos pro usuário
      this.filter.StartDate = '';
      this.filter.EndDate = '';
      // this.disableDateFields = false;
    }
  }

  /**
   * Gerencia eventos relacionados à hierarquia de unidades, estruturas e instrumentos.
   * @param {any} $event - O evento disparado pela hierarquia.
   */
  getEventHierarchy($event) {}

  /**
   * Verifica se uma estrutura foi selecionada na hierarquia.
   * @returns {boolean} - `true` se uma estrutura estiver selecionada, caso contrário `false`.
   */
  public get isStructureSelected(): boolean {
    const hierarchyFilters = this.hierarchy?.getFilters();
    return !!hierarchyFilters?.clients?.[0]?.id && !!hierarchyFilters?.units?.[0]?.id && !!hierarchyFilters?.structures?.[0]?.id;
  }

  /**
   * Obtém a estrutura selecionada e abre o modal de inserção de ficha de inspeção.
   * @returns {any} - Estrutura selecionada.
   */
  public getSelectedStructure(): void {
    const hierarchyFilters = this.hierarchy?.getFilters();
    const structureId = hierarchyFilters?.structures?.[0]?.id || null;

    if (!structureId) {
      this.toastr.error('Selecione uma estrutura antes de continuar.');
      return;
    }

    if (this.permissaoUsuario.newActionPlan && this.isStructureSelected) {
      this.router.navigate(['inspections/action-plan/newActionPlan'], {
        queryParams: { structureId }
      });
    }
  }

  /**
   * Método para tratar eventos de clique nas linhas da tabela.
   * Redireciona para a página apropriada com base na ação do evento.
   * @param {any} $event - O evento de clique na linha.
   */
  clickRowEvent($event: any = null): void {
    const row = this.tableData.find((item) => item.id === $event.id);

    switch ($event.action) {
      case 'link':
        this.handleLinkAction($event, row);
        break;

      case 'editActionPlan':
        this.router.navigate([`/inspections/action-plan/${$event.id}/edit`], {
          queryParams: { structureId: row.structure_id }
        });
        break;

      case 'viewActionPlan':
        this.router.navigate([`/inspections/action-plan/${$event.id}/view`], {
          queryParams: { structureId: row.structure_id }
        });
        break;

      case 'deleteActionPlan':
        this.modalTitle = 'Excluir Plano de Ação';
        this.modalMessage = ModalConfirm.ExcluirPlanoDeAcao;
        this.modalInstruction = null;
        this.modalConfig = { iconHeader: null, action: 'confirmDelete' };
        this.modalData = { id: $event.id };
        this.ModalConfirm.openModal();
        break;

      case 'confirmDelete':
        this.deleteActionPlan($event.data.id);
        break;
    }
  }

  /**
   * Abre o link de ficha ou ocorrência em nova aba.
   */
  private handleLinkAction($event: any, row: any): void {
    if ($event?.column === 'inspection_sheet_search_identifier') {
      const url = this.router.serializeUrl(this.router.createUrlTree([`/inspections/inspection-sheet/${row.inspection_sheet_id}/view`]));
      window.open(url, '_blank');
    }
    if ($event?.column === 'occurrence_search_identifier') {
      const url = this.router.serializeUrl(this.router.createUrlTree([`/inspections/inspection-sheet/occurrence/${row.occurrence_id}/view`]));
      window.open(url, '_blank');
    }
  }

  /**
   * Exclui um plano de ação pelo seu identificador e atualiza a listagem.
   *
   * @param actionPlanId Identificador do plano de ação a ser excluído.
   */
  deleteActionPlan(actionPlanId: string) {
    this.ngxSpinnerService.show();

    this.inspectionSheetServiceApi.deleteActionPlan(actionPlanId).subscribe((resp) => {
      this.searchActionPlans();

      setTimeout(() => {
        this.message.text = MessageCadastro.DeleteActionPlan;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
      }, 500);
    });
  }

  /**
   * Define as ações disponíveis para o miniDashboard com base no status e permissões.
   * Usado pelo componente <app-table>.
   */
  getActionButtons(row: any): any[] {
    const actions = [];

    // Ação: Visualizar
    if (row.status === 'Concluída') {
      actions.push({
        icon: 'fas fa-eye',
        label: 'Visualizar',
        action: 'viewActionPlan'
      });
    }

    // Ação: Editar e Validar (oculta apenas se estiver Concluída)
    if (row.status !== 'Concluída') {
      if (this.profile.level != 1) {
        actions.push({
          icon: 'fas fa-edit',
          label: 'Editar',
          action: 'editActionPlan'
        });
      }
    }

    // Ação: Excluir
    if (this.profile.level == 6) {
      actions.push({
        icon: 'fas fa-trash-alt',
        label: 'Excluir',
        action: 'deleteActionPlan'
      });
    }

    return actions;
  }

  /**
   * Abre um modal de referência se for válido.
   * @param {any} modalRef - Referência do modal a ser aberto.
   */
  openModal(modalRef: any): void {
    if (modalRef && modalRef.openModal) {
      modalRef.openModal();
    } else {
      console.error('Modal referência inválida ou método não encontrado.');
    }
  }

  /**
   * Alterna a exibição de colunas da tabela de acordo com o evento de seleção ou deseleção.
   * @param {any} $event - Evento acionado ao selecionar ou deselecionar colunas.
   * @param {string} type - Tipo de ação realizada (selecionar, deselecionar, selecionar todas, etc).
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }
    this.searchActionPlans();
  }
}
