<div class="list-content">
  <div class="button-report">
    <!-- Emitir Relatório -->
    <app-button
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fa fa-file-text-o'"
      [label]="'Emitir Relatório'"
      [routerLink]="['sendReport']"
      class="me-2"
      *ngIf="permissaoUsuario?.create"
    ></app-button>

    <!-- Criar Agendamento -->
    <app-button
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fa fa-calendar-plus-o'"
      [label]="'Criar Agendamento'"
      [routerLink]="['createNewSchedule']"
      *ngIf="permissaoUsuario?.create"
    ></app-button>
  </div>

  <form [formGroup]="formFilter">
    <div class="row mt-1">
      <!-- Tipos de Relatórios -->
      <div class="col-md-3" *ngIf="permissaoUsuario?.create">
        <label class="form-label">Tipo</label>
        <select class="form-select" formControlName="SubjectType">
          <ng-template ngFor let-item [ngForOf]="subjectType">
            <option [ngValue]="item.value">
              {{ item.label }}
            </option>
          </ng-template>
        </select>
      </div>
    </div>

    <!-- Alerta -->
    <div
      class="col-md-12 mt-2 alert"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
    >
      {{ message.text }}
    </div>

    <!-- Tabela -->
    <div class="row mt-3" *ngIf="tableData.length > 0">
      <app-table
        [messageReturn]="messageReturn"
        [tableHeader]="tableHeader"
        [tableData]="tableData"
        (sendClickRowEvent)="clickRowEvent($event)"
        [permissaoUsuario]="permissaoUsuario"
        [menuMiniDashboard]="'MiniDashboardReports'"
      ></app-table>
    </div>

    <!-- Botão Voltar -->
    <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [label]="'Voltar à tela inicial'"
        [icon]="'fa fa-arrow-left'"
        [click]="goBack.bind(this)"
        class="me-1"
      ></app-button>
    </div>
  </form>
</div>

<!-- Confirmar exclusao do agendamento -->
<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
  [data]="modalData"
></app-modal-confirm>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
