import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListSectionsComponent } from './list-sections/list-sections.component';
import { RegisterSectionComponent } from './register-section/register-section.component';
import { HistorySectionComponent } from './history-section/history-section.component';

import { AppGuard } from './../../guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListSectionsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastrarSecao,
    component: RegisterSectionComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarSecao,
    component: RegisterSectionComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarSecao,
    component: RegisterSectionComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.HistoricoSecao,
    component: HistorySectionComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SectionsRoutingModule {}
