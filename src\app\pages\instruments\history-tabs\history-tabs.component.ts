import { Component, OnInit, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-history-tabs',
  templateUrl: './history-tabs.component.html',
  styleUrls: ['./history-tabs.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HistoryTabsComponent implements OnInit {
  public instrumentTabConfig: any = { styleColor: false, active: true };
  public readingTabConfig: any = { styleColor: false, active: false };

  constructor() {}

  ngOnInit(): void {}

  selectTab(option: any = '') {
    switch (option) {
      case 'instrument':
        this.instrumentTabConfig.active = true;
        this.readingTabConfig.active = false;
        break;
      case 'reading':
        this.instrumentTabConfig.active = false;
        this.readingTabConfig.active = true;
        break;
      default:
        break;
    }
  }
}
