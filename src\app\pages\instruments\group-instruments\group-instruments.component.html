<div class="list-content">
  <form [formGroup]="formGroupInstruments" (ngSubmit)="validate()">
    <div
      class="alert alert-success mt-3"
      role="alert"
      *ngIf="message.status && action != 'delete'"
    >
      {{ message.text }}
    </div>
    <div class="row g-3 mt-1">
      <!-- Cliente -->
      <div class="col-md-3">
        <label class="form-label">Cliente</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="clientSettings"
          [data]="clients"
          (onSelect)="getUnits($event, 'select')"
          (onDeSelect)="getUnits($event, 'deselect')"
          formControlName="client"
          [disabled]="editGroup"
        >
        </ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formGroupInstruments.get('client').valid &&
            formGroupInstruments.get('client').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Unidade -->
      <div class="col-md-3">
        <label class="form-label">Unidade</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="unitSettings"
          [data]="units"
          (onSelect)="getStructures($event, 'select')"
          (onDeSelect)="getStructures($event, 'deselect')"
          formControlName="client_unit"
          [disabled]="editGroup"
        >
        </ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formGroupInstruments.get('client_unit').valid &&
            formGroupInstruments.get('client_unit').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Estrutura -->
      <div class="col-md-3">
        <label class="form-label">Estrutura</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="structureSettings"
          [data]="structures"
          (onSelect)="managerStructure('select')"
          (onDeSelect)="managerStructure('deselect')"
          formControlName="structure"
          [disabled]="editGroup"
        >
        </ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formGroupInstruments.get('structure').valid &&
            formGroupInstruments.get('structure').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Criar grupo -->
      <div class="col-md-2 align-self-end">
        <app-button
          [class]="'btn-logisoil-green'"
          [customBtn]="true"
          [icon]="'fa fa-thin fa-object-group'"
          [label]="'Criar Grupo'"
          data-bs-toggle="tooltip"
          data-bs-placement="bottom"
          (click)="resetForm('group', !view)"
          [disabled]="view"
          *ngIf="!editGroup"
        ></app-button>
      </div>

      <!-- Alertas -->
      <div
        class="alert alert-warning"
        role="alert"
        *ngIf="
          messageReturn.status &&
          formGroupInstruments.controls['structure'].value != ''
        "
      >
        {{ messageReturn.text }}
      </div>
      <!-- Alertas -->
    </div>

    <div
      class="row mt-3"
      [style]="
        ctrlGroup ? 'visibility: visible' : 'visibility: hidden; height: 0'
      "
    >
      <!-- Aba Novo Grupo  -->
      <ul class="nav nav-tabs px-2">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page">Novo Grupo</a>
        </li>
      </ul>
      <div class="col-md-3 mt-2">
        <label class="form-label">Nome</label>
        <input
          type="text"
          class="form-control"
          formControlName="name"
          autocomplete="off"
          maxlength="32"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formGroupInstruments.get('name').valid &&
            formGroupInstruments.get('name').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div class="col-md-12">
        <label class="form-title mt-2"
          >Instrumentos que farão parte do grupo:</label
        >
      </div>
      <div class="row">
        <div class="col-md-12">
          <small
            class="form-text text-danger"
            *ngIf="
              !formGroupInstruments.get('amountInstruments').valid &&
              formGroupInstruments.get('amountInstruments').value == 0 &&
              formGroupInstruments.get('amountInstruments').touched
            "
            >Selecione ao menos um instrumento</small
          >
        </div>
      </div>

      <div class="col-md-12">
        <div class="row">
          <ng-template
            ngFor
            let-typeInstrumentItem
            [ngForOf]="listTypeInstruments"
            let-i="index"
          >
            <div class="col-md-3 mt-2">
              <app-list-type-instruments
                #listInstrumentRef
                [edit]="edit"
                [view]="view"
                [data]="typeInstrumentItem"
                [item]="typeInstrumentItem.item"
                [index]="i"
                [type]="typeInstrumentItem.type"
                (sendInstruments)="getInstrumentsInList($event)"
              ></app-list-type-instruments>
            </div>
          </ng-template>
        </div>
      </div>
      <div class="row mt-2">
        <!-- Mensagem de erro -->
        <app-alert
          [class]="'alert-danger'"
          [messages]="messagesError"
        ></app-alert>
      </div>
      <!-- Botões -->
      <div class="col-md-12 d-flex justify-content-end mb-3">
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-floppy-disk'"
          [label]="!editGroup ? 'Salvar' : 'Editar'"
          [type]="false"
          class="me-1"
          [disabled]="!formGroupInstruments.valid"
        >
        </app-button>
        <app-button
          [class]="'btn-logisoil-red'"
          [icon]="'fa fa-thin fa-xmark'"
          [label]="'Cancelar'"
          [type]="true"
          (click)="ctrlGroup = false; resetForm('group')"
        >
        </app-button>
      </div>
    </div>

    <!-- Mapa -->
    <div class="row g-3 mt-2 mb-3">
      <div class="col-md-12">
        <app-button
          [class]="'btn-logisoil-green me-2'"
          [customBtn]="true"
          [icon]="'fas fa fa-map-location-dot'"
          [label]="'Mapa'"
          [ngbTooltip]="'Expandir/recolher o mapa'"
          [tooltipClass]="'info-tooltip'"
          [placement]="'end'"
          (click)="showMaps = !showMaps"
        ></app-button>
        <app-button
          [class]="'btn-logisoil-green me-2'"
          [customBtn]="true"
          [icon]="'fa fa-refresh'"
          [label]="'Recarregar mapa'"
          (click)="refreshGroupInstruments()"
        ></app-button>
        <app-button
          [class]="'btn-logisoil-red'"
          [customBtn]="true"
          [icon]="'fa fa-bullseye'"
          [label]="'Remover pulso'"
          (click)="removePulse()"
        ></app-button>
      </div>
    </div>
    <div class="row g-3">
      <div class="col-md-12 maps" [ngClass]="showMaps ? 'show' : ''">
        <app-google-maps
          [height]="showMaps ? '500px' : '0px'"
        ></app-google-maps>
        <div *ngIf="showMaps">
          <label class="list-instruments mt-2">Legenda:</label>
          <div class="row">
            <div class="col-auto" *ngFor="let item of groupInstruments">
              <i class="{{ item.icon }}" [style.color]="item.color"></i>
              <span class="legend ms-2">{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Alerta -->
    <div class="row g-3 mt-2" *ngIf="message.status && action == 'delete'">
      <div class="col-md-12 alert alert-success" role="alert">
        {{ message.text }}
      </div>
    </div>

    <!-- Grupo cadastrados -->
    <div class="row g-3 mt-3" *ngIf="tableData.length > 0">
      <div class="col-md-6">
        <app-table
          [messageReturn]="messageReturn"
          [tableHeader]="tableHeader"
          [tableData]="tableData"
          [permissaoUsuario]="permissaoUsuario"
          [actionCustom]="actionCustom"
          (sendClickRowEvent)="clickRowEvent($event)"
        ></app-table>

        <app-paginator
          [collectionSize]="collectionSize"
          [page]="page"
          [maxSize]="10"
          [boundaryLinks]="true"
          [pageSize]="pageSize"
          (sendPageChange)="loadPage($event)"
          [enableItemPerPage]="true"
        ></app-paginator>
      </div>
    </div>

    <!-- Botão Voltar -->
    <div class="row g-3 mt-3">
      <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
        <app-button
          [class]="'btn-logisoil-blue'"
          [icon]="'fa fa-arrow-left'"
          [label]="'Voltar à tela principal'"
          [routerLink]="['/instruments']"
        ></app-button>
      </div>
    </div>
  </form>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
