import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';
import { MultiSelectDefault, Status } from 'src/app/constants/app.constants';
import { groupInstruments } from 'src/app/constants/instruments.constants';

import { ClientUnitService as ClientUnitServiceApi } from 'src/app/services/api/clientUnit.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';

import { CoordinateService } from 'src/app/services/coordinate.service';
import { DataService } from 'src/app/services/data.service';
import { FilterService } from 'src/app/services/filter.service';
import { UserService } from 'src/app/services/user.service';

import { GoogleMapsComponent } from 'src/app/components/google-maps/google-maps.component';

import fn from 'src/app/utils/function.utils';

import { NgxSpinnerService } from 'ngx-spinner';

import { SharedService } from 'src/app/services/shared.service';
import { Subject, Subscription } from 'rxjs';

@Component({
  selector: 'app-list-sections',
  templateUrl: './list-sections.component.html',
  styleUrls: ['./list-sections.component.scss']
})
export class ListSectionsComponent implements OnInit {
  @ViewChild(GoogleMapsComponent) googleMaps: GoogleMapsComponent;
  @ViewChild('hierarchy') hierarchy: any;

  public tableHeader: any = [
    {
      label: 'ID',
      width: '50px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Seção',
      width: '100px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'Estrutura',
      width: '200px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['structure']
    },
    {
      label: 'Revisão',
      width: '50px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['reviews_counter']
    },
    {
      label: 'Status',
      width: '40px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['active']
    },
    {
      label: 'INA`s',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['active_wli_counter']
    },
    {
      label: 'PZ`s',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['active_pz_counter']
    },
    {
      label: 'Azimute/Esconsa (graus)',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['azimuth']
    },
    {
      label: 'Ações',
      width: '40px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['miniDashboard']
    }
  ];

  public tableData: any = [];
  public selectedColumns = this.tableHeader;

  public instruments: any = [];
  public sections: any = [];

  public status: any = Status;

  public func = fn;

  public section: any = {
    id: null,
    name: null,
    active: null
  };

  public sectionSettings = MultiSelectDefault.Sections;
  public viewSettings = MultiSelectDefault.View;

  public message: any = [{ text: '', status: false }];
  public messageReturn: any = [{ text: '', status: false }];

  public filterParams: any = {};
  public filterBodyParams: any = {
    section_ids: [],
    structure_ids: []
  };

  public filter: any = {
    SearchIdentifier: '',
    Identifier: '',
    SectionId: [],
    Active: ''
  };

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public dataMapsSection = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  public centerDefault = { lat: -17.930178, lng: -43.7908453 };
  public coordinateSections: any = [];

  //Exibir instrumentos no mapa
  public groupInstruments: any = groupInstruments;
  public intervalId: any = null;
  public document: any = null;
  public clickEventsubscription: Subscription;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public filterSearch: any = {};
  public filterSearchBody: any = {};

  can(action: string): boolean {
    return !!this.permissaoUsuario?.[action];
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private clientUnitServiceApi: ClientUnitServiceApi,
    private coordinateService: CoordinateService,
    private dataService: DataService,
    private filterService: FilterService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private sectionsServiceApi: SectionsServiceApi,
    private structuresServiceApi: StructuresServiceApi,
    private userService: UserService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private sharedService: SharedService
  ) {}

  /**
   * Método de inicialização do componente.
   * Carrega o perfil do usuário, permissões e gerencia os filtros e seções.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    let queryParams = this.activatedRoute.snapshot.queryParams;
    if (queryParams.structure_id && queryParams.structure_id != '') {
      this.callFromStructure(queryParams.structure_id);
    } else {
      this.ngxSpinnerService.show();
    }

    this.sharedService.subject = new Subject<any>();
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros.
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
    }, 1000);
  }

  /**
   * Método para buscar as seções de uma estrutura específica.
   * @param {any} structure - Estrutura selecionada.
   * @param {string} action - Ação de seleção ou desseleção.
   */
  getSections(structure, action: string = 'select') {
    if (action === 'select') {
      this.sectionsServiceApi.getSectionList({ structureId: structure.id }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.sections = dados;
      });
    }

    let filterHierarchy = this.hierarchy.getFilters();

    this.mapsStructure(filterHierarchy.clients[0].id, structure.id);
    this.mapsSection(structure.id);
  }

  /**
   * Método para obter a lista de tipos de instrumentos de uma estrutura.
   * @param {string} structureId - ID da estrutura.
   */
  getListTypeInstruments(structureId: string) {
    this.instruments = [];

    this.instrumentsServiceApi.getGroupInstrumentsMaps({ StructureId: structureId }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.instruments = dados;

      setTimeout(() => {
        this.plotInstruments();
      }, 500);
    });
  }

  /**
   * Método para obter a lista de seções a partir dos parâmetros de filtro.
   * @param {any} params - Parâmetros de filtro para a busca.
   * @param {any} bodyParams - Parâmetros adicionais para a busca.
   */
  getSectionsList(params, bodyParams) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.sectionsServiceApi.postSectionsSearch(bodyParams, params).subscribe(
      (resp) => {
        let dados: any = resp;

        if (dados != null && dados != undefined) {
          dados = dados.body === undefined ? dados : dados.body;

          if (dados) {
            this.tableData = dados ? dados.data : [];
            this.collectionSize = dados.total_items_count;
            this.formatData();
          } else {
            this.tableData = [];
            this.collectionSize = 0;
            this.messageReturn.text = MessagePadroes.NoRegisterClient;
            this.messageReturn.status = true;

            setTimeout(() => {
              this.messageReturn.status = false;
            }, 4000);
          }
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegisterClient;
          this.messageReturn.status = true;

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status === 500) {
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  //Método para pesquisar seções com base nos filtros aplicados.
  searchSection() {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterBodyParams.structure_ids = [];
    this.filterBodyParams.section_ids = [];

    this.filterParams = {
      SearchIdentifier: this.filter.SearchIdentifier,
      Identifier: this.filter.Identifier,
      ClientId: filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '',
      ClientUnitId: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '',
      StructureId: filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].id : '',
      Active: this.filter.Active
    };

    if (filterHierarchy.structures && filterHierarchy.structures.length > 0) {
      this.filterBodyParams.structure_ids.push(filterHierarchy.structures[0].id);
    }

    if (Array.isArray(this.filter.SectionId) && this.filter.SectionId.length > 0) {
      this.filterBodyParams.section_ids = this.filter.SectionId.map((item: any) => {
        let id = item.id;
        return id;
      });
    }

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterSearch['Section'] = this.filter.SectionId;
    this.filterSearch['coordinateSections'] = this.coordinateSections;

    this.filterSearchBody = {
      ...this.filterBodyParams
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters(), this.filterSearchBody);

    this.getSectionsList(this.filterSearch, this.filterSearchBody);
  }

  /**
   * Método para gerenciar eventos da hierarquia (clientes, unidades, estruturas).
   * @param {any} $event - Evento disparado pela hierarquia.
   */
  getEventHierarchy($event) {
    switch ($event.type) {
      case 'units':
        this.sections = [];
        this.filter.SectionId = '';
        break;
      case 'structures':
        this.sections = [];
        this.filter.SectionId = '';
        if ($event.element != null) {
          this.getSections($event.element, $event.action);
        }
        break;
    }
    if ($event.action === 'deselect') {
      this.sections = [];
      this.filter.SectionId = [];
    }
  }

  /**
   * Método para formatar os dados exibidos na tabela.
   * Concatena valores e ajusta o formato dos dados.
   */
  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      let itemSection = item;

      if (item.structure) {
        itemSection['structure'] = item.structure.name;
      }
      itemSection['azimuth'] = item.normal_line_azimuth.toFixed(2) + (item.skew_line_azimuth ? ' / ' + item.skew_line_azimuth.toFixed(2) : '');

      return item;
    });
  }

  /**
   * Método para alternar o status ativo/inativo de uma seção.
   * @param {any} section - A seção cujo status será alterado.
   */
  toggleStatus(section: any) {
    this.section.id = section.id;
    this.section.active = section.active;
    this.editSection();
  }

  //Método para editar uma seção específica.
  editSection() {
    this.sectionsServiceApi.patchSections(this.section.id, this.section).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.AlteracaoStatus;
      this.message.status = true;

      setTimeout(() => {
        this.message.status = false;
        this.router.navigate(['/sections']);
      }, 4000);
    });
  }

  //Método para redefinir os filtros aplicados na pesquisa.
  resetFilter() {
    this.hierarchy.resetFilters();

    this.filter = {
      SearchIdentifier: '',
      Identifier: '',
      SectionId: [],
      Active: ''
    };

    this.sections = [];

    this.filterParams = {};
    this.filterBodyParams.structure_ids = [];
    this.filterBodyParams.section_ids = [];

    this.filterSearch = {};
    this.filterSearchBody = {};

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters(), this.filterSearchBody);

    this.managerFilters();

    this.googleMaps.clearMap();
  }

  /**
   * Método para gerenciar os filtros, executando a busca e carregando os resultados.
   * @param {boolean} $btn - Indica se a ação foi iniciada por um botão.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchSection();
    } else {
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchSection();
      } else {
        this.filterSearch = data.filters;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;
        this.filterSearchBody = data.filtersBody;

        //Formulario do filtro
        this.filter.Identifier = this.filterSearch.Identifier;
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier;
        this.filter.Active = this.filterSearch.Active;
        this.filter.ClientId = this.filterSearch.ClientId;
        this.filter.ClientUnitId = this.filterSearch.ClientUnitId;
        this.filter.StructureId = this.filterSearch.StructureId;
        this.filter.SectionId = this.filterSearch.Section;

        this.getSectionsList(this.filterSearch, this.filterSearchBody);
      }

      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
        this.hierarchy.setUnits(data.filtersHierarchy.units);
        this.hierarchy.setStructures(data.filtersHierarchy.structures);

        if (data.filtersHierarchy.structures && data.filtersHierarchy.structures[0] !== undefined) {
          this.coordinateSections = this.filterSearch.coordinateSections;
          this.getSections(data.filtersHierarchy.structures[0]);
        }

        if (this.filterSearch.Section && this.filterSearch.Section.length > 0) {
          this.filterSearch.Section.forEach((section) => {
            this.plotSections(section);
          });
        }
      }
    }
  }

  /**
   * Método para alternar a exibição das colunas da tabela.
   * @param {any} $event - Evento que contém as informações da coluna.
   * @param {string} type - Tipo de ação (selecionar, desselecionar, selecionar tudo, desselecionar tudo).
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Método para tratar eventos de clique nas linhas da tabela.
   * Redireciona para a página apropriada com base na ação do evento.
   * @param {any} $event - O evento de clique na linha.
   */
  clickRowEvent($event: any = null) {
    const routerLink = $event.routerLink.split('?')[0];

    const actionLabels: any = {
      edit: 'editar',
      history: 'visualizar'
    };

    const phraseEndings: any = {
      edit: 'esta Seção.',
      history: 'o histórico desta Seção.'
    };

    const isRestrictedAction = ['edit', 'history'].includes($event.action);
    const hasPermission = this.can($event.action);

    if (isRestrictedAction && !hasPermission) {
      this.showPermissionAlert(`Você não tem permissão para ${actionLabels[$event.action]} ${phraseEndings[$event.action]}`);
      return;
    }

    switch ($event.action) {
      case 'edit':
        this.router.navigate([routerLink + '/' + $event.id + '/edit']);
        break;
      case 'view':
        this.router.navigate([routerLink + '/' + $event.id + '/view']);
        break;
      case 'history':
        this.router.navigate([routerLink + '/' + $event.id + '/history']);
        break;
      default:
        break;
    }
  }

  /**
   * Exibe uma mensagem de alerta na tela por 5 segundos.
   */
  private showPermissionAlert(text: string): void {
    this.message = {
      text,
      status: true,
      class: 'alert-danger'
    };
    setTimeout(() => (this.message.status = false), 5000);
  }

  /**
   * Método para carregar as coordenadas da estrutura no mapa.
   * @param {string} clientId - ID do cliente.
   * @param {string} structureId - ID da estrutura.
   */
  mapsStructure(clientId, structureId) {
    this.dataService.getStructureCoordinate(clientId, [structureId]).subscribe((coordinate) => {
      if (coordinate.length > 0) {
        this.dataMapsSection.center.lat = coordinate[0].decimal_geodetic.latitude;
        this.dataMapsSection.center.lng = coordinate[0].decimal_geodetic.longitude;
        this.dataMapsSection.markers[0].position.lat = coordinate[0].decimal_geodetic.latitude;
        this.dataMapsSection.markers[0].position.lng = coordinate[0].decimal_geodetic.longitude;
        this.sendDataMap('markers');
      }
    });
  }

  /**
   * Método para carregar as coordenadas das seções no mapa.
   * @param {string} structureId - ID da estrutura.
   */
  mapsSection(structureId) {
    this.dataService.getSectionCoordinate([structureId]).subscribe((coordinateSections) => {
      this.coordinateSections = coordinateSections;
    });
  }

  /**
   * Método para plotar seções no mapa.
   * @param {any} sections - Seções a serem plotadas.
   * @param {string} action - Ação de seleção ou desseleção.
   */
  plotSections(sections, action: string = 'select') {
    switch (action) {
      case 'select':
        sections = [sections];
        break;
      case 'selectAll':
        break;
      case 'deselect':
        sections = [sections];
        break;
      case 'deselectAll':
        this.dataMapsSection.polylines = [];
        break;
    }

    sections.forEach((section) => {
      //Busca o index com no id da secao selecionada no objeto que foi retornado pela API
      let idx = fn.findIndexInArrayofObject(this.coordinateSections, 'id', section.id);

      //Verifica se a secao esta inserida no array de polylines
      let idxPoly = fn.findIndexInArrayofObject(this.dataMapsSection.polylines, 'id', section.id);

      //Armazena os dados da secao selecionada
      let sectionCoordinate = this.coordinateSections[idx];

      // Plotar no mapa
      if (action.substring(0, 3) === 'sel' && idxPoly == -1) {
        let polyline = {
          path: [],
          strokeColor: sectionCoordinate.map_line_setting.color,
          strokeOpacity: sectionCoordinate.map_line_setting.type == 1 ? 0 : 1,
          strokeWeight: sectionCoordinate.map_line_setting.width,
          icons: [
            {
              icon: {
                path: 'M 0,-1 0,1',
                strokeOpacity: sectionCoordinate.map_line_setting.type == 1 ? 1 : 0,
                scale: 4,
                strokeWeight: sectionCoordinate.map_line_setting.width
              },
              offset: '0',
              repeat: '20px'
            }
          ],
          id: section.id,
          name: section.name
        };

        polyline.path.push({
          lat: sectionCoordinate.upstream_decimal_geodetic.latitude,
          lng: sectionCoordinate.upstream_decimal_geodetic.longitude
        });
        if (sectionCoordinate.midpoint_decimal_geodetic) {
          polyline.path.push({
            lat: sectionCoordinate.midpoint_decimal_geodetic.latitude,
            lng: sectionCoordinate.midpoint_decimal_geodetic.longitude
          });
        }
        polyline.path.push({
          lat: sectionCoordinate.downstream_decimal_geodetic.latitude,
          lng: sectionCoordinate.downstream_decimal_geodetic.longitude
        });

        //adiciona a secao para exibir no mapa
        this.dataMapsSection.polylines.push(polyline);
      }
      // remover do mapa
      if (action.substring(0, 3) === 'des' && idxPoly != -1) {
        this.dataMapsSection.polylines = fn.removeIndexArrayByFilter(this.dataMapsSection.polylines, idxPoly);
      }
    });
    this.sendDataMap('polylinesMultiple', false);
  }

  //Método para plotar instrumentos no mapa.
  plotInstruments() {
    this.instruments.forEach((instrument) => {
      let color = fn.findIndexInArrayofObject(this.groupInstruments, 'type', instrument.type, 'color');

      let svgMarker = {
        path: 'M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0',
        fillColor: color,
        fillOpacity: 1.0,
        strokeWeight: 1.5,
        strokeColor: 'white',
        rotation: 0,
        scale: 0.3,
        anchor: new google.maps.Point(0, 0)
      };

      let marker = {
        position: {
          lat: instrument.decimal_geodetic_coordinate.latitude,
          lng: instrument.decimal_geodetic_coordinate.longitude
        },
        title: instrument.identifier,
        options: {},
        icon: svgMarker,
        id: 'mk-' + instrument.identifier,
        zIndex: -999
      };

      let infoWindowMarker = {
        content: '',
        ariaLabel: instrument.identifier,
        id: instrument.identifier,
        data: instrument,
        contentConfig: [
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-blue',
              icon: '',
              label: 'Selecionar',
              type: true,
              eventClick: true,
              event: 'mapInstrument',
              id: 'iw-button-' + fn.hashCode(instrument.identifier)
            }
          }
        ]
      };

      marker['infoWindowMarker'] = infoWindowMarker;

      this.dataMapsSection.markers.push(marker);
    });
    this.sendDataMap('markersMultiple', false);
  }

  /**
   * Método para enviar os dados ao mapa do Google.
   * @param {string} option - Opção de envio.
   * @param {boolean} clear - Se deve limpar o mapa antes de enviar os dados.
   */
  sendDataMap(option, clear = true) {
    this.googleMaps.setDataMap(this.dataMapsSection, option, clear);
  }

  /**
   * Método para carregar uma estrutura e gerenciar os filtros a partir da estrutura.
   * @param {string} structureId - ID da estrutura a ser carregada.
   */
  callFromStructure(structureId) {
    this.structuresServiceApi.getStructureById(structureId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.clientUnitServiceApi.getClientUnitsById(dados.client_unit.id).subscribe((resp) => {
        let dadosUnit: any = resp;
        dadosUnit = dadosUnit.body === undefined ? dadosUnit : dadosUnit.body;

        this.hierarchy.setClients([{ id: dadosUnit.client.id, name: dadosUnit.client.name }]);
        this.hierarchy.setUnits([{ id: dados.client_unit.id, name: dados.client_unit.name }]);
        this.hierarchy.setStructures([{ id: dados.id, name: dados.name }]);

        this.managerFilters(true);

        let params = {
          action: 'select',
          type: 'structures',
          element: {
            id: dados.id,
            name: dados.name
          }
        };

        this.getEventHierarchy(params);
      });
    });
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }

    this.managerFilters();
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  //Método para voltar à página anterior.
  goBack() {
    this.router.navigate(['/']);
  }
}
