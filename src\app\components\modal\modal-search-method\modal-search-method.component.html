<ng-template #modalSearchMethod let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">Configuração do Método de Busca</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click'); closeModal()"
    ></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formSearchMethod">
      <div class="row">
        <div class="col-sm-12">
          <div class="card" *ngIf="methodsSelected.subtitle != null">
            <div class="card-header">
              {{ methodsSelected.subtitle }}
            </div>
            <div class="card-body">
              <div class="row">
                <div
                  class="col-sm-12"
                  *ngFor="
                    let surfaceTypeField of methodsSelected.fields;
                    let i = index
                  "
                >
                  <label class="form-label">{{
                    surfacesTypeMethodsFields[surfaceTypeField].name
                  }}</label>
                  <input
                    type="number"
                    [min]="surfacesTypeMethodsFields[surfaceTypeField].min"
                    [max]="surfacesTypeMethodsFields[surfaceTypeField].max"
                    [maxlength]="
                      surfacesTypeMethodsFields[surfaceTypeField].length
                    "
                    [step]="surfacesTypeMethodsFields[surfaceTypeField].step"
                    (keypress)="
                      func.controlNumber(
                        $event,
                        formSearchMethod.get(sufix + '_' + surfaceTypeField),
                        surfacesTypeMethodsFields[surfaceTypeField].option
                      )
                    "
                    (keyup)="
                      func.controlNumber(
                        $event,
                        formSearchMethod.get(sufix + '_' + surfaceTypeField)
                      )
                    "
                    class="form-control"
                    [formControlName]="sufix + '_' + surfaceTypeField"
                    [required]="true"
                  />
                  <small
                    class="form-text text-danger"
                    *ngIf="
                      !formSearchMethod.get(sufix + '_' + surfaceTypeField)
                        .valid &&
                      formSearchMethod.get(sufix + '_' + surfaceTypeField)
                        .touched
                    "
                  >
                    Campo Obrigatório.
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-gray'"
      [label]="'Fechar'"
      (click)="c('Close click'); closeModal()"
    >
    </app-button>
  </div>
</ng-template>
