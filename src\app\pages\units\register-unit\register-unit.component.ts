import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

import { Status, MultiSelectDefault, accessLevel, Datum, coordinateFormat, zoneLetterUTM, zoneNumberUTM } from 'src/app/constants/app.constants';
import { MessageCadastro } from 'src/app/constants/message.constants';

import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { ClientService } from 'src/app/services/api/client.service';
import { CoordinateConversionsService } from 'src/app/services/api/coordinateConversions';
import { CoordinateService } from 'src/app/services/coordinate.service';

import { GoogleMapsComponent } from 'src/app/components/google-maps/google-maps.component';

import fn from 'src/app/utils/function.utils';

import { NgxSpinnerService } from 'ngx-spinner';

//Tour guiado
import { CustomTourService } from 'src/app/services/custom-tour.service';
import { TourService } from 'ngx-ui-tour-ng-bootstrap';

@Component({
  selector: 'app-register-unit',
  templateUrl: './register-unit.component.html',
  styleUrls: ['./register-unit.component.scss']
})
export class RegisterUnitComponent implements OnInit {
  @ViewChild(GoogleMapsComponent) googleMaps: GoogleMapsComponent;

  public formClientUnit: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    active: new FormControl(true),
    name: new FormControl(null, [Validators.required]),
    clients: new FormControl([], [Validators.required]),
    datum: new FormControl('', [Validators.required]),
    coordinate_format: new FormControl(null, [Validators.required]),
    zone_number: new FormControl({ value: '', disabled: true }, [Validators.required]),
    zone_letter: new FormControl({ value: '', disabled: true }, [Validators.required]),
    northing: new FormControl({ value: null, disabled: true }),
    easting: new FormControl({ value: null, disabled: true }),
    latitude: new FormControl({ value: null, disabled: true }),
    longitude: new FormControl({ value: null, disabled: true }),
    coordinate_valid: new FormControl(true, [Validators.required])
  });

  public clientUnit: any = {
    id: null,
    name: null,
    client: {
      id: null,
      name: null
    },
    coordinate_setting: {
      datum: null,
      coordinate_format: null,
      coordinate_systems: {
        utm: {
          zone_number: null,
          zone_letter: null,
          northing: null,
          easting: null
        },
        decimal_geodetic: {
          latitude: null,
          longitude: null
        }
      }
    },
    active: null
  };

  public coordinateFormatSel: any;
  public coordinateFormatSelected: string;
  public coordinateFormatString: string;
  public coordinateFormatList: any = coordinateFormat;

  public edit: boolean = false;
  public view: boolean = false;

  public status: any = Status;

  public clients: any = [];
  public clientUnitRequest: any = {};
  public clientSettings = MultiSelectDefault.Clients;
  public accessLevel: any = accessLevel;

  public datum: any = Datum;
  public zoneLetterUTM: any = zoneLetterUTM;
  public zoneNumberUTM: any = zoneNumberUTM;

  public charCounts: { [key: string]: number } = {};
  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public func = fn;

  public formCrtl: boolean = false;

  public dataMapsUnit = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  constructor(
    private activatedRoute: ActivatedRoute,
    private clientUnitService: ClientUnitService,
    private clientService: ClientService,
    private coordinateConversionsService: CoordinateConversionsService,
    private coordinateService: CoordinateService,
    private customTourService: CustomTourService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    public tourService: TourService
  ) {}

  /**
   * Método executado ao inicializar o componente.
   * Verifica se há um ID de unidade na rota ativa para buscar os dados e alterar o estado de edição ou visualização.
   */
  ngOnInit(): void {
    this.formCrtl = true;
    this.getClient();
    if (this.activatedRoute.snapshot.params.unitId) {
      this.edit = true;
      this.getClientUnit(this.activatedRoute.snapshot.params.unitId);
      if (this.activatedRoute.snapshot.url && this.activatedRoute.snapshot.url[1] && this.activatedRoute.snapshot.url[1].path == 'view') {
        this.edit = false;
        this.view = true;
      }
    }
  }

  /**
   * Busca a lista de clientes ativos e atribui os dados ao array de clientes.
   */
  getClient() {
    this.clientService.getClientsList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      // Filtrar clientes ativos
      this.clients = dados;
    });
  }

  /**
   * Registra uma nova unidade de cliente no sistema, exibe uma mensagem de sucesso e redireciona para a página de unidades.
   * Em caso de erro, exibe as mensagens de erro.
   */
  registerClientUnit() {
    this.ngxSpinnerService.show();

    this.formCrtl = false;

    this.messagesError = [];

    this.clientUnitService.postClientUnits(this.clientUnitRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.SucessoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        this.formClientUnit.reset();
        this.formClientUnit.get('clients').setValue('');
        this.formClientUnit.get('datum').setValue('');

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/units']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.errors) {
            this.messagesError = [];
            Object.keys(error.error.errors).forEach((key) => {
              this.messagesError.push(...error.error.errors[key]);
            });
          }
        }
        this.formCrtl = true;
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Busca os detalhes de uma unidade de cliente com base no ID fornecido e preenche o formulário com os dados.
   * Se o modo de visualização estiver ativo, desabilita o formulário.
   * @param {string} unitId - O ID da unidade de cliente a ser buscada.
   */
  getClientUnit(unitId: string) {
    this.clientUnitService.getClientUnitsById(unitId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      delete dados.client.active;

      this.formClientUnit.get('id').setValue(dados.search_identifier);
      this.formClientUnit.get('name').setValue(dados.name);
      this.formClientUnit.get('clients').setValue(dados.client.id);

      this.formClientUnit.get('active').setValue(dados.active);
      this.formClientUnit.get('datum').setValue(dados.coordinate_setting.datum);
      this.formClientUnit.get('coordinate_format').setValue(dados.coordinate_setting.coordinate_format.toString());

      this.formClientUnit.get('zone_number').setValue(dados.coordinate_setting.coordinate_systems.utm.zone_number);
      this.formClientUnit.get('zone_letter').setValue(dados.coordinate_setting.coordinate_systems.utm.zone_letter);
      this.formClientUnit.get('northing').setValue(dados.coordinate_setting.coordinate_systems.utm.northing);
      this.formClientUnit.get('easting').setValue(dados.coordinate_setting.coordinate_systems.utm.easting);
      this.formClientUnit.get('latitude').setValue(dados.coordinate_setting.coordinate_systems.decimal_geodetic.latitude);
      this.formClientUnit.get('longitude').setValue(dados.coordinate_setting.coordinate_systems.decimal_geodetic.longitude);

      this.getSelectedCoordinateFormat();
      this.updatePositionMaps('unit');

      if (this.view) {
        this.formClientUnit.disable();
      }

      this.charCounts['name'] = this.formClientUnit.get('name').value.length;
    });
  }

  /**
   * Edita uma unidade de cliente existente com base nos dados do formulário.
   * Exibe uma mensagem de sucesso e redireciona para a página de unidades após a edição.
   * Em caso de erro, exibe as mensagens de erro.
   */
  editClientUnit() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.clientUnitService.putClientUnits(this.clientUnitRequest.id, this.clientUnitRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/units']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }

        this.formCrtl = true;
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Formata os dados do formulário para serem enviados na requisição.
   * Decide se o formulário será utilizado para criar ou editar uma unidade de cliente.
   */
  formatData() {
    this.clientUnitRequest = { ...this.clientUnit }; // Carregar a estrutura do request

    this.clientUnitRequest.name = this.formClientUnit.get('name')?.value || '';
    this.clientUnitRequest.active = this.formClientUnit.get('active')?.value || false;

    const clientControl = this.formClientUnit.get('clients');
    this.clientUnitRequest.client.id = clientControl?.value || null;
    this.clientUnitRequest.client.name = Array.isArray(clientControl?.value) && clientControl?.value[0]?.name ? clientControl.value[0].name : '';

    this.clientUnitRequest.coordinate_setting.datum = this.formClientUnit.get('datum')?.value || '';
    this.clientUnitRequest.coordinate_setting.coordinate_format = parseInt(this.formClientUnit.get('coordinate_format')?.value) || 0;

    this.clientUnitRequest.coordinate_setting.coordinate_systems.utm.zone_number = this.formClientUnit.get('zone_number')?.value || '';
    this.clientUnitRequest.coordinate_setting.coordinate_systems.utm.zone_letter = this.formClientUnit.get('zone_letter')?.value || '';
    this.clientUnitRequest.coordinate_setting.coordinate_systems.utm.northing = this.formClientUnit.get('northing')?.value || '';
    this.clientUnitRequest.coordinate_setting.coordinate_systems.utm.easting = this.formClientUnit.get('easting')?.value || '';

    this.clientUnitRequest.coordinate_setting.coordinate_systems.decimal_geodetic.latitude = this.formClientUnit.get('latitude')?.value || '';
    this.clientUnitRequest.coordinate_setting.coordinate_systems.decimal_geodetic.longitude = this.formClientUnit.get('longitude')?.value || '';

    if (!this.edit) {
      // Salvar
      delete this.clientUnitRequest.id;
      delete this.clientUnitRequest.active;
      this.registerClientUnit();
    } else {
      // Editar
      this.clientUnitRequest.id = this.activatedRoute.snapshot.params.unitId;
      this.editClientUnit();
    }
  }

  /**
   * Valida os dados do formulário chamando o método de formatação e decisão de salvar ou editar.
   */
  validate() {
    this.formatData();
  }

  /**
   * Converte as coordenadas entre diferentes formatos (UTM ou Geodésico Decimal) e atualiza os campos do formulário com as novas coordenadas.
   * Se houver erros, exibe as mensagens de erro.
   */
  coordinatesConversion() {
    this.coordinateService.coordinatesConversion(this.formClientUnit).subscribe((coordinates) => {
      if (coordinates !== null && coordinates.hasOwnProperty('hasError') && coordinates.hasError) {
        const error = coordinates.error;
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        setTimeout(() => {
          this.messagesError = [];
        }, 8000);

        this.formClientUnit.controls['coordinate_valid'].setValue('');
      } else {
        if (coordinates !== null) {
          if (coordinates.type == 'UTM') {
            this.formClientUnit.get('zone_letter').setValue(coordinates.zone_letter);
            this.formClientUnit.get('zone_number').setValue(coordinates.zone_number);
            this.formClientUnit.get('northing').setValue(coordinates.northing);
            this.formClientUnit.get('easting').setValue(coordinates.easting);
          } else if (coordinates.type == 'Geodetic') {
            this.formClientUnit.get('latitude').setValue(coordinates.latitude);
            this.formClientUnit.get('longitude').setValue(coordinates.longitude);
          }
          this.formClientUnit.controls['coordinate_valid'].setValue(true);
          this.updatePositionMaps('unit');
        }
      }
    });
  }

  /**
   * Envia os dados do mapa para o Google Maps, atualizando as informações da unidade.
   * @param {string} option - A opção de configuração do mapa.
   * @param {boolean} [clear=true] - Indica se o mapa deve ser limpo antes de adicionar novos dados.
   */
  sendDataMap(option, clear = true) {
    this.googleMaps.setDataMap(this.dataMapsUnit, option, clear);
  }

  /**
   * Lida com a mudança no formato de coordenadas selecionado no formulário e ajusta os campos visíveis de acordo com o formato.
   * @param {any} coordinateFormat - O formato de coordenadas selecionado.
   */
  onCoordinateFormatChange(coordinateFormat: any) {
    this.getSelectedCoordinateFormat();
  }

  /**
   * Obtém o formato de coordenadas selecionado no formulário e ajusta os campos habilitados com base no formato (UTM ou Geodésico Decimal).
   */
  getSelectedCoordinateFormat() {
    this.coordinateFormatSel = coordinateFormat.find((item) => item.id === parseInt(this.formClientUnit.get('coordinate_format').value));
    this.coordinateFormatString = this.coordinateFormatSel.value;

    if (this.coordinateFormatString === 'UTM') {
      this.formClientUnit.get('zone_number').enable();
      this.formClientUnit.get('zone_letter').enable();
      this.formClientUnit.get('northing').enable();
      this.formClientUnit.get('easting').enable();

      this.formClientUnit.get('latitude').disable();
      this.formClientUnit.get('longitude').disable();
    } else if (this.coordinateFormatString === 'Decimal Geodetic') {
      this.formClientUnit.get('latitude').enable();
      this.formClientUnit.get('longitude').enable();

      this.formClientUnit.get('zone_number').disable();
      this.formClientUnit.get('zone_letter').disable();
      this.formClientUnit.get('northing').disable();
      this.formClientUnit.get('easting').disable();
    }
  }

  /**
   * Atualiza a posição do mapa com base nas coordenadas e datum selecionados no formulário.
   * @param {string} type - O tipo de entidade (neste caso, 'unit') para a qual as coordenadas estão sendo atualizadas.
   */
  updatePositionMaps(type: string): void {
    if (type == 'unit') {
      let params = {
        decimal_geodetic: {
          latitude: this.formClientUnit.get('latitude').value,
          longitude: this.formClientUnit.get('longitude').value
        },
        input_datum: this.formClientUnit.get('datum').value,
        output_datum: 2 //Sirgas
      };
      this.coordinateConversionsService.postCoordinateDatum(params).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;

        this.dataMapsUnit.center.lat = dados.latitude;
        this.dataMapsUnit.center.lng = dados.longitude;
        this.dataMapsUnit.markers[0].position.lat = dados.latitude;
        this.dataMapsUnit.markers[0].position.lng = dados.longitude;

        this.sendDataMap('markers');
      });
    }
  }

  loadTourGuide() {
    this.customTourService.startTour(this.tourService, 'assets/tour-guide/register-unit.tourguide.json');
  }

  // Atualiza o contador do campo específico
  onValueChange(event: any, field: string): void {
    this.charCounts[field] = event.target.value.length;
  }
}
