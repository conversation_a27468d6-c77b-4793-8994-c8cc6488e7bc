<form [formGroup]="formReading" class="mb-3">
  <div class="row">
    <!-- Instrumento -->
    <div class="col-md-3">
      <label class="form-label">Instrumento</label>
      <select
        class="form-select"
        formControlName="instrument"
        (change)="changeInstrument(formReading.controls['instrument'].value)"
      >
        <option value="" *ngIf="formReading.controls['instrument'].value == ''">
          Selecione...
        </option>
        <option
          *ngFor="let instrumentItem of instrumentsList"
          [ngValue]="instrumentItem.id"
        >
          {{ instrumentItem.identifier }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('instrument').valid &&
          formReading.get('instrument').touched &&
          !formReading.get('instrument').disabled
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Célula de pressão - Apenas PZE -->
    <div class="col-md-3">
      <label class="form-label">Célula de pressão</label>
      <input type="text" class="form-control" formControlName="measure" />
    </div>

    <!-- Data e hora -->
    <div class="col-md-3">
      <label class="form-label">Data e hora</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('date').valid && formReading.get('date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- quota -->
    <div class="col-md-3">
      <label class="form-label">{{
        typeInstrument.id == 1 ? 'Cota NA' : 'Cota Piezométrica'
      }}</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="quota"
          (blur)="func.formatType($event); calcQuota('quota')"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="func.controlNumber($event, formReading.get('quota'))"
          appDisableScroll
        /><span class="input-group-text">{{ units[0] }}</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('quota').valid && formReading.get('quota').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <div class="row mt-2">
    <!-- depth -->
    <div class="col-md-3">
      <label class="form-label">Profundidade</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="depth"
          (blur)="func.formatType($event); calcQuota('depth')"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="func.controlNumber($event, formReading.get('depth'))"
          appDisableScroll
        />
        <span class="input-group-text">{{ units[0] }}</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('depth').valid && formReading.get('depth').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Pressão -->
    <div class="col-md-3">
      <label class="form-label">Pressão</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="pressure"
          placeholder="Campo opcional"
          (blur)="func.formatType($event); calcQuota('pressure')"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'positiveDecimal')"
          (keyup)="func.controlNumber($event, formReading.get('pressure'))"
          appDisableScroll
        />
        <span class="input-group-text">{{ units[1] }}</span>
      </div>
    </div>

    <!-- Seco -->
    <div class="col-md-3 align-self-end">
      <input
        class="form-check-input me-2"
        formControlName="dry"
        type="checkbox"
        value=""
        (change)="toogleDry()"
      />
      <label class="form-label">Seco</label>
    </div>
  </div>
</form>
