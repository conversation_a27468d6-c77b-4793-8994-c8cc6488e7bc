import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ClickOutsideModule } from 'ng4-click-outside';
import { ColorSketchModule } from 'ngx-color/sketch';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';

import { LogisoilDirectivesModule } from 'src/app/shared/logisoil-directives.module';
import { SharedModule } from '@components/shared.module';

import { StabilityRoutingModule } from './stability-routing.module';

import { AnalysisStabilityComponent } from './analysis-stability/analysis-stability.component';
import { ChartStabilityComponent } from './chart-stability/chart-stability.component';
import { ListStabilityComponent } from './list-stability/list-stability.component';
import { MapsStabilityComponent } from './maps-stability/maps-stability.component';
import { ListSimulationsComponent } from './simulations/list-simulations/list-simulations.component';
import { RegisterSimulationsComponent } from './simulations/register-simulations/register-simulations.component';
import { ResultSimulationsComponent } from './simulations/result-simulations/result-simulations.component';
//Modal
import { ModalAnalysisComponent } from './analysis-stability/modal-analysis/modal-analysis.component';
import { ModalFixedSimulationComponent } from './simulations/list-simulations/modal-fixed-simulation/modal-fixed-simulation.component';
import { ModalShareSimulationComponent } from './simulations/list-simulations/modal-share-simulation/modal-share-simulation.component';

@NgModule({
  declarations: [
    ListStabilityComponent,
    MapsStabilityComponent,
    AnalysisStabilityComponent,
    ChartStabilityComponent,
    ListSimulationsComponent,
    RegisterSimulationsComponent,
    ResultSimulationsComponent,
    ModalAnalysisComponent,
    ModalShareSimulationComponent,
    ModalFixedSimulationComponent
  ],
  imports: [
    ClickOutsideModule,
    ColorSketchModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    LogisoilDirectivesModule,
    NgbModule,
    NgMultiSelectDropDownModule.forRoot(),
    SharedModule,
    StabilityRoutingModule
  ]
})
export class StabilityModule {}
