<form [formGroup]="formStaticMaterials">
  <div class="row mt-2">
    <!-- <PERSON>ltro Cliente, Unidade, Estrutura -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-12"
      (sendEventHierarchy)="getEventHierarchy($event)"
      (filtersChanged)="filterEventHierarchy($event)"
      [disabled]="this.view"
    ></app-hierarchy>
  </div>

  <div class="row mt-2 mb-3">
    <!-- Nome -->
    <div class="col-md-4">
      <label class="form-label">Nome</label>
      <input
        type="text"
        class="form-control"
        formControlName="name"
        autocomplete="off"
        maxlength="50"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formStaticMaterials.get('name').valid &&
          formStaticMaterials.get('name').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <app-tabs-conditions
    [view]="view"
    [edit]="edit"
    #tabConditions
  ></app-tabs-conditions>
</form>
