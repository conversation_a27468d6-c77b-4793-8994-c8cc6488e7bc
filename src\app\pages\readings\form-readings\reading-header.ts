const readingTableHeader = [
  {
    label: 'Selecionar',
    width: '55px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['select'],
    type: 'check'
  },
  {
    label: 'ID',
    width: '40px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['reading_search_identifier']
  },
  {
    label: 'Instrumento',
    width: '60px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['identifier'],
    extra: true //Losango
  },
  {
    label: 'Data e hora',
    width: '50px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['date_format']
  },
  {
    label: 'Cota leitura (m)',
    width: '10%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['quota']
  },
  {
    label: 'Profundidade leitura (m)',
    width: '10%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['depth']
  },
  {
    label: 'Pressão (kPa)',
    width: '50px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['pressure']
  },
  {
    label: 'Seco',
    width: '40px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['dry']
  },
  {
    label: 'Referência',
    width: '55px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['is_referential_reading']
  },
  {
    label: 'Ações',
    width: '10%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['actionCustom']
  }
];

export { readingTableHeader };
