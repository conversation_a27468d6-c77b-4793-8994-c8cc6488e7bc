import { Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { FormGroup, FormControl, Validators, FormArray } from '@angular/forms';
import { CustomValidators } from 'src/app/utils/custom-validators';

import { MessageCadastro } from 'src/app/constants/message.constants';

import { ImagesService as ImagesServiceApi } from 'src/app/services/api/image.service';

//Para colocar a URL do arquivo como seguro
import { DomSanitizer } from '@angular/platform-browser';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';

@Component({
  selector: 'app-images',
  templateUrl: './images.component.html',
  styleUrls: ['./images.component.scss']
})
export class ImagesComponent implements OnInit, OnChanges {
  @ViewChild('fileInput') fileInput: ElementRef;
  @ViewChild('modalConfirm') ModalConfirm: any;

  @Input() maxFiles: number = 999999;
  @Input() maxFileSize: number = 10000000;
  @Input() imagesItens: any = [];

  @Input() message: any = [{ text: '', status: false, class: 'alert-success' }];
  @Input() messagesError: any = null;

  @Input() uploadActive: boolean = true;

  public formImages: FormGroup = new FormGroup({
    image: new FormControl(null, Validators.compose([CustomValidators.validadorExtensaoArquivo(['png', 'jpg', 'jpeg', 'gif', 'bmp'])])),
    description: new FormArray([])
  });

  public maxlength: number = 50;
  public fileContent: string[] = [];
  public fileName: string[] = [];
  public fileContentDownload: any[] = [];

  public images: any = [];

  public limitsFiles = true;
  public limitFileSize = true;

  public showCarousel: boolean = false;

  public selectedImage: number = null;

  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };
  public modalData: any = {};
  public modalId: any = null;

  public showImages: boolean = true;

  constructor(private sanitizer: DomSanitizer, private imagesServiceApi: ImagesServiceApi) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.imagesItens && changes.imagesItens.currentValue != null && !changes.maxFiles && !changes.maxFileSize) {
      this.managerImages(changes.imagesItens.currentValue);
    }
  }

  uploadFile($event: any) {
    let files = $event.dataTransfer ? $event.dataTransfer.files : $event.target.files;
    this.limitsFiles = true;
    if (files.length + this.images.length > this.maxFiles) {
      this.limitsFiles = false;
    } else {
      this.limitFileSize = true;
      Array.from(files).forEach((file: any, index) => {
        if (file.size > this.maxFileSize) {
          this.limitFileSize = false;
        }
      });

      if (this.limitFileSize) {
        Array.from(files).forEach((file: any, index) => {
          let pattern = /image-*/;
          let reader = new FileReader();
          if (!file.type.match(pattern)) {
            return;
          }
          reader.onload = ($event: any) => this.uploadFileLoaded($event, index, file);
          reader.readAsDataURL(file);
        });
      }
    }
  }

  uploadFileLoaded($event: any, index: number, file: any) {
    let reader = $event.target;
    const fileContent = reader.result;
    const fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl(fileContent);
    const fileName = this.formImages.get('image').value.split('\\');
    const idx = this.images.length;
    this.images.push({
      id: null,
      file: file,
      path: fileContent,
      description: '',
      fileName: fileName[fileName.length - 1],
      fileContentDownload: fileContentDownload,
      charachtersCount: 0,
      counter: `0 de ${this.maxlength}`,
      index: idx,
      new: true,
      user: '',
      date: ''
    });
    this.addDescription(null, idx);
  }

  addDescription(descriptionValue: any = null, index = null) {
    const value = descriptionValue == null ? '' : descriptionValue;
    const control = new FormControl(value); // Se descriptionValue for null, atribui uma string vazia
    (this.formImages.get('description') as FormArray).push(control);
    this.formImages.controls['description']['controls'][index].setValue(descriptionValue);
  }

  removeImage(index, notConfirm = false) {
    if (this.images[index].new || notConfirm) {
      const descriptionArray = this.formImages.get('description') as FormArray;
      descriptionArray.removeAt(index);
      this.images.splice(index, 1);
    } else {
      this.confirmDeletetion(index);
    }
  }

  showCarouselImage(i) {
    this.formImages.get('image').reset();
    this.selectedImage = i;
    this.showCarousel = true;
  }

  onValueChange(event: any, index: number): void {
    this.images[index].charachtersCount = event.target.value.length;
    this.images[index].counter = `${this.images[index].charachtersCount} de ${this.maxlength}`;
  }

  onBlur(event: any, index: number): void {
    this.images[index].description = event.target.value;
  }

  closeCarousel() {
    this.showCarousel = false;
    this.selectedImage = null;
  }

  getFormControlAtIndex(controlName: string, index: number) {
    return (this.formImages.get(controlName) as FormArray).controls[index];
  }

  managerImages($images) {
    this.images = [];
    this.showImages = false;

    $images.forEach((image, index) => {
      // Inicia com path vazio até que a imagem seja carregada
      const newImage = {
        id: image.id,
        file: null,
        path: null,
        description: image.description,
        fileName: '',
        fileContentDownload: null,
        charachtersCount: 0,
        counter: `0 de ${this.maxlength}`,
        index: index,
        new: false,
        user: image.uploaded_by,
        date: moment(image.uploaded_date).subtract(3, 'hours').format('DD/MM/YYYY HH:mm:ss')
      };

      this.addDescription(image.description, index);

      // Chama a função getImageById e atualiza a propriedade path quando a imagem é carregada
      this.getImageById(image.id).then((imageData) => {
        const decode = fn.base64Extension(imageData['base64'].slice(0, 100));
        //newImage.id = image.id;
        newImage.path = `data:${decode.mimeType};base64,` + imageData['base64'];
        newImage.fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl(newImage.path);
        newImage.fileName = imageData['original_file_name']; //newImage.description = image.description; //`image_${newImage.index}.${decode.extension}`;
        // Adiciona a nova imagem ao array this.images
        this.images.push(newImage);
        this.images = fn.sortObject(this.images, { property: 'index', descending: true });
      });
    });

    this.showImages = true;
  }

  getImageById(imageId): Promise<string> {
    // Retorna uma Promise que será resolvida com o path da imagem
    return new Promise((resolve, reject) => {
      this.imagesServiceApi.getImageById(imageId, {}).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        const imageData = dados;
        resolve(imageData);
      });
    });
  }

  confirmDeletetion(index) {
    this.modalTitle = 'Excluir imagem';
    this.modalMessage = 'Deseja confirmar a exclusão da imagem?';
    this.modalInstruction = ``;
    this.modalConfig.iconHeader = 'fa fa-trash-o';
    this.modalConfig.action = 'confirmDeletion';
    this.modalId = index;
    this.ModalConfirm.openModal();
  }

  clickEvent($event: any = null) {
    if ($event.action === 'confirmDeletion') {
      const imageId = this.images[$event.id].id;
      this.deleteImage(imageId);
      this.removeImage($event.id, true);
    }
  }

  deleteImage(id) {
    this.imagesServiceApi.deleteImage(id).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.DeleteImages;
        this.message.status = true;
        this.message.class = 'alert-success';
        setTimeout(() => {
          this.message.status = false;
        }, 3000);
      },
      (error) => {
        if (error.status !== 200) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 3000);
        }
      }
    );
  }
}
