import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';

import { DashboardRoutingModule } from './dashboard-routing.module';
import { SharedModule } from 'src/app/components/shared.module';

import { DashboardComponent } from './dashboard.component';
import { ModalChartFsComponent } from './modal-chart-fs/modal-chart-fs.component';

@NgModule({
  declarations: [DashboardComponent, ModalChartFsComponent],
  imports: [CommonModule, FormsModule, DashboardRoutingModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, SharedModule]
})
export class DashboardModule {}
