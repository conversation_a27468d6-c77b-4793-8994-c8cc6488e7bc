import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { Datum, coordinateFormat, zoneLetterUTM, zoneNumberUTM } from 'src/app/constants/app.constants';
import { accessLevel as accessLevelPermission } from 'src/app/constants/permissions.constants';
import { currentSituation } from 'src/app/constants/structure.constants';
import { GoogleMapsComponent } from 'src/app/components/google-maps/google-maps.component';
import { MessagePadroes } from 'src/app/constants/message.constants';

import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { CoordinateConversionsService } from 'src/app/services/api/coordinateConversions';
import { CoordinateService } from 'src/app/services/coordinate.service';
import { ImagesService as ImagesServiceApi } from 'src/app/services/api/image.service';

import { SharedService } from 'src/app/services/shared.service';

import fn from 'src/app/utils/function.utils';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-general-tab',
  templateUrl: './general-tab.component.html',
  styleUrls: ['./general-tab.component.scss']
})
export class GeneralTabComponent implements OnInit {
  @ViewChild('mapStructure', { static: false }) mapStructure: GoogleMapsComponent;
  @ViewChild('mapInstruments', { static: false }) mapInstruments: GoogleMapsComponent;

  @ViewChild('modalComponents') ModalComponents: any;

  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  public formStructure: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    client_unit: new FormControl([], [Validators.required]),
    name: new FormControl(null, [Validators.required]),
    status: new FormControl('', [Validators.required]),
    datum: new FormControl('', [Validators.required]),
    coordinate_format: new FormControl(null, [Validators.required]),
    zone_number: new FormControl({ value: '', disabled: true }, [Validators.required]),
    zone_letter: new FormControl({ value: '', disabled: true }, [Validators.required]),
    northing: new FormControl({ value: null, disabled: true }),
    easting: new FormControl({ value: null, disabled: true }),
    latitude: new FormControl({ value: null, disabled: true }),
    longitude: new FormControl({ value: null, disabled: true }),
    coordinate_valid: new FormControl(true, [Validators.required]),
    general_zoom: new FormControl(14),
    instruments_zoom: new FormControl(17)
  });

  public units: any = [];
  public status: any = currentSituation;
  public datum: any = Datum;
  public zoneLetterUTM: any = zoneLetterUTM;
  public zoneNumberUTM: any = zoneNumberUTM;
  public cordinateFormat: any = coordinateFormat;

  public coordinateFormatSel: any;
  public coordinateFormatSelected: string;
  public coordinateFormatString: string;
  public coordinateFormatList: any = coordinateFormat;

  public charCounts: { [key: string]: number } = {};
  public message: any = [{ text: '', status: false, class: 'alert-success' }];
  public messagesError: any = null;

  public filterParams: any = {};
  public filter: any = {
    Active: '',
    CoordinateSystems: ''
  };

  public unitSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'name',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: false,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  public dataMapsStructure = {
    height: '500px',
    width: '100%',
    zoom: 14,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  public dataMapsInstruments = {
    height: '500px',
    width: '100%',
    zoom: 17,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  public unitsDisabled = false;

  public configModal: any = null;
  public titleModal: string = '';

  public clickEventSubscription: Subscription;
  public func = fn;

  constructor(
    private cd: ChangeDetectorRef,
    private clientUnitService: ClientUnitService,
    private coordinateConversionsService: CoordinateConversionsService,
    private coordinateService: CoordinateService,
    private sharedService: SharedService,
    private imagesServiceApi: ImagesServiceApi
  ) {
    this.clickEventSubscription = this.sharedService.getClickEvent().subscribe((instrument) => {
      this.eventMap(instrument);
    });
  }

  ngOnInit(): void {
    this.getUnits();
    this.validateAccess();
  }

  /**
   * Busca a lista de unidades ativas do cliente e armazena em `units`.
   */
  getUnits() {
    this.clientUnitService.getClientUnitsId({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.units = dados;
    });
  }

  /**
   * Converte coordenadas geográficas ou UTM com base no formulário atual.
   * Atualiza os campos correspondentes e a posição no mapa, ou exibe mensagens de erro.
   */
  coordinatesConversion() {
    this.coordinateService.coordinatesConversion(this.formStructure).subscribe((coordinates) => {
      if (coordinates !== null && coordinates.hasOwnProperty('hasError') && coordinates.hasError) {
        const error = coordinates.error;
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        setTimeout(() => {
          this.messagesError = [];
        }, 4000);
        this.formStructure.controls['coordinate_valid'].setValue('');
      } else {
        if (coordinates !== null) {
          if (coordinates.type == 'UTM') {
            this.formStructure.get('zone_letter').setValue(coordinates.zone_letter);
            this.formStructure.get('zone_number').setValue(coordinates.zone_number);
            this.formStructure.get('northing').setValue(coordinates.northing);
            this.formStructure.get('easting').setValue(coordinates.easting);
          } else if (coordinates.type == 'Geodetic') {
            this.formStructure.get('latitude').setValue(coordinates.latitude);
            this.formStructure.get('longitude').setValue(coordinates.longitude);
          }
          this.formStructure.controls['coordinate_valid'].setValue(true);
          this.updatePositionMaps('structure');
        }
      }
    });
  }

  /**
   * Envia dados para o mapa correspondente (estrutura ou instrumento), com a opção de limpar dados existentes.
   *
   * @param option - Tipo de dado a ser enviado (ex: 'markersMultiple', 'zoom').
   * @param clear - Se `true`, limpa os dados anteriores. Padrão é `true`.
   * @param map - Identificador do mapa ('structure' ou 'instrument').
   */
  sendDataMap(option, clear = true, map = '') {
    if (map == 'structure') {
      this.mapStructure.setDataMap(this.dataMapsStructure, option, clear);
    }

    if (map == 'instrument') {
      this.mapInstruments.setDataMap(this.dataMapsInstruments, option, clear);
    }
  }

  /**
   * Evento disparado ao mudar o formato de coordenada.
   * Atualiza os campos ativos no formulário com base no tipo selecionado.
   *
   * @param coordinateFormat - Valor do formato selecionado.
   */
  onCoordinateFormatChange(coordinateFormat: any) {
    this.getSelectedCoordinateFormat();
  }

  /**
   * Ativa ou desativa campos de coordenadas conforme o formato selecionado no formulário (`UTM` ou `Geodetic`).
   */
  getSelectedCoordinateFormat() {
    if (!this.view) {
      this.coordinateFormatSel = coordinateFormat.find((item) => item.id === parseInt(this.formStructure.get('coordinate_format').value));
      this.coordinateFormatString = this.coordinateFormatSel.value;

      if (this.coordinateFormatString === 'UTM') {
        this.formStructure.get('zone_number').enable();
        this.formStructure.get('zone_letter').enable();
        this.formStructure.get('northing').enable();
        this.formStructure.get('easting').enable();

        this.formStructure.get('latitude').disable();
        this.formStructure.get('longitude').disable();
      } else if (this.coordinateFormatString === 'Decimal Geodetic') {
        this.formStructure.get('latitude').enable();
        this.formStructure.get('longitude').enable();

        this.formStructure.get('zone_number').disable();
        this.formStructure.get('zone_letter').disable();
        this.formStructure.get('northing').disable();
        this.formStructure.get('easting').disable();
      }
    }
  }

  /**
   * Atualiza a posição do marcador e centro do mapa da estrutura com base nas coordenadas convertidas.
   *
   * @param type - Tipo de mapa a ser atualizado (apenas 'structure' neste contexto).
   */
  updatePositionMaps(type: string): void {
    if (type == 'structure') {
      let params = {
        decimal_geodetic: {
          latitude: this.formStructure.get('latitude').value,
          longitude: this.formStructure.get('longitude').value
        },
        input_datum: this.formStructure.get('datum').value,
        output_datum: 2 //Sirgas
      };

      this.coordinateConversionsService.postCoordinateDatum(params).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;

        this.dataMapsStructure.center.lat = dados.latitude;
        this.dataMapsStructure.center.lng = dados.longitude;
        this.dataMapsStructure.markers[0].position.lat = dados.latitude;
        this.dataMapsStructure.markers[0].position.lng = dados.longitude;

        this.dataMapsInstruments.center.lat = dados.latitude;
        this.dataMapsInstruments.center.lng = dados.longitude;
        this.dataMapsInstruments.markers[0].position.lat = dados.latitude;
        this.dataMapsInstruments.markers[0].position.lng = dados.longitude;

        this.sendDataMap('markersMultiple', false, 'instrument');
        this.managerMap();
      });
    }
  }

  /**
   * Configura e exibe a InfoWindow do marcador da estrutura no mapa.
   * Aplica botões customizados e centraliza a visualização.
   */
  managerMap() {
    if (this.view) {
      let infoWindowMarker = {
        content: '',
        ariaLabel: this.formStructure.controls['name'].value,
        id: this.formStructure.controls['name'].value,
        data: { structure_id: this.formStructure.controls['id'].value },
        classTitle: 'd-flex justify-content-center',
        contentConfig: [
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-blue',
              icon: '',
              label: 'Ver imagens',
              type: true,
              eventClick: true,
              event: 'showImage',
              id: 'iw-button-' + fn.hashCode(this.formStructure.controls['name'].value)
            },
            classItem: 'd-flex justify-content-center',
            show: true
          }
        ]
      };

      this.dataMapsStructure.markers[0]['id'] = 'mk-' + this.formStructure.controls['name'].value;
      this.dataMapsStructure.markers[0]['infoWindowMarker'] = infoWindowMarker;
    }

    this.sendDataMap('markersMultiple', false, 'structure');
    this.mapStructure.openInfoWindowFromMarkerId(['mk-' + this.formStructure.controls['name'].value], 'mk-', false);
  }

  /**
   * Trata eventos emitidos pelo mapa, como clique em botão de imagem ou alteração de zoom.
   *
   * @param $event - Evento emitido pelo mapa.
   */
  eventMap($event) {
    switch ($event.type) {
      case 'showImage':
        this.getImages();
        break;
      case 'mapZoomChanged':
        if ($event.params && $event.params.mapId == 'mapStructure') {
          this.setZoom('structure', $event.data, false);
        }
        if ($event.params && $event.params.mapId == 'mapInstruments') {
          this.setZoom('instruments', $event.data, false);
        }
        break;
    }
  }

  /**
   * Atualiza o nível de zoom no mapa de estrutura ou instrumentos e salva no formulário.
   *
   * @param type - Tipo de mapa: 'structure' ou 'instruments'.
   * @param zoom - Valor do zoom a ser definido.
   * @param option - Se `true`, atualiza visualmente o mapa. Padrão é `true`.
   */
  setZoom(type: string, zoom: string, option = true): void {
    if (type == 'structure') {
      this.dataMapsStructure.zoom = parseInt(zoom);
      this.formStructure.get('general_zoom').setValue(parseInt(zoom));
      if (option) {
        this.mapStructure.setDataMap(this.dataMapsStructure, 'zoom', false, false);
      }
    }
    if (type == 'instruments') {
      this.dataMapsInstruments.zoom = parseInt(zoom);
      this.formStructure.get('instruments_zoom').setValue(parseInt(zoom));
      if (option) {
        this.mapInstruments.setDataMap(this.dataMapsInstruments, 'zoom', false, false);
      }
    }
  }

  /**
   * Evento utilizado em campos de zoom manual. Repassa o valor para o método `setZoom`.
   *
   * @param $event - Valor do novo zoom.
   * @param type - Tipo de mapa (padrão vazio para controle externo).
   */
  zoomChange($event, type: string = '') {
    this.setZoom(type, $event);
  }

  /**
   * Valida as permissões de acesso com base no perfil.
   * Caso o usuário não tenha permissão de suporte, desativa o formulário.
   *
   * @param role - (Opcional) Nível de permissão a ser validado.
   */
  validateAccess(role: number = 0): any {
    if (this.profile.description !== accessLevelPermission.SuperSuporte && this.profile.description !== accessLevelPermission.Suporte) {
      this.view = true;
    }

    if (this.view) {
      this.formStructure.disable();
      this.unitsDisabled = true;
    }
  }

  /**
   * Popula os campos do formulário `formStructure` com os dados recebidos.
   * Atualiza o zoom e a posição do mapa conforme necessário.
   *
   * @param dataTab - Objeto contendo os dados a serem inseridos no formulário.
   */
  setData(dataTab: any = []) {
    for (var index in dataTab) {
      this.formStructure.get(index).setValue(dataTab[index]);
      switch (index) {
        case 'general_zoom':
          this.setZoom('structure', dataTab[index]);
          break;
      }
    }

    this.charCounts['name'] = this.formStructure.get('name').value.length;
    this.updatePositionMaps('structure');
  }

  /**
   * Retorna os valores do formulário `formStructure` como um objeto.
   *
   * @returns Objeto contendo os dados preenchidos no formulário.
   */
  getData() {
    let dataTab = {};
    for (let index in this.formStructure.controls) {
      dataTab[index] = this.formStructure.get(index).value;
    }
    return dataTab;
  }

  /**
   * Busca e exibe imagens associadas à estrutura no modal.
   * Utiliza o `imagesServiceApi` para recuperar imagens e configura o modal dinamicamente.
   */
  getImages() {
    let params = {
      Entities: 0,
      'Filters.Structures': this.formStructure.controls['id'].value
    };

    this.imagesServiceApi.getImages(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.titleModal = 'Imagens da estrutura : ' + this.formStructure.controls['name'].value;
      this.configModal = {
        imagesItens: dados.images,
        message: { text: '', status: false, class: 'alert-success' },
        uploadActive: false
      };

      if (dados.images.length == 0) {
        this.configModal['message'] = { text: MessagePadroes.NoImage, status: true, class: 'alert-warning' };
      }

      this.ModalComponents.openModal();
    });
  }

  // Atualiza o contador do campo específico
  onValueChange(event: any, field: string): void {
    this.charCounts[field] = event.target.value.length;
  }
}
