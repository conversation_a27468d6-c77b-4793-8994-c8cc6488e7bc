import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup } from '@angular/forms';

import { FilterService } from 'src/app/services/filter.service';
import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { UserService } from 'src/app/services/user.service';

import { InsertInspectionSheet, InspectionSheetStatus, InspectionSheetType } from 'src/app/constants/inspections.constants';
import { ModalInsertInspectionSheetComponent } from './modal-insert-inspection-sheet/modal-insert-inspection-sheet.component';
import { MessageCadastro, MessagePadroes, ModalConfirm } from 'src/app/constants/message.constants';

@Component({
  selector: 'app-inspection-sheet',
  templateUrl: './inspection-sheet.component.html',
  styleUrls: ['./inspection-sheet.component.scss']
})
export class InspectionSheetComponent implements OnInit {
  @ViewChild('modalConfirm') ModalConfirm: any;
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalInsertInspectionSheet', { static: false })
  ModalInsertInspectionSheet: ModalInsertInspectionSheetComponent;
  @ViewChild('modalHistoryInspectionSheet', { static: false }) ModalHistoryInspectionSheet;

  inspectionSheetForm: FormGroup = this.fb.group({
    ClientId: [''],
    ClientUnitId: [''],
    StructureId: [''],
    StartDate: [''],
    EndDate: [''],
    Status: [''],
    Type: [''],
    SearchIdentifier: ['']
  });

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public filterSearch: any = {};
  public filterParams: any = {};
  public filter: any = {
    StartDate: '',
    EndDate: '',
    Status: 'all',
    Type: 'all',
    SearchIdentifier: ''
  };

  public structureId: string = '';

  public inspectionSheetStatus = InspectionSheetStatus;
  public inspectionSheetType = InspectionSheetType;
  public insertInspectionSheet = InsertInspectionSheet;

  public selectedStatus: string | number = 'all';
  public selectedOrigin: string | number = 'all';

  public dropdownInspectionSheet = false;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public modalData: any = {};
  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  public tableData: Array<{ [key: string]: any }> = [];
  public tableHeader: any = [
    {
      label: 'ID',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Data inspeção',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['start_date']
    },
    {
      label: 'Status',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['status']
    },
    {
      label: 'Pontuação',
      width: '140px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['score']
    },
    {
      label: 'Ocorrências',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['occurrences']
    },
    {
      label: 'Origem',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['type']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['miniDashboard']
    }
  ];

  constructor(
    private fb: FormBuilder,
    private filterService: FilterService,
    private inspectionSheetServiceApi: InspectionSheetServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private userService: UserService
  ) {}

  /**
   * Executado ao inicializar o componente.
   * Obtém informações do perfil do usuário e permissões associadas.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros.
   */
  ngAfterViewInit(): void {
    this.ngxSpinnerService.show();

    setTimeout(() => {
      // Aplica filtros e carrega os dados automaticamente
      if (this.filter) {
        this.searchInspectionSheet();
      } else if (this.hierarchy?.elements?.length > 0) {
        this.managerFilters(true);
      } else {
        this.managerFilters();
      }
      this.ngxSpinnerService.hide(); // Oculta o spinner após os dados serem carregados
    }, 1000);
  }

  /**
   * Aplica os filtros salvos à pesquisa da ficha de inspeção.
   * @param {any} filters - Filtros a serem aplicados.
   */
  private applySavedFilters(filters: any): void {
    this.filterSearch = filters;
    this.page = filters.Page || 1;
    this.pageSize = filters.PageSize || 10;

    this.filter = {
      SearchIdentifier: filters.SearchIdentifier || '',
      StartDate: filters.StartDate || '',
      EndDate: filters.EndDate || '',
      Status: filters.Status || '',
      Type: filters.Type || '',
      ...this.extractHierarchyFilters() // Adiciona os filtros de hierarquia
    };
  }

  /**
   * Extrai os filtros de hierarquia da estrutura selecionada.
   * @returns {any} - Objeto contendo os filtros de hierarquia.
   */
  private extractHierarchyFilters(): any {
    const hierarchyFilters = this.hierarchy.getFilters();

    return {
      ClientId: hierarchyFilters?.clients?.[0]?.id || '',
      ClientUnitId: hierarchyFilters?.units?.[0]?.id || '',
      StructureId: hierarchyFilters?.structures?.[0]?.id || ''
    };
  }

  /**
   * Obtém a lista de fichas de inspeção a partir dos parâmetros fornecidos.
   * @param {any} params - Parâmetros da consulta.
   */
  getInspectionSheetList(params) {
    this.ngxSpinnerService.show();

    this.message.text = '';
    this.message.status = false;

    this.inspectionSheetServiceApi.getInspectionSheet(params).subscribe(
      (resp) => {
        const dados: any = resp;
        if (dados.status == 200) {
          // Formata os dados antes de atribuí-los à tabela
          this.tableData = dados.body.data ? this.formatInspectionSheetData(dados.body.data) : [];
          this.collectionSize = dados.body.total_items_count;
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.message.text = MessagePadroes.NoRegister;
          this.message.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.message.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide(); // Garantir ocultação aqui
      },
      (error) => {
        console.error(error);
        this.ngxSpinnerService.hide(); // Garantir ocultação no caso de erro
      }
    );
  }

  /**
   * Realiza a busca das fichas de inspeção aplicando os filtros selecionados.
   */
  searchInspectionSheet(): void {
    this.filterParams = {
      SearchIdentifier: this.filter.SearchIdentifier || '',
      StartDate: this.filter.StartDate || '',
      EndDate: this.filter.EndDate || '',
      Status: this.selectedStatus !== 'all' ? this.selectedStatus : '', // Remove "all"
      Type: this.selectedOrigin !== 'all' ? this.selectedOrigin : '', // Remove "all"
      ...this.extractHierarchyFilters(),
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterService.setFilters(this.filterParams, this.hierarchy.getFilters());
    this.getInspectionSheetList(this.filterParams);
  }

  /**
   * Formata os dados da ficha de inspeção para exibição na tabela.
   * @param {any[]} data - Dados das fichas de inspeção.
   * @returns {any[]} - Dados formatados para exibição.
   */
  formatInspectionSheetData(data: any[]) {
    return data.map((item) => ({
      id: item.id,
      search_identifier: item.search_identifier,
      start_date: item.start_date ? new Date(item.start_date).toLocaleDateString() : 'N/A',
      status: InspectionSheetStatus.find((status) => status.value === item.status)?.label || 'Desconhecido',
      type: InspectionSheetType.find((type) => type.value === item.type)?.label || 'Desconhecido', // Corrigido para corresponder à tabela
      occurrences: item.occurrences,
      score: item.score, // Adicionado para preencher a coluna de pontuação
      status_id: item.status
    }));
  }

  /**
   * Gerencia os filtros da pesquisa, aplicando filtros salvos ou executando nova busca.
   * @param {boolean} [triggerSearch=false] - Indica se deve executar a busca imediatamente.
   */
  managerFilters(triggerSearch: boolean = false): void {
    if (triggerSearch) {
      this.searchInspectionSheet();
    } else {
      const data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchInspectionSheet();
      } else {
        this.applySavedFilters(data.filters);

        // Configura os filtros de hierarquia, se disponíveis
        if (Object.keys(data.filtersHierarchy).length !== 0) {
          this.hierarchy.setClients(data.filtersHierarchy.clients);
          this.hierarchy.setUnits(data.filtersHierarchy.units);
          this.hierarchy.setStructures(data.filtersHierarchy.structures);
        }
      }
    }
  }

  /**
   * Reseta os filtros da pesquisa e remove filtros salvos.
   */
  resetFilter(): void {
    this.ngxSpinnerService.show();

    this.hierarchy.resetFilters();

    this.filter = {
      StartDate: '',
      EndDate: '',
      SearchIdentifier: ''
    };

    this.selectedStatus = 'all'; // Reseta para "Todos"
    this.selectedOrigin = 'all'; // Reseta para "Todas"

    this.filterParams = {};
    this.filterSearch = {};
    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    localStorage.removeItem('inspectionSheetFilters'); // Limpa os filtros persistidos
    this.managerFilters();

    this.ngxSpinnerService.hide();
  }

  /**
   * Verifica se uma estrutura foi selecionada na hierarquia.
   * @returns {boolean} - `true` se uma estrutura estiver selecionada, caso contrário `false`.
   */
  public get isStructureSelected(): boolean {
    const hierarchyFilters = this.hierarchy?.getFilters();
    return !!hierarchyFilters?.clients?.[0]?.id && !!hierarchyFilters?.units?.[0]?.id && !!hierarchyFilters?.structures?.[0]?.id;
  }

  /**
   * Obtém a estrutura selecionada e abre o modal de inserção de ficha de inspeção.
   * @returns {any} - Estrutura selecionada.
   */
  public getSelectedStructure(): any {
    const hierarchyFilters = this.hierarchy?.getFilters();

    this.structureId = hierarchyFilters?.structures?.[0].id || null;
    this.openModal(this.ModalInsertInspectionSheet);
  }

  /**
   * Método para tratar eventos de clique nas linhas da tabela.
   * Redireciona para a página apropriada com base na ação do evento.
   * @param {any} $event - O evento de clique na linha.
   */
  clickRowEvent($event: any = null) {
    const row = this.tableData.find((item) => item.id === $event.id);

    const actionLabels: any = {
      edit: 'editar',
      delete: 'excluir',
      history: 'visualizar o histórico'
    };

    const phraseEndings: any = {
      edit: 'esta Ficha de Inspeção.',
      delete: 'esta Ficha de Inspeção.',
      history: 'desta Ficha de Inspeção.'
    };

    const isRestrictedAction = ['edit', 'delete', 'history'].includes($event.action);
    const hasPermission = this.permissaoUsuario?.[$event.action];

    if (isRestrictedAction && !hasPermission) {
      this.showPermissionAlert(`Você não tem permissão para ${actionLabels[$event.action]} ${phraseEndings[$event.action]}`);
      return;
    }

    switch ($event.action) {
      case 'edit':
        this.router.navigate([`/inspections/inspection-sheet/${row.type.toLowerCase().replace(/%20|\s/g, '')}`], {
          queryParams: {
            inspectionSheetId: $event.id,
            inspectionSheetSearchIdentifier: row.search_identifier,
            status: row.status_id
          }
        });
        break;

      case 'view':
        this.router.navigate([`/inspections/inspection-sheet/${$event.id}/view`]);
        break;

      case 'delete':
        this.modalTitle = 'Excluir ficha';
        this.modalMessage = ModalConfirm.ExcluirFichadeInspecao;
        this.modalInstruction = null;
        this.modalConfig = { iconHeader: null, action: 'confirmDelete' };
        this.modalData = { id: $event.id };
        this.ModalConfirm.openModal();
        break;

      case 'confirmDelete':
        this.deleteInspectionSheet($event.data.id);
        break;

      case 'history':
        this.modalData = { id: $event.id, search_identifier: row.search_identifier };
        this.ModalHistoryInspectionSheet.openModal();
        break;
    }
  }

  /**
   * Exibe uma mensagem de alerta na tela por 5 segundos.
   */
  private showPermissionAlert(message: string): void {
    this.message = {
      text: message,
      status: true,
      class: 'alert-danger'
    };
    setTimeout(() => (this.message.status = false), 5000);
  }

  /**
   * Exclui uma ficha de inspeção com base no ID fornecido.
   * @param {string} inspectionSheetId - ID da ficha de inspeção a ser excluída.
   */
  deleteInspectionSheet(inspectionSheetId: string) {
    this.ngxSpinnerService.show();

    this.inspectionSheetServiceApi.deleteInspectionSheet(inspectionSheetId).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.DeleteInspectionSheet;
      this.message.status = true;
      this.message.class = 'alert-success';
      this.searchInspectionSheet();

      setTimeout(() => {
        this.message.status = false;
      }, 4000);

      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Abre um modal de referência se for válido.
   * @param {any} modalRef - Referência do modal a ser aberto.
   */
  openModal(modalRef: any): void {
    if (modalRef && modalRef.openModal) {
      modalRef.openModal();
    } else {
      console.error('Modal referência inválida ou método não encontrado.');
    }
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }
    this.searchInspectionSheet();
  }
}
