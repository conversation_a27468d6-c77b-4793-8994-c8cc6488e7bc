<div class="list-content">
  <!-- Notificações de Banner-->
  <app-alert
    *ngIf="showNotificationBanner"
    [class]="'alert-warning'"
    class="mt-3"
    [messages]="bannerNotifications"
    [showCloseButton]="true"
    [onClose]="handleCloseNotificationBanner.bind(this)"
  ></app-alert>

  <!-- Cadastrar novo cliente -->
  <div class="button-client">
    <app-button
      tourAnchor="register_client_button"
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Cadastrar novo cliente'"
      [routerLink]="['create']"
    ></app-button>
  </div>

  <div class="row g-3 mt-1">
    <!-- SearchIdentifier -->
    <div class="col-md-2" tourAnchor="id_filter">
      <label class="form-label">ID</label>
      <input
        [(ngModel)]="filter.SearchIdentifier"
        type="number"
        step="1"
        min="1"
        class="form-control"
        placeholder="ID cliente"
        autocomplete="off"
        (keypress)="
          func.controlNumber(
            $event,
            filter.SearchIdentifier,
            'positive',
            'ngModel'
          )
        "
        (keyup)="
          func.controlNumber($event, filter.SearchIdentifier, null, 'ngModel')
        "
      />
    </div>
    <!-- Select Cliente -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-6"
      tourAnchor="client_dropdown"
    ></app-hierarchy>
    <!-- Status -->
    <div class="col-md-2" tourAnchor="status_dropdown">
      <label class="form-label">Status</label>
      <select class="form-select" [(ngModel)]="filter.Active">
        <option value="">Selecione...</option>
        <option *ngFor="let item of status" [ngValue]="item.value">
          {{ item.status }}
        </option>
      </select>
    </div>
    <!-- Visualização -->
    <div class="col-md-2" tourAnchor="visualization_dropdown">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Períodos -->
    <div class="col-md-4">
      <div class="card" tourAnchor="test_period">
        <div class="card-header periods text-center">Período de Teste</div>
        <div class="card-body">
          <div class="row">
            <div class="col-6 col-xs-12 text-center">
              <label class="form-label">Início</label>
              <input
                [(ngModel)]="filter.StartTrialPeriod"
                type="date"
                class="form-control"
              />
            </div>
            <div class="col-6 col-xs-12 text-center">
              <label class="form-label">Fim</label>
              <input
                [(ngModel)]="filter.EndTrialPeriod"
                type="date"
                class="form-control"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Período Contratual -->
    <div class="col-md-4">
      <div class="card" tourAnchor="contractual_period">
        <div class="card-header periods text-center">Período Contratual</div>
        <div class="card-body">
          <div class="row">
            <div class="col-6 col-xs-12 text-center">
              <label class="form-label">Início</label>
              <input
                [(ngModel)]="filter.StartContractualPeriod"
                type="date"
                class="form-control"
              />
            </div>
            <div class="col-6 col-xs-12 text-center">
              <label class="form-label">Fim</label>
              <input
                [(ngModel)]="filter.EndContractualPeriod"
                type="date"
                class="form-control"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Botões -->
    <div class="col-md-4 d-flex align-items-end justify-content-end">
      <app-button
        tourAnchor="search_button"
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        class="me-2"
        (click)="managerFilters(true)"
      ></app-button>
      <app-button
        tourAnchor="reset_button"
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>
  </div>

  <!-- Alertas -->
  <div
    class="alert alert-warning mt-4"
    role="alert"
    *ngIf="messageReturn.status"
  >
    {{ messageReturn.text }}
  </div>

  <div class="alert alert-success mt-4" role="alert" *ngIf="message.status">
    {{ message.text }}
  </div>

  <div
    class="alert alert-warning mt-4"
    role="alert"
    *ngIf="messageWarning.status"
  >
    {{ messageWarning.text }}
  </div>

  <!-- Tabela -->
  <div class="col-12 mt-3" tourAnchor="table_clients">
    <app-table
      *ngIf="tableData.length > 0"
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeader"
      [tableSubheader]="tableSubheader"
      [tableData]="tableData"
      (sendToggleStatus)="toggleStatus($event)"
      [permissaoUsuario]="permissaoUsuario"
    ></app-table>
    <!-- Tabela -->

    <!-- Paginação -->
    <app-paginator
      tourAnchor="paginator"
      *ngIf="tableData.length > 0"
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event)"
      [enableItemPerPage]="true"
    ></app-paginator>
  </div>

  <!-- Botão Voltar -->
  <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
    <app-button
      tourAnchor="back_button"
      [class]="'btn-logisoil-blue'"
      [label]="'Voltar à tela inicial'"
      [icon]="'fa fa-arrow-left'"
      [click]="goBack.bind(this)"
      class="me-1"
    ></app-button>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>

<tour-step-template></tour-step-template>
