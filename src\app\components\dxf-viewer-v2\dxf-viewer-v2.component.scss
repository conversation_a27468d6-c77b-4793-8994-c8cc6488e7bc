.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
}

.root-layers {
  overflow-y: auto;
  overflow-x: auto;
}

.layer-item {
  cursor: pointer;
}

.layer-item:hover {
  background-color: rgba($color: #000000, $alpha: 0.15);
}

.dxf-container {
  border: 1px solid #ddd;
  border-radius: 5px;
  position: relative;
  height: 100%;
}

.dxf-image {
  background-color: #f0f0f0;
  border-radius: 5px;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  display: flex;
}

.canvasContainer {
  flex: 1;
}

.dropdown-menu {
  margin: 0 auto;
  right: 0;
}

.container {
  margin: 0;
  padding: 0;
  height: 100%;
}
