<div class="list-content mt-2">
  <!-- Alerta -->
  <div
    class="alert alert-success mt-2"
    role="alert"
    *ngIf="messageSection.status"
  >
    {{ messageSection.text }}
  </div>
  <!-- Alerta -->

  <div class="row g-3 mt-2">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="generalTabConfig.active ? 'active' : ''"
          id="general-tab"
          type="button"
          role="tab"
          aria-controls="general"
          aria-selected="true"
          (click)="crtlSaveSection = ''; selectTab('general')"
          [style.background-color]="
            generalTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="generalTabConfig.styleColor ? '#ffffff' : ''"
        >
          Geral
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="reviewTabConfig.active ? 'active' : ''"
          id="review-tab"
          type="button"
          role="tab"
          aria-controls="review"
          aria-selected="true"
          (click)="crtlSaveSection = 'review'; selectTab('review')"
          [style.background-color]="reviewTabConfig.styleColor ? '#dc3545' : ''"
          [style.color]="reviewTabConfig.styleColor ? '#ffffff' : ''"
        >
          Revisão Seção
        </button>
      </li>
    </ul>

    <div class="tab-content" id="myTabContent">
      <!-- Aba Geral -->
      <div
        class="tab-pane fade"
        [ngClass]="generalTabConfig.active ? 'show active' : ''"
        id="general"
        role="tabpanel"
        aria-labelledby="general-tab"
      >
        <app-general-tab
          [view]="view"
          [edit]="edit"
          [profile]="profile"
          [permissaoUsuario]="permissaoUsuario"
          (sendStructure)="setStructure($event)"
        ></app-general-tab>
      </div>
      <!-- Aba Revisão Seção -->
      <div
        class="tab-pane fade"
        [ngClass]="reviewTabConfig.active ? 'show active' : ''"
        id="review"
        role="tabpanel"
        aria-labelledby="review-tab"
      >
        <app-review-tab
          [edit]="edit"
          [view]="view"
          [sectionId]="sectionId"
          [profile]="profile"
          [permissaoUsuario]="permissaoUsuario"
        ></app-review-tab>
      </div>
    </div>
  </div>

  <div class="row mt-2">
    <!-- Mensagens de erro -->
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <!-- Botões -->
  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-green'"
      [icon]="'fa fa-thin fa-floppy-disk'"
      [label]="'Salvar'"
      [type]="false"
      class="me-1"
      (click)="validate()"
      *ngIf="crtlSaveSection != '' && !view && formCrtl"
    >
    </app-button>
    <!-- Botão Retornar -->
    <app-button
      *ngIf="reviewTabConfig.active"
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Retornar'"
      class="me-1"
      (click)="onBack()"
    ></app-button>
    <!-- Botão Avançar -->
    <app-button
      *ngIf="generalTabConfig.active"
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-right'"
      [label]="'Avançar'"
      class="me-1"
      (click)="onNext()"
    ></app-button>
  </div>

  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      class="me-1"
      [routerLink]="['/sections']"
    ></app-button>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
