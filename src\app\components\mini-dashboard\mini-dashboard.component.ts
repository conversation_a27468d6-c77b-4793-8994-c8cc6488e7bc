import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MenuService } from 'src/app/services/menu.service';

@Component({
  selector: 'app-mini-dashboard',
  templateUrl: './mini-dashboard.component.html',
  styleUrls: ['./mini-dashboard.component.scss']
})
export class MiniDashboardComponent implements OnInit {
  @Input() menu: string = '';
  @Input() config: any = null;
  @Output() public sendClickRowEvent = new EventEmitter();
  @Input() actions: any[] | null = null;

  public dropdownMiniDashboard = false;

  constructor(private menuService: MenuService) {}

  public miniDashboard: any = null;

  ngOnInit(): void {
    if (!this.actions || this.actions.length === 0) {
      this.miniDashboard = this.menuService.setMenu(this.menu, this.config);
    }
  }

  onClickedOutside(element: string) {
    switch (element) {
      case 'miniDashboard':
        this.dropdownMiniDashboard = false;
        break;
    }
  }

  public clickEvent(action: string, params: any = {}): void {
    if (action) {
      this.sendClickRowEvent.emit({ action, ...params });
    }
  }
}
