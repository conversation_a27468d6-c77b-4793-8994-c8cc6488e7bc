import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalInsertAspectComponent } from './modal-insert-aspect.component';

describe('ModalInsertAspectComponent', () => {
  let component: ModalInsertAspectComponent;
  let fixture: ComponentFixture<ModalInsertAspectComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalInsertAspectComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalInsertAspectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
