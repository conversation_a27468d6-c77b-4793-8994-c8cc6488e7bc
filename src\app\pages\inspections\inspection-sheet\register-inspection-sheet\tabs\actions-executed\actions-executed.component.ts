import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { Anomaly, ActionResultClassification } from 'src/app/constants/inspections.constants';

@Component({
  selector: 'app-actions-executed',
  templateUrl: './actions-executed.component.html',
  styleUrls: ['./actions-executed.component.scss']
})
export class ActionsExecutedComponent implements OnInit, OnChanges {
  @Input() public inspectionSheetType: number = null;
  @Input() public status: number = null;
  @Input() public locked: boolean = false;
  @Input() public view: boolean = false;
  @Output() public formChanged = new EventEmitter<any>();

  public actionsForm: FormGroup = this.fb.group({
    anomalies: this.fb.array([]) // FormArray para as anomalias
  });

  public anomaly = Anomaly; // Mapeamento de anomalias da constante
  public actionResultClassificationOptions = ActionResultClassification; // Mapeamento de classificações

  constructor(private fb: FormBuilder) { }

  ngOnInit(): void { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.status || changes.locked) {
      this.toggleForm();
    }
  }

  get anomalies(): FormArray {
    return this.actionsForm.get('anomalies') as FormArray;
  }

  setData(dados: any): void {
    const anomaliesData = dados?.identified_anomalies || [];
    this.anomalies.clear(); // Limpa o FormArray antes de popular

    anomaliesData.forEach((anomaly) => {
      this.anomalies.push(
        this.fb.group({
          Id: [anomaly.id],
          Anomaly: [anomaly.anomaly],
          ExecutedActions: [anomaly.executed_actions],
          ActionResultClassification: [anomaly.action_result_classification],
          AnomalyScore: [anomaly.anomaly_score],
          Note: [anomaly.note]
        })
      );
    });

    this.toggleForm();
  }

  // Método para buscar o nome da anomalia baseado no ID
  getAnomalyName(anomalyId: number): string {
    const anomalyKey = Object.keys(this.anomaly).find((key) => this.anomaly[key].id === anomalyId);
    return anomalyKey ? this.anomaly[anomalyKey].name : 'Anomalia desconhecida';
  }

  // Evento para mudanças (select)
  onChange(controlName: string, index?: number): void {
    if (this.actionsForm.disabled) return;
    const control = this.anomalies.at(index).get(controlName);
    if (control?.dirty) {
      this.triggerSave();
    }
  }

  // Evento para blur (textarea)
  onBlur(controlName: string, index?: number): void {
    if (this.actionsForm.disabled) return;
    const control = this.anomalies.at(index).get(controlName);
    if (control?.dirty) {
      this.triggerSave();
    }
  }

  // Dispara o salvamento parcial
  triggerSave(): void {
    this.formChanged.emit();
  }

  toggleForm() {
    const isLocked = this.locked || [2, 3].includes(this.status);
    if (isLocked) {
      this.actionsForm.disable();
    } else {
      this.actionsForm.enable();
    }
  }

  getActionResultLabel(value: any): string {
    const option = this.actionResultClassificationOptions.find((opt) => opt.value === value);
    return option ? option.label : '';
  }
}
