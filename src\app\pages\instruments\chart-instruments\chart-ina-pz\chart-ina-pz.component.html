<form [formGroup]="formChart">
  <div class="row g-3 mt-1">
    <!-- Instrumento -->
    <div class="col-md-3">
      <label class="form-label">Instrumento:</label>
      <ng-multiselect-dropdown
        [settings]="instrumentsSettings"
        [data]="instruments"
        formControlName="instrument"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Periodo -->
    <div class="col-md-3">
      <label class="form-label">Período:</label>
      <select class="form-select" formControlName="period">
        <option value="">Selecione...</option>
        <option *ngFor="let item of periods" [ngValue]="item.value">
          {{ item.label }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('period').valid && formChart.get('period').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>
  <!-- Configuracoes do grafico -->
  <div class="row mt-3" *ngIf="!ctrlConfiguration">
    <app-button
      [class]="'btn-logisoil-green text-nowrap'"
      [customBtn]="true"
      [icon]="'fa fa-cog'"
      [label]="'Configurações do gráfico'"
      (click)="ctrlConfiguration = !ctrlConfiguration"
    ></app-button>
  </div>
  <div class="row mt-3" *ngIf="ctrlConfiguration">
    <div class="col-md-12">
      <div class="card">
        <div
          class="card-header form-control-bg"
          (click)="ctrlConfiguration = !ctrlConfiguration"
          style="cursor: pointer"
        >
          <i class="fa fa-cog me-2"></i>
          Configurações do gráfico
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-2">
              <label class="form-label">Tipo de linha:</label>
              <select class="form-select" formControlName="line_type">
                <option
                  *ngFor="let item of chartLineSetting"
                  [ngValue]="item.id"
                >
                  {{ item.value }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Espessura da linha:</label>
              <input
                type="number"
                class="form-control"
                min="1"
                max="5"
                step="1"
                formControlName="line_stroke"
              />
            </div>
            <div class="col-md-2">
              <label class="form-label">Intervalo eixo X:</label>
              <input
                type="number"
                class="form-control"
                step="0.1"
                formControlName="xAxis_interval"
              />
            </div>
            <div class="col-md-2">
              <label class="form-label">Rotação texto eixo X:</label>
              <select class="form-select" formControlName="xAxis_rotate">
                <option value="">Selecione...</option>
                <option value="0">Horizontal</option>
                <option value="90">Vertical</option>
                <option value="30">30 graus</option>
                <option value="45">45 graus</option>
                <option value="60">60 graus</option>
              </select>
            </div>
          </div>
          <div class="row mt-3">
            <!-- Leituras -->
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">Leituras:</div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <label class="form-label">Marcador</label>
                      <ng-select
                        [items]="markers"
                        formControlName="marker"
                        bindValue="marker"
                        bindLabel="label"
                        [clearable]="false"
                        [multiple]="false"
                        [closeOnSelect]="true"
                      >
                        <ng-template ng-label-tmp let-item="item">
                          <div style="display: flex; align-items: center">
                            <img
                              [src]="item.img"
                              alt="{{ item.label }}"
                              style="
                                width: 50px;
                                height: 20px;
                                margin-right: 8px;
                              "
                            />
                          </div>
                        </ng-template>
                        <ng-template
                          ng-option-tmp
                          let-item="item"
                          let-index="index"
                        >
                          <div style="display: flex; align-items: center">
                            <img
                              [src]="item.img"
                              alt="{{ item.label }}"
                              style="
                                width: 50px;
                                height: 20px;
                                margin-right: 8px;
                              "
                            />
                          </div>
                        </ng-template>
                      </ng-select>
                    </div>
                    <div class="col-md-4">
                      <label class="form-label">Tamanho marcador</label>
                      <input
                        type="number"
                        class="form-control"
                        min="1"
                        max="12"
                        step="1"
                        formControlName="marker_length"
                      />
                    </div>
                    <div class="col-md-3 align-self-end">
                      <input
                        class="form-check-input me-1"
                        type="checkbox"
                        formControlName="only_markers"
                      />
                      <label class="form-label">Somente marcadores</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- Leituras secas -->
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">Leituras Secas:</div>
                <div class="card-body">
                  <div class="row">
                    <!-- Marcador -->
                    <div class="col-md-4">
                      <label class="form-label">Marcador</label>
                      <ng-select
                        [items]="markers"
                        formControlName="dry_marker"
                        bindValue="marker"
                        bindLabel="label"
                        [clearable]="false"
                        [multiple]="false"
                        [closeOnSelect]="true"
                      >
                        <ng-template ng-label-tmp let-item="item">
                          <div style="display: flex; align-items: center">
                            <img
                              [src]="item.img"
                              alt="{{ item.label }}"
                              style="
                                width: 50px;
                                height: 20px;
                                margin-right: 8px;
                              "
                            />
                          </div>
                        </ng-template>
                        <ng-template
                          ng-option-tmp
                          let-item="item"
                          let-index="index"
                        >
                          <div style="display: flex; align-items: center">
                            <img
                              [src]="item.img"
                              alt="{{ item.label }}"
                              style="
                                width: 50px;
                                height: 20px;
                                margin-right: 8px;
                              "
                            />
                          </div>
                        </ng-template>
                      </ng-select>
                    </div>
                    <!-- Tamanho marcador -->
                    <div class="col-md-4">
                      <label class="form-label">Tamanho marcador</label>
                      <input
                        type="number"
                        class="form-control"
                        min="1"
                        max="12"
                        step="1"
                        formControlName="dry_marker_length"
                      />
                    </div>
                    <!-- Color -->
                    <div
                      class="col-12 col-md-4 d-flex flex-column justify-content-start"
                      (clickOutside)="onClickedOutside('colorPicker', 0)"
                    >
                      <label class="form-label">Cor</label>
                      <button
                        class="color form-control"
                        (click)="showColorPicker[0] = !showColorPicker[0]"
                        [style.background]="selectedColor[0]"
                      ></button>
                      <div
                        *ngIf="showColorPicker[0]"
                        style="position: relative"
                      >
                        <color-sketch
                          [color]="selectedColor[0]"
                          (onChangeComplete)="changeComplete($event, 0)"
                          class="color-picker-container"
                        ></color-sketch>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Eixo Y -->
          <div class="row">
            <ng-container *ngFor="let itemYAxis of yAxisLabel; let i = index">
              <div [ngClass]="i == 0 ? 'col-md-12' : 'col-md-6'">
                <div class="card mt-3">
                  <div
                    class="card-header"
                    (click)="yAxisLabel[i].show = !yAxisLabel[i].show"
                    style="cursor: pointer"
                  >
                    {{ itemYAxis.label }}{{ i > 0 ? ' - Opcional' : '' }}
                    <i
                      [ngClass]="
                        yAxisLabel[i].show
                          ? 'fa fa-chevron-up'
                          : 'fa fa-chevron-down'
                      "
                    ></i>
                  </div>
                  <div class="card-body" *ngIf="itemYAxis.show">
                    <div class="row">
                      <div
                        class="col-md-12 align-self-end"
                        *ngIf="[1, 2].includes(itemYAxis.id)"
                      >
                        <input
                          class="form-check-input me-1"
                          type="checkbox"
                          formControlName="only{{ itemYAxis.id }}_markers"
                        />
                        <label class="form-label">Somente marcadores</label>
                      </div>
                      <div class="col-md-4" *ngIf="itemYAxis.id == 1">
                        <label class="form-label"
                          >Régua linimétrica - Montante:</label
                        >
                        <ng-multiselect-dropdown
                          [placeholder]="'Selecione...'"
                          [settings]="linimetricSettings"
                          [data]="instrument_upstream"
                          formControlName="instrument_upstream"
                        >
                        </ng-multiselect-dropdown>
                      </div>
                      <div class="col-md-4" *ngIf="itemYAxis.id == 1">
                        <label class="form-label"
                          >Régua linimétrica - Jusante:</label
                        >
                        <ng-multiselect-dropdown
                          [placeholder]="'Selecione...'"
                          [settings]="linimetricSettings"
                          [data]="instrument_downstream"
                          formControlName="instrument_downstream"
                        >
                        </ng-multiselect-dropdown>
                      </div>
                      <div class="col-md-4" *ngIf="itemYAxis.id == 2">
                        <label class="form-label"
                          >Pluviômetro/Pluviógrafo:</label
                        >
                        <ng-multiselect-dropdown
                          [placeholder]="'Selecione...'"
                          [settings]="climateSettings"
                          [data]="instrument_climate"
                          formControlName="instrument_climate"
                        >
                        </ng-multiselect-dropdown>
                      </div>
                      <div class="w-100"></div>
                      <div
                        [ngClass]="itemYAxis.id == 0 ? 'col-md-2' : 'col-md-4'"
                      >
                        <label class="form-label">Valor mínimo eixo Y:</label>
                        <input
                          type="number"
                          class="form-control"
                          step="0.1"
                          formControlName="yAxis{{ itemYAxis.id }}_min"
                        />
                      </div>
                      <div
                        [ngClass]="itemYAxis.id == 0 ? 'col-md-2' : 'col-md-4'"
                      >
                        <label class="form-label">Valor máximo eixo Y:</label>
                        <input
                          type="number"
                          class="form-control"
                          step="0.1"
                          formControlName="yAxis{{ itemYAxis.id }}_max"
                        />
                      </div>
                      <div
                        [ngClass]="itemYAxis.id == 0 ? 'col-md-2' : 'col-md-4'"
                      >
                        <label class="form-label">Intervalo eixo Y:</label>
                        <input
                          type="number"
                          class="form-control"
                          step="0.1"
                          formControlName="yAxis{{ itemYAxis.id }}_interval"
                        />
                      </div>
                    </div>
                    <div class="row mt-2" *ngIf="[1, 2].includes(itemYAxis.id)">
                      <div class="col-md-4" *ngIf="itemYAxis.id == 2">
                        <label class="form-label">Interpretação</label>
                        <select class="form-select">
                          <option value="max">Máximo</option>
                          <option>Acumulado</option>
                        </select>
                      </div>
                      <div class="col-md-4" *ngIf="itemYAxis.id == 2">
                        <label class="form-label">Tipo de gráfico</label>
                        <select
                          class="form-select"
                          formControlName="graphic{{ itemYAxis.id }}_type"
                        >
                          <option value="bar">Barras</option>
                          <option value="line">Linhas</option>
                        </select>
                      </div>
                      <div
                        class="col-md-4"
                        *ngIf="
                          itemYAxis.id == 1 ||
                          (itemYAxis.id == 2 &&
                            formChart.controls[
                              'graphic' + itemYAxis.id + '_type'
                            ].value == 'line')
                        "
                      >
                        <label class="form-label">Marcador</label>
                        <ng-select
                          [items]="markers"
                          formControlName="marker{{ itemYAxis.id }}"
                          bindValue="marker"
                          bindLabel="label"
                          [clearable]="false"
                          [multiple]="false"
                          [closeOnSelect]="true"
                        >
                          <ng-template ng-label-tmp let-item="item">
                            <div style="display: flex; align-items: center">
                              <img
                                [src]="item.img"
                                alt="{{ item.label }}"
                                style="
                                  width: 50px;
                                  height: 20px;
                                  margin-right: 8px;
                                "
                              />
                            </div>
                          </ng-template>
                          <ng-template
                            ng-option-tmp
                            let-item="item"
                            let-index="index"
                          >
                            <div style="display: flex; align-items: center">
                              <img
                                [src]="item.img"
                                alt="{{ item.label }}"
                                style="
                                  width: 50px;
                                  height: 20px;
                                  margin-right: 8px;
                                "
                              />
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                      <div
                        class="col-md-4"
                        *ngIf="
                          itemYAxis.id == 1 ||
                          (itemYAxis.id == 2 &&
                            formChart.controls[
                              'graphic' + itemYAxis.id + '_type'
                            ].value == 'line')
                        "
                      >
                        <label class="form-label">Tamanho marcador</label>
                        <input
                          type="number"
                          class="form-control"
                          min="1"
                          max="12"
                          step="1"
                          formControlName="marker{{ itemYAxis.id }}_length"
                        />
                      </div>
                      <div
                        class="col-12 col-md-4 d-flex flex-column justify-content-start"
                        (clickOutside)="
                          onClickedOutside('colorPicker', itemYAxis.id)
                        "
                      >
                        <label class="form-label">Cor</label>
                        <button
                          class="color form-control"
                          (click)="
                            showColorPicker[itemYAxis.id] =
                              !showColorPicker[itemYAxis.id]
                          "
                          [style.background]="selectedColor[itemYAxis.id]"
                        ></button>
                        <div
                          *ngIf="showColorPicker[itemYAxis.id]"
                          style="position: relative"
                        >
                          <color-sketch
                            [color]="selectedColor[itemYAxis.id]"
                            (onChangeComplete)="
                              changeComplete($event, itemYAxis.id)
                            "
                            class="color-picker-container"
                          ></color-sketch>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </div>
          <div class="col-md-12 d-flex align-items-end justify-content-end">
            <app-button
              [class]="'btn-logisoil-gray text-nowrap mt-3'"
              [icon]="'fa fa-eraser'"
              [label]="'Limpar Configurações'"
              (click)="resetConfigurations()"
            ></app-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Grafico -->
  <div class="row mt-3">
    <div class="col-md-4">
      <app-button
        [class]="'btn-logisoil-green me-2 text-nowrap'"
        [customBtn]="true"
        [icon]="'fa fa-line-chart'"
        [label]="'Gerar gráfico'"
        (click)="getChart()"
      ></app-button>
    </div>
  </div>
  <!-- Alerta -->
  <div class="row mt-3">
    <div class="col-md-12">
      <div
        class="alert alert-warning"
        role="alert"
        *ngIf="messageReturn.status"
      >
        {{ messageReturn.text }}
      </div>
    </div>
  </div>

  <div class="row mt-2 mb-3">
    <div class="col-md-2" *ngIf="chart.options">
      <label class="form-label"
        ><label class="form-label"
          >Tamanho: {{ formChart.controls['chart_height'].value }} px</label
        >
        <input
          type="range"
          class="range"
          #chartHeight
          min="300"
          max="1600"
          step="10"
          formControlName="chart_height"
          (input)="setHeight(chartHeight.value)"
        />
      </label>
    </div>
    <div class="col-md-12" *ngIf="chart.options">
      <app-e-charts
        [dataChart]="chart"
        [height]="formChart.controls['chart_height'].value"
      ></app-e-charts>
    </div>
  </div>

  <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [click]="goBack.bind(this)"
    ></app-button>
  </div>
</form>
