<div
  class="add-inspection d-flex align-items-end justify-content-end mt-2 mb-3"
>
  <app-button
    *ngIf="!view"
    [class]="'btn-logisoil-green'"
    [customBtn]="true"
    [icon]="'fas fa-plus-circle'"
    [label]="'Inserir aspecto'"
    (click)="openInsertAspectModal()"
  ></app-button>
</div>

<div class="row" [formGroup]="areaForm">
  <div class="accordion" formArrayName="areas">
    <div
      class="accordion-item"
      *ngFor="let area of areaForm.get('areas')['controls']; let i = index"
      [formGroupName]="i"
    >
      <h2 class="accordion-header">
        <button
          *ngIf="!view"
          class="accordion-button"
          [class.collapsed]="activeItemId !== area.get('id').value"
          (click)="toggleItem(area.get('id').value)"
          type="button"
          aria-expanded="activeItemId  === area.get('id').value"
        >
          {{ i + 1 }} - {{ area.get('name').value }}
        </button>
        <span
          class="accordion-button accordion-button-view"
          *ngIf="view"
          aria-expanded="true"
          style="cursor: default"
        >
          {{ i + 1 }} - {{ area.get('name').value }}
        </span>
      </h2>
      <div
        class="accordion-collapse"
        [@accordionAnimation]="
          view
            ? 'open'
            : activeItemId === area.get('id').value
            ? 'open'
            : 'closed'
        "
      >
        <div class="accordion-body" formArrayName="aspects">
          <ng-container
            *ngFor="
              let aspect of area.get('aspects')['controls'];
              let j = index
            "
            [formGroupName]="j"
          >
            <app-aspects-item
              [formGroup]="aspect"
              [index]="i + 1 + '.' + (j + 1)"
              [hierarchy]="hierarchy"
              [occurrencesLinkable]="
                getOccurrencesForAspect(
                  area.get('id').value,
                  aspect.get('id').value
                )
              "
              (updateOccurrence)="onUpdateOccurrence($event, area, j)"
              [item]="{
                area: area,
                areaIndex: i,
                aspect: aspect,
                aspectIndex: j
              }"
              (showMap)="showMap($event)"
              (formChanged)="onFormChange($event)"
              [status]="status"
              [locked]="locked"
              [view]="view"
            ></app-aspects-item>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>

<app-modal-insert-aspect
  #modalInsertAspect
  [areasAspectsList]="areasAspectsList"
  [areas]="areas"
  (aspectInserted)="onAspectInserted($event)"
></app-modal-insert-aspect>
