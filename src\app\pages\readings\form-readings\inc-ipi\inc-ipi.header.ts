const incIpiTableHeader = [
  {
    label: 'Selecionar',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['select'],
    type: 'check'
  },
  {
    label: 'ID',
    width: '60px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['reading_search_identifier']
  },
  {
    label: 'Instrumento',
    width: '50%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['identifier']
  },
  {
    label: 'Data e hora',
    width: '50%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['date_format']
  },
  {
    label: 'Referência',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['is_referential_reading']
  },
  {
    label: 'Ponto de medição',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['measure']
  },
  {
    label: 'Profundidade (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['depth']
  },

  {
    label: 'Leitura eixo A (graus)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['a_axis_reading']
  },
  {
    label: 'Leitura eixo B (graus)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['b_axis_reading']
  },
  {
    label: 'Deslocamento médio A (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['average_displacement_a']
  },
  {
    label: 'Deslocamento médio B (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['average_displacement_b']
  },
  {
    label: 'Deslocamento acum. A (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['accumulated_displacement_a']
  },
  {
    label: 'Deslocamento acum. B (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['accumulated_displacement_b']
  },
  {
    label: 'Desvio A (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['deviation_a']
  },
  {
    label: 'Desvio B (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['deviation_b']
  },
  {
    label: 'Ações',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['actionCustom']
  }
];

export { incIpiTableHeader };
