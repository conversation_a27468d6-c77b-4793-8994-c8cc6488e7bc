<form class="row g-3">
  <div class="col-md-3">
    <app-button
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Adicionar Revisão Material'"
      data-bs-toggle="tooltip"
      data-bs-placement="bottom"
      [disabled]="view"
      (click)="resetForm('review', !view)"
      *ngIf="!editReview"
    >
    </app-button>
  </div>
</form>

<form class="mt-4" [formGroup]="formStaticMaterialsReview">
  <div *ngIf="ctrlReview">
    <ul class="nav nav-tabs px-2">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page"
          >{{
            viewReview ? 'Visualizar' : editReview ? 'Editar' : 'Nova'
          }}
          Revisão</a
        >
      </li>
    </ul>
    <div class="row mt-3">
      <!-- Data e hora -->
      <div class="col-md-3">
        <label class="form-label">Data e hora de início:</label>
        <input
          type="datetime-local"
          class="form-control"
          formControlName="start_date"
        />
      </div>
    </div>
    <div class="row mt-3">
      <!-- Descrição -->
      <div class="col-md-12">
        <label class="form-label">Descrição (Opcional):</label>
        <textarea
          pInputTextArea
          rows="2"
          class="form-control"
          formControlName="description"
          data-ls-module="charCounter"
          maxlength="100"
          (input)="onValueChange($event)"
        ></textarea>
      </div>
      <small class="form-text">Caracteres {{ counter }} (Máximo: 100) </small>
    </div>
    <small
      class="form-text text-danger"
      *ngIf="
        !formStaticMaterialsReview.get('description').valid &&
        formStaticMaterialsReview.get('description').touched
      "
      ><i class="fa fa-exclamation-circle me-2"></i>Limite de caracteres:
      100.</small
    >
    <hr />
    <!-- Formulário Revisão Material -->
    <app-tabs-conditions
      [view]="viewReview"
      [edit]="editReview"
      [info]="
        'Preencha ao menos um dos formulários abaixo para completar o cadastro da revisão do material.'
      "
      #tabConditionsReview
    ></app-tabs-conditions>

    <!-- Botões -->
    <div class="row mt-2">
      <div class="col-md-12 d-flex justify-content-end">
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fas fa-plus-circle'"
          [label]="'Adicionar'"
          [type]="true"
          class="me-1"
          [disabled]="!formStaticMaterialsReview.valid"
          (click)="validateReview()"
          *ngIf="!editReview"
          [disabled]="view"
        >
        </app-button>
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-pencil'"
          [label]="'Editar'"
          [type]="true"
          class="me-1"
          [disabled]="!formStaticMaterialsReview.valid"
          *ngIf="editReview && !viewReview"
          [disabled]="viewReview"
          (click)="validateReview()"
        >
        </app-button>
        <app-button
          [class]="'btn-logisoil-red'"
          [icon]="'fa fa-thin fa-xmark'"
          [label]="'Cancelar'"
          [type]="true"
          class="me-1"
          (click)="ctrlReview = false; resetForm('review')"
        >
        </app-button>
      </div>
    </div>
  </div>
</form>

<!-- Alertas -->
<div
  class="mt-2 col-md-12 alert"
  [ngClass]="message.class"
  role="alert"
  *ngIf="message.status"
  [innerHTML]="message.text"
></div>
<app-alert
  class="mt-2"
  [class]="'alert-danger'"
  [messages]="messagesError"
></app-alert>

<ul *ngIf="!ctrlReview" class="px-2 mt-4 nav nav-tabs">
  <li class="nav-item">
    <a class="nav-link active" aria-current="page">Histórico de Revisões</a>
  </li>
</ul>

<!-- Histórico de Revisões -->
<div class="row mt-2">
  <app-table
    *ngIf="!ctrlReview"
    [messageReturn]="messageReturn"
    [tableHeader]="tableHeader"
    [tableData]="tableData"
    [permissaoUsuario]="permissaoUsuario"
    [actionCustom]="actionCustom"
    (sendClickRowEvent)="clickRowEvent($event)"
  >
  </app-table>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
