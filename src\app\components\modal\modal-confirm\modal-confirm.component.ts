import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-confirm',
  templateUrl: './modal-confirm.component.html',
  styleUrls: ['./modal-confirm.component.scss']
})
export class ModalConfirmComponent implements OnInit {
  @ViewChild('modalConfirm') modalConfirm: ElementRef;
  @Output() public sendClickEvent = new EventEmitter();
  @Input() public title: string = '';
  @Input() public message: string = '';
  @Input() public instruction: string = '';
  @Input() public id: string = '';
  @Input() public data: any = {};
  @Input() public modalConfig: any = {
    iconHeader: 'fa fa-trash-o',
    action: 'confirm'
  };

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  /**
   * Abre o modal de confirmação utilizando o serviço de modais (`modalService`).
   *
   * Este método é responsável por exibir a caixa de diálogo referenciada em `modalConfirm`.
   */
  openModal() {
    this.modalService.open(this.modalConfirm);
  }

  /**
   * Emite um evento de clique contendo os dados da ação configurada no modal.
   *
   * - O evento emitido inclui:
   *   - Ação (`this.modalConfig.action`)
   *   - Identificador (`this.id`)
   *   - Dados relacionados (`this.data`)
   *
   * Este método pode ser chamado ao clicar em botões de confirmação, cancelamento ou outros controles do modal.
   *
   * @param {any} action - (opcional) Ação alternativa ou sobrescrita.
   */
  clickRowEvent(action: any = null) {
    this.sendClickEvent.emit({ action: this.modalConfig.action, id: this.id, data: this.data });
  }

  /**
   * Emite o evento de confirmação com os dados da modal.
   * O `context` deve ser definido em `data.context` no componente pai.
   */
  confirm(): void {
    this.sendClickEvent.emit({
      data: this.data,
      id: this.id
    });
  }
}
