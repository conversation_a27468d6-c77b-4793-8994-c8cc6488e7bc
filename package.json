{"name": "logisoil-frontend-v3", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "playwright:install": "playwright install"}, "private": true, "dependencies": {"@angular/animations": "~13.1.0", "@angular/cdk": "~13.1.0", "@angular/common": "~13.1.0", "@angular/compiler": "~13.1.0", "@angular/core": "~13.1.0", "@angular/forms": "~13.1.0", "@angular/google-maps": "~13.3.9", "@angular/localize": "^13.3.3", "@angular/platform-browser": "~13.1.0", "@angular/platform-browser-dynamic": "~13.1.0", "@angular/router": "~13.1.0", "@brazilian-utils/brazilian-utils": "^1.0.0-rc.12", "@fortawesome/fontawesome-free": "^6.1.1", "@fullcalendar/angular": "^6.0.2", "@fullcalendar/core": "^6.0.2", "@fullcalendar/daygrid": "^6.0.2", "@fullcalendar/interaction": "^6.0.2", "@fullcalendar/list": "^6.0.2", "@fullcalendar/timegrid": "^6.0.2", "@ng-bootstrap/ng-bootstrap": "^12.0.2", "@ng-idle/core": "^10.0.0", "@ng-idle/keepalive": "^10.0.0", "@ng-select/ng-select": "^8.3.0", "@popperjs/core": "^2.11.8", "@tmcw/togeojson": "^5.8.1", "@tweenjs/tween.js": "^23.1.2", "@types/three": "^0.161.0", "@xmldom/xmldom": "^0.8.10", "angular-auth-oidc-client": "^11.6.10", "angular-draggable-droppable": "^8.0.0", "angular-resizable-element": "^7.0.0", "angular-resize-event": "^3.1.1", "bootstrap": "^5.2.0", "bootstrap-icons": "^1.8.1", "br-mask": "^0.0.10", "calendar-utils": "^0.10.4", "d3": "^7.8.5", "date-fns": "^2.28.0", "decimal.js": "^10.4.3", "dxf-viewer": "^1.0.36", "echarts": "^5.4.3", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "jquery": "^3.6.0", "jwt-decode": "^3.1.2", "moment": "^2.29.3", "moment-timezone": "^0.5.45", "ng-multiselect-dropdown": "^0.3.8", "ng2-charts": "^3.1.2", "ng4-click-outside": "^1.0.1", "ngx-color": "^7.3.3", "ngx-echarts": "^8.0.1", "ngx-spinner": "^13.1.1", "ngx-toastr": "^13.2.1", "ngx-ui-tour-ng-bootstrap": "^8.1.0", "popper.js": "^1.16.1", "positioning": "^2.0.1", "rxjs": "~7.4.0", "three": "^0.161.0", "tslib": "^2.3.0", "uuid": "^9.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.1.2", "@angular/cli": "~13.1.2", "@angular/compiler-cli": "~13.1.0", "@playwright/test": "^1.40.0", "@types/google.maps": "^3.39.12", "@types/googlemaps": "^3.39.13", "@types/jasmine": "~3.10.0", "@types/node": "^12.20.55", "eslint": "^8.12.0", "hammerjs": "^2.0.8", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "prettier": "^2.6.2", "tslint": "^6.1.3", "typescript": "~4.5.2"}}