<form [formGroup]="formChart">
  <div class="row g-3 mt-1">
    <!-- Instrumento -->
    <div class="col-md-3">
      <label class="form-label">Instrumento:</label>
      <ng-multiselect-dropdown
        [settings]="instrumentsSettings"
        [data]="instruments"
        formControlName="instrument"
        (onSelect)="changeInstrument($event, 'select')"
        (onDeSelect)="changeInstrument($event, 'deselect')"
        [placeholder]="'Selecione...'"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Data e hora inicial -->
    <div class="col-md-3">
      <label class="form-label">Data e hora inicial:</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="start_date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('start_date').valid &&
          formChart.get('start_date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Data e hora final -->
    <div class="col-md-3">
      <label class="form-label">Data e hora final:</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="end_date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('end_date').valid && formChart.get('end_date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>
  <!-- Configuracoes do grafico -->
  <div
    class="row mt-3"
    *ngIf="formChart.get('start_date').valid && formChart.get('end_date').valid"
  >
    <app-button
      [class]="'btn-logisoil-green text-nowrap'"
      [customBtn]="true"
      [icon]="'fa fa-cog'"
      [label]="'Configurações do gráfico'"
      (click)="ctrlConfiguration = !ctrlConfiguration"
    ></app-button>
  </div>
  <div
    class="row mt-3"
    *ngIf="
      ctrlConfiguration &&
      formChart.get('start_date').valid &&
      formChart.get('end_date').valid
    "
  >
    <div class="col-md-12">
      <div class="card">
        <div
          class="card-header form-control-bg"
          (click)="ctrlConfiguration = !ctrlConfiguration"
          style="cursor: pointer"
        >
          <i class="fa fa-cog me-2"></i>
          Configurações do gráfico
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <label class="form-label">Tipo de gráfico:</label>
              <select class="form-select" formControlName="graphic_type">
                <option value="bar">Barras</option>
                <option value="line">Linhas</option>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formChart.get('graphic_type').valid &&
                  formChart.get('graphic_type').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
            <!-- Período -->
            <div class="col-md-3">
              <label class="form-label">Selecione o período:</label>
              <select class="form-select" formControlName="interval">
                <option value="">Selecione...</option>
                <option *ngFor="let item of periods" [ngValue]="item.value">
                  {{ item.label }}
                </option>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formChart.get('interval').valid &&
                  formChart.get('interval').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
            <!-- Interpretação -->
            <div class="col-md-3">
              <label class="form-label">Interpretação</label>
              <select class="form-select" formControlName="aggregation">
                <option value="">Selecione...</option>
                <option *ngFor="let item of aggregation" [ngValue]="item.value">
                  {{ item.label }}
                </option>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formChart.get('aggregation').valid &&
                  formChart.get('aggregation').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
            <!-- Cor -->
            <div
              class="col-md-2"
              (clickOutside)="onClickedOutside('colorPicker', 0)"
            >
              <label class="form-label">Cor (da barra ou da linha):</label>
              <button
                class="color form-control"
                (click)="showColorPicker[0] = !showColorPicker[0]"
                [style.background]="selectedColor[0]"
              ></button>
              <div *ngIf="showColorPicker[0]" style="position: relative">
                <color-sketch
                  [color]="selectedColor[0]"
                  (onChangeComplete)="changeComplete($event, 0)"
                  class="color-picker-container"
                ></color-sketch>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Grafico -->
  <div
    class="row mt-3"
    *ngIf="
      formChart.get('interval').valid && formChart.get('aggregation').valid
    "
  >
    <div class="col-md-4">
      <app-button
        [class]="'btn-logisoil-green me-2 text-nowrap'"
        [customBtn]="true"
        [icon]="'fa fa-line-chart'"
        [label]="'Gerar gráfico'"
        (click)="getChart()"
      ></app-button>
    </div>
  </div>

  <!-- Alerta -->
  <div class="row mt-3">
    <div class="col-md-12">
      <div
        class="alert alert-warning"
        role="alert"
        *ngIf="messageReturn.status"
      >
        {{ messageReturn.text }}
      </div>
    </div>
  </div>

  <div class="row mt-2 mb-3">
    <div class="col-md-2" *ngIf="chart.options">
      <label class="form-label"
        >Tamanho: {{ formChart.controls['chart_height'].value }} px
      </label>
      <input
        type="range"
        class="range"
        #chartHeight
        min="300"
        max="1600"
        step="10"
        formControlName="chart_height"
        (input)="setHeight(chartHeight.value)"
      />
    </div>
  </div>
  <div class="row mt-2 mb-3">
    <div class="col-md-12" *ngIf="chart.options">
      <app-e-charts
        [dataChart]="chart"
        [height]="
          formChart.controls['chart_height'].value + chartLegendsTop - 5
        "
      ></app-e-charts>
    </div>
  </div>

  <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [click]="goBack.bind(this)"
    ></app-button>
  </div>
</form>
