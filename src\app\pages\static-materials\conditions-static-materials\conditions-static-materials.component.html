<form class="list-content" [formGroup]="formConditionStaticMaterials">
  <div class="row">
    <div class="col-md-12">
      <label class="form-label">{{ title }}</label>
    </div>

    <!--  Peso específico natural -->
    <div class="col-md-3">
      <label class="form-label">Peso específico natural</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="natural_specific_weight"
          autocomplete="off"
          min="0"
          step="0.01"
          (keypress)="
            func.controlNumber(
              $event,
              formConditionStaticMaterials.get('natural_specific_weight'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber(
              $event,
              formConditionStaticMaterials.get('natural_specific_weight')
            )
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <span class="input-group-text">kN/m³</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formConditionStaticMaterials.get('natural_specific_weight').valid &&
          formConditionStaticMaterials.get('natural_specific_weight').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!--  Peso específico saturado -->
    <div class="col-md-3">
      <input
        class="form-check-input me-1"
        formControlName="is_saturated_specific_weight"
        type="checkbox"
        value=""
        checked
        (change)="
          formService.checkboxControlValidate(
            formConditionStaticMaterials,
            'saturated_specific_weight'
          )
        "
      />
      <label class="form-label">Peso específico saturado</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="saturated_specific_weight"
          autocomplete="off"
          min="0"
          step="0.01"
          (keypress)="
            func.controlNumber(
              $event,
              formConditionStaticMaterials.get('saturated_specific_weight'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber(
              $event,
              formConditionStaticMaterials.get('saturated_specific_weight')
            )
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <span class="input-group-text">kN/m³</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formConditionStaticMaterials.get('saturated_specific_weight')
            .valid &&
          formConditionStaticMaterials.get('saturated_specific_weight')
            .touched &&
          formConditionStaticMaterials.get('is_saturated_specific_weight').value
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Color -->
    <div class="col-md-2" (clickOutside)="onClickedOutside('colorPicker')">
      <label class="form-label">Cor</label>
      <br />
      <button
        class="material-color"
        (click)="showColorPicker = !showColorPicker"
        [style.background]="selectedColor"
        [disabled]="view"
      ></button>
      <div
        class="d-flex justify-content-center justify-content-md-start color-picker-container"
        *ngIf="showColorPicker"
      >
        <div style="width: 220px; display: inline-block">
          <color-sketch
            [color]="selectedColor"
            (onChangeComplete)="changeComplete($event)"
          ></color-sketch>
        </div>
      </div>
    </div>
  </div>

  <!-- Modelo constitutivo -->
  <div class="row">
    <div class="col-md-12">
      <div class="section-container">
        <div class="section-header">
          <label>Modelo Constitutivo</label>
        </div>
        <div class="row p-3">
          <div class="col-md-4">
            <ng-multiselect-dropdown
              [placeholder]="'Selecione...'"
              [settings]="dropdownSettings"
              [data]="constitutiveModel"
              formControlName="constitutive_model"
              (onSelect)="itemEvent($event, 'select', 'constitutiveModel')"
              (onDeSelect)="itemEvent($event, 'deselect', 'constitutiveModel')"
              [disabled]="view"
            ></ng-multiselect-dropdown>
            <small
              class="form-text text-danger"
              *ngIf="
                !formConditionStaticMaterials.get('constitutive_model').valid &&
                formConditionStaticMaterials.get('constitutive_model').touched
              "
              ><i class="fa fa-exclamation-circle me-2" aria-hidden="true"></i
              >Selecione um dos modelos.</small
            >
          </div>

          <!-- Modelos constitutivos -->
          <div class="col-md-12">
            <!-- Modelo constitutivo - Mohr-Coulomb -->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 1">
              <app-mohr-coloumb
                #mohrColoumbRef
                [data]="dataElement"
                [view]="view"
              ></app-mohr-coloumb>
            </div>

            <!-- Undrained -->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 2">
              <app-undrained
                #undrainedRef
                [data]="dataElement"
                [view]="view"
              ></app-undrained>
            </div>

            <!-- No strenght  - não tem nenhum campo para preencher-->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 3">
              <div class="col-md-12">
                <div class="row">
                  <label class="form-label" style="font-style: italic"
                    >Fórmula:</label
                  >
                  <div class="col-md-3">
                    <img
                      src="assets/images/static-materials/no-strength.png"
                      class="img-fluid img-thumbnail"
                      style="max-height: 80px; width: auto"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!--  Infinite Strength -->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 4">
              <app-infinite-strength
                #infiniteStrengthRef
                [data]="dataElement"
                [view]="view"
              ></app-infinite-strength>
            </div>

            <!--  Shear Normal Function -->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 5">
              <app-shear-normal-function
                #shearNormalFunctionRef
                [data]="dataElement"
                [view]="view"
              ></app-shear-normal-function>
            </div>

            <!--  Hoek Brown -->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 6">
              <app-hoek-brown
                #hoekBrownRef
                [data]="dataElement"
                [view]="view"
              ></app-hoek-brown>
            </div>

            <!--  Generalized Hoek Brown -->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 7">
              <app-generalized-hoek-brown
                #generalizedHoekBrownRef
                [data]="dataElement"
                [view]="view"
              ></app-generalized-hoek-brown>
            </div>

            <!--  Vertical Stress Ratio -->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 8">
              <app-vertical-stress-ratio
                #verticalStressRatioRef
                [data]="dataElement"
                [view]="view"
              ></app-vertical-stress-ratio>
            </div>

            <!--  Shansep -->
            <div class="mt-1 row" *ngIf="selectedConstitutiveModel == 9">
              <app-shansep
                #shansepRef
                [data]="dataElement"
                [view]="view"
              ></app-shansep>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Parametros freaticos -->
  <div class="row">
    <div class="col-md-12">
      <div class="section-container">
        <div class="section-header">
          <label>Parâmetros Freáticos</label>
        </div>

        <div class="row mt-2">
          <!-- Superfície de água  -->
          <div class="col-md-4 ms-3 mb-3">
            <label class="form-label">Superfície de água:</label>
            <ng-multiselect-dropdown
              [placeholder]="'Selecione...'"
              [settings]="dropdownSettings"
              [data]="waterSurface"
              formControlName="water_surface"
              (onSelect)="itemEvent($event, 'select')"
              (onDeSelect)="itemEvent($event, 'deselect')"
              [disabled]="view"
            ></ng-multiselect-dropdown>
            <small
              class="form-text text-danger"
              *ngIf="
                !formConditionStaticMaterials.get('water_surface').valid &&
                formConditionStaticMaterials.get('water_surface').touched
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Hu -->
          <div class="col-md-2">
            <label class="form-label">Hu:</label
            ><ng-multiselect-dropdown
              [placeholder]="'Selecione...'"
              [settings]="dropdownSettings"
              [data]="hu"
              formControlName="hu"
              (onSelect)="
                itemEvent($event, 'select', 'hu');
                formService.dependencyControlValidate(
                  formConditionStaticMaterials,
                  'hu',
                  'custom_hu_value',
                  selectedHu == 1
                )
              "
              (onDeSelect)="
                itemEvent($event, 'deselect', 'hu');
                formService.dependencyControlValidate(
                  formConditionStaticMaterials,
                  'hu',
                  'custom_hu_value',
                  selectedHu == 1
                )
              "
              [disabled]="view"
            ></ng-multiselect-dropdown>
            <small
              class="form-text text-danger"
              *ngIf="
                !formConditionStaticMaterials.get('hu').valid &&
                formConditionStaticMaterials.get('hu').touched
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Custom hu -->
          <div class="col-md-2" *ngIf="selectedHu == 1">
            <label class="form-label">Custom:</label>
            <input
              type="text"
              class="form-control"
              formControlName="custom_hu_value"
              min="0"
              max="1"
              step="0.01"
              placeholder="Digite um valor entre 0 e 1"
              (keypress)="
                func.controlNumber(
                  $event,
                  formConditionStaticMaterials.get('custom_hu_value'),
                  'positiveDecimalDot'
                )
              "
              (keyup)="
                func.controlNumber(
                  $event,
                  formConditionStaticMaterials.get('custom_hu_value')
                )
              "
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formConditionStaticMaterials.get('custom_hu_value').valid &&
                formConditionStaticMaterials.get('custom_hu_value').touched
              "
              >Campo Obrigatório.</small
            >
          </div>
        </div>

        <!-- Usar parâmetros de resistência da condição drenada acima da
              superfície de água -->
        <div class="row mt-2" *ngIf="title !== 'Condição Drenada'">
          <div class="col-md-12">
            <input
              class="form-check-input me-1 ms-3"
              type="checkbox"
              formControlName="use_drained_resistance_over_water_surface"
            />
            <label class="form-label">
              Usar parâmetros de resistência da condição drenada acima da
              superfície de água
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
