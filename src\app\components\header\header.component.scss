* {
  padding: 0px;
  margin: 0px;
}

li {
  list-style: none;
}

a {
  text-decoration: none;
}

img {
  max-width: 100%;
}

/*HEADER*/

.header-logisoil {
  box-sizing: border-box;
  display: grid;
  height: 71px;
  grid-template-columns: auto 1fr auto;
  align-items: center;
}

.header-logisoil nav {
  ul.header-menu {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-top: 15px;
    justify-content: flex-end;
    padding-right: 30px;
  }
}

.header-logisoil nav a.logo-client {
  color: #ffffff;
  font-size: 1.125em;
  border-radius: 5px;
  transition: 0.1s;
}

.header-logisoil nav ul.header-menu > li + li {
  margin-left: 12px;
}

.logo-logisoil {
  width: 200px;
  margin: 0 auto;
}

.logo-client {
  display: flex;
  align-items: center;
  justify-content: center;
  border: rgba(0, 0, 0, 0.29) 1px solid;
  background-color: #ffffff;
  width: 71px;
  height: 30px;
}

.header-text {
  font-size: 1.625em;
  color: #adafb1;
  height: 26px;
  padding-left: 30px;
}

@media (max-width: 1100px) {
  .logo-logisoil {
    display: none;
  }
  .header-logisoil {
    grid-template-columns: auto auto;
  }
}

@media (max-width: 900px) {
  .header-text {
    display: none;
  }
  .header-logisoil {
    grid-template-columns: auto;
  }
}

.btn {
  &.btn-structure {
    background-color: #ffffff;
    color: #34b575;
    border: rgba(0, 0, 0, 0.29) 1px solid;
    height: 30px;
    width: 95px;
    padding: 0;
    img {
      filter: invert(53%) sepia(92%) saturate(368%) hue-rotate(99deg)
        brightness(94%) contrast(50%);
    }

    &:hover {
      background-color: #34b575;
      border-color: #34b575;
      i {
        color: #ffffff;
      }
      img {
        filter: invert(100%) sepia(0%) saturate(100%) hue-rotate(0deg)
          brightness(100%) contrast(100%);
      }
    }
  }
}

.menu-minidashboard,
.menu-profile {
  font-size: 14px;
  li {
    a {
      img {
        filter: invert(100%) sepia(0%) saturate(100%) hue-rotate(0deg)
          brightness(100%) contrast(100%);
        margin-right: 8px;
      }
      i {
        color: #ffffff;
        margin-right: 16px;
      }
    }
  }
}

.dropdown-minidashboard,
.dropdown-structure,
.dropdown-profile {
  position: relative;
}

.dropdown-minidashboard > ul,
.dropdown-structure > ul,
.dropdown-profile > ul {
  background-color: #ffffff;
  position: absolute;
  border-radius: 5px;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.29);
  top: 40px;
  left: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
  pointer-events: none;
  transform: rotateX(-90deg);
  transform-origin: top center;
  transition: all 0.25s ease;
  transition-timing-function: ease-out;
  box-sizing: border-box;
  z-index: 9999999;
}

.dropdown-minidashboard > ul,
.dropdown-profile > ul {
  min-width: 180px;
}

.dropdown-structure > ul {
  left: -12px;
  min-width: 180px;
  width: 232px;
  padding: 18px;
}

.dropdown-minidashboard > ul.active,
.dropdown-structure > ul.active,
.dropdown-profile > ul.active {
  opacity: 1;
  pointer-events: all;
  transform: rotateX(0deg);
}

.dropdown-minidashboard > ul > li,
.dropdown-profile > ul > li {
  height: 40px;
  display: flex;
  align-items: center;
  transition: 0.3s ease;
}

.dropdown-minidashboard > ul > li:first-child,
.dropdown-profile > ul > li:first-child {
  border-radius: 5px 5px 0 0;
}

.dropdown-minidashboard > ul > li:last-child,
.dropdown-profile > ul > li:last-child {
  border-radius: 0 0 5px 5px;
}

.dropdown-minidashboard > ul > li:hover,
.dropdown-profile > ul > li:hover {
  border-left: 7px solid #34b575;
  background-color: rgba(52, 181, 117, 0.15);
  a {
    margin-left: -2px;
  }
}

.dropdown-minidashboard > ul > li > a,
.dropdown-profile > ul > li > a {
  display: block;
  line-height: 32px;
  text-decoration: none;
  color: #707070;
  margin-left: 5px;
}

.dropdown-minidashboard > ul > li > a > i,
.dropdown-profile > ul > li > a > i {
  margin: 0 5px 0 10px;
  font-size: 0.875em;
}

.dropdown-minidashboard > ul > li > a > img,
.dropdown-profile > ul > li > a > img {
  margin: 0 5px 0 10px;
  height: 16px;
}

.dropdown-minidashboard > ul > li > a > span,
.dropdown-profile > ul > li > a > span {
  font-size: 0.875em;
}

.dropdown-structure > ul > li > .form-item {
  color: rgba(0, 0, 0, 0.7);
  font-family: averta;
  font-size: 0.825em;
  width: 184px;
  margin: 6px;
  height: 30px;
}

.dropdown-profile > ul {
  left: -112px;
}

.dropdown-minidashboard {
  border: rgba(0, 0, 0, 0.29) 1px solid;
  border-radius: 5px;
}
