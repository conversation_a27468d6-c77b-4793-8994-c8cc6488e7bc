import { Component, OnInit, ViewEncapsulation, ViewChildren, QueryList, ElementRef, Input, OnChanges, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { IDropdownSettings } from 'ng-multiselect-dropdown';

import { FormService } from 'src/app/services/form.service';

import { stressHistoryType, stressHistoryMethod } from 'src/app/constants/static-materials.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-shansep',
  templateUrl: './shansep.component.html',
  styleUrls: ['./shansep.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ShansepComponent implements OnInit, OnChanges {
  @ViewChildren('materialPointsRef') materialPointsRef: QueryList<ElementRef>;

  @Input() public data: any = null;
  @Input() public view: boolean = false;

  public formShansep: FormGroup = new FormGroup({
    a: new FormControl('', [Validators.required]),
    s: new FormControl('', [Validators.required]),
    m: new FormControl('', [Validators.required]),
    stress_history_type: new FormControl([], [Validators.required]), //Tipo de historico de tensoes:
    stress_history_method: new FormControl('', [Validators.required]), // Metodo de historico de tensoes:
    constant: new FormControl('', [Validators.required]),
    maximum_shear_strength: new FormControl({ value: null, disabled: true }),
    is_maximum_shear_strength: new FormControl(false),
    tensile_strength: new FormControl({ value: null, disabled: true }),
    is_tensile_strength: new FormControl(false)
  });

  public func = fn;

  public stressHistoryType = stressHistoryType;
  public stressHistoryMethod = stressHistoryMethod;

  public stressHistoryMethodData: any = [];
  public label_x: string = '';
  public label_y: string = '';

  public dataPoints = null;

  public dropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'name',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 10,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  constructor(public formService: FormService) {}

  ngOnInit(): void {
    // this.stressHistoryMethod = fn.enumToArrayComplete(this.stressHistoryMethod)[0];
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  itemEvent(item: any, action: string = 'select', subType: string = '') {
    switch (subType) {
      case 'stressHistoryType':
        this.formShansep.controls['stress_history_method'].setValue([]);
        this.stressHistoryMethodData = this.stressHistoryMethod[0][parseInt(item.id)];
        break;
      case 'stressHistoryMethod':
        if (this.formShansep.controls['stress_history_type'].value[0].id == 1) {
          if (this.formShansep.controls['stress_history_method'].value[0].id == 2) {
            this.label_x = 'Profundidade';
            this.label_y = 'RSA';
          } else if (this.formShansep.controls['stress_history_method'].value[0].id == 3) {
            this.label_x = 'Elevação';
            this.label_y = 'RSA';
          }
        } else if (this.formShansep.controls['stress_history_type'].value[0].id == 2) {
          if (this.formShansep.controls['stress_history_method'].value[0].id == 2) {
            this.label_x = 'Profundidade';
            this.label_y = 'RSA';
          } else if (this.formShansep.controls['stress_history_method'].value[0].id == 3) {
            this.label_x = 'Elevação';
            this.label_y = 'PC';
          }
        }
        break;
    }
  }

  getPoints() {
    let pointsValue = [];
    this.materialPointsRef.toArray()[0]['formPoints'].value.points.forEach((point, index) => {
      pointsValue.push({
        id: point.id,
        value_1: point.point_x,
        value_2: point.point_y,
        index: index + 1
      });
    });
    return pointsValue;
  }

  splitData($dados) {
    this.formShansep.controls['a'].setValue($dados.a);
    this.formShansep.controls['s'].setValue($dados.s);
    this.formShansep.controls['m'].setValue($dados.m);

    if ($dados.maximum_shear_strength != null) {
      this.formShansep.controls['is_maximum_shear_strength'].setValue(true);
      this.formShansep.controls['maximum_shear_strength'].setValue($dados.maximum_shear_strength);
      this.formService.checkboxControlValidate(this.formShansep, 'maximum_shear_strength');
    }

    if ($dados.tensile_strength != null) {
      this.formShansep.controls['is_tensile_strength'].setValue(true);
      this.formShansep.controls['tensile_strength'].setValue($dados.tensile_strength);
      this.formService.checkboxControlValidate(this.formShansep, 'tensile_strength');
    }

    this.formShansep.controls['stress_history_type'].setValue([
      fn.findIndexInArrayofObject(this.stressHistoryType, 'id', $dados.stress_history_type.toString(), 'name', true)
    ]);

    this.stressHistoryMethodData = this.stressHistoryMethod[0][$dados.stress_history_type];

    this.formShansep.controls['stress_history_method'].setValue([
      fn.findIndexInArrayofObject(this.stressHistoryMethod[0][$dados.stress_history_type], 'id', $dados.stress_history_method.toString(), 'name', true)
    ]);

    this.formShansep.controls['constant'].setValue($dados.constant);

    this.itemEvent(null, null, 'stressHistoryMethod');

    this.dataPoints = $dados.point_values;

    if (this.view) {
      this.formShansep.disable();
    }
  }

  validate() {
    let formPointValid = true;

    let formFields: any = [
      'a',
      's',
      'm',
      'stress_history_type',
      'maximum_shear_strength',
      'is_maximum_shear_strength',
      'tensile_strength',
      'is_tensile_strength'
    ];
    if (this.formShansep.controls['stress_history_type'].value && this.formShansep.controls['stress_history_type'].value[0]) {
      formFields.push('stress_history_method');
      if (this.formShansep.controls['stress_history_method'].value && this.formShansep.controls['stress_history_method'].value[0]) {
        if (this.formShansep.controls['stress_history_method'].value[0].id == '1') {
          formFields.push('constant');
        } else {
          formPointValid = this.materialPointsRef.toArray()[0]['validate']();
        }
      }
    }

    let formValid = this.formService.validateFormList(this.formShansep, formFields);

    if (!formValid || !formPointValid) {
      this.formShansep.markAllAsTouched();
    }

    return formValid && formPointValid;
  }
}
