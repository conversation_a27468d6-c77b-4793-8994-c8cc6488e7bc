<form [formGroup]="formFilter" class="row g-3 mt-1 text-left list-content">
  <div class="col-md-12">
    <label class="form-label">Período:</label>
    <select
      class="form-select"
      formControlName="period"
      (change)="changePeriod(formFilter.controls['period'].value)"
    >
      <option value="">Selecione...</option>
      <ng-container *ngFor="let period of periods">
        <option [ngValue]="period.value" *ngIf="period.show">
          {{ period.label }}
        </option>
      </ng-container>
    </select>
  </div>
  <div class="col-md-12">
    <label class="form-label">Número de dias:</label>
    <input
      type="number"
      class="form-control"
      min="1"
      step="1"
      formControlName="variation_period_days"
    />
  </div>
  <div class="col-md-12 mb-2">
    <label class="form-label">Ordem:</label>
    <select class="form-select" formControlName="order_by">
      <option value="">Selecione...</option>
      <ng-container *ngFor="let ordenation of ordenations">
        <option [ngValue]="ordenation.value">
          {{ ordenation.label }}
        </option>
      </ng-container>
    </select>
  </div>
  <div class="col-md-12 mb-2">
    <app-button
      [class]="'btn-logisoil-blue'"
      [label]="'OK'"
      (click)="applyFilter()"
    ></app-button>
    <app-button
      [class]="'btn-logisoil-gray ms-2'"
      [label]="'Limpar'"
      (click)="resetFilter()"
    ></app-button>
  </div>
</form>
