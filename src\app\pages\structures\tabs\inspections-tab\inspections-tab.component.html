<div class="row">
  <div class="col-md-8">
    <label class="form-label">Aspectos a observar nas inspeções</label>
    <div class="accordion accordion-flush" id="accordionAspects">
      <div class="accordion-item">
        <div class="accordion-header" id="flush-headingOne">
          <button
            class="accordion-button"
            type="button"
            data-bs-target="#flush-collapseOne"
            [ngClass]="aspectsCollapse ? '' : 'collapsed'"
            (click)="aspectsCollapse = !aspectsCollapse"
          >
            Selecione...
          </button>
        </div>
        <div
          id="flush-collapseOne"
          class="accordion-collapse collapse"
          data-bs-parent="#accordionAspects"
          [ngClass]="aspectsCollapse ? 'show' : ''"
        >
          <div class="accordion-body mt-1">
            <app-sortable-list
              [dataList]="aspectsList"
              [checkbox]="true"
              [edit]="'true'"
              [orderField]="'index'"
              [textField]="'description'"
              [selected]="aspectsNumberSelected"
              [filter]="true"
              [dragDrop]="dragDropAspects"
              (sendNumberSelected)="
                getNumberSelected($event, formAspectStructure.get('aspects'))
              "
              (sendEditItem)="getAspects($event.id, 'edit')"
            ></app-sortable-list>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-2 mt-4 d-flex align-items-start">
    <!-- Adicionar Aspecto -->
    <app-button
      [class]="'btn-logisoil-add'"
      [icon]="'fa fa-thin fa-plus'"
      class="me-1"
      (click)="resetForm('aspect', true)"
      [disabled]="view"
      *ngIf="!view"
    >
    </app-button>
  </div>
</div>

<!-- Alerta Aspect-->
<div class="alert alert-success mt-2" role="alert" *ngIf="messageAspect.status">
  {{ messageAspect.text }}
</div>

<!-- Aspecto -->
<form [formGroup]="formAspects" (ngSubmit)="validate('aspect')">
  <ng-container *ngIf="ctrlAspects && !view">
    <div class="row mt-4">
      <ul class="nav nav-tabs px-2">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page"
            >Novo aspecto a observar</a
          >
        </li>
      </ul>
      <div class="row mt-4">
        <!-- Mensagem de erro -->
        <app-alert
          [class]="'alert-danger'"
          [messages]="messagesErrorAspect"
        ></app-alert>
      </div>
      <div class="row mt-2">
        <!-- Checkbox  -->
        <div class="col-md-6">
          <input
            class="form-check-input me-2"
            type="checkbox"
            formControlName="allow_option_not_applicable"
            [checked]="formAspects.get('allow_option_not_applicable').value"
          />
          <label class="form-label">Permitir opção “Não se aplica”</label>
        </div>
      </div>
      <!-- Resposta que gera ocorrência -->
      <div class="row mt-2">
        <div class="col-md-6 d-flex align-items-start">
          <label class="form-label me-3 mb-0"
            >Resposta que gera ocorrência</label
          >
          <div class="form-check form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              formControlName="response_for_occurrence"
              [value]="1"
            />
            <label class="form-check-label">Sim</label>
          </div>
          <div class="form-check form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              formControlName="response_for_occurrence"
              [value]="2"
            />
            <label class="form-check-label">Não</label>
          </div>
        </div>
      </div>
      <!-- Descrição -->
      <div class="row mt-2">
        <div class="col-md-12 mt-2">
          <label class="form-label">Descrição</label>
          <input
            type="text"
            class="form-control"
            formControlName="description"
            autocomplete="off"
            maxlength="255"
            data-ls-module="charCounter"
            (input)="onValueChange($event)"
          />
          <small class="form-text d-block"
            >Caracteres {{ counter }} (Máximo: 255)
          </small>
          <small
            class="form-text text-danger d-block"
            *ngIf="
              !formAspects.get('description').valid &&
              formAspects.get('description').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
      </div>
    </div>
    <div class="row mt-2">
      <!-- Area -->
      <div class="col-md-6">
        <label class="form-label">Área</label>
        <select
          class="form-select"
          formControlName="area"
          (change)="resetForm('area')"
        >
          <option value="">Selecione...</option>
          <option *ngFor="let item of areasList" [ngValue]="item.id">
            {{ item.name }}
          </option>
        </select>
        <small
          class="form-text text-danger"
          *ngIf="
            !formAspects.get('area').valid && formAspects.get('area').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Botão Adicionar  -->
      <div class="col-md-2 mt-4">
        <app-button
          [class]="'btn-logisoil-add'"
          [icon]="'fa fa-thin fa-plus'"
          class="me-1"
          (click)="resetForm('area', true)"
          *ngIf="formAspects.get('area').value == ''"
          [disabled]="view"
        >
        </app-button>
        <!-- Botão Editar -->
        <app-button
          [class]="'btn-logisoil-editItem'"
          [icon]="'fa fa-thin fa-pencil'"
          class="me-1 mt-2"
          (click)="ctrlArea = true; getArea(formAspects.get('area').value)"
          *ngIf="formAspects.get('area').value != ''"
          [disabled]="view"
        >
        </app-button>
      </div>
    </div>
    <div class="col-md-4 mt-4 d-flex align-items-start">
      <app-button
        [class]="'btn-logisoil-red'"
        [icon]="'fa fa-thin fa-xmark'"
        [label]="'Cancelar'"
        [type]="false"
        class="me-1"
        (click)="ctrlAspects = false; resetForm('aspect')"
      >
      </app-button>

      <app-button
        [class]="'btn-logisoil-green'"
        [icon]="'fa fa-thin fa-floppy-disk'"
        [label]="'Salvar'"
        [type]="false"
        class="me-1"
        [disabled]="!formAspects.valid"
      >
      </app-button>
    </div>
  </ng-container>
</form>

<!-- Alerta Área-->
<div class="alert alert-success mt-2" role="alert" *ngIf="messageArea.status">
  {{ messageArea.text }}
</div>

<!--Área -->
<form [formGroup]="formArea" (ngSubmit)="validate('area')">
  <div class="row mt-4" *ngIf="ctrlArea">
    <ul class="nav nav-tabs px-2">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page">Área</a>
      </li>
    </ul>
    <div class="row mt-4">
      <!-- Mensagem de erro -->
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesErrorArea"
      ></app-alert>
    </div>
    <div class="row mt-2">
      <div class="col-md-6">
        <label class="form-label">Nome</label
        ><input
          type="text"
          class="form-control"
          formControlName="name"
          autocomplete="off"
          maxlength="255"
          data-ls-module="charCounter"
          (input)="onValueChange($event)"
        />
        <small class="form-text d-block"
          >Caracteres {{ counter }} (Máximo: 255)
        </small>
        <small
          class="form-text text-danger d-block"
          *ngIf="!formArea.get('name').valid && formArea.get('name').touched"
          >Campo Obrigatório.</small
        >
      </div>
      <div class="buttons col-sm-4">
        <app-button
          [class]="'btn-logisoil-red'"
          [icon]="'fa fa-thin fa-xmark'"
          [label]="'Cancelar'"
          [type]="false"
          class="me-1"
          (click)="ctrlArea = false; resetForm('area')"
        >
        </app-button>
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-floppy-disk'"
          [label]="'Salvar'"
          [type]="false"
          [disabled]="!formArea.valid"
        >
        </app-button>
      </div>
    </div>
  </div>
</form>
