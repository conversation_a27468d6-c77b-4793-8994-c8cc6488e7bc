import { <PERSON><PERSON><PERSON>w<PERSON>nit, ChangeDetector<PERSON><PERSON>, Component, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';

import { ActivatedRoute, Router } from '@angular/router';

import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';

import { IdleService } from 'src/app/services/idle.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { UserService } from 'src/app/services/user.service';

import { InspectionSheetType, Anomaly } from 'src/app/constants/inspections.constants';

//Componentes filho
import { GeneralDataComponent } from './tabs/general-data/general-data.component';
import { PreviousSituationComponent } from './tabs/previous-situation/previous-situation.component';
import { AspectsObservedComponent } from './tabs/aspects-observed/aspects-observed.component';
import { ConservationStatusComponent } from './tabs/conservation-status/conservation-status.component';
import { GeneralObservationsComponent } from './tabs/general-observations/general-observations.component';
import { ActionsExecutedComponent } from './tabs/actions-executed/actions-executed.component';
import { CurrentSituationComponent } from './tabs/current-situation/current-situation.component';

import { AspectsItemComponent } from './tabs/aspects-observed/aspects-item/aspects-item.component';
import { AbstractControl, FormArray } from '@angular/forms';
import { catchError, finalize, forkJoin, interval, map, of, startWith, Subscription, switchMap } from 'rxjs';

import * as moment from 'moment';

@Component({
  selector: 'app-register-inspection-sheet',
  templateUrl: './register-inspection-sheet.component.html',
  styleUrls: ['./register-inspection-sheet.component.scss']
})
export class RegisterInspectionSheetComponent implements OnInit, AfterViewInit {
  @ViewChild(GeneralDataComponent) generalTab: GeneralDataComponent;
  @ViewChild(PreviousSituationComponent) previousSituationTab: PreviousSituationComponent;
  @ViewChild(AspectsObservedComponent) aspectsObservedTab: AspectsObservedComponent;
  @ViewChild(ConservationStatusComponent) conservationStatusTab: ConservationStatusComponent;
  @ViewChild(GeneralObservationsComponent) generalObservationsTab: GeneralObservationsComponent;
  @ViewChild(ActionsExecutedComponent) actionsExecutedTab: ActionsExecutedComponent;
  @ViewChild(CurrentSituationComponent) currentSituationTab: CurrentSituationComponent;

  @ViewChildren(AspectsItemComponent) aspectItemComponents!: QueryList<AspectsItemComponent>;

  @ViewChild('modalConfirm') ModalConfirm: any;

  @ViewChild('reloadTimer') reloadTimer: any; // Referência ao componente de timer

  public inspectionSheetType: number = null;

  public generalTabData: any = {};
  public previousSituationTabData: any = {};
  public aspectsObservedTabData: any = {};
  public generalObservationsTabData: any = {};
  public actionsExecutedTabData: any = {};
  public currentSituationTabData: any = {};

  public tabKeys: Array<keyof typeof this.tabsConfig>;

  public tabsConfig = {
    general: { name: 'Dados cadastrais', active: true, styleColor: false, disabled: false, show: true },
    previousSituation: { name: 'Situação pretérita', active: false, styleColor: false, disabled: false, show: true },
    aspectsObserved: { name: 'Aspectos observados', active: false, styleColor: false, disabled: false, show: true },
    conservationStatus: { name: 'Estado de conservação', active: false, styleColor: false, disabled: false, show: true },
    generalObservations: { name: 'Observações gerais', active: false, styleColor: false, disabled: false, show: true },
    actionsExecuted: { name: 'Ações executadas', active: false, styleColor: false, disabled: false, show: true },
    currentSituation: { name: 'Situação atual', active: false, styleColor: false, disabled: false, show: true }
  };

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public title: string = '';

  public dados: any = '';

  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  public allowTabNavigation: boolean = true;
  public status: any = null;
  public lockedMessage: string = '';

  private updateInterval$: Subscription;
  public spinner: boolean = true;

  public showTimer: boolean = false;
  public timer = 10000;

  constructor(
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private idleService: IdleService,
    private inspectionSheetServiceApi: InspectionSheetServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private userService: UserService,
    private toastr: ToastrService
  ) {}

  /**
   * Inicializa o componente ao ser carregado.
   */
  ngOnInit(): void {
    this.ngxSpinnerService.show();

    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    // Calcula as chaves de tabsConfig
    this.tabKeys = Object.keys(this.tabsConfig) as Array<keyof typeof this.tabsConfig>;

    this.idleService.setupIdleDetection(10000000, this.timer);

    // Escuta eventos de inatividade
    this.idleService.idleDetected$.subscribe(() => {
      this.showTimer = true; // Mostra o componente de timer
    });

    // Escuta eventos de atividade
    this.idleService.activityDetected$.subscribe(() => {
      this.showTimer = false; // Oculta o timer
    });
  }

  /**
   * Executado após a visualização do componente ser inicializada.
   */
  ngAfterViewInit(): void {
    this.status = parseInt(this.activatedRoute.snapshot.queryParams.status ?? 0);

    if (this.activatedRoute.snapshot.queryParams.inspectionSheetId) {
      const inspectionSheetId = this.activatedRoute.snapshot.queryParams.inspectionSheetId;
      const forceEmptyOccurrences = [2, 3].includes(this.status);

      if (inspectionSheetId) {
        this.startUpdateInterval(inspectionSheetId, forceEmptyOccurrences);
      }
    }
  }

  /**
   * Inicia um intervalo para atualização periódica da ficha de inspeção.
   * @param {string} inspectionSheetId - ID da ficha de inspeção.
   * @param {boolean} forceEmptyOccurrences - Indica se deve forçar ocorrências vazias.
   */
  private startUpdateInterval(inspectionSheetId: string, forceEmptyOccurrences: boolean): void {
    this.updateInterval$ = interval(3000000)
      .pipe(
        startWith(0), // Primeira execução imediata
        switchMap(() => this.getInspectionSheetById(inspectionSheetId, forceEmptyOccurrences))
      )
      .subscribe();
  }

  /**
   * Obtém os detalhes da ficha de inspeção pelo ID.
   * @param {string} inspectionSheetId - ID da ficha de inspeção.
   * @param {boolean} [forceEmptyOccurrences=false] - Indica se deve forçar ocorrências vazias.
   */
  getInspectionSheetById(inspectionSheetId: string, forceEmptyOccurrences: boolean = false) {
    if (this.spinner) {
      this.ngxSpinnerService.show();
    }

    const occurrencesLinkable$ = forceEmptyOccurrences
      ? of([]) // Força o retorno de um array vazio
      : this.inspectionSheetServiceApi.getOccurrencesLinkable(inspectionSheetId).pipe(
          map((resp) => resp ?? []), // Garante que sempre retorna um array vazio se resp for null ou undefined
          catchError((error) => {
            console.error('Erro no endpoint getOccurrencesLinkable:', error);
            return of([]); // Retorna array vazio em caso de erro
          })
        );

    return forkJoin({
      inspectionSheet: this.inspectionSheetServiceApi.getInspectionSheetById(inspectionSheetId).pipe(
        catchError((error) => {
          console.error('Erro no endpoint getInspectionSheetById:', error);
          return of(null); // Retorna valor padrão em caso de erro
        })
      ),
      occurrencesLinkable: occurrencesLinkable$
    }).pipe(
      map(({ inspectionSheet, occurrencesLinkable }) => {
        if (inspectionSheet) {
          this.setData(inspectionSheet, occurrencesLinkable);
        } else {
          console.warn('Não foi possível obter a ficha de inspeção.');
        }
      }),
      catchError((error) => {
        console.error('Erro geral no forkJoin:', error);
        return of(null); // Trata erros e continua o fluxo
      }),
      finalize(() => {
        this.ngxSpinnerService.hide();
      })
    );
  }

  /**
   * Configura os dados da ficha de inspeção.
   * @param {any} dados - Dados da ficha de inspeção.
   * @param {any} occurrencesLinkable - Ocorrências vinculáveis.
   */
  setData(dados: any, occurrencesLinkable: any) {
    if (dados.locked) {
      this.lockedMessage = `A ficha está sendo editada por: ${dados.locked_by.first_name} ${dados.locked_by.surname} (${moment(dados.locked_at).format(
        'DD/MM/YYYY HH:mm:ss'
      )})`;
    } else {
      this.spinner = false;
    }

    this.dados = dados;

    this.inspectionSheetType = dados.type;
    const inspectionSheetType = InspectionSheetType.find((inspectionSheet) => inspectionSheet.value === dados.type);
    const inspectionSheetName = inspectionSheetType ? inspectionSheetType.label : ''; // Retorna o valor ou null se não encontrado
    const inspectionSheetSearchIdentifier = this.activatedRoute.snapshot?.queryParams?.inspectionSheetSearchIdentifier ?? '';

    this.title = `Ficha de Inspeção - ${inspectionSheetName} - Nº: ${inspectionSheetSearchIdentifier}`;
    this.generalTab.setData(dados);

    if ((dados?.areas && dados.areas.length > 0) || this.status === 1) {
      this.aspectsObservedTab.setData(dados, occurrencesLinkable);
    } else {
      this.tabsConfig.aspectsObserved.show = false;
    }

    if (dados?.previous_situation) {
      this.previousSituationTab.setData(dados);
    } else {
      this.tabsConfig.previousSituation.show = false;
    }

    this.conservationStatusTab.setData(dados);

    if ((dados?.notes && dados.notes.length > 0) || this.status === 1) {
      this.generalObservationsTab.setData(dados);
    } else {
      this.tabsConfig.generalObservations.show = false;
    }

    if ([3].includes(this.inspectionSheetType)) {
      if ((dados?.identified_anomalies && dados.identified_anomalies.length > 0) || this.status === 1) {
        this.actionsExecutedTab.setData(dados);
        this.currentSituationTab.setData(dados);
      } else {
        this.tabsConfig.actionsExecuted.show = false;
        this.tabsConfig.currentSituation.show = false;
      }
    } else {
      this.tabsConfig.actionsExecuted.show = false;
      this.tabsConfig.currentSituation.show = false;
    }
  }

  /**
   * Seleciona uma aba na interface.
   * @param {keyof typeof this.tabsConfig} tab - Aba a ser ativada.
   */
  selectTab(tab: keyof typeof this.tabsConfig): void {
    Object.keys(this.tabsConfig).forEach((key) => {
      this.tabsConfig[key as keyof typeof this.tabsConfig].active = key === tab;
    });
  }

  /**
   * Navega para a próxima aba disponível.
   */
  onNext(): void {
    const keys = Object.keys(this.tabsConfig) as Array<keyof typeof this.tabsConfig>;
    const currentIndex = keys.findIndex((key) => this.tabsConfig[key].active);

    if (currentIndex < keys.length - 1 && this.allowTabNavigation) {
      this.selectTab(keys[currentIndex + 1]);
    }
  }

  /**
   * Navega para a aba anterior disponível.
   */
  onPrevious(): void {
    const keys = Object.keys(this.tabsConfig) as Array<keyof typeof this.tabsConfig>;
    const currentIndex = keys.findIndex((key) => this.tabsConfig[key].active);

    if (currentIndex > 0 && this.allowTabNavigation) {
      this.selectTab(keys[currentIndex - 1]);
    }
  }

  /**
   * Verifica se há uma próxima aba disponível.
   * @returns {boolean} - Retorna `true` se houver uma próxima aba, caso contrário, `false`.
   */
  hasNextTab(): boolean {
    const keys = Object.keys(this.tabsConfig).filter((key) => this.tabsConfig[key as keyof typeof this.tabsConfig].show) as Array<keyof typeof this.tabsConfig>;
    const currentIndex = keys.findIndex((key) => this.tabsConfig[key].active);

    return currentIndex < keys.length - 1;
  }

  /**
   * Verifica se há uma aba anterior disponível.
   * @returns {boolean} - Retorna `true` se houver uma aba anterior, caso contrário, `false`.
   */
  hasPreviousTab(): boolean {
    const keys = Object.keys(this.tabsConfig).filter((key) => this.tabsConfig[key as keyof typeof this.tabsConfig].show) as Array<keyof typeof this.tabsConfig>;
    const currentIndex = keys.findIndex((key) => this.tabsConfig[key].active);

    return currentIndex > 0;
  }

  /**
   * Salva a ficha de inspeção.
   * @param {any} [$event=null] - Evento opcional com detalhes do salvamento.
   */
  onSave($event = null) {
    const userCompletedTheInspection: boolean = $event?.userCompletedTheInspection ?? false;
    const payload = this.generatePayload(userCompletedTheInspection);

    if (userCompletedTheInspection) {
      // Configurar e abrir o modal para confirmação
      this.modalTitle = 'Finalizar preenchimento';
      this.modalMessage = 'Deseja finalizar a ficha de inspeção?';
      this.modalInstruction = 'Atenção: após a confirmação, os dados não poderão ser mais alterados.';
      this.modalConfig = {
        iconHeader: 'fa fa-check-circle',
        action: 'confirmInspection'
      };

      this.ModalConfirm.openModal(); // Abrir modal
    } else {
      // Chamar o endpoint diretamente sem confirmação
      this.submitInspection(payload, userCompletedTheInspection, $event);
    }
  }

  /**
   * Adiciona uma nova ocorrência.
   */
  onAddOccurrence(): void {
    this.onSave(false);
  }

  /**
   * Gera o payload para envio da ficha de inspeção.
   * @param {boolean} [validateForm=false] - Indica se deve validar o formulário antes de gerar o payload.
   * @returns {any} - Payload gerado para envio.
   */
  generatePayload(validateForm: boolean = false): any {
    // Validação do formulário do GeneralDataComponent
    if (validateForm) {
      const formValidGeneralTab = this.generalTab.validateForm();

      if (formValidGeneralTab) {
      } else {
        return null;
      }
    }

    const generalDataFormValues = this.generalTab.generalDataForm.value;
    const currentSituationFormValues = this.currentSituationTab.currentSituationForm.value;
    const conservationStatusData = this.conservationStatusTab.conservationStatusForm.value;
    const notesFormValues = this.tabsConfig.generalObservations.show ? this.generalObservationsTab.notesForm.value : null;
    const actionsExecutedFormValues =
      this.tabsConfig.actionsExecuted.show && this.tabsConfig.currentSituation.show ? this.actionsExecutedTab.actionsForm.value : null;

    const areas = this.tabsConfig.aspectsObserved.show
      ? (this.aspectsObservedTab.areaForm.get('areas') as FormArray).controls.map((areaControl: AbstractControl) => {
          const aspectsFormArray = areaControl.get('aspects') as FormArray;

          return {
            id: areaControl.get('id')?.value,
            name: areaControl.get('name')?.value,
            aspects: aspectsFormArray.controls.map((aspectControl: AbstractControl) => {
              const occurrencesFormArray = aspectControl.get('occurrences') as FormArray;

              return {
                id: aspectControl.get('id')?.value,
                description: aspectControl.get('description')?.value,
                allow_option_not_applicable: aspectControl.get('allow_option_not_applicable')?.value,
                response_for_occurrence: aspectControl.get('response_for_occurrence')?.value,
                occurrences: occurrencesFormArray.controls.map((occurrenceControl: AbstractControl) => {
                  const isExisting = occurrenceControl.get('id')?.value !== null;

                  // Nova ocorrência: remover "id" e "search_identifier"
                  if (!isExisting) {
                    return {
                      northing: occurrenceControl.get('northing')?.value,
                      easting: occurrenceControl.get('easting')?.value,
                      note: occurrenceControl.get('note')?.value,
                      linked_to: occurrenceControl.get('linked_to')?.value,
                      response: occurrenceControl.get('response')?.value,
                      occurrence_attachments: (occurrenceControl.get('occurrence_attachments') as FormArray)?.value || []
                    };
                  }

                  // Ocorrência existente: incluir "id" e "search_identifier"
                  return {
                    id: occurrenceControl.get('id')?.value,
                    search_identifier: occurrenceControl.get('search_identifier')?.value,
                    northing: occurrenceControl.get('northing')?.value,
                    easting: occurrenceControl.get('easting')?.value,
                    note: occurrenceControl.get('note')?.value,
                    linked_to: occurrenceControl.get('linked_to')?.value,
                    response: occurrenceControl.get('response')?.value,
                    occurrence_attachments: (occurrenceControl.get('occurrence_attachments') as FormArray)?.value || []
                  };
                })
              };
            })
          };
        })
      : [];

    // Identified anomalies - Integrar dados do CurrentSituationComponent
    const identifiedAnomalies =
      this.tabsConfig.actionsExecuted.show && this.tabsConfig.currentSituation.show
        ? actionsExecutedFormValues.anomalies.map((actionAnomaly: any) => {
            const situationAnomaly = currentSituationFormValues.situations.find((situation: any) => situation.Id === actionAnomaly.Id);

            return {
              id: actionAnomaly.Id, // ID da anomalia (comum entre os componentes)
              anomaly: Number(actionAnomaly.Anomaly) || 0, // Anomaly do ActionsExecutedComponent (conversão para número)
              executed_actions: actionAnomaly.ExecutedActions || '', // Ações executadas
              action_result_classification: Number(actionAnomaly.ActionResultClassification) || 0, // Classificação (número)
              anomaly_score: Number(situationAnomaly?.AnomalyScore) || 0, // Pontuação do CurrentSituationComponent (número)
              note: situationAnomaly?.Note || '' // Nota do CurrentSituationComponent
            };
          })
        : [];
    //General observation
    const notes = this.tabsConfig.generalObservations.show
      ? Array.isArray(notesFormValues.notes)
        ? notesFormValues.notes.map((note: any) => {
            const mappedNote: any = {
              note: note.note, // Texto da nota
              file: note.file?.base64
                ? {
                    base64:
                      note.file.base64 instanceof Object
                        ? (note.file.base64 as any).changingThisBreaksApplicationSecurity?.toString().replace(/^data:image\/\w+;base64,/, '') || null // Remove o prefixo base64 e encapsulamento de segurança
                        : note.file.base64?.toString().replace(/^data:image\/\w+;base64,/, '') || null,
                    name: note.file?.name || null // Nome do arquivo
                  }
                : null // Define `file` como `null` se `base64` não existir
            };

            // Adiciona `id` apenas se não for `null`
            if (note.id) {
              mappedNote.id = note.id;
            }

            return mappedNote;
          })
        : []
      : [];

    // Construir o payload completo
    return {
      id: this.dados.id,
      type: this.dados.type,
      //General data
      start_date: generalDataFormValues.StartDate ? moment(generalDataFormValues.StartDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DDTHH:mm:ss') : null,
      end_date: generalDataFormValues.EndDate ? moment(generalDataFormValues.EndDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DDTHH:mm:ss') : null,
      executed_by: generalDataFormValues.ExecutedBy ?? null,
      responsibles: Array.isArray(generalDataFormValues.Responsibles)
        ? generalDataFormValues.Responsibles.map((responsible: any) => {
            const mappedResponsible: any = {
              responsible: responsible.Responsible,
              is_internal_representative: responsible.IsInternalResponsible
            };

            // Adiciona `id` apenas se não for `null`
            if (responsible.Id !== null && responsible.Id !== undefined) {
              mappedResponsible.id = responsible.Id;
            }

            return mappedResponsible;
          })
        : [],
      evaluator_name: generalDataFormValues.EvaluatorName ?? null,
      evaluator_position: generalDataFormValues.EvaluatorPosition ?? null,
      evaluator_crea: generalDataFormValues.EvaluatorCrea ?? null,
      evaluator_art: generalDataFormValues.EvaluatorArt ?? null,
      file: this.dados.file,
      //General observation
      notes: notes,
      //Actions Executed
      identified_anomalies: identifiedAnomalies,
      //Aspects Observed
      areas: areas,
      //Conservation status
      spillway_structures_reliability: conservationStatusData.spillway_structures_reliability,
      percolation: conservationStatusData.percolation,
      deformations_and_settlements: conservationStatusData.deformations_and_settlements,
      slope_deterioration: conservationStatusData.slope_deterioration,
      surface_drainage: conservationStatusData.surface_drainage
    };
  }

  /**
   * Manipula o evento de clique em um botão ou ação específica.
   * @param {any} [$event=null] - Evento disparado.
   */
  clickEvent($event: any = null): void {
    if ($event.action === 'confirmInspection') {
      const payload = this.generatePayload(true);
      if (!payload) {
        console.warn('Payload inválido ou incompleto.');
        return;
      }
      // Confirmado, enviar o payload
      this.submitInspection(payload, true, { action: 'completeInspection' });
    }
  }

  /**
   * Submete os dados da ficha de inspeção ao backend.
   * @param {any} payload - Dados formatados da ficha de inspeção.
   * @param {boolean} userCompletedTheInspection - Indica se o usuário finalizou a inspeção.
   * @param {any} $event - Evento disparado com informações adicionais.
   */
  private submitInspection(payload: any, userCompletedTheInspection: boolean, $event: any): void {
    let messageToaster = '';
    this.inspectionSheetServiceApi.putInspectionSheet(this.dados.id, userCompletedTheInspection, payload).subscribe(
      (resp) => {
        switch ($event?.action) {
          case 'addAspect':
          case 'addOccurrence':
          case 'addOccurrenceAttachment':
          case 'addOccurrenceLinked':
            //this.getInspectionSheetById(resp.toString(), [2, 3].includes(this.status));

            // Cancelar o intervalo atual
            if (this.updateInterval$) {
              this.updateInterval$.unsubscribe();
            }

            // Chamar o endpoint imediatamente
            const inspectionSheetId = this.activatedRoute.snapshot.queryParams.inspectionSheetId;
            const forceEmptyOccurrences = [2, 3].includes(this.status);
            this.getInspectionSheetById(inspectionSheetId, forceEmptyOccurrences).subscribe(() => {
              // Reiniciar o intervalo após a execução do endpoint
              this.startUpdateInterval(inspectionSheetId, forceEmptyOccurrences);
            });

            this.tabsConfig.aspectsObserved.active = true;

            messageToaster = 'Ficha atualizada com com sucesso';
            if ($event?.action === 'addOccurrence') {
              messageToaster = 'Gerando Id para ocorrência';
            }

            break;
          case 'completeInspection':
            break;
          case 'useIdle':
            this.idleService.stopIdleDetection();
            this.router.navigate(['/inspections']);
            break;
          default:
            messageToaster = 'Ficha atualizada com com sucesso';
            break;
        }

        if (messageToaster != '') {
          this.toastr.success(messageToaster, 'Ficha Salva:');
        }
      },
      (error) => {
        //this.toastr.error('Não foi possível atualizar ficha.', 'Erro:');
        if (error.status >= 400) {
          this.messagesError = [];

          if (Array.isArray(error.error.errors)) {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          } else {
            Object.keys(error.error.errors).forEach((key) => {
              if (Array.isArray(error.error.errors[key])) {
                error.error.errors[key].forEach((msgError) => {
                  this.messagesError.push({ message: `${key}: ${msgError}` });
                });
              }
            });
          }
          // Exibe as mensagens por 4 segundos
          // setTimeout(() => {
          //   this.messagesError = [];
          // }, 4000);
        }
      }
    );
  }

  /**
   * Manipula alterações no formulário.
   * @param {any} $event - Evento disparado ao alterar o formulário.
   */
  onFormChange($event): void {
    this.onSave();
  }

  /**
   * Manipula mudanças na validação do formulário.
   * @param {any} $event - Evento contendo o status de validação.
   */
  onValidationChanged($event): void {
    if ($event.valid) {
      // Habilita todas as abas
      Object.keys(this.tabsConfig).forEach((key) => {
        this.tabsConfig[key as keyof typeof this.tabsConfig].disabled = false; // Reativa a aba
      });

      // Reativa os botões de navegação
      this.allowTabNavigation = true; // Variável controladora para os botões
      this.onSave();
    } else {
      console.warn(`Aba ${$event.tab} inválida.`);
      // Desabilita todas as abas, exceto a aba atual ($event.tab)
      Object.keys(this.tabsConfig).forEach((key) => {
        const isTargetTab = key === $event.tab;
        this.tabsConfig[key as keyof typeof this.tabsConfig].disabled = !isTargetTab; // Desativa as outras abas
      });
      // Desativa os botões de navegação
      this.allowTabNavigation = false; // Variável controladora para os botões
    }
  }

  /**
   * Executado ao destruir o componente.
   */
  ngOnDestroy(): void {
    // Cancela a subscrição do intervalo ao destruir o componente
    if (this.updateInterval$) {
      this.updateInterval$.unsubscribe();
    }

    this.idleService.stopIdleDetection();
  }

  /**
   * Executado quando o timer de inatividade é concluído.
   */
  onTimerComplete(): void {
    this.showTimer = false; // Oculta o timer
    this.executeAction(); // Chama o método desejado
  }

  /**
   * Executa a ação configurada ao detectar inatividade.
   */
  executeAction(): void {
    this.onSave({ action: 'useIdle' });
    // Insira aqui a lógica que deve ser executada
  }
}
