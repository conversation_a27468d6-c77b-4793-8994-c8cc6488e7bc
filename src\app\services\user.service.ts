import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { accessLevel, accessPermission, userLocale } from '../constants/app.constants';

import { acl, specialActions } from '../constants/permissions.constants';

import { filter, Observable, Subject } from 'rxjs';
import { map } from 'rxjs/operators';

import fn from '../utils/function.utils';
import jwt_decode from 'jwt-decode';

import { UsersService as UsersServiceApi } from 'src/app/services/api/users.service';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  public permissaoSubject: Subject<any> = new Subject();
  public permissaoUsuario: any = [];
  public permissoes: any = [];

  public userProfile: any = {};

  constructor(private oidcSecurityService: OidcSecurityService, private router: Router, private usersServiceApi: UsersServiceApi) {}

  /**
   * Retorna o perfil do usuário autenticado com base no token JWT.
   *
   * - Se `getUser` for `true`, retorna informações completas do usuário, como nome, e-mail, locale, permissões e alias.
   * - Caso contrário, retorna apenas o nível de acesso do usuário.
   *
   * @param getUser Indica se deve retornar o perfil completo do usuário.
   * @param page (opcional) Página atual para retornar as permissões específicas.
   * @returns Objeto com informações do usuário ou nível de acesso.
   */
  getProfile(getUser: boolean = false, page: string = null): any {
    const token = this.oidcSecurityService.getToken();
    const user: any = jwt_decode(token);
    const roles = user.realm_access.roles
      .map((role: string) => {
        if (accessLevel[role]) {
          return accessLevel[role];
        }
      })
      .filter((role: any) => role != undefined);

    if (getUser) {
      const arrayLocale: any = fn.objectToArray(userLocale, {
        id: 'sigla',
        value: 'label'
      });

      const alias = user.given_name.toUpperCase()[0] + user.family_name.toUpperCase()[0];

      const siglaLocale = fn.enumToArray(userLocale, {
        id: 'id',
        value: 'sigla'
      });
      const labelLocale = fn.enumToArray(userLocale, {
        id: 'id',
        value: 'label'
      });

      let i = 0;
      i = siglaLocale.findIndex((item: { label: any }) => (item.label === user.locale ? user.locale : ''));

      return {
        ...roles[0],
        name: user.given_name,
        family_name: user.family_name,
        email: user.email,
        alias,
        preferred_username: user.preferred_username,
        id: user.sub,
        permission: page ? accessPermission[roles[0].description][page] : null,
        locale: {
          id: siglaLocale[i].id,
          sigla: siglaLocale[i].label,
          label: labelLocale[i].label
        }
      };
    }
    return roles[0];
  }

  /**
   * Retorna a lista de localidades disponíveis em formato `{ id, label }` a partir do enum `userLocale`.
   */
  getLocale() {
    return fn.enumToArray(userLocale, { id: 'id', value: 'label' });
  }

  /**
   * Retorna um observable das permissões atuais atribuídas ao usuário.
   * Ideal para uso reativo em componentes que dependem de mudanças de permissão.
   *
   * @param user (opcional) Usuário para verificar permissões específicas.
   */
  getPermission(user: any = null): Observable<any> {
    return this.permissaoSubject.asObservable();
  }

  /**
   * Define as permissões do usuário com base na rota acessada e atualiza as variáveis `permissaoUsuario` e `permissoes`.
   * Este método escuta os eventos de navegação do Angular Router.
   */
  setPermission() {
    const level = this.getProfile();

    this.router.events.pipe(filter((event: any) => event instanceof NavigationEnd)).subscribe((event) => {
      const ocorrencia = (event.url.match(/\//g) || []).length;
      const pathQuery = event.url.split('?');
      const path = pathQuery[0].split('/');
      const tela = '/' + path[1];

      this.permissaoUsuario = acl[level.description][tela];
      this.permissoes = acl[level.description];

      if (ocorrencia === 1) {
        //listar
      } else if (ocorrencia === 2) {
        const ocorrenciaParam = (event.url.match(/;/g) || []).length;
        if (ocorrenciaParam === 0) {
          //Cadastrar-visualizar
        } else {
          // ??? Instrumentos
        }
      } else if (ocorrencia === 3) {
        //Editar
      }
    });
  }

  /**
   * Retorna as permissões (ACL) com base na URL atual e no nível de acesso do usuário.
   *
   * @param url Caminho da rota acessada.
   * @returns Permissões atribuídas à rota para o nível do usuário.
   */
  getAcl(url) {
    const level = this.getProfile();

    const pathQuery = url.split('?');
    const path = pathQuery[0].split('/');
    const tela = '/' + path[1];
    return acl[level.description][tela];
  }

  /**
   * Retorna a ação interpretada a partir da URL acessada, como `list`, `create`, `edit` ou `view`.
   *
   * - Considera a estrutura do path e presença de parâmetros.
   * - Também trata ações especiais definidas em `specialActions`.
   *
   * @param url Caminho da rota acessada.
   * @returns Ação interpretada (ex: list, create, edit, view).
   */
  getAction(url) {
    const ocorrencia = (url.match(/\//g) || []).length;

    const pathQuery = url.split('?');
    const path = pathQuery[0].split('/');
    const finalPath = path.pop();

    if (ocorrencia === 1) {
      return 'list';
    } else if (ocorrencia === 2) {
      const ocorrenciaParam = (url.match(/;/g) || []).length;
      if (ocorrenciaParam === 0) {
        if (finalPath in specialActions) {
          return specialActions[finalPath];
        } else {
          return 'create';
        }
      } else {
        // ??? Instrumentos
      }
    } else if (ocorrencia === 3) {
      const path = url.split('/');
      const partPath = path[3].split('?'); //Ignorar queryparams
      return partPath[0]; //Vai retornar edit ou view
    } else if (ocorrencia === 4) {
      return finalPath;
    }
  }

  /**
   * Retorna e armazena no atributo `userProfile` os dados completos do usuário,
   * incluindo nome, e-mail, alias, username, id, perfil e idioma.
   *
   * @param getUser Se `true`, retorna informações detalhadas do usuário.
   * @param page (opcional) Página atual para buscar permissões específicas.
   * @returns Objeto `userProfile` com os dados carregados do usuário.
   */
  getUserProfile(getUser: boolean = false, page: string = null) {
    const usuario = this.getProfile(getUser, page);
    this.userProfile['label'] = usuario.alias;
    this.userProfile['id'] = usuario.id;
    this.userProfile['name'] = usuario.name;
    this.userProfile['family_name'] = usuario.family_name;
    this.userProfile['alias'] = usuario.alias;
    this.userProfile['email'] = usuario.email;
    this.userProfile['username'] = usuario.preferred_username;
    this.userProfile['role'] = usuario.value;
    this.userProfile['locale'] = usuario.locale;
    return this.userProfile;
  }

  /**
   * Busca o usuário pelo ID informado (ou o usuário atual se `userId` estiver vazio).
   * ⚠️ Este método atualmente está com um fluxo assíncrono incorreto para retorno direto.
   * Idealmente, deveria retornar um observable com `return this.usersServiceApi.getUserById(...)`.
   *
   * @param userId (opcional) ID do usuário a ser buscado.
   * @returns Dados do usuário (⚠️ incompleto, pois não há retorno assíncrono real).
   */
  getUser(userId: string = '') {
    const id = userId != '' ? userId : this.userProfile.id ? this.userProfile.id : null;
    let dados: any = [];
    if (id != null) {
      this.usersServiceApi.getUserById(id).subscribe((resp) => {
        dados = resp;
        dados = dados.body === undefined ? dados : dados.body;
        return dados;
      });
    } else {
      return dados;
    }
  }

  /**
   * Retorna a hierarquia do usuário logado, incluindo clientes, unidades e estruturas.
   *
   * @returns Observable com a hierarquia do usuário no formato `{ clients, client_units, structures }`.
   */
  getUserHierarchy() {
    return this.usersServiceApi.getUsersMe().pipe(
      map((res: any) => {
        let dados = res;
        dados = dados.body === undefined ? res : res.body;
        let item = {};
        item['clients'] = res.clients;
        item['client_units'] = res.client_units;
        item['structures'] = res.structures;
        return item;
      })
    );
  }
}
