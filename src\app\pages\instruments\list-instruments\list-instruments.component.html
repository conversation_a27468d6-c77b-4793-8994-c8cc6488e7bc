<div class="list-content">
  <!-- Menu -->
  <div class="dashboard-menu mb-3 mt-3">
    <app-drag-and-drop
      [dataList]="menu"
      [orderField]="'Index'"
      [textField]="'Titulo'"
      [class]="'drag-list-flex'"
      (sendClickEvent)="clickEvent($event)"
    ></app-drag-and-drop>
  </div>
</div>
<div class="list-content mt-2">
  <!-- Notificações de Banner-->
  <app-alert
    *ngIf="showNotificationBanner"
    [class]="'alert-warning'"
    class="mt-3"
    [messages]="bannerNotifications"
    [showCloseButton]="true"
    [onClose]="handleCloseNotificationBanner.bind(this)"
  ></app-alert>

  <!-- Cadastrar instrumento -->
  <div
    class="button-instrument"
    (clickOutside)="onClickedOutside('instrument')"
  >
    <app-button
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Cadastrar Instrumento'"
      (click)="openModal('selectInstrument')"
      *ngIf="permissaoUsuario?.create"
    ></app-button>
  </div>

  <div class="row g-3 mt-1">
    <!-- ID -->
    <div class="col-md-3">
      <label class="form-label">ID</label>
      <input
        [(ngModel)]="filter.SearchIdentifier"
        type="number"
        step="1"
        min="1"
        class="form-control"
        placeholder="ID Instrumento"
        autocomplete="off"
        (keypress)="
          func.controlNumber(
            $event,
            filter.SearchIdentifier,
            'positive',
            'ngModel'
          )
        "
        (keyup)="
          func.controlNumber($event, filter.SearchIdentifier, null, 'ngModel')
        "
      />
    </div>
    <!-- Instrumento -->
    <div class="col-md-3">
      <label class="form-label">Nome</label>
      <input
        [(ngModel)]="filter.Identifier"
        type="text"
        class="form-control"
        placeholder="Nome instrumento"
        autocomplete="off"
      />
    </div>
    <!-- Tipo -->
    <div class="col-md-3">
      <label class="form-label">Tipo</label>
      <select class="form-select" [(ngModel)]="filter.Type">
        <option value="">Selecione...</option>
        <option *ngFor="let item of typeInstruments" [ngValue]="item.id">
          {{ item.name }}
        </option>
      </select>
    </div>
    <!-- Subtipo -->
    <div class="col-md-3">
      <label class="form-label">Subtipo</label>
      <select class="form-select" [(ngModel)]="filter.Subtype">
        <option value="">Selecione...</option>
        <option *ngFor="let item of subTypes" [ngValue]="item.id">
          {{ item.value }}
        </option>
      </select>
    </div>

    <!-- Selects Cliente, Unidade e Estrutura -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-9"
    ></app-hierarchy>

    <!-- Data da instalação -->
    <div class="col-md-3">
      <label class="form-label">Data da instalação</label>
      <input
        [(ngModel)]="filter.InstallationDate"
        type="date"
        class="form-control"
      />
    </div>

    <!-- Status -->
    <div class="col-md-3">
      <label class="form-label">Online</label>
      <select class="form-select" [(ngModel)]="filter.Online">
        <option value="">Selecione...</option>
        <option *ngFor="let item of onLine" [ngValue]="item.value">
          {{ item.label }}
        </option>
      </select>
    </div>
    <!-- Automatizado? -->
    <div class="col-md-3">
      <label class="form-label">Automatizado?</label>
      <select class="form-select" [(ngModel)]="filter.Automated">
        <option value="">Selecione...</option>
        <option *ngFor="let item of automated" [ngValue]="item.value">
          {{ item.label }}
        </option>
      </select>
    </div>
    <!-- Visualização -->
    <div class="col-md-3">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Botões -->
    <div class="col-md-3 d-flex align-items-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        class="me-1"
        (click)="managerFilters(true)"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>
  </div>

  <div class="row mt-3 mb-3">
    <label class="form-label"
      ><i class="fa fa-refresh"></i> Conversão de Coordenadas</label
    >
    <!-- Datum -->
    <div class="col-md-3">
      <label class="form-label">DATUM</label>
      <select class="form-select" [(ngModel)]="filter.Datum">
        <option value="">Selecione...</option>
        <option *ngFor="let item of datum" [ngValue]="item.id">
          {{ item.value }}
        </option>
      </select>
    </div>
    <!-- Coordenadas -->
    <div class="col-md-3">
      <label class="form-label">Coordenadas</label>
      <select class="form-select" [(ngModel)]="filter.CoordinateFormat">
        <option *ngFor="let item of coordinateFormat" [ngValue]="item.id">
          {{ item.label }}
        </option>
      </select>
    </div>
    <div class="col-md-3 d-flex align-items-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-refresh'"
        [label]="'Converter'"
        class="me-1"
        (click)="searchInstrument()"
      ></app-button>
    </div>
  </div>

  <!-- Alertas -->
  <div class="alert alert-warning" role="alert" *ngIf="messageReturn.status">
    {{ messageReturn.text }}
  </div>
  <div
    class="alert alert-success"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status"
  >
    {{ message.text }}
  </div>

  <!-- Tabela -->
  <app-table
    *ngIf="tableData.length > 0"
    [messageReturn]="messageReturn"
    [tableHeader]="tableHeader"
    [tableData]="tableData"
    (sendClickRowEvent)="clickRowEvent($event)"
    [permissaoUsuario]="permissaoUsuario"
    [menuMiniDashboard]="'miniDashboardInstrument'"
    [filterHeader]="filter"
  >
  </app-table>

  <!-- Paginação -->
  <app-paginator
    *ngIf="tableData.length > 0"
    [collectionSize]="collectionSize"
    [page]="page"
    [maxSize]="10"
    [boundaryLinks]="true"
    [pageSize]="pageSize"
    (sendPageChange)="loadPage($event)"
    [enableItemPerPage]="true"
  ></app-paginator>

  <div class="row g-3 mt-2" *ngIf="instrumentEvidence != null">
    <div class="col-md-3">
      <ng-multiselect-dropdown
        [placeholder]="'Selecione o(s) subtipo(s)'"
        [settings]="multipleSettings"
        [data]="subTypes"
        [(ngModel)]="filter.SubtypeMap"
        (onSelect)="changeSubtypes($event, 'select')"
        (onSelectAll)="changeSubtypes($event, 'selectAll')"
        (onDeSelect)="changeSubtypes($event, 'deselect')"
        (onDeSelectAll)="changeSubtypes($event, 'deselectAll')"
      >
      </ng-multiselect-dropdown>
    </div>
    <div class="col-md-6">
      <app-button
        [class]="
          showSections ? 'btn-logisoil-red me-2' : 'btn-logisoil-green me-2'
        "
        [ngClass]="showMaps ? 'show' : ''"
        [customBtn]="true"
        [label]="showSections ? 'Ocultar Seções' : 'Ver Seções'"
        (click)="managerSections()"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-blue me-2'"
        [customBtn]="true"
        [label]="'Criar grupo de instrumentos'"
        (click)="createGroupInstruments()"
        *ngIf="groupInstrumentsList.length > 0"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-green'"
        [customBtn]="true"
        [icon]="'fa fa-refresh'"
        [label]="'Recarregar mapa'"
        (click)="refreshMapInstruments()"
      ></app-button>
    </div>
  </div>
  <!-- Mapa -->
  <div class="row g-3 mt-1">
    <div class="col-md-2" *ngIf="showMaps">
      <label class="form-label">Zoom mapa:{{ dataMapsStructure.zoom }}</label>
      <br />
      <input
        type="range"
        class="range"
        #zoomStructure
        (input)="setZoom('structure', zoomStructure.value)"
        [value]="dataMapsStructure.zoom"
        min="0"
        max="22"
      />
    </div>
    <!-- Alerta -->
    <div
      class="alert alert-warning mt-2"
      role="alert"
      *ngIf="messageSection.status"
    >
      {{ messageSection.text }}
    </div>
  </div>

  <div class="row g-3">
    <!-- Mapa -->
    <div class="col-md-12 maps" [ngClass]="showMaps ? 'show' : ''">
      <app-google-maps
        #mapInstruments
        [height]="showMaps ? '500px' : '0px'"
      ></app-google-maps>
      <div *ngIf="showMaps">
        <label class="list-instruments mt-2">Legenda:</label>
        <div class="row">
          <div class="col-auto" *ngFor="let item of groupInstruments">
            <i class="{{ item.icon }}" [style.color]="item.color"></i>
            <span class="legend ms-2">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row g-3">
    <!-- Botão Voltar -->
    <div class="col-md-12 mt-3 d-flex align-items-end justify-content-end mb-2">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela inicial'"
        [click]="goBack.bind(this)"
      ></app-button>
    </div>
  </div>
  <app-modal-instrument #modalInstrument></app-modal-instrument>

  <!-- Botao Baixar Informacoes -->
  <app-modal-download-list-instrument
    #modalDownloadListInstrument
    (sendClickEvent)="clickEvent($event)"
  ></app-modal-download-list-instrument>

  <!-- Cadastro de instrumento via planilha -->
  <app-modal-insert-instrument-by-spreadsheet
    #modalInsertInstrumentBySpreadsheet
    (sendClickEvent)="clickEvent($event)"
  ></app-modal-insert-instrument-by-spreadsheet>

  <!-- Grafico variacao absoluta -->
  <app-modal-chart
    #modalChart
    [title]="titleModalChart"
    [instrumentInfo]="instrumentInfo"
  ></app-modal-chart>

  <ngx-spinner
    bdColor="rgba(51,51,51,0.8)"
    size="medium"
    color="#34b575"
    type="ball-clip-rotate-pulse"
    [fullScreen]="true"
    ><p class="mt-1" style="font-size: 18px; color: white">
      Carregando...
    </p></ngx-spinner
  >

  <!-- Modal generico para componentes -->
  <app-modal-components
    #modalComponents
    [config]="configModal"
    [component]="'app-images'"
    [title]="titleModal"
  ></app-modal-components>
</div>
