.list-content {
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.3);
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-control,
.form-select {
  font-size: 0.875em;
}

.form-check-input {
  margin-right: 0.5rem;
}

.span {
  font-size: 0.875em !important;
}

.table > thead > tr > th {
  font-size: 0.875em !important;
  color: #032561;
}

.table > tr {
  display: none;
}

.list-readings {
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 350px;
}

.d-flex.align-items-end > ng-multiselect-dropdown,
.d-flex.align-items-end > .form-select {
  flex: 1;
  min-width: 0;
}

.simulation-section,
.simulation-surface,
.simulation-readings {
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  padding: 1.5rem 1.5rem 1rem 1.5rem; // padding bottom reduzido para não colar no fim
  margin-bottom: 1.5rem;
  background-color: #ffffff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  position: relative;
  max-width: 100%;
  overflow: hidden;
  padding-top: 3rem; // espaço para o header fixo
}

.simulation-section::before,
.simulation-surface::before,
.simulation-readings::before {
  content: '';
  display: block;
  height: 38px;
  width: 100%;
  background-color: #34b575;
  position: absolute;
  top: 0;
  left: 0;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  z-index: 0;
}

.simulation-section .section-title,
.simulation-surface .section-title,
.simulation-readings .section-title {
  font-size: 1rem;
  color: #ffffff;
  margin: 0;
  padding: 0 1rem;
  height: 38px;
  line-height: 38px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0.5rem;

  input[type='checkbox'] {
    transform: scale(1.1);
    margin: 0;
  }
}

.simulation-surface,
.simulation-section,
.simulation-readings {
  overflow: visible; // Permite dropdown sair da div
  position: relative; // Garante posicionamento correto do dropdown
}

// Eleva o z-index da lista de opções do dropdown
ng-multiselect-dropdown .dropdown-list {
  z-index: 9999 !important;
}
