import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

import { Status, MultiSelectDefault } from 'src/app/constants/app.constants';
import { MessageCadastro } from 'src/app/constants/message.constants';
import { MessagePadroes } from 'src/app/constants/message.constants';

import { UserService } from 'src/app/services/user.service';
import { ClientService } from 'src/app/services/api/client.service';
import { FilterService } from 'src/app/services/filter.service';
import { NotificationService } from 'src/app/services/notification.service';

import * as moment from 'moment';

import fn from 'src/app/utils/function.utils';

import { NgxSpinnerService } from 'ngx-spinner';

//Tour guiado
import { CustomTourService } from 'src/app/services/custom-tour.service';
import { TourService } from 'ngx-ui-tour-ng-bootstrap';

@Component({
  selector: 'app-clients',
  templateUrl: './list-clients.component.html',
  styleUrls: ['./list-clients.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListClientsComponent implements OnInit {
  @ViewChild('hierarchy') hierarchy: any;

  tableHeader: any = [
    {
      label: 'ID',
      width: '80px',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Cliente',
      width: '100%',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'Período de Teste',
      width: '240px',
      rowspan: '',
      colspan: '2',
      show: true,
      referent: ['trial_period_start', 'trial_period_end']
    },
    {
      label: 'Período Contratual',
      width: '240px',
      rowspan: '',
      colspan: '2',
      show: true,
      referent: ['contractual_period_start', 'contractual_period_end']
    },
    {
      label: 'Status',
      width: '80px',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['active']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['action']
    }
  ];

  tableSubheader: any = [
    {
      label: 'Início',
      width: '120px',
      show: true,
      parent: 'Período de Teste',
      child: ''
    },
    {
      label: 'Fim',
      width: '120px',
      show: true,
      parent: 'Período de Teste',
      child: ''
    },
    {
      label: 'Início',
      width: '120px',
      show: true,
      parent: 'Período Contratual',
      child: ''
    },
    {
      label: 'Fim',
      width: '120px',
      show: true,
      parent: 'Período Contratual',
      child: ''
    }
  ];

  public tableData: any = [];

  public func = fn;

  public status: any = Status;

  public viewSettings = MultiSelectDefault.View;

  public selectedColumns = this.tableHeader;
  public selectedClient: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };
  public messageWarning: any = { text: '', status: false };

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public bannerNotifications: any = [];
  public showNotificationBanner: boolean = true;

  public filterParams: any = {};
  public filter: any = {
    Name: '',
    Active: '',
    SearchIdentifier: '',
    StartTrialPeriod: '',
    EndTrialPeriod: '',
    StartContractualPeriod: '',
    EndContractualPeriod: ''
  };

  public client: any = {
    id: null,
    active: null
  };

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    }
  };

  public filterSearch: any = {};

  constructor(
    private router: Router,
    private clientService: ClientService,
    private customTourService: CustomTourService,
    private filterService: FilterService,
    private userService: UserService,
    private ngxSpinnerService: NgxSpinnerService,
    private notificationService: NotificationService,
    public tourService: TourService
  ) {}

  /**
   * Inicializa o componente ao carregá-lo.
   * Exibe o spinner de carregamento, carrega o perfil do usuário e as permissões associadas.
   * Caso o usuário não tenha permissão para visualizar a lista de clientes, remove o item correspondente dos elementos visíveis.
   */
  ngOnInit(): void {
    this.ngxSpinnerService.show();

    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.notificationService.notificationsBanner$.subscribe(({ licenseNotifications }) => {
      this.bannerNotifications = licenseNotifications;
    });

    this.notificationService.bannerVisibility$.subscribe(({ licenseBannerStatus }) => {
      this.showNotificationBanner = licenseBannerStatus;
    });
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros após um pequeno delay.
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
    }, 1000);
  }

  /**
   * Obtém a lista de clientes com base nos parâmetros fornecidos e formata os dados para exibição na tabela.
   * @param {any} params - Parâmetros de filtro para a busca de clientes.
   */
  getClientsList(params) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.clientService.getClients(params).subscribe(
      (resp) => {
        const dados: any = resp;
        if (dados.status == 200) {
          this.tableData = dados.body.data ? dados.body.data : [];
          this.collectionSize = dados.body.total_items_count;
          this.formatData();
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * Alterna o status de um cliente e salva as alterações.
   * @param {any} client - Objeto cliente com o status atualizado.
   */
  toggleStatus(client: any) {
    this.client.id = client.id;
    this.client.active = client.active;
    this.editClient();
  }

  //Edita as informações de um cliente específico, alterando o status e exibindo uma mensagem de sucesso.
  editClient() {
    this.clientService.patchClients(this.client.id, this.client).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.AlteracaoStatus;
      this.message.status = true;
      this.messageReturn.class = 'alert-success';

      setTimeout(() => {
        this.message.status = false;
      }, 4000);
    });
  }

  //Pesquisa clientes com base nos filtros aplicados e atualiza a lista de clientes exibida.
  searchClient() {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterParams = {};

    this.filter.StartTrialPeriod = this.filter.StartTrialPeriod != '' ? moment(this.filter.StartTrialPeriod).format('YYYY-MM-DD') : '';
    this.filter.EndTrialPeriod = this.filter.EndTrialPeriod != '' ? moment(this.filter.EndTrialPeriod).format('YYYY-MM-DD') : '';
    this.filter.StartContractualPeriod = this.filter.StartContractualPeriod != '' ? moment(this.filter.StartContractualPeriod).format('YYYY-MM-DD') : '';
    this.filter.EndContractualPeriod = this.filter.EndContractualPeriod != '' ? moment(this.filter.EndContractualPeriod).format('YYYY-MM-DD') : '';

    //Formatacao de dados
    this.filterParams.Active = this.filter.Active;
    this.filterParams.Name = filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].name : '';
    this.filterParams.SearchIdentifier = this.filter.SearchIdentifier == null ? '' : this.filter.SearchIdentifier;
    this.filterParams.ClientId = '';
    this.filterParams.StartTrialPeriod = this.filter.StartTrialPeriod;
    this.filterParams.EndTrialPeriod = this.filter.EndTrialPeriod;
    this.filterParams.StartContractualPeriod = this.filter.StartContractualPeriod;
    this.filterParams.EndContractualPeriod = this.filter.EndContractualPeriod;

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());

    this.getClientsList(this.filterSearch);
  }

  /**
   * Gerencia a aplicação e recuperação de filtros, decidindo se deve executar uma nova pesquisa ou usar os filtros existentes.
   * @param {boolean} [$btn=false] - Indica se a pesquisa deve ser realizada novamente.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchClient(); // Executa a busca diretamente ao clicar no botão
    } else {
      // Recupera os filtros salvos
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchClient(); // Realiza uma nova busca caso não haja filtros
      } else {
        // Aplica os filtros existentes
        this.filterSearch = data.filters;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;

        //Formulario de filtro
        this.filter.Name = this.filterSearch.Name;
        this.filter.Active = this.filterSearch.Active;
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier;
        this.filter.StartTrialPeriod = this.filterSearch.StartTrialPeriod;
        this.filter.EndTrialPeriod = this.filterSearch.EndTrialPeriod;
        this.filter.StartContractualPeriod = this.filterSearch.StartContractualPeriod;
        this.filter.EndContractualPeriod = this.filterSearch.EndContractualPeriod;
        this.getClientsList(this.filterSearch);
      }

      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
      }
    }
  }

  //Reseta os filtros aplicados ao formulário de pesquisa de clientes e executa a busca padrão.
  resetFilter() {
    this.hierarchy.resetFilters();

    this.filter = {
      Name: '',
      Active: '',
      SearchIdentifier: '',
      StartTrialPeriod: '',
      EndTrialPeriod: '',
      StartContractualPeriod: '',
      EndContractualPeriod: ''
    };

    this.filterParams = {};
    this.filterSearch = {};
    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.managerFilters();
  }

  //Formata as datas no formato de exibição esperado para a lista de clientes.
  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      item.trial_period_start = '';
      item.trial_period_end = '';
      item.contractual_period_start = '';
      item.contractual_period_end = '';

      if (item.periods.trial_period && item.periods.trial_period.start) {
        item.trial_period_start = moment(item.periods.trial_period.start).format('DD/MM/YYYY');
      }
      if (item.periods.trial_period && item.periods.trial_period.end) {
        item.trial_period_end = moment(item.periods.trial_period.end).format('DD/MM/YYYY');
      }
      if (item.periods.contractual_period && item.periods.contractual_period.start) {
        item.contractual_period_start = moment(item.periods.contractual_period.start).format('DD/MM/YYYY');
      }
      if (item.periods.contractual_period && item.periods.contractual_period.end) {
        item.contractual_period_end = moment(item.periods.contractual_period.end).format('DD/MM/YYYY');
      }
      delete item.periods;
      return item;
    });
  }

  /**
   * Alterna a visibilidade das colunas no grid de visualização de clientes.
   * @param {any} $event - Evento disparado ao alterar a visibilidade das colunas.
   * @param {string} type - Tipo de ação (select, deselect, selectAll, deselectAll).
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;

      //Subheader
      if (this.tableSubheader) {
        this.tableSubheader.forEach((element: any, i: number) => {
          if (this.tableSubheader[i].parent === $event.label) {
            this.tableSubheader[i].show = type === 'select' ? true : type === 'deselect' ? false : this.tableSubheader[i].show;
          }
        });
      }
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });

      //Subheader
      if (this.tableSubheader) {
        this.tableSubheader.forEach((element: any, i: number) => {
          this.tableSubheader[i].show = true;
        });
      }
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });

      if (this.tableSubheader) {
        this.tableSubheader.forEach((element: any, i: number) => {
          this.tableSubheader[i].show = false;
        });
      }
    }
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }
    this.managerFilters();
  }

  //Fecha o banner de notificação e notifica o serviço para que o banner não seja mais exibido.
  handleCloseNotificationBanner() {
    this.showNotificationBanner = false;
    this.notificationService.handleCloseLicenseBanner();
  }

  loadTourGuide() {
    this.customTourService.startTour(this.tourService, 'assets/tour-guide/list-clients.tourguide.json');
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  //Retorna para a página inicial da aplicação.
  goBack() {
    this.router.navigate(['/']);
  }
}
