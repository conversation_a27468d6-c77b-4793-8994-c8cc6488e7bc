<div class="list-content">
  <div class="row g-3 mt-2">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
      <!-- Aba Geral -->
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="generalTabConfig.active ? 'active' : ''"
          id="general-tab"
          type="button"
          role="tab"
          aria-controls="general"
          aria-selected="true"
          (click)="crtlSaveStaticMaterials = ''; selectTab('general')"
          [style.background-color]="
            generalTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="generalTabConfig.styleColor ? '#ffffff' : ''"
        >
          Geral
        </button>
      </li>
      <!-- Aba Revisão -->
      <li class="nav-item" role="presentation" *ngIf="edit || view">
        <button
          class="nav-link"
          [ngClass]="reviewTabConfig.active ? 'active' : ''"
          id="review-tab"
          type="button"
          role="tab"
          aria-controls="review"
          aria-selected="true"
          (click)="crtlSaveStaticMaterials = 'review'; selectTab('review')"
          [style.background-color]="reviewTabConfig.styleColor ? '#dc3545' : ''"
          [style.color]="reviewTabConfig.styleColor ? '#ffffff' : ''"
        >
          Revisão Material
        </button>
      </li>
    </ul>

    <div class="tab-content" id="myTabContent">
      <!-- Aba Geral - Conteúdo -->
      <div
        class="tab-pane fade"
        [ngClass]="generalTabConfig.active ? 'show active' : ''"
        id="general"
        role="tabpanel"
        aria-labelledby="general-tab"
      >
        <app-general-tab
          [view]="view"
          [edit]="edit"
          #generalTab
        ></app-general-tab>
      </div>
      <!-- Aba Revisão Material - Conteúdo -->
      <div
        class="tab-pane fade"
        [ngClass]="reviewTabConfig.active ? 'show active' : ''"
        id="review"
        role="tabpanel"
        aria-labelledby="review-tab"
      >
        <app-review-tab [edit]="edit" [view]="view" #reviewTab></app-review-tab>
      </div>
    </div>
  </div>

  <!-- Alertas -->
  <div
    class="col-md-12 alert mt-2"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status"
    [innerHTML]="message.text"
  ></div>
  <app-alert
    class="mt-2"
    [class]="'alert-danger'"
    [messages]="messagesError"
  ></app-alert>

  <!-- Botões -->
  <div class="col-md-12 d-flex justify-content-end mt-3">
    <!-- Botão Salvar -->
    <app-button
      [class]="'btn-logisoil-green'"
      [icon]="'fa fa-thin fa-floppy-disk'"
      [label]="'Salvar'"
      [type]="true"
      class="me-1"
      (click)="validate()"
      *ngIf="generalTabConfig.active && !view"
    >
    </app-button>
    <!-- Botão Retornar -->
    <app-button
      *ngIf="reviewTabConfig.active"
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Retornar'"
      class="me-1"
      (click)="onBack()"
    ></app-button>
    <!-- Botão Avançar -->
    <app-button
      *ngIf="generalTabConfig.active && edit"
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-right'"
      [label]="'Avançar'"
      class="me-1"
      (click)="onNext()"
    ></app-button>
  </div>

  <div class="col-md-12 d-flex justify-content-end mt-3 mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/materials']"
    ></app-button>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
