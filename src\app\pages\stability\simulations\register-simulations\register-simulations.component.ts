import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { debounceTime, distinctUntilChanged, filter, Subscription } from 'rxjs';

import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';
import {
  CalculationMethods,
  Conditions,
  Period,
  PhreaticPiezometric,
  UpstreamWaterLevelReference,
  DownstreamWaterLevelReference,
  BeachLengthReference,
  ReferenceReadings,
  SurfaceType,
  SearchMethod,
  PhreaticPiezometricFields,
  ConditionFields
} from 'src/app/constants/simulations.constants';

import { NgxSpinnerService } from 'ngx-spinner';
import { UserService } from 'src/app/services/user.service';
import { NotificationService as NotificationServiceApi } from 'src/app/services/notification.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { SimulatorService as SimulatorServiceApi } from 'src/app/services/api/simulator.service';

import * as moment from 'moment';
import * as _ from 'lodash';
import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-register-simulations',
  templateUrl: './register-simulations.component.html',
  styleUrls: ['./register-simulations.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RegisterSimulationsComponent implements OnInit, OnDestroy {
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalSearchMethodCircular') ModalSearchMethodCircular: any;
  @ViewChild('modalSearchMethodNonCircular') ModalSearchMethodNonCircular: any;

  public func = fn;
  public formSimulation: FormGroup;
  public formSimulationSections: FormGroup;

  public formFields: any = [
    { name: 'SectionId', isArray: true, isRequired: true },
    { name: 'SectionReviewId', isArray: true, isRequired: false },
    { name: 'SurfaceTypeCircular', isArray: false, isRequired: false },
    { name: 'SurfaceTypeNonCircular', isArray: false, isRequired: false },
    { name: 'CalculationMethodsCircular', isArray: true, isRequired: true, condition: 'SurfaceTypeCircular' },
    { name: 'SearchMethodCircular', isArray: false, isRequired: true, condition: 'SurfaceTypeCircular' },
    { name: 'CalculationMethodsNonCircular', isArray: true, isRequired: true, condition: 'SurfaceTypeNonCircular' },
    { name: 'SearchMethodNonCircular', isArray: false, isRequired: true, condition: 'SurfaceTypeNonCircular' },
    { name: 'Conditions', isArray: true, isRequired: true },
    { name: 'PhreaticPiezometric', isArray: false, isRequired: true },
    { name: 'SafetyFactorTarget', isArray: false, isRequired: true, condition: 'SafetyFactorTargetCheck' },
    { name: 'SafetyFactorTargetCheck', isArray: false, isRequired: false },
    { name: 'ReferenceReadings', isArray: false, isRequired: true },
    { name: 'Period', isArray: false, isRequired: true },
    { name: 'PhreaticVariation', isArray: false, isRequired: true },
    { name: 'AfterDate', isArray: false, isRequired: true },
    { name: 'BeforeDate', isArray: false, isRequired: true },
    { name: 'ReadingStatisticalMeasure', isArray: false, isRequired: true },
    { name: 'UpstreamWaterLevelReference', isArray: false, isRequired: true },
    { name: 'UpstreamWaterLevel', isArray: false, isRequired: true },
    { name: 'DownstreamWaterLevelReference', isArray: false, isRequired: true },
    { name: 'DownstreamWaterLevel', isArray: false, isRequired: true },
    { name: 'SeismicCoefficientHorizontal', isArray: false, isRequired: true },
    { name: 'SeismicCoefficientVertical', isArray: false, isRequired: true },
    { name: 'Name', isArray: false, isRequired: false }
  ];

  private formSubscriptions: Subscription[] = [];
  private previousFormValue: any;

  public sectionSettings = _.cloneDeep(MultiSelectDefault.Sections);
  public sections: any = [];
  public sectionReviewsSettings = _.cloneDeep(MultiSelectDefault.Sections);
  public sectionConstructionStages: { [key: string]: any[] } = {};

  public sectionsReviews: any = {};
  public sectionsReviewsLatest: any = {};

  public edit: boolean = false;

  //Método de cálculo
  public calculationMethods = {
    Circular: CalculationMethods,
    NonCircular: CalculationMethods
  };

  //Método de busca
  public searchMethod = {
    Circular: [],
    NonCircular: []
  };

  public conditions = Conditions; //Condições
  public surfaceType = SurfaceType; //Tipo de Superfície

  public phreaticPiezometric = PhreaticPiezometric; //Freática-Piezométrica
  public upstreamWaterLevelReference = UpstreamWaterLevelReference; //Ref. NA montante
  public downstreamWaterLevelReference = DownstreamWaterLevelReference; //Ref. NA jusante
  public beachLengthReference = BeachLengthReference; //Ref. praia
  public referenceReadings = ReferenceReadings; //Referência Leituras
  public period = Period; //Período
  public phreaticPiezometricFields = PhreaticPiezometricFields;
  public conditionFields = ConditionFields;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public messageNoInstrument: any = { text: '', status: false, class: 'alert-success' };
  public messageNoDxf: any = { text: '', status: false, class: 'alert-success' };

  public messageNotInstruments: any = {};

  public bannerNotifications: any = [];
  public showNotificationBanner: boolean = true;

  public ctrlSism: boolean = false;

  public dados: any = null;

  public titleCard: string = '';
  public showFields: boolean = false;

  //Modal
  public configModal: any = null;
  public titleModal: string = '';

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public dropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 10,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  public conditionSettings = {
    singleSelection: false,
    idField: 'value',
    textField: 'label',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 3,
    allowSearchFilter: false
  };

  public showFieldsItems = {
    ReferenceReadings: false,
    Period: false,
    AfterDate: false,
    BeforeDate: false,
    PhreaticVariation: false,
    SeismicCoefficientHorizontal: true,
    SeismicCoefficientVertical: true,
    UpstreamWaterLevelReference: false,
    UpstreamWaterLevel: false,
    DownstreamWaterLevelReference: false,
    DownstreamWaterLevel: false
  };

  // public showFieldsItems: any = {
  //   ReferenceReadings: false,
  //   Period: false,
  //   PhreaticVariation: false,
  //   AfterDate: false,
  //   BeforeDate: false,
  //   ReadingStatisticalMeasure: false,
  //   SeismicCoefficientHorizontal: false,
  //   SeismicCoefficientVertical: false,
  //   UpstreamWaterLevelReference: false,
  //   UpstreamWaterLevel: false,
  //   DownstreamWaterLevelReference: false,
  //   DownstreamWaterLevel: false
  // };

  public sectionsMap: { [key: string]: any } = {};
  public showConditionSummary: boolean = true;

  public disabledSimulation: boolean | null = null;

  constructor(
    private formBuilder: FormBuilder,
    private ngxSpinnerService: NgxSpinnerService,
    private notificationServiceApi: NotificationServiceApi,
    private sectionsServiceApi: SectionsServiceApi,
    private simulatorServiceApi: SimulatorServiceApi,
    private router: Router,
    private userService: UserService
  ) {
    this.buildForm();
  }

  /**
   * Método executado ao inicializar o componente.
   * Configura o perfil do usuário, permissões e monitora mudanças no formulário.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.sectionSettings.singleSelection = false;
    this.sectionReviewsSettings.singleSelection = true;

    this.notificationServiceApi.notificationsBanner$.subscribe(({ stabilityNotifications }) => {
      this.bannerNotifications = stabilityNotifications;
    });

    this.notificationServiceApi.bannerVisibility$.subscribe(({ stabilityBannerStatus }) => {
      this.showNotificationBanner = stabilityBannerStatus;
    });

    this.previousFormValue = this.formSimulation.value;

    this.monitorAllFormControls();
    this.initializeFieldValidations();
    this.updateWaterLevelFields('UpstreamWaterLevelReference', 'UpstreamWaterLevel');
    this.updateWaterLevelFields('DownstreamWaterLevelReference', 'DownstreamWaterLevel');

    // Observa mudança na seção
    this.formSubscriptions.push(
      this.formSimulation
        .get('SectionId')
        ?.valueChanges.pipe(
          debounceTime(300),
          distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)),
          filter((value: any[]) => value !== null) // Garante que não seja null
        )
        .subscribe((value: any[]) => {
          if (value.length === 0) {
            this.resetForms(false); // Chama reset se não houver seções
          } else {
            this.validatePhreaticConfiguration();
          }
        })
    );

    // Observa mudança na configuração freática
    this.formSimulation.get('PhreaticPiezometric')?.valueChanges.subscribe(() => {
      this.validatePhreaticConfiguration();
    });

    // Observa mudança na revisão da seção
    this.formSimulation.get('SectionForm')?.valueChanges.subscribe(() => {
      this.validatePhreaticConfiguration();
    });

    // Observa mudança na condição
    this.formSimulation.get('Conditions')?.valueChanges.subscribe(() => {
      this.validatePhreaticConfiguration();
    });
  }

  ngAfterViewInit() {
    setTimeout(() => window.dispatchEvent(new Event('resize')), 100);
  }

  /**
   * Inicializa as validações de campos no formulário de simulação.
   * Ajusta as configurações de validação para os tipos de superfície 'Circular' e 'NonCircular'
   * com base nos valores atuais dos respectivos campos.
   */
  initializeFieldValidations() {
    this.handleSurfaceTypeChange('Circular', this.formSimulation.get('SurfaceTypeCircular').value);
    this.handleSurfaceTypeChange('NonCircular', this.formSimulation.get('SurfaceTypeNonCircular').value);
  }

  /**
   * Gerencia o comportamento do componente com base no tipo e ação do evento recebido.
   * Para 'units', limpa as seções. Para 'structures', carrega os dados do simulador se a ação for 'select'.
   * Em caso de 'deselect', reseta os formulários.
   * @param {any} $event - O evento contendo o tipo ('units', 'structures') e ação ('select', 'deselect').
   */
  getEventHierarchy($event) {
    this.resetForms();

    switch ($event.type) {
      case 'units':
        this.sections = [];
        break;
      case 'structures':
        this.sections = [];
        if ($event.element != null) {
          if ($event.action === 'select') {
            this.getSimulatorData($event.element);
          }
        }
        break;
    }
  }

  /**
   * Obtém os dados do simulador relacionados à estrutura selecionada.
   * Faz uma requisição à API do simulador passando os parâmetros necessários.
   * @param {any} structure - A estrutura selecionada para buscar os dados de simulação.
   */
  getSimulatorData(structure) {
    this.ngxSpinnerService.show();

    this.messagesError = [];
    this.message.text = '';
    this.message.status = false;
    this.message.class = 'alert-success';

    const params = {
      StructureId: structure.id,
      IgnoreDamagedInstruments: this.formSimulation.get('IgnoreDamagedInstruments').value
    };

    this.simulatorServiceApi.getStructuresSimulationData(params).subscribe(
      (resp) => {
        this.dados = resp;
        this.dados = this.dados.body === undefined ? this.dados : this.dados.body;
        this.sections = this.extractSectionsIdAndName(this.dados);

        this.sectionsMap = {};
        this.dados.sections?.forEach((section) => {
          this.sectionsMap[section.id] = section;
        });

        if (this.sections.length === 0) {
          this.message = {
            text: MessagePadroes.NeedToHaveDXF,
            status: true,
            class: 'alert-warning'
          };
          setTimeout(() => {
            this.message.status = false;
          }, 5000);
        }

        this.ngxSpinnerService.hide();
      },
      (error) => {
        this.ngxSpinnerService.hide();
        console.log(error);
      }
    );
  }

  /**
   * Constrói o formulário de simulações com os campos necessários e suas validações.
   */
  buildForm() {
    this.formSimulation = this.formBuilder.group({
      Name: [''],
      SectionId: [[], Validators.required],
      SectionReviewId: [[], Validators.required],
      SurfaceTypeCircular: [false],
      SurfaceTypeNonCircular: [false],

      //Métodos de Cálculo e de Busca
      CalculationMethodsCircular: [''],
      SearchMethodCircular: [''],
      CalculationMethodsNonCircular: [''],
      SearchMethodNonCircular: [''],

      //Condições e Freática
      Conditions: [''],
      PhreaticPiezometric: [''],
      SafetyFactorTarget: [''],
      SafetyFactorTargetCheck: [false],

      ReferenceReadings: [''],
      Period: [2],
      PhreaticVariation: [''],
      AfterDate: [''],
      BeforeDate: [''],
      // ReadingStatisticalMeasure: [''],

      UpstreamWaterLevelReference: [1],
      UpstreamWaterLevel: [''],

      DownstreamWaterLevelReference: [1],
      DownstreamWaterLevel: [''],

      SeismicCoefficientHorizontal: [''],
      SeismicCoefficientVertical: [''],

      IgnoreDamagedInstruments: [false],

      SectionForm: this.formBuilder.array([])
    });

    this.formSimulationSections = this.formBuilder.group({
      SectionFormItems: this.formBuilder.array([])
    });

    this.calculatePeriod(2);
  }

  /**
   * Monitora todos os controles do formulário e define assinaturas para mudanças nos valores.
   */
  monitorAllFormControls() {
    Object.keys(this.formSimulation.controls).forEach((controlName) => {
      const control = this.formSimulation.get(controlName);
      if (control instanceof FormArray) {
        control.controls.forEach((group) => {
          if (group instanceof FormGroup) {
            this.monitorFormGroupControls(group);
          }
        });
      } else if (control instanceof FormControl) {
        this.subscribeToControl(control, controlName);
      }
    });
  }

  /**
   * Monitora os controles dentro de um grupo de formulário e define assinaturas para mudanças nos valores.
   * @param {FormGroup} group - O grupo de formulário a ser monitorado.
   */
  monitorFormGroupControls(group: FormGroup) {
    Object.keys(group.controls).forEach((controlName) => {
      const control = group.get(controlName);
      if (control instanceof FormControl) {
        this.subscribeToControl(control, controlName);
      }
    });
  }

  /**
   * Inscreve-se nas mudanças de um controle específico e executa ações dependendo do controle que foi alterado.
   * @param {FormControl} control - O controle a ser monitorado.
   * @param {string} controlName - O nome do controle.
   */
  subscribeToControl(control: FormControl, controlName: string) {
    let previousValue = control.value;
    this.formSubscriptions.push(
      control.valueChanges.subscribe((value) => {
        if (value === previousValue) {
          return; // Evita a execução se o valor não mudou
        }

        previousValue = value;

        if (controlName === 'SectionId' && this.dados != null) {
          this.populateFormSimulationSectionFormArray(this.dados.sections, value);
        }

        if (controlName === 'SurfaceTypeCircular' || controlName === 'SurfaceTypeNonCircular') {
          const type = controlName.replace('SurfaceType', '');
          this.handleSurfaceTypeChange(type, value);
        }

        // Controle por campo
        if (controlName === 'Conditions') {
          value = Array.isArray(value) ? value : [];
          if (!value.some((item) => item.value === 3)) {
            this.formSimulation.get('SafetyFactorTargetCheck').setValue(false);
            this.formSimulation.get('SafetyFactorTarget').setValue('');
            this.ctrlSism = false;
          } else {
            this.ctrlSism = true;
          }
          if (this.dados != null) {
            this.populateFormSimulationSectionFormArray(this.dados.sections, this.formSimulation.get('SectionId').value);
            this.updateFieldVisibility();
          }
        }

        if (controlName === 'PhreaticPiezometric') {
          this.updateFieldVisibility();
          this.buildSectionsForm();
        }

        if (controlName === 'IgnoreDamagedInstruments') {
          this.updateFormOnIgnoreDamagedInstrumentsChange();
        }

        if (controlName === 'SafetyFactorTargetCheck') {
          const safetyFactorTargetControl = this.formSimulation.get('SafetyFactorTarget');
          if (value) {
            safetyFactorTargetControl.setValidators([Validators.required]);
          } else {
            safetyFactorTargetControl.clearValidators();
          }
          safetyFactorTargetControl.updateValueAndValidity();
        }

        if (controlName === 'UpstreamWaterLevelReference') {
          this.updateWaterLevelFields('UpstreamWaterLevelReference', 'UpstreamWaterLevel');
        }

        if (controlName === 'DownstreamWaterLevelReference') {
          this.updateWaterLevelFields('DownstreamWaterLevelReference', 'DownstreamWaterLevel');
        }

        if (controlName === 'Period') {
          this.calculatePeriod(value);
        }
      })
    );
  }

  /**
   * Calcula o período com base no valor fornecido e ajusta os campos de data do formulário de simulação.
   * Define as datas de início e fim conforme o valor do período, bloqueando ou desbloqueando os campos
   * 'AfterDate' e 'BeforeDate' de acordo.
   *
   * @param {number} value - O valor do período utilizado para calcular as datas e definir o estado dos campos de data.
   */
  calculatePeriod(value) {
    const afterDateControl = this.formSimulation.get('AfterDate');
    const beforeDateControl = this.formSimulation.get('BeforeDate');

    beforeDateControl.setValue(null);
    afterDateControl.setValue(null);

    if (parseInt(value) > 1) {
      const today = moment().toDate(); // Define a data de hoje como um objeto Date

      if (beforeDateControl) {
        beforeDateControl.setValue(moment(today).format('YYYY-MM-DD')); // Seta a data de hoje no formato 'YYYY-MM-DD'

        const afterDate = this.calculateNewDate(today, parseInt(value));
        afterDateControl.setValue(moment(afterDate).format('YYYY-MM-DD'));
      }
    }
    if (parseInt(value) !== 0) {
      // Bloquear os campos AfterDate e BeforeDate
      afterDateControl.disable();
      beforeDateControl.disable();
    } else {
      afterDateControl.enable();
      beforeDateControl.enable();
    }
  }

  /**
   * Lida com as mudanças no tipo de superfície e ajusta os métodos de cálculo e busca de acordo.
   * @param {string} type - O tipo de superfície ('Circular' ou 'NonCircular').
   * @param {boolean} isEnabled - Indica se o tipo de superfície está habilitado.
   */
  handleSurfaceTypeChange(type: string, isEnabled: boolean) {
    const calcMethodControl = this.formSimulation.get(`CalculationMethods${type}`);
    const searchMethodControl = this.formSimulation.get(`SearchMethod${type}`);

    if (isEnabled) {
      this.setCalculationMethods(this.toSnakeCase(type));

      calcMethodControl.setValidators([Validators.required]);
      searchMethodControl.setValidators([Validators.required]);
      calcMethodControl.enable();
      searchMethodControl.enable();
    } else {
      calcMethodControl.setValue('');
      searchMethodControl.setValue('');
      calcMethodControl.clearValidators();
      searchMethodControl.clearValidators();
      calcMethodControl.disable();
      searchMethodControl.disable();
    }

    calcMethodControl.updateValueAndValidity();
    searchMethodControl.updateValueAndValidity();
  }

  /**
   * Retorna o FormArray associado ao 'SectionForm' no formulário de simulação.
   * @returns {FormArray} O FormArray de seções.
   */
  get sectionForm(): FormArray {
    return this.formSimulation.get('SectionForm') as FormArray;
  }

  /**
   * Retorna o FormArray associado aos itens de 'SectionFormItems' no formulário de simulação.
   * @returns {FormArray} O FormArray de itens de seções.
   */
  get sectionFormItems(): FormArray {
    return this.formSimulationSections.get('SectionFormItems') as FormArray;
  }

  /**
   * Define os métodos de cálculo com base no tipo de superfície selecionado e nos dados disponíveis.
   * @param {string} surfaceType - O tipo de superfície em formato snake_case.
   */
  setCalculationMethods(surfaceType) {
    const sufix = surfaceType
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');

    if (surfaceType != null && this.dados != null) {
      const ids = this.dados.structure.slide2_configuration[surfaceType + '_parameters'].calculation_methods;
      const methods = this.calculationMethods[sufix].filter((item) => ids.includes(Number(item.id)));
      this.formSimulation.get('CalculationMethods' + sufix).setValue(methods);
    } else {
      this.formSimulation.get('CalculationMethods' + sufix).setValue([]);
    }
    this.setSearchMethod(surfaceType, sufix);
  }

  /**
   * Define o método de busca com base no tipo de superfície e métodos de cálculo.
   * @param {any} surfaceType - O tipo de superfície.
   * @param {any} [calculationMethods=null] - Os métodos de cálculo.
   */
  setSearchMethod(surfaceType, sufix = null) {
    if (surfaceType != null) {
      this.searchMethod[sufix] = SearchMethod['surface_type_' + surfaceType].search;
      if (this.dados != null) {
        let searchMethod = this.dados.structure.slide2_configuration[surfaceType + '_parameters'][surfaceType + '_search_method'];
        setTimeout(() => {
          this.formSimulation.get('SearchMethod' + sufix).setValue(searchMethod.toString());
        }, 50);
      }
    } else {
      this.formSimulation.get('SearchMethod' + sufix).setValue('');
    }
  }

  /**
   * Popula o FormArray `sectionForm` com as seções e condições filtradas,
   * configurando controles dinâmicos e regras de validação com base nos valores fornecidos.
   *
   * @param {any[]} sections - Array de todas as seções disponíveis.
   * @param {any[]} selectedSections - Array de seções selecionadas pelo usuário.
   */
  populateFormSimulationSectionFormArray(sections: any[], selectedSections: any[]) {
    this.ngxSpinnerService.show();

    const formArray = this.sectionForm;
    const sectionIds = selectedSections.map((section) => section.id);
    const currentValues = formArray.value;

    formArray.clear();

    if (this.formSimulation.get('Conditions').value.length > 0) {
      const filteredSections = sections.filter((section) => sectionIds.includes(section.id));
      const filteredConditions = this.formSimulation.get('Conditions').value;

      filteredSections.forEach((section) => {
        const group = this.formBuilder.group({});

        if (section.minimum_drained_depth !== undefined && filteredConditions.some((item) => item.value === 1)) {
          group.addControl('MinimumDrainedDepth', new FormControl(section.minimum_drained_depth, Validators.required));
        }
        if (section.minimum_undrained_depth !== undefined && filteredConditions.some((item) => item.value === 2)) {
          group.addControl('MinimumUndrainedDepth', new FormControl(section.minimum_undrained_depth, Validators.required));
        }
        if (section.minimum_pseudo_static_depth !== undefined && filteredConditions.some((item) => item.value === 3)) {
          group.addControl('MinimumPseudoStaticDepth', new FormControl(section.minimum_pseudo_static_depth, Validators.required));
        }
        group.addControl('MinimumSectionName', new FormControl({ value: section.name, disabled: true }));
        group.addControl('MinimumSectionId', new FormControl(section.id));

        if (section.beach_length !== undefined && section.beach_length !== null) {
          const beachLengthReferenceControl = new FormControl(1, Validators.required);
          const beachLengthControl = new FormControl(section.beach_length, Validators.required);

          group.addControl('BeachLengthReference', beachLengthReferenceControl);
          group.addControl('BeachLength', beachLengthControl);

          // Adiciona a lógica para habilitar/desabilitar BeachLength
          beachLengthReferenceControl.valueChanges.subscribe((value) => {
            if (value === 1) {
              beachLengthControl.enable();
              beachLengthControl.setValue(beachLengthControl.value || ''); // Garantir que o valor seja mantido ou definido
              beachLengthControl['placeholder'] = `Último: ${beachLengthControl.value || section.beach_length}`; // Placeholder atualizado
            } else {
              beachLengthControl.disable();
              beachLengthControl['placeholder'] = `Último: ${beachLengthControl.value || section.beach_length}`; // Placeholder mantido ao desabilitar
            }
            beachLengthControl.updateValueAndValidity();
          });

          // Inicializa o estado dos campos com base no valor atual de BeachLengthReference
          if (beachLengthReferenceControl.value === 1) {
            beachLengthControl.enable();
            beachLengthControl.setValue(beachLengthControl.value || ''); // Garantir que o valor seja mantido ou definido
            beachLengthControl['placeholder'] = `Último: ${beachLengthControl.value || section.beach_length}`; // Placeholder atualizado
          } else {
            beachLengthControl.disable();
            beachLengthControl['placeholder'] = `Último: ${beachLengthControl.value || section.beach_length}`; // Placeholder mantido ao desabilitar
          }

          beachLengthControl.updateValueAndValidity();
        }

        const formatedReviews = this.getFormattedReviews(this.dados, section.id);
        this.sectionsReviews[section.id] = formatedReviews.data;
        this.sectionsReviewsLatest[section.id] = formatedReviews.latest;

        group.addControl('SectionReviewId', new FormControl([formatedReviews.selected]));
        const selectedReviewFull = formatedReviews.selected ? section.reviews.find((r) => r.id === formatedReviews.selected.id) : null;

        this.sectionConstructionStages[section.id] = selectedReviewFull?.construction_stages || [];

        group.addControl('ConstructionStageId', new FormControl(this.sectionConstructionStages[section.id][0]?.id ?? null));

        formArray.push(group);

        const currentValue = currentValues.find((value) => value.MinimumSectionId === section.id);
        if (currentValue) {
          group.patchValue(currentValue, { emitEvent: false });
        }

        this.monitorFormGroupControls(group);
      });

      formArray.updateValueAndValidity({ emitEvent: false });
    }

    this.ngxSpinnerService.hide();
  }

  /**
   * Atualiza a visibilidade dos campos do formulário com base nas condições selecionadas e na configuração freática/piezométrica,
   * ajustando a visibilidade e validação dos campos dinâmicos do formulário.
   */
  updateFieldVisibility(): void {
    const selectedConditions = this.formSimulation.get('Conditions').value || [];
    const selectedPhreaticPiezometric = this.formSimulation.get('PhreaticPiezometric').value;

    this.showFields = selectedConditions.length > 0 && selectedPhreaticPiezometric;

    const conditionsText = selectedConditions
      .map((condition: any) => {
        return this.conditions.find((item) => item.value === condition.value)?.label || '';
      })
      .join(', ');

    const phreaticPiezometricText = this.phreaticPiezometric.find((item) => item.value === selectedPhreaticPiezometric)?.label || '';

    this.titleCard = `Tipo de condição: ${conditionsText} - Freática/Piezométrica: ${phreaticPiezometricText}`;

    Object.keys(this.showFieldsItems).forEach((key) => {
      this.showFieldsItems[key] = false;
    });

    if (selectedPhreaticPiezometric !== 4) {
      // Combinar todos os campos de todas as configurações correspondentes
      const combinedFields = new Set<string>();

      const matchingPhreaticFields = this.phreaticPiezometricFields.find((field) => field.phreatic_piezometric === selectedPhreaticPiezometric);

      if (matchingPhreaticFields) {
        matchingPhreaticFields.fields.forEach((field) => combinedFields.add(field.form));
      }

      selectedConditions.forEach((condition: any) => {
        const matchingConditionFields = this.conditionFields.find((field) => field.condition === condition.value);
        if (matchingConditionFields) {
          matchingConditionFields.fields.forEach((field) => combinedFields.add(field.form));
        }
      });

      const dynamicFields = Array.from(combinedFields);
      const linimetricRulers = this.dados?.structure?.linimetric_rulers;
      const upstreamRuler = linimetricRulers.find((ruler) => ruler.position === 1);
      const downstreamRuler = linimetricRulers.find((ruler) => ruler.position === 2);

      dynamicFields.forEach((field) => {
        const control = this.formSimulation.get(field);
        if (control && control instanceof FormControl) {
          if (field.startsWith('Upstream')) {
            this.updateFieldState(field, upstreamRuler, control);
          } else if (field.startsWith('Downstream')) {
            this.updateFieldState(field, downstreamRuler, control);
          } else {
            control.setValidators(Validators.required);
            this.showFieldsItems[field] = true;
            control.updateValueAndValidity();
          }
        }
      });

      const periodControl = this.formSimulation.get('Period');
      const periodValue = parseInt(periodControl.value, 10);
      if (periodValue >= 2 && periodValue <= 6) {
        if (dynamicFields.includes('AfterDate')) {
          this.showFieldsItems.AfterDate = true;
        }
        if (dynamicFields.includes('BeforeDate')) {
          this.showFieldsItems.BeforeDate = true;
        }
      } else if (periodValue === 1 || periodValue === 0) {
        if (dynamicFields.includes('AfterDate')) {
          this.showFieldsItems.AfterDate = true;
        }
        if (dynamicFields.includes('BeforeDate')) {
          this.showFieldsItems.BeforeDate = true;
        }
      } else {
        this.showFieldsItems.AfterDate = false;
        this.showFieldsItems.BeforeDate = false;
      }
    } else {
      this.formSimulation.get('UpstreamWaterLevelReference').setValue(null);
      this.formSimulation.get('DownstreamWaterLevelReference').setValue(null);
    }
  }

  /**
   * Gerencia o estado dos campos de nível d'água (habilitado/desabilitado) de acordo com a configuração de referência selecionada.
   *
   * @param {string} referenceControlName - Nome do controle de referência de nível d'água.
   * @param {string} waterLevelControlName - Nome do controle de nível d'água a ser atualizado.
   */
  updateWaterLevelFields(referenceControlName: string, waterLevelControlName: string) {
    const referenceControl = this.formSimulation.get(referenceControlName);
    const waterLevelControl = this.formSimulation.get(waterLevelControlName);

    if (referenceControl && waterLevelControl) {
      if (referenceControl.value === 1) {
        waterLevelControl.enable();
        waterLevelControl.setValidators([Validators.required]);
        waterLevelControl.setValue(waterLevelControl.value || ''); // Pode definir um valor padrão ou manter o valor atual
      } else {
        waterLevelControl.disable();
        waterLevelControl.clearValidators();
        waterLevelControl.setValue(''); // Limpa o valor ao desabilitar
      }
      waterLevelControl.updateValueAndValidity();
    }
  }

  /**
   * Atualiza o estado de um campo específico, aplicando validações e definindo placeholders com base na presença de dados no `ruler`.
   *
   * @param {string} field - Nome do campo a ser atualizado.
   * @param {any} ruler - Dados da régua linimétrica para aplicação de valores.
   * @param {FormControl} control - Controle de formulário a ser atualizado.
   */
  updateFieldState(field: string, ruler: any, control: FormControl): void {
    if (ruler) {
      if (!control.value && !field.endsWith('Reference')) {
        control.setValue(ruler.quota);
      }
      control.setValidators(Validators.required);
      control.enable();

      // Atribuir valor ao placeholder
      control['placeholder'] = control.value ? `Último: ${control.value}` : `Último: ${ruler.quota}`;
    } else {
      //if (!field.startsWith('Upstream') && !field.startsWith('Downstream')) {
      control.clearValidators();
      control.disable();
      //}

      // Limpar o placeholder ao desabilitar
      control['placeholder'] = '';
    }
    this.showFieldsItems[field] = true;
    control.updateValueAndValidity();
  }

  /**
   * Retorna o `FormArray` contendo os instrumentos de uma seção específica no formulário.
   *
   * @param {AbstractControl} section - Controle de seção do formulário.
   * @returns {FormArray} - Array de instrumentos associados à seção.
   */
  getInstruments(section: AbstractControl): FormArray {
    return section.get('SectionFormItemsInstruments') as FormArray;
  }

  /**
   * Preenche as leituras mais recentes para cada instrumento dentro de uma seção específica no formulário,
   * configurando o campo `dry` e os valores de `quota`.
   *
   * @param {number} sectionIndex - Índice da seção dentro do `FormArray` de seções.
   */
  fillLatestReadings(sectionIndex: number) {
    const section = this.sectionFormItems.at(sectionIndex) as FormGroup;
    const instrumentsFormArray = section.get('SectionFormItemsInstruments') as FormArray;

    instrumentsFormArray.controls.forEach((instrumentControl, instrumentIndex) => {
      const instrumentData = this.dados.sections.find((sec) => sec.id === section.get('SectionId').value).instruments[instrumentIndex];

      if (instrumentData.dry) {
        instrumentControl.get('dry').setValue(true);
        instrumentControl.get('quota').setValue('');
        instrumentControl.get('quota').disable();
      } else {
        instrumentControl.get('dry').setValue(false);
        instrumentControl.get('quota').setValue(instrumentData.quota);
        instrumentControl.get('quota').enable();
      }
    });
  }

  /**
   * Constrói o formulário de seções com base nas seções selecionadas e nos filtros aplicados,
   * criando grupos dinâmicos para cada seção e seus respectivos instrumentos.
   *
   * @param {string} [sectionId=''] - Identificador da seção (opcional) para filtragem específica.
   */
  buildSectionsForm(sectionId: string = '') {
    const selectedSections = this.formSimulation.get('SectionId').value;
    // Chamar buildSectionsForm se a condição for atendida
    if (selectedSections.length > 0 && this.showFields) {
      const formSections = this.sectionFormItems;
      const currentSections = formSections.value;

      // Limpar o formArray antes de adicionar novos grupos
      formSections.clear();

      const filteredSections = this.dados.sections.filter((section) => selectedSections.some((sel) => sel.id === section.id));

      filteredSections.forEach((section) => {
        const existingSection = currentSections.find((s) => s.SectionId === section.id);
        const sectionGroup = this.createSectionGroup(section, existingSection, sectionId);

        formSections.push(sectionGroup);
      });
    } else {
      this.clearSectionsForm();
    }
  }

  /**
   * Cria um `FormGroup` para uma seção específica, adicionando os instrumentos e configurando os controles de quota e estado.
   *
   * @param {any} section - Dados da seção a ser adicionada ao formulário.
   * @param {any} existingSection - Dados da seção existente no formulário.
   * @param {string} sectionId - Identificador da seção.
   * @returns {FormGroup} - Grupo de controles da seção.
   */
  createSectionGroup(section, existingSection, sectionId: string): FormGroup {
    const sectionGroup = this.formBuilder.group({
      SectionId: [section.id],
      SectionName: [section.name],
      SectionFormItemsInstruments: this.formBuilder.array([]),
      SectionReviewId: [section.id]
    });

    const instrumentsFormArray = sectionGroup.get('SectionFormItemsInstruments') as FormArray;

    section.instruments.forEach((instrument) => {
      const IgnoreDamagedInstruments = this.formSimulation.get('IgnoreDamagedInstruments').value;
      if ([1, 2, 3].includes(instrument.type) && ((IgnoreDamagedInstruments && instrument.online) || !IgnoreDamagedInstruments)) {
        const existingInstrument = existingSection?.SectionFormItemsInstruments.find((inst) => inst.instrument_id === instrument.instrument_id);
        const instrumentGroup = this.createInstrumentGroup(instrument, existingInstrument, sectionId);

        this.setQuotaFieldState(instrumentGroup);

        instrumentGroup.get('dry').valueChanges.subscribe(() => {
          this.setQuotaFieldState(instrumentGroup);
        });

        instrumentsFormArray.push(instrumentGroup);
      } else {
        this.messageNotInstruments[section.id] = 'Não há instrumentos do tipo INA, PZ e PZE associados à essa seção.';
      }
    });

    return sectionGroup;
  }

  /**
   * Cria um `FormGroup` para um instrumento específico dentro de uma seção, configurando o estado inicial do campo `dry` e a quota.
   *
   * @param {any} instrument - Dados do instrumento a ser adicionado ao formulário.
   * @param {any} existingInstrument - Dados do instrumento existente no formulário.
   * @param {string} sectionId - Identificador da seção.
   * @returns {FormGroup} - Grupo de controles do instrumento.
   */
  createInstrumentGroup(instrument, existingInstrument, sectionId: string): FormGroup {
    const instrumentGroup = this.formBuilder.group({
      dry: [false], // Sempre inicializando como false
      instrument_id: [instrument.instrument_id],
      measurement_id: [instrument.measurement_id],
      instrument_identifier: [instrument.instrument_identifier],
      quota: ['', Validators.required] // Sempre inicializando como vazio e requerido
    });

    // Habilita o campo de quota se o instrumento não estiver marcado como seco
    this.setQuotaFieldState(instrumentGroup);

    // Monitorando mudanças no campo `dry`
    instrumentGroup.get('dry').valueChanges.subscribe((isDry) => {
      if (isDry) {
        instrumentGroup.get('quota').setValue(''); // Limpa o valor de quota
        instrumentGroup.get('quota').disable(); // Desabilita o campo de quota
      } else {
        instrumentGroup.get('quota').enable(); // Habilita o campo de quota
      }
    });

    return instrumentGroup;
  }

  /**
   * Atualiza o estado do campo `quota` em função do valor do campo `dry`, desabilitando ou habilitando-o conforme necessário.
   *
   * @param {FormGroup} instrumentGroup - Grupo de controles do instrumento.
   */
  setQuotaFieldState(instrumentGroup: FormGroup): void {
    const dryControl = instrumentGroup.get('dry');
    const quotaControl = instrumentGroup.get('quota');

    if (dryControl.value) {
      quotaControl.setValue('');
      quotaControl.disable();
    } else {
      quotaControl.enable();

      // Marcar o campo como "touched" se ele for tocado e não preenchido
      if (quotaControl.value === '' && quotaControl.touched) {
        quotaControl.markAsTouched();
      }
    }

    quotaControl.updateValueAndValidity();
  }

  /**
   * Atualiza o formulário ao alterar o valor do campo `IgnoreDamagedInstruments`, reconstruindo as seções e aplicando os valores anteriores.
   */
  updateFormOnIgnoreDamagedInstrumentsChange(): void {
    const formValues = this.formSimulationSections.value;
    this.buildSectionsForm();
    this.formSimulationSections.patchValue(formValues);
  }

  /**
   * Limpa o FormArray de seções, removendo todos os grupos e resetando o formulário de seções.
   */
  clearSectionsForm() {
    if (this.sectionFormItems) {
      this.sectionFormItems.clear();
    }
  }

  /**
   * Calcula uma nova data baseada na data inicial e no valor do período, ajustando a data para um período relativo específico.
   *
   * @param {Date} dateParam - Data de referência inicial.
   * @param {number} periodValue - Valor do período utilizado para calcular a nova data.
   * @returns {Date} - Nova data calculada conforme o período especificado.
   */
  calculateNewDate(dateParam: Date, periodValue: number): Date {
    let newDate = new Date(dateParam);
    switch (periodValue) {
      case 2:
        newDate = moment(dateParam).subtract(6, 'months').toDate();
        break;
      case 3:
        newDate = moment(dateParam).subtract(1, 'years').toDate();
        break;
      case 4:
        newDate = moment(dateParam).subtract(2, 'years').toDate();
        break;
      case 5:
        newDate = moment(dateParam).subtract(3, 'years').toDate();
        break;
      case 6:
        newDate = moment(dateParam).subtract(5, 'years').toDate();
        break;
    }
    return newDate;
  }

  /**
   * Método executado ao destruir o componente.
   * Desfaz todas as assinaturas de observáveis para evitar vazamentos de memória.
   */
  ngOnDestroy() {
    this.formSubscriptions.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Obtém a lista de seções com base na estrutura selecionada.
   * @param {any} structure - A estrutura selecionada.
   * @param {string} [action='select'] - A ação realizada (seleção ou desseleção).
   */
  getSections(structure, action: string = 'select') {
    if (action === 'select') {
      this.sectionsServiceApi.getSectionList({ structureId: structure.id }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.sections = dados;
      });
    } else {
      this.sections = [];
      this.dados = [];
    }
  }

  /**
   * Reseta todos os formulários e estados associados no componente, limpando valores, mensagens de erro,
   * e redefinindo os estados de visibilidade e de mensagens.
   */
  resetForms(options: boolean = true): void {
    this.messagesError = null;

    this.formSimulation.reset({
      Name: '',
      SectionId: [],
      SurfaceTypeCircular: false,
      SurfaceTypeNonCircular: false,
      CalculationMethodsCircular: '',
      SearchMethodCircular: '',
      CalculationMethodsNonCircular: '',
      SearchMethodNonCircular: '',
      Conditions: '',
      PhreaticPiezometric: '',
      SafetyFactorTarget: '',
      SafetyFactorTargetCheck: false,
      ReferenceReadings: '',
      Period: 2,
      PhreaticVariation: '',
      AfterDate: '',
      BeforeDate: '',
      //ReadingStatisticalMeasure: '',
      UpstreamWaterLevelReference: 1,
      UpstreamWaterLevel: '',
      DownstreamWaterLevelReference: 1,
      DownstreamWaterLevel: '',
      SeismicCoefficientHorizontal: '',
      SeismicCoefficientVertical: '',
      IgnoreDamagedInstruments: false
    });

    this.formSimulationSections.reset({
      SectionFormItems: []
    });

    (this.formSimulation.get('SectionForm') as FormArray).clear();
    (this.formSimulationSections.get('SectionFormItems') as FormArray).clear();

    this.showFields = false;
    this.titleCard = '';
    Object.keys(this.showFieldsItems).forEach((key) => {
      this.showFieldsItems[key] = false;
    });

    if (options) {
      this.sections = [];
      this.dados = null;
      this.messageNotInstruments = [];
      this.ctrlSism = false;
    }

    this.message.text = '';
    this.message.status = false;
    this.message.class = 'alert-success';

    this.calculatePeriod(2);
  }

  /**
   * Valida os formulários `formSimulation` e `formSimulationSections`, aplicando as regras de validação personalizadas
   * e chamando os métodos necessários para registro da simulação, se os formulários estiverem válidos.
   */
  validateForms() {
    this.markFormGroupTouched(this.formSimulation);
    this.markFormGroupTouched(this.formSimulationSections);
    let isValid = true;
    // Validar formSimulation
    if (!this.validateFormSimulation()) {
      isValid = false;
    }
    // Validar formSimulationSections somente se PhreaticPiezometric for igual a 1
    if (this.formSimulation.get('PhreaticPiezometric').value === 1) {
      if (!this.validateFormSimulationSections()) {
        isValid = false;
      }
    }
    // Atualizar o estado de validação dos formulários
    this.formSimulation.updateValueAndValidity();
    this.formSimulationSections.updateValueAndValidity();
    // Gerar objeto de depuração e imprimir no console
    const debugObject = this.generateDebugObject();

    if (isValid) {
      const params = this.simulationRequest();
      this.registerSimulation(params);
    }
  }

  /**
   * Marca todos os controles dentro de um `FormGroup` ou `FormArray` como `touched` e atualiza seus estados de validade.
   *
   * @param {FormGroup | FormArray} formGroup - Grupo ou array de formulário a ser marcado como `touched`.
   */
  markFormGroupTouched(formGroup: FormGroup | FormArray) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      if (control instanceof FormControl) {
        control.markAsTouched();
        control.updateValueAndValidity();
      } else if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      }
    });
  }

  /**
   * Realiza validações específicas para o formulário `formSimulation`, incluindo a verificação de campos obrigatórios
   * e a validação de pelo menos um tipo de superfície circular ou não circular.
   *
   * @returns {boolean} - Retorna `true` se o formulário estiver válido, caso contrário, `false`.
   */
  validateFormSimulation(): boolean {
    let isValid = true;

    // Validações individuais de campos
    isValid = this.validateFieldArray('SectionId') && isValid;
    isValid = this.validateBooleanField('SurfaceTypeCircular', ['CalculationMethodsCircular', 'SearchMethodCircular']) && isValid;
    isValid = this.validateBooleanField('SurfaceTypeNonCircular', ['CalculationMethodsNonCircular', 'SearchMethodNonCircular']) && isValid;
    isValid = this.validateFieldArray('Conditions') && isValid;
    isValid = this.validateField('PhreaticPiezometric') && isValid;
    isValid = this.validateConditionalField('SafetyFactorTargetCheck', 'SafetyFactorTarget') && isValid;
    isValid = this.validateDynamicFields() && isValid;

    // Validar ao menos um de SurfaceTypeCircular ou SurfaceTypeNonCircular ser true
    const surfaceTypeCircular = this.formSimulation.get('SurfaceTypeCircular').value;
    const surfaceTypeNonCircular = this.formSimulation.get('SurfaceTypeNonCircular').value;
    if (!surfaceTypeCircular && !surfaceTypeNonCircular) {
      this.formSimulation.get('SurfaceTypeCircular').setErrors({ required: true });
      this.formSimulation.get('SurfaceTypeNonCircular').setErrors({ required: true });
      isValid = false;
    }

    return isValid;
  }

  /**
   * Valida as seções do formulário `formSimulationSections`, garantindo que os instrumentos e campos associados
   * cumpram as regras de validação definidas.
   *
   * @returns {boolean} - Retorna `true` se todas as seções estiverem válidas, caso contrário, `false`.
   */
  validateFormSimulationSections(): boolean {
    let isValid = true;

    const sectionFormItems = this.formSimulationSections.get('SectionFormItems') as FormArray;
    sectionFormItems.controls.forEach((sectionGroup: AbstractControl) => {
      if (sectionGroup instanceof FormGroup) {
        const instrumentsArray = sectionGroup.get('SectionFormItemsInstruments') as FormArray;

        if (instrumentsArray) {
          instrumentsArray.controls.forEach((instrumentGroup: AbstractControl) => {
            if (instrumentGroup instanceof FormGroup) {
              const quotaControl = instrumentGroup.get('quota');
              const dryControl = instrumentGroup.get('dry');

              if (quotaControl && dryControl) {
                const quotaValue = quotaControl.value;

                // Validação específica: se `dry` for falso, `quota` deve ser obrigatório
                if (
                  !dryControl.value &&
                  (quotaValue === null || quotaValue === undefined || quotaValue === '' || (typeof quotaValue === 'string' && quotaValue.trim() === ''))
                ) {
                  quotaControl.setErrors({ required: true });
                  isValid = false;
                }
              }

              // Verificar outros campos dentro de `instrumentGroup` se necessário
              Object.keys(instrumentGroup.controls).forEach((key) => {
                const control = instrumentGroup.get(key);
                if (control && control.invalid && control.touched && !control.disabled) {
                  isValid = false;
                }
              });
            }
          });
        }
      }
    });

    return isValid;
  }

  /**
   * Valida o campo especificado no formulário `formSimulation`, aplicando uma regra de valor obrigatório.
   *
   * @param {string} fieldName - Nome do campo a ser validado.
   * @returns {boolean} - Retorna `true` se o campo estiver válido, caso contrário, `false`.
   */
  validateField(fieldName: string): boolean {
    const control = this.formSimulation.get(fieldName);
    if (control.value === '') {
      control.setErrors({ required: true });
      return false;
    }
    return true;
  }

  /**
   * Valida campos de array no formulário `formSimulation`, garantindo que pelo menos um item esteja selecionado.
   *
   * @param {string} fieldName - Nome do campo de array a ser validado.
   * @returns {boolean} - Retorna `true` se o campo estiver válido, caso contrário, `false`.
   */
  validateFieldArray(fieldName: string): boolean {
    const control = this.formSimulation.get(fieldName);
    if (control.value.length === 0) {
      control.setErrors({ required: true });
      return false;
    }
    return true;
  }

  /**
   * Valida campos booleanos no formulário `formSimulation` e aplica regras específicas nos campos relacionados.
   *
   * @param {string} booleanField - Nome do campo booleano a ser verificado.
   * @param {string[]} relatedFields - Campos relacionados que precisam de validação adicional.
   * @returns {boolean} - Retorna `true` se o campo e os campos relacionados estiverem válidos, caso contrário, `false`.
   */
  validateBooleanField(booleanField: string, relatedFields: string[]): boolean {
    let isValid = true;
    const control = this.formSimulation.get(booleanField);
    if (control.value) {
      relatedFields.forEach((field) => {
        isValid = this.validateFieldArray(field) && isValid;
      });
    }
    return isValid;
  }

  /**
   * Valida um campo condicional, assegurando que o campo alvo seja obrigatório caso o campo de condição seja verdadeiro.
   *
   * @param {string} conditionField - Nome do campo de condição.
   * @param {string} targetField - Nome do campo alvo.
   * @returns {boolean} - Retorna `true` se o campo condicional estiver válido, caso contrário, `false`.
   */
  validateConditionalField(conditionField: string, targetField: string): boolean {
    const control = this.formSimulation.get(conditionField);
    if (control.value && this.formSimulation.get(targetField).value === '') {
      this.formSimulation.get(targetField).setErrors({ required: true });
      return false;
    }
    return true;
  }

  /**
   * Valida campos dinâmicos visíveis no formulário `formSimulation`, aplicando regras de valor obrigatório
   * conforme os campos são ativados/desativados.
   *
   * @returns {boolean} - Retorna `true` se todos os campos dinâmicos visíveis estiverem válidos, caso contrário, `false`.
   */
  validateDynamicFields(): boolean {
    let isValid = true;
    const dynamicFields = [
      'ReferenceReadings',
      'PhreaticVariation',
      'AfterDate',
      'BeforeDate',
      'ReadingStatisticalMeasure',
      'UpstreamWaterLevelReference',
      'UpstreamWater',
      'DownstreamWaterLevelReference',
      'DownstreamWaterLevel'
    ];

    dynamicFields.forEach((field) => {
      const control = this.formSimulation.get(field);
      if (control) {
        // Verificar se o controle não é nulo
        if (this.showFieldsItems[field] && control.enabled) {
          if (!control.value) {
            control.setErrors({ required: true });
            isValid = false;
          } else {
            control.setErrors(null);
          }
        } else if (control.disabled) {
          control.setErrors(null);
          control.markAsUntouched();
        }
      }
    });

    return isValid;
  }

  // validateGroupFields(group: FormGroup): boolean {
  //   let isValid = true;
  //   Object.keys(group.controls).forEach((key) => {
  //     const control = group.get(key);
  //     if (control) {
  //       if (control.enabled && !control.value) {
  //         control.setErrors({ required: true });
  //         isValid = false;
  //       } else {
  //         control.setErrors(null);
  //         control.markAsUntouched();
  //       }
  //     }
  //   });
  //   return isValid;
  // }

  /**
   * Valida os instrumentos em uma seção específica, assegurando que o campo `quota` seja preenchido quando necessário.
   *
   * @param {FormGroup} sectionGroup - Grupo de formulário representando a seção.
   * @returns {boolean} - Retorna `true` se todos os instrumentos estiverem válidos, caso contrário, `false`.
   */
  validateInstruments(sectionGroup: FormGroup): boolean {
    let isValid = true;
    const instrumentsFormArray = sectionGroup.get('SectionFormItemsInstruments') as FormArray;
    instrumentsFormArray.controls.forEach((instrumentControl: AbstractControl) => {
      if (instrumentControl instanceof FormGroup) {
        const dry = instrumentControl.get('dry')?.value;
        const quotaControl = instrumentControl.get('quota');
        const quotaValue = quotaControl?.value;

        if (!dry && quotaControl && quotaControl.enabled && (!quotaValue || (typeof quotaValue === 'string' && quotaValue.trim() === ''))) {
          quotaControl.setErrors({ required: true });
          isValid = false;
        } else if (quotaControl) {
          quotaControl.setErrors(null);
          quotaControl.markAsUntouched();
        }
      }
    });
    return isValid;
  }

  /**
   * Obtém os controles visíveis e habilitados de um `FormGroup` ou `FormArray`, estruturando os valores
   * de forma hierárquica e convertendo os nomes dos controles para `snake_case`.
   *
   * @param {FormGroup | FormArray} formGroup - Grupo ou array de formulário a ser estruturado.
   * @returns {any} - Estrutura hierárquica dos valores dos controles visíveis.
   */
  getVisibleFormControlsStructured(formGroup: FormGroup | FormArray): any {
    const result: any = {};

    if (formGroup instanceof FormArray) {
      return formGroup.controls
        .filter((control: AbstractControl) => control.enabled)
        .map((control: AbstractControl) => this.getVisibleFormControlsStructured(control as FormGroup));
    }

    if (formGroup instanceof FormGroup) {
      Object.keys(formGroup.controls).forEach((controlName) => {
        const control = formGroup.get(controlName);

        if (control && control.enabled) {
          const snakeCaseName = this.toSnakeCase(controlName);

          if (control instanceof FormArray) {
            result[snakeCaseName] = control.controls
              .filter((arrayControl: AbstractControl) => arrayControl.enabled)
              .map((arrayControl: AbstractControl) => this.getVisibleFormControlsStructured(arrayControl as FormGroup));
          } else if (control instanceof FormGroup) {
            result[snakeCaseName] = this.getVisibleFormControlsStructured(control);
          } else if (control instanceof FormControl) {
            result[snakeCaseName] = control.value;
          }
        }
      });
    }

    return result;
  }

  /**
   * Converte uma string para o formato `snake_case`.
   *
   * @param {string} str - String a ser convertida.
   * @returns {string} - String convertida para o formato `snake_case`.
   */
  toSnakeCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();
  }

  /**
   * Prepara os dados para envio de uma requisição de simulação, reunindo informações do formulário e configurando
   * as propriedades necessárias para a simulação.
   *
   * @returns {any} - Objeto de parâmetros para envio na requisição de simulação.
   */
  simulationRequest() {
    const formSimulationSectionsValues = this.getVisibleFormControlsStructured(this.formSimulationSections);
    const formSimulationValues = this.getVisibleFormControlsStructured(this.formSimulation);
    const circularSearchValues = this.ModalSearchMethodCircular.formSearchMethod.value;
    const nonCircularSearchValues = this.ModalSearchMethodNonCircular.formSearchMethod.value;

    const slide2Configuration = {
      circular_parameters: formSimulationValues.surface_type_circular
        ? {
            calculation_methods: formSimulationValues.calculation_methods_circular.map((method) => parseInt(method.id)),
            circular_search_method: Number(formSimulationValues.search_method_circular) || null,
            divisions_along_slope: circularSearchValues.circular_divisions_along_slope || 0,
            circles_per_division: circularSearchValues.circular_circles_per_division || 0,
            number_of_iterations: circularSearchValues.circular_number_of_iterations || 0,
            divisions_next_iteration: circularSearchValues.circular_divisions_next_iteration || 0,
            radius_increment: circularSearchValues.circular_radius_increment || 0,
            number_of_surfaces: circularSearchValues.circular_number_of_surfaces || 0
          }
        : null,

      non_circular_parameters: formSimulationValues.surface_type_non_circular
        ? {
            calculation_methods: formSimulationValues.calculation_methods_non_circular.map((method) => parseInt(method.id)),
            non_circular_search_method: Number(formSimulationValues.search_method_non_circular) || null,
            divisions_along_slope: nonCircularSearchValues.non_circular_divisions_along_slope || 0,
            surfaces_per_division: nonCircularSearchValues.non_circular_surfaces_per_division || 0,
            number_of_iterations: nonCircularSearchValues.non_circular_number_of_iterations || 0,
            divisions_next_iteration: nonCircularSearchValues.non_circular_divisions_next_iteration || 0,
            number_of_vertices_along_surface: nonCircularSearchValues.non_circular_number_of_vertices_along_surface || 0,
            number_of_surfaces: nonCircularSearchValues.non_circular_number_of_surfaces || 0,
            number_of_nests: nonCircularSearchValues.non_circular_number_of_nests || 0,
            maximum_iterations: nonCircularSearchValues.non_circular_maximum_iterations || 0,
            initial_number_of_surface_vertices: nonCircularSearchValues.non_circular_initial_number_of_surface_vertices || 0,
            initial_number_of_iterations: nonCircularSearchValues.non_circular_initial_number_of_iterations || 0,
            maximum_number_of_steps: nonCircularSearchValues.non_circular_maximum_number_of_steps || 0,
            number_of_factors_safety_compared_before_stopping: nonCircularSearchValues.non_circular_number_of_factors_safety_compared_before_stopping || 0,
            tolerance_for_stopping_criterion: nonCircularSearchValues.non_circular_tolerance_for_stopping_criterion || 0,
            number_of_particles: nonCircularSearchValues.non_circular_number_of_particles || 0
          }
        : null
    };

    const requestPayload = {
      name: formSimulationValues.name || '',
      sections: formSimulationSectionsValues.section_form_items.map((section) => {
        // Busca o valor correspondente em formSimulationValues.section_form usando section_id
        const correspondingFormValue = formSimulationValues.section_form.find((formValueSection) => formValueSection.minimum_section_id === section.section_id);

        // Validação opcional: se houver etapas disponíveis e não tiver selecionado, lança erro.
        if (this.sectionsMap[section.section_id]?.construction_stages?.length > 0 && !correspondingFormValue?.construction_stage_id) {
          this.messagesError = this.messagesError || [];
          this.messagesError.push({
            message: `A etapa de obra é obrigatória para a seção ${this.sectionsMap[section.section_id].name}.`
          });
        }

        return {
          section_id: section.section_id,
          section_review_id: correspondingFormValue?.section_review_id[0]?.id || null,
          construction_stage_id: correspondingFormValue?.construction_stage_id || null,

          instruments:
            formSimulationValues.phreatic_piezometric === 1
              ? section.section_form_items_instruments.map((instrument) => ({
                  instrument_id: instrument.instrument_id,
                  measurement_id: instrument.measurement_id,
                  quota: instrument.quota || null,
                  dry: instrument.dry
                }))
              : [],
          minimum_drained_depth: correspondingFormValue?.minimum_drained_depth || null,
          minimum_undrained_depth: correspondingFormValue?.minimum_undrained_depth || null,
          minimum_pseudo_static_depth: correspondingFormValue?.minimum_pseudo_static_depth || null,
          beach_length_statistical_measure: correspondingFormValue?.beach_length_reference || null,
          beach_length: correspondingFormValue?.beach_length || null
        };
      }),

      slide2_configuration: slide2Configuration,
      should_evaluate_drained_condition: formSimulationValues.conditions.some((condition) => condition.value === 1),
      should_evaluate_undrained_condition: formSimulationValues.conditions.some((condition) => condition.value === 2),
      should_evaluate_pseudo_static_condition: formSimulationValues.conditions.some((condition) => condition.value === 3),
      safety_factor_target: formSimulationValues.safety_factor_target || null,
      seismic_coefficient: formSimulationValues.conditions.some((condition) => condition.value === 3)
        ? {
            horizontal: formSimulationValues.seismic_coefficient_horizontal || 0,
            vertical: formSimulationValues.seismic_coefficient_vertical || 0
          }
        : null,
      water_table_configuration: formSimulationValues.phreatic_piezometric || null,
      reading_statistical_measure: formSimulationValues.phreatic_piezometric == 1 ? 1 : formSimulationValues.reference_readings || null,
      water_table_variation: formSimulationValues.phreatic_variation || null,
      start_date: formSimulationValues.after_date || null,
      end_date: formSimulationValues.before_date || null,
      upstream_linimetric_ruler_statistical_measure: formSimulationValues.upstream_water_level_reference || null,
      upstream_linimetric_ruler_quota: formSimulationValues.upstream_water_level || null,
      downstream_linimetric_ruler_statistical_measure: formSimulationValues.downstream_water_level_reference || null,
      downstream_linimetric_ruler_quota: formSimulationValues.downstream_water_level || null,
      ignore_damaged_instruments: formSimulationValues.ignore_damaged_instruments || false
    };

    return requestPayload;
  }

  /**
   * Registra uma simulação enviando os parâmetros para a API e exibe uma mensagem de sucesso ou erro,
   * gerenciando o estado do `ngxSpinnerService` e o redirecionamento após a conclusão.
   *
   * @param {any} params - Parâmetros a serem enviados para o registro da simulação.
   */
  registerSimulation(params) {
    this.ngxSpinnerService.show();

    this.messagesError = [];

    this.simulatorServiceApi.postSimulations(params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.SuccessSimulation;
        this.message.status = true;
        this.message.class = 'alert-success';
        this.ngxSpinnerService.hide();

        setTimeout(() => {
          this.message.text = '';
          this.message.status = false;
          this.message.class = 'alert-success';
          this.router.navigate(['/stability/simulations']);
        }, 5000);
      },
      (error) => {
        console.log(error);
        this.ngxSpinnerService.hide();
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
        }
      }
    );
  }

  /**
   * Gera um array de objetos de depuração para verificação dos controles do formulário,
   * exibindo o estado de validade e habilitação de cada controle.
   *
   * @returns {any[]} - Array de objetos de depuração representando o estado dos controles.
   */
  generateDebugObject(): any[] {
    const debugArray = [];
    this.constructDebugObject(this.formSimulation, 'formSimulation', debugArray);
    this.constructDebugObject(this.formSimulationSections, 'formSimulationSections', debugArray);
    return debugArray;
  }

  /**
   * Constrói o objeto de depuração de maneira recursiva para `FormGroup` ou `FormArray`,
   * adicionando o nome do grupo pai e o estado de validade de cada controle.
   *
   * @param {FormGroup | FormArray} formGroup - Grupo ou array de formulário a ser processado.
   * @param {string} parentName - Nome do grupo pai para exibir no objeto de depuração.
   * @param {any[]} debugArray - Array que armazena os objetos de depuração.
   */
  constructDebugObject(formGroup: FormGroup | FormArray, parentName: string, debugArray: any[]) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      if (control instanceof FormControl) {
        debugArray.push({
          formGroup: parentName,
          formControl: key,
          isValid: control.enabled ? control.valid : true // Considerar controles válidos se estiverem desabilitados
        });
      } else if (control instanceof FormGroup) {
        this.constructDebugObject(control, `${parentName}.${key}`, debugArray);
      } else if (control instanceof FormArray) {
        control.controls.forEach((group, index) => {
          this.constructDebugObject(group as FormGroup, `${parentName}.${key}[${index}]`, debugArray);
        });
      }
    });
  }

  /**
   * Extrai IDs e nomes das seções válidas de dados de simulação.
   *
   * Este método percorre os dados fornecidos e extrai uma lista de objetos contendo
   * `id` e `name` para cada seção que atende a dois critérios:
   * 1. A última revisão possui um arquivo DXF (`last_review_has_dxf` é true).
   * 2. A instrumentação da seção é válida (`is_instrumentation_valid` é true).
   *
   * @param simulationData - Dados da simulação contendo seções.
   * @returns Uma lista de objetos com `id` e `name` das seções válidas.
   */
  // extractSectionsIdAndName(simulationData: any) {
  //   this.messagesError = []; // Inicializa a lista de mensagens de erro

  //   if (!simulationData || !simulationData.sections) {
  //     return [];
  //   }

  //   const sections = simulationData.sections;

  //   // 1 - Verificar se todas as seções têm `is_instrumentation_valid` como `false`
  //   const allInstrumentationInvalid = sections.every((section) => !section.is_instrumentation_valid);
  //   if (allInstrumentationInvalid) {
  //     this.messageNoInstrument = {
  //       text: MessagePadroes.NoInstrumentationValid,
  //       status: true,
  //       class: 'alert-warning'
  //     };
  //     setTimeout(() => {
  //       this.messageNoInstrument.status = false;
  //     }, 5000);
  //   }

  //   // 2 - Verificar se todas as revisões de todas as seções têm `has_dxf` como `false`
  //   const allReviewsNoDXF = sections.every((section) => (section.reviews ? section.reviews.every((review) => !review.has_dxf) : true));
  //   if (allReviewsNoDXF) {
  //     this.messageNoDxf = {
  //       text: MessagePadroes.NoDxfReviewSection,
  //       status: true,
  //       class: 'alert-warning'
  //     };
  //     setTimeout(() => {
  //       this.messageNoDxf.status = false;
  //     }, 5000);
  //   }

  //   // 3 - Filtrar seções que têm `is_instrumentation_valid` como `true`
  //   // e pelo menos uma revisão com `has_dxf` como `true`
  //   const validSections = sections
  //     .filter((section: any) => {
  //       const hasValidInstrumentation = section.is_instrumentation_valid === true;
  //       const hasDXF = section.reviews && section.reviews.some((review: any) => review.has_dxf === true);
  //       return hasValidInstrumentation && hasDXF;
  //     })
  //     .map((section: any) => ({
  //       id: section.id,
  //       name: section.name
  //     }));

  //   return validSections;
  // }

  extractSectionsIdAndName(simulationData: any) {
    this.messagesError = []; // Limpa mensagens

    if (!simulationData || !simulationData.sections) {
      return [];
    }

    const sections = simulationData.sections;

    // Agora retorna TODAS as seções disponíveis, sem bloquear nenhuma
    return sections.map((section: any) => ({
      id: section.id,
      name: section.name
    }));
  }

  /**
   * Obtém uma lista formatada de revisões de uma seção específica, selecionando a revisão mais recente
   * e a primeira com `has_dxf: true`, além de formatar as datas de cada revisão.
   *
   * - Encontra a seção correspondente ao `sectionId` fornecido e, caso existam revisões, processa a lista.
   * - Define `latestReview` como a revisão mais recente baseada na `start_date`.
   * - Define `selectedReview` como a primeira revisão encontrada com `has_dxf: true`.
   * - Retorna as revisões formatadas, a revisão selecionada e a revisão mais recente.
   *
   * @param {any} data - Os dados contendo as seções e suas revisões.
   * @param {string} sectionId - O ID da seção para a qual as revisões devem ser obtidas.
   * @returns {Object} - Um objeto contendo:
   *  - `data`: Array de revisões formatadas com `has_dxf: true`.
   *  - `selected`: A primeira revisão com `has_dxf: true`.
   *  - `latest`: A revisão mais recente com base na `start_date`.
   */
  getFormattedReviews(data: any, sectionId: string): any {
    let selectedReview = null;
    let latestReview = null;
    let selectedReviewStages = [];

    const section = data.sections.find((section: any) => section.id === sectionId);

    if (!section || !section.reviews?.length) {
      return {
        data: [],
        selected: null,
        latest: null,
        stages: []
      };
    }

    // Identifica a revisão mais recente
    section.reviews.forEach((review: any) => {
      if (!latestReview || moment(review.start_date).isAfter(moment(latestReview.start_date))) {
        const formattedDate = moment(review.start_date).format('DD/MM/YYYY HH:mm:ss');
        latestReview = {
          id: review.id,
          name: `Revisão ${review.index} - ${formattedDate}`,
          start_date: review.start_date
        };
      }
    });

    const reviews = section.reviews
      .filter((review: any) => review.has_dxf || (review.is_under_construction && review.construction_stages?.length > 0))
      .map((review: any) => {
        const formattedDate = moment(review.start_date).format('DD/MM/YYYY HH:mm:ss');
        const formattedReview = {
          id: review.id,
          name: `Revisão ${review.index} - ${formattedDate}`
        };

        if (!selectedReview) {
          selectedReview = formattedReview;
          selectedReviewStages = review.construction_stages || [];
        }

        return formattedReview;
      });

    delete latestReview['start_date'];

    return {
      data: reviews,
      selected: selectedReview,
      latest: latestReview,
      stages: selectedReviewStages
    };
  }

  validatePhreaticConfiguration() {
    const sectionsId = this.formSimulation.get('SectionId')?.value;

    const phreaticConfig = +this.formSimulation.get('PhreaticPiezometric')?.value;
    let conditions = this.formSimulation.get('Conditions')?.value;
    if (Array.isArray(conditions)) {
      const conditionObj = conditions[0];
      conditions = conditionObj?.value ?? conditionObj;
    }
    conditions = typeof conditions === 'object' ? conditions?.value : conditions;
    this.disabledSimulation = false;

    sectionsId.forEach((sectionItem) => {
      const section = this.sectionsMap[sectionItem.id];
      const hasInstruments = section.has_instruments;
      const reviewId = this.formSimulation.get('SectionForm')?.value?.[0]?.SectionReviewId?.[0]?.id;
      const selectedReview = section.reviews.find((r) => r.id === reviewId);
      const dxfHasWaterline = selectedReview?.dxf_has_waterline ?? false;
      const result = this.checkSimulationValidity(hasInstruments, dxfHasWaterline, phreaticConfig);

      if (result.simulationAllowed === false) {
        this.disabledSimulation = true;
      }

      this.sectionsMap[sectionItem.id].message = result.message;
    });

    if (sectionsId?.length > 0 && phreaticConfig !== 0 && conditions === 1) {
      if (!this.disabledSimulation) {
        this.enableSimulation();
        this.updateFieldVisibility();
      }
    }
  }

  checkSimulationValidity(
    hasInstruments: boolean,
    dxfHasWaterline: boolean,
    phreaticConfig: number // onde 4 === 'manual'
  ): any {
    // Seção COM instrumentos
    if (hasInstruments) {
      if (phreaticConfig === 4 && !dxfHasWaterline) {
        return {
          simulationAllowed: false,
          message: {
            status: true,
            class: 'alert alert-danger',
            text: 'A opção "Manual" só é válida se o DXF da revisão selecionada contiver a linha freática.'
          }
        };
      }
      // Simulação permitida
      return {
        simulationAllowed: true,
        message: null
      };
    }

    // Seção SEM instrumentos
    if (!hasInstruments) {
      if (phreaticConfig === 4) {
        // Manual
        if (dxfHasWaterline) {
          return {
            simulationAllowed: true,
            message: null
          };
        } else {
          return {
            simulationAllowed: false,
            message: {
              status: true,
              class: 'alert alert-danger',
              text: 'Simulação não permitida: a seção não possui instrumentação e a revisão não possui linha freática.'
            }
          };
        }
      } else {
        // Configuração diferente de manual
        if (dxfHasWaterline) {
          return {
            simulationAllowed: false,
            message: {
              status: true,
              class: 'alert alert-danger',
              text: 'Simulação não permitida: a seção não possui instrumentação e a revisão possui linha freática.'
            }
          };
        } else {
          return {
            simulationAllowed: false,
            message: {
              status: true,
              class: 'alert alert-danger',
              text: 'Simulação não permitida: a seção não possui instrumentação e a revisão não possui linha freática.'
            }
          };
        }
      }
    }

    return {
      simulationAllowed: false,
      message: {
        status: true,
        class: 'alert alert-danger',
        text: 'Erro inesperado na validação da simulação. Verifique os dados da seção e da revisão.'
      }
    };
  }

  disableSimulation() {
    if (this.formSimulation.enabled) {
      this.formSimulation.disable();
    }
  }

  enableSimulation() {
    if (this.formSimulation.disabled) {
      this.formSimulation.enable();
    }
  }

  clearMessages() {
    this.message = { status: false, class: '', text: '' };
    this.messageNoDxf = { status: false, class: '', text: '' };
    this.messageNoInstrument = { status: false, class: '', text: '' };
  }

  onReviewChange(index: number): void {
    const section = this.sectionForm.at(index);
    const sectionId = section.get('MinimumSectionId')?.value;
    const selectedReview = section.get('SectionReviewId')?.value?.[0];

    if (!sectionId || !selectedReview?.id) {
      this.sectionConstructionStages[index] = [];
      return;
    }

    const sectionObj = this.sections.find((s) => s.id === sectionId);
    const reviewObj = sectionObj?.reviews?.find((r) => r.id === selectedReview.id);

    this.sectionConstructionStages[index] = reviewObj?.construction_stages || [];
  }
}
