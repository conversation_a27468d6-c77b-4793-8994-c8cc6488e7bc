import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ReportsRoutingModule } from './reports-routing.module';
import { SharedModule } from '@components/shared.module';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgSelectModule } from '@ng-select/ng-select';

import { ListReportsComponent } from './list-reports/list-reports.component';
import { SendReportComponent } from './send-report/send-report.component';
import { CreateNewScheduleComponent } from './create-new-schedule/create-new-schedule.component';

@NgModule({
  declarations: [ListReportsComponent, SendReportComponent, CreateNewScheduleComponent],
  imports: [
    CommonModule,
    FormsModule,
    NgbModule,
    ReactiveFormsModule,
    SharedModule,
    NgMultiSelectDropDownModule.forRoot(),
    NgSelectModule,
    ReportsRoutingModule
  ]
})
export class ReportsModule {}
