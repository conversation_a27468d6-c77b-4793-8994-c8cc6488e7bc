import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';

import { fieldsReading } from 'src/app/constants/readings.constants';

import fn from 'src/app/utils/function.utils';
import Decimal from 'decimal.js';

@Component({
  selector: 'app-mr',
  templateUrl: './mr.component.html',
  styleUrls: ['./mr.component.scss']
})
export class MrComponent implements OnInit, OnChanges {
  @Input() public instrumentsList: any = [];
  @Input() public index: number = null;
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public units: any = null;
  @Input() public typeInstrument: any = null;
  @Input() public datetime: any = null;
  @Input() public references: any = null;

  @Output() public setInstrument = new EventEmitter();
  @Output() public executeCalc = new EventEmitter();

  public formReading: FormGroup = new FormGroup({
    instrument: new FormControl('', [Validators.required]),
    date: new FormControl({ value: '', disabled: true }, [Validators.required]),
    delta_ref: new FormControl({ value: '', disabled: true }),
    absolute_settlement: new FormControl({ value: '', disabled: true }),
    relative_settlement: new FormControl({ value: '', disabled: true }),
    quota: new FormControl({ value: '', disabled: true }),
    absolute_depth: new FormControl({ value: '', disabled: true }),
    relative_depth: new FormControl({ value: '', disabled: true }),
    measure: new FormControl({ value: '', disabled: true }), //Anel magnetico
    //Para calcular
    top_quota: new FormControl({ value: '', disabled: true }),
    measure_referencial: new FormControl({ value: '', disabled: true }),
    magnetic_ring: new FormControl({ value: '', disabled: true }),
    //Para edicao
    id: new FormControl({ value: '', disabled: true })
  });

  public controls: any = null;

  public fieldsReading = fieldsReading;

  public message: any = [{ text: '', status: false }];
  public messagesError: any = null;

  public func = fn;

  constructor(private formService: FormService) {}

  /**
   * Método de ciclo de vida Angular chamado ao inicializar o componente.
   * Inicializa os controles do formulário.
   */
  ngOnInit(): void {
    this.controls = this.formReading.controls;
  }

  /**
   * Método de ciclo de vida Angular chamado quando as propriedades vinculadas ao componente mudam.
   * Lida com as mudanças nos dados, unidades e datetime, recalculando ou atualizando os valores conforme necessário.
   * @param {SimpleChanges} changes - As alterações detectadas no componente.
   */
  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReading.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }

    if (changes.units && changes.units.previousValue != undefined && !(changes.units.previousValue === changes.units.currentValue)) {
      this.recalculate(changes.units.previousValue, changes.units.currentValue);
    }

    if (changes.datetime && changes.datetime.currentValue != null) {
      this.controls['date'].setValue(this.datetime);
    }
  }

  /**
   * Emite o instrumento selecionado através do evento setInstrument.
   * @param {any} instrument - O instrumento selecionado.
   */
  changeInstrument(instrument) {
    this.setInstrument.emit(instrument);
  }

  /**
   * Divide os dados recebidos e atualiza os campos do formulário com os valores correspondentes.
   * Se a visualização estiver ativada, desativa o formulário.
   * @param {any} $dados - Os dados a serem divididos e inseridos no formulário.
   */
  splitData($dados) {
    if (this.index > 0 || this.edit || this.view) {
      this.controls['instrument'].disable();
      this.instrumentsList = $dados.instrumentsList;
    } else {
      this.controls['instrument'].enable();
    }

    this.formService.toggleFormList(this.formReading, this.fieldsReading[this.typeInstrument.id]);

    this.controls['instrument'].setValue($dados.instrument.id);
    this.controls['measure'].setValue($dados.measure.identifier);
    this.controls['measure_referencial'].setValue($dados.measure_referencial);
    this.controls['top_quota'].setValue($dados.top_quota);
    this.controls['magnetic_ring'].setValue($dados.measure);

    if ($dados.edit) {
      this.controls['id'].setValue($dados.edit.id);

      let date = $dados.edit.date.split('.');
      this.controls['date'].setValue(date[0]);

      this.controls['delta_ref'].setValue($dados.edit.delta_ref);
      this.controls['absolute_settlement'].setValue($dados.edit.absolute_settlement);
      this.controls['relative_settlement'].setValue($dados.edit.relative_settlement);
      this.controls['quota'].setValue($dados.edit.quota);
      this.controls['absolute_depth'].setValue($dados.edit.depth);
      this.controls['relative_depth'].setValue($dados.edit.relative_depth);
    }

    if (this.view) {
      this.formReading.disable();
    }

    //Após importar a planilha, não é mais necessário fazer o recálculo
    // if (this.spreadsheet) {
    //   setTimeout(() => {
    //     this.calcAfterLoadSpreadsheet();
    //   }, 200);
    // }
  }

  /**
   * Realiza cálculos relacionados ao assentamento e profundidade absolutos, ou outros parâmetros relacionados.
   * Converte valores de cotas e cálculos de assentamento, ajustando os resultados de acordo com a opção especificada.
   * @param {string} [option=''] - A opção de cálculo a ser realizada (ex: absolute_settlement, absolute_depth, delta).
   */
  calcAbsolute(option: string = '') {
    if (option == 'absolute_settlement') {
      let top_quota = this.controls['top_quota'].value;
      let top_quotaDecimal: any = fn.convertLengthDecimal(top_quota, 'm', 'mm');
      let absolute_settlement = this.controls['absolute_settlement'].value;

      if (!fn.isEmpty(absolute_settlement)) {
        let quota = this.controls['magnetic_ring'].value.quota;
        let quotaDecimal: any = fn.convertLengthDecimal(quota, 'm', 'mm');

        let absolute_settlementDecimal: any = fn.convertLengthDecimal(absolute_settlement, this.units[0], 'mm');

        let absolute_depthDecimal: any = Decimal.sub(top_quotaDecimal, quotaDecimal);
        absolute_depthDecimal = Decimal.sub(absolute_depthDecimal, absolute_settlementDecimal);

        absolute_depthDecimal = fn.convertLengthDecimal(absolute_depthDecimal, 'mm', this.units[0]);
        this.controls['absolute_depth'].setValue(absolute_depthDecimal);

        this.calcAbsolute('delta');
      }
    } else if (option == 'absolute_depth') {
      let top_quota = this.controls['top_quota'].value;
      let top_quotaDecimal: any = fn.convertLengthDecimal(top_quota, 'm', 'mm');
      let absolute_depth = this.controls['absolute_depth'].value;

      if (!fn.isEmpty(absolute_depth)) {
        let quota = this.controls['magnetic_ring'].value.quota;
        let quotaDecimal: any = fn.convertLengthDecimal(quota, 'm', 'mm');

        let absolute_depthDecimal: any = fn.convertLengthDecimal(absolute_depth, this.units[0], 'mm');

        let absolute_settlementDecimal: any = Decimal.sub(top_quotaDecimal, quotaDecimal);
        absolute_settlementDecimal = Decimal.sub(absolute_settlementDecimal, absolute_depthDecimal);

        absolute_settlementDecimal = fn.convertLengthDecimal(absolute_settlementDecimal, 'mm', this.units[0]);
        this.controls['absolute_settlement'].setValue(absolute_settlementDecimal);

        this.calcAbsolute('delta');
      }
    } else if (option == 'delta') {
      if (this.controls['magnetic_ring'].value.is_referencial) {
        this.controls['delta_ref'].setValue(0);
      }
      this.calculate('delta_ref');
    } else if (option == 'relative_settlement') {
      if (this.controls['magnetic_ring'].value.is_referencial) {
        this.controls['relative_settlement'].setValue(0);
      } else {
        let delta_ref = this.controls['delta_ref'].value;
        let delta_refDecimal: any = fn.convertLengthDecimal(delta_ref, 'm', 'mm');

        let delta_ref_magnetic_ring = this.controls['magnetic_ring'].value.delta_ref;
        let delta_ref_magnetic_ringDecimal: any = fn.convertLengthDecimal(delta_ref_magnetic_ring, 'm', 'mm');

        let relative_settlementDecimal: any = Decimal.sub(delta_refDecimal, delta_ref_magnetic_ringDecimal);

        this.controls['relative_settlement'].setValue(relative_settlementDecimal);
      }
      this.calcAbsolute('quota');
    } else if (option == 'quota') {
      if (this.controls['magnetic_ring'].value.is_referencial) {
        let quota = this.controls['magnetic_ring'].value.quota;
        let quotaDecimal = fn.convertLengthDecimal(quota, 'm', 'm');

        this.controls['quota'].setValue(quotaDecimal);

        let top_quota = this.controls['top_quota'].value;
        let top_quotaDecimal: any = fn.convertLengthDecimal(top_quota, 'm', 'm');

        let quotaReference = this.controls['magnetic_ring'].value.quota;
        let quotaReferenceDecimal: any = fn.convertLengthDecimal(quotaReference, 'm', 'm');

        let relative_depthDecimal: any = Decimal.sub(top_quotaDecimal, quotaReferenceDecimal);
        this.controls['relative_depth'].setValue(relative_depthDecimal);
      }
      this.calculate('quota');
    } else if (option == 'relative_depth') {
      if (!this.controls['magnetic_ring'].value.is_referencial) {
        let top_quota = this.controls['top_quota'].value;
        let top_quotaDecimal: any = fn.convertLengthDecimal(top_quota, 'm', 'm');

        let quota = this.controls['quota'].value;
        let quotaDecimal: any = new Decimal(quota);

        let relative_depthDecimal: any = Decimal.sub(top_quotaDecimal, quotaDecimal);
        this.controls['relative_depth'].setValue(relative_depthDecimal);
      }
    }
  }

  /**
   * Executa cálculos adicionais com base no tipo de cálculo fornecido.
   * Emite um evento para realizar os cálculos em componentes externos.
   * @param {string} [calc=''] - O tipo de cálculo a ser realizado.
   * @param {string} [suffix=''] - Sufixo opcional para o cálculo.
   */
  calculate(calc: string = '', suffix: string = '') {
    switch (calc) {
      case 'delta_ref':
      case 'quota':
        this.executeCalc.emit({ typeInstrument: this.typeInstrument.id, calc: calc, index: this.index });
        break;
      default:
        break;
    }
  }

  /**
   * Recalcula valores quando a unidade de medida é alterada.
   * Converte valores de assentamento absoluto de acordo com a nova unidade selecionada.
   * @param {any} previousUnit - A unidade de medida anterior.
   * @param {any} currentUnit - A nova unidade de medida.
   */
  recalculate(previousUnit, currentUnit) {
    if (this.controls['absolute_settlement'].value != '' && previousUnit[0] != currentUnit[0]) {
      let absolute_settlement = this.controls['absolute_settlement'].value;
      absolute_settlement = fn.convertLengthDecimal(absolute_settlement, previousUnit[0], currentUnit[0]);
      this.controls['absolute_settlement'].setValue(absolute_settlement);
      this.calcAbsolute('absolute_settlement');
    }
  }

  /**
   * Executa os cálculos de assentamento absoluto e profundidade absoluta após a importação de uma planilha.
   */
  calcAfterLoadSpreadsheet() {
    this.calcAbsolute('absolute_settlement');
    this.calcAbsolute('absolute_depth');
  }
}
