import { Injectable } from '@angular/core';
import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import * as _ from 'lodash';
import Decimal from 'decimal.js';

@Injectable({
  providedIn: 'root'
})
export class ReadingService {
  public reading = {
    instrument: {
      id: '',
      identifier: '',
      type: null
    },
    is_referential: null,
    values: [],
    should_recalculate_stability: false
  };

  public readingSections = {
    section: {
      id: '',
      name: ''
    }
  };

  public readingSectionsItem = {
    date: null,
    length: 0
  };

  public readingMeasure = {
    id: null,
    measurement: null,
    date: null,
    quota: null,
    depth: null,
    pressure: null,
    dry: null,
    elevation: null,
    positive_a: null,
    negative_a: null,
    positive_b: null,
    negative_b: null,
    average_displacement_a: null,
    average_displacement_b: null,
    accumulated_displacement_a: null,
    accumulated_displacement_b: null,
    deviation_a: null,
    deviation_b: null,
    a_axis_reading: null,
    b_axis_reading: null,
    datum: null,
    east_coordinate: null,
    north_coordinate: null,
    east_displacement: null,
    north_displacement: null,
    z_displacement: null,
    total_planimetric_displacement: null,
    a_displacement: null,
    b_displacement: null,
    relative_depth: null,
    delta_ref: null,
    absolute_settlement: null,
    relative_settlement: null,
    nature: null,
    a_axis_pga: null,
    b_axis_pga: null,
    z_axis_pga: null,
    pluviometry: null,
    intensity: null
  };

  public allReadings = []; //Cadastro via planilha - Vai armazenar o que retornar do endpoint

  constructor() {}

  /**
   * Encontra a referência de uma medição específica dentro de uma lista de referências.
   * @param {string} measurementId - O ID da medição a ser encontrada.
   * @param {any} references - Lista de referências de medições.
   * @returns {any} - O item da lista de referências correspondente ao measurementId.
   */
  findReference(measurementId: string, references: any = {}) {
    let idx = references.values?.find(({ measurement }) => measurement.id == measurementId);
    return idx;
  }

  /**
   * Encontra o índice de um item em um array com base em uma chave e um valor fornecidos.
   * @param {any[]} array - O array a ser pesquisado.
   * @param {any} id - O identificador que será utilizado para comparação.
   * @param {any} value - O valor a ser comparado no array.
   * @returns {number} - O índice do item encontrado, ou -1 se não encontrado.
   */
  findIndexItemByIdandValue(array: any, id: any, value: any) {
    return array.findIndex((item) => item[id] === value);
  }

  /**
   * Calcula o deslocamento acumulado médio dos componentes fornecidos, convertendo os valores para unidades de milímetros.
   * @param {any} components - Os componentes que contêm os valores de deslocamento.
   * @param {any[]} units - As unidades de medida a serem usadas na conversão.
   * @param {number} [idUnit=0] - O índice da unidade a ser usada na conversão.
   */
  calcAccumulatedDisplacement(components: any, units: any, idUnit: number = 0) {
    let allComponent = components.toArray();

    allComponent.sort(function (a, b) {
      return b.controls['depth'].value - a.controls['depth'].value;
    });

    allComponent.forEach((component, index) => {
      let controls = component.formReading.controls;

      ['a', 'b'].forEach((letter) => {
        if (index == 0) {
          !fn.isEmpty(controls['average_displacement_' + letter].value)
            ? controls['accumulated_displacement_' + letter].setValue(
                fn.convertLengthDecimal(controls['average_displacement_' + letter].value, units[idUnit], 'mm')
              )
            : '';
        } else {
          let previousControl = allComponent[index - 1].formReading.controls;
          !fn.isEmpty(previousControl['accumulated_displacement_' + letter].value) && !fn.isEmpty(controls['average_displacement_' + letter].value)
            ? controls['accumulated_displacement_' + letter].setValue(
                fn.convertLengthDecimal(
                  previousControl['accumulated_displacement_' + letter].value * 1 + controls['average_displacement_' + letter].value * 1,
                  units[idUnit],
                  'mm'
                )
              )
            : '';
        }
      });
    });
  }

  /**
   * Calcula os desvios "A" e "B" dos componentes, comparando os valores acumulados de deslocamento com as referências fornecidas.
   * @param {any} components - Os componentes que contêm os valores de deslocamento.
   * @param {any[]} units - As unidades de medida a serem usadas na conversão.
   * @param {any} references - As referências de deslocamento acumulado a serem comparadas.
   * @param {number} [idUnit=0] - O índice da unidade a ser usada na conversão.
   */
  calcDesviation(components: any, units: any, references: any, idUnit: number = 0) {
    let allComponent = components.toArray();
    allComponent.forEach((component) => {
      let controls = component.formReading.controls;

      ['a', 'b'].forEach((letter) => {
        let accumulated_displacement = controls['accumulated_displacement_' + letter].value;
        let accumulated_displacementDecimal: any = new Decimal(parseFloat(accumulated_displacement));
        let measureId = controls['measure_id'].value;
        let reference = this.findReference(measureId, references);
        let deviationDecimal: any = new Decimal(0);
        if (accumulated_displacement != '' && reference != undefined) {
          let reference_accumulated_displacement = reference['accumulated_displacement_' + letter];
          let reference_accumulated_displacementDecimal: any = new Decimal(reference_accumulated_displacement);
          deviationDecimal = accumulated_displacementDecimal.sub(reference_accumulated_displacementDecimal);
        }
        controls['deviation_' + letter].setValue(deviationDecimal);
      });
    });
  }

  /**
   * Calcula o delta de referência para um medidor de recalque, usando profundidades absolutas e referências.
   * @param {any} components - Os componentes que contêm os valores de profundidade.
   * @param {any[]} units - As unidades de medida a serem usadas na conversão.
   * @param {number} [idUnit=0] - O índice da unidade a ser usada na conversão.
   */
  calcDeltaRef(components: any, units: any, idUnit: number = 0) {
    let allComponent = components.toArray();
    let measure_referencial = null;
    allComponent.forEach((component) => {
      let controls = component.formReading.controls;
      if (controls['magnetic_ring'].value.is_referencial) {
        measure_referencial = controls;
      }
    });

    if (!fn.isEmpty(measure_referencial['absolute_depth'].value)) {
      allComponent.forEach((component) => {
        let controls = component.formReading.controls;
        if (!fn.isEmpty(controls['magnetic_ring'].value.is_referencial) && !fn.isEmpty(controls['absolute_depth'].value)) {
          let absolute_depthReference = measure_referencial['absolute_depth'].value;
          let absolute_depthReferenceDecimal: any = fn.convertLengthDecimal(absolute_depthReference, units[idUnit], 'mm');

          let absolute_depth = controls['absolute_depth'].value;
          let absolute_depthDecimal: any = fn.convertLengthDecimal(absolute_depth, units[idUnit], 'mm');

          let delta_refDecimal: any = Decimal.sub(absolute_depthReferenceDecimal, absolute_depthDecimal);
          delta_refDecimal = Decimal.div(delta_refDecimal, 1000);
          controls['delta_ref'].setValue(delta_refDecimal);
          component.calcAbsolute('relative_settlement');
        }
      });
    }
  }

  /**
   * Calcula a cota (quota) de um medidor de recalque, utilizando o delta de referência e a cota de referência.
   * @param {any} components - Os componentes que contêm os valores de cota.
   * @param {any[]} units - As unidades de medida a serem usadas na conversão.
   * @param {number} [idUnit=0] - O índice da unidade a ser usada na conversão.
   */
  calcQuotaMr(components: any, units: any, idUnit: number = 0) {
    let allComponent = components.toArray();
    let measure_referencial = null;
    allComponent.forEach((component) => {
      let controls = component.formReading.controls;
      if (controls['magnetic_ring'].value.is_referencial) {
        measure_referencial = controls;
      }
    });

    if (!fn.isEmpty(measure_referencial['quota'].value)) {
      allComponent.forEach((component) => {
        let controls = component.formReading.controls;
        if (!fn.isEmpty(controls['magnetic_ring'].value.is_referencial) && !fn.isEmpty(controls['delta_ref'].value)) {
          let quotaReferenceDecimal: any = new Decimal(measure_referencial['quota'].value);
          let delta_ref = controls['delta_ref'].value;
          let delta_refDecimal: any = fn.convertLengthDecimal(delta_ref, 'm', 'm');

          let quotaDecimal: any = Decimal.add(quotaReferenceDecimal, delta_refDecimal);
          controls['quota'].setValue(quotaDecimal);
          component.calcAbsolute('relative_depth');
        }
      });
    }
  }

  /**
   * Obtém as leituras dos instrumentos a partir de um formulário e valida os dados.
   * @param {any} formReadings - O formulário que contém os dados das leituras.
   * @returns {any} - Um objeto contendo as leituras validadas.
   */
  getReadings(formReadings: any) {
    //Camada para validar
    let readingsData = [];

    if (this.allReadings.length == 0) {
      //Se e somente se todas as leituras forem validas
      formReadings.forEach((formReading: any) => {
        let dataReading = _.cloneDeep(this.reading);

        dataReading['values'] = [];
        dataReading['instrument']['id'] = formReading.selectInstrument.id;
        dataReading['instrument']['identifier'] = formReading.selectInstrument.identifier;
        dataReading['instrument']['type'] = formReading.selectInstrument.type;
        dataReading['is_referential'] = formReading.controls['is_referencial'].value;

        if (formReading.edit) {
          dataReading['id'] = formReading.controls['id'].value;
        }

        formReading.readingInstrumentRef.toArray().forEach((reading: any) => {
          dataReading['values'].push(this.getReadingsInstruments(reading, formReading.selectInstrument.type));
        });

        readingsData.push(dataReading);
      });
    } else {
      readingsData = this.allReadings;
    }
    return { valid: true, data: readingsData };
  }

  /**
   * Extrai os valores de leitura de um instrumento específico com base no tipo de instrumento.
   * @param {any} reading - O objeto de leitura do instrumento.
   * @param {number} typeInstrument - O tipo de instrumento.
   * @returns {any} - Os valores de leitura processados e formatados.
   */
  getReadingsInstruments(reading, typeInstrument) {
    let units: any = reading.units;
    let dataReadingValues: any = _.cloneDeep(this.readingMeasure);

    if (reading.edit) {
      dataReadingValues['id'] = reading.controls['id'].value;
    }

    switch (typeInstrument) {
      case 1: //INA
      case 2: //PZ
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['quota'] = reading.controls['quota'].value ? fn.convertLengthDecimal(reading.controls['quota'].value, units[0], 'm') : null;
        dataReadingValues['depth'] = reading.controls['depth'].value ? fn.convertLengthDecimal(reading.controls['depth'].value, units[0], 'm') : null;
        dataReadingValues['pressure'] = reading.controls['pressure'].value
          ? fn.convertToPressureDecimal(reading.controls['pressure'].value, units[1], 'kPa')
          : null;
        dataReadingValues['pressure'] = parseFloat(dataReadingValues['pressure']);
        dataReadingValues['dry'] = reading.controls['dry'].value;
        break;
      case 3: //PZE
        dataReadingValues['measurement'] = { id: reading.data.measure.id };
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['quota'] = reading.controls['quota'].value ? fn.convertLengthDecimal(reading.controls['quota'].value, units[0], 'm') : null;
        dataReadingValues['dry'] = reading.controls['dry'].value;
        break;
      case 4: //INC_CONV
        dataReadingValues['measurement'] = { id: reading.data.measure.id };
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['depth'] = reading.controls['depth'].value ? fn.convertLengthDecimal(reading.controls['depth'].value, units[0], 'mm') : null;
        //dataReadingValues['elevation'] = fn.convertLengthDecimal(reading.controls['elevation'].value, units[0], 'mm');
        dataReadingValues['quota'] = fn.convertLengthDecimal(reading.controls['elevation'].value, units[0], 'mm');
        dataReadingValues['positive_a'] = reading.controls['positive_a'].value
          ? fn.convertLengthDecimal(reading.controls['positive_a'].value, units[0], 'mm')
          : null;
        dataReadingValues['negative_a'] = reading.controls['negative_a'].value
          ? fn.convertLengthDecimal(reading.controls['negative_a'].value, units[0], 'mm')
          : null;
        dataReadingValues['positive_b'] = reading.controls['positive_b'].value
          ? fn.convertLengthDecimal(reading.controls['positive_b'].value, units[0], 'mm')
          : null;
        dataReadingValues['negative_b'] = reading.controls['negative_b'].value
          ? fn.convertLengthDecimal(reading.controls['negative_b'].value, units[0], 'mm')
          : null;
        dataReadingValues['average_displacement_a'] = fn.convertLengthDecimal(reading.controls['average_displacement_a'].value, units[0], 'mm');
        dataReadingValues['average_displacement_b'] = fn.convertLengthDecimal(reading.controls['average_displacement_b'].value, units[0], 'mm');
        dataReadingValues['accumulated_displacement_a'] = fn.convertLengthDecimal(reading.controls['accumulated_displacement_a'].value, units[0], 'mm');
        dataReadingValues['accumulated_displacement_b'] = fn.convertLengthDecimal(reading.controls['accumulated_displacement_b'].value, units[0], 'mm');
        dataReadingValues['deviation_a'] = fn.convertLengthDecimal(reading.controls['deviation_a'].value, units[0], 'mm');
        dataReadingValues['deviation_b'] = fn.convertLengthDecimal(reading.controls['deviation_b'].value, units[0], 'mm');
        break;
      case 5: //IPI
        dataReadingValues['measurement'] = { id: reading.data.measure.id };
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['depth'] = fn.convertLengthDecimal(reading.controls['depth'].value, units[1], 'mm');
        //dataReadingValues['elevation'] = fn.convertLengthDecimal(reading.controls['elevation'].value, units[1], 'mm');
        dataReadingValues['quota'] = fn.convertLengthDecimal(reading.controls['elevation'].value, units[1], 'mm');
        dataReadingValues['length'] = fn.convertLengthDecimal(reading.controls['length'].value, units[1], 'mm');
        dataReadingValues['a_axis_reading'] = reading.controls['a_axis_reading'].value
          ? fn.convertToDegreeDecimal(reading.controls['a_axis_reading'].value, units[0], 'graus(°)')
          : null;
        dataReadingValues['b_axis_reading'] = reading.controls['b_axis_reading'].value
          ? fn.convertToDegreeDecimal(reading.controls['b_axis_reading'].value, units[0], 'graus(°)')
          : null;
        dataReadingValues['average_displacement_a'] = fn.convertLengthDecimal(reading.controls['average_displacement_a'].value, units[1], 'mm');
        dataReadingValues['average_displacement_b'] = fn.convertLengthDecimal(reading.controls['average_displacement_b'].value, units[1], 'mm');
        dataReadingValues['accumulated_displacement_a'] = fn.convertLengthDecimal(reading.controls['accumulated_displacement_a'].value, units[1], 'mm');
        dataReadingValues['accumulated_displacement_b'] = fn.convertLengthDecimal(reading.controls['accumulated_displacement_b'].value, units[1], 'mm');
        dataReadingValues['deviation_a'] = fn.convertLengthDecimal(reading.controls['deviation_a'].value, units[1], 'mm');
        dataReadingValues['deviation_b'] = fn.convertLengthDecimal(reading.controls['deviation_b'].value, units[1], 'mm');
        break;
      case 6: //MS
      case 7: //PR
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['datum'] = reading.controls['datum'].value;
        dataReadingValues['east_coordinate'] = fn.convertLengthDecimal(reading.controls['east_coordinate'].value, units[0], 'm');
        dataReadingValues['north_coordinate'] = fn.convertLengthDecimal(reading.controls['north_coordinate'].value, units[0], 'm');
        dataReadingValues['quota'] = fn.convertLengthDecimal(reading.controls['quota'].value, units[0], 'm');
        dataReadingValues['east_displacement'] = reading.controls['east_displacement'].value;
        dataReadingValues['north_displacement'] = reading.controls['north_displacement'].value;
        dataReadingValues['z_displacement'] = reading.controls['z_displacement'].value;
        dataReadingValues['total_planimetric_displacement'] = reading.controls['total_planimetric_displacement'].value;
        dataReadingValues['a_displacement'] =
          reading.controls['a_displacement'].value && reading.controls['a_displacement'].value != '-' ? reading.controls['a_displacement'].value : null;
        dataReadingValues['b_displacement'] =
          reading.controls['b_displacement'].value && reading.controls['b_displacement'].value != '-' ? reading.controls['b_displacement'].value : null;
        break;
      case 8: //MR
        dataReadingValues['measurement'] = { id: reading.data.measure.id };
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['delta_ref'] = reading.controls['delta_ref'].value;
        dataReadingValues['quota'] = reading.controls['quota'].value;
        dataReadingValues['absolute_settlement'] = fn.convertLengthDecimal(reading.controls['absolute_settlement'].value, units[0], 'mm');
        dataReadingValues['relative_settlement'] = reading.controls['relative_settlement'].value;
        dataReadingValues['depth'] = fn.convertLengthDecimal(reading.controls['absolute_depth'].value, units[0], 'm');
        dataReadingValues['relative_depth'] = reading.controls['relative_depth'].value;
        break;
      case 9: //GEO
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['nature'] = { id: reading.controls['nature'].value };
        dataReadingValues['a_axis_pga'] = reading.controls['a_axis_pga'].value;
        dataReadingValues['b_axis_pga'] = reading.controls['b_axis_pga'].value;
        dataReadingValues['z_axis_pga'] = reading.controls['z_axis_pga'].value;
        dataReadingValues['east_coordinate'] = reading.controls['east_coordinate'].value;
        dataReadingValues['north_coordinate'] = reading.controls['north_coordinate'].value;
        break;
      case 10: //RL
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['quota'] = fn.convertLengthDecimal(reading.controls['quota'].value, units[0], 'm');
        break;
      case 12: //Pluviometro
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['pluviometry'] = reading.controls['pluviometry'].value
          ? fn.convertLengthDecimal(reading.controls['pluviometry'].value, units[0], 'mm')
          : null;
        break;
      case 13: //Pluviografo
        dataReadingValues['date'] = reading.controls['date'].value;
        dataReadingValues['pluviometry'] = reading.controls['pluviometry'].value
          ? fn.convertLengthDecimal(reading.controls['pluviometry'].value, units[0], 'mm')
          : null;
        dataReadingValues['intensity'] = reading.controls['intensity'].value;
        break;
    }
    return dataReadingValues;
  }

  /**
   * Obtém as leituras das seções de comprimento de praia a partir de um formulário.
   * @param {any} formReadings - O formulário que contém os dados das leituras de seções.
   * @returns {any} - Um objeto contendo as leituras validadas das seções.
   */
  getReadingsSections(formReadings: any) {
    //Camada para validar
    let readingsData = null;

    //Se e somente se todas as leituras forem validas
    formReadings.forEach((formReading: any) => {
      let dataReading = _.cloneDeep(this.readingSections);

      if (formReading.edit) {
        dataReading['id'] = formReading.controls['id'].value;
      }

      dataReading['section']['id'] = formReading.selectSection.id;
      dataReading['section']['name'] = formReading.selectSection.identifier;

      formReading.readingInstrumentRef.toArray().forEach((reading: any) => {
        readingsData = { ...dataReading, ...this.getReadingsSectionsItem(reading, formReading.selectSection.type) };
      });
    });
    return { valid: true, data: readingsData };
  }

  /**
   * Extrai os valores de leitura de uma seção com base no tipo de instrumento.
   * @param {any} reading - O objeto de leitura da seção.
   * @param {number} typeInstrument - O tipo de instrumento.
   * @returns {any} - Os valores de leitura processados e formatados.
   */
  getReadingsSectionsItem(reading, typeInstrument) {
    let units: any = reading.units;
    let dataReadingValues: any = _.cloneDeep(this.readingSectionsItem);

    switch (typeInstrument) {
      case 11:
        dataReadingValues['date'] = moment(reading.controls['date'].value).format('YYYY-MM-DDTHH:MM:SS');
        dataReadingValues['length'] = fn.convertLengthDecimal(reading.controls['length'].value, units[0], 'm');
        break;
    }
    return dataReadingValues;
  }

  /**
   * Obtém as leituras selecionadas com base na opção fornecida (patch, delete ou packages).
   * @param {any} data - Os dados das leituras.
   * @param {string} dateTime - A data e hora da leitura.
   * @param {string} [option='patch'] - A opção de operação (patch, delete ou packages).
   * @returns {any[]} - Um array de leituras selecionadas.
   */
  getSelectedReadings(data, dateTime, option = 'patch') {
    return data
      .filter((item) => item.select === true)
      .map((item) => {
        if (option == 'patch') {
          let reading = {};
          reading['id'] = item.select_id;
          reading['date'] = dateTime;
          return reading;
        } else if (option == 'delete') {
          return item.reading_id;
        } else if (option == 'packages') {
          return item.select_id;
        }
      });
  }

  /**
   * Reseta as variáveis internas do serviço para seus estados iniciais.
   * Útil ao limpar formulários ou reiniciar um fluxo de leitura.
   */
  resetReadings() {
    this.reading = {
      instrument: {
        id: '',
        identifier: '',
        type: null
      },
      is_referential: null,
      values: [],
      should_recalculate_stability: false
    };

    this.readingSections = {
      section: {
        id: '',
        name: ''
      }
    };

    this.readingSectionsItem = {
      date: null,
      length: 0
    };

    this.readingMeasure = {
      id: null,
      measurement: null,
      date: null,
      quota: null,
      depth: null,
      pressure: null,
      dry: null,
      elevation: null,
      positive_a: null,
      negative_a: null,
      positive_b: null,
      negative_b: null,
      average_displacement_a: null,
      average_displacement_b: null,
      accumulated_displacement_a: null,
      accumulated_displacement_b: null,
      deviation_a: null,
      deviation_b: null,
      a_axis_reading: null,
      b_axis_reading: null,
      datum: null,
      east_coordinate: null,
      north_coordinate: null,
      east_displacement: null,
      north_displacement: null,
      z_displacement: null,
      total_planimetric_displacement: null,
      a_displacement: null,
      b_displacement: null,
      relative_depth: null,
      delta_ref: null,
      absolute_settlement: null,
      relative_settlement: null,
      nature: null,
      a_axis_pga: null,
      b_axis_pga: null,
      z_axis_pga: null,
      pluviometry: null,
      intensity: null
    };

    this.allReadings = [];
  }
}
