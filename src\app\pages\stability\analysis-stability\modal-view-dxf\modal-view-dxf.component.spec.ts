import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalViewDxfComponent } from './modal-view-dxf.component';

describe('ModalViewDxfComponent', () => {
  let component: ModalViewDxfComponent;
  let fixture: ComponentFixture<ModalViewDxfComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalViewDxfComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalViewDxfComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
