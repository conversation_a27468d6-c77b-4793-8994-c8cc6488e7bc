<app-loader-backdrop></app-loader-backdrop>

<div class="list-section">
  <div class="container-grid mt-2 mb-3">
    <!-- Mapa -->
    <div class="grid-item" style="position: relative" #mapWrapper>
      <div class="grid-header">
        <div>
          <i class="fas fa-map-marked me-2"></i>
          <span *ngIf="structureName != ''">
            Estrutura: {{ structureName }}
          </span>
        </div>
      </div>

      <app-google-maps [discount]="30"></app-google-maps>

      <!-- Dropdown para Legenda -->
      <div
        class="dropdown"
        style="position: absolute; top: 42px; right: 64px"
        *ngIf="legendItens?.length > 0"
      >
        <app-button
          [class]="'btn-logisoil-green'"
          [customBtn]="true"
          [label]="'Legenda'"
          (click)="toggleDropdown()"
          aria-expanded="false"
        ></app-button>
        <ul
          class="dropdown-menu dropdown-menu-end mt-1 p-3"
          [class.show]="dropdownOpen"
          style="max-height: 300px; overflow-y: auto; width: 250px"
        >
          <li
            class="dropdown-item d-flex align-items-center mb-2"
            *ngFor="let item of legendItens"
          >
            <i class="{{ item.icon }} me-2" [style.color]="item.color"></i>
            <span class="legend">{{ item.name }}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Gráfico FS -->
    <div class="grid-item chart-container">
      <div class="grid-header">
        <div>
          <i class="fa fa-area-chart me-2"></i>
          <span>Gráfico FS: {{ structureName }} </span>
        </div>
      </div>

      <div id="carouselChartsFS" class="carousel slide" data-bs-ride="false">
        <div class="carousel-inner">
          <!-- Mensagem quando não houver nenhuma seção -->
          <div
            *ngIf="sections.length === 0"
            class="d-flex justify-content-center align-items-center"
            style="height: 340px"
          >
            <div class="text-center">
              <i class="fa fa-exclamation-circle text-warning fs-3"></i>
              <p class="mt-2 mb-0 text-muted">
                Nenhuma seção cadastrada para esta estrutura.
              </p>
            </div>
          </div>

          <!-- Itens do carrossel (um por seção) -->
          <div
            *ngFor="let section of sections; let i = index"
            class="carousel-item"
            [class.active]="i === activeIndex"
          >
            <!-- Cabeçalho com setas -->
            <div
              class="d-flex justify-content-center align-items-center mb-2 section-navigation"
            >
              <button
                class="btn btn-link p-0 me-3 carousel-control-prev-custom"
                type="button"
                data-bs-target="#carouselChartsFS"
                data-bs-slide="prev"
              >
                <i class="fa fa-chevron-left"></i>
              </button>

              <span>Seção: {{ section.name }}</span>

              <button
                class="btn btn-link p-0 ms-3 carousel-control-next-custom"
                type="button"
                data-bs-target="#carouselChartsFS"
                data-bs-slide="next"
              >
                <i class="fa fa-chevron-right"></i>
              </button>
            </div>

            <!-- Gráfico -->
            <div *ngIf="getSectionGraphById(section.id)?.chartFS?.options">
              <app-e-charts
                #chartFsRefs
                [height]="340"
                [dataChart]="getSectionGraphById(section.id)?.chartFS"
                [id]="'safetyFactor-' + section.id"
              ></app-e-charts>
            </div>

            <!-- Mensagem caso não haja dados para a seção -->
            <div
              *ngIf="!getSectionGraphById(section.id)?.chartFS?.options"
              class="d-flex justify-content-center align-items-center"
              style="height: 340px"
            >
              <div class="text-center">
                <i class="fa fa-exclamation-circle text-warning fs-3"></i>
                <p class="mt-2 mb-0 text-muted">
                  Nenhum dado disponível para esta seção.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Instrumentos ativos -->
    <div class="grid-item">
      <div class="grid-header">
        <div>
          <img
            class="icon-svg me-2"
            src="/assets/ico/ico-menu/instrumentacao.svg"
          />
          <span>Instrumentos ativos: {{ totalInstruments }}</span>
        </div>
      </div>

      <!-- Mensagem de erro no mesmo padrão de messagesError -->
      <div
        class="d-flex justify-content-center align-items-center h-100"
        *ngIf="messagesError?.length > 0"
      >
        <div class="row m-4">
          <span
            class="message-no-content"
            *ngFor="let message of messagesError; let i = index"
          >
            {{ message.message }}
          </span>
        </div>
      </div>

      <div
        class="d-grid"
        [ngStyle]="{
          'grid-template-columns': 'repeat(' + columns + ', 1fr)',
          'grid-template-rows': 'repeat(' + rows + ', 1fr)',
          gap: '10px',
          padding: '10px',
          height: '100%',
          'align-items': rows === 1 && columns === 2 ? 'center' : 'stretch',
          'justify-content': rows === 1 && columns === 2 ? 'center' : 'start'
        }"
      >
        <div *ngFor="let instrument of instruments" class="square-card">
          <div
            [style.borderColor]="instrument.border"
            [style.borderStyle]="'solid'"
            [style.borderWidth]="'3px'"
            class="text-center fw-bold rounded content"
            [ngStyle]="{
              'background-color': instrument['background'],
              color: instrument['text']
            }"
          >
            <div class="instrument-count">{{ instrument.count }}</div>
            <div class="instrument-name">{{ instrument.name }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Últimas atualizações -->
    <div class="grid-item">
      <div class="grid-header">
        <div>
          <i class="fa fa-refresh me-2"></i>
          <span>Últimas atualizações</span>
        </div>
      </div>
      <div class="card mt-2 mb-2 p-3 shadow-sm">
        <div class="row g-3">
          <!-- Todos os itens exceto o último (se total for ímpar) -->
          <ng-container *ngFor="let update of latestUpdates; let i = index">
            <!-- Se for o último E for total ímpar -->
            <div
              *ngIf="
                i === latestUpdates.length - 1 && latestUpdates.length % 2 === 1
              "
              class="col-md-6 offset-md-3"
            >
              <div class="update-card">
                <h6 class="title">{{ update.title }}</h6>
                <p class="value">{{ update.subtitle }}</p>
                <small class="date">{{ update.data }}</small>
              </div>
            </div>

            <!-- Demais itens -->
            <div
              *ngIf="
                !(
                  i === latestUpdates.length - 1 &&
                  latestUpdates.length % 2 === 1
                )
              "
              class="col-md-6"
            >
              <div class="update-card">
                <h6 class="title">{{ update.title }}</h6>
                <p class="value">{{ update.subtitle }}</p>
                <small class="date">{{ update.data }}</small>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>
