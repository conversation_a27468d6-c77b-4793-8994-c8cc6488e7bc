import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { MessageInputInvalid } from 'src/app/constants/message.constants';

import { ChartService as ChartServiceApi } from 'src/app/services/api/chart.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';

import * as moment from 'moment';
import * as d3 from 'd3';
import fn from 'src/app/utils/function.utils';
import * as _ from 'lodash';

@Component({
  selector: 'app-chart-inc-ipi',
  templateUrl: './chart-inc-ipi.component.html',
  styleUrls: ['./chart-inc-ipi.component.scss']
})
export class ChartIncIpiComponent implements OnInit {
  public formChart: FormGroup = new FormGroup({
    instrument: new FormControl([]),
    start_date: new FormControl('', [Validators.required]),
    end_date: new FormControl('', [Validators.required]),
    chart_height: new FormControl(300)
  });

  public controls: any = null;
  public chartA: any = {};
  public chartB: any = {};

  public chartSeriesA: any = [];
  public chartSeriesB: any = [];
  public chartLegendsA: any = [];
  public chartLegendsB: any = [];
  public chartLegendsTop: number = 0;
  public chartLegendsBottom: number = 0;

  public minMax: any = {
    a: { min: null, max: null },
    b: { min: null, max: null }
  };

  public yAxis: any = [];

  public instrumentId: any = null;
  public instruments: any = [];
  public instrumentsSettings = MultiSelectDefault.Instruments;

  public messageReturn: any = [{ text: '', status: false }];

  constructor(
    private activatedRoute: ActivatedRoute,
    private chartServiceApi: ChartServiceApi,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private router: Router
  ) {}

  /**
   * Método de inicialização do componente.
   * Configura os controles e carrega a lista de instrumentos.
   */
  ngOnInit(): void {
    this.controls = this.formChart.controls;
    this.instrumentsSettings.singleSelection = true;
    this.getInstruments();
  }

  /**
   * Obtém a lista de instrumentos com base na estrutura e tipo de instrumento especificados.
   * Define o instrumento selecionado nos controles do formulário.
   */
  getInstruments() {
    this.instrumentId = this.activatedRoute.snapshot.params.instrumentId;

    let params = { StructureId: this.activatedRoute.snapshot.queryParams.structure, Type: this.activatedRoute.snapshot.queryParams.typeInstrument };
    this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
      let dados: any = resp;

      if (dados.status == 200) {
        dados = dados.body === undefined ? dados : dados.body;
        this.instruments = dados;
        let instrumentInfo = fn.findIndexInArrayofObject(this.instruments, 'id', this.activatedRoute.snapshot.params.instrumentId, 'identifier', true);
        this.controls['instrument'].setValue([instrumentInfo]);
      }
    });
  }

  /**
   * Obtém os dados do gráfico de percolação com base nos instrumentos selecionados.
   * @param {string|null} id - ID opcional para o gráfico.
   */
  getChart(id = null) {
    this.resetConfigurations();
    this.messageReturn.text = '';
    this.messageReturn.status = false;

    let params = {};

    if (this.controls['start_date'].value != '') {
      params['StartDate'] = moment(this.controls['start_date'].value).format('YYYY-MM-DD');
    }

    if (this.controls['end_date'].value != '') {
      params['EndDate'] = moment(this.controls['end_date'].value).format('YYYY-MM-DD');
    }

    this.chartServiceApi.getChartInc(this.instrumentId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      if (dados) {
        this.constructChart(dados);
      } else {
        this.messageReturn.text = MessageInputInvalid.NoChart;
        this.messageReturn.status = true;

        setTimeout(() => {
          this.messageReturn.status = false;
        }, 4000);
      }
    });
  }

  //Constrói o gráfico a partir dos dados fornecidos.
  constructChart(data) {
    this.minMax = {
      a: { min: 0, max: 0 },
      b: { min: 0, max: 0 }
    };

    this.constructYAxis(data);
  }

  //Prepara os dados para os eixos Y do gráfico.
  constructYAxis(data) {
    this.yAxis = [];

    data.readings.map((reading) => {
      reading.data.map((measure) => {
        if (measure.measurement_quota != null) {
          this.yAxis.push(measure.measurement_quota);
        }
        if (measure.alert_level != null) {
          this.yAxis.push(measure.alert_level);
        }
        if (measure.attention_level != null) {
          this.yAxis.push(measure.attention_level);
        }
        if (measure.emergency_level != null) {
          this.yAxis.push(measure.emergency_level);
        }
      });
    });

    data.lithotypes.map((lithotype) => {
      if (lithotype.quota != null) {
        this.yAxis.push(lithotype.quota);
      }
    });

    this.yAxis = fn.arrayUnique(this.yAxis);
    this.yAxis.sort(function (a, b) {
      return a - b; // Isso fará com que os elementos sejam ordenados de forma crescente
    });

    this.constructSeries(data);
  }

  //Limpa o formulário das configurações do gráfico.
  resetConfigurations() {
    this.chartA = {};
    this.chartB = {};

    this.chartSeriesA = [];
    this.chartSeriesB = [];
    this.chartLegendsA = [];
    this.chartLegendsB = [];
    this.chartLegendsTop = 0;
    this.chartLegendsBottom = 0;

    this.minMax = {
      a: { min: null, max: null },
      b: { min: null, max: null }
    };

    this.yAxis = [];
  }

  //Constrói as séries de dados do gráfico.
  constructSeries(data) {
    const depthObject = {};
    this.chartSeriesA = [];
    this.chartSeriesB = [];
    this.chartLegendsA = [];
    this.chartLegendsB = [];

    let series = {
      deviation_a: {},
      deviation_b: {}
    };

    data.readings.map((reading) => {});

    for (const depthValue of this.yAxis) {
      depthObject[depthValue] = null;
    }

    const datesSorted: any = data.readings.sort((a, b) => {
      const dateA: any = new Date(a.date.replace('T', ' ')).getTime();
      const dateB: any = new Date(b.date.replace('T', ' ')).getTime();

      return dateA - dateB;
    });

    const dates = datesSorted.map((reading) => {
      return reading.date;
    });

    this.chartLegendsTop = 70;
    this.chartLegendsBottom = (dates.length / 3) * -1;
    this.controls['chart_height'].setValue(Math.floor(this.chartLegendsTop) + 720);

    dates.map((date) => {
      series.deviation_a[date] = _.cloneDeep(depthObject);
      series.deviation_b[date] = _.cloneDeep(depthObject);
      this.chartLegendsA.push(date);
      this.chartLegendsB.push(date);
    });

    series.deviation_a['Atenção'] = _.cloneDeep(depthObject);
    series.deviation_a['Alerta'] = _.cloneDeep(depthObject);
    series.deviation_a['Emergência'] = _.cloneDeep(depthObject);
    this.chartLegendsA.push('Atenção');
    this.chartLegendsA.push('Alerta');
    this.chartLegendsA.push('Emergência');

    data.readings.map((reading) => {
      const date = reading.date;
      reading.data.map((measure) => {
        const depth = measure.measurement_quota;
        const alert = measure.alert_level;
        const attention = measure.attention_level;
        const emergency = measure.emergency_level;

        series.deviation_a[date][depth] = series.deviation_a[date].hasOwnProperty(depth) ? measure.deviation_a : null;
        series.deviation_b[date][depth] = series.deviation_b[date].hasOwnProperty(depth) ? measure.deviation_b : null;

        series.deviation_a['Atenção'][attention] = series.deviation_a['Atenção'].hasOwnProperty(attention) ? measure.deviation_a : null;
        series.deviation_a['Alerta'][alert] = series.deviation_a['Alerta'].hasOwnProperty(alert) ? measure.deviation_a : null;
        series.deviation_a['Emergência'][emergency] = series.deviation_a['Emergência'].hasOwnProperty(emergency) ? measure.deviation_a : null;
      });
    });

    const gradientColorsA = this.generateColors(Object.keys(series.deviation_a).length + data['lithotypes'].length, ['#e83e8c', '#28a745', '#007bff']);
    const gradientColorsB = this.generateColors(Object.keys(series.deviation_b).length + data['lithotypes'].length, ['#e83e8c', '#28a745', '#007bff']);

    let iA = 0;

    for (const key in series.deviation_a) {
      let color = key == 'Alerta' ? '#fd7e14' : key == 'Atenção' ? '#ffc107' : key == 'Emergência' ? '#dc3545' : gradientColorsA[iA];
      let areaStyle = ['Atenção', 'Alerta', 'Emergência'].includes(key) ? { opacity: 0.05 } : null;

      const itemSeriesA = {
        name: key,
        type: 'line',
        data: fn.valuesOfObject(series.deviation_a[key]),
        connectNulls: true,
        itemStyle: {
          color: color
        },
        smooth: true,
        areaStyle: areaStyle
      };
      this.chartSeriesA.push(itemSeriesA);
      this.defineMinMax(itemSeriesA.data, 'a');
      iA++;
    }

    let iB = 0;
    for (const key in series.deviation_b) {
      const itemSeriesB = {
        name: key,
        type: 'line',
        data: fn.valuesOfObject(series.deviation_b[key]),
        connectNulls: true,
        itemStyle: {
          color: gradientColorsB[iB]
        },
        smooth: true
      };
      this.chartSeriesB.push(itemSeriesB);
      this.defineMinMax(itemSeriesB.data, 'b');
      iB++;
    }

    //Materiais
    data['lithotypes'].map((material) => {
      this.chartLegendsA.push(material.lithotype);
      const itemSeriesA = {
        name: material.lithotype,
        type: 'line',
        data: [
          [this.minMax.a.min, this.yAxis.indexOf(material.quota)],
          [this.minMax.a.max, this.yAxis.indexOf(material.quota)]
        ],
        lineStyle: {
          type: 'dashed' // Define a linha como tracejada
        },
        smooth: true
      };
      this.chartSeriesA.push(itemSeriesA);
      iA++;

      this.chartLegendsB.push(material.lithotype);
      const itemSeriesB = {
        name: material.lithotype,
        type: 'line',
        data: [
          [this.minMax.b.min, this.yAxis.indexOf(material.quota)],
          [this.minMax.b.max, this.yAxis.indexOf(material.quota)]
        ],
        lineStyle: {
          type: 'dashed' // Define a linha como tracejada
        },
        smooth: true
      };
      this.chartSeriesB.push(itemSeriesB);
      iB++;
    });
    this.constructXAxis();
  }

  //Constrói o eixo X do gráfico com base nas datas dos dados fornecidos.
  constructXAxis() {
    this.generateChart();
  }

  //Define a altura do gráfico.
  setHeight(height: string): void {
    this.controls['chart_height'].setValue(parseInt(height));
    this.generateChart();
  }

  /**
   * Gera uma paleta de cores interpolada com base em um comprimento específico.
   * @param {number} lenght - O número de cores a serem geradas.
   * @param {string[]} customPalette - (Opcional) Paleta de cores personalizada a ser usada em vez da paleta padrão.
   * @returns {string[]} - Array de cores geradas.
   */
  generateColors(lenght, customPalette) {
    // Use a paleta de cores personalizada se fornecida, caso contrário, use a paleta de cores padrão
    const paletaArcoIris = customPalette || [
      '#e83e8c', // Rosa
      '#fd7e14', // Laranja
      '#ffc107', // Amarelo
      '#28a745', // Verde
      '#007bff', // Azul
      '#4B0082', // Índigo
      '#8B00FF' // Violeta
    ];

    const coresIntermediarias = [];

    if (paletaArcoIris.length > 1) {
      const intervalo = 1 / (paletaArcoIris.length - 1);
      for (let i = 0; i < lenght; i++) {
        const index1 = Math.floor((i / lenght) * (paletaArcoIris.length - 1));
        const index2 = Math.min(index1 + 1, paletaArcoIris.length - 1);
        const percentual = ((i / lenght) * (paletaArcoIris.length - 1) - index1) / intervalo;
        const corIntermediaria = d3.interpolate(paletaArcoIris[index1], paletaArcoIris[index2])(percentual);
        coresIntermediarias.push(corIntermediaria);
      }
    }

    return coresIntermediarias;
  }

  /**
   * Define os valores mínimos e máximos dos dados da série para o eixo Y.
   * @param {any} array - Dados da série.
   * @param {number} index - Índice do eixo Y.
   */
  defineMinMax(array: any, letter = 'a', index = null) {
    array = array.filter((item) => item !== null && item !== undefined);

    array = array.map((item) => {
      if (item != null && item != undefined) {
        return typeof item == 'number' ? item : item.value;
      }
    });

    const min = Math.min(...array);
    const max = Math.max(...array);
    let previous = min - (min % 10);
    let next = max + (10 - (max % 10));

    this.minMax[letter].min = this.minMax[letter].min == null ? previous : this.minMax[letter].min;
    this.minMax[letter].min = Math.min(this.minMax[letter].min, previous);
    previous = this.minMax[letter].min;

    this.minMax[letter].max = this.minMax[letter].max == null ? next : this.minMax[letter].max;
    this.minMax[letter].max = Math.max(this.minMax[letter].max, next);
    next = this.minMax[letter].max;
  }

  //Gera e configura o gráfico usando as opções definidas.
  generateChart() {
    this.chartA['options'] = {
      title: {
        text: 'Desvio do Deslocamento - Eixo A (mm)',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegendsA,
        bottom: this.chartLegendsBottom,
        icon: 'rect',
        left: 'center'
      },
      grid: {
        containLabel: true,
        top: this.chartLegendsTop,
        left: '50',
        right: '50',
        bottom: '0',
        height: this.controls['chart_height'].value
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: [
        {
          type: 'value',
          position: 'top',
          interval: Math.ceil((this.minMax.a.max - this.minMax.a.min) / 15),
          min: this.minMax.a.min,
          max: this.minMax.a.max,
          axisLine: {
            lineStyle: {
              width: 2,
              color: '#000000'
            }
          },
          axisLabel: {
            formatter: function (params) {
              if (typeof params === 'number') {
                return params.toFixed(0); //Formata o valor para duas casas decimais
              } else {
                return params;
              }
            }
          },
          name: 'Desvios (mm)',
          nameLocation: 'middle',
          nameGap: 30
        }
      ],
      yAxis: [
        {
          type: 'category',
          data: this.yAxis,
          name: 'Cota (m)',
          nameGap: 60,
          nameLocation: 'center',
          nameRotate: 90,
          offset: 8,
          position: 'left',
          alignTicks: true,
          axisLine: {
            lineStyle: {
              width: 2,
              color: '#000000'
            }
          },
          axisLabel: {
            formatter: function (params) {
              if (typeof params === 'number') {
                return params.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return parseFloat(params).toFixed(2);
              }
            }
          },
          inverse: true
        }
      ],
      series: this.chartSeriesA
    };

    this.chartB['options'] = {
      title: {
        text: 'Desvio do Deslocamento - Eixo B (mm)',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        bottom: this.chartLegendsBottom,
        icon: 'rect',
        left: 'center'
      },
      grid: {
        containLabel: true,
        top: this.chartLegendsTop,
        left: '50',
        right: '50',
        bottom: '0',
        height: this.controls['chart_height'].value
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: [
        {
          type: 'value',
          position: 'top',
          interval: Math.ceil((this.minMax.b.max - this.minMax.b.min) / 15),
          min: this.minMax.b.min,
          max: this.minMax.b.max,
          axisLine: {
            lineStyle: {
              width: 2,
              color: '#000000'
            }
          },
          axisLabel: {
            formatter: function (params) {
              if (typeof params === 'number') {
                return params.toFixed(0); //Formata o valor para duas casas decimais
              } else {
                return params;
              }
            }
          },
          name: 'Desvios (mm)',
          nameLocation: 'middle',
          nameGap: 30
        }
      ],
      yAxis: [
        {
          type: 'category',
          data: this.yAxis,
          name: 'Cota (m)',
          nameGap: 60,
          nameLocation: 'center',
          nameRotate: 90,
          offset: 8,
          position: 'left',
          alignTicks: true,
          axisLine: {
            lineStyle: {
              width: 2,
              color: '#000000'
            }
          },
          axisLabel: {
            formatter: function (params) {
              if (typeof params === 'number') {
                return params.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return parseFloat(params).toFixed(2);
              }
            }
          },
          inverse: true
        }
      ],
      series: this.chartSeriesB
    };
  }

  /**
   * Altera o instrumento selecionado e redefine as configurações do gráfico.
   * @param {any} $event - Evento contendo o instrumento selecionado.
   * @param {string} action - Ação a ser tomada, padrão é 'select'.
   */
  changeInstrument($event, action: string = 'select') {
    if (action === 'select') {
      this.instrumentId = $event.id;
      this.resetConfigurations();
    }
  }

  //Navega de volta para a tela de instrumentos.
  goBack() {
    this.router.navigate(['/instruments']);
  }
}
