import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { AlertCenterRoutingModule } from './alert-center-routing.module';
import { SharedModule } from 'src/app/components/shared.module';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';

import { AlertCenterComponent } from './alert-center.component';

@NgModule({
  declarations: [AlertCenterComponent],
  imports: [CommonModule, FormsModule, NgbModule, ReactiveFormsModule, SharedModule, NgMultiSelectDropDownModule.forRoot(), AlertCenterRoutingModule]
})
export class AlertCenterModule {}
