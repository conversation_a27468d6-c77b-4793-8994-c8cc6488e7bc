import { After<PERSON>iew<PERSON>nit, Component, ElementRef, OnInit, QueryList, ViewChild, ViewChildren, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ChangeDetectorRef } from '@angular/core';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { FileFormat, typeInstruments } from 'src/app/constants/instruments.constants';
import { MessageCadastro, MessageInputInvalid, MessagePadroes, MessageUpload } from 'src/app/constants/message.constants';
import { Readings } from 'src/app/models/readings.model';
import { Units } from 'src/app/constants/readings.constants';

import { ClientService as ClientServiceApi } from 'src/app/services/api/client.service';
import { ClientUnitService as ClientUnitServiceApi } from 'src/app/services/api/clientUnit.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { ReadingService as ReadingServiceApi } from 'src/app/services/api/reading.service';
import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';

import { FormService } from 'src/app/services/form.service';
import { ReadingService } from 'src/app/services/reading.service';
import { UserService } from 'src/app/services/user.service';

import fn from 'src/app/utils/function.utils';
import * as uuid from 'uuid';
import { format } from 'date-fns';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-register-readings',
  templateUrl: './register-readings.component.html',
  styleUrls: ['./register-readings.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RegisterReadingsComponent implements OnInit {
  @ViewChild('modalInstructionsSpreadsheet') ModalInstructionsSpreadsheet: any;
  @ViewChildren('formReadingsRef') formReadingsRef: QueryList<ElementRef>;

  public formRegisterReadings: FormGroup = new FormGroup({
    client: new FormControl([], [Validators.required]),
    client_unit: new FormControl([], [Validators.required]),
    structure: new FormControl([], [Validators.required]),
    type_instrument: new FormControl(null, [Validators.required])
  });

  public formUnitsReadings: FormGroup = new FormGroup({
    unit_0: new FormControl('', [Validators.required]),
    unit_1: new FormControl('', [Validators.required]),
    attribute_datetime: new FormControl(''),
    file_format: new FormControl([], [Validators.required]),
    file_upload: new FormControl(null),
    instrument_sections: new FormControl(''),
    should_recalculate_stability: new FormControl(false)
  });

  public readingId: any = null;

  public typeInstrumentsList: any = typeInstruments;

  public filter: any = { ClientId: '', ClientUnitId: '' };

  public edit: boolean = false;
  public view: boolean = false;
  public beachLength: boolean = false;
  public spreadsheet: boolean = false;

  public clientSettings = MultiSelectDefault.Single;
  public instrumentSettings = MultiSelectDefault.Single;
  public structureSettings = MultiSelectDefault.Single;
  public unitSettings = MultiSelectDefault.Single;

  public singleSettingsModal = null;
  public fileFormats = FileFormat;
  public fileValid: boolean = false;

  public clients: any = [];
  public structures: any = [];
  public units: any = [];
  public instruments: any = [];
  public sections: any = [];
  public instrumentSections: any = [];

  public beachLengthId: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;
  public messageUpload: any = { msg: [], status: false, class: 'alert-danger', config: {} };

  public profile: any = null;
  public permissaoUsuario: any = null;

  public instrumentType: Readings;

  public elemFormReadings: any;

  public listFormReadings: any = [];

  public mesasureUnits: any = [];
  public unitsConstants = Units;
  public unitField = [null, null];
  public typeInstrument: any = null;
  public selectInstrument: any = null;
  public selectStructure: any = null;

  public datetime: any = null;

  public ctrlListFormReadings: boolean = true;

  public title: string = 'Nova(s) Leituras(s)';

  public tempFile: any = {};

  public func = fn;

  constructor(
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private clientServiceApi: ClientServiceApi,
    private clientUnitServiceApi: ClientUnitServiceApi,
    public formService: FormService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private readingServiceApi: ReadingServiceApi,
    private sectionsServiceApi: SectionsServiceApi,
    private structuresServiceApi: StructuresServiceApi,
    private userService: UserService,
    private readingService: ReadingService,
    private router: Router,
    private ngxSpinnerService: NgxSpinnerService
  ) {}

  ngOnInit(): void {
    this.readingService.resetReadings();
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;

    if (this.activatedRoute.snapshot.queryParams && this.activatedRoute.snapshot.queryParams.praia) {
      this.beachLength = true;

      this.typeInstrumentsList = [
        {
          name: 'Comprimento de praia',
          id: 11,
          subType: null,
          pressureCells: false,
          measuringPoints: false,
          magneticRings: false,
          fields: [],
          typeMeasure: '',
          nameMeasure: '',
          alias: 'PA'
        }
      ];
    }

    if (this.activatedRoute.snapshot.params.readingId) {
      this.edit = true;
      this.readingId = this.activatedRoute.snapshot.params.readingId;
      this.getReading(this.activatedRoute.snapshot.params.readingId);
      this.title = 'Editar Leitura(s)';
      if (this.activatedRoute.snapshot.url && this.activatedRoute.snapshot.url[1] && this.activatedRoute.snapshot.url[1].path == 'view') {
        this.edit = false;
        this.view = true;
        this.title = 'Visualizar Leitura(s)';
      }
    }

    this.singleSettingsModal = {
      singleSelection: true,
      idField: 'value',
      textField: 'label',
      selectAllText: 'Selecionar todos',
      unSelectAllText: 'Desmarcar seleção',
      searchPlaceholderText: 'Pesquisar...',
      itemsShowLimit: 5,
      allowSearchFilter: false,
      enableCheckAll: false,
      closeDropDownOnSelection: true,
      noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
      noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
    };

    this.loadFilter(this.formRegisterReadings, 'client', 'client_unit', 'structure', false);
  }

  /**
   * Obtém a lista de clientes e inicializa os valores dos formulários relacionados a clientes, unidades, estruturas e instrumentos.
   * Esse método limpa as listas de clientes, unidades, estruturas, instrumentos e seções, além de resetar os campos do formulário `formRegisterReadings`.
   * Após isso, faz uma requisição à API para obter a lista de clientes. Se houver apenas um cliente na lista, o método atualiza o formulário com esse cliente e chama `getUnits` para obter as unidades associadas a ele.
   * @returns {void}
   */
  getClients() {
    this.clients = [];
    this.units = [];
    this.structures = [];
    this.instruments = [];
    this.sections = [];

    this.formRegisterReadings.get('client').setValue('');
    this.formRegisterReadings.get('client_unit').setValue('');
    this.formRegisterReadings.get('structure').setValue('');
    this.formRegisterReadings.get('type_instrument').setValue('');

    this.clientServiceApi.getClientsList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;

      if (this.clients.length == 1) {
        this.formRegisterReadings.get('client').setValue(this.clients);
        this.getUnits(this.clients[0]);
      }
    });
  }

  /**
   * Obtém a lista de unidades para um cliente específico e inicializa os valores dos formulários relacionados a unidades, estruturas e instrumentos.
   * Esse método limpa as listas de unidades, estruturas, instrumentos e seções, além de resetar os campos do formulário `formRegisterReadings` relacionados às unidades, estruturas e instrumentos.
   * Se a ação especificada for 'select', faz uma requisição à API para obter as unidades do cliente. Se houver apenas uma unidade na lista, o método atualiza o formulário com essa unidade e chama `getStructures` para obter as estruturas associadas a ela.
   * @param {any} client - O cliente para o qual serão obtidas as unidades.
   * @param {string} [action='select'] - A ação a ser realizada, sendo 'select' o valor padrão.
   * @returns {void}
   */
  getUnits(client, action: string = 'select') {
    this.units = [];
    this.structures = [];
    this.instruments = [];
    this.sections = [];

    this.formRegisterReadings.get('client_unit').setValue('');
    this.formRegisterReadings.get('structure').setValue('');
    this.formRegisterReadings.get('type_instrument').setValue('');

    if (action === 'select') {
      this.clientUnitServiceApi.getClientUnitsId({ clientId: client.id, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.units = dados;
        if (this.units.length == 1) {
          this.formRegisterReadings.get('client_unit').setValue(this.units);
          this.getStructures(this.units[0]);
        }
      });
    }
  }

  /**
   * Obtém a lista de estruturas para uma unidade específica do cliente e inicializa os valores dos formulários relacionados a estruturas e instrumentos.
   * Esse método limpa as listas de estruturas, instrumentos e seções, além de resetar os campos do formulário `formRegisterReadings` relacionados às estruturas e instrumentos.
   * Se a ação especificada for 'select', faz uma requisição à API para obter as estruturas da unidade do cliente. Se houver apenas uma estrutura na lista, o método atualiza o formulário com essa estrutura.
   * @param {any} clientUnit - A unidade do cliente para a qual serão obtidas as estruturas.
   * @param {string} [action='select'] - A ação a ser realizada, sendo 'select' o valor padrão.
   * @returns {void}
   */
  getStructures(clientUnit, action: string = 'select') {
    this.structures = [];
    this.instruments = [];
    this.sections = [];

    this.formRegisterReadings.get('structure').setValue('');
    this.formRegisterReadings.get('type_instrument').setValue('');

    if (action === 'select') {
      let params = {
        clientUnitId: clientUnit.id,
        active: true
      };

      if (this.beachLength) {
        params['OnlyWithTailingsBeach'] = true;
      }

      this.structuresServiceApi.getStructureList(params).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.structures = dados;

        if (this.beachLength) {
          this.formRegisterReadings.controls['type_instrument'].setValue([{ id: 11, name: 'Comprimento de praia' }]);
          this.formRegisterReadings.controls['type_instrument'].disable();
          if (dados === null) {
            this.message.text = 'Não há estruturas com comprimento de praia.'; //MessagePadroes.NoRegister;
            this.message.status = true;
            this.message.class = 'alert-warning';

            setTimeout(() => {
              this.message.text = '';
              this.message.status = false;
              this.message.class = 'alert-success';
            }, 4000);
          }
        }

        if (this.structures && this.structures.length == 1) {
          this.formRegisterReadings.get('structure').setValue(this.structures);
          this.getListSelect(this.formRegisterReadings.get('type_instrument').value[0], 'select');
        }
      });
    }
  }

  /**
   * Obtém a lista de seções ou instrumentos com base no tipo de instrumento selecionado.
   * Esse método verifica se o tipo de instrumento é válido. Se for, chama o método apropriado para obter a lista de seções ou instrumentos.
   * @param {any} typeInstrument - O tipo de instrumento selecionado.
   * @param {string} [action='select'] - A ação a ser realizada, sendo 'select' o valor padrão.
   * @returns {void}
   */
  getListSelect(typeInstrument, action: string = 'select') {
    this.messageUpload = { msg: [], status: false, class: 'alert-danger', config: {} };

    if (typeInstrument != undefined) {
      if (typeInstrument.id != 11) {
        this.getInstruments(typeInstrument, action);
      } else if (typeInstrument.id == 11) {
        this.getSections(typeInstrument, action);
      }
    }
  }

  //Comprimento de praia
  /**
   * Obtém a lista de seções para uma estrutura específica com base no tipo de instrumento selecionado.
   * Esse método limpa as listas de seções e leituras, além de resetar o campo do formulário `typeInstrument`. Se a ação especificada for 'select', faz uma requisição à API para obter as seções da estrutura.
   * Se não houver seções registradas, exibe uma mensagem de alerta.
   * @param {any} typeInstrument - O tipo de instrumento selecionado.
   * @param {string} [action='select'] - A ação a ser realizada, sendo 'select' o valor padrão.
   * @returns {void}
   */
  getSections(typeInstrument, action: string = 'select') {
    //Pegar dados da estrutura
    if (typeInstrument != undefined) {
      let idx = fn.findIndexByValue(this.structures, this.formRegisterReadings.controls['structure'].value[0].id, 'id');
      this.selectStructure = this.structures[idx];
      this.sections = [];
      this.listFormReadings = [];
      this.typeInstrument = null;

      this.message.text = '';
      this.message.status = false;
      this.message.class = 'alert-success';

      if (action === 'select') {
        this.sectionsServiceApi
          .getSectionList({
            StructureId: this.formRegisterReadings.controls['structure'].value[0].id
          })
          .subscribe((resp: any) => {
            if (resp.status == 200) {
              let dados: any = resp;
              dados = dados.body === undefined ? dados : dados.body;
              this.sections = this.formatSection(dados, typeInstrument.id);
            } else if (resp.status == 204) {
              this.message.text = MessagePadroes.NoRegister;
              this.message.status = true;
              this.message.class = 'alert-warning';

              setTimeout(() => {
                this.message.text = '';
                this.message.status = false;
                this.message.class = 'alert-success';
              }, 4000);
            }
          });

        this.typeInstrument = typeInstrument;
        this.setUnits();
        this.addForm();
      }
    }
  }

  /**
   * Formata os dados das seções de acordo com o tipo de instrumento.
   * Esse método mapeia os dados das seções, retornando um array de objetos formatados com `id`, `identifier` e `type`.
   * @param {any} dados - Os dados das seções a serem formatados.
   * @param {number} typeInstrument - O tipo de instrumento.
   * @returns {any[]} - Array de seções formatadas.
   */
  formatSection(dados, typeInstrument) {
    return dados.map((section) => {
      return {
        id: section.id,
        identifier: section.name,
        type: typeInstrument
      };
    });
  }

  /**
   * Obtém a lista de instrumentos para uma estrutura específica com base no tipo de instrumento selecionado.
   * Esse método limpa as listas de instrumentos e leituras, além de resetar o campo do formulário `typeInstrument`. Se a ação especificada for 'select', faz uma requisição à API para obter os instrumentos da estrutura.
   * Se não houver instrumentos registrados, exibe uma mensagem de alerta.
   * @param {any} typeInstrument - O tipo de instrumento selecionado.
   * @param {string} [action='select'] - A ação a ser realizada, sendo 'select' o valor padrão.
   * @returns {void}
   */
  getInstruments(typeInstrument, action: string = 'select') {
    //Pegar dados da estrutura
    if (typeInstrument != undefined) {
      let idx = fn.findIndexByValue(this.structures, this.formRegisterReadings.controls['structure'].value[0].id, 'id');
      this.selectStructure = this.structures[idx];
      this.instruments = [];
      this.listFormReadings = [];
      this.typeInstrument = null;

      this.message.text = '';
      this.message.status = false;
      this.message.class = 'alert-success';

      if (action === 'select') {
        this.instrumentsServiceApi
          .getInstrumentsList({
            Type: typeInstrument.id,
            StructureId: this.formRegisterReadings.controls['structure'].value[0].id
          })
          .subscribe((resp: any) => {
            if (resp.status == 200) {
              let dados: any = resp;
              dados = dados.body === undefined ? dados : dados.body;
              this.instruments = dados;
            } else if (resp.status == 204) {
              this.message.text = MessagePadroes.NoRegister;
              this.message.status = true;
              this.message.class = 'alert-warning';

              setTimeout(() => {
                this.message.text = '';
                this.message.status = false;
                this.message.class = 'alert-success';
              }, 4000);
            }
          });

        this.typeInstrument = typeInstrument;
        this.setUnits();
        this.addForm();
      }
    }
  }

  /**
   * Configura as unidades de medida para a leitura de acordo com o tipo de instrumento.
   * Esse método atualiza os valores dos campos de unidade no formulário `formUnitsReadings` com base nas constantes de unidade configuradas para o tipo de instrumento.
   * @returns {void}
   */
  setUnits() {
    this.mesasureUnits = this.unitsConstants[0][this.typeInstrument.id];
    this.formUnitsReadings.controls['unit_0'].setValue(this.mesasureUnits[0] ? this.mesasureUnits[0].default : '');
    this.formUnitsReadings.controls['unit_1'].setValue(this.mesasureUnits[1] ? this.mesasureUnits[1].default : '');

    this.changeUnit();
  }

  /**
   * Configura a data e hora para a leitura.
   * Esse método atualiza o valor da propriedade `datetime` com base no campo de data e hora do formulário `formUnitsReadings`.
   * @returns {void}
   */
  setDatetime() {
    this.datetime = this.formUnitsReadings.controls['attribute_datetime'].value;
  }

  get canShowDatetimeButton(): boolean {
    const values = this.listFormReadings?.[0]?.data?.values;
    const hasMultipleMeasurementPoints = Array.isArray(values) && values.length > 1;
    return (this.typeInstrument != null && !this.view && (!this.edit || hasMultipleMeasurementPoints)) || this.spreadsheet;
  }

  /**
   * Atualiza as unidades de medida de acordo com as seleções feitas no formulário.
   * Esse método atualiza o campo `unitField` com as unidades selecionadas no formulário `formUnitsReadings`.
   * @returns {void}
   */
  changeUnit() {
    let changeUnit = [null, null];
    changeUnit[0] = this.formUnitsReadings.controls['unit_0'].value;
    changeUnit[1] = this.formUnitsReadings.controls['unit_1'].value != '' ? this.formUnitsReadings.controls['unit_1'].value : null;

    this.unitField = changeUnit;
  }

  /**
   * Adiciona um novo formulário de leitura à lista.
   * Esse método cria um novo identificador único (uidForm) e adiciona um novo formulário de leitura à lista `listFormReadings`.
   * @param {string} [option=null] - Opção para especificar se o formulário deve ser adicionado via botão.
   * @returns {void}
   */
  addForm(option = null) {
    if (option == 'button') {
      this.spreadsheet = false;
    }
    let uidForm = uuid.v4();
    this.listFormReadings.push({ uidForm: uidForm });
  }

  /**
   * Remove todos os formulários de leitura da lista.
   * Esse método limpa a lista `listFormReadings` e adiciona um novo formulário.
   * @returns {void}
   */
  removeAllForm() {
    this.listFormReadings = [];
    this.addForm();
  }

  /**
   * Remove um formulário de leitura específico da lista.
   * Esse método encontra o índice do formulário de leitura com o identificador `uidForm` e o remove da lista `listFormReadings`.
   * Se a lista ficar vazia, adiciona um novo formulário.
   * @param {string} uidForm - O identificador único do formulário de leitura a ser removido.
   * @returns {void}
   */
  removeForm(uidForm) {
    let idx = this.readingService.findIndexItemByIdandValue(this.listFormReadings, 'uidForm', uidForm);
    if (idx !== -1) {
      this.listFormReadings.splice(idx, 1);
    }
    if (this.listFormReadings.length === 0) {
      this.addForm();
    }
  }

  /**
   * Adiciona leituras à lista de leituras.
   * Esse método obtém os dados de leitura do formulário e chama o método apropriado para registrar ou editar as leituras, dependendo do modo (edição ou cadastro).
   * @returns {void}
   */
  addReadings() {
    if (!this.beachLength) {
      let respReadings = this.readingService.getReadings(this.formReadingsRef.toArray());
      if (respReadings.valid) {
        if (!this.edit) {
          this.registerReadings(respReadings.data);
        } else {
          respReadings.data[0].should_recalculate_stability = this.formUnitsReadings.controls['should_recalculate_stability'].value;
          this.editReadings(respReadings.data[0]);
        }
      }
    } else {
      let respReadings = this.readingService.getReadingsSections(this.formReadingsRef.toArray());
      if (respReadings.valid) {
        if (!this.edit) {
          this.registerReadingsSections(respReadings.data);
        } else {
          this.editReadingsSections(respReadings.data);
        }
      }
    }
  }

  /**
   * Obtém uma leitura específica pelo seu identificador.
   * Esse método faz uma requisição à API para obter os dados de leitura com base no identificador fornecido. Dependendo do tipo de leitura (comprimento de praia ou não), chama o método apropriado.
   * @param {string} [readingId=''] - O identificador da leitura a ser obtida.
   * @returns {void}
   */
  getReading(readingId: string = '') {
    this.ngxSpinnerService.show();

    let method = !this.beachLength ? 'getReadingById' : 'getBeachLengthById';

    this.readingServiceApi[method](readingId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      // this.getSectionsFromInstument(dados.instrument.id);
      this.splitData(dados);
      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Registra leituras de comprimento de praia.
   * Esse método faz uma requisição à API para registrar leituras de comprimento de praia. Em caso de sucesso, exibe uma mensagem de sucesso e redireciona para a página de leituras.
   * Em caso de erro, exibe mensagens de erro.
   * @param {any} data - Os dados das leituras de comprimento de praia a serem registrados.
   * @returns {void}
   */
  registerReadingsSections(data) {
    this.ngxSpinnerService.show();
    this.messagesError = [];

    this.readingServiceApi.postBeachLength(data).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.SucessoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.text = '';
          this.message.status = false;
          this.message.class = 'alert-success';

          this.router.navigate(['/readings']);
        }, 4000);
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
          setTimeout(() => {
            this.messagesError = [];
          }, 5000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Registra leituras.
   * Esse método faz uma requisição à API para registrar leituras. Em caso de sucesso, exibe uma mensagem de sucesso e redireciona para a página de leituras. Em caso de erro, exibe mensagens de erro.
   * @param {any} data - Os dados das leituras a serem registradas.
   * @returns {void}
   */
  registerReadings(data) {
    this.ngxSpinnerService.show();
    this.messagesError = [];

    this.readingServiceApi.postReadings(data).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.SucessoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.text = '';
          this.message.status = false;
          this.message.class = 'alert-success';
          this.router.navigate(['/readings']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
          setTimeout(() => {
            this.messagesError = [];
          }, 5000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Edita leituras.
   * Esse método faz uma requisição à API para editar leituras. Em caso de sucesso, exibe uma mensagem de sucesso e redireciona para a página de leituras. Em caso de erro, exibe mensagens de erro.
   * @param {any} data - Os dados das leituras a serem editadas.
   * @returns {void}
   */
  editReadings(data) {
    this.ngxSpinnerService.show();
    this.messagesError = [];

    this.readingServiceApi.putReadings(data).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.text = '';
          this.message.status = false;
          this.message.class = 'alert-success';
          this.router.navigate(['/readings']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
          setTimeout(() => {
            this.messagesError = [];
          }, 5000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Edita leituras de comprimento de praia.
   * Esse método faz uma requisição à API para editar leituras de comprimento de praia. Em caso de sucesso, exibe uma mensagem de sucesso e redireciona para a página de leituras. Em caso de erro, exibe mensagens de erro.
   * @param {any} params - Os dados das leituras de comprimento de praia a serem editadas.
   * @returns {void}
   */
  editReadingsSections(params: any) {
    this.ngxSpinnerService.show();
    this.messagesError = [];

    this.readingServiceApi.putBeachLength(params.id, params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.text = '';
          this.message.status = false;
          this.message.class = 'alert-success';
          this.router.navigate(['/readings']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
          setTimeout(() => {
            this.messagesError = [];
          }, 5000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Valida os formulários e adiciona leituras se válidas.
   * Esse método valida os formulários `formRegisterReadings` e os formulários de leitura. Se válidos, chama `addReadings` para adicionar as leituras.
   * Exibe mensagens de erro se a validação falhar.
   * @returns {void}
   */
  validate() {
    let formValid = !this.edit ? this.formService.validateForm(this.formRegisterReadings) : true;
    let formValidReadings = true;
    let formReadings = this.formReadingsRef.toArray();

    formReadings.forEach((formReading: any) => {
      formReading.readingInstrumentRef.toArray().forEach((reading: any) => {
        let ctrlValid = this.formService.validateForm(reading.formReading);
        if (!ctrlValid) {
          reading.formReading.markAllAsTouched();
          if (formValidReadings) {
            formValidReadings = false;
          }
        }
      });
    });

    this.messagesError = [];

    if (!formValid || !formValidReadings) {
      if (!this.edit) {
        this.formRegisterReadings.markAllAsTouched();
      }
      this.messagesError = [{ message: MessageInputInvalid.RegisterInvalid }];
      setTimeout(() => {
        this.messagesError = [];
      }, 5000);
    } else {
      this.addReadings();
    }
  }

  /**
   * Divide os dados em partes para exibição e processamento.
   * Esse método formata e adiciona os dados divididos à lista `listFormReadings`, configurando os campos apropriados no formulário.
   * @param {any} $dados - Os dados a serem divididos.
   * @returns {void}
   */
  splitData($dados) {
    if (!this.spreadsheet) {
      $dados = [$dados];
      this.formRegisterReadings.controls['type_instrument'].disable();
    } else {
      //this.getListSelect(this.formRegisterReadings.get('type_instrument').value[0], 'select');
      $dados = $dados.hasOwnProperty('readings') ? $dados.readings : $dados;
      this.readingService.allReadings = $dados;
      $dados = $dados.slice(0, 100); // Exibir somente os 100 primeiros itens da planilha
      this.view = true; //Para bloquear os campos do cadastro via planilha
      this.listFormReadings = [];
    }

    let count = 1;
    const total = $dados.length;
    this.ctrlListFormReadings = false;

    $dados.forEach((dado, index) => {
      let id = !this.spreadsheet ? dado.id : uuid.v4();

      if (!this.beachLength) {
        this.typeInstrument = fn.findIndexInArrayofObject(this.typeInstrumentsList, 'id', dado.instrument.type, 'id', true);
        this.instruments = [dado.instrument];
      } else {
        //Quando for praia
        this.sections = this.formatSection([dado.section], 11);
        this.typeInstrument = fn.findIndexInArrayofObject(this.typeInstrumentsList, 'id', 11, 'id', true);
      }
      this.listFormReadings.push({ uidForm: id, data: dado, instruments: this.instruments, sections: this.sections });
      if (count == total) {
        this.ctrlListFormReadings = true;
        this.formRegisterReadings.controls['type_instrument'].setValue([this.typeInstrument]);
        this.setUnits();
      }
      count++;
    });

    this.cdr.detectChanges();
  }

  /**
   * Faz o download de um template de planilha.
   * Esse método faz uma requisição à API para baixar um template de planilha com base nos parâmetros de tipo de instrumento e formato de arquivo selecionados.
   * @returns {void}
   */
  getDownloadTemplate() {
    let params = {
      InstrumentType: this.formRegisterReadings.controls['type_instrument'].value[0].id,
      FileFormat: this.formUnitsReadings.controls['file_format'].value[0].value
    };

    let idx = fn.findIndexInArrayofObject(this.typeInstrumentsList, 'id', params.InstrumentType);

    this.readingServiceApi.getReadingsFileDownloadTemplate(params).subscribe(
      (resp) => {
        if (resp['status'] == 200) {
          let extension = resp['body'].type == 'text/csv' ? '.csv' : '.xlsx';
          let fileName = this.typeInstrumentsList[idx].alias + '_template_' + format(new Date(), 'ddMMyyyyHH:mm:ss') + extension;
          this.downloadFile(resp['body'], resp['body'].type, fileName);
        }
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
          setTimeout(() => {
            this.messagesError = [];
          }, 5000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Processa o upload de um arquivo de template de leituras.
   * Esse método processa o upload de um arquivo de template de leituras, exibindo mensagens de sucesso ou erro conforme o resultado do processamento.
   * @param {any} event - O evento de upload de arquivo.
   * @returns {void}
   */
  getReadingsFileUpload() {
    this.tempFile = {};
    if (this.fileValid) {
      this.ctrlListFormReadings = false;

      let queryParams = {
        structureId: this.formRegisterReadings.controls['structure'].value[0].id
      };
      const formData = new FormData();

      formData.append('file', this.formUnitsReadings.controls['file_upload'].value);

      this.readingServiceApi.postReadingsFileConvertToAdd(formData, queryParams).subscribe(
        (resp) => {
          const contentType = resp['headers'].get('Content-Type');
          if (contentType && contentType.includes('application/json')) {
            resp['body'].text().then((text: string) => {
              try {
                let dados = JSON.parse(text);
                this.spreadsheet = true;
                this.splitData(dados);
              } catch (error) {
                console.error('Erro ao analisar JSON', error);
              }
            });
          } else {
            const file = this.formUnitsReadings.controls['file_upload'].value;
            const fileNamePart = file.name.split('.');
            const extension = fileNamePart.pop();
            const fileName = fileNamePart.join('') + '_' + format(new Date(), 'ddMMyyyyHH:mm:ss') + '.' + extension;

            this.tempFile = {
              body: resp['body'],
              type: resp['body'].type,
              name: fileName
            };

            this.messageUpload = { msg: [{ message: MessageUpload.ErroPlanilhaLeitura }], status: true, class: 'alert-warning', config: {} };
          }
        },
        (error) => {
          if (error.status !== 200) {
            this.messagesError = [];
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
            setTimeout(() => {
              this.messagesError = [];
            }, 5000);
          } else if (error.status === 200) {
            console.log('erro 200');
          }
        }
      );
    }
  }

  /**
   * Faz o download de um arquivo com base nos dados fornecidos.
   * Esse método cria um objeto Blob a partir dos dados e do tipo de arquivo especificados, gera uma URL para o Blob e dispara o download do arquivo usando um elemento âncora temporário.
   * @param {any} data - Os dados a serem incluídos no arquivo.
   * @param {string} [type='text/csv'] - O tipo de arquivo, podendo ser 'text/csv' ou 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'.
   * @param {string} fileName - O nome do arquivo a ser baixado.
   * @returns {void}
   */
  downloadFile(data: any, type: string = 'text/csv', fileName: string) {
    type = type == 'text/csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    const blob = new Blob([data], { type: type });
    const url = window.URL.createObjectURL(blob);
    //window.open(url);
    let anchor = document.createElement('a');
    anchor.download = fileName;
    anchor.href = url;
    anchor.click();
  }

  /**
   * Processa o upload de um arquivo.
   * Esse método obtém o arquivo carregado a partir do evento de upload, verifica a extensão do arquivo para garantir que seja um arquivo válido (xlsx ou csv) e atualiza o formulário com o arquivo.
   * Se o arquivo não for válido, adiciona uma mensagem de erro.
   * @param {any} $event - O evento de upload de arquivo.
   * @returns {void}
   */
  uploadFile($event: any) {
    let file = $event.dataTransfer ? $event.dataTransfer.files[0] : $event.target.files[0];

    if (file && file.name) {
      let extension = file.name.split('.');
      this.fileValid = ['xlsx', 'csv'].includes(extension[extension.length - 1]);

      if (this.fileValid) {
        this.formUnitsReadings.controls['file_upload'].setValue(file);
      } else {
        this.messagesError = [];
        this.messagesError.push({
          code: '000',
          message: MessageInputInvalid.NoFile
        });
      }
    } else {
      this.fileValid = false;
    }
  }

  /**
   * Manipula eventos de clique.
   * Esse método verifica a ação especificada no evento de clique e executa a ação correspondente. No caso da ação 'downloadPlanilhaErros', faz o download de um arquivo de erros temporário.
   * @param {any} $event - O evento de clique.
   * @returns {void}
   */
  clickEvent($event) {
    switch ($event.action) {
      case 'downloadPlanilhaErros':
        if (this.tempFile.hasOwnProperty('body')) {
          this.downloadFile(this.tempFile.body, this.tempFile.type, this.tempFile.name);
          this.message = { text: '', status: false, class: 'alert-success' };
        }
        break;
    }
  }

  /**
   * Carrega os filtros salvos no `localStorage` e preenche os campos do formulário com base nos dados disponíveis.
   * Verifica se há valores salvos para ClientId, ClientUnitId, e StructureId, e preenche os campos correspondentes no formulário.
   * Se os valores existirem, também busca as unidades e estruturas associadas.
   *
   * @param {FormGroup} $form - O formulário que será preenchido com os filtros.
   * @param {string} client - O nome do campo de cliente no formulário.
   * @param {string} unit - O nome do campo de unidade no formulário.
   * @param {string} structure - O nome do campo de estrutura no formulário.
   */
  loadFilter($form, client, unit, structure, onlyId = false) {
    if ($form.get('client')?.value?.length !== 0) {
      this.getClients();
    }

    const savedFilters = localStorage.getItem('filterHierarchy');
    if (savedFilters && !this.edit && !this.view) {
      const filterHierarchy = JSON.parse(savedFilters);
      const configClient = {
        value: onlyId ? filterHierarchy.ClientId.id : [{ id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }],
        param: onlyId ? filterHierarchy.ClientId.id : { id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }
      };

      const configClientUnit = {
        value: onlyId ? filterHierarchy.ClientUnitId.id : [{ id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }],
        param: onlyId ? filterHierarchy.ClientUnitId.id : { id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }
      };

      const configStructure = {
        value: onlyId ? filterHierarchy.StructureId.id : [{ id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }],
        param: onlyId ? filterHierarchy.StructureId.id : { id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }
      };

      // Verificar se existe ClientId, ClientUnitId, e StructureId e preenchê-los
      if (filterHierarchy.ClientId) {
        $form.get(client)?.setValue(configClient.value);
        this.getUnits(configClient.param);
      }

      if (filterHierarchy.ClientUnitId) {
        $form.get(unit)?.setValue(configClientUnit.value);
        this.getStructures(configClientUnit.param);
      }

      if (filterHierarchy.StructureId) {
        $form.get(structure)?.setValue(configStructure.value);
        setTimeout(() => {
          this.getListSelect($form.get('type_instrument').value[0], 'select');
        }, 500);
      }
    }
  }
}
