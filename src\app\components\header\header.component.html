<header class="header-logisoil">
  <p class="header-text">Be<PERSON>-vindo (a), {{ user.name }}.</p>
  <a href="#" class="logo-logisoil">
    <img
      src="/assets/images/logoLogisoil.png"
      alt="Logisoil"
      title="Logisoil"
    />
  </a>
  <nav>
    <ul class="header-menu">
      <li>
        <a class="logo-client" id="logo">
          <img
            [src]="
              isValidLogo(logoContent)
                ? logoContent
                : '/assets/ico/WL_logo_walm.svg'
            "
            [alt]="logoName || 'Logo Walm'"
            height="20"
          />
        </a>
      </li>

      <!-- Menu Estrutura/Unidade -->
      <li
        class="dropdown-structure"
        (clickOutside)="onClickedOutside('structure')"
      >
        <button
          type="button"
          class="btn btn-structure d-flex justify-content-around align-items-center"
          (click)="dropdownStructure = !dropdownStructure"
        >
          <img src="/assets/ico/ico-menu/estruturas.svg" height="16px" />
          <img src="/assets/ico/ico-menu/unidades.svg" height="16px" />
          <img src="/assets/ico/search.svg" height="16px" />
        </button>

        <ul
          [formGroup]="filterForm"
          [ngClass]="dropdownStructure ? 'active' : ''"
        >
          <!-- Cliente -->
          <li>
            <select
              class="form-select form-item"
              formControlName="ClientId"
              (change)="getUnits(filterForm.get('ClientId')?.value)"
            >
              <option value="">Cliente:</option>
              <option *ngFor="let client of clients" [value]="client.id">
                {{ client.name }}
              </option>
            </select>
          </li>

          <!-- Unidade -->
          <li>
            <select
              class="form-select form-item"
              formControlName="ClientUnitId"
              (change)="getStructures(filterForm.get('ClientUnitId')?.value)"
            >
              <option value="">Unidade:</option>
              <option *ngFor="let unit of units" [value]="unit.id">
                {{ unit.name }}
              </option>
            </select>
          </li>

          <!-- Estrutura -->
          <li>
            <select class="form-select form-item" formControlName="StructureId">
              <option value="">Estrutura:</option>
              <option
                *ngFor="let structure of structures"
                [value]="structure.id"
              >
                {{ structure.name }}
              </option>
            </select>
          </li>
          <li>
            <app-button
              [class]="'btn-search'"
              [label]="'Carregar'"
              (click)="saveHierarchy(); onClickedOutside('structure')"
            ></app-button>
          </li>
        </ul>
      </li>

      <!-- Notificações -->
      <li>
        <app-notifications></app-notifications>
      </li>

      <!-- Menu Profile -->
      <li class="dropdown-profile" (clickOutside)="onClickedOutside('profile')">
        <app-button
          [class]="'btn-logisoil'"
          [icon]="'fa fa-cog'"
          [title]="'Meu Perfil'"
          (click)="dropdownProfile = !dropdownProfile"
        ></app-button>
        <ul [ngClass]="dropdownProfile ? 'active' : ''">
          <ng-template
            ngFor
            let-menuProfile
            [ngForOf]="menuProfile"
            let-i="index"
          >
            <li>
              <a href="#" (click)="openModal(menuProfile.Click)">
                <img
                  *ngIf="menuProfile.IconeType === 'svg'"
                  src="/assets/ico/ico-menu/{{ menuMiniDashboard.Icone }}"
                />
                <i
                  *ngIf="menuProfile.IconeType !== 'svg'"
                  [ngClass]="menuProfile.Icone"
                ></i>
                <span>{{ menuProfile.Titulo }}</span>
              </a>
            </li>
          </ng-template>
        </ul>
      </li>
      <!-- Logout -->
      <li>
        <app-button
          [class]="'btn-logisoil'"
          [icon]="'fa fa-sign-out'"
          [title]="'Sair'"
          (click)="auth.logout()"
        ></app-button>
      </li>
    </ul>
  </nav>
</header>
<app-modal-profile #modalProfile [user]="user"></app-modal-profile>
<app-modal-termo #modalTermo [user]="user"></app-modal-termo>
