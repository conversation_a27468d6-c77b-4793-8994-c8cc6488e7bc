<ng-template #modalAttachments let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">Anexos</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click'); reset()"
    ></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="newAttachmentForm" *ngIf="!view">
      <div class="row">
        <!-- Upload de Arquivo -->
        <div class="col-md-6">
          <label class="form-label">Anexar novo arquivo</label>
          <input
            type="file"
            class="form-control"
            #fileInput
            id="fileInput"
            (change)="onFileSelected($event)"
          />
          <small class="text-muted">
            Arquivo selecionado:
            {{ newAttachmentForm.get('file.name')?.value || 'Nenhum arquivo' }}
          </small>
        </div>
      </div>

      <div class="row mt-3">
        <label class="form-label fw-bold d-block"
          >Datum da Estrutura:
          {{
            hierarchy?.structure?.coordinate_setting?.datum_description
          }}</label
        >
        <!-- Coordenadas -->
        <div class="col-md-12">
          <div class="input-group mb-2">
            <span class="input-group-text">Coordenada E (m)</span>
            <input type="text" class="form-control" formControlName="easting" />
            <span class="input-group-text">Coordenada N (m)</span>
            <input
              type="text"
              class="form-control"
              formControlName="northing"
            />
            <app-button
              [class]="'btn-logisoil-blue me-1'"
              [icon]="'fa fa-map-location-dot'"
              (click)="showMap($event)"
            ></app-button>
          </div>
        </div>
      </div>

      <!-- Adicionar Anexo -->
      <div class="row my-3">
        <div class="col-md-12 d-flex justify-content-end">
          <app-button
            [class]="'btn-logisoil-green'"
            [label]="'Adicionar Anexo'"
            [disabled]="
              !newAttachmentForm.valid ||
              !newAttachmentForm.get('northing')?.value ||
              !newAttachmentForm.get('easting')?.value ||
              !newAttachmentForm.get('file.name')?.value
            "
            (click)="addAttachment()"
          ></app-button>
        </div>
      </div>
    </form>

    <!-- Tabela de anexos existentes -->
    <form [formGroup]="occurrenceAttachmentsForm">
      <div formArrayName="occurrence_attachments">
        <div class="row">
          <div class="col-md-12">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>Anexo</th>
                  <th class="text-center align-middle">Coordenadas</th>
                  <th class="text-center align-middle" *ngIf="!view">Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="let attachment of attachments.controls; let i = index"
                  [formGroupName]="i"
                >
                  <td>
                    <img
                      [src]="attachment.get('file').value.base64"
                      alt="thumbnail"
                      style="width: 50px; height: 50px"
                    />
                    <br />
                    <span>{{ attachment.get('file').value.name }}</span>
                  </td>
                  <td class="text-center align-middle">
                    {{ attachment.get('northing').value }},
                    {{ attachment.get('easting').value }}
                  </td>
                  <td class="text-center align-middle" *ngIf="!view">
                    <app-button
                      [class]="'btn-logisoil-red btn-sm me-2'"
                      [icon]="'fa fa-trash'"
                      (click)="removeAttachment(i)"
                    ></app-button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Botões -->
  <div class="modal-footer">
    <app-button
      [class]="!view ? 'btn-logisoil-red' : 'btn-logisoil-blue'"
      [icon]="'fa fa-thin fa-xmark'"
      [label]="!view ? 'Cancelar' : 'Fechar'"
      (click)="c('Close click'); reset()"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-green'"
      [icon]="'fa fa-thin fa-floppy-disk'"
      [label]="'Salvar'"
      (click)="save()"
      *ngIf="!view"
    >
    </app-button>
  </div>
</ng-template>
