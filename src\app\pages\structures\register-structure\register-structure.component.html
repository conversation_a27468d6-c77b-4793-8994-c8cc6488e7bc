<div class="list-content">
  <!-- Alerta -->
  <div
    class="alert mt-3"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status"
    [innerHTML]="message.text"
  ></div>

  <div class="row g-3 mt-2">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="generalTabConfig.active ? 'active' : ''"
          id="general-tab"
          type="button"
          role="tab"
          aria-controls="general"
          aria-selected="true"
          (click)="crtlSaveStructure = ''; selectTab('general')"
          [style.background-color]="
            generalTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="generalTabConfig.styleColor ? '#ffffff' : ''"
        >
          Geral
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="responsibleTabConfig.active ? 'active' : ''"
          id="responsible-tab"
          type="button"
          role="tab"
          aria-controls="responsible"
          aria-selected="false"
          (click)="crtlSaveStructure = ''; selectTab('responsible')"
          [style.background-color]="
            responsibleTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="responsibleTabConfig.styleColor ? '#ffffff' : ''"
        >
          Responsáveis
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="datasheetTabConfig.active ? 'active' : ''"
          id="datasheet-tab"
          type="button"
          role="tab"
          aria-controls="datasheet"
          aria-selected="false"
          (click)="crtlSaveStructure = ''; selectTab('datasheet')"
          [style.background-color]="
            datasheetTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="datasheetTabConfig.styleColor ? '#ffffff' : ''"
        >
          Ficha técnica
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="inspectionsTabConfig.active ? 'active' : ''"
          id="inspections-tab"
          type="button"
          role="tab"
          aria-controls="inspections"
          aria-selected="false"
          (click)="crtlSaveStructure = ''; selectTab('inspections')"
          [style.background-color]="
            inspectionsTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="inspectionsTabConfig.styleColor ? '#ffffff' : ''"
        >
          Inspeções
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="stabilityTabConfig.active ? 'active' : ''"
          id="stability-tab"
          type="button"
          role="tab"
          aria-controls="stability"
          aria-selected="false"
          (click)="crtlSaveStructure = ''; selectTab('stability')"
          [style.background-color]="
            stabilityTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="stabilityTabConfig.styleColor ? '#ffffff' : ''"
        >
          Estabilidade
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="avancedTabConfig.active ? 'active' : ''"
          id="avanced-tab"
          type="button"
          role="tab"
          aria-controls="avanced"
          aria-selected="false"
          (click)="crtlSaveStructure = ''; selectTab('avanced')"
          [style.background-color]="
            avancedTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="avancedTabConfig.styleColor ? '#ffffff' : ''"
        >
          Avançado
        </button>
      </li>
      <li class="nav-item" role="presentation" *ngIf="permissaoUsuario.layers">
        <button
          class="nav-link"
          [ngClass]="layersTabConfig.active ? 'active' : ''"
          id="layers-tab"
          type="button"
          role="tab"
          aria-controls="layers"
          aria-selected="false"
          (click)="crtlSaveStructure = ''; selectTab('layers')"
          [style.background-color]="layersTabConfig.styleColor ? '#dc3545' : ''"
          [style.color]="layersTabConfig.styleColor ? '#ffffff' : ''"
        >
          Layers
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          [ngClass]="configurationsTabConfig.active ? 'active' : ''"
          id="configurations-tab"
          type="button"
          role="tab"
          aria-controls="configurations"
          aria-selected="false"
          (click)="
            crtlSaveStructure = 'configurations-tab';
            selectTab('configurations')
          "
          [style.background-color]="
            configurationsTabConfig.styleColor ? '#dc3545' : ''
          "
          [style.color]="configurationsTabConfig.styleColor ? '#ffffff' : ''"
        >
          Configurações do Slide2
        </button>
      </li>
    </ul>
    <div class="tab-content" id="myTabContent">
      <!-- Aba Geral -->
      <div
        class="tab-pane fade"
        [ngClass]="generalTabConfig.active ? 'show active' : ''"
        id="general"
        role="tabpanel"
        aria-labelledby="general-tab"
      >
        <app-general-tab
          [view]="view"
          [profile]="profile"
          [permissaoUsuario]="permissaoUsuario"
        ></app-general-tab>
      </div>
      <!-- Aba Responsáveis -->
      <div
        class="tab-pane fade"
        [ngClass]="responsibleTabConfig.active ? 'show active' : ''"
        id="responsible"
        role="tabpanel"
        aria-labelledby="responsible-tab"
      >
        <app-responsible-tab
          [view]="view"
          [permissaoUsuario]="permissaoUsuario"
        ></app-responsible-tab>
      </div>
      <!-- Aba Ficha Técnica -->
      <div
        class="tab-pane fade"
        [ngClass]="datasheetTabConfig.active ? 'show active' : ''"
        id="datasheet"
        role="tabpanel"
        aria-labelledby="datasheet-tab"
      >
        <app-datasheet-tab
          [view]="view"
          [profile]="profile"
          [permissaoUsuario]="permissaoUsuario"
        ></app-datasheet-tab>
      </div>
      <!-- Aba Inspeções -->
      <div
        class="tab-pane fade"
        [ngClass]="inspectionsTabConfig.active ? 'show active' : ''"
        id="inspections"
        role="tabpanel"
        aria-labelledby="inspections-tab"
      >
        <app-inspections-tab
          [view]="view"
          [profile]="profile"
          [permissaoUsuario]="permissaoUsuario"
        ></app-inspections-tab>
      </div>
      <!-- Aba Estabilidade -->
      <div
        class="tab-pane fade"
        [ngClass]="stabilityTabConfig.active ? 'show active' : ''"
        id="stability"
        role="tabpanel"
        aria-labelledby="stability-tab"
      >
        <app-stability-tab
          [view]="view"
          [profile]="profile"
          [permissaoUsuario]="permissaoUsuario"
        ></app-stability-tab>
      </div>
      <!-- Aba Avançado -->
      <div
        class="tab-pane fade"
        [ngClass]="avancedTabConfig.active ? 'show active' : ''"
        id="avanced"
        role="tabpanel"
        aria-labelledby="avanced-tab"
      >
        <app-avanced-tab
          [view]="view"
          [edit]="edit"
          [profile]="profile"
          [permissaoUsuario]="permissaoUsuario"
        ></app-avanced-tab>
      </div>
      <!-- Aba Layers -->
      <div
        class="tab-pane fade"
        [ngClass]="layersTabConfig.active ? 'show active' : ''"
        id="layers"
        role="tabpanel"
        aria-labelledby="layers-tab"
      >
        <app-layers-tab
          [view]="view"
          [edit]="edit"
          [profile]="profile"
          [permissaoUsuario]="permissaoUsuario"
        ></app-layers-tab>
      </div>
      <!-- Aba Configurações de Análise do Slide2 -->
      <div
        class="tab-pane fade"
        [ngClass]="configurationsTabConfig.active ? 'show active' : ''"
        id="configurations"
        role="tabpanel"
        aria-labelledby="configurations-tab"
      >
        <app-configurations-tab [view]="view"></app-configurations-tab>
      </div>
    </div>
  </div>

  <div class="row mt-2">
    <!-- Mensagens de erro -->
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <!-- Botões -->
  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-green'"
      [icon]="'fa fa-thin fa-floppy-disk'"
      [label]="'Salvar'"
      [type]="false"
      class="me-1"
      *ngIf="crtlSaveStructure != '' && !view && formCrtl"
      (click)="validate()"
    >
    </app-button>
    <!-- Botão Retornar -->
    <app-button
      *ngIf="!generalTabConfig.active"
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Retornar'"
      class="me-1"
      (click)="onPrevious()"
    ></app-button>
    <!-- Botão Avançar -->
    <app-button
      *ngIf="!configurationsTabConfig.active"
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-right'"
      [label]="'Avançar'"
      class="me-1"
      (click)="onNext()"
    ></app-button>
  </div>
  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      class="me-1"
      [routerLink]="['/structures']"
    ></app-button>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
