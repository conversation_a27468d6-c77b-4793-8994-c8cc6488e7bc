import { Component, OnInit, ViewChild } from '@angular/core';

import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';
import { DataService } from 'src/app/services/data.service';

import { MessagePadroes } from 'src/app/constants/message.constants';
import { InspectionSheetStatus } from 'src/app/constants/inspections.constants';

import { ConservationStatusComponent } from '../register-inspection-sheet/tabs/conservation-status/conservation-status.component';
import { ActionsExecutedComponent } from '../register-inspection-sheet/tabs/actions-executed/actions-executed.component';
import { CurrentSituationComponent } from '../register-inspection-sheet/tabs/current-situation/current-situation.component';
import { GeneralObservationsComponent } from '../register-inspection-sheet/tabs/general-observations/general-observations.component';
import { AspectsObservedComponent } from '../register-inspection-sheet/tabs/aspects-observed/aspects-observed.component';
import { PreviousSituationComponent } from '../register-inspection-sheet/tabs/previous-situation/previous-situation.component';

import { ActivatedRoute } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';

import { finalize, map, Observable, of, switchMap } from 'rxjs';
import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';

@Component({
  selector: 'app-view-inspection-sheet',
  templateUrl: './view-inspection-sheet.component.html',
  styleUrls: ['./view-inspection-sheet.component.scss']
})
export class ViewInspectionSheetComponent implements OnInit {
  @ViewChild(PreviousSituationComponent) previousSituationTab: PreviousSituationComponent;
  @ViewChild(AspectsObservedComponent) aspectsObservedTab: AspectsObservedComponent;
  @ViewChild(ConservationStatusComponent) conservationStatusTab: ConservationStatusComponent;
  @ViewChild(GeneralObservationsComponent) generalObservationsTab: GeneralObservationsComponent;
  @ViewChild(ActionsExecutedComponent) actionsExecutedTab: ActionsExecutedComponent;
  @ViewChild(CurrentSituationComponent) currentSituationTab: CurrentSituationComponent;

  public dados: any = null;
  public message: any = { text: '', status: false, class: 'alert-success' };
  public load: boolean | null = null;

  public inspectionSheetType: number = null;

  public startDateForm = {
    label: 'Início da inspeção'
  };

  constructor(
    private activatedRoute: ActivatedRoute,
    private dataService: DataService,
    private ngxSpinnerService: NgxSpinnerService,
    private inspectionSheetServiceApi: InspectionSheetServiceApi
  ) {}

  /**
   * Executado ao inicializar o componente.
   * Obtém a ficha de inspeção pelo ID presente nos parâmetros da rota.
   */
  ngOnInit(): void {
    if (this.activatedRoute?.snapshot?.params?.inspectionSheetId) {
      const inspectionSheetId = this.activatedRoute?.snapshot?.params?.inspectionSheetId;
      this.getInspectionSheetById(inspectionSheetId).subscribe((dados) => {
        this.dados = dados;

        if (this.dados) {
          // Se dados existem, pode processar normal
          this.setData(this.dados);
        } else {
          // Se não veio nada, ainda assim ativa o load para liberar a tela
          this.load = false;
        }
      });
    }
  }

  /**
   * Obtém os detalhes da ficha de inspeção pelo ID.
   * @param {string} inspectionSheetId - ID da ficha de inspeção.
   * @returns {Observable<any>} - Observable contendo os dados da ficha de inspeção.
   */
  getInspectionSheetById(inspectionSheetId: string): Observable<any> {
    this.ngxSpinnerService.show();

    return this.inspectionSheetServiceApi.getInspectionSheetById(inspectionSheetId).pipe(
      switchMap((resp: any) => {
        if (!resp || resp?.status === 204 || !resp?.body) {
          return of(null);
        }

        // Se veio body normal
        return this.formatInspectionSheetData(resp.body);
      }),
      finalize(() => {
        this.ngxSpinnerService.hide();
      })
    );
  }

  /**
   * Carrega o logotipo do cliente associado à ficha de inspeção.
   * @param {string} clientId - ID do cliente.
   * @returns {Observable<{ content: string; name: string } | null>} - Observable contendo os dados do logotipo ou `null` se não houver.
   */
  loadLogo(clientId: string): Observable<{ content: string; name: string } | null> {
    if (clientId != '') {
      return this.dataService.getClientLogo(clientId).pipe(
        map((logoData) => {
          if (logoData.logo && logoData.logo.base64) {
            if (logoData.logo.base64 && !logoData.logo.base64.startsWith('data:')) {
              const decode = fn.base64Extension(logoData.logo.base64.slice(0, 100));
              if (decode.mimeType) {
                return {
                  content: `data:${decode.mimeType};base64,${logoData.logo.base64}`,
                  name: logoData.logo.name
                };
              }
            }
          }
          return null;
        })
      );
    }
    return new Observable((observer) => {
      observer.next(null);
      observer.complete();
    });
  }

  /**
   * Formata os dados da ficha de inspeção, incluindo informações adicionais como datas formatadas e logotipo do cliente.
   * @param {any} dados - Dados da ficha de inspeção.
   * @returns {Observable<any>} - Observable contendo os dados formatados da ficha de inspeção.
   */
  formatInspectionSheetData(dados: any): Observable<any> {
    this.inspectionSheetType = dados.type;
    return this.loadLogo(dados.client.id).pipe(
      map((logo) => ({
        ...dados,
        client: {
          ...dados.client,
          logo
        },
        general_data: {
          coordinate: `${dados.structure.coordinate_setting.coordinate_systems.decimal_geodetic.latitude} Lat ${dados.structure.coordinate_setting.coordinate_systems.decimal_geodetic.longitude} Lng`,
          created_date: moment(dados.created_date).format('DD/MM/YYYY HH:mm:ss') || '',
          created_by: `${dados.created_by.first_name} ${dados.created_by.surname}`,
          start_date: moment(dados.start_date).format('DD/MM/YYYY HH:mm:ss') || '',
          end_date: moment(dados.end_date).format('DD/MM/YYYY HH:mm:ss') || '',
          status: InspectionSheetStatus.find((item) => item.value === dados.status)?.label || ''
        }
      }))
    );
  }

  /**
   * Define os dados da ficha de inspeção no componente.
   * @param {any} dados - Dados da ficha de inspeção.
   */
  setData(dados: any) {
    setTimeout(() => {
      if (dados?.previous_situation) {
        this.previousSituationTab.setData(dados);
      }

      if ((dados?.areas && dados.areas.length > 0) || dados.status === 1) {
        this.aspectsObservedTab.setData(dados, []);
      }

      this.conservationStatusTab.setData(dados);

      if ([3].includes(this.inspectionSheetType)) {
        if ((dados?.identified_anomalies && dados.identified_anomalies.length > 0) || dados.status === 1) {
          this.actionsExecutedTab.setData(dados);
          this.currentSituationTab.setData(dados);
        }
      }

      if ((dados?.notes && dados.notes.length > 0) || dados.status === 1) {
        this.generalObservationsTab.setData(dados);
      }
    }, 50);
    this.load = true;
  }
}
