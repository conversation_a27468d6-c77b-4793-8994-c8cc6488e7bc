import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ChartAbsoluteVariationComponent } from './chart-absolute-variation.component';

describe('ChartAbsoluteVariationComponent', () => {
  let component: ChartAbsoluteVariationComponent;
  let fixture: ComponentFixture<ChartAbsoluteVariationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ChartAbsoluteVariationComponent]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ChartAbsoluteVariationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
