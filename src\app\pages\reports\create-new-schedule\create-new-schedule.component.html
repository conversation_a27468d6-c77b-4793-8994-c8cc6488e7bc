<div class="send-report">
  <p class="mt-2">{{ !edit ? 'Novo' : 'Editar' }} Agendamento de Relatório</p>

  <form [formGroup]="formCreateNewSchedule">
    <div class="row">
      <app-hierarchy
        #hierarchy
        [elements]="elements"
        [validate]="true"
      ></app-hierarchy>

      <div class="col-md-3">
        <label class="form-label">Tipo</label>
        <select class="form-select" formControlName="SubjectType">
          <ng-template ngFor let-item [ngForOf]="subjectType">
            <option [ngValue]="item.value">
              {{ item.label }}
            </option>
          </ng-template>
        </select>
      </div>

      <div class="col-md-9">
        <label class="form-label">Título</label>
        <input
          type="text"
          class="form-control"
          formControlName="Title"
          autocomplete="off"
          maxlength="255"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formCreateNewSchedule.get('Title').valid &&
            formCreateNewSchedule.get('Title').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div class="col-md-8">
        <label class="form-label">Responsável</label>
        <input
          type="text"
          class="form-control"
          formControlName="ResponsibleName"
          autocomplete="off"
          maxlength="255"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formCreateNewSchedule.get('ResponsibleName').valid &&
            formCreateNewSchedule.get('ResponsibleName').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div class="col-md-2">
        <label class="form-label">Dias a analisar</label>
        <input
          class="form-control"
          type="number"
          formControlName="DaysToAnalyze"
          min="0"
          step="1"
          (keypress)="
            func.controlNumber(
              $event,
              formCreateNewSchedule.get('DaysToAnalyze'),
              'positiveDecimalLimit'
            )
          "
          (keyup)="
            func.controlNumber(
              $event,
              formCreateNewSchedule.get('DaysToAnalyze')
            )
          "
        />
      </div>

      <div class="col-md-2">
        <label class="form-label">Frequência</label>
        <select
          class="form-select"
          formControlName="PeriodicityType"
          (change)="managerFormGroup()"
        >
          <ng-template ngFor let-item [ngForOf]="periodicityType">
            <option [ngValue]="item.value">
              {{ item.label }}
            </option>
          </ng-template>
        </select>
      </div>

      <div class="col-md-12">
        <label class="form-label">E-mails (separar por virgula) </label>
        <input
          type="text"
          class="form-control"
          formControlName="DestinationEmails"
          autocomplete="off"
          maxlength="255"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formCreateNewSchedule.get('DestinationEmails').valid &&
            formCreateNewSchedule.get('DestinationEmails').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <label class="form-label"
        >Padrão de Recorrência -
        {{
          func.findIndexInArrayofObject(
            this.periodicityType,
            'value',
            controls['PeriodicityType'].value,
            'label'
          )
        }}</label
      >

      <div class="col-md-6" *ngIf="controls['PeriodicityType'].value === 1">
        <div class="form-check">
          <input
            class="form-check-input"
            type="radio"
            formControlName="DailyRecurrencePattern"
            value="every_n_days"
            (change)="managerFormGroup('daily')"
            [checked]="
              controls['DailyRecurrencePattern'].value === 'every_n_days'
            "
          />
          <label class="fst-normal"
            >A cada
            <input
              type="number"
              formControlName="DailyPeriodicity"
              class="form-control"
              min="0"
              step="1"
              style="width: 80px; display: inline-block"
            />
            dias.</label
          >
        </div>
      </div>

      <div class="col-mb-3" *ngIf="controls['PeriodicityType'].value === 1">
        <div class="form-check">
          <input
            class="form-check-input"
            type="radio"
            formControlName="DailyRecurrencePattern"
            value="everyday"
            (change)="managerFormGroup('daily')"
            [checked]="controls['DailyRecurrencePattern'].value === 'everyday'"
          />
          <label class="fst-normal">Todos os dias da semana.</label>
        </div>
      </div>

      <div class="col-md-12" *ngIf="controls['PeriodicityType'].value === 2">
        <div formArrayName="WeeklyEmissionDays">
          <div
            class="form-check form-check-inline"
            *ngFor="let weekDay of weeklyEmissionDays; let i = index"
          >
            <input
              class="form-check-input"
              type="checkbox"
              [id]="weekDay.label"
              [formControlName]="i"
              [value]="weekDay.value"
            />
            <label class="fst-normal form-check-label" [for]="weekDay.label">{{
              weekDay.label
            }}</label>
          </div>
        </div>
      </div>
      <!-- Padrão de Recorrência - Mensal -->
      <label class="fst-normal" *ngIf="controls['PeriodicityType'].value === 3"
        >Dia
        <input
          type="number"
          formControlName="MonthlyEmissionDay"
          class="form-control"
          min="1"
          max="31"
          step="1"
          style="width: 80px; display: inline-block"
        />
        a cada
        <label>
          <input
            type="number"
            formControlName="MonthlyPeriodicity"
            class="form-control"
            min="1"
            max="12"
            step="1"
            style="width: 80px; display: inline-block"
          />
          mês/meses.</label
        >
      </label>

      <app-alert
        [class]="'mt-2 alert-danger'"
        [messages]="messagesError"
      ></app-alert>

      <div
        class="mt-2 alert"
        [ngClass]="message.class"
        role="alert"
        *ngIf="message.status"
      >
        {{ message.text }}
      </div>

      <div class="d-flex justify-content-end mt-3">
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="!edit ? 'fa fa-calendar-plus-o' : 'fa fa-floppy-disk'"
          [label]="!edit ? 'Criar Agendamento' : 'Salvar'"
          [disabled]="isButtonDisabled"
          (click)="validate()"
          [type]="false"
        ></app-button>
      </div>
    </div>
    <div class="col-md-12 mt-3 d-flex justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [routerLink]="['/reports']"
      ></app-button>
    </div>
  </form>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
