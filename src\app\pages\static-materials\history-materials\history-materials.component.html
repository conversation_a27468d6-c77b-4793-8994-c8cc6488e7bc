<div class="list-content">
  <!-- Tabel<PERSON> de Histórico -->
  <div class="col-md-12 mt-2">
    <label class="form-title"
      >Histórico de cadastros e edições de materiais:</label
    >
    <app-table
      *ngIf="tableData.length > 0"
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeader"
      [tableData]="tableData"
      [permissaoUsuario]="permissaoUsuario"
    >
    </app-table>
    <div
      class="alert alert-warning mt-2"
      role="alert"
      *ngIf="messageReturn.status"
    >
      {{ messageReturn.text }}
    </div>
    <!-- Paginação -->
    <app-paginator
      *ngIf="tableData.length > 0"
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event, 'history')"
    ></app-paginator>
  </div>

  <div class="col-md-12 mt-2 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/materials']"
    ></app-button>
  </div>
</div>
