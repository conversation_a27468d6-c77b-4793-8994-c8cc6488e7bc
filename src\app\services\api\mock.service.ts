import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MockService {
  private baseUrl = 'http://localhost/mock/';

  constructor(private http: HttpClient) {}

  getMockChartInc(params: any = {}): Observable<any> {
    return this.http.get(this.baseUrl + 'gerarmock.php', { params: params });
  }

  getMockChartInaPz(params: any = {}): Observable<any> {
    return this.http.post(this.baseUrl + 'ina-pz.php', params);
  }

  getMockChartMr(params: any = {}): Observable<any> {
    return this.http.post(this.baseUrl + 'mr.php', params);
  }

  getMockChartRl(params: any = {}): Observable<any> {
    return this.http.post(this.baseUrl + 'rl.php', params);
  }

  getMockChartMsPr(params: any = {}): Observable<any> {
    return this.http.post(this.baseUrl + 'ms-pr.php', params);
  }

  getMockVariationAbsolute(params: any = {}): Observable<any> {
    return this.http.post(this.baseUrl + 'variacao-absoluta.php', params);
  }

  getMockStabilityChart(params: any = {}): Observable<any> {
    return this.http.post(this.baseUrl + 'chart-stability.php', params);
  }

  getSimulationConfiguration(params: any = {}): Observable<any> {
    return this.http.post(this.baseUrl + 'simulator.php', params);
  }
}
