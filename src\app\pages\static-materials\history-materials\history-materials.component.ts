import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { MessageInputInvalid } from 'src/app/constants/message.constants';

import { StaticMaterialsService as StaticMaterialsServiceApi } from 'src/app/services/api/staticMaterials.service';
import { UserService } from 'src/app/services/user.service';

import * as moment from 'moment';

@Component({
  selector: 'app-history-materials',
  templateUrl: './history-materials.component.html',
  styleUrls: ['./history-materials.component.scss']
})
export class HistoryMaterialsComponent implements OnInit {
  public profile: any = null;
  public permissaoUsuario: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };
  public messagesError: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Data/Hora',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Usuário',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Modificações',
      width: '65%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    }
  ];

  constructor(private activatedRoute: ActivatedRoute, private staticMaterialsServiceApi: StaticMaterialsServiceApi, private userService: UserService) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.getStaticMaterialsHistory(this.activatedRoute.snapshot.params.staticMaterialId);
  }

  getStaticMaterialsHistory(sectionId: string) {
    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.staticMaterialsServiceApi.getStaticMaterialsHistory(sectionId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados) {
        this.tableData = dados ? dados.data : [];
        this.collectionSize = dados.total_items_count;
        this.messageReturn.text = '';
        this.messageReturn.status = false;
        this.formatDataHistory('tableData');
      } else {
        this.tableData = [];
        this.collectionSize = 0;
        this.messageReturn.text = MessageInputInvalid.NoHistory;
        this.messageReturn.status = true;
        this.message.class = 'alert-warning';
      }
    });
  }

  formatDataHistory(type) {
    this[type] = this[type].map((item: any) => {
      let itemData = {
        created_date: moment(item.created_date).format('DD/MM/YYYY HH:mm:ss'),
        user: item.modified_by.first_name + ' ' + item.modified_by.surname,
        changes: item.changes
      };
      return itemData;
    });
  }

  loadPage(selectPage: number, option: string): void {
    if (option == 'history') {
      this.page = selectPage;
      this.getStaticMaterialsHistory(this.activatedRoute.snapshot.params.staticMaterialId);
    }
  }
}
