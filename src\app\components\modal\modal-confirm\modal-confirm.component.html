<ng-template #modalConfirm let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">
      <i class="me-1" [class]="modalConfig.iconHeader"></i>{{ title }}
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>

  <div class="modal-body">
    <i class="fa fa-info-circle"></i> {{ message }} <br />
    <div *ngIf="instruction">{{ instruction }}</div>

    <!-- Radios de validação, só aparecem se data.options for array -->
    <div class="mt-3" *ngIf="data?.options?.length">
      <div class="form-check" *ngFor="let opt of data.options">
        <input
          class="form-check-input"
          type="radio"
          [id]="opt.value"
          [name]="'validationOptions'"
          [value]="opt.value"
          [(ngModel)]="data.selected"
        />
        <label class="form-check-label" [for]="opt.value">{{
          opt.label
        }}</label>
      </div>
    </div>
  </div>

  <!-- Botões -->
  <div class="modal-footer">
    <app-button
      *ngIf="modalConfig?.action && modalConfig?.action !== ''"
      [class]="'btn-logisoil-green'"
      [label]="'Confirmar'"
      [icon]="'fa fa-check'"
      [type]="false"
      (click)="clickRowEvent(); c('Close click')"
    >
    </app-button>

    <app-button
      [class]="modalConfig?.action ? 'btn-logisoil-red' : 'btn-logisoil-blue'"
      [label]="modalConfig?.action ? 'Cancelar' : 'Fechar'"
      (click)="c('Close click')"
    ></app-button>
  </div>
</ng-template>
