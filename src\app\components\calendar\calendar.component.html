<ul class="dashboard-calendar-status">
  <li>
    <app-button
      [class]="'btn-logisoil-newActivity '"
      [label]="'Criar atividade'"
      [icon]="'fa fa-calendar-plus-o calendar me-1'"
      (click)="ModalCalendarActivities.openModal()"
    ></app-button>
  </li>
  <li>
    <i class="fa fa-circle"></i>
    <span>Concluídas</span>
  </li>
  <li>
    <i class="fa fa-circle"></i>
    <span>Atrasadas</span>
  </li>
  <li>
    <i class="fa fa-circle"></i>
    <span>No prazo</span>
  </li>
</ul>

<div class="calendar-main">
  <full-calendar *ngIf="calendarVisible" [options]="calendarOptions">
    <ng-template #eventContent let-arg>
      <b>{{ arg.timeText }}</b>
      <i>{{ arg.event.title }}</i>
    </ng-template>
  </full-calendar>
</div>

<app-modal-calendar-activities
  #modalCalendarActivities
  (sendEvent)="getEvent($event)"
></app-modal-calendar-activities>
