import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { MessageInputInvalid } from 'src/app/constants/message.constants';

import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';
import { UserService } from 'src/app/services/user.service';

import * as moment from 'moment';

@Component({
  selector: 'app-history-structure',
  templateUrl: './history-structure.component.html',
  styleUrls: ['./history-structure.component.scss']
})
export class HistoryStructureComponent implements OnInit {
  public profile: any = null;
  public permissaoUsuario: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };
  public messagesError: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Data/Hora',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Usuário',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Modificações',
      width: '65%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    }
  ];

  constructor(private activatedRoute: ActivatedRoute, private structureServiceApi: StructuresServiceApi, private userService: UserService) {}

  /**
   * Lifecycle hook executado após a criação do componente.
   * Carrega o perfil do usuário, permissões e o histórico da estrutura com base no ID da rota.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.getStructureHistory(this.activatedRoute.snapshot.params.structureId);
  }

  /**
   * Busca o histórico de modificações de uma estrutura via API.
   * Atualiza a tabela e exibe mensagem apropriada caso não haja dados.
   *
   * @param structureId - ID da estrutura a ser consultada.
   */
  getStructureHistory(structureId: string) {
    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.structureServiceApi.getStructuresHistory(structureId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados) {
        this.tableData = dados ? dados.data : [];
        this.collectionSize = dados.total_items_count;
        this.messageReturn.text = '';
        this.messageReturn.status = false;
        this.formatDataHistory('tableData');
      } else {
        this.tableData = [];
        this.collectionSize = 0;
        this.messageReturn.text = MessageInputInvalid.NoHistory;
        this.messageReturn.status = true;
        this.message.class = 'alert-warning';
      }
    });
  }

  /**
   * Formata os dados de histórico convertendo a data para o formato `DD/MM/YYYY HH:mm:ss`
   * e combinando o nome do usuário que realizou a modificação.
   *
   * @param type - Nome da propriedade que contém os dados (ex: 'tableData').
   */
  formatDataHistory(type) {
    this[type] = this[type].map((item: any) => {
      let itemData = {
        created_date: moment(item.created_date).subtract(3, 'hours').format('DD/MM/YYYY HH:mm:ss'),
        user: item.modified_by.first_name + ' ' + item.modified_by.surname,
        changes: item.changes
      };
      return itemData;
    });
  }

  /**
   * Atualiza a página atual da tabela de histórico e recarrega os dados.
   *
   * @param selectPage - Número da página selecionada.
   * @param option - Tipo de dado a ser recarregado (ex: 'history').
   */
  loadPage(selectPage: number, option: string): void {
    if (option == 'history') {
      this.page = selectPage;
      this.getStructureHistory(this.activatedRoute.snapshot.params.structureId);
    }
  }
}
