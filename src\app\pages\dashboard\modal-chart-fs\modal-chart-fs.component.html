<ng-template #modalChartFS let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">Análise do Fator de Segurança {{ title }}</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click'); closeModal()"
    ></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formSafetyFactor" class="mt-2">
      <div class="row">
        <!-- Superfície e Condição -->
        <div class="col-md-3">
          <label class="form-label">Superfície / Condição</label>
          <select
            class="form-select"
            formControlName="SurfaceAndConditions"
            (change)="
              reset();
              setFormValuesFromSelectedItem(
                formSafetyFactor.get('SurfaceAndConditions').value
              )
            "
          >
            <option value="">Selecione...</option>
            <ng-template ngFor let-item [ngForOf]="surfaceAndCondition">
              <option [ngValue]="item.id">
                {{ item.name }}
              </option>
            </ng-template>
          </select>
        </div>
        <!-- Data -->
        <div class="col-md-3">
          <label class="form-label">Data e hora</label>
          <select
            class="form-select"
            formControlName="CreatedDate"
            (change)="reset()"
          >
            <option value="">Selecione...</option>
            <ng-template ngFor let-item [ngForOf]="createdDate">
              <option [ngValue]="item.id">
                {{ item.date_format }}
              </option>
            </ng-template>
          </select>
        </div>
        <!-- Exibir imagem -->
        <div
          class="col-md-3"
          style="padding: 28px"
          *ngIf="formSafetyFactor.get('CreatedDate').value !== ''"
        >
          <app-button
            [class]="'btn-logisoil-blue'"
            [label]="'Exibir imagem'"
            (click)="showImage(formSafetyFactor.get('CreatedDate').value)"
          ></app-button>
        </div>
      </div>
      <!-- Alerta -->
      <div
        class="alert mt-2"
        [ngClass]="message.class"
        role="alert"
        *ngIf="message.status"
        [innerHTML]="message.text"
      ></div>
      <!-- Downloads -->
      <div class="row mt-4" *ngIf="ctrlShowImage">
        <div class="col-md-12">
          <app-button
            [class]="'btn-logisoil-blue me-3'"
            [label]="'Download DXF'"
            [icon]="'fa fa-file'"
            (click)="downloadFile('DXF')"
          ></app-button>
          <app-button
            [class]="'btn-logisoil-blue me-3'"
            [label]="'Download PNG'"
            [icon]="'fa fa-file-image-o'"
            (click)="downloadFile('PNG')"
          ></app-button>
          <app-button
            [class]="'btn-logisoil-blue me-3'"
            [label]="'Download ZIP'"
            [icon]="'fa fa-file-archive-o'"
            (click)="downloadFile('ZIP')"
          ></app-button>
        </div>
      </div>
      <div class="row mt-3" *ngIf="ctrlShowImage">
        <div class="col-md-12">
          <app-dxf-viewer
            [fileDxf]="imagesFiles.DXF.file"
            [idCanvas]="'canvasDXFDashboardModal'"
          ></app-dxf-viewer>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-gray'"
      [label]="'Fechar'"
      (click)="c('Close click'); closeModal()"
    >
    </app-button>
  </div>
</ng-template>
