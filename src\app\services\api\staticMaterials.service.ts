import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class StaticMaterialsService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  getStaticMaterialsList(params: any = {}) {
    const url = '/static-materials';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Retorna os Materiais para uso em filtro
  getStaticMaterialsSearch(params: any = {}) {
    const url = '/static-materials/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Cadastro de Material
  postStaticMaterials(params: any) {
    const url = '/static-materials';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Busca o Material por ID
  getStaticMaterialsById(id: string) {
    const url = `/static-materials/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putStaticMaterials(id: string, params: any) {
    const url = `/static-materials/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  //Alterar o status de um material
  patchStaticMaterials(id: string, params: any) {
    const url = `/static-materials/${id}`;
    return this.api.patch<any>(url, params, 'client');
  }

  // Cadastro de revisão de um material
  postStaticMaterialsReview(id: string, params: any) {
    const url = `/static-materials/${id}/review`;
    return this.api.post<any>(url, params, {}, 'client');
  }

  getStaticMaterialsReviewById(id: string) {
    const url = `/static-materials/review/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  // Histórico
  getStaticMaterialsHistory(id: string, params: any = {}) {
    const url = `/static-materials/${id}/history`;
    return this.api.get<any[]>(url, params, false, 'client');
  }
}
