import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalInstrumentComponent } from './modal-instrument.component';

describe('ModalInstrumentComponent', () => {
  let component: ModalInstrumentComponent;
  let fixture: ComponentFixture<ModalInstrumentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalInstrumentComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalInstrumentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
