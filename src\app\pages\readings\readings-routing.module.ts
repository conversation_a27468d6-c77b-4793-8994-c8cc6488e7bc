import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListReadingsComponent } from './list-readings/list-readings.component';
import { RegisterReadingsComponent } from './register-readings/register-readings.component';

import { AppGuard } from '../../guards/app.guard';
import { HistoryTabsReadingComponent } from './history-tabs-reading/history-tabs-reading.component';

const routes: Routes = [
  {
    path: '',
    component: ListReadingsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastrarLeitura,
    component: RegisterReadingsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarLeitura,
    component: RegisterReadingsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarLeitura,
    component: RegisterReadingsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.HistoricoLeituras,
    component: HistoryTabsReadingComponent,
    canActivate: [AppGuard]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReadingsRoutingModule {}
