.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-control {
  border-color: #d4d2d2;
  font-size: 0.875em;
}

.form-select {
  font-size: 0.875em !important;
  line-height: 1.52857143 !important;
}

.button-client {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}

.row-scroll {
  overflow-x: scroll;
  flex-wrap: nowrap;
}

/* Estilo base para os botões do grupo */
.btn-group-blue {
  background-color: #ffffff;
  color: #032561;
  font-size: 0.875em;
  border: none;
  transition: all 100ms ease;
  border: 1px solid #032561;
}

// .btn-group-blue:hover {
//   background-color: rgba(3, 37, 97, 0.9);
//   color: #ffffff;
//   border-color: rgba(3, 37, 97, 0.9);
// }

/* Remover sombras e bordas para estados de foco e clique */
// .btn-group-blue:focus,
// .btn-group-blue:active,
// .btn-group-blue:focus-visible {
//   background-color: rgba(3, 37, 97, 0.9);
//   color: #ffffff;
//   box-shadow: none;
//   outline: none;
// }

/* Botão desativado */
// .btn-group-blue:disabled {
//   background-color: #032561;
//   color: #ffffff;
//   opacity: 0.65;
// }

/* Estilo para o botão selecionado */
.btn-group-blue.selected {
  background-color: rgba(3, 37, 97, 0.9);
  color: #ffffff;
}
