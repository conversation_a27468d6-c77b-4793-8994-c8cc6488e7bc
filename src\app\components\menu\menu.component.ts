import { Component, OnInit } from '@angular/core';
import { MenuService } from 'src/app/services/menu.service';

@Component({
  selector: 'app-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss']
})
export class MenuComponent implements OnInit {
  public menu: any = [];
  public menuAdmin: any = [];
  public menuAlertas: any = [];
  public menuActive = true;

  constructor(private menuService: MenuService) {}

  ngOnInit(): void {
    this.menu = this.menuService.setMenu('principal');

    this.menuAdmin = this.menuService.setMenu('admin');

    this.menuAlertas = this.menuService.setMenu('alertas');
  }
}
