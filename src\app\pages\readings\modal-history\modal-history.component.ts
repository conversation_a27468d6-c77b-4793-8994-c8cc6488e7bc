import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation, OnChanges, SimpleChanges } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { MessageInputInvalid } from 'src/app/constants/message.constants';

import { ReadingService as ReadingServiceApi } from 'src/app/services/api/reading.service';
import * as moment from 'moment';

@Component({
  selector: 'app-modal-history',
  templateUrl: './modal-history.component.html',
  styleUrls: ['./modal-history.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalHistoryomponent implements OnInit, OnChanges {
  @ViewChild('modalHistory') modalHistory: ElementRef;

  @Input() public title: string = '';
  @Input() public historyId: string = '';
  @Input() public historyType: string = '';

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public messageReturn: any = [{ text: '', status: false }];

  public tableData: any = [];

  public tableHeader: any = [
    {
      label: 'ID',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['id']
    },
    {
      label: 'Usuário',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Modificações',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    },
    {
      label: 'Data/Hora',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    }
  ];

  constructor(private modalService: NgbModal, private readingServiceApi: ReadingServiceApi) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.historyId && changes.historyId.currentValue != null && changes.historyId.currentValue != '') {
      this.getReadingHistory(this.historyId);
    }
  }

  openModal() {
    this.modalService.open(this.modalHistory, { size: 'lg' });
  }

  getReadingHistory(historyId: string) {
    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    let method = this.historyType == 'Instrument' ? 'getReadingHistory' : 'getBeachLengthHistory';

    this.readingServiceApi[method](historyId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (resp.status == 200) {
        this.tableData = dados ? dados.data : [];
        this.collectionSize = dados.total_items_count;
        this.messageReturn.text = '';
        this.messageReturn.status = false;
        this.formatData();
      } else {
        this.tableData = [];
        this.collectionSize = 0;
        this.messageReturn.text = MessageInputInvalid.NoHistory;
        this.messageReturn.status = true;
      }
    });
  }

  formatData() {
    this.tableData = this.tableData.map((item: any, index: number) => {
      let itemData = {
        id: index + 1,
        created_date: moment(item.created_date).format('DD/MM/YYYY HH:mm:ss'),
        user: item.modified_by.first_name + ' ' + item.modified_by.surname,
        changes: item.changes
      };
      return itemData;
    });
  }

  loadPage(selectPage: number) {
    this.page = selectPage;
    this.getReadingHistory(this.historyId);
  }
}
