<div class="list-content">
  <app-alert
    *ngIf="showNotificationBanner"
    [class]="'alert-warning'"
    class="mt-3"
    [messages]="bannerNotifications"
    [showCloseButton]="true"
    [onClose]="handleCloseNotificationBanner.bind(this)"
  ></app-alert>
  <!-- Cadastrar nova leitura -->
  <div class="button-client">
    <app-button
      [class]="'btn-logisoil-green me-2'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Cadastrar Nova Leitura'"
      *ngIf="permissaoUsuario?.create"
      [routerLink]="['create']"
    ></app-button>
    <!-- Cadastrar comprimento de praia -->
    <app-button
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Cadastrar comprimento de praia'"
      *ngIf="permissaoUsuario.create"
      [routerLink]="['create']"
      [queryParams]="{ praia: true }"
    ></app-button>
  </div>

  <div class="row g-3 mt-1">
    <div class="col-md-3">
      <label class="form-label">ID</label>
      <input
        type="number"
        step="1"
        min="1"
        class="form-control"
        [(ngModel)]="filter.SearchIdentifier"
        placeholder="ID da leitura"
        autocomplete="off"
        (keypress)="
          func.controlNumber(
            $event,
            filter.SearchIdentifier,
            'positive',
            'ngModel'
          )
        "
        (keyup)="
          func.controlNumber($event, filter.SearchIdentifier, null, 'ngModel')
        "
      />
    </div>

    <!-- Selects Cliente, Unidade e Estrutura -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-9"
      (sendEventHierarchy)="getEventHierarchy($event)"
    ></app-hierarchy>
  </div>

  <div class="row mt-1">
    <!-- Seção -->
    <div class="col-md-3">
      <label class="form-label">Seção</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="sectionSettings"
        [data]="sections"
        [(ngModel)]="filterBodyParams.section_ids"
        (onSelect)="getInstruments($event, 'select')"
        (onDeSelect)="getInstruments($event, 'deselect')"
        (onSelectAll)="getInstruments($event, 'selectAll')"
        (onDeSelectAll)="getInstruments($event, 'deselectAll')"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Instrumento -->
    <div class="col-md-3" *ngIf="ctrlInstrument">
      <label class="form-label">Instrumento</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="instrumentsSettings"
        [data]="instruments"
        [(ngModel)]="filterBodyParams.instrument_ids"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Data inicial -->
    <div class="col-md-3">
      <label class="form-label">Data e hora inicial</label>
      <input
        type="datetime-local"
        class="form-control"
        [(ngModel)]="filter.StartDate"
      />
      <app-button
        [class]="'btn-logisoil-blue mt-1'"
        [label]="'Data única'"
        class="me-1"
        (click)="filter.EndDate = filter.StartDate"
      ></app-button>
    </div>
    <!-- Data final -->
    <div class="col-md-3">
      <label class="form-label">Data e hora final</label>
      <input
        type="datetime-local"
        class="form-control"
        [(ngModel)]="filter.EndDate"
      />
    </div>
  </div>
  <div class="row">
    <!-- Visualização -->
    <div class="col-md-3">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>

    <!-- Botões -->
    <div class="col-md-3 d-flex align-items-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        class="me-1"
        (click)="managerFilters(true)"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>
  </div>
  <!-- Instrumentos -->
  <div class="row g-3 mt-2">
    <div class="col-md-12 d-flex align-items-center justify-content-center">
      <div class="btn-group" role="group">
        <ng-template ngFor let-navbarItem [ngForOf]="navbar" let-i="index">
          <button
            type="button"
            class="btn btn-group-blue"
            (click)="changeTypeInstrument(navbarItem.id)"
            [ngClass]="{ selected: filter.InstrumentType === navbarItem.id }"
          >
            {{ navbarItem.label }}
          </button>
        </ng-template>
      </div>
    </div>
  </div>
  <!-- Botoes -->
  <div class="row mt-3">
    <div class="col-md-12">
      <app-button
        *ngIf="selectedReadings > 0"
        [class]="'btn-logisoil-blue me-2'"
        [icon]="'fa fa-calendar'"
        [label]="'Editar data e horário'"
        (click)="showDateTime = !showDateTime"
      >
      </app-button>
      <app-button
        *ngIf="selectedReadings > 0"
        [class]="'btn-logisoil-red me-2'"
        [icon]="'fa fa-trash-o'"
        [label]="'Excluir selecionados'"
        (click)="openModal('confirmDelete')"
      ></app-button>
      <!-- Apenas INA's e PZ's -->
      <app-button
        *ngIf="
          (typeInstrumentId == '' || typeInstrumentId <= 3) &&
          selectedReadings > 0 &&
          selectedReadings == selectedPackages
        "
        [class]="'btn-logisoil-green me-2'"
        [icon]="'fa fa-cube'"
        [label]="'Gerar pacotes'"
        (click)="openModal('generatePackages')"
      >
      </app-button>
      <!-- Praia -->
      <app-button
        [class]="'btn-logisoil-blue'"
        [label]="'Comprimento de praia'"
        (click)="changeTypeInstrument('11')"
      >
      </app-button>
    </div>
  </div>

  <form [formGroup]="formDateTime">
    <!-- Alerta -->
    <div
      class="alert alert-success mt-2"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
    >
      {{ message.text }}
    </div>

    <app-alert
      class="mt-2"
      [class]="'alert-danger'"
      [messages]="messagesError"
    ></app-alert>

    <!-- Botão Editar data e horário -->
    <div class="row mt-2" *ngIf="showDateTime && selectedReadings > 0">
      <div class="col-md-3 d-flex align-items-end justify-content-end">
        <label class="form-label">
          <i class="fa fa-pencil-square-o me-2" aria-hidden="true"></i>
          Editar
          {{ selectedReadings }} leituras selecionadas</label
        >
      </div>
      <div class="col-md-2">
        <label class="form-label">Nova data e horário</label>
        <input
          type="datetime-local"
          class="form-control"
          formControlName="date"
        />
      </div>
      <div class="col-md-3 d-flex align-items-end">
        <app-button
          *ngIf="formDateTime.valid"
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-pencil-square-o'"
          [label]="'Editar'"
          [type]="true"
          (click)="actionReadings('patch')"
        >
        </app-button>
      </div>
    </div>
  </form>

  <!-- Alerta -->
  <div
    class="alert alert-warning mt-3"
    role="alert"
    *ngIf="messageReturn.status"
  >
    {{ messageReturn.text }}
  </div>

  <div style="display: grid" *ngIf="tableData.length > 0">
    <div class="row-scroll mt-3">
      <!-- Tabela -->
      <app-table
        [messageReturn]="messageReturn"
        [tableHeader]="tableHeader"
        [tableData]="tableData"
        [permissaoUsuario]="permissaoUsuario"
        [actionCustom]="actionCustom"
        (sendClickRowEvent)="clickRowEvent($event)"
      >
      </app-table>
    </div>
  </div>

  <div class="row mt-3">
    <!-- Paginação -->
    <app-paginator
      *ngIf="tableData.length > 0"
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event)"
      [enableItemPerPage]="true"
    ></app-paginator>

    <!-- Botão Voltar -->
    <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela inicial'"
        [click]="goBack.bind(this)"
      ></app-button>
    </div>
  </div>
</div>

<!-- Confirmar exclusao -->
<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
></app-modal-confirm>

<!-- Histórico -->
<app-modal-history
  #modalHistory
  [title]="modalTitle"
  [historyId]="historyId"
  [historyType]="historyType"
></app-modal-history>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
