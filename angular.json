{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "898693eb-4f27-426d-b9ea-4f75b89b41f1"}, "version": 1, "newProjectRoot": "projects", "projects": {"Logisoil-Frontend-V3": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/logisoil-frontend-v3", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/silent-renew.html", "src/staticwebapp.config.json"], "styles": ["src/styles.scss", "./node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/ngx-toastr/toastr.css"], "scripts": ["./node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "./node_modules/jquery/dist/jquery.min.js", "./node_modules/bootstrap/dist/js/bootstrap.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "bundles"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"port": 4201}, "configurations": {"production": {"browserTarget": "Logisoil-Frontend-V3:build:production"}, "development": {"browserTarget": "Logisoil-Frontend-V3:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "Logisoil-Frontend-V3:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "defaultProject": "Logisoil-Frontend-V3"}