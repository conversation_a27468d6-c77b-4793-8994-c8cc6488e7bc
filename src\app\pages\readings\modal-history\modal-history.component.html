<ng-template #modalHistory let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title"><i class="fa fa-history me-1"></i>{{ title }}</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>

  <div class="modal-body">
    <!-- Alerta -->
    <div
      class="alert alert-warning mt-3"
      role="alert"
      *ngIf="messageReturn.status"
    >
      {{ messageReturn.text }}
    </div>
    <!-- Ta<PERSON><PERSON> de Histórico -->
    <div class="col-md-12 mt-3" *ngIf="tableData.length > 0">
      <app-table
        [messageReturn]="messageReturn"
        [tableHeader]="tableHeader"
        [tableData]="tableData"
      >
      </app-table>

      <!-- Paginação -->
      <app-paginator
        [collectionSize]="collectionSize"
        [page]="page"
        [maxSize]="10"
        [boundaryLinks]="true"
        [pageSize]="pageSize"
        (sendPageChange)="loadPage($event)"
      ></app-paginator>
    </div>
  </div>

  <!-- Botões -->
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-red'"
      [label]="'Fechar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>
