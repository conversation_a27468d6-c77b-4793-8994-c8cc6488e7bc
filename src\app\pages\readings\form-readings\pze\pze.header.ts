const pzeTableHeader = [
  {
    label: 'Selecionar',
    width: '85px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['select'],
    type: 'check'
  },
  {
    label: 'ID',
    width: '60px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['reading_search_identifier']
  },
  {
    label: 'Instrumento',
    width: '40%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['identifier'],
    extra: true //Losango
  },
  {
    label: 'Data e hora',
    width: '30%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['date_format']
  },
  {
    label: 'Célula de pressão',
    width: '30%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['measure']
  },

  {
    label: '<PERSON><PERSON>mé<PERSON> (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['quota']
  },
  {
    label: 'Profundidade (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['depth']
  },
  {
    label: 'Pressão (kPa)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['pressure']
  },
  {
    label: 'Seco',
    width: '60px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['dry']
  },
  {
    label: 'Ações',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['actionCustom']
  }
];

export { pzeTableHeader };
