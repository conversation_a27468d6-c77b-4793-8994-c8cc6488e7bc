import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { surfacesType, surfacesTypeMethodsFields as surfacesTypeMethodsFieldsEnum } from 'src/app/constants/structure.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-modal-search-method',
  templateUrl: './modal-search-method.component.html',
  styleUrls: ['./modal-search-method.component.scss']
})
export class ModalSearchMethodComponent implements OnInit, OnChanges {
  @ViewChild('modalSearchMethod') ModalSearchMethod: ElementRef;
  @Input() public title: string = '';
  @Input() public surfaceType: string = '';
  @Input() public searchMethod: string = '';
  @Input() public slide2Configuration: any = null;

  @Output() modalClosed = new EventEmitter<void>();

  public formSearchMethod: FormGroup = new FormGroup({
    //Parâmetros circulares:
    circular_divisions_along_slope: new FormControl(null, [Validators.required]),
    circular_circles_per_division: new FormControl(null, [Validators.required]),
    circular_number_of_iterations: new FormControl(null, [Validators.required]),
    circular_divisions_next_iteration: new FormControl(null, [Validators.required]),
    circular_radius_increment: new FormControl(null, [Validators.required]),
    circular_number_of_surfaces: new FormControl(null, [Validators.required]),

    //Parâmetros não circulares:
    non_circular_divisions_along_slope: new FormControl(null, [Validators.required]),
    non_circular_surfaces_per_division: new FormControl(null, [Validators.required]),
    non_circular_number_of_iterations: new FormControl(null, [Validators.required]),
    non_circular_divisions_next_iteration: new FormControl(null, [Validators.required]),
    non_circular_number_of_vertices_along_surface: new FormControl(null, [Validators.required]),
    non_circular_number_of_surfaces: new FormControl(null, [Validators.required]),
    non_circular_number_of_nests: new FormControl(null, [Validators.required]),
    non_circular_maximum_iterations: new FormControl(null, [Validators.required]),
    non_circular_initial_number_of_surface_vertices: new FormControl(null, [Validators.required]),
    non_circular_initial_number_of_iterations: new FormControl(null, [Validators.required]),
    non_circular_maximum_number_of_steps: new FormControl(null, [Validators.required]),
    non_circular_number_of_factors_safety_compared_before_stopping: new FormControl(null, [Validators.required]),
    non_circular_tolerance_for_stopping_criterion: new FormControl(null, [Validators.required]),
    non_circular_number_of_particles: new FormControl(null, [Validators.required])
  });

  public surfacesTypes = surfacesType;
  public surfacesTypeMethodsFields = surfacesTypeMethodsFieldsEnum;
  public sufix: string = '';

  public methodsSelected: any = {
    title: '',
    subtitle: null,
    method: null,
    fields: []
  };

  public func = fn;

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (this.surfaceType != '' && this.searchMethod != '') {
      this.getSurfacesTypesMethods(this.surfaceType, this.searchMethod);
    }
    if (changes.slide2Configuration && changes.slide2Configuration.currentValue != null) {
      this.setFormValues(this.slide2Configuration);
    }
  }

  getSurfacesTypesMethods(surfacesTypes, searchMethod) {
    let type = surfacesTypes == '1' ? 'surface_type_circular' : 'surface_type_non_circular';
    this.sufix = surfacesTypes == '1' ? 'circular' : 'non_circular';
    let method = this.surfacesTypes[type].search.find((method) => method.value === searchMethod);
    if (method != null) {
      this.methodsSelected.subtitle = method.method;
      this.methodsSelected.method = method.value;
      this.methodsSelected.fields = method.fields;
    } else {
      this.methodsSelected.subtitle = null;
      this.methodsSelected.method = null;
      this.methodsSelected.fields = [];
    }
  }

  setFormValues(slide2Configuration) {
    //configurations-tab
    if (slide2Configuration.circular_parameters != null) {
      this.formSearchMethod.get('circular_divisions_along_slope').setValue(slide2Configuration.circular_parameters.divisions_along_slope);
      this.formSearchMethod.get('circular_circles_per_division').setValue(slide2Configuration.circular_parameters.circles_per_division);
      this.formSearchMethod.get('circular_number_of_iterations').setValue(slide2Configuration.circular_parameters.number_of_iterations);
      this.formSearchMethod.get('circular_divisions_next_iteration').setValue(slide2Configuration.circular_parameters.divisions_next_iteration);
      this.formSearchMethod.get('circular_radius_increment').setValue(slide2Configuration.circular_parameters.radius_increment);
      this.formSearchMethod.get('circular_number_of_surfaces').setValue(slide2Configuration.circular_parameters.number_of_surfaces);
    }

    if (slide2Configuration.non_circular_parameters != null) {
      this.formSearchMethod.get('non_circular_divisions_along_slope').setValue(slide2Configuration.non_circular_parameters.divisions_along_slope);
      this.formSearchMethod.get('non_circular_surfaces_per_division').setValue(slide2Configuration.non_circular_parameters.surfaces_per_division);
      this.formSearchMethod.get('non_circular_number_of_iterations').setValue(slide2Configuration.non_circular_parameters.number_of_iterations);
      this.formSearchMethod.get('non_circular_divisions_next_iteration').setValue(slide2Configuration.non_circular_parameters.divisions_next_iteration);
      this.formSearchMethod
        .get('non_circular_number_of_vertices_along_surface')
        .setValue(slide2Configuration.non_circular_parameters.number_of_vertices_along_surface);
      this.formSearchMethod.get('non_circular_number_of_surfaces').setValue(slide2Configuration.non_circular_parameters.number_of_surfaces);
      this.formSearchMethod.get('non_circular_number_of_nests').setValue(slide2Configuration.non_circular_parameters.number_of_nests);
      this.formSearchMethod.get('non_circular_maximum_iterations').setValue(slide2Configuration.non_circular_parameters.maximum_iterations);

      this.formSearchMethod
        .get('non_circular_initial_number_of_surface_vertices')
        .setValue(
          slide2Configuration.non_circular_parameters.initial_number_of_surface_vertices
            ? slide2Configuration.non_circular_parameters.initial_number_of_surface_vertices
            : 0
        );

      this.formSearchMethod.get('non_circular_initial_number_of_iterations').setValue(slide2Configuration.non_circular_parameters.initial_number_of_iterations);
      this.formSearchMethod.get('non_circular_maximum_number_of_steps').setValue(slide2Configuration.non_circular_parameters.maximum_number_of_steps);
      this.formSearchMethod
        .get('non_circular_number_of_factors_safety_compared_before_stopping')
        .setValue(slide2Configuration.non_circular_parameters.number_of_factors_safety_compared_before_stopping);
      this.formSearchMethod
        .get('non_circular_tolerance_for_stopping_criterion')
        .setValue(slide2Configuration.non_circular_parameters.tolerance_for_stopping_criterion);
      this.formSearchMethod.get('non_circular_number_of_particles').setValue(slide2Configuration.non_circular_parameters.number_of_particles);
    }
  }

  /**
   * Abre o modal do fator de segurança.
   */
  openModal() {
    this.modalService.open(this.ModalSearchMethod, {});
  }

  /**
   * Fecha o modal do fator de segurança e reseta os valores.
   */
  closeModal(): void {
    this.modalClosed.emit();
  }
}
