import { HttpClient, HttpEvent, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { AuthService } from '../auth.service';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private httpOptions: any = {};

  constructor(private http: HttpClient, private authService: AuthService) {
    this.createHttpOptions();
  }

  /**
   * Cria as opções HTTP com os headers apropriados.
   *
   * - Adiciona token de autenticação (Bearer).
   * - Define o idioma da requisição.
   * - Se for envio de `FormData`, remove o `Content-Type` para o browser definir automaticamente.
   *
   * @param {boolean} formData - Se true, remove o `Content-Type` para uploads de arquivos.
   */
  createHttpOptions(formData = false): void {
    let headers = {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + this.authService.getToken(),
      'Accept-Language': this.authService.getLocale()
    };

    if (formData) {
      delete headers['Content-Type'];
    }

    this.httpOptions = {
      headers: new HttpHeaders(headers)
    };
  }

  /**
   * Retorna a URL base da API com base no identificador configurado no `environment`.
   *
   * @param {string} _api - Identificador da API.
   * @returns {string} - URL base da API.
   */
  getApi(_api: string) {
    return environment.apiUrl.find((api) => api.id == _api).url;
  }

  /**
   * Realiza uma requisição GET com headers e parâmetros dinâmicos.
   *
   * @template T - Tipo esperado da resposta.
   * @param {string} url - Caminho da API (sem a base).
   * @param {any} [params=null] - Parâmetros de query.
   * @param {boolean} [blob=false] - Se true, espera um retorno `Blob` (ex: arquivo).
   * @param {string} [api=''] - Identificador da API.
   * @param {boolean} [response=false] - Se true, retorna o objeto completo da resposta.
   * @returns {Observable<HttpEvent<T>>} - Observable da resposta HTTP.
   */
  get<T>(url: string, params: any = null, blob = false, api: string = '', response = false): Observable<HttpEvent<T>> {
    this.createHttpOptions();
    url = this.getApi(api) + url;

    if (params) {
      this.httpOptions['observe'] = 'response';
      this.httpOptions['params'] = params;
    }

    if (blob) {
      this.httpOptions['responseType'] = 'blob';
    }

    if (response) {
      this.httpOptions['observe'] = 'response';
    }

    return this.http.get<T>(url, this.httpOptions);
  }

  /**
   * Realiza uma requisição POST com opções flexíveis.
   *
   * @template T - Tipo esperado da resposta.
   * @param {string} url - Caminho da API (sem a base).
   * @param {any} data - Corpo da requisição.
   * @param {any} [params={}] - Parâmetros adicionais.
   * @param {string} [api=''] - Identificador da API.
   * @param {boolean} [formData=false] - Se true, não define Content-Type (usado em upload).
   * @param {boolean} [blob=false] - Se true, espera retorno como `Blob`.
   * @param {boolean} [response=false] - Se true, retorna objeto completo da resposta.
   * @returns {Observable<HttpEvent<T>>} - Observable da resposta HTTP.
   */
  post<T>(url: string, data: any, params: any = {}, api: string = '', formData = false, blob = false, response = false): Observable<HttpEvent<T>> {
    this.createHttpOptions(formData);
    url = this.getApi(api) + url;

    if (blob) {
      this.httpOptions['observe'] = 'response';
      this.httpOptions['responseType'] = 'blob' as 'json';
    }

    if (response) {
      this.httpOptions['observe'] = 'response';
    }

    return this.http.post<T>(url, data, { ...this.httpOptions, params });
  }

  /**
   * Realiza uma requisição PUT.
   *
   * @template T - Tipo esperado da resposta.
   * @param {string} url - Caminho da API (sem a base).
   * @param {any} data - Corpo da requisição.
   * @param {string} [api=''] - Identificador da API.
   * @returns {Observable<HttpEvent<T>>} - Observable da resposta HTTP.
   */
  put<T>(url: string, data: any, api: string = ''): Observable<HttpEvent<T>> {
    this.createHttpOptions();
    url = this.getApi(api) + url;
    return this.http.put<T>(url, data, this.httpOptions);
  }

  /**
   * Realiza uma requisição DELETE, com suporte a corpo.
   *
   * @template T - Tipo esperado da resposta.
   * @param {string} url - Caminho da API (sem a base).
   * @param {string} [api=''] - Identificador da API.
   * @param {any} [params=null] - Parâmetros opcionais enviados no corpo.
   * @returns {Observable<HttpEvent<T>>} - Observable da resposta HTTP.
   */
  delete<T>(url: string, api: string = '', params = null): Observable<HttpEvent<T>> {
    this.createHttpOptions();
    if (params != null) {
      this.httpOptions['body'] = params;
    }
    url = this.getApi(api) + url;
    return this.http.delete<T>(url, this.httpOptions);
  }

  /**
   * Realiza uma requisição PATCH.
   *
   * @template T - Tipo esperado da resposta.
   * @param {string} url - Caminho da API (sem a base).
   * @param {any} data - Corpo da requisição.
   * @param {string} [api=''] - Identificador da API.
   * @returns {Observable<HttpEvent<T>>} - Observable da resposta HTTP.
   */
  patch<T>(url: string, data: any, api: string = ''): Observable<HttpEvent<T>> {
    this.createHttpOptions();
    url = this.getApi(api) + url;
    return this.http.patch<T>(url, data, this.httpOptions);
  }
}
