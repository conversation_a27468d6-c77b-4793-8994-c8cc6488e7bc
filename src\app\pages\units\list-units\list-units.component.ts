import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

import { Status, MultiSelectDefault } from 'src/app/constants/app.constants';
import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';

import { ClientUnitService as ClientUnitServiceApi } from 'src/app/services/api/clientUnit.service';
import { UserService } from 'src/app/services/user.service';
import { FilterService } from 'src/app/services/filter.service';

import fn from 'src/app/utils/function.utils';
import { NgxSpinnerService } from 'ngx-spinner';

//Tour guiado
import { CustomTourService } from 'src/app/services/custom-tour.service';
import { TourService } from 'ngx-ui-tour-ng-bootstrap';

@Component({
  selector: 'app-list-units',
  templateUrl: './list-units.component.html',
  styleUrls: ['./list-units.component.scss']
})
export class ListUnitsComponent implements OnInit {
  @ViewChild('hierarchy') hierarchy: any;

  tableHeader: any = [
    { label: 'ID', width: '50px', show: true, referent: ['search_identifier'] },
    { label: 'Cliente', width: '50%', show: true, referent: ['client_name'] },
    { label: 'Unidade', width: '50%', show: true, referent: ['name'] },
    { label: 'Status', width: '100px', show: true, referent: ['active'] },
    { label: 'Ações', width: '100px', show: true, referent: ['miniDashboard'] }
  ];

  public tableData = [];
  public selectedColumns = this.tableHeader;
  public labelActions: boolean = true;

  public func = fn;

  public status: any = Status;
  public clients: any = [];
  public units: any = [];

  public viewSettings = MultiSelectDefault.View;
  public selectedClient: any = null;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public filterParams: any = {};
  public filter: any = {
    Name: '',
    Active: '',
    SearchIdentifier: '',
    StartDate: '',
    EndDate: ''
  };

  public clientUnit: any = {
    id: null,
    active: null
  };

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    }
  };

  public filterSearch: any = {};

  constructor(
    private clientUnitServiceApi: ClientUnitServiceApi,
    private customTourService: CustomTourService,
    private filterService: FilterService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    public tourService: TourService,
    private userService: UserService
  ) {}

  /**
   * Método executado na inicialização do componente.
   * Carrega o perfil do usuário, permissões e exibe o spinner de carregamento.
   * Se o usuário não tiver permissão para listar clientes, remove o elemento de clientes da visualização.
   */
  ngOnInit(): void {
    this.ngxSpinnerService.show();

    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    if (!this.permissoes['/clients'].list) {
      delete this.elements.clients;
    }
  }

  /**
   * Método executado após a inicialização da visualização do componente.
   * Verifica os filtros de Cliente e Unidade e inicia o gerenciamento dos filtros após um delay.
   */
  ngAfterViewInit(): void {
    if (this.permissoes['/units'].list) {
      setTimeout(() => {
        // Verificar se os filtros de Cliente e Unidade estão preenchidos
        if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
          this.managerFilters(true); // Dispara a busca automaticamente
        } else {
          this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
        }
      }, 2000); // Ajuste o tempo do delay se necessário
    }
  }

  /**
   * Recupera a lista de unidades do cliente com base nos parâmetros fornecidos.
   * Exibe o spinner durante o carregamento e define uma mensagem de retorno caso nenhum registro seja encontrado.
   *
   * @param {any} params - Parâmetros de busca para filtrar as unidades do cliente.
   */
  getUnitsList(params) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.clientUnitServiceApi.getClientUnits(params).subscribe(
      (resp) => {
        const dados: any = resp;
        if (dados.status == 200) {
          this.tableData = dados.body.data ? dados.body.data : [];
          this.collectionSize = dados.body.total_items_count;
          this.formatData();
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * Edita as unidades do cliente e atualiza o status com uma mensagem de confirmação.
   */
  editClientUnits() {
    this.clientUnitServiceApi.patchClientUnits(this.clientUnit.id, this.clientUnit).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.AlteracaoStatus;
      this.message.status = true;
      this.message.class = 'alert-success';

      setTimeout(() => {
        this.message.status = false;
      }, 4000);
    });
  }

  /**
   * Realiza a busca de unidades de clientes com base nos filtros definidos.
   * Define os parâmetros de filtro para a busca e chama o método de listagem de unidades.
   */
  searchClientUnit() {
    let filterHierarchy = this.hierarchy.getFilters();
    this.filterParams = {};

    this.filterParams = {
      Active: this.filter.Active,
      Name: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].name : '',
      SearchIdentifier: this.filter.SearchIdentifier == null ? '' : this.filter.SearchIdentifier,
      ClientId: filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : ''
    };

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.getUnitsList(this.filterSearch);
  }

  /**
   * Redefine os filtros para os valores padrão e atualiza o formulário e a visualização.
   */
  resetFilter() {
    this.hierarchy.resetFilters();

    this.filter = {
      Name: '',
      Active: '',
      SearchIdentifier: '',
      StartDate: '',
      EndDate: ''
    };

    this.filterParams = {};
    this.filterSearch = {};

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.managerFilters();
  }

  /**
   * Gerencia os filtros com base na interação do usuário. Se `$btn` for verdadeiro, dispara a busca.
   * Caso contrário, utiliza os filtros armazenados e configura a exibição dos dados.
   *
   * @param {boolean} [$btn=false] - Define se a busca deve ser disparada automaticamente.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchClientUnit();
    } else {
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchClientUnit();
      } else {
        this.filterSearch = data.filters;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;

        //Formulario de filtro
        this.filter.Name = this.filterSearch.Name;
        this.filter.Active = this.filterSearch.Active;
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier;
        this.filter.ClientId = this.filterSearch.ClientId;

        this.getUnitsList(this.filterSearch);
      }

      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
        this.hierarchy.setUnits(data.filtersHierarchy.units);
      }
    }
  }

  /**
   * Formata os dados da tabela, adicionando propriedades de cliente para cada item.
   * Retorna o array de dados atualizado.
   */
  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      item.client_id = item.client.id;
      item.client_name = item.client.name;
      item.client_active = item.client.active;
      return item;
    });
  }

  /**
   * Alterna o status ativo/inativo de uma unidade do cliente e chama o método de edição de unidade.
   *
   * @param {any} clientUnit - Objeto que representa a unidade do cliente a ser alterada.
   */
  toggleStatus(clientUnit: any) {
    this.clientUnit.id = clientUnit.id;
    this.clientUnit.active = clientUnit.active;
    this.editClientUnits();
  }

  /**
   * Gerencia a visibilidade das colunas na tabela de visualização com base no tipo de seleção.
   * Alterna a exibição de colunas específicas ou todas.
   *
   * @param {any} $event - Evento de seleção ou desseleção de colunas.
   * @param {string} type - Tipo de ação para a exibição das colunas ('select', 'deselect', 'selectAll', 'deselectAll').
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Evento de clique na linha da tabela, executando a ação de navegação para edição ou visualização.
   *
   * @param {any} [$event=null] - Evento que contém a ação a ser realizada e o ID da linha selecionada.
   */
  clickRowEvent($event: any = null) {
    switch ($event.action) {
      case 'edit':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/edit']);
        break;
      case 'view':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/view']);
        break;
      default:
        break;
    }
  }

  loadTourGuide() {
    this.customTourService.startTour(this.tourService, 'assets/tour-guide/list-units.tourguide.json');
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }

    this.managerFilters();
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  /**
   * Redireciona o usuário para a página inicial.
   */
  goBack() {
    this.router.navigate(['/']);
  }
}
