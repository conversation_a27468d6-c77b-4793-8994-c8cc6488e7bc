import { APP_INITIALIZER, NgModule } from '@angular/core';
import { AuthModule, OidcConfigService } from 'angular-auth-oidc-client';
import { environment } from 'src/environments/environment';

export const configureAuth = (oidcConfigService: OidcConfigService) => {
  return () => {
    oidcConfigService.withConfig({
      stsServer: environment.kcServer,
      redirectUrl: `${window.location.origin}`,
      clientId: environment.kcId,
      scope: 'openid profile email offline_access',
      responseType: 'code',
      postLogoutRedirectUri: `${window.location.origin}`,
      startCheckSession: false,
      silentRenew: true,
      silentRenewUrl: `${window.location.origin}/silent-renew.html`,
      renewTimeBeforeTokenExpiresInSeconds: 600,
      maxIdTokenIatOffsetAllowedInSeconds: 600,
      authWellknownEndpoint: environment.kcAuth,
      triggerAuthorizationResultEvent: true,
      postLoginRoute: '',
      unauthorizedRoute: '/unauthorized',
      storage: localStorage
    });
  };
};

@NgModule({
  imports: [AuthModule.forRoot()],
  providers: [
    OidcConfigService,
    {
      provide: APP_INITIALIZER,
      useFactory: configureAuth,
      deps: [OidcConfigService],
      multi: true
    }
  ],
  exports: [AuthModule]
})
export class AuthConfigModule {}
