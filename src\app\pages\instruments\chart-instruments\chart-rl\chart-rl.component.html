<form [formGroup]="formChart">
  <div class="row g-3 mt-1">
    <!-- Instrumento -->
    <div class="col-md-6" *ngIf="instrument_upstream.length > 0">
      <label class="form-label">Instrumento (montante):</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="instrumentsSettings"
        [data]="instrument_upstream"
        formControlName="instrument_upstream"
      >
      </ng-multiselect-dropdown>
    </div>
    <div class="col-md-3" *ngIf="instrument_downstream.length > 0">
      <label class="form-label">Instrumento (jusante):</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="instrumentsSettings"
        [data]="instrument_downstream"
        formControlName="instrument_downstream"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Data e hora inicial -->
    <div class="col-md-3">
      <label class="form-label">Data e hora inicial:</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="start_date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('start_date').valid &&
          formChart.get('start_date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Data e hora final -->
    <div class="col-md-3">
      <label class="form-label">Data e hora final:</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="end_date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('end_date').valid && formChart.get('end_date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>
  <div class="row">
    <!-- Marcador -->
    <div class="col-md-2">
      <label class="form-label">Marcador</label>
      <ng-select
        [items]="markers"
        formControlName="marker"
        bindValue="marker"
        bindLabel="label"
        [clearable]="false"
        [multiple]="false"
        [closeOnSelect]="true"
      >
        <ng-template ng-label-tmp let-item="item">
          <div style="display: flex; align-items: center">
            <img
              [src]="item.img"
              alt="{{ item.label }}"
              style="width: 50px; height: 20px; margin-right: 8px"
            />
          </div>
        </ng-template>
        <ng-template ng-option-tmp let-item="item" let-index="index">
          <div style="display: flex; align-items: center">
            <img
              [src]="item.img"
              alt="{{ item.label }}"
              style="width: 50px; height: 20px; margin-right: 8px"
            />
          </div>
        </ng-template>
      </ng-select>
    </div>
    <div class="col-md-2">
      <label class="form-label">Tamanho marcador</label>
      <input
        type="number"
        class="form-control"
        min="1"
        max="12"
        step="1"
        formControlName="marker_length"
      />
    </div>
    <!-- Cor -->
    <div
      class="col-md-2 d-flex flex-column justify-content-start"
      (clickOutside)="onClickedOutside('colorPicker', 0)"
    >
      <label class="form-label">Cor:</label>
      <button
        class="color form-control"
        (click)="showColorPicker[0] = !showColorPicker[0]"
        [style.background]="selectedColor[0]"
      ></button>
      <div *ngIf="showColorPicker[0]" style="position: relative">
        <color-sketch
          [color]="selectedColor[0]"
          (onChangeComplete)="changeComplete($event, 0)"
          class="color-picker-container"
        ></color-sketch>
      </div>
    </div>
  </div>
  <!-- Grafico -->
  <div class="row mt-3">
    <div class="col-md-4">
      <app-button
        [class]="'btn-logisoil-green me-2 text-nowrap'"
        [customBtn]="true"
        [icon]="'fa fa-line-chart'"
        [label]="'Gerar gráfico'"
        (click)="getChart()"
      ></app-button>
    </div>
  </div>
  <!-- Alerta -->
  <div class="row mt-3">
    <div class="col-md-12">
      <div
        class="alert alert-warning"
        role="alert"
        *ngIf="messageReturn.status"
      >
        {{ messageReturn.text }}
      </div>
    </div>
  </div>
  <div class="row mt-2 mb-3">
    <div class="col-md-2" *ngIf="chart.options">
      <label class="form-label"
        ><label class="form-label"
          >Tamanho: {{ formChart.controls['chart_height'].value }} px</label
        >
        <input
          type="range"
          class="range"
          #chartHeight
          min="300"
          max="1600"
          step="10"
          formControlName="chart_height"
          (input)="setHeight(chartHeight.value)"
        />
      </label>
    </div>
    <div class="col-md-12" *ngIf="chart.options">
      <app-e-charts
        [dataChart]="chart"
        [height]="formChart.controls['chart_height'].value"
      ></app-e-charts>
    </div>
  </div>
  <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [click]="goBack.bind(this)"
    ></app-button>
  </div>
</form>
