import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalSearchMethodComponent } from './modal-search-method.component';

describe('ModalSearchMethodComponent', () => {
  let component: ModalSearchMethodComponent;
  let fixture: ComponentFixture<ModalSearchMethodComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalSearchMethodComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalSearchMethodComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
