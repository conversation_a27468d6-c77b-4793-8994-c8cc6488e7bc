# Playwright E2E Tests for Logisoil Frontend

This directory contains end-to-end tests for the Logisoil Angular application using Playwright.

## Overview

The Logisoil application uses OIDC (OpenID Connect) authentication with Keycloak. The tests are designed to:

1. Navigate to the application
2. Handle the redirect to Keycloak authentication
3. Attempt login with provided credentials
4. Verify that the login fails (as expected with invalid credentials)
5. Capture screenshots and logs for debugging

## Test Structure

### `login.spec.js`
Contains tests for the authentication flow:

- **Main Login Test**: Tests the complete login flow with invalid credentials and verifies failure
- **Network Error Test**: Tests how the application handles network failures during authentication
- **Loading Spinner Test**: Verifies that loading indicators are shown during authentication

## Prerequisites

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Install Playwright Browsers**:
   ```bash
   npm run playwright:install
   ```

3. **Start the Angular Application**:
   ```bash
   npm start
   ```
   The application should be running on `http://localhost:4200`

## Running Tests

### Run all tests in headful mode (browser visible):
```bash
npm run test:e2e:headed
```

### Run all tests in headless mode:
```bash
npm run test:e2e
```

### Run tests with Playwright UI (interactive mode):
```bash
npm run test:e2e:ui
```

### Run specific test file:
```bash
npx playwright test tests/login.spec.js --headed
```

## Test Configuration

The tests are configured to:
- Run in **headful mode** by default (browser visible)
- Use a **slow motion** of 500ms between actions for better visibility
- Take **screenshots** on failure
- Record **videos** on failure
- Generate an **HTML report** after test execution

## Test Credentials

The tests use the following credentials (which should fail):
- **Username**: `<EMAIL>`
- **Password**: `123455`

## Expected Behavior

Since the provided credentials are invalid, the tests expect:
1. The login attempt to fail
2. An error message to be displayed on the Keycloak login page
3. The user to remain on the authentication page (not redirected back to the app)

## Screenshots and Reports

- **Screenshots**: Saved to `tests/screenshots/` directory
- **Videos**: Saved to `test-results/` directory (on failure)
- **HTML Report**: Generated after test execution, can be viewed with `npx playwright show-report`

## Debugging

### Console Logs
The tests include extensive console logging to help debug issues:
- Page navigation events
- Element detection
- Error messages
- URL changes

### Screenshots
Screenshots are automatically taken:
- On test failure
- At specific points during the login flow
- For network error scenarios

### Common Issues

1. **Keycloak Server Unreachable**: If the Keycloak server is down or unreachable, tests will fail
2. **Slow Network**: Increase timeout values if network is slow
3. **Different Keycloak UI**: If Keycloak UI changes, selectors may need updating

## Customization

### Updating Selectors
If the Keycloak login page changes, update the selectors in `login.spec.js`:
- `usernameSelectors` array for username field
- `loginButtonSelectors` array for submit button
- `errorSelectors` array for error messages

### Changing Credentials
Update the credentials in the test file:
```javascript
await usernameField.fill('<EMAIL>');
await passwordField.fill('your-password');
```

### Timeout Adjustments
Modify timeout values in `playwright.config.js` or individual tests if needed.

## Browser Support

Tests are configured to run on:
- **Chromium** (Chrome/Edge)
- **Firefox**
- **WebKit** (Safari)

All browsers run in headful mode with slow motion for better visibility during test execution.
