import {
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  QueryList,
  Renderer2,
  ViewChild,
  ViewChildren,
  ViewEncapsulation
} from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MessageCadastro, MessageInputInvalid } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { groupInstruments } from 'src/app/constants/instruments.constants';

import { GoogleMapsComponent } from '@components/google-maps/google-maps.component';

import { DataService } from 'src/app/services/data.service';
import { UserService } from 'src/app/services/user.service';
import { SharedService } from 'src/app/services/shared.service';

import { Subscription } from 'rxjs';

import { ClientService } from 'src/app/services/api/client.service';
import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { StructuresService } from 'src/app/services/api/structure.service';

import fn from 'src/app/utils/function.utils';

import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-group-instruments',
  templateUrl: './group-instruments.component.html',
  styleUrls: ['./group-instruments.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class GroupInstrumentsComponent implements OnInit {
  @ViewChild(GoogleMapsComponent) googleMaps: GoogleMapsComponent;
  @ViewChildren('listInstrumentRef') listInstrumentRef: QueryList<ElementRef>;

  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;

  @Output() public sendStructure = new EventEmitter();

  public formGroupInstruments: FormGroup = new FormGroup({
    id: new FormControl(null),
    name: new FormControl('', [Validators.required]),
    client: new FormControl([], [Validators.required]),
    client_unit: new FormControl([], [Validators.required]),
    structure: new FormControl([], [Validators.required]),
    //Validacao
    amountInstruments: new FormControl('', [Validators.min(1), Validators.required])
  });

  public clients: any = [];
  public instruments: any = [];
  public structures: any = [];
  public units: any = [];

  public clientSettings = MultiSelectDefault.Single;
  public structureSettings = MultiSelectDefault.Single;
  public unitSettings = MultiSelectDefault.Single;

  public listTypeInstruments: any = [];

  public groupInstruments: any = groupInstruments;

  public filter: any = { ClientId: '', ClientUnitId: '', StructureId: '' };

  public ctrlGroup: boolean = false;
  public editGroup: boolean = false;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;
  public messageReturn: any = { text: '', status: false };
  public action: string = '';

  public func = fn;

  public showMaps: boolean = false;

  public dataMapsStructure = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  //Tabela dos grupos de instrumentos cadastrados
  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Grupo de Instrumentos',
      width: '80%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'Ações',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom']
    }
  ];

  //Coluna Acoes
  public actionCustom: any = [
    {
      class: 'btn-logisoil-show-location',
      icon: 'fa fa-thin fa-location-dot',
      label: '',
      title: 'Exibir locação no mapa',
      type: 'true',
      option: 'show'
    },
    {
      class: 'btn-logisoil-edit-group ',
      icon: 'fa fa-thin fa-pen-to-square',
      label: '',
      title: 'Editar grupo',
      type: 'true',
      option: 'edit'
    },
    {
      class: 'btn-logisoil-remove-item',
      icon: 'fa fa-thin fa-trash-can',
      label: '',
      title: 'Excluir grupo',
      type: 'true',
      option: 'remove'
    }
  ];

  public document: any = null;
  public clickEventsubscription: Subscription;
  public intervalId: any = null;
  public selectedGroupInstrumentId: string = '';

  public instrumentsByList: any = [];

  constructor(
    private clientService: ClientService,
    private clientUnitService: ClientUnitService,
    private dataService: DataService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private structuresService: StructuresService,
    private userService: UserService,
    @Inject(DOCUMENT) documentDom: Document,
    private renderer: Renderer2,
    private element: ElementRef,
    private sharedService: SharedService,
    private ngxSpinnerService: NgxSpinnerService
  ) {
    this.document = documentDom;
    this.clickEventsubscription = this.sharedService.getClickEvent().subscribe((instrument) => {
      this.eventMap(instrument);
    });
  }

  /**
   * Método que é executado ao iniciar o componente.
   * Inicializa as permissões do usuário, gerencia a lista de grupos de instrumentos e obtém a lista de clientes.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    if (this.sharedService.groupInstrumentsList.length > 0) {
      this.managerGroupByList();
    }

    this.getClients();
    this.loadFilter(this.formGroupInstruments, 'client', 'client_unit', 'structure', false);
  }

  // Obtém a lista de clientes disponíveis e configura as unidades e estruturas baseadas no cliente selecionado.
  getClients() {
    this.clients = [];
    this.units = [];
    this.structures = [];

    this.formGroupInstruments.get('client').setValue('');
    this.formGroupInstruments.get('client_unit').setValue('');
    this.formGroupInstruments.get('structure').setValue('');

    this.clientService.getClientsList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;

      if (this.clients.length == 1) {
        this.formGroupInstruments.get('client').setValue(this.clients);
        this.getUnits(this.clients[0]);
      }
    });
  }

  /**
   * Obtém a lista de unidades para o cliente selecionado e as armazena na propriedade `units`.
   * @param {any} client - O cliente selecionado.
   * @param {string} [action='select'] - A ação a ser realizada, podendo ser 'select' ou 'deselect'.
   */
  getUnits(client, action: string = 'select') {
    this.units = [];
    this.structures = [];

    this.formGroupInstruments.get('client_unit').setValue('');
    this.formGroupInstruments.get('structure').setValue('');

    if (action === 'select') {
      this.clientUnitService.getClientUnitsId({ clientId: client.id, active: true }).subscribe(
        (resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;
          this.units = dados;

          if (this.units.length == 1) {
            this.formGroupInstruments.get('client_unit').setValue(this.units);
            this.getStructures(this.units[0]);
          }
        },
        (error) => {
          console.error('Erro ao carregar estruturas', error);
        }
      );
    } else {
      this.googleMaps.clearMap();
    }
  }

  /**
   * Obtém a lista de estruturas para a unidade do cliente selecionada e as armazena na propriedade `structures`.
   * @param {any} clientUnit - A unidade do cliente selecionada.
   * @param {string} [action='select'] - A ação a ser realizada, podendo ser 'select' ou 'deselect'.
   */
  getStructures(clientUnit, action: string = 'select') {
    this.structures = [];
    this.formGroupInstruments.controls['structure'].setValue([]);

    if (action === 'select') {
      this.structuresService.getStructureList({ clientUnitId: clientUnit.id, active: true }).subscribe(
        (resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;
          this.structures = dados;

          if (this.structures.length == 1) {
            this.formGroupInstruments.get('structure').setValue(this.structures);
          }
        },
        (error) => {
          console.error('Erro ao carregar estruturas', error);
        }
      );
    } else {
      this.googleMaps.clearMap();
    }
  }

  // Obtém a lista de grupos de instrumentos para a estrutura selecionada e os exibe na tabela.
  getGroupInstruments() {
    this.ngxSpinnerService.show();
    this.messageReturn.text = '';
    this.messageReturn.status = false;

    if (this.formGroupInstruments.controls['structure'].value.length > 0) {
      let structureId = this.formGroupInstruments.controls['structure'].value[0].id;
      const params = {
        Page: this.page,
        PageSize: this.pageSize,
        StructureId: structureId
      };

      this.instrumentsServiceApi.getGroupInstrumentsSearch(params).subscribe(
        (resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;

          if (dados) {
            this.tableData = dados ? dados.data : [];
            this.collectionSize = dados.total_items_count;
          } else {
            this.tableData = [];
            this.collectionSize = 0;
            this.messageReturn.text = MessageInputInvalid.NoGroups;
            this.messageReturn.status = true;
            this.message.class = 'alert-warning';

            setTimeout(() => {
              this.messageReturn.status = false;
            }, 4000);
          }
          this.ngxSpinnerService.hide();
        },
        (error) => {
          console.log(error);
          this.ngxSpinnerService.hide();
        }
      );
    }
  }

  /**
   * Cadastra um novo grupo de instrumentos usando os dados fornecidos.
   * @param {any} groupInstrumentsRequest - Os dados do grupo de instrumentos a ser cadastrado.
   */
  registerGroupInstruments(groupInstrumentsRequest) {
    this.instrumentsServiceApi.postGroupInstruments(groupInstrumentsRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.GrupoInstrumentoCadastrado;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.getGroupInstruments();
      },
      (error) => {
        console.log(error);
        if (error.status === 400) {
          const index = Object.keys(error.error.errors);
          this.messagesError = error.error.errors[index[0]][0];
        }
      }
    );
  }

  /**
   * Seleciona um grupo de instrumentos para edição, exibindo os dados do grupo no formulário.
   * @param {string} [groupInstrumentId=null] - O ID do grupo de instrumentos a ser selecionado.
   */
  selectedGroupInstruments(groupInstrumentId: string = null) {
    this.selectedGroupInstrumentId = groupInstrumentId;
    this.removePulse();

    let idx = fn.findIndexInArrayofObject(this.tableData, 'id', groupInstrumentId);
    let markerId = [];

    this.tableData[idx].instruments.forEach((instrument) => {
      if (this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]')) {
        markerId.push('mk-' + instrument.identifier);
      }
    });

    this.googleMaps.openInfoWindowFromMarkerId(markerId, 'mk-');

    this.formGroupInstruments.controls['id'].setValue(groupInstrumentId);
    this.formGroupInstruments.controls['name'].setValue(this.tableData[idx].name);

    this.ctrlGroup = true;
    this.editGroup = true;
    this.showMaps = true;

    setTimeout(() => {
      this.tableData[idx].instruments.forEach((instrument) => {
        if (this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]')) {
          this.managerInstruments(instrument);
        }
      });
    }, 5);
  }

  //Seleciona os dados de um grupo de instrumentos a partir da lista de instrumentos, exibindo-os no mapa.
  selectedGroupInstrumentsList() {
    this.removePulse();

    let markerId = [];

    this.instrumentsByList.forEach((instrument) => {
      if (this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]')) {
        markerId.push('mk-' + instrument.identifier);
      }
    });

    this.googleMaps.openInfoWindowFromMarkerId(markerId, 'mk-');

    this.ctrlGroup = true;
    this.editGroup = false;
    this.showMaps = true;

    setTimeout(() => {
      this.instrumentsByList.forEach((instrument) => {
        if (this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]')) {
          this.managerInstruments(instrument);
        }
      });
    }, 5);
  }

  /**
   * Edita um grupo de instrumentos existente com os novos dados fornecidos.
   * @param {any} groupInstrumentsRequest - Os dados atualizados do grupo de instrumentos.
   */
  editGroupInstruments(groupInstrumentsRequest) {
    this.instrumentsServiceApi.putGroupInstruments(groupInstrumentsRequest.id, groupInstrumentsRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.refreshGroupInstruments();
      },
      (error) => {
        console.log(error);
        if (error.status === 400) {
          const index = Object.keys(error.error.errors);
          this.messagesError = error.error.errors[index[0]][0];
        }
      }
    );
  }

  /**
   * Exclui um grupo de instrumentos com base no ID fornecido.
   * @param {string} groupInstrumentId - O ID do grupo de instrumentos a ser excluído.
   */
  deleteGroupInstruments(groupInstrumentId: string) {
    this.instrumentsServiceApi.deleteGroupInstruments(groupInstrumentId).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.DeleteGrupoInstrumento;
        this.message.status = true;
        this.message.class = 'alert-success';

        this.action = 'delete';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.getGroupInstruments();
      },
      (error) => {
        console.log(error);
        if (error.status === 400) {
          const index = Object.keys(error.error.errors);
          this.messagesError = error.error.errors[index[0]][0];
        }
      }
    );
  }

  //Obtém a lista de tipos de instrumentos disponíveis para a estrutura selecionada.
  getListTypeInstruments() {
    this.instruments = [];
    if (this.formGroupInstruments.controls['structure'].value.length > 0) {
      let structureId = this.formGroupInstruments.controls['structure'].value[0].id;
      this.instrumentsServiceApi.getGroupInstrumentsMaps({ StructureId: structureId }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.instruments = dados;
        this.managerListTypeInstruments();
      });
    }
  }

  /**
   * Formata os dados do formulário de grupo de instrumentos e realiza o cadastro ou edição.
   * @param {string} [type=''] - O tipo de operação (cadastrar ou editar).
   */
  formatData(type: string = '') {
    let groupInstrumensRequest = {};
    groupInstrumensRequest['name'] = this.formGroupInstruments.controls['name'].value;
    groupInstrumensRequest['instruments'] = [];
    this.listInstrumentRef.toArray().forEach((listInstrumentItem) => {
      let formTypeInstruments = listInstrumentItem['formTypeInstruments'];
      if (formTypeInstruments.controls['typeInstrument'].value) {
        formTypeInstruments.controls['typeInstrument'].value.forEach((instrument) => {
          groupInstrumensRequest['instruments'].push({ id: instrument.id });
        });
      }
    });

    if (!this.editGroup) {
      this.registerGroupInstruments(groupInstrumensRequest);
    } else {
      groupInstrumensRequest['id'] = this.formGroupInstruments.controls['id'].value;
      this.editGroupInstruments(groupInstrumensRequest);
    }
  }

  //Gerencia a tela separando os instrumentos da estrutura por tipo, para popular os multiselects.
  managerListTypeInstruments() {
    this.listTypeInstruments = this.groupInstruments.map((groupInstrumentItem: any, index: number) => {
      let item = {};
      item = groupInstrumentItem;
      item['instruments'] = [];
      item['item'] = index;
      return item;
    });

    if (!fn.isEmpty(this.instruments)) {
      this.instruments.map((instrumentItem) => {
        let idx = fn.findIndexInArrayofObject(this.listTypeInstruments, 'type', instrumentItem.type);
        this.listTypeInstruments[idx].instruments.push(instrumentItem);
      });
      this.intervalId = setInterval(() => {
        this.plotInstruments(this.listTypeInstruments);

        setTimeout(() => {
          if (this.selectedGroupInstrumentId != '') {
            this.selectedGroupInstruments(this.selectedGroupInstrumentId);
          }
        }, 5);
      }, 200);
    }
  }

  /**
   * Gerencia a exibição e ocultação de estruturas e instrumentos no mapa de acordo com a ação selecionada.
   * @param {string} [action='select'] - A ação a ser realizada, podendo ser 'select' ou 'deselect'.
   */
  managerStructure(action = 'select') {
    if (action === 'select') {
      this.mapsStructure();
      this.getListTypeInstruments();
      this.getGroupInstruments();
    } else {
      this.tableData = [];
      this.googleMaps.clearMap();
    }
  }

  /**
   * Gerencia a exibição dos instrumentos no mapa e a atualização dos botões de ação.
   * @param {any} instrument - O instrumento a ser gerenciado.
   * @param {string|null} [option=null] - Opção adicional para controle.
   */
  managerInstruments(instrument, option = null) {
    this.ctrlGroup = true;

    let findIdentifier = instrument.data ? instrument.data.id : instrument.identifier;
    let idx = fn.findIndexInArrayofObject(this.instruments, 'identifier', findIdentifier);
    let selectedInstrument = this.instruments[idx];

    this.listInstrumentRef.toArray().every((listInstrumentItem) => {
      if (listInstrumentItem['type'] == selectedInstrument.type) {
        let dados = listInstrumentItem['formTypeInstruments'].controls['typeInstrument'].value;
        dados = dados.length == 0 ? [] : dados;

        let instrument = { id: selectedInstrument.id, identifier: selectedInstrument.identifier };

        let action =
          this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).innerHTML.search('Selecionar') !== -1 ? 'add' : 'remove';

        if (option == 'edit') {
          action = 'add';
        }

        if (action === 'add') {
          dados = fn.managerArrayObject(dados, instrument, 'addIfNotExist');
          if (dados) {
            listInstrumentItem['formTypeInstruments'].controls['typeInstrument'].setValue(dados);
            this.managerButtonInfoWindow(instrument, 'toRemove');
            this.validateAmount('add');
            return false;
          }
        } else if (action === 'remove') {
          dados = fn.managerArrayObject(dados, instrument, 'removeIfExist');
          if (dados) {
            listInstrumentItem['formTypeInstruments'].controls['typeInstrument'].setValue(dados);
            this.managerButtonInfoWindow(instrument, 'toAdd');
            this.validateAmount('remove');
            return false;
          }
        }
      }
      return true;
    });
  }

  /**
   * Gerencia a alternância dos botões de adicionar e remover no infoWindow de um instrumento.
   * @param {any} instrument - O instrumento a ser gerenciado no infoWindow.
   */
  managerInstrumentsInfoWindow(instrument) {
    let findIdentifier = instrument.data ? instrument.data.id : instrument.identifier;
    let idx = fn.findIndexInArrayofObject(this.instruments, 'identifier', findIdentifier);
    let selectedInstrument = this.instruments[idx];
    let action = 'toAdd';

    this.listInstrumentRef.toArray().every((listInstrumentItem) => {
      if (listInstrumentItem['type'] == selectedInstrument.type) {
        let dados = listInstrumentItem['formTypeInstruments'].controls['typeInstrument'].value;
        dados = dados.length == 0 ? [] : dados;

        let instrument = { id: selectedInstrument.id, identifier: selectedInstrument.identifier };

        if (dados.some((item) => JSON.stringify(item) === JSON.stringify(instrument))) {
          action = 'toRemove';
        }
        setTimeout(() => {
          this.managerButtonInfoWindow(instrument, action);
        }, 5);
        return false;
      }
      return true;
    });
  }

  /**
   * Atualiza o texto e o estilo CSS do botão no infoWindow de um instrumento.
   * @param {any} instrument - O instrumento a ser gerenciado.
   * @param {string} action - A ação a ser realizada ('toAdd' ou 'toRemove').
   */
  managerButtonInfoWindow(instrument, action) {
    if (action == 'toRemove' && this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier))) {
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).classList.remove('btn-logisoil-green');
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).classList.add('btn-logisoil-red');
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).innerHTML = 'Remover';
    } else if (action == 'toAdd' && this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier))) {
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).classList.remove('btn-logisoil-red');
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).classList.add('btn-logisoil-green');
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).innerHTML = 'Selecionar';
    }
  }

  /**
   * Manipula a seleção e desmarcação de itens nos multiselects dos instrumentos.
   * @param {any} $event - O evento que contém os dados dos instrumentos selecionados ou desmarcados.
   */
  getInstrumentsInList($event) {
    let instruments = $event.instruments;
    let action = $event.action;
    switch (action) {
      case 'select':
        instruments = [instruments];
        break;
      case 'selectAll':
        break;
      case 'deselect':
        instruments = [instruments];
        break;
      case 'deselectAll':
        break;
    }
    instruments.forEach((instrument) => {
      if (action.substring(0, 3) === 'des') {
        this.managerButtonInfoWindow(instrument, 'toAdd');
        this.validateAmount('remove');
      } else if (action.substring(0, 3) === 'sel') {
        this.googleMaps.openInfoWindowFromMarkerId(['mk-' + instrument.identifier], 'mk-', false);
        setTimeout(() => {
          this.managerButtonInfoWindow(instrument, 'toRemove');
          this.validateAmount('add');
        }, 5);
      }
    });
  }

  /**
   * Valida se pelo menos um instrumento foi selecionado, ajustando a quantidade total.
   * @param {string} action - A ação a ser realizada ('add' ou 'remove').
   */
  validateAmount(action) {
    let amount = this.formGroupInstruments.controls['amountInstruments'].value == '' ? 0 : this.formGroupInstruments.controls['amountInstruments'].value;
    if (action == 'add') {
      amount++;
    } else if (action == 'remove') {
      amount--;
      this.formGroupInstruments.controls['amountInstruments'].markAsTouched();
    }
    this.formGroupInstruments.controls['amountInstruments'].setValue(amount < 0 ? 0 : amount);
  }

  //Exibe a estrutura selecionada no mapa, posicionando os marcadores de acordo com as coordenadas.
  mapsStructure() {
    let clientId = this.formGroupInstruments.controls['client'].value[0].id;
    if (this.formGroupInstruments.controls['structure'].value.length > 0) {
      let structureId = this.formGroupInstruments.controls['structure'].value[0].id;

      this.dataService.getStructureCoordinate(clientId, [structureId]).subscribe((coordinate) => {
        if (coordinate.length > 0) {
          this.dataMapsStructure.center.lat = coordinate[0].decimal_geodetic.latitude;
          this.dataMapsStructure.center.lng = coordinate[0].decimal_geodetic.longitude;
          this.dataMapsStructure.markers[0].position.lat = coordinate[0].decimal_geodetic.latitude;
          this.dataMapsStructure.markers[0].position.lng = coordinate[0].decimal_geodetic.longitude;
          this.sendDataMap('markers');
        }
      });
    } else {
      this.googleMaps.clearMap();
    }
  }

  /**
   * Exibe os instrumentos da estrutura selecionada no mapa.
   * @param {any} listTypeInstruments - A lista de tipos de instrumentos para plotagem no mapa.
   * @param {string} [action='select'] - A ação a ser realizada, podendo ser 'select' ou 'deselect'.
   */
  plotInstruments(listTypeInstruments, action: string = 'select') {
    let idx = -1;
    listTypeInstruments.forEach((element, index) => {
      if (element.instruments.length > 0 && idx == -1) {
        idx = index;
      }
    });
    if (idx == -1) {
      clearInterval(this.intervalId);
    }
    if (!this.element.nativeElement.querySelector('div[title="' + listTypeInstruments[idx].instruments[0].identifier + '"]')) {
      listTypeInstruments.forEach((listTypeInstrumentItem) => {
        listTypeInstrumentItem.instruments.forEach((instrument) => {
          let svgMarker = {
            path: 'M256 512c141.4 0 256-114.6 256-256S397.4 0 256 0S0 114.6 0 256S114.6 512 256 512z',
            fillColor: listTypeInstrumentItem.color,
            fillOpacity: 1.0,
            strokeWeight: 1.5,
            strokeColor: 'white',
            rotation: 0,
            scale: 0.025
            //anchor: new google.maps.Point(0, 20)
          };

          let marker = {
            position: {
              lat: instrument.decimal_geodetic_coordinate.latitude,
              lng: instrument.decimal_geodetic_coordinate.longitude
            },
            title: instrument.identifier,
            options: {},
            icon: svgMarker,
            id: 'mk-' + instrument.identifier
          };

          let infoWindowMarker = {
            content: '',
            ariaLabel: instrument.identifier,
            id: instrument.identifier,
            contentConfig: [
              {
                component: 'app-button',
                attrs: {
                  class: 'btn-logisoil-blue',
                  icon: '',
                  label: 'Selecionar',
                  type: true,
                  eventClick: true,
                  event: 'mapInstrument',
                  id: 'iw-button-' + fn.hashCode(instrument.identifier)
                }
              }
            ]
          };

          marker['infoWindowMarker'] = infoWindowMarker;

          this.dataMapsStructure.markers.push(marker);
        });
      });
      this.sendDataMap('markersMultiple', false);
    } else {
      clearInterval(this.intervalId);
    }
  }

  /**
   * Destaca os instrumentos no mapa ao clicar na ação de exibição na listagem.
   * @param {string} groupInstrumentId - O ID do grupo de instrumentos a ser exibido.
   */
  showInstrumentsMap(groupInstrumentId) {
    this.removePulse();
    let idx = fn.findIndexInArrayofObject(this.tableData, 'id', groupInstrumentId);
    let markerId = [];

    this.tableData[idx].instruments.forEach((instrument) => {
      if (this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]')) {
        this.renderer.addClass(this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]'), 'pulse-pin-green');
        markerId.push('mk-' + instrument.identifier);
      }
    });
    this.googleMaps.openInfoWindowFromMarkerId(markerId, 'mk-');
    this.showMaps = true;
  }

  //Remove o efeito de pulso dos instrumentos destacados no mapa.
  removePulse() {
    if (this.element.nativeElement.querySelector('.pulse-pin-green')) {
      let control = true;
      while (control) {
        this.element.nativeElement.querySelector('.pulse-pin-green').classList.remove('pulse-pin-green');
        if (!this.element.nativeElement.querySelector('.pulse-pin-green')) {
          control = false;
        }
      }
    }
  }

  //Recarrega o mapa e as informações dos grupos de instrumentos.
  refreshGroupInstruments() {
    this.dataMapsStructure.markers = [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ];

    this.managerStructure();

    this.listInstrumentRef.toArray().forEach((listInstrumentItem) => {
      let formTypeInstruments = listInstrumentItem['formTypeInstruments'];
      if (formTypeInstruments.controls['typeInstrument'].value) {
        formTypeInstruments.controls['typeInstrument'].setValue('');
      }
    });

    this.formGroupInstruments.controls['name'].setValue('');
    this.formGroupInstruments.controls['name'].setErrors(null);
    this.formGroupInstruments.controls['name'].markAllAsTouched();
  }

  /**
   * Manipula os eventos de mapa, gerenciando as ações dos instrumentos.
   * @param {any} $event - O evento que contém os dados dos instrumentos no mapa.
   */
  eventMap($event) {
    switch ($event.type) {
      case 'mapInstrument':
        this.managerInstruments($event);
        break;
      case 'mapInstrumentInfoWindow':
        this.managerInstrumentsInfoWindow($event);
        break;
    }
  }

  /**
   * Envia as informações atualizadas para o mapa, como marcadores e estrutura.
   * @param {string} option - A opção para determinar o que deve ser atualizado no mapa.
   * @param {boolean} [clear=true] - Define se o mapa deve ser limpo antes de adicionar novos dados.
   */
  sendDataMap(option, clear = true) {
    this.googleMaps.setDataMap(this.dataMapsStructure, option, clear);
  }

  /**
   * Manipula os eventos de clique nas linhas da tabela de grupos de instrumentos, executando a ação correspondente.
   * @param {any} $event - O evento que contém a ação e o ID do grupo de instrumentos.
   */
  clickRowEvent($event: any = null) {
    switch ($event.action) {
      case 'remove':
        this.deleteGroupInstruments($event.id);
        break;
      case 'edit':
        this.selectedGroupInstruments($event.id);
        break;
      case 'show':
        this.showMaps = true;
        setTimeout(() => {
          this.showInstrumentsMap($event.id);
        }, 500);
        break;
      default:
        break;
    }
  }

  /**
   * Redefine o formulário de grupo de instrumentos, limpando os campos e reiniciando as variáveis de controle.
   * @param {string} [type=''] - O tipo de formulário a ser redefinido ('group').
   * @param {boolean} [option=false] - Define se o controle de grupo deve ser mantido ativado.
   */
  resetForm(type: string = '', option: boolean = false) {
    if (type === 'group') {
      this.ctrlGroup = false;
      this.selectedGroupInstrumentId = '';
      this.refreshGroupInstruments();
      if (option) {
        this.ctrlGroup = true;
      }
      this.editGroup = false;
    }
  }

  /**
   * Valida o formulário de grupo de instrumentos antes de realizar o cadastro ou edição.
   * @param {string} [type=''] - O tipo de operação (cadastrar ou editar).
   */
  validate(type: string = '') {
    this.formatData(type);
  }

  /**
   * Método que carrega a página selecionada e executa a função correspondente.
   * @param {number} selectPage - O número da página a ser carregada.
   */
  loadPage(selectPage: number): void {
    this.page = selectPage;
    // this.getHistoryInstruments(this.activatedRoute.snapshot.params.instrumentId);
  }

  //Gerencia a exibição dos instrumentos no mapa e na lista, utilizando os dados compartilhados do serviço.
  managerGroupByList() {
    this.instrumentsByList = this.sharedService.groupInstrumentsList.map((instrument) => {
      let item = {};
      item['id'] = instrument.data.id;
      item['identifier'] = instrument.data.identifier;
      item['type'] = instrument.data.type;

      return item;
    });
    this.getUnits({ id: this.sharedService.client.id, name: this.sharedService.client.name });
    this.getStructures({ id: this.sharedService.clientUnit.id, name: this.sharedService.clientUnit.name });

    this.formGroupInstruments.controls['client'].setValue([this.sharedService.client]);
    this.formGroupInstruments.controls['client_unit'].setValue([this.sharedService.clientUnit]);
    this.formGroupInstruments.controls['structure'].setValue([this.sharedService.structure]);

    this.managerStructure('select');
    this.ngxSpinnerService.show();

    // setTimeout(() => {
    this.selectedGroupInstrumentsList();
    this.ngxSpinnerService.hide();
    // }, 4000);
  }

  /**
   * Carrega os filtros salvos no `localStorage` e preenche os campos do formulário com base nos dados disponíveis.
   * Verifica se há valores salvos para ClientId, ClientUnitId, e StructureId, e preenche os campos correspondentes no formulário.
   * Se os valores existirem, também busca as unidades e estruturas associadas.
   *
   * @param {FormGroup} $form - O formulário que será preenchido com os filtros.
   * @param {string} client - O nome do campo de cliente no formulário.
   * @param {string} unit - O nome do campo de unidade no formulário.
   * @param {string} structure - O nome do campo de estrutura no formulário.
   */
  loadFilter($form, client, unit, structure, onlyId = false) {
    const savedFilters = localStorage.getItem('filterHierarchy');
    if (savedFilters && !this.edit && !this.view) {
      const filterHierarchy = JSON.parse(savedFilters);
      const configClient = {
        value: onlyId ? filterHierarchy.ClientId.id : [{ id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }],
        param: onlyId ? filterHierarchy.ClientId.id : { id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }
      };

      const configClientUnit = {
        value: onlyId ? filterHierarchy.ClientUnitId.id : [{ id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }],
        param: onlyId ? filterHierarchy.ClientUnitId.id : { id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }
      };

      const configStructure = {
        value: onlyId ? filterHierarchy.StructureId.id : [{ id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }],
        param: onlyId ? filterHierarchy.StructureId.id : { id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }
      };

      // Verificar se existe ClientId, ClientUnitId, e StructureId e preenchê-los
      if (filterHierarchy.ClientId) {
        $form.get(client)?.setValue(configClient.value);
        this.getUnits(configClient.param);
      }

      if (filterHierarchy.ClientUnitId) {
        $form.get(unit)?.setValue(configClientUnit.value);
        this.getStructures(configClientUnit.param);
      }

      if (filterHierarchy.StructureId) {
        $form.get(structure)?.setValue(configStructure.value);
        this.managerStructure('select');
      }
    }
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }
}
