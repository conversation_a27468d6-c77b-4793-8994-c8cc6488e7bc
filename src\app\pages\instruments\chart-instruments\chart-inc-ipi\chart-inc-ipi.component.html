<form [formGroup]="formChart">
  <div class="row g-3 mt-1">
    <!-- Instrumento -->
    <div class="col-md-3">
      <label class="form-label">Instrumento:</label>
      <ng-multiselect-dropdown
        [settings]="instrumentsSettings"
        [data]="instruments"
        formControlName="instrument"
        (onSelect)="changeInstrument($event, 'select')"
        (onDeSelect)="changeInstrument($event, 'deselect')"
        [placeholder]="'Selecione...'"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Data e hora inicial -->
    <div class="col-md-3">
      <label class="form-label">Data e hora inicial:</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="start_date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('start_date').valid &&
          formChart.get('start_date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Data e hora final -->
    <div class="col-md-3">
      <label class="form-label">Data e hora final:</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="end_date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formChart.get('end_date').valid && formChart.get('end_date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>
  <!-- Grafico -->
  <div
    class="row mt-3"
    *ngIf="formChart.get('start_date').valid && formChart.get('end_date').valid"
  >
    <div class="col-md-4">
      <app-button
        [class]="'btn-logisoil-green me-2 text-nowrap'"
        [customBtn]="true"
        [icon]="'fa fa-line-chart'"
        [label]="'Gerar gráfico'"
        (click)="getChart()"
      ></app-button>
    </div>
  </div>

  <!-- Alerta -->
  <div class="row mt-3">
    <div class="col-md-12">
      <div
        class="alert alert-warning"
        role="alert"
        *ngIf="messageReturn.status"
      >
        {{ messageReturn.text }}
      </div>
    </div>
  </div>

  <div class="row mt-2 mb-3">
    <div class="col-md-2" *ngIf="chartA.options && chartB.options">
      <label class="form-label"
        >Tamanho: {{ formChart.controls['chart_height'].value }} px
      </label>
      <input
        type="range"
        class="range"
        #chartHeight
        min="300"
        max="1600"
        step="10"
        formControlName="chart_height"
        (input)="setHeight(chartHeight.value)"
      />
    </div>
  </div>
  <div class="row mt-2 mb-3">
    <div class="col-md-6" *ngIf="chartA.options">
      <app-e-charts
        [dataChart]="chartA"
        [height]="
          formChart.controls['chart_height'].value + chartLegendsTop - 5
        "
      ></app-e-charts>
    </div>
    <div class="col-md-6" *ngIf="chartB.options">
      <app-e-charts
        [dataChart]="chartB"
        [height]="
          formChart.controls['chart_height'].value + chartLegendsTop - 5
        "
      ></app-e-charts>
    </div>
  </div>
  <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [click]="goBack.bind(this)"
    ></app-button>
  </div>
</form>
