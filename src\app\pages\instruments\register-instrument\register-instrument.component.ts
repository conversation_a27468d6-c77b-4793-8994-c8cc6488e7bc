import { <PERSON>mpo<PERSON>, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChildren, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { Automated, OnLine, typeInstruments } from 'src/app/constants/instruments.constants';
import { MessageCadastro, MessageInputInvalid } from 'src/app/constants/message.constants';

import { ClientService } from 'src/app/services/api/client.service';
import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { StructuresService } from 'src/app/services/api/structure.service';

import { FormService } from 'src/app/services/form.service';
import { UserService } from 'src/app/services/user.service';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';
import { NgxSpinnerService } from 'ngx-spinner';

//Tour guiado
import { CustomTourService } from 'src/app/services/custom-tour.service';
import { TourService } from 'ngx-ui-tour-ng-bootstrap';
import { left } from '@popperjs/core';

@Component({
  selector: 'app-register-instrument',
  templateUrl: './register-instrument.component.html',
  styleUrls: ['./register-instrument.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RegisterInstrumentComponent implements OnInit {
  @ViewChildren('instrumentRef') instrumentRef: QueryList<ElementRef>;

  public formInstruments: FormGroup = new FormGroup({
    client: new FormControl([], [Validators.required]),
    client_unit: new FormControl([], [Validators.required]),
    structure: new FormControl([], [Validators.required]),
    multiple_installation_date: new FormControl(''),
    multiple_responsible_for_installation: new FormControl(''),
    multiple_model: new FormControl(''),
    multiple_automated: new FormControl(false),
    multiple_online: new FormControl(true),

    //Edicao instrumentos em massa e Cadastro via planilha
    file_type_instrument: new FormControl(''),
    file_upload_edit: new FormControl(null),
    file_upload_edit_size: new FormControl('', [Validators.max(10485760)]),
    file_name: new FormControl('')
  });

  public dropdownInstructions = false;

  public validateInstrument: boolean = false;
  public validateMeasure: boolean = false;

  public instrumentId: any = null;

  public instrumentsData: any = [];
  public instrumentsReference: any = [];
  public instrumentsRequest: any = [];
  public instrumentRequest: any = {};
  public measureRequest: any = {};

  public filter: any = { ClientId: '', ClientUnitId: '' };
  public edit: boolean = false;
  public view: boolean = false;

  public typeInstrumentsList: any = typeInstruments;

  public clientSettings = MultiSelectDefault.Single;
  public instrumentSettings = MultiSelectDefault.Single;
  public structureSettings = MultiSelectDefault.Single;
  public unitSettings = MultiSelectDefault.Single;

  public clients: any = [];
  public structures: any = [];
  public units: any = [];

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;
  public messagesInfo: any = null;

  public profile: any = null;
  public permissaoUsuario: any = null;

  public func = fn;

  public charCounts: { [key: string]: number } = {}; // Objeto para rastrear caracteres de vários campos

  //Preenche Multiplos
  public ctrlFillMultiples: boolean = false;
  public automated: any = Automated;
  public onLine: any = OnLine;

  public typeMeasure: any = '';
  public nameMeasure: any = '';

  public editingBatch: boolean = false;
  public formCrtl: boolean = false;
  public fileContentEdit: string = '';
  public fileNameEdit: string = '';
  public fileEdit: any = '';
  public fileValid: boolean = false;

  public insertSpreadsheet: boolean = false;

  public dynamicTourData: any = {};

  public coordinateStructure: any = null;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private clientService: ClientService,
    private clientUnitService: ClientUnitService,
    private customTourService: CustomTourService,
    private formService: FormService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private structuresService: StructuresService,
    private userService: UserService,
    private ngxSpinnerService: NgxSpinnerService,
    public tourService: TourService
  ) {}

  //Inicializa o componente, configurando variáveis, estados e carregando os dados necessários.
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.formCrtl = true;

    if (this.activatedRoute.snapshot.params.instrumentId) {
      this.edit = true;
      this.instrumentId = this.activatedRoute.snapshot.params.instrumentId;
      this.getInstrument(this.activatedRoute.snapshot.params.instrumentId);
      if (this.activatedRoute.snapshot.url && this.activatedRoute.snapshot.url[1] && this.activatedRoute.snapshot.url[1].path == 'view') {
        this.edit = false;
        this.view = true;
      }
    } else if (this.activatedRoute.snapshot.url[0].path == 'editingBatch') {
      this.formCrtl = false;
      this.editingBatch = true;
    } else if (this.activatedRoute.snapshot.url[0].path == 'insertBySpreadsheet') {
      this.formCrtl = false;
      this.insertSpreadsheet = true;
    }

    this.instrumentsReference = this.edit || this.view || this.editingBatch || this.insertSpreadsheet ? [] : [1];

    // Inicializa contadores com valores vazios
    for (const key of Object.keys(this.formInstruments.controls)) {
      const initialValue = this.formInstruments.get(key)?.value || '';
      this.charCounts[key] = initialValue?.length || 0;

      // Atualiza dinamicamente ao digitar
      this.formInstruments.get(key)?.valueChanges.subscribe((value) => {
        this.charCounts[key] = value?.length || 0;
      });
    }

    this.getClients();
    this.loadFilter(this.formInstruments, 'client', 'client_unit', 'structure', false);
  }

  // Obtém a lista de clientes disponíveis e configura as unidades e estruturas baseadas no cliente selecionado.
  getClients() {
    this.clients = [];
    this.units = [];
    this.structures = [];

    this.formInstruments.get('client').setValue('');
    this.formInstruments.get('client_unit').setValue('');
    this.formInstruments.get('structure').setValue('');

    this.coordinateStructure = '';

    this.clientService.getClientsList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;

      if (this.clients.length == 1) {
        this.formInstruments.get('client').setValue(this.clients);
        this.getUnits(this.clients[0]);
      }
    });
  }

  /**
   * Obtém as unidades disponíveis para o cliente selecionado e configura as estruturas baseadas na unidade selecionada.
   * @param {any} client - O cliente selecionado.
   * @param {string} action - A ação a ser executada, por padrão é 'select'.
   */
  getUnits(client, action: string = 'select') {
    this.units = [];
    this.structures = [];

    this.formInstruments.get('client_unit').setValue('');
    this.formInstruments.get('structure').setValue('');

    this.coordinateStructure = '';

    if (action === 'select') {
      this.clientUnitService.getClientUnitsId({ clientId: client.id, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.units = dados;

        if (this.units.length == 1) {
          this.formInstruments.get('client_unit').setValue(this.units);
          this.getStructures(this.units[0]);
        }
      });
    }
  }

  /**
   * Obtém as estruturas disponíveis para a unidade de cliente selecionada.
   * @param {any} clientUnit - A unidade de cliente selecionada.
   * @param {string} action - A ação a ser executada, por padrão é 'select'.
   */
  getStructures(clientUnit, action: string = 'select') {
    this.structures = [];
    this.formInstruments.get('structure').setValue('');
    this.coordinateStructure = null;

    if (action === 'select') {
      this.structuresService.getStructureList({ clientUnitId: clientUnit.id, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.structures = dados;

        if (this.structures.length == 1) {
          this.formInstruments.get('structure').setValue(this.structures);
          if (!this.edit && !this.view) {
            this.coordinateStructure = this.structures[0].coordinate_setting;
          }
        }

        if (this.structures.length > 0 && this.formInstruments.get('structure').value) {
          this.setDatum(this.formInstruments.get('structure').value[0]);
        }
      });
    }
  }

  setDatum(structure, action: string = 'select') {
    this.coordinateStructure = '';

    if (action === 'select') {
      const structureItem = this.structures.find((item) => item.id === structure.id);
      this.coordinateStructure = structureItem.coordinate_setting;
    }
  }

  //Registra um novo instrumento na base de dados com as informações fornecidas.
  registerInstruments() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.instrumentsServiceApi.postInstruments(this.instrumentsRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.InstrumentoCadastrado;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.message.class = 'alert-success';

          this.router.navigate(['/instruments']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        setTimeout(() => {
          this.messagesError = [];
        }, 4000);

        this.formCrtl = true;
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Obtém os dados de um instrumento específico pelo ID e preenche o formulário com essas informações.
   * @param {string} instrumentId - O ID do instrumento a ser buscado.
   */
  getInstrument(instrumentId: string = '') {
    this.ngxSpinnerService.show();

    this.instrumentsServiceApi.getInstrumentsById(instrumentId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.splitData(dados);
      this.ngxSpinnerService.hide();
    });
  }

  //Faz o upload de um arquivo para edição em massa dos instrumentos, obtendo os registros do arquivo.
  getInstrumentGroup() {
    this.ngxSpinnerService.show();

    if (this.fileValid) {
      let queryParams = {
        Type: this.formInstruments.controls['file_type_instrument'].value[0].id
      };
      const formData = new FormData();

      formData.append('file', this.formInstruments.controls['file_upload_edit'].value);

      let method = this.editingBatch ? 'postInstrumentsFileConvertToUpdate' : 'postInstrumentsFileConvertToAdd';

      this.instrumentsServiceApi[method](formData, queryParams).subscribe(
        (resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;
          this.formatInstrumentGroup(dados);
          this.ngxSpinnerService.hide();
        },
        (error) => {
          if (error.status !== 200) {
            this.messagesError = [];
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
            this.messagesError.push({
              code: '000',
              message: 'Remova o arquivo, faça as devidas correções e envie novamente.'
            });
            setTimeout(() => {
              this.messagesError = [];
            }, 4000);
          }
          this.ngxSpinnerService.hide();
        }
      );
    }
  }

  //Edita um instrumento existente na base de dados com as novas informações fornecidas.
  editInstrument() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.instrumentsServiceApi.putInstruments(this.instrumentsRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/instruments']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        setTimeout(() => {
          this.messagesError = [];
        }, 4000);

        this.formCrtl = true;
        this.ngxSpinnerService.hide();
      }
    );
  }

  //Adiciona um novo instrumento à lista de referências.
  addInstrument() {
    let max = 0;
    if (this.instrumentsReference.length > 0) {
      max = Math.max(...this.instrumentsReference);
    }
    this.instrumentsReference.push(max + 1);
  }

  /**
   * Remove um instrumento da lista de referências.
   * @param {any} $event - O evento que contém o índice do instrumento a ser removido.
   */
  removeInstrument($event) {
    this.instrumentsReference.splice($event.index, 1);
  }

  //Valida os dados do formulário de instrumentos e prepara a união dos dados para cadastro ou edição.
  validate() {
    this.messagesInfo = null;

    if (!this.formInstruments.valid) {
      this.formInstruments.markAllAsTouched();
    }

    this.getFormsInstruments();

    if (this.validateInstrument && this.validateMeasure && this.formInstruments.valid) {
      this.joinData();
    } else {
      this.messagesInfo = [{ message: MessageInputInvalid.RegisterInvalid }];
    }
  }

  //Obtém os formulários de instrumentos preenchidos e valida os dados.
  getFormsInstruments() {
    this.instrumentRef.toArray().forEach((instrumentItem) => {
      let formInstrument = instrumentItem['formInstrument'];

      if (instrumentItem['securityLevels']) {
        if (formInstrument.controls['type'].value != 6 && formInstrument.controls['type'].value != 7) {
          //Niveis de seguranca
          formInstrument.controls['sl_instrument_attention'].setValue(instrumentItem['securityLevels'].formSecurityLevels.controls['attention'].value);
          formInstrument.controls['sl_instrument_alert'].setValue(instrumentItem['securityLevels'].formSecurityLevels.controls['alert'].value);
          formInstrument.controls['sl_instrument_emergency'].setValue(instrumentItem['securityLevels'].formSecurityLevels.controls['emergency'].value);
          formInstrument.controls['sl_instrument_abrupt_variation_between_readings'].setValue(
            instrumentItem['securityLevels'].formSecurityLevels.controls['abrupt_variation_between_readings'].value
          );
          formInstrument.controls['sl_instrument_maximum_daily_rainfall'].setValue(
            instrumentItem['securityLevels'].formSecurityLevels.controls['maximum_daily_rainfall'].value
          );
          formInstrument.controls['sl_instrument_rain_intensity'].setValue(
            instrumentItem['securityLevels'].formSecurityLevels.controls['rain_intensity'].value
          );
          formInstrument.controls['sl_instrument_axis'].setValue(instrumentItem['securityLevels'].formSecurityLevels.controls['axis'].value);
        } else {
          for (let i = 1; i <= 6; i++) {
            //Niveis de seguranca
            formInstrument.controls['sl_instrument_id_' + i].setValue(instrumentItem['securityLevels'].formSecurityLevels.controls['id_' + i].value);
            formInstrument.controls['sl_instrument_attention_' + i].setValue(
              instrumentItem['securityLevels'].formSecurityLevels.controls['attention_' + i].value
            );
            formInstrument.controls['sl_instrument_alert_' + i].setValue(instrumentItem['securityLevels'].formSecurityLevels.controls['alert_' + i].value);
            formInstrument.controls['sl_instrument_emergency_' + i].setValue(
              instrumentItem['securityLevels'].formSecurityLevels.controls['emergency_' + i].value
            );
            formInstrument.controls['sl_instrument_abrupt_variation_between_readings_' + i].setValue(
              instrumentItem['securityLevels'].formSecurityLevels.controls['abrupt_variation_between_readings_' + i].value
            );
            formInstrument.controls['sl_instrument_maximum_daily_rainfall_' + i].setValue(
              instrumentItem['securityLevels'].formSecurityLevels.controls['maximum_daily_rainfall_' + i].value
            );
            formInstrument.controls['sl_instrument_rain_intensity_' + i].setValue(
              instrumentItem['securityLevels'].formSecurityLevels.controls['rain_intensity_' + i].value
            );
            formInstrument.controls['sl_instrument_axis_' + i].setValue(instrumentItem['securityLevels'].formSecurityLevels.controls['axis_' + i].value);
          }
        }
      }
      instrumentItem['formValid'] = !this.validateFormsInstruments(formInstrument);
      instrumentItem['getFormsMeasurements']();
      this.validateMeasure = instrumentItem['validateMeasure'];
    });
  }

  /**
   * Valida os dados de um formulário de instrumento específico.
   * @param {any} formInstrument - O formulário do instrumento a ser validado.
   * @returns {boolean} - Retorna true se o formulário for válido, caso contrário, false.
   */
  validateFormsInstruments(formInstrument) {
    this.validateInstrument = this.formService.validateForm(formInstrument);

    if (!this.validateInstrument) {
      formInstrument.markAllAsTouched();
    }
    return this.validateInstrument;
  }

  //Junta os dados de instrumentos e medições e prepara o envio para cadastro ou edição.
  joinData() {
    this.instrumentsRequest = [];
    this.instrumentRequest = {};
    this.measureRequest = {};

    this.instrumentRef.toArray().forEach((instrumentItem) => {
      let formInstrument = instrumentItem['formInstrument'];
      let totalMeasurements = instrumentItem['measureRef'].toArray().length;

      this.instrumentRequest = this.formatData('instrument', formInstrument);

      if (totalMeasurements > 0) {
        instrumentItem['measureRef'].toArray().forEach((measureItem) => {
          let formMeasure = measureItem['formMeasure'];

          this.measureRequest = this.formatData('measure', formMeasure, {}, formInstrument.controls['type'].value);
          this.instrumentRequest.security_levels = null;
          this.instrumentRequest.measurements.push(this.measureRequest);
        });
      }

      this.instrumentsRequest.push(this.instrumentRequest);
    });

    if ((!this.edit && !this.editingBatch && !this.insertSpreadsheet) || this.insertSpreadsheet) {
      this.registerInstruments();
    } else if (this.editingBatch || this.edit) {
      this.editInstrument();
    }
  }

  /**
   * Divide os dados recebidos e os preenche no formulário.
   * @param {any} $dados - Os dados a serem preenchidos no formulário.
   * @param {boolean} group - Indica se os dados estão sendo divididos para um grupo de instrumentos.
   */
  splitData($dados, group = false) {
    this.instrumentsData = [];
    this.instrumentsReference = [];

    $dados = $dados.length == undefined ? [$dados] : $dados;
    this.instrumentsData = $dados;

    this.instrumentsData.forEach((instrumentItem) => {
      this.addInstrument();
    });

    if (!group) {
      this.getUnits({ id: $dados[0].client.id, name: $dados[0].client.name });
      this.getStructures({ id: $dados[0].client_unit.id, name: $dados[0].client_unit.name });

      this.formInstruments.controls['client'].setValue([{ id: $dados[0].client.id, name: $dados[0].client.name }]);
      this.formInstruments.controls['client_unit'].setValue([{ id: $dados[0].client_unit.id, name: $dados[0].client_unit.name }]);
      this.formInstruments.controls['structure'].setValue([{ id: $dados[0].structure.id, name: $dados[0].structure.name }]);
      if (this.edit) {
        this.formInstruments.controls['client'].disable();
        this.formInstruments.controls['client_unit'].disable();
        this.formInstruments.controls['structure'].disable();
      } else if (this.view) {
        this.formInstruments.disable();
      }
    } else {
      this.formInstruments.controls['structure'].setValue([{ id: $dados[0].structure.id, name: $dados[0].structure.name }]);

      this.formInstruments.controls['client'].setErrors(null);
      this.formInstruments.controls['client'].clearValidators();
      this.formInstruments.controls['client_unit'].setErrors(null);
      this.formInstruments.controls['client_unit'].clearValidators();
      this.formInstruments.controls['structure'].setErrors(null);
      this.formInstruments.controls['structure'].clearValidators();
    }

    this.formCrtl = true;

    // Atualiza contadores de caracteres após carregar os dados
    for (const key of Object.keys(this.formInstruments.controls)) {
      const value = this.formInstruments.get(key)?.value || '';
      this.charCounts[key] = value?.length || 0;
    }
  }

  /**
   * Formata os dados do formulário para serem enviados na requisição de cadastro ou edição.
   * @param {string} option - A opção de dados a ser formatada, pode ser 'instrument' ou 'measure'.
   * @param {any} data - Os dados a serem formatados.
   * @param {any} [params={}] - Parâmetros adicionais para formatação.
   * @param {number|null} type - O tipo de instrumento ou medição.
   * @returns {any} - Retorna os dados formatados.
   */
  formatData(option: string = 'instrument', data, params = {}, type = null) {
    if (option === 'instrument') {
      let structureId =
        !this.editingBatch && !this.insertSpreadsheet ? this.formInstruments.controls['structure'].value[0].id : data.controls['structure'].value.id;

      let instrument = {
        type: data.controls['type'].value,
        structure: {
          id: structureId
        },
        identifier: data.controls['identifier'].value,
        alternative_name: data.controls['alternative_name'].value,
        coordinate_setting: {
          datum: data.controls['datum'].value,
          coordinate_format: parseInt(data.controls['coordinate_format'].value),
          coordinate_systems: {
            utm: {
              zone_number: data.controls['zone_number'].value,
              zone_letter: data.controls['zone_letter'].value,
              northing: data.controls['northing'].value,
              easting: data.controls['easting'].value
            },
            decimal_geodetic: {
              latitude: data.controls['latitude'].value,
              longitude: data.controls['longitude'].value
            }
          }
        },
        top_quota: fn.isEmpty(data.controls['top_quota'].value) ? null : data.controls['top_quota'].value,
        base_quota: fn.isEmpty(data.controls['base_quota'].value) ? null : data.controls['base_quota'].value,
        depth: fn.isEmpty(data.controls['depth'].value) ? null : data.controls['depth'].value,
        azimuth: fn.isEmpty(data.controls['azimuth'].value) ? null : data.controls['azimuth'].value,
        upper_limit: fn.isEmpty(data.controls['upper_limit'].value) ? null : data.controls['upper_limit'].value,
        lower_limit: fn.isEmpty(data.controls['lower_limit'].value) ? null : data.controls['lower_limit'].value,
        measurement_frequency: data.controls['measurement_frequency'].value,
        automated: data.controls['automated'].value,
        online: data.controls['online'].value,
        geophone_type: fn.isEmpty(data.controls['geophone_type'].value) ? null : data.controls['geophone_type'].value,
        dry_type: fn.isEmpty(data.controls['dry_type'].value) ? null : data.controls['dry_type'].value,
        elevation: fn.isEmpty(data.controls['elevation'].value) ? null : data.controls['elevation'].value,
        responsible_for_installation: data.controls['responsible_for_installation'].value,
        installation_date: data.controls['installation_date'].value != '' ? moment(data.controls['installation_date'].value).format('YYYY-MM-DD') : '',
        model: data.controls['model'].value,
        linimetric_ruler_position: fn.isEmpty(data.controls['linimetric_ruler_position'].value) ? null : data.controls['linimetric_ruler_position'].value,
        security_levels: null,
        measurements: []
      };

      if (![1, 2, 3].includes(data.controls['type'].value)) {
        instrument['dry_type'] = null;
      }

      if (data.controls['type'].value != 6 && data.controls['type'].value != 7) {
        instrument['security_levels'] = [
          {
            attention: fn.isEmpty(data.controls['sl_instrument_attention'].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_attention'].value,
            alert: fn.isEmpty(data.controls['sl_instrument_alert'].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_alert'].value,
            emergency: fn.isEmpty(data.controls['sl_instrument_emergency'].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_emergency'].value,
            abrupt_variation_between_readings: fn.isEmpty(data.controls['sl_instrument_abrupt_variation_between_readings'].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_abrupt_variation_between_readings'].value,
            maximum_daily_rainfall: fn.isEmpty(data.controls['sl_instrument_maximum_daily_rainfall'].value)
              ? data.controls['type'].value != 12 || data.controls['type'].value != 13
                ? null
                : null
              : data.controls['sl_instrument_maximum_daily_rainfall'].value,
            rain_intensity: fn.isEmpty(data.controls['sl_instrument_rain_intensity'].value)
              ? data.controls['type'].value != 13
                ? null
                : null
              : data.controls['sl_instrument_rain_intensity'].value,
            axis: fn.isEmpty(data.controls['sl_instrument_axis'].value)
              ? data.controls['type'].value == 6 || data.controls['type'].value != 7
                ? null
                : null
              : data.controls['sl_instrument_axis'].value
          }
        ];
      } else {
        instrument['security_levels'] = [];
        for (let i = 1; i <= 6; i++) {
          instrument['security_levels'].push({
            id: fn.isEmpty(data.controls['sl_instrument_id_' + i].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_id_' + i].value,
            attention: fn.isEmpty(data.controls['sl_instrument_attention_' + i].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_attention_' + i].value,
            alert: fn.isEmpty(data.controls['sl_instrument_alert_' + i].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_alert_' + i].value,
            emergency: fn.isEmpty(data.controls['sl_instrument_emergency_' + i].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_emergency_' + i].value,
            abrupt_variation_between_readings: fn.isEmpty(data.controls['sl_instrument_abrupt_variation_between_readings_' + i].value)
              ? data.controls['type'].value == 12 || data.controls['type'].value == 13
                ? null
                : null
              : data.controls['sl_instrument_abrupt_variation_between_readings_' + i].value,
            maximum_daily_rainfall: fn.isEmpty(data.controls['sl_instrument_maximum_daily_rainfall_' + i].value)
              ? data.controls['type'].value != 12 || data.controls['type'].value != 13
                ? null
                : null
              : data.controls['sl_instrument_maximum_daily_rainfall_' + i].value,
            rain_intensity: fn.isEmpty(data.controls['sl_instrument_rain_intensity_' + i].value)
              ? data.controls['type'].value != 13
                ? null
                : null
              : data.controls['sl_instrument_rain_intensity_' + i].value,
            axis: fn.isEmpty(data.controls['sl_instrument_axis_' + i].value)
              ? data.controls['type'].value == 6 || data.controls['type'].value != 7
                ? null
                : null
              : data.controls['sl_instrument_axis_' + i].value
          });
        }
      }

      if (this.edit || this.editingBatch || this.insertSpreadsheet) {
        instrument['id'] = data.controls['id'].value;
      }
      return instrument;
    } else if (option === 'measure') {
      let measure = {
        identifier: data.controls['identifier'].value,
        alternative_name: data.controls['alternative_name'].value,
        quota: fn.isEmpty(data.controls['quota'].value) ? null : data.controls['quota'].value,
        lithotype: fn.isEmpty(data.controls['lithotype'].value) ? null : data.controls['lithotype'].value,
        limit: fn.isEmpty(data.controls['limit'].value) ? null : data.controls['limit'].value,
        delta_ref: fn.isEmpty(data.controls['delta_ref'].value) ? null : data.controls['delta_ref'].value,
        is_referencial: data.controls['is_referencial'].value,
        elevation: fn.isEmpty(data.controls['elevation'].value) ? null : data.controls['elevation'].value,
        length: fn.isEmpty(data.controls['length'].value) ? null : data.controls['length'].value,
        active: data.controls['active'].value,
        security_levels: {
          attention: fn.isEmpty(data.controls['sl_measure_attention'].value) ? null : data.controls['sl_measure_attention'].value,
          alert: fn.isEmpty(data.controls['sl_measure_alert'].value) ? null : data.controls['sl_measure_alert'].value,
          emergency: fn.isEmpty(data.controls['sl_measure_emergency'].value) ? null : data.controls['sl_measure_emergency'].value,
          abrupt_variation_between_readings: fn.isEmpty(data.controls['sl_measure_abrupt_variation_between_readings'].value)
            ? null
            : data.controls['sl_measure_abrupt_variation_between_readings'].value
        }
      };
      if (this.edit || this.editingBatch || this.insertSpreadsheet) {
        measure['id'] = data.controls['id'] ? data.controls['id'].value : null;
      }

      //Modificação: elevation => quota e quota => depth
      if ([4, 5].includes(type)) {
        measure['depth'] = measure['quota'];
        measure['quota'] = measure['elevation'];
        delete measure['elevation'];
      }
      return measure;
    }
  }

  //Preenche os campos de múltiplos instrumentos com valores comuns.
  fillMutipleUpdate() {
    this.instrumentRef.toArray().forEach((instrumentItem) => {
      let formInstrument = instrumentItem['formInstrument'];
      if (this.ctrlFillMultiples) {
        formInstrument.controls['installation_date'].setValue(this.formInstruments.controls['multiple_installation_date'].value);
        formInstrument.controls['responsible_for_installation'].setValue(this.formInstruments.controls['multiple_responsible_for_installation'].value);
        formInstrument.controls['model'].setValue(this.formInstruments.controls['multiple_model'].value);
        formInstrument.controls['automated'].setValue(this.formInstruments.controls['multiple_automated'].value);
        formInstrument.controls['online'].setValue(this.formInstruments.controls['multiple_online'].value);
      } else {
        formInstrument.controls['installation_date'].setValue('');
        formInstrument.controls['responsible_for_installation'].setValue('');
        formInstrument.controls['model'].setValue('');
        formInstrument.controls['automated'].setValue('');
        formInstrument.controls['online'].setValue('');
      }
    });
  }

  /**
   * Faz o upload de um arquivo de planilha e valida a extensão do arquivo.
   * @param {any} $event - O evento de upload do arquivo.
   */
  uploadFile($event: any) {
    let file = $event.dataTransfer ? $event.dataTransfer.files[0] : $event.target.files[0];

    if (file && file.name) {
      this.formInstruments.controls['file_name'].setValue(file.name);
      let extension = file.name.split('.');
      this.fileValid = ['xlsx', 'csv'].includes(extension[extension.length - 1]);

      if (this.fileValid) {
        this.formInstruments.controls['file_upload_edit'].setValue(file);
      } else {
        this.messagesError = [];
        this.messagesError.push({
          code: '000',
          message: MessageInputInvalid.NoFile + ' Remova o arquivo, faça as devidas correções e envie novamente.'
        });
      }
    } else {
      this.fileValid = false;
    }
  }

  //Remove o arquivo carregado anteriormente e limpa as mensagens de erro.
  removeFile() {
    this.messagesError = [];
    this.formInstruments.controls['file_name'].setValue('');
  }

  /**
   * Formata os dados do grupo de instrumentos para edição em massa.
   * @param {any} dados - Os dados dos instrumentos a serem formatados.
   */
  formatInstrumentGroup(dados) {
    if (this.editingBatch) {
      dados = dados.map((dado) => {
        let item = dado.updated;
        item['_current'] = dado.current;
        return item;
      });
    }
    this.splitData(dados, true);
  }

  /**
   * Carrega os filtros salvos no `localStorage` e preenche os campos do formulário com base nos dados disponíveis.
   * Verifica se há valores salvos para ClientId, ClientUnitId, e StructureId, e preenche os campos correspondentes no formulário.
   * Se os valores existirem, também busca as unidades e estruturas associadas.
   *
   * @param {FormGroup} $form - O formulário que será preenchido com os filtros.
   * @param {string} client - O nome do campo de cliente no formulário.
   * @param {string} unit - O nome do campo de unidade no formulário.
   * @param {string} structure - O nome do campo de estrutura no formulário.
   */
  loadFilter($form, client, unit, structure, onlyId = false) {
    const savedFilters = localStorage.getItem('filterHierarchy');
    if (savedFilters && !this.edit && !this.view) {
      const filterHierarchy = JSON.parse(savedFilters);
      const configClient = {
        value: onlyId ? filterHierarchy.ClientId.id : [{ id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }],
        param: onlyId ? filterHierarchy.ClientId.id : { id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }
      };

      const configClientUnit = {
        value: onlyId ? filterHierarchy.ClientUnitId.id : [{ id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }],
        param: onlyId ? filterHierarchy.ClientUnitId.id : { id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }
      };

      const configStructure = {
        value: onlyId ? filterHierarchy.StructureId.id : [{ id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }],
        param: onlyId ? filterHierarchy.StructureId.id : { id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }
      };

      // Verificar se existe ClientId, ClientUnitId, e StructureId e preenchê-los
      if (filterHierarchy.ClientId) {
        $form.get(client)?.setValue(configClient.value);
        this.getUnits(configClient.param);
      }

      if (filterHierarchy.ClientUnitId) {
        $form.get(unit)?.setValue(configClientUnit.value);
        this.getStructures(configClientUnit.param);
      }

      if (filterHierarchy.StructureId) {
        $form.get(structure)?.setValue(configStructure.value);
      }
    }
  }

  loadTourGuide() {
    // this.customTourService.startTour(
    //   this.tourService,
    //   'assets/tour-guide/tour-guide-instruments/register-instruments.tourguide.json',
    //   null,
    //   this.getCustomSteps()
    // );
  }

  getCustomSteps() {
    let customSteps = [];
    const typeMeasure = this.instrumentRef.toArray()[0]['typeMeasure'];
    switch (typeMeasure) {
      case 'pressure_cell':
        customSteps.push({
          anchorId: 'measure_quota',
          title: 'Cota da célula de pressão (m)',
          content:
            'Informe um valor, em metros. O valor da cota das células de pressão devem ser sempre menores que o valor da cota de topo. Preenchimento obrigatório.'
        });
        break;
      case 'measure_point':
        customSteps.push({
          anchorId: 'measure_quota',
          title: 'Profundidade (m)',
          content: 'Informe a profundidade do instrumento, em metros. Preenchimento obrigatório.',
          placement: left
        });
        break;
      default:
        customSteps.push({
          anchorId: 'measure_quota',
          title: 'Cota (m)',
          content: 'Informe um valor, em metros. Preenchimento obrigatório.'
        });
        break;
    }
    return customSteps;
  }

  // Atualiza o contador do campo específico
  onValueChange(event: any, field: string): void {
    this.charCounts[field] = event.target.value.length;
  }
}
