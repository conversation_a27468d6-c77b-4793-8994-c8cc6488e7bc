import { Injectable } from '@angular/core';

import { typeInstruments } from '../constants/instruments.constants';

import fn from '../utils/function.utils';

@Injectable({
  providedIn: 'root'
})
export class InstrumentsService {
  constructor() {}

  getDataInstruments(id, option) {
    let idx = fn.findIndexInArrayofObject(typeInstruments, 'id', parseInt(id));

    switch (option) {
      case 'fields':
        return idx !== -1 ? typeInstruments[idx].fields : [];
        break;
      case 'name':
        return idx !== -1 ? typeInstruments[idx].name : '';
        break;
      case 'typeMeasure':
        return idx !== -1 ? typeInstruments[idx].typeMeasure : '';
        break;
      case 'nameMeasure':
        return idx !== -1 ? typeInstruments[idx].nameMeasure : '';
        break;
    }
  }
}
