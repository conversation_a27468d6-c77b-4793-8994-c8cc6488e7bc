import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { SharedModule } from '@components/shared.module';

import { LogisoilDirectivesModule } from 'src/app/shared/logisoil-directives.module';
import { ReadingsRoutingModule } from './readings-routing.module';

import { ListReadingsComponent } from './list-readings/list-readings.component';
import { RegisterReadingsComponent } from './register-readings/register-readings.component';
import { FormReadingsComponent } from './form-readings/form-readings.component';

//Formulários
import { InaPzComponent } from './form-readings/ina-pz/ina-pz.component';
import { PzeComponent } from './form-readings/pze/pze.component';
import { IncConvComponent } from './form-readings/inc-conv/inc-conv.component';
import { IncIpiComponent } from './form-readings/inc-ipi/inc-ipi.component';
import { MsPrComponent } from './form-readings/ms-pr/ms-pr.component';
import { MrComponent } from './form-readings/mr/mr.component';
import { GeoComponent } from './form-readings/geo/geo.component';
import { RlComponent } from './form-readings/rl/rl.component';
import { PluviometroComponent } from './form-readings/pluviometro/pluviometro.component';
import { PraiaComponent } from './form-readings/praia/praia.component';

//Modal
import { ModalHistoryomponent } from './modal-history/modal-history.component';
import { ModalInstructionsSpreadsheetComponent } from './modal-instructions-spreadsheet/modal-instructions-spreadsheet.component';

//Instruções preenchimento planilha
import { InaPzInstructionsComponent } from './modal-instructions-spreadsheet/instructions.component';
import { IncConvInstructionsComponent } from './modal-instructions-spreadsheet/instructions.component';
import { IpiInstructionsComponent } from './modal-instructions-spreadsheet/instructions.component';
import { MsPrInstructionsComponent } from './modal-instructions-spreadsheet/instructions.component';
import { MrInstructionsComponent } from './modal-instructions-spreadsheet/instructions.component';
import { RlInstructionsComponent } from './modal-instructions-spreadsheet/instructions.component';
import { GeoInstructionsComponent } from './modal-instructions-spreadsheet/instructions.component';
import { PluviografoComponent } from './form-readings/pluviografo/pluviografo.component';
import { HistoryReadingComponent } from './history-reading/history-reading.component';
import { HistoryTabsReadingComponent } from './history-tabs-reading/history-tabs-reading.component';

@NgModule({
  declarations: [
    ListReadingsComponent,
    RegisterReadingsComponent,
    FormReadingsComponent,
    //Formulários
    InaPzComponent,
    PzeComponent,
    IncConvComponent,
    IncIpiComponent,
    MsPrComponent,
    MrComponent,
    GeoComponent,
    RlComponent,
    PluviometroComponent,
    PraiaComponent,
    //Modal
    ModalHistoryomponent,
    ModalInstructionsSpreadsheetComponent,
    //Instruções preenchimento planilha
    InaPzInstructionsComponent,
    IncConvInstructionsComponent,
    IpiInstructionsComponent,
    MsPrInstructionsComponent,
    MrInstructionsComponent,
    RlInstructionsComponent,
    GeoInstructionsComponent,
    PluviografoComponent,
    HistoryReadingComponent,
    HistoryTabsReadingComponent
  ],
  imports: [
    CommonModule,
    NgbModule,
    NgMultiSelectDropDownModule.forRoot(),
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    ReadingsRoutingModule,
    LogisoilDirectivesModule
  ]
})
export class ReadingsModule {}
