<ng-template #modalCalendarActivities let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title" id="modal-new-activity">
      <i class="fa fa-calendar-plus-o calendar"></i>
      Nova atividade
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <form [formGroup]="formCalendarActivity">
    <div class="modal-body">
      <div class="row">
        <div class="col-md-12">
          <label class="form-label">Título:</label>
          <input
            type="text"
            formControlName="title"
            class="form-control"
            maxlength="80"
          />
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formCalendarActivity.get('title').valid &&
            formCalendarActivity.get('title').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <div class="row mt-2">
        <div class="col-md-12">
          <label class="form-label">Descrição:</label>
          <textarea
            formControlName="description"
            rows="6"
            class="form-control"
            maxlength="1000"
            data-ls-module="charCounter"
            (input)="onValueChange($event)"
          ></textarea>
          <small
            class="form-text text-danger"
            *ngIf="
              !formCalendarActivity.get('description').valid &&
              formCalendarActivity.get('description').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <small class="form-text text-secondary"
          >Caracteres {{ counter }} (Máximo: 1000)
        </small>
      </div>
      <div class="row mt-2">
        <div class="col-md-6">
          <label class="form-label">Início:</label>
          <input
            formControlName="initial_date"
            class="form-control"
            type="datetime-local"
          />
          <small
            class="form-text text-danger"
            *ngIf="
              !formCalendarActivity.get('initial_date').valid &&
              formCalendarActivity.get('initial_date').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <div class="col-md-6">
          <label class="form-label">Fim:</label>
          <input
            formControlName="final_date"
            class="form-control"
            type="datetime-local"
          />
          <small
            class="form-text text-danger"
            *ngIf="
              !formCalendarActivity.get('final_date').valid &&
              formCalendarActivity.get('final_date').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12">
          <div class="form-group form-check">
            <input
              formControlName="repeat"
              type="checkbox"
              class="form-check-input"
            />
            <label class="form-check-label" class="form-label">Repete</label>
          </div>
        </div>
      </div>
      <div
        class="row mt-2"
        *ngIf="formCalendarActivity.controls['repeat'].value"
      >
        <div class="col-md-6">
          <label class="form-label">Frequência:</label>
          <select class="form-control">
            <option>Diariamente</option>
            <option>Semanalmente</option>
            <option>Quinzenalmente</option>
            <option>Mensalmente</option>
            <option>Semestralmente</option>
            <option>Anualmente</option>
          </select>
        </div>
        <div class="col-md-6">
          <label class="form-label">Repete até:</label>
          <input class="form-control" type="datetime-local" />
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12">
          <label for="">Atribuir a:</label>
          <select multiple class="form-control">
            <option>bruna.resende</option>
            <option>igor.oliveira</option>
            <option>jessica.camilo</option>
            <option>luis.almeida</option>
            <option>michelle.scognamillo</option>
          </select>
          <small class="form-text text-muted"
            >Pressione "ctrl" para selecionar mais de um.</small
          >
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-6">
          <label for="">Me lembre:</label>
          <select class="form-control">
            <option selected>15 min antes</option>
            <option>30 min antes</option>
            <option>1 hora antes</option>
            <option>1 dia antes</option>
            <option>1 semana antes</option>
            <option>Não me lembre</option>
          </select>
        </div>
        <div class="col-md-6 mt-md-4">
          <div class="form-group form-check">
            <input type="checkbox" class="form-check-input" checked />
            <label class="form-check-label">E-mails de lembrete</label>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <app-button
        [class]="'btn-logisoil-red'"
        [label]="'Cancelar'"
        (click)="c('Close click')"
      >
      </app-button>
      <app-button
        [class]="'btn-logisoil-green'"
        [label]="'Salvar'"
        [icon]="'fa fa-floppy-o'"
        [type]="false"
        (click)="getForm(); c('Close click')"
      >
      </app-button>
    </div>
  </form>
</ng-template>
