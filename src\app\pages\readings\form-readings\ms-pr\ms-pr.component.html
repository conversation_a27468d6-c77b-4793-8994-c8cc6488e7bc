<form [formGroup]="formReading" class="mb-3">
  <div class="row">
    <div class="col-md-3">
      <label class="form-label">Instrumento</label>
      <select
        class="form-select"
        formControlName="instrument"
        (change)="changeInstrument(formReading.controls['instrument'].value)"
      >
        <option value="" *ngIf="formReading.controls['instrument'].value == ''">
          Selecione...
        </option>
        <option
          *ngFor="let instrumentItem of instrumentsList"
          [ngValue]="instrumentItem.id"
        >
          {{ instrumentItem.identifier }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('instrument').valid &&
          formReading.get('instrument').touched &&
          !formReading.get('instrument').disabled
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Data e hora -->
    <div class="col-md-2">
      <label class="form-label">Data e hora</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('date').valid && formReading.get('date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Datum -->
    <div class="col-md-2">
      <label class="form-label">Datum</label>
      <select class="form-select" formControlName="datum">
        <option value="">Selecione...</option>
        <option *ngFor="let item of datum" [ngValue]="item.id">
          {{ item.value }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('datum').valid &&
          formReading.get('datum').touched &&
          formReading.get('datum').errors != null
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Coordenadas -->
    <div class="col-md-5">
      <label class="form-label">Coordenadas</label>
      <div class="input-group">
        <span class="input-group-text">E (m)</span>
        <input
          type="text"
          class="form-control"
          formControlName="east_coordinate"
          (blur)="func.formatType($event); calcDisplacement('east')"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <span class="input-group-text">N (m)</span>
        <input
          type="text"
          class="form-control"
          formControlName="north_coordinate"
          (blur)="func.formatType($event); calcDisplacement('north')"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          (!formReading.get('east_coordinate').valid &&
            formReading.get('east_coordinate').touched) ||
          (!formReading.get('north_coordinate').valid &&
            formReading.get('north_coordinate').touched)
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <div class="row mt-2">
    <!-- Cota -->
    <div class="col-md-3">
      <label class="form-label">Cota</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="quota"
          (blur)="func.formatType($event); calcDisplacement('z')"
          (focus)="func.formatType($event)"
          appDisableScroll
        /><span class="input-group-text">{{ units[0] }}</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('quota').valid && formReading.get('quota').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- east_displacement -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento E</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="east_displacement"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <span class="input-group-text">mm</span>
      </div>
    </div>

    <!-- north_displacement -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento N</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="north_displacement"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <span class="input-group-text">mm</span>
      </div>
    </div>

    <!-- z_displacement -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento Z</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="z_displacement"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <span class="input-group-text">mm</span>
      </div>
    </div>
  </div>

  <div class="row mt-2">
    <!-- total_planimetric_displacement -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento planimétrico total</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="total_planimetric_displacement"
        />
        <span class="input-group-text">mm</span>
      </div>
    </div>

    <!-- a_displacement -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento A</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="a_displacement"
        />
        <span class="input-group-text">mm</span>
      </div>
    </div>

    <!-- b_displacement -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento B</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="b_displacement"
        />
        <span class="input-group-text">mm</span>
      </div>
    </div>
  </div>
</form>
