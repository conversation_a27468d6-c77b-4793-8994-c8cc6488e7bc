import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalOccurrenceDetailsComponent } from './modal-occurrence-details.component';

describe('ModalOccurrenceDetailsComponent', () => {
  let component: ModalOccurrenceDetailsComponent;
  let fixture: ComponentFixture<ModalOccurrenceDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalOccurrenceDetailsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalOccurrenceDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
