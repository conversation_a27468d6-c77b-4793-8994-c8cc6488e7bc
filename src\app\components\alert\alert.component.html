<div
  class="alert"
  [ngClass]="class"
  role="alert"
  *ngIf="isVisible && sanitizedMessages && sanitizedMessages.length > 0"
>
  <label *ngIf="label != ''">{{ label }}</label>
  <ul>
    <li
      *ngFor="let message of sanitizedMessages; let i = index"
      [innerHTML]="message"
    ></li>
  </ul>
  <button
    type="button"
    class="close-button"
    (click)="closeAlert()"
    [hidden]="!showCloseButton"
  >
    &times;
  </button>
</div>
