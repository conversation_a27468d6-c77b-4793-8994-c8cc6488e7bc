.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.form-control {
  border-color: #d4d2d2;
  font-size: 0.875em;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-select {
  font-size: 0.875em !important;
  line-height: 1.52857143 !important;
}

.table > thead > tr > th {
  background: rgba(3, 37, 97, 0.1);
  font-size: 0.875em !important;
  text-align: center;
  color: #032561;
  border: 1px solid #ffffff;
}

.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #ffffff;
}

tbody {
  font-size: 0.875em !important;
  text-align: center;
  border: 1px solid #d4d2d2;
  color: #032561;
  background-color: rgba(212, 210, 210, 0.1);
}

.th-component {
  width: 200px;
  position: absolute;
  top: 28px;
  left: 0;
  z-index: 9999;
}

.pointer {
  cursor: pointer;
}

.nav-link :hover {
  text-decoration: none;
  background-color: #34b575;
}

.nav,
.nav-tabs,
.nav-link active {
  font-size: 0.875em;
}

.form-title {
  color: #032561;
  font-family: averta-bold;
  font-size: 0.875em;
}