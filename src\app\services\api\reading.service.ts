import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class ReadingService {
  constructor(private api: ApiService) {}

  // Recupera os pontos de referência para aquele instrumento
  getReferencePoints(instrumentId: string) {
    const url = `/readings/values/instruments/${instrumentId}/reference`;
    return this.api.get<any[]>(url, null, false, 'client');
  }

  // Cadastro de Leituras
  postReadings(readings) {
    const url = `/readings`;
    return this.api.post<any>(url, readings, {}, 'user');
  }

  putReadings(params: any) {
    const url = `/readings`;
    return this.api.put<any>(url, params, 'client');
  }

  // Retorna as leituras para uso em filtro
  postReadingsSearch(params: any = {}, qparams: any = {}) {
    const url = '/readings/values/search';
    return this.api.post<any>(url, params, qparams, 'client');
  }

  // Busca a Leitura por ID
  getReadingById(id: string) {
    const url = `/readings/${id}`;
    return this.api.get<any>(url, {}, false, 'client');
  }

  patchReadings(params: any) {
    const url = `/readings/values`;
    return this.api.patch<any>(url, params, 'client');
  }

  getReadingReference(id: string) {
    const url = `/readings/values/instruments/${id}/reference`;
    return this.api.get<any>(url, {}, false, 'client');
  }

  // Exclui o registro da leitura no banco de dados
  deleteReadings(params: any) {
    const url = `/readings`;
    return this.api.delete<any>(url, 'client', params);
  }

  // Histórico
  getReadingHistory(id: string, params: any = {}) {
    const url = `/readings/${id}/history`;
    return this.api.get<any[]>(url, params, false, 'client');
  }

  //Cadastro de comprimento de praia
  postBeachLength(readings) {
    const url = `/readings/beach-lengths`;
    return this.api.post<any>(url, readings, {}, 'user');
  }

  postBeachLengthsSearch(params: any = {}, qparams: any = {}) {
    const url = '/readings/beach-lengths/search';
    return this.api.post<any>(url, params, qparams, 'client');
  }

  // Busca comprimento de praia por ID
  getBeachLengthById(id: string) {
    const url = `/readings/beach-lengths/${id}`;
    return this.api.get<any>(url, {}, false, 'client');
  }

  //Edita comprimento de praia por ID
  putBeachLength(id: string, params: any) {
    const url = `/readings/beach-lengths/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  // Histórico comprimento de praia por ID
  getBeachLengthHistory(id: string, params: any = {}) {
    const url = `/readings/beach-lengths/${id}/history`;
    return this.api.get<any[]>(url, params, false, 'client');
  }

  //Download planilha de acordo com o tipo de instrumento
  getReadingsFileDownloadTemplate(params: any) {
    const url = `/readings/file/download/template`;
    return this.api.get<any>(url, params, true, 'client');
  }

  //Cadastro de leituras via planilha
  postReadingsFileConvertToAdd(params: any, queryParams: any = {}) {
    const url = `/readings/file/convert/to/add`;
    return this.api.post<any>(url, params, queryParams, 'client', true, true);
  }
}
