<ng-template #modalInsertInstrumentBySpreadsheet let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">Cadastro de Instrumento via Planilha</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <form
    [formGroup]="formInsertInstrumentBySpreadsheet"
    (ngSubmit)="clickRowEvent('sendDownloadSpreadsheet')"
  >
    <div class="modal-body">
      <p>
        <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
        Preencha a planilha de acordo com o cabeçalho criado. Lembre-se de
        manter o formato do arquivo!
      </p>
      <p>
        <i class="fa fa-exclamation-circle me-2" aria-hidden="true"></i
        ><strong>Importante: nunca altere a ordem das colunas!</strong>
      </p>

      <p>
        <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>Preencha apenas
        um dos formatos de coordenadas para todos os instrumentos
        <strong class="text-decoration-underline">(UTM ou geográficas)</strong>.
      </p>

      <p>
        <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>Para
        instrumentos com múltiplos pontos de medição, múltiplas células de
        pressão e múltiplos anéis magnéticos, manter o
        <strong>
          mesmo identificador e dados de cadastro do instrumento
          principal</strong
        >, alterar apenas a identificação e dados referentes aos próprios pontos
        de medição, células de pressão ou anéis magnéticos. Nestes casos, a
        linha referente ao instrumento deve se repetir tantas vezes quantos
        pontos de medição, células de pressão ou anéis magnéticos existirem para
        ele.
      </p>

      <p>
        <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
        Tamanho máximo permitido: 10MB.
      </p>

      <div class="row">
        <!-- Selects Cliente, Unidade e Estrutura -->
        <!-- <app-hierarchy
          #hierarchy
          [elements]="elements"
          class="col-md-12"
        ></app-hierarchy> -->
        <div class="col-md-4">
          <label class="form-label">Cliente</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="clientSettings"
            [data]="clients"
            (onSelect)="getUnits($event, 'select')"
            (onDeSelect)="getUnits($event, 'deselect')"
            formControlName="client"
            [disabled]="
              formInsertInstrumentBySpreadsheet.controls['client'].disabled
            "
          >
          </ng-multiselect-dropdown>
          <small
            class="form-text text-danger"
            *ngIf="
              !formInsertInstrumentBySpreadsheet.get('client').valid &&
              formInsertInstrumentBySpreadsheet.get('client').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <div class="col-md-4">
          <label class="form-label">Unidade</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="unitSettings"
            [data]="units"
            (onSelect)="getStructures($event, 'select')"
            (onDeSelect)="getStructures($event, 'deselect')"
            formControlName="client_unit"
            [disabled]="
              formInsertInstrumentBySpreadsheet.controls['client_unit'].disabled
            "
          >
          </ng-multiselect-dropdown>
          <small
            class="form-text text-danger"
            *ngIf="
              !formInsertInstrumentBySpreadsheet.get('client_unit').valid &&
              formInsertInstrumentBySpreadsheet.get('client_unit').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <div class="col-md-4">
          <label class="form-label">Estrutura</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="structureSettings"
            [data]="structures"
            formControlName="structure"
            [disabled]="
              formInsertInstrumentBySpreadsheet.controls['structure'].disabled
            "
          >
          </ng-multiselect-dropdown>
          <small
            class="form-text text-danger"
            *ngIf="
              !formInsertInstrumentBySpreadsheet.get('structure').valid &&
              formInsertInstrumentBySpreadsheet.get('structure').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
      </div>
      <div class="row mt-1">
        <div class="col-md-4">
          <label class="form-label">Tipo de instrumento:</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="typeInstrumentSettingsModal"
            [data]="typeInstruments"
            formControlName="type_instrument"
          >
          </ng-multiselect-dropdown>
          <small
            class="form-text text-danger"
            *ngIf="
              !formInsertInstrumentBySpreadsheet.get('type_instrument').valid &&
              formInsertInstrumentBySpreadsheet.get('type_instrument').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <div class="col-md-3">
          <label class="form-label">DATUM</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="datumSettingsModal"
            [data]="datum"
            formControlName="datum"
          >
          </ng-multiselect-dropdown>
          <small
            class="form-text text-danger"
            *ngIf="
              !formInsertInstrumentBySpreadsheet.get('datum').valid &&
              formInsertInstrumentBySpreadsheet.get('datum').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <div class="col-md-3">
          <label class="form-label">Formato do arquivo:</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="singleSettingsModal"
            [data]="fileFormats"
            formControlName="file_format"
          >
          </ng-multiselect-dropdown>
          <small
            class="form-text text-danger"
            *ngIf="
              !formInsertInstrumentBySpreadsheet.get('file_format').valid &&
              formInsertInstrumentBySpreadsheet.get('file_format').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <div class="col-md-2">
          <label class="form-label">Número de linhas:</label>
          <input
            type="text"
            formControlName="rows"
            class="form-control"
            min="0"
            step="1"
            [placeholder]="'Campo Opcional'"
            (keypress)="func.controlNumber($event, null, 'positive')"
            (keyup)="
              func.controlNumber(
                $event,
                formInsertInstrumentBySpreadsheet.get('rows')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
          />
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-6">
          <app-button
            [class]="'btn-logisoil-blue'"
            [label]="'Gerar planilha modelo'"
            [icon]="'fa fa-download'"
            [type]="true"
            (click)="clickRowEvent('downloadTemplate')"
            [disabled]="!formInsertInstrumentBySpreadsheet.valid"
          >
          </app-button>
        </div>
      </div>
      <app-alert
        [class]="'alert-warning mt-2'"
        [messages]="messagesError"
        [label]="labelAlert"
      ></app-alert>
    </div>
    <div class="modal-footer">
      <app-button
        [class]="'btn-logisoil-green'"
        [label]="'Iniciar cadastro via planilha'"
        [icon]="'fa fa-chevron-circle-right'"
        [type]="true"
        (click)="clickRowEvent('insertBySpreadsheet')"
      >
      </app-button>
      <app-button
        [class]="'btn-logisoil-red'"
        [label]="'Cancelar'"
        (click)="c('Close click')"
      >
      </app-button>
    </div>
  </form>
</ng-template>
