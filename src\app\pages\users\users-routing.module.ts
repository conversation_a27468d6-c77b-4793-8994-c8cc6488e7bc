import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListUsersComponent } from './list-users/list-users.component';
import { RegisterUserComponent } from './register-user/register-user.component';

import { AppGuard } from 'src/app/guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListUsersComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastrarUsuario,
    component: RegisterUserComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarUsuario,
    component: RegisterUserComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarUsuario,
    component: RegisterUserComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UsersRoutingModule {}
