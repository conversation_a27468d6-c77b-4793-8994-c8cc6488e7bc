let values = [];

const Status = [
  { status: 'Ativo', value: true },
  { status: 'Inativo', value: false }
];

const Actions = [
  { id: 1, value: 'Add' },
  { id: 2, value: 'Update' }
];

const MapLine = [
  { id: 1, value: 'Cont<PERSON><PERSON>' },
  { id: 2, value: '<PERSON>ja<PERSON>' }
];

const Datum = [
  { id: 1, value: 'Córrego Alegre' },
  { id: 2, value: 'SIRGAS2000' },
  { id: 3, value: 'WGS84' },
  { id: 4, value: 'SAD69' }
];

const coordinateFormat = [
  { id: 1, value: 'Decimal Geodetic', label: 'Geográficas (Lat./Long.)' },
  { id: 2, value: 'UTM', label: 'UTM (Norte/Leste)' }
];

const Automated = [
  { id: 1, value: 'Sim' },
  { id: 2, value: 'Não' }
];

//Mapa de deslocamento
const MetricUnit = [
  { name: 'mm', id: 'mm' },
  { name: 'cm', id: 'cm' },
  { name: 'm', id: 'm' }
];

// Zone Letter: vai de C a X, exceto I e O
values = [];
for (let i = 67; i <= 88; i++) {
  if (i != 73 && i != 79) {
    values.push({ id: String.fromCharCode(i), value: String.fromCharCode(i) });
  }
}
const zoneLetterUTM = values;

// Zone Number: vai de 1 a 60
values = [];
for (let i = 1; i <= 60; i++) {
  values.push({ id: i, value: i });
}
const zoneNumberUTM = values;

//Tela de Notificações
const TypeAlerts = [
  { value: 1, label: 'Instrumentação' },
  { value: 2, label: 'Leituras' },
  { value: 3, label: 'Estabilidade' },
  { value: 4, label: 'Licença Logisoil' },
  { value: 5, label: 'Inspeções' },
  { value: 6, label: 'Análise de Estabilidade' },
  { value: 7, label: 'Estruturas' }
];

export interface intAccessLevel {
  [key: string]: {
    level: number;
    value: string;
    description: string;
    weight: number;
    edit: boolean;
    create: boolean;
  };
}

const statusLevel = {
  'super-support': 6,
  6: {
    level: 6,
    label: 'Super Suporte',
    description: 'super-support',
    weight: 60,
    edit: true,
    create: true
  },
  support: 5,
  5: {
    level: 5,
    value: 'Suporte',
    description: 'support',
    weight: 50,
    edit: false,
    create: false
  },
  'super-administrator': 4,
  4: {
    level: 4,
    value: 'Super Administrador',
    description: 'super-administrator',
    weight: 40,
    edit: true,
    create: true
  },
  administrador: 3,
  3: {
    level: 3,
    value: 'Administrador',
    description: 'administrator',
    weight: 30,
    edit: false,
    create: true
  },
  operator: 2,
  2: {
    level: 2,
    value: 'Operador',
    description: 'operator',
    weight: 20,
    edit: false,
    create: false
  },
  auditor: 1,
  1: {
    level: 1,
    value: 'Auditor',
    description: 'auditor',
    weight: 10,
    edit: false,
    create: false
  }
};

const accessLevel: intAccessLevel = {
  'super-support': {
    level: 6,
    value: 'Super Suporte',
    description: 'super-support',
    weight: 60,
    edit: true,
    create: true
  },
  support: {
    level: 5,
    value: 'Suporte',
    description: 'support',
    weight: 50,
    edit: false,
    create: false
  },
  'super-administrator': {
    level: 4,
    value: 'Super Administrador',
    description: 'super-administrator',
    weight: 40,
    edit: true,
    create: true
  },
  administrator: {
    level: 3,
    value: 'Administrador',
    description: 'administrator',
    weight: 30,
    edit: false,
    create: true
  },
  operator: {
    level: 2,
    value: 'Operador',
    description: 'operator',
    weight: 20,
    edit: false,
    create: false
  },
  auditor: {
    level: 1,
    value: 'Auditor',
    description: 'auditor',
    weight: 10,
    edit: false,
    create: false
  }
};

export interface intAccessPermisson {
  [key: string]: {
    [key: string]: {
      access: boolean;
      create: boolean;
      read: boolean;
      update: boolean;
      delete: boolean;
      client: boolean | string;
      'client-unit': boolean | string;
      structure: boolean | string;
    };
  };
}

const accessPermission: intAccessPermisson = {
  'super-support': {
    user: {
      access: true,
      create: true,
      read: true,
      update: true,
      delete: true,
      client: true,
      'client-unit': true,
      structure: true
    }
  },
  'super-administrator': {
    user: {
      access: true,
      create: true,
      read: true,
      update: true,
      delete: true,
      client: 'only',
      'client-unit': true,
      structure: true
    }
  },
  administrator: {
    user: {
      access: true,
      create: true,
      read: true,
      update: true,
      delete: true,
      client: 'only',
      'client-unit': true,
      structure: true
    }
  },
  operator: {
    user: {
      access: true,
      create: false,
      read: true,
      update: false,
      delete: false,
      client: false,
      'client-unit': false,
      structure: 'only'
    }
  },
  support: {
    user: {
      access: true,
      create: false,
      read: true,
      update: false,
      delete: false,
      client: true,
      'client-unit': 'only',
      structure: 'only'
    }
  },
  auditor: {
    user: {
      access: true,
      create: false,
      read: true,
      update: false,
      delete: false,
      client: false,
      'client-unit': 'only',
      structure: 'only'
    }
  }
};

export interface intMultiSelectSettings {
  [key: string]: {
    singleSelection: boolean;
    idField: string;
    textField: string;
    selectAllText?: string;
    unSelectAllText: string;
    searchPlaceholderText: string;
    itemsShowLimit: number;
    allowSearchFilter: boolean;
    enableCheckAll: boolean;
    closeDropDownOnSelection: boolean;
    noFilteredDataAvailablePlaceholderText: string;
    noDataAvailablePlaceholderText: string;
  };
}

const MultiSelectDefault: intMultiSelectSettings = {
  Clients: {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  Instruments: {
    singleSelection: false,
    idField: 'id',
    textField: 'identifier',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  Units: {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todas',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  Users: {
    singleSelection: true,
    idField: 'id',
    textField: 'name',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  Structures: {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todas',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  Sections: {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todas',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  View: {
    singleSelection: false,
    idField: 'label',
    textField: 'label',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 1,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  Single: {
    singleSelection: true,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: false,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  Multiple: {
    singleSelection: false,
    idField: 'id',
    textField: 'value',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  CoordinateFormat: {
    singleSelection: true,
    idField: 'id',
    textField: 'label',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: false,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  },
  Default: {
    singleSelection: true,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: false,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  }
};

const maxTrialPeriod: number = 90;

// Cadastro de Usuário
const userLocale = {
  PtBr: 1,
  1: {
    sigla: 'pt-BR',
    label: 'Português (Brasil)'
  },
  En: 2,
  2: {
    sigla: 'en',
    label: 'Inglês'
  },
  Es: 3,
  3: {
    sigla: 'es',
    label: 'Espanhol'
  }
};

enum TipoInstrumento {
  INA = 1,
  PiezometroTuboAberto = 2,
  PiezometroEletrico = 3,
  InclinometroConvencional = 4,
  InclinometroIPI = 5,
  MarcoSuperficial = 6,
  Prisma = 7,
  Recalque = 8,
  Geofone = 9,
  Linimetric = 10
}

enum LengthUnit {
  milimeters = 'mm',
  centimeters = 'cm',
  meters = 'm'
}
enum PressureUnit {
  kiloPascal = 'KPa',
  bar = 'bar',
  mca = 'mca'
}

const lengthUnits: LengthUnit[] = [LengthUnit.milimeters, LengthUnit.centimeters, LengthUnit.meters];

export {
  Status,
  Actions,
  MapLine,
  Datum,
  coordinateFormat,
  Automated,
  MetricUnit,
  zoneLetterUTM,
  zoneNumberUTM,
  statusLevel,
  accessLevel,
  accessPermission,
  MultiSelectDefault,
  maxTrialPeriod,
  userLocale,
  TypeAlerts,
  TipoInstrumento,
  LengthUnit,
  lengthUnits,
  PressureUnit
};
