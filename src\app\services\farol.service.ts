import { Injectable } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root'
})
export class FarolService {
  constructor(private sanitizer: DomSanitizer) {}

  private readonly faixas = [
    { min: 0, max: 0, letra: 'A', cor: '#00f000', corTexto: '#fff', significado: 'Satisfatório' },
    { min: 1, max: 24, letra: 'B', cor: '#ede202', corTexto: '#000', significado: 'Satisfatório com restrições' },
    { min: 25, max: 80, letra: 'C', cor: '#f09c00', corTexto: '#fff', significado: 'Insatisfatório com possibilidade de comprometimento da segurança' },
    { min: 81, max: Infinity, letra: 'D', cor: '#ff0202', corTexto: '#fff', significado: 'Insatisfatório com comprometimento da segurança' }
  ];

  /**
   * Retorna HTML seguro com o farol renderizado.
   * @param gut Valor GUT
   */
  generateFarol(gut: number): SafeHtml {
    const faixa = this.faixas.find((f) => gut >= f.min && gut <= f.max);
    if (!faixa) return '';

    const html = `
  <span
    class="badge-farol"
    style="background-color: ${faixa.cor}; color: ${faixa.corTexto};"
    title="${faixa.significado}"
  >
    ${faixa.letra}
  </span>`;

    return this.sanitizer.bypassSecurityTrustHtml(html);
  }
}
