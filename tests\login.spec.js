const { test, expect } = require('@playwright/test');

/**
 * Login Tests for Logisoil Angular Application
 *
 * This application uses OIDC authentication with Keycloak, so the login flow
 * involves redirecting to an external authentication provider.
 */
test.describe('Login Functionality', () => {

  test.beforeEach(async ({ page }) => {
    // Set up any necessary configurations
    await page.setViewportSize({ width: 1280, height: 720 });

    // Enable console logging for debugging
    page.on('console', msg => console.log('PAGE LOG:', msg.text()));
    page.on('pageerror', exception => console.log('PAGE ERROR:', exception));
  });

  test('should redirect to Keycloak login and handle failed authentication', async ({ page }) => {
    console.log('Starting login test...');

    // Navigate to the application
    await page.goto('/', { waitUntil: 'networkidle' });

    // Wait for potential redirect to Keycloak
    await page.waitForTimeout(2000);

    // Check if we're redirected to the Keycloak login page
    const currentUrl = page.url();
    console.log('Current URL after initial navigation:', currentUrl);

    // Expect to be redirected to Keycloak authentication
    expect(currentUrl).toContain('logisoil.eastus2.cloudapp.azure.com/auth');

    // Wait for the Keycloak login form to be visible
    // Try multiple possible selectors for username field
    const usernameSelectors = [
      'input[name="username"]',
      'input[id="username"]',
      'input[type="email"]',
      '#username',
      '.username'
    ];

    let usernameField = null;
    for (const selector of usernameSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3000 });
        usernameField = page.locator(selector).first();
        console.log(`Found username field with selector: ${selector}`);
        break;
      } catch (e) {
        console.log(`Username selector ${selector} not found, trying next...`);
        continue;
      }
    }

    if (!usernameField) {
      throw new Error('Could not find username field on Keycloak login page');
    }

    // Find password field
    const passwordField = page.locator('input[name="password"], input[id="password"], input[type="password"]').first();

    // Fill in the login credentials
    await usernameField.fill('<EMAIL>');
    await passwordField.fill('123455');

    console.log('Filled in credentials, attempting login...');

    // Find and click the login/submit button
    const loginButtonSelectors = [
      'input[type="submit"]',
      'button[type="submit"]',
      'input[value*="Log"]',
      'input[value*="Sign"]',
      'button:has-text("Sign")',
      'button:has-text("Log")',
      'button:has-text("Entrar")',
      'input[value*="Entrar"]',
      '#kc-login'
    ];

    let loginButton = null;
    for (const selector of loginButtonSelectors) {
      try {
        const button = page.locator(selector).first();
        if (await button.isVisible()) {
          loginButton = button;
          console.log(`Found login button with selector: ${selector}`);
          break;
        }
      } catch (e) {
        continue;
      }
    }

    if (!loginButton) {
      throw new Error('Could not find login button on Keycloak login page');
    }

    await loginButton.click();

    console.log('Login button clicked, waiting for response...');

    // Wait for the authentication response
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000); // Additional wait for any async operations

    // Check for authentication failure
    // Keycloak typically shows error messages for failed authentication
    const errorSelectors = [
      '.alert-error',
      '.error',
      '.kc-feedback-text',
      '.alert-danger',
      '.alert',
      '[class*="error"]',
      '[class*="alert"]',
      '[class*="feedback"]',
      'span:has-text("Invalid")',
      'span:has-text("Incorrect")',
      'span:has-text("Inválido")',
      'span:has-text("Incorreto")',
      'div:has-text("Invalid")',
      'div:has-text("Incorrect")',
      'div:has-text("Inválido")',
      'div:has-text("Incorreto")',
      'div:has-text("credenciais")',
      'div:has-text("credentials")'
    ];

    let errorFound = false;
    let errorMessage = '';

    // First, check for visible error messages
    for (const selector of errorSelectors) {
      try {
        const errorElement = page.locator(selector);
        if (await errorElement.isVisible()) {
          errorMessage = await errorElement.textContent();
          errorFound = true;
          console.log(`Error found with selector "${selector}": ${errorMessage}`);
          break;
        }
      } catch (e) {
        // Continue to next selector if this one fails
        continue;
      }
    }

    // Alternative check: verify we're still on the Keycloak login page
    if (!errorFound) {
      const finalUrl = page.url();
      console.log('Final URL after login attempt:', finalUrl);

      // If we're still on the Keycloak domain and not redirected back to the app,
      // it indicates the login failed
      if (finalUrl.includes('logisoil.eastus2.cloudapp.azure.com/auth')) {
        errorFound = true;
        errorMessage = 'Login failed - remained on authentication page';
        console.log('Login failure detected: still on Keycloak page');
      }

      // Additional check: look for the presence of login form again
      const usernameFieldStillPresent = await page.locator('input[name="username"], input[id="username"]').isVisible().catch(() => false);
      if (usernameFieldStillPresent) {
        errorFound = true;
        errorMessage = 'Login failed - login form still visible';
        console.log('Login failure detected: login form still present');
      }
    }

    // Assert that the login attempt failed
    expect(errorFound).toBe(true);
    console.log('Login failure confirmed:', errorMessage);

    // Take a screenshot for debugging purposes
    await page.screenshot({ path: 'tests/screenshots/login-failure.png', fullPage: true });
    console.log('Screenshot saved to tests/screenshots/login-failure.png');
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Test network error handling
    await page.route('**/*', route => {
      if (route.request().url().includes('auth')) {
        route.abort();
      } else {
        route.continue();
      }
    });

    await page.goto('/');

    // Should handle the network error gracefully
    // The exact behavior depends on how the app handles network failures
    await page.waitForTimeout(3000);

    // Take screenshot of error state
    await page.screenshot({ path: 'tests/screenshots/network-error.png', fullPage: true });
  });

  test('should display loading spinner during authentication', async ({ page }) => {
    await page.goto('/');

    // Check for loading spinner
    const spinner = page.locator('ngx-spinner, .spinner, [class*="spinner"], [class*="loading"]');

    // The spinner should be visible during the authentication process
    await expect(spinner).toBeVisible({ timeout: 5000 });

    console.log('Loading spinner detected during authentication process');
  });

});
