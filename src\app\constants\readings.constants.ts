//ina-pz-pze-ipi-ms-mr-praia-pluviometro-pluviografo
const metricUnit = [
  { name: 'mm', id: 'mm' },
  { name: 'cm', id: 'cm' },
  { name: 'm', id: 'm' }
];

//ina-pz-pze
const pressureUnit = [
  { name: 'kPa', id: 'kPa' },
  { name: 'bar', id: 'bar' },
  { name: 'mca', id: 'mca' }
];

//Leituras eixos A e B - ipi
const readingUnit = [
  { name: 'mm/m', id: 'mm/m' },
  { name: 'graus(°)', id: 'graus(°)' }
];

const Units = [
  {
    //Indicador de nível d'água
    1: [
      {
        label: 'Medida para entrada de dados - Cota NA/Profundidade',
        unit: metricUnit,
        default: 'm'
      },
      {
        label: 'Medida para entrada de dados - Pressão',
        unit: pressureUnit,
        default: 'kPa'
      }
    ],
    //Piezômetro de tubo aberto
    2: [
      {
        label: 'Medida para entrada de dados - Cota Piezométrica/Profundidade',
        unit: metricUnit,
        default: 'm'
      },
      {
        label: 'Medida para entrada de dados - Pressão',
        unit: pressureUnit,
        default: 'kPa'
      }
    ],
    //Piezômetro elétrico
    3: [
      {
        label: 'Medida para entrada de dados - Cota Piezométrica',
        unit: metricUnit,
        default: 'm'
      }
    ],
    //Inclinômetro convencional
    4: [
      {
        label: 'Medida para entrada de dados - Cota NA',
        unit: metricUnit,
        default: 'mm'
      }
    ],
    //IPI
    5: [
      {
        label: 'Medida para entrada de dados - Leituras eixos A e B',
        unit: readingUnit,
        default: 'graus(°)'
      },
      {
        label: 'Medida para entrada de dados - Deslocamento',
        unit: metricUnit,
        default: 'mm'
      }
    ],
    //Marco superficial
    6: [
      {
        label: 'Medida para entrada de dados - Cota',
        unit: metricUnit,
        default: 'm'
      }
    ],
    //Prisma
    7: [
      {
        label: 'Medida para entrada de dados - Cota',
        unit: metricUnit,
        default: 'm'
      }
    ],
    //Medidor de recalque
    8: [
      {
        label: 'Medida para entrada de dados',
        unit: metricUnit,
        default: 'm'
      }
    ],
    //Geofone
    9: [],
    //Régua linimétrica
    10: [
      {
        label: 'Medida para entrada de dados - Cota NA',
        unit: metricUnit,
        default: 'm'
      }
    ],
    //Praia
    11: [
      {
        label: 'Medida para entrada de dados - Comprimento',
        unit: metricUnit,
        default: 'm'
      }
    ],
    //Pluviômetro
    12: [
      {
        label: 'Medida para entrada de dados - Cota NA',
        unit: metricUnit,
        default: 'mm'
      }
    ],
    //Pluviógrafo
    13: [
      {
        label: 'Medida para entrada de dados - Cota NA',
        unit: metricUnit,
        default: 'mm'
      }
    ]
  }
];

const fieldsReading = [
  [],
  [
    //01-INA
    'date',
    'quota',
    'depth',
    'pressure',
    'dry'
  ],
  [
    //02-PZ
    'date',
    'quota',
    'depth',
    'pressure',
    'dry'
  ],
  [
    //03-PZE
    'date',
    'quota',
    'dry'
  ],
  [
    //04-INC-CONV
    'date',
    'positive_a',
    'negative_a',
    'positive_b',
    'negative_b',
    'average_displacement_a',
    'average_displacement_b',
    'is_referencial'
  ],
  [
    //05-IPI
    'date',
    'a_axis_reading',
    'b_axis_reading',
    'average_displacement_a',
    'average_displacement_b',
    'is_referencial'
  ],
  [
    //06-MS
    'date',
    'quota',
    'east_coordinate',
    'north_coordinate',
    'is_referencial'
  ],
  [
    //07-PR
    'date',
    'quota',
    'east_coordinate',
    'north_coordinate',
    'is_referencial'
  ],
  [
    //08-MR
    'date',
    'absolute_settlement',
    'absolute_depth'
  ],
  [
    //09-GEO
    'date',
    'nature',
    'a_axis_pga',
    'b_axis_pga',
    'z_axis_pga',
    'east_coordinate',
    'north_coordinate'
  ],
  [
    //10-RL
    'date',
    'quota'
  ],
  [
    //11-Praia
    'date',
    'length'
  ],
  [
    //12-Pluviômetro
    'date',
    'pluviometry'
  ],
  [
    //13-Pluviógrafo
    'date',
    'pluviometry'
  ]
];

const navBar = {
  //Indicador de nível d'água
  1: {
    label: 'INA'
  },
  //Piezômetro de tubo aberto
  2: {
    label: 'PZ Tubo Aberto'
  },
  //Piezômetro elétrico
  3: {
    label: 'PZ Elétrico'
  },
  //Inclinômetro convencional
  4: {
    label: 'INC Convencional'
  },
  //IPI
  5: {
    label: 'INC IPI'
  },
  //Marco superficial
  6: {
    label: 'Marco Superficial'
  },
  //Prisma
  7: {
    label: 'Prisma'
  },
  //Medidor de recalque
  8: {
    label: 'Medidor de Recalque'
  },
  //Geofone
  9: {
    label: 'Geofone'
  },
  //Régua linimétrica
  10: {
    label: 'Régua Linimétrica'
  },
  //Pluviômetro
  12: {
    label: 'Pluviômetro'
  },
  //Pluviógrafo
  13: {
    label: 'Pluviógrafo'
  }
};

export { metricUnit, pressureUnit, Units, readingUnit, fieldsReading, navBar };
