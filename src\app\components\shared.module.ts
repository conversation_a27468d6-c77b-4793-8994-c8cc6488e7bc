import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgSelectModule } from '@ng-select/ng-select';

import { RouterModule } from '@angular/router';
import { ClickOutsideModule } from 'ng4-click-outside';
import { ColorSketchModule } from 'ngx-color/sketch';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FullCalendarModule } from '@fullcalendar/angular';
import { GoogleMapsModule } from '@angular/google-maps';
import { NgbPaginationModule, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerModule } from 'ngx-spinner';
import { NgChartsModule } from 'ng2-charts';
import { NgxEchartsModule } from 'ngx-echarts';
import { ToastrModule } from 'ngx-toastr';
import { NgIdleModule } from '@ng-idle/core';
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';

//Components
import { AlertComponent } from './alert/alert.component';
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';
import { ButtonComponent } from './button/button.component';
import { CalendarComponent } from './calendar/calendar.component';
import { CarouselComponent } from './carousel/carousel.component';
import { ContentComponent } from './content/content.component';
import { DragAndDropComponent } from './drag-and-drop/drag-and-drop.component';
import { DxfViewerComponent } from './dxf-viewer/dxf-viewer.component';
import { FarolLegendComponent } from './farol-legend/farol-legend.component';
import { FullComponent } from './full/full.component';
import { GoogleMapsComponent } from './google-maps/google-maps.component';
import { GutBadgeComponent } from './gut-badge/gut-badge.component';
import { HeaderComponent } from './header/header.component';
import { HierarchyComponent } from './hierarchy/hierarchy.component';
import { ImagesComponent } from './images/images.component';
import { ListTypeInstrumentsComponent } from './list-type-instruments/list-type-instruments.component';
import { MainComponent } from './main/main.component';
import { MapInfoComponent } from './map-info/map-info.component';
import { MenuComponent } from './menu/menu.component';
import { MiniDashboardComponent } from './mini-dashboard/mini-dashboard.component';
// Modal
import { ModalCalendarActivitiesComponent } from './modal/modal-calendar-activities/modal-calendar-activities.component';
import { ModalComponentsComponent } from './modal/modal-components/modal-components.component';
import { ModalConfirmComponent } from './modal/modal-confirm/modal-confirm.component';
import { ModalDownloadListInstrumentComponent } from './modal/modal-download-list-instrument/modal-download-list-instrument.component';
import { ModalViewDxfComponent } from '@pages/stability/analysis-stability/modal-view-dxf/modal-view-dxf.component';
import { ModalInstructionsComponent } from './modal/modal-instructions/modal-instructions.component';
import { ModalInstrumentComponent } from './modal/modal-instrument/modal-instrument.component';
import { ModalInsertInstrumentBySpreadsheetComponent } from './modal/modal-insert-instrument-by-spreadsheet/modal-insert-instrument-by-spreadsheet.component';
import { ModalMapComponent } from './modal/modal-map/modal-map.component';
import { ModalProfileComponent } from './modal/modal-profile/modal-profile.component';
import { ModalSafetyFactorComponent } from './modal/modal-safety-factor/modal-safety-factor.component';
import { ModalSearchMethodComponent } from './modal/modal-search-method/modal-search-method.component';
import { ModalTermoComponent } from './modal/modal-termo/modal-termo.component';
import { ModalStandardComponent } from './modal/modal-standard/modal-standard.component';
// Modal
import { NotificationsComponent } from './notifications/notifications.component';
import { PaginatorComponent } from './paginator/paginator.component';
import { ReloadTimerComponent } from './reload-timer/reload-timer.component';
import { SelectInputComponent } from './select-input/select-input.component';
import { SortableListComponent } from './sortable-list/sortable-list.component';
import { TableComponent } from './table/table.component';

//Graficos
import { ChartsComponent } from './charts/charts.component';
import { EChartsComponent } from './e-charts/e-charts.component';
import { ModalChartComponent } from './modal/modal-chart/modal-chart.component';
import { ChartAbsoluteVariationComponent } from '@pages/instruments/chart-instruments/chart-absolute-variation/chart-absolute-variation.component';

//Table Headers
import { ThAbsoluteVariationComponent } from '@pages/instruments/list-instruments/th-absolute-variation/th-absolute-variation.component';

//Tag Input
import { TagInputComponent } from './tag-input/tag-input.component';

//Tour guiado
import { TourNgBootstrapModule } from 'ngx-ui-tour-ng-bootstrap';

import { DxfViewerV2Component } from './dxf-viewer-v2/dxf-viewer-v2.component';

//Backdrop
import { LoaderBackdropComponent } from './loader-backdrop/loader-backdrop.component';

import { LogisoilDirectivesModule } from '../shared/logisoil-directives.module';

@NgModule({
  declarations: [
    AlertComponent,
    BreadcrumbComponent,
    ButtonComponent,
    CarouselComponent,
    CalendarComponent,
    ContentComponent,
    DragAndDropComponent,
    DxfViewerComponent,
    DxfViewerV2Component,
    FarolLegendComponent,
    FullComponent,
    GoogleMapsComponent,
    GutBadgeComponent,
    HeaderComponent,
    HierarchyComponent,
    ImagesComponent,
    ListTypeInstrumentsComponent,
    MainComponent,
    MapInfoComponent,
    MenuComponent,
    MiniDashboardComponent,
    //Modal
    ModalCalendarActivitiesComponent,
    ModalComponentsComponent,
    ModalConfirmComponent,
    ModalDownloadListInstrumentComponent,
    ModalViewDxfComponent,
    ModalInsertInstrumentBySpreadsheetComponent,
    ModalInstructionsComponent,
    ModalInstrumentComponent,
    ModalMapComponent,
    ModalProfileComponent,
    ModalSafetyFactorComponent,
    ModalTermoComponent,
    ModalStandardComponent,
    //Modal
    NotificationsComponent,
    PaginatorComponent,
    SelectInputComponent,
    SortableListComponent,
    TableComponent,
    //Graficos
    ChartsComponent,
    EChartsComponent,
    ModalChartComponent,
    ChartAbsoluteVariationComponent,
    //Table Headers
    ThAbsoluteVariationComponent,
    TagInputComponent,
    ModalSearchMethodComponent,
    LoaderBackdropComponent,
    ReloadTimerComponent
  ],
  imports: [
    CommonModule,
    ColorSketchModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbModule,
    DragDropModule,
    ClickOutsideModule,
    GoogleMapsModule,
    FullCalendarModule,
    NgMultiSelectDropDownModule,
    NgSelectModule,
    NgxSpinnerModule,
    NgChartsModule,
    NgxEchartsModule.forRoot({
      echarts: () => import('echarts')
    }),
    ToastrModule.forRoot({
      timeOut: 8000, // Duração padrão do toast (em milissegundos)
      positionClass: 'toast-top-right', // Posição do toast na tela
      preventDuplicates: true, // Impede a exibição de toasts duplicados
      progressBar: true,
      closeButton: true
    }),
    TourNgBootstrapModule.forRoot(),
    NgIdleModule.forRoot(),
    NgIdleKeepaliveModule.forRoot(),
    LogisoilDirectivesModule
  ],
  exports: [
    AlertComponent,
    ButtonComponent,
    CarouselComponent,
    CalendarComponent,
    ContentComponent,
    DragAndDropComponent,
    DxfViewerComponent,
    DxfViewerV2Component,
    FarolLegendComponent,
    FullComponent,
    GoogleMapsComponent,
    GutBadgeComponent,
    HierarchyComponent,
    ImagesComponent,
    ListTypeInstrumentsComponent,
    NgxSpinnerModule,
    MainComponent,
    MapInfoComponent,
    TourNgBootstrapModule,
    //Modal
    ModalComponentsComponent,
    ModalConfirmComponent,
    ModalDownloadListInstrumentComponent,
    ModalViewDxfComponent,
    ModalInsertInstrumentBySpreadsheetComponent,
    ModalInstructionsComponent,
    ModalInstrumentComponent,
    ModalMapComponent,
    ModalSafetyFactorComponent,
    ModalSearchMethodComponent,
    ModalStandardComponent,
    //Modal
    PaginatorComponent,
    SelectInputComponent,
    SortableListComponent,
    TableComponent,
    //Graficos
    ChartsComponent,
    EChartsComponent,
    ModalChartComponent,

    TagInputComponent,
    LoaderBackdropComponent,
    ReloadTimerComponent
  ]
})
export class SharedModule {}
