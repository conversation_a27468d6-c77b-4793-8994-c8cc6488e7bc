import { Component, ElementRef, Input, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';

import { StaticMaterialsService as StaticMaterialsServiceApi } from 'src/app/services/api/staticMaterials.service';
import { ConditionService } from 'src/app/services/condition.service';

@Component({
  selector: 'app-tabs-conditions',
  templateUrl: './tabs-conditions.component.html',
  styleUrls: ['./tabs-conditions.component.scss']
})
export class TabsConditionsComponent implements OnInit {
  @ViewChild('hierarchy') hierarchy: any;

  @ViewChildren('drainedRef') drainedRef: QueryList<ElementRef>;
  @ViewChildren('undrainedRef') undrainedRef: QueryList<ElementRef>;
  @ViewChildren('pseudoRef') pseudoRef: QueryList<ElementRef>;

  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public info: string = 'Preencha ao menos um dos formulários abaixo para completar o cadastro do material.';

  public drainedTabConfig: any = { styleColor: false, active: true };
  public undrainedTabConfig: any = { styleColor: false, active: false };
  public pseudoTabConfig: any = { styleColor: false, active: false };

  public dataDrained: any = null;
  public dataUndrained: any = null;
  public dataPseudo: any = null;

  public materialsList: any = [];

  public ctrlMsgTab: any = null;

  constructor(private staticMaterialsServiceApi: StaticMaterialsServiceApi, private conditionService: ConditionService) {}

  ngOnInit(): void {}

  /**
   * Ativa a aba selecionada (drenada, não drenada ou pseudo-estática),
   * desativando as demais. Usado para controle visual de tabs no formulário.
   *
   * @param option - O tipo de aba a ser ativada: 'drained', 'undrained' ou 'pseudo'.
   */
  selectTab(option: any = '') {
    switch (option) {
      case 'drained':
        this.drainedTabConfig.active = true;
        this.undrainedTabConfig.active = false;
        this.pseudoTabConfig.active = false;
        break;
      case 'undrained':
        this.drainedTabConfig.active = false;
        this.undrainedTabConfig.active = true;
        this.pseudoTabConfig.active = false;
        break;
      case 'pseudo':
        this.drainedTabConfig.active = false;
        this.undrainedTabConfig.active = false;
        this.pseudoTabConfig.active = true;
        break;
    }
  }

  /**
   * Busca a lista de materiais estáticos vinculados à estrutura selecionada.
   *
   * A busca só é feita se a ação for 'select'. Os resultados são armazenados
   * na propriedade `materialsList` para uso em dropdowns ou seleção.
   *
   * @param structure - Objeto com dados da estrutura (deve conter `id`).
   * @param action - A ação realizada: por padrão 'select'. Outras ações não disparam a busca.
   */
  getStaticMaterialsList(structure, action: string = 'select') {
    this.materialsList = [];

    if (action === 'select') {
      this.staticMaterialsServiceApi.getStaticMaterialsList({ StructureId: structure.id }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.materialsList = dados;
      });
    }
  }

  /**
   * Copia os dados de um formulário de condição para outro,
   * incluindo materiais, modelos constitutivos, HU, superfícies de água, etc.
   *
   * Os formulários são acessados a partir de referências armazenadas como ViewChildren.
   *
   * @param fromRef - Nome da referência de origem.
   * @param toRef - Nome da referência de destino.
   */
  copyFromTo(fromRef, toRef) {
    let fromCondition = this[fromRef].toArray()[0];
    let toCondition = this[toRef].toArray()[0];
    this.conditionService.copyElementFromTo(fromCondition, toCondition);
  }
}
