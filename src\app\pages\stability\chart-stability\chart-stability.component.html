<div class="list-content mb-3">
  <form [formGroup]="formStabilityChart">
    <!-- <PERSON>ltro Cliente, Unidade, Estrutura -->
    <div class="row mt-2">
      <app-hierarchy
        #hierarchy
        [elements]="elements"
        class="col-md-12"
        (sendEventHierarchy)="getEventHierarchy($event)"
        (filtersChanged)="filterEventHierarchy($event)"
      ></app-hierarchy>
    </div>
    <div class="row mt-2">
      <!-- Tipo de Superfície -->
      <div class="col-md-2">
        <label class="form-label">Tipo de Superfície</label>
        <select
          class="form-select"
          formControlName="SurfaceType"
          (change)="setDataSelect([], 'period')"
        >
          <option value="">Selecione...</option>
          <ng-template ngFor let-item [ngForOf]="surfaceType">
            <option [ngValue]="item.value">
              {{ item.label }}
            </option>
          </ng-template>
        </select>
      </div>
      <!-- Métodos de cálculo -->
      <div class="col-md-3">
        <label class="form-label">Método de cálculo</label>
        <select
          class="form-select"
          formControlName="CalculationMethod"
          (change)="setDataSelect([], 'period')"
        >
          <option value="">Selecione...</option>
          <ng-template ngFor let-item [ngForOf]="calculationMethods">
            <option [ngValue]="item.value">
              {{ item.label }}
            </option>
          </ng-template>
        </select>
      </div>
      <!-- Período -->
      <div class="col-md-2">
        <label class="form-label">Data e hora inicial</label>
        <input
          type="datetime-local"
          class="form-control"
          formControlName="StartDate"
        />
      </div>
      <div class="col-md-2">
        <label class="form-label">Data e hora final</label>
        <input
          type="datetime-local"
          class="form-control"
          formControlName="EndDate"
        />
      </div>
      <div class="col-md-3 d-flex align-items-end">
        <app-button
          *ngIf="ctrlBtnFilter"
          [class]="'btn-logisoil-blue me-4'"
          [customBtn]="true"
          [icon]="'fa fa-line-chart'"
          [label]="'Gerar gráfico'"
          (click)="getSafetyFactorList()"
        ></app-button>
        <app-button
          [class]="'btn-logisoil-gray'"
          [icon]="'fa fa-eraser'"
          [label]="'Limpar'"
          (click)="resetFilter()"
        ></app-button>
      </div>
    </div>

    <!-- Alerta de erro -->
    <app-alert
      class="mt-3"
      [class]="'alert-danger'"
      [messages]="messagesError"
    ></app-alert>
    <!-- Alerta para tipo de superfície -->
    <div
      class="alert mt-2"
      [ngClass]="messageSurfaceType.class"
      role="alert"
      *ngIf="messageSurfaceType.status"
      [innerHTML]="messageSurfaceType.text"
    ></div>
    <!-- Alerta para método de cálculo -->
    <div
      class="alert mt-2"
      [ngClass]="messageCalculationMethods.class"
      role="alert"
      *ngIf="messageCalculationMethods.status"
      [innerHTML]="messageCalculationMethods.text"
    ></div>
    <div
      class="alert mt-2"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
      [innerHTML]="message.text"
    ></div>

    <div class="row mt-4 mb-2" *ngIf="ctrlBtnFilter && dados != null">
      <div class="col-md-12 d-flex">
        <app-button
          [class]="'btn-logisoil-blue'"
          [label]="'Ver Análise'"
          (click)="ModalSafetyFactor.openModal()"
          class="me-2"
        ></app-button>
        <ng-template ngFor let-section [ngForOf]="sections" let-i="index">
          <div (clickOutside)="onClickedOutside('colorPicker', section.name)">
            <app-button
              [class]="'btn-logisoil-white'"
              [icon]="'fa fa-square fa-xl'"
              [iconColor]="selectedColor[section.name]"
              [label]="'Cor Seção ' + section.name"
              class="me-2"
              (click)="
                showColorPicker[section.name] = !showColorPicker[section.name]
              "
            ></app-button>
            <div
              class="color-picker-container"
              *ngIf="showColorPicker[section.name]"
            >
              <div style="width: 220px; display: inline-block">
                <color-sketch
                  [color]="selectedColor[section.name]"
                  (onChangeComplete)="changeComplete($event, section.name)"
                ></color-sketch>
              </div>
            </div>
          </div>
        </ng-template>
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-floppy-disk'"
          [label]="'Salvar cores'"
          [type]="false"
          style="margin-bottom: 7px"
          (click)="editSectionsChartLineColor()"
        >
        </app-button>
      </div>
    </div>

    <!-- Accordion -->
    <div class="row" *ngIf="ctrlBtnFilter && dados != null">
      <div class="accordion" id="accordionExample">
        <!-- Tabela -->
        <div class="accordion-item">
          <h2 class="accordion-header" id="headingOne">
            <button
              class="accordion-button collapsed"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#collapseOne"
              aria-expanded="true"
              aria-controls="collapseOne"
            >
              <strong>Tabela</strong>
            </button>
          </h2>
          <div
            id="collapseOne"
            class="accordion-collapse collapse show"
            aria-labelledby="headingOne"
            data-bs-parent="#accordionExample"
          >
            <div class="accordion-body">
              <app-table
                [tableHeader]="tableHeader"
                [tableData]="tableData"
                [permissaoUsuario]="permissaoUsuario"
              ></app-table>
            </div>
          </div>
        </div>
        <!-- Fatores de Segurança para a condição Não Drenada -->
        <div class="accordion-item">
          <h2 class="accordion-header" id="headingTwo">
            <button
              class="accordion-button collapsed"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#collapseTwo"
              aria-expanded="true"
              aria-controls="collapseTwo"
            >
              <strong>Fatores de Segurança para a condição Não Drenada</strong>
            </button>
          </h2>
          <div
            id="collapseTwo"
            class="accordion-collapse collapse show"
            aria-labelledby="headingTwo"
            data-bs-parent="#accordionExample"
          >
            <!-- Gráfico ND -->
            <div class="accordion-body">
              <div class="col-md-12" *ngIf="chart.undrained.options">
                <app-e-charts
                  [dataChart]="chart.undrained"
                  [height]="300"
                  (chartInit)="initMarkerClickEvent($event, 'undrained')"
                  [id]="'undrained'"
                  #chartUndrained
                ></app-e-charts>
              </div>
            </div>
          </div>
        </div>
        <!-- Fatores de Segurança para a condição Drenada -->
        <div class="accordion-item">
          <h2 class="accordion-header" id="headingThree">
            <button
              class="accordion-button collapsed"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#collapseThree"
              aria-expanded="true"
              aria-controls="collapseThree"
            >
              <strong>Fatores de Segurança para a condição Drenada</strong>
            </button>
          </h2>
          <div
            id="collapseThree"
            class="accordion-collapse collapse show"
            aria-labelledby="headingThree"
            data-bs-parent="#accordionExample"
          >
            <!-- Gráfico D -->
            <div class="accordion-body">
              <div class="col-md-12" *ngIf="chart.drained.options">
                <app-e-charts
                  [dataChart]="chart.drained"
                  [height]="300"
                  (chartInit)="initMarkerClickEvent($event, 'drained')"
                  [id]="'drained'"
                  #chartDrained
                ></app-e-charts>
              </div>
            </div>
          </div>
        </div>
        <!-- Fatores de Segurança para a condição Pseudo estática -->
        <div class="accordion-item">
          <h2 class="accordion-header" id="headingFour">
            <button
              class="accordion-button collapsed"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#collapseFour"
              aria-expanded="true"
              aria-controls="collapseFour"
            >
              <strong
                >Fatores de Segurança para a condição Pseudo estática</strong
              >
            </button>
          </h2>
          <div
            id="collapseFour"
            class="accordion-collapse collapse show"
            aria-labelledby="headingFour"
            data-bs-parent="#accordionExample"
          >
            <!-- Gráfico Pseudo -->
            <div class="accordion-body">
              <div class="col-md-12" *ngIf="chart.pseudoStatic.options">
                <app-e-charts
                  [dataChart]="chart.pseudoStatic"
                  [height]="300"
                  (chartInit)="initMarkerClickEvent($event, 'pseudoStatic')"
                  [id]="'pseudoStatic'"
                  #chartPseudoStatic
                ></app-e-charts>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Voltar -->
    <div class="col-md-12 mt-3 d-flex justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [routerLink]="['/stability']"
      ></app-button>
    </div>
  </form>
</div>

<app-modal-safety-factor
  #modalSafetyFactor
  [title]="titleModal"
  [sections]="sections"
  [surfaceType]="controls['SurfaceType'].value"
  [calculationMethod]="controls['CalculationMethod'].value"
  [mapSliFileType]="mapSliFileType"
  [sectionResults]="dados?.section_results"
  [selectedItem]="selectedItem"
></app-modal-safety-factor>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
