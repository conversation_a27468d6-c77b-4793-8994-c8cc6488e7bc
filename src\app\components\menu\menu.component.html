<div class="sidebar" [ngClass]="menuActive ? 'active' : ''">
  <div class="logo_content">
    <div class="logo">
      <a
        (click)="menuActive = !menuActive"
        [ngbTooltip]="'Clique para expandir/recolher o menu'"
      >
        <img src="/assets/images/logoLogisoil.png" alt="" />
        <img src="/assets/images/logoL.png" alt="" />
      </a>
    </div>
    <ul class="nav_list">
      <ng-template ngFor let-menu [ngForOf]="menu" let-i="index">
        <li *ngIf="menu.Show">
          <a [routerLink]="menu.Rota ? [menu.Rota] : []">
            <i [ngClass]="menu.Icone" *ngIf="menu.IconeType !== 'svg'"></i>
            <img
              src="/assets/ico/ico-menu/{{ menu.Icone }}"
              *ngIf="menu.IconeType === 'svg'"
            />
            <span class="links_name">{{ menu.Titulo }}</span>
          </a>
        </li>
      </ng-template>
      <!-- Acesso telas Cliente/Unidade/Estrutura -->
      <li>
        <a style="cursor: pointer">
          <i class="fa-solid fa-user-gear"></i>
          <span class="links_name">Administrador</span>
        </a>
        <div>
          <ul class="nav_list_submenu" [ngClass]="menuActive ? 'active' : ''">
            <ng-template
              ngFor
              let-menuAdmin
              [ngForOf]="menuAdmin"
              let-i="index"
            >
              <li *ngIf="menuAdmin.Show">
                <a [routerLink]="menuAdmin.Rota ? [menuAdmin.Rota] : []">
                  <i
                    [ngClass]="menuAdmin.Icone"
                    *ngIf="menuAdmin.IconeType !== 'svg'"
                  ></i>
                  <img
                    src="/assets/ico/ico-menu/{{ menuAdmin.Icone }}"
                    *ngIf="menuAdmin.IconeType === 'svg'"
                    alt="{{ menuAdmin.Titulo }}"
                    title="{{ menuAdmin.Titulo }}"
                  />
                  <span class="links_name">{{ menuAdmin.Titulo }}</span>
                </a>
              </li>
            </ng-template>
          </ul>
        </div>
      </li>
    </ul>
  </div>
</div>
