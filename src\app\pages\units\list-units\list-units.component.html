<div class="list-content">
  <!-- Cadastrar nova unidade -->
  <div class="button-client">
    <app-button
      tourAnchor="register_unit_button"
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Cadastrar nova unidade'"
      [routerLink]="['create']"
      *ngIf="permissaoUsuario.create"
    ></app-button>
  </div>

  <div class="row g-3 mt-1">
    <!-- ID -->
    <div class="col-md-3" tourAnchor="id_filter">
      <label class="form-label">ID</label>
      <input
        [(ngModel)]="filter.SearchIdentifier"
        type="number"
        step="1"
        min="1"
        class="form-control"
        placeholder="ID unidade"
        autocomplete="off"
        (keypress)="
          func.controlNumber(
            $event,
            filter.SearchIdentifier,
            'positive',
            'ngModel'
          )
        "
        (keyup)="
          func.controlNumber($event, filter.SearchIdentifier, null, 'ngModel')
        "
      />
    </div>
    <!-- Selects Cliente, Unidade -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-6"
      tourAnchor="client_dropdown"
    ></app-hierarchy>
    <!-- Status -->
    <div class="col-md-3" tourAnchor="status_dropdown">
      <label class="form-label">Status</label>
      <select class="form-select" [(ngModel)]="filter.Active">
        <option value="">Selecione...</option>
        <option *ngFor="let item of status" [ngValue]="item.value">
          {{ item.status }}
        </option>
      </select>
    </div>
  </div>
  <div class="row mt-1 mb-3">
    <!-- Visualização -->
    <div class="col-md-3" tourAnchor="visualization_dropdown">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>

    <!-- Botões -->
    <div class="col-md-9 d-flex align-items-end justify-content-end">
      <app-button
        tourAnchor="search_button"
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        class="me-1"
        (click)="managerFilters(true)"
      ></app-button>
      <app-button
        tourAnchor="reset_button"
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>
  </div>

  <!-- Alertas -->
  <div
    class="alert alert-warning mt-3"
    role="alert"
    *ngIf="messageReturn.status"
  >
    {{ messageReturn.text }}
  </div>

  <div class="alert alert-success mt-4" role="alert" *ngIf="message.status">
    {{ message.text }}
  </div>

  <!-- Tabela -->
  <div class="col-12 mt-3" tourAnchor="table_units">
    <app-table
      *ngIf="tableData.length > 0"
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeader"
      [tableData]="tableData"
      (sendToggleStatus)="toggleStatus($event)"
      (sendClickRowEvent)="clickRowEvent($event)"
      [permissaoUsuario]="permissaoUsuario"
      [menuMiniDashboard]="'miniDashboardUnit'"
    >
    </app-table>
    <!-- Tabela -->

    <!-- Paginação -->
    <app-paginator
      tourAnchor="paginator"
      *ngIf="tableData.length > 0"
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event)"
      [enableItemPerPage]="true"
    ></app-paginator>
  </div>

  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      tourAnchor="back_button"
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela inicial'"
      [click]="goBack.bind(this)"
    ></app-button>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>

<tour-step-template></tour-step-template>
