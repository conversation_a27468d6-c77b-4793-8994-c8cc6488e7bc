import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { IDropdownSettings } from 'ng-multiselect-dropdown';

import { Slide2ConfigurationService } from 'src/app/services/api/slide2Configuration.service';

import {
  calculationMethods as calculationMethodsEnum,
  surfacesType as surfacesTypeEnum,
  surfacesTypeMethodsFields as surfacesTypeMethodsFieldsEnum
} from 'src/app/constants/structure.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-configurations-tab',
  templateUrl: './configurations-tab.component.html',
  styleUrls: ['./configurations-tab.component.scss']
})
export class ConfigurationsTabComponent implements OnInit {
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;

  public formConfigurations: FormGroup = new FormGroup({
    calculation_methods_circular: new FormControl(''),
    calculation_methods_non_circular: new FormControl(''),

    surface_type_circular: new FormControl(''),
    surface_type_non_circular: new FormControl(''),

    //Parâmetros circulares:
    circular_divisions_along_slope: new FormControl(30, [Validators.required]),
    circular_circles_per_division: new FormControl(12, [Validators.required]),
    circular_number_of_iterations: new FormControl(12, [Validators.required]),
    circular_divisions_next_iteration: new FormControl(50, [Validators.required]),
    circular_radius_increment: new FormControl(30, [Validators.required]),
    circular_number_of_surfaces: new FormControl(50000, [Validators.required]),

    //Parâmetros não circulares:
    non_circular_divisions_along_slope: new FormControl(30, [Validators.required]),
    non_circular_surfaces_per_division: new FormControl(12, [Validators.required]),
    non_circular_number_of_iterations: new FormControl(12, [Validators.required]),
    non_circular_divisions_next_iteration: new FormControl(50, [Validators.required]),
    non_circular_number_of_vertices_along_surface: new FormControl(8, [Validators.required]),
    non_circular_number_of_surfaces: new FormControl(50000, [Validators.required]),
    non_circular_number_of_nests: new FormControl(75, [Validators.required]),
    non_circular_maximum_iterations: new FormControl(500, [Validators.required]),
    non_circular_initial_number_of_surface_vertices: new FormControl(8, [Validators.required]),
    non_circular_initial_number_of_iterations: new FormControl(2000, [Validators.required]),
    non_circular_maximum_number_of_steps: new FormControl(20, [Validators.required]),
    non_circular_number_of_factors_safety_compared_before_stopping: new FormControl(8, [Validators.required]),
    non_circular_tolerance_for_stopping_criterion: new FormControl(0.0001, [Validators.required]),
    non_circular_number_of_particles: new FormControl(75, [Validators.required]),

    surface_type: new FormControl('', [Validators.min(1)])
  });

  public calculationMethods = calculationMethodsEnum;
  public surfacesTypes = surfacesTypeEnum;
  public surfacesTypeMethodsFields = surfacesTypeMethodsFieldsEnum;
  public searchMethods: any = [];

  public slide2Configurations: any = {
    name: null,

    circular_search_method: null,
    non_circular_search_method: null,

    calculation_methods: null,

    circular_parameters: {
      divisions_along_slope: null,
      circles_per_division: null,
      number_of_iterations: null,
      divisions_next_iteration: null,
      radius_increment: null,
      number_of_surfaces: null
    },
    non_circular_parameters: {
      divisions_along_slope: null,
      surfaces_per_division: null,
      number_of_iterations: null,
      divisions_next_iteration: null,
      number_of_vertices_along_surface: null,
      number_of_surfaces: null,
      number_of_nests: null,
      initial_number_of_surface_vertices: null,
      initial_number_of_iterations: null,
      maximum_number_of_steps: null,
      number_of_factors_safety_compared_before_stopping: null,
      tolerance_for_stopping_criterion: null,
      number_of_particles: null
    }
  };

  public dropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 10,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  public methodsSelected: any = {
    surface_type_circular: {
      title: 'Parâmetros circulares',
      subtitle: null,
      method: null,
      fields: []
    },
    surface_type_non_circular: {
      title: 'Parâmetros não circulares',
      subtitle: null,
      method: null,
      fields: []
    }
  };

  public calculationMethodsDisabled = false;

  public func = fn;

  constructor(private slide2Configuration: Slide2ConfigurationService) {}

  /**
   * Lifecycle hook executado após a criação do componente.
   * Inicia a validação de acesso com base no perfil do usuário.
   */
  ngOnInit(): void {
    this.validateAccess();
  }

  /**
   * Atualiza os métodos de cálculo e campos associados com base no tipo de superfície selecionado.
   *
   * @param type - Tipo de superfície ('surface_type_circular' ou 'surface_type_non_circular').
   */
  getSurfacesTypesMethods(type: string) {
    let methodId = null;
    if (this.formConfigurations.get(type).value !== '') {
      methodId = parseInt(this.formConfigurations.get(type).value);
    }

    if (methodId != null) {
      this.methodsSelected[type].subtitle = this.surfacesTypes[type].search[methodId].method;

      this.methodsSelected[type].method = this.surfacesTypes[type].search[methodId].value;

      this.methodsSelected[type].fields = this.surfacesTypes[type].search[methodId].fields;
    } else {
      // this.formConfigurations.get('surface_type').setValue(this.formConfigurations.get('surface_type').value - 1);
      this.methodsSelected[type].subtitle = null;
      this.methodsSelected[type].method = null;
      this.methodsSelected[type].fields = [];
    }

    this.managerValidateSurfacesType();
  }

  /**
   * Gerencia e aplica as validações nos campos de métodos de cálculo
   * com base nos tipos de superfície selecionados.
   * Também atualiza o campo 'surface_type' com valor de controle binário.
   */
  managerValidateSurfacesType() {
    //Validação
    // if (this.formConfigurations.get('surface_type_circular').value === '' && this.formConfigurations.get('surface_type_non_circular').value === '') {
    //   this.formConfigurations.get('surface_type').setValue(0);
    // } else {
    let controle = 0;
    if (this.formConfigurations.get('surface_type_circular').value !== '') {
      controle += 1;
      this.formConfigurations.get('calculation_methods_circular').setValidators([Validators.required]);
      this.formConfigurations.get('calculation_methods_circular').updateValueAndValidity();
    } else {
      this.formConfigurations.controls['calculation_methods_circular'].setErrors(null);
      this.formConfigurations.controls['calculation_methods_circular'].clearValidators();
    }

    if (this.formConfigurations.get('surface_type_non_circular').value !== '') {
      controle += 2;
      this.formConfigurations.get('calculation_methods_non_circular').setValidators([Validators.required]);
      this.formConfigurations.get('calculation_methods_non_circular').updateValueAndValidity();
    } else {
      this.formConfigurations.controls['calculation_methods_non_circular'].setErrors(null);
      this.formConfigurations.controls['calculation_methods_non_circular'].clearValidators();
    }
    this.formConfigurations.get('surface_type').setValue(controle);
    // }
  }

  /**
   * Valida o acesso ao formulário e desativa os campos caso esteja em modo visualização.
   *
   * @param role - (Opcional) Nível de permissão a ser validado. Padrão é 0.
   */
  validateAccess(role: number = 0): any {
    if (this.view) {
      this.formConfigurations.disable();
      this.calculationMethodsDisabled = true;
    }
  }

  /**
   * Popula o formulário `formConfigurations` com os dados recebidos.
   * Para os tipos de superfície, também atualiza os métodos de cálculo correspondentes.
   *
   * @param dataTab - Objeto contendo os dados que devem ser atribuídos ao formulário.
   */
  setData(dataTab: any = []) {
    for (var index in dataTab) {
      this.formConfigurations.get(index).setValue(dataTab[index]);
      switch (index) {
        case 'surface_type_circular':
          this.getSurfacesTypesMethods('surface_type_circular');
          break;
        case 'surface_type_non_circular':
          this.getSurfacesTypesMethods('surface_type_non_circular');
          break;
      }
    }
  }

  /**
   * Recupera os valores do formulário `formConfigurations`,
   * convertendo os métodos de cálculo selecionados para arrays de IDs.
   *
   * @returns Um objeto contendo os dados do formulário formatados para envio.
   */
  getData() {
    let dataTab = {};
    for (let index in this.formConfigurations.controls) {
      switch (index) {
        case 'calculation_methods_circular':
        case 'calculation_methods_non_circular':
          let obj = this.formConfigurations.get(index).value;
          let arr = [];
          for (let key in obj) {
            arr.push(parseInt(obj[key]['id']));
          }
          dataTab[index] = arr;
          // dataTab[index] = fn.extractIndex(this.formConfigurations.get(index).value, 'id');
          // dataTab[index] = dataTab[index].map((item: any) => {
          //   return parseInt(item);
          // });
          break;
        default:
          dataTab[index] = this.formConfigurations.get(index).value;
          break;
      }
    }
    return dataTab;
  }
}
