import { AfterViewInit, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

import { MultiSelectDefault, Status } from 'src/app/constants/app.constants';
import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';

import { FilterService } from 'src/app/services/filter.service';
import { UserService } from 'src/app/services/user.service';
import { StaticMaterialsService as StaticMaterialsServiceApi } from 'src/app/services/api/staticMaterials.service';

import fn from 'src/app/utils/function.utils';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-list-static-materials',
  templateUrl: './list-static-materials.component.html',
  styleUrls: ['./list-static-materials.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListStaticMaterialsComponent implements OnInit, AfterViewInit {
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalConfirm') ModalConfirm: any;

  public tableHeader: any = [
    {
      label: 'ID',
      width: '80px',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Material',
      width: '25%',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'Condição',
      width: '25%',
      rowspan: '',
      colspan: '3',
      show: true,
      referent: ['drained_color', 'undrained_color', 'pseudo_static_color'],
      type: 'color'
    },
    {
      label: 'Unidade',
      width: '25%',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['client_unit_name']
    },
    {
      label: 'Estrutura',
      width: '25%',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['structure_name']
    },
    {
      label: 'Revisão',
      width: '80px',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['reviews_counter']
    },
    {
      label: 'Status',
      width: '80px',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['active']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '2',
      colspan: '',
      show: true,
      referent: ['miniDashboard']
    }
  ];

  public tableSubheader: any = [
    {
      label: 'Drenada',
      width: '',
      rowspan: '',
      colspan: '',
      show: true,
      parent: 'Condições',
      child: '',
      type: 'color'
    },
    {
      label: 'Não drenada',
      width: '',
      rowspan: '',
      colspan: '',
      show: true,
      parent: 'Condições',
      child: '',
      type: 'color'
    },
    {
      label: 'Pseudo-estática',
      width: '',
      rowspan: '',
      colspan: '',
      show: true,
      parent: 'Condições',
      child: '',
      type: 'color'
    }
  ];

  public tableData: any = [];
  public selectedColumns = this.tableHeader;

  public staticMaterials: any = [];
  public viewSettings = MultiSelectDefault.View;
  public status: any = Status;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };
  public messagesError: any = null;

  public filterParams: any = {};
  public filterBodyParams: any = {
    client_unit_ids: [],
    structure_ids: []
  };

  public filter: any = {
    Identifier: '',
    SearchIdentifier: '',
    Name: ''
  };

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public modalTitle: string = '';
  public modalMessage: string = '';
  public staticMaterialId: string = '';

  public func = fn;

  public material: any = {
    id: null,
    active: null
  };

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public filterSearch: any = {};

  can(action: string): boolean {
    return !!this.permissaoUsuario?.[action];
  }

  constructor(
    private filterService: FilterService,
    private router: Router,
    private staticMaterialsServiceApi: StaticMaterialsServiceApi,
    private userService: UserService,
    private ngxSpinnerService: NgxSpinnerService
  ) {}

  /**
   * Inicializa o componente ao carregá-lo.
   * Exibe o spinner de carregamento, carrega o perfil do usuário e as permissões associadas.
   * Caso o usuário não tenha permissão para visualizar a lista de clientes, remove o item correspondente dos elementos visíveis.
   */
  ngOnInit(): void {
    this.ngxSpinnerService.show();

    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros após um pequeno delay e dispara a busca de materiais automaticamente.
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
    }, 1000);
  }

  /**
   * Recupera e exibe a lista de materiais estáticos em uma tabela com base nos parâmetros fornecidos.
   * Em caso de erro na busca ou ausência de registros, exibe uma mensagem de retorno.
   * @param {object} params - Parâmetros de busca para a API de materiais.
   */
  getStaticMaterialsList(params) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.staticMaterialsServiceApi.getStaticMaterialsSearch(params).subscribe(
      (resp) => {
        let dados: any = resp;

        if (dados.status == 200) {
          this.tableData = dados.body.data ? dados.body.data : [];
          this.collectionSize = dados.body.total_items_count;
          this.formatData();
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * Realiza a busca de materiais com base nos filtros aplicados, utilizando parâmetros de filtragem hierárquica.
   * Define `filterBodyParams` e `filterParams` com os valores dos filtros selecionados.
   * @param {object} [filterParams] - Parâmetros opcionais para refinar a busca.
   */
  searchMaterial(filterParams?: any) {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterBodyParams.client_unit_ids = [];
    this.filterBodyParams.structure_ids = [];

    this.filterParams = {
      SearchIdentifier: this.filter.SearchIdentifier,
      Identifier: this.filter.Identifier,
      Name: this.filter.Name != '' ? this.filter.Name : '',
      ClientId: filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '',
      ClientUnitId: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '',
      StructureId: filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].id : ''
    };

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());

    this.getStaticMaterialsList(this.filterSearch);
  }

  /**
   * Gerencia os filtros aplicados. Se `$btn` for verdadeiro, executa uma nova busca;
   * caso contrário, recupera os filtros aplicados anteriormente e refina a busca.
   * @param {boolean} [$btn=false] - Indica se a função foi chamada a partir de um botão de busca.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchMaterial(); // Executa a busca diretamente ao clicar no botão
    } else {
      // Recupera os filtros salvos
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchMaterial(); // Realiza uma nova busca caso não haja filtros
      } else {
        // Aplica os filtros existentes
        this.filterSearch = data.filters;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;

        // Atualiza os valores do formulário com os filtros recuperados
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier !== undefined ? this.filterSearch.SearchIdentifier : '';
        this.filter.Identifier = this.filterSearch.Identifier !== undefined ? this.filterSearch.Identifier : '';
        this.filter.Name = this.filterSearch.Name !== undefined ? this.filterSearch.Name : '';

        // Aplica os filtros de hierarquia caso estejam disponíveis
        this.filterBodyParams.client_unit_ids = [];
        this.filterBodyParams.structure_ids = [];
        this.filterParams = {
          SearchIdentifier: this.filter.SearchIdentifier,
          Identifier: this.filter.Identifier,
          Name: this.filter.Name,
          ClientId: this.filterSearch.ClientId || '',
          ClientUnitId: this.filterSearch.ClientUnitId || '',
          StructureId: this.filterSearch.StructureId || ''
        };

        // Executa a busca com os filtros aplicados
        this.searchMaterial(this.filterSearch);
      }

      // Configura os filtros de hierarquia, se disponíveis
      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
        this.hierarchy.setUnits(data.filtersHierarchy.units);
        this.hierarchy.setStructures(data.filtersHierarchy.structures);
      }
    }
  }

  /**
   * Formata e apresenta os dados dos materiais na tabela, configurando campos específicos
   * como `client_unit_id`, `client_unit_name`, e `structure_name`.
   */
  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      let itemMaterial = item;

      itemMaterial['name'] = item.name;
      itemMaterial['drained_color'] = item.drained_color;
      itemMaterial['undrained_color'] = item.undrained_color;
      itemMaterial['pseudo_static_color'] = item.pseudo_static_color;
      itemMaterial['client_unit_id'] = item.client_unit.id;
      itemMaterial['client_unit_name'] = item.client_unit.name;
      itemMaterial['structure_name'] = item.structure.name;

      return itemMaterial;
    });
  }

  /**
   * Reseta os filtros aplicados ao formulário de pesquisa de clientes e executa uma nova busca padrão.
   * Redefine o formulário e os parâmetros de filtro para os valores iniciais.
   */
  resetFilter() {
    this.hierarchy.resetFilters();

    this.filter = {
      SearchIdentifier: '',
      Identifier: '',
      Name: ''
    };

    this.filterParams = {};
    this.filterSearch = {};
    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.managerFilters();
  }

  /**
   * Executa ações ao clicar em uma linha da tabela, navegando para a rota de edição ou visualização
   * com base no tipo de ação especificada no evento.
   * @param {any} [$event=null] - Evento disparado ao clicar em uma linha, contendo detalhes da ação e do item.
   */
  clickRowEvent($event: any = null) {
    const actionLabels: any = {
      edit: 'editar',
      delete: 'excluir',
      history: 'visualizar'
    };

    const phraseEndings: any = {
      edit: 'este material.',
      delete: 'este material.',
      history: 'o histórico deste material.'
    };

    const isRestrictedAction = ['edit', 'delete', 'history'].includes($event.action);
    const hasPermission = this.can($event.action);

    if (isRestrictedAction && !hasPermission) {
      this.showPermissionAlert(`Você não tem permissão para ${actionLabels[$event.action]} ${phraseEndings[$event.action]}`);
      return;
    }

    switch ($event.action) {
      case 'edit':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/edit']);
        break;
      case 'view':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/view']);
        break;
      case 'history':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/history']);
        break;
      default:
        break;
    }
  }

  /**
   * Exibe uma mensagem de alerta na tela por 5 segundos.
   */
  private showPermissionAlert(text: string): void {
    this.message = {
      text,
      status: true,
      class: 'alert-danger'
    };
    setTimeout(() => (this.message.status = false), 5000);
  }

  /**
   * Alterna a visibilidade das colunas no grid de visualização de clientes.
   * @param {any} $event - Evento disparado ao alterar a visibilidade das colunas.
   * @param {string} type - Tipo de ação (select, deselect, selectAll, deselectAll).
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Alterna o status ativo/inativo de um material.
   * Define o ID e status atual do material antes de ativar ou desativá-lo.
   * @param {any} material - Objeto de material que contém o ID e o status atual.
   */
  toggleStatus(material: any) {
    this.material.id = material.id;
    this.material.active = material.active;
    this.activateMaterial();
  }

  /**
   * Ativa ou desativa o material, atualizando o status na API e exibindo uma mensagem de confirmação.
   * O status da mensagem é resetado automaticamente após um período de tempo.
   */
  activateMaterial() {
    this.staticMaterialsServiceApi.patchStaticMaterials(this.material.id, this.material).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.AlteracaoStatus;
      this.message.status = true;

      setTimeout(() => {
        this.message.status = false;
      }, 4000);
    });
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }

    this.managerFilters();
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  //Retorna para a página inicial da aplicação.
  goBack() {
    this.router.navigate(['/']);
  }
}
