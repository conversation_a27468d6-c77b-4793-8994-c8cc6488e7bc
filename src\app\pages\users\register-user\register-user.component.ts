import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { Status, accessLevel, statusLevel, userLocale, Actions } from 'src/app/constants/app.constants';
import { accessLevel as accessLevelPermission } from 'src/app/constants/permissions.constants';
import { MessageCadastro } from 'src/app/constants/message.constants';

import { IDropdownSettings } from 'ng-multiselect-dropdown';

import { UserService } from 'src/app/services/user.service';
import { UsersService as UsersServiceApi } from 'src/app/services/api/users.service';
import { ClientService } from 'src/app/services/api/client.service';
import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { StructuresService } from 'src/app/services/api/structure.service';

import * as moment from 'moment';

import fn from 'src/app/utils/function.utils';

import { NgxSpinnerService } from 'ngx-spinner';

//Tour guiado
import { CustomTourService } from 'src/app/services/custom-tour.service';
import { TourService } from 'ngx-ui-tour-ng-bootstrap';

@Component({
  selector: 'app-register-user',
  templateUrl: './register-user.component.html',
  styleUrls: ['./register-user.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RegisterUserComponent implements OnInit {
  public formUser: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    first_name: new FormControl(null, [Validators.required]),
    surname: new FormControl(null, [Validators.required]),
    email_address: new FormControl(null, [Validators.required]),
    username: new FormControl(null, [Validators.required]),
    active: new FormControl(true),
    role: new FormControl('', [Validators.required]),
    client: new FormControl([], [Validators.required]),
    clientUnit: new FormControl([], [Validators.required]),
    structure: new FormControl([], [Validators.required]),
    locale: new FormControl('', [Validators.required])
  });

  public user: any = {
    id: null,
    first_name: null,
    surname: null,
    email_address: null,
    username: null,
    active: true,
    role: null,
    clients: [],
    client_units: [],
    structures: [],
    locale: null
  };

  public clientSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  public dropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todas',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  public edit: boolean = false;
  public status: any = Status;

  public clients: any = [];
  public clientUnits: any = [];
  public structures: any = [];
  public locales: any = [];

  public clientDisabled: boolean = true;
  public clientUnitDisabled: boolean = true;
  public structureDisabled: boolean = true;

  public accessLevelEnum = accessLevel;
  public roleUser: any = [];

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = [{ text: '', status: false }];

  public userRequest: any = {};

  public selectedClient: any = [];
  public selectedUnit: any = [];

  public userLevel = fn.objectToArray(accessLevel, {
    id: 'level',
    value: 'value'
  });

  public profile: any = null;
  public permissaoUsuario: any = null;

  public formUsername: string = '';
  public formRole: string = '';

  public messagesError: any = null;

  public managerClient: any = null;

  public conditionLevel: number = 0;
  public view: boolean = false;

  public page: number = 1;
  public pageSize: number = 5;
  public collectionSize: number = 0;

  public formCrtl: boolean = false;

  public tableHeader: any = [
    {
      label: 'Data/Hora',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Usuário',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['username']
    },
    {
      label: 'Ações',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actions']
    }
  ];

  public tableData: any = [];
  public actions: any = Actions;
  public charCounts: { [key: string]: number } = {};

  constructor(
    private activatedRoute: ActivatedRoute,
    private clientService: ClientService,
    private clientUnitService: ClientUnitService,
    private customTourService: CustomTourService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private structuresService: StructuresService,
    public tourService: TourService,
    private userService: UserService,
    private usersServiceApi: UsersServiceApi
  ) {}

  /**
   * Inicializa o componente carregando os dados necessários para a exibição
   * e preparando o formulário de acordo com o perfil do usuário e parâmetros da rota.
   */
  ngOnInit(): void {
    this.roleUser = fn.enumToArray(accessLevel, {
      id: 'level',
      value: 'value'
    });

    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.formCrtl = true;

    this.locales = this.userService.getLocale();

    if (this.activatedRoute.snapshot.params.userId) {
      this.edit = true;
      this.getUser(this.activatedRoute.snapshot.params.userId);
      if (this.activatedRoute.snapshot.url && this.activatedRoute.snapshot.url[1] && this.activatedRoute.snapshot.url[1].path == 'view') {
        this.edit = false;
        this.view = true;
      }
    }
    this.conditionLevel = this.profile.level == 6 ? 7 : this.profile.level;

    if (this.view) {
      this.getHistory(this.activatedRoute.snapshot.params.userId);
    }

    // Inicializa o contador para cada campo do formulário
    for (const key of Object.keys(this.formUser.controls)) {
      const initialValue = this.formUser.get(key)?.value || '';
      this.charCounts[key] = initialValue.length;
    }
  }

  /**
   * Registra um novo usuário com os dados preenchidos no formulário.
   * Exibe mensagens de sucesso ou erro conforme a resposta da API.
   */
  registerUser() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.usersServiceApi.postUser(this.userRequest).subscribe(
      (resp) => {
        const dados: any = resp;

        this.message.text = MessageCadastro.SucessoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';
        this.formUser.reset();

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/users']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status === 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
        this.formCrtl = true;
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Recupera os detalhes de um usuário específico pelo ID para preenchimento do formulário
   * com dados existentes, e gerencia os controles de acordo com o nível de acesso do usuário.
   * @param userId - ID do usuário a ser buscado
   */
  getUser(userId: string) {
    this.usersServiceApi.getUserById(userId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      const level: any = statusLevel;

      this.formUser.get('first_name').setValue(dados.first_name);
      this.formUser.get('surname').setValue(dados.surname);
      this.formUser.get('username').setValue(dados.username);
      this.formUser.get('email_address').setValue(dados.email_address);
      this.formUser.get('active').setValue(dados.active);
      this.formUser.get('role').setValue(level[dados.role].description);
      this.formUser.get('client').setValue(dados.clients);
      this.formUser.get('clientUnit').setValue(dados.client_units);
      this.formUser.get('structure').setValue(dados.structures);
      this.formUser.get('locale').setValue(dados.locale);

      this.formUsername = dados.username;
      this.formRole = level[dados.role].label;

      this.managerAssociation(level[dados.role].description);
      this.managerReturn();
      this.validateAccess(dados.role);
    });
  }

  /**
   * Edita as informações de um usuário existente enviando os dados atualizados para a API.
   * Exibe mensagens de sucesso ou erro conforme a resposta da API.
   */
  editUser() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.usersServiceApi.putUsers(this.userRequest.id, this.userRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/users']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        setTimeout(() => {
          this.messagesError = [];
        }, 4000);

        this.formCrtl = true;
        this.ngxSpinnerService.hide();
      }
    );
  }

  // Recupera a lista de clientes ativos para popular o campo de seleção de clientes no formulário.
  getClient() {
    this.clientService.getClientsList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;

      if (this.clients.length === 1) {
        this.formUser.get('client').setValue(this.clients);
        this.getClientUnits(this.clients[0], 'select');
      }
    });
  }

  //Recupera a lista completa de clientes para o perfil do usuário, independentemente do status ativo.
  getClientProfile() {
    this.clientService.getClientsList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;
    });
  }

  /**
   * Recupera as unidades de um cliente específico e gerencia as seleções do campo de unidades.
   *
   * @param $item - Cliente selecionado
   * @param action - Ação a ser executada (select, deselect, deselectAll)
   */
  getClientUnits($item: any, action: string = '') {
    let search = false;
    let removeClientUnits = false;
    let clientId = 0;

    switch (action) {
      case 'select':
        clientId = this.selectedClient.includes($item.id);
        if (!clientId) {
          search = true;
          this.selectedClient.push($item.id);
        }
        break;

      case 'deselect':
        clientId = this.selectedClient.includes($item.id);
        if (clientId) {
          const index = this.selectedClient.indexOf($item.id);
          if (index >= 0) {
            this.selectedClient.splice(index, 1);
            search = true;
            removeClientUnits = true;
          }
        }
        break;

      case 'deselectAll':
        // this.selectedClient = [];
        this.selectedUnit = [];
        this.clientUnits = [];
        this.structures = [];
        // this.formUser.get('client').setValue([]);
        this.formUser.get('clientUnit').setValue([]);
        this.formUser.get('structure').setValue([]);
        break;
    }

    if (search) {
      const params = {
        clientId: $item.id,
        active: true
      };

      this.clientUnitService.getClientUnitsId(params).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        if (dados) {
          if (!removeClientUnits) {
            this.clientUnits = this.clientUnits.concat(dados);

            if (this.clientUnits.length === 1) {
              this.formUser.get('clientUnit').setValue(this.clientUnits);
              this.getStructures(this.clientUnits[0], 'select');
            }
          } else {
            let clientUnitsItens = [];
            if (this.formUser.get('clientUnit').value != '' || this.formUser.get('clientUnit').value.length > 0) {
              clientUnitsItens = this.formUser.get('clientUnit').value.filter((value: any) => {
                let find = false;
                dados.forEach((element: any) => {
                  if (element.id == value.id && !find) {
                    find = true;
                  }
                });
                if (!find) {
                  return value;
                }
              });
            }
            this.clientUnits = this.clientUnits.filter((value: any) => {
              let find = false;
              dados.forEach((element: any) => {
                if (element.id == value.id && !find) {
                  find = true;
                  this.getStructures(value, 'deselect');
                }
              });
              if (!find) {
                return value;
              }
            });
            this.formUser.get('clientUnit').setValue(clientUnitsItens);
          }
        }
      });
    }
  }

  /**
   * Recupera as estruturas de uma unidade específica e gerencia as seleções do campo de estruturas.
   *
   * @param $item - Unidade selecionada
   * @param action - Ação a ser executada (select, deselect, deselectAll)
   */
  getStructures($item: any, action: string = '') {
    let search = false;
    let removeStructure = false;
    let unitId = 0;

    switch (action) {
      case 'select':
        unitId = this.selectedUnit.includes($item.id);
        if (!unitId) {
          search = true;
          this.selectedUnit.push($item.id);
        }
        break;

      case 'deselect':
        unitId = this.selectedUnit.includes($item.id);
        if (unitId) {
          const index = this.selectedUnit.indexOf($item.id);
          if (index >= 0) {
            this.selectedUnit.splice(index, 1);
            search = true;
            removeStructure = true;
          }
        }
        break;

      case 'deselectAll':
        this.selectedUnit = [];
        this.structures = [];
        this.formUser.get('structure').setValue([]);
        this.formUser.get('clientUnit').setValue([]);
        break;
    }

    if (search) {
      const params = {
        clientUnitId: $item.id,
        active: true
      };

      this.structuresService.getStructureList(params).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        if (dados) {
          if (!removeStructure) {
            this.structures = this.structures.concat(dados);

            if (this.structures.length === 1) {
              this.formUser.get('structure').setValue(this.structures);
            }
          } else {
            let structuresItens = [];
            if (this.formUser.get('structure').value != '' || this.formUser.get('structure').value.length > 0) {
              structuresItens = this.formUser.get('structure').value.filter((value: any) => {
                let find = false;
                dados.forEach((element: any) => {
                  if (element.id == value.id && !find) {
                    find = true;
                  }
                });
                if (!find) {
                  return value;
                }
              });
            }
            this.structures = this.structures.filter((value: any) => {
              let find = false;
              dados.forEach((element: any) => {
                if (element.id == value.id && !find) {
                  find = true;
                }
              });
              if (!find) {
                return value;
              }
            });
            this.formUser.get('structure').setValue(structuresItens);
          }
        }
      });
    }
  }

  /**
   * Gerencia as seleções de unidades de clientes com base nas permissões do perfil.
   * @param $item - Cliente selecionado
   * @param action - Ação a ser executada (select, deselect, selectAll)
   */
  managerClientUnits($item: any, action: string = '') {
    if (this.profile.description === accessLevelPermission.SuperSuporte) {
      if (this.clientSettings.singleSelection && action === 'select') {
        // if (this.managerClient !== null) {
        this.getClientUnits(this.managerClient, 'deselectAll');
        //}
        this.managerClient = $item;
      }

      if (action === 'selectAll') {
        $item.forEach((item: any) => {
          this.getClientUnits(item, 'select');
        });
      } else {
        this.getClientUnits($item, action);
      }
    }
  }

  /**
   * Gerencia as seleções de estruturas de unidades com base nas permissões do perfil.
   * @param $item - Unidade selecionada
   * @param action - Ação a ser executada (select, deselect, selectAll)
   */
  managerStructures($item: any, action: string = '') {
    if (this.profile.description === accessLevelPermission.SuperSuporte) {
      if (action === 'selectAll') {
        $item.forEach((item: any) => {
          this.getStructures(item, 'select');
        });
      } else {
        this.getStructures($item, action);
      }
    }
  }

  // Realiza a validação dos dados antes de enviar para o servidor
  validate() {
    this.formatData();
  }

  // Prepara os dados do usuário para envio, formatando e ajustando os campos de acordo com o perfil e permissões
  formatData() {
    const userStatusLevel: any = fn.objectToArray(statusLevel, {
      id: 'description',
      value: 'level'
    });

    const role = userStatusLevel[this.formUser.get('role').value].level;

    this.userRequest = this.user;
    this.userRequest.username = this.formUser.get('username').value;
    this.userRequest.first_name = this.formUser.get('first_name').value;
    this.userRequest.surname = this.formUser.get('surname').value;
    this.userRequest.email_address = this.formUser.get('email_address').value;
    this.userRequest.role = parseInt(role);
    this.userRequest.clients = this.formUser.get('client').value;
    this.userRequest.client_units = this.formUser.get('clientUnit').value;
    this.userRequest.structures = this.formUser.get('structure').value;
    this.userRequest.locale = parseInt(this.formUser.get('locale').value);

    switch (parseInt(this.userRequest.role)) {
      case 5:
      case 4:
      case 3:
      case 2:
      case 1:
        // this.userRequest.clients = [];
        break;
      case 6:
        this.userRequest.clients = [];
        this.userRequest.client_units = [];
        this.userRequest.structures = [];
        break;
    }

    this.userRequest.clients = fn.removeIndexObject(this.userRequest.clients, 'name');
    this.userRequest.client_units = fn.removeIndexObject(this.userRequest.client_units, 'name');
    this.userRequest.structures = fn.removeIndexObject(this.userRequest.structures, 'name');

    if (!this.edit) {
      delete this.userRequest.id;
      delete this.userRequest.active;
      this.registerUser();
    } else {
      this.userRequest.id = this.activatedRoute.snapshot.params.userId;
      this.editUser();
    }
  }

  /**
   * Configura as opções de associação do cliente, unidade e estrutura com base no perfil do usuário.
   *
   * @param role - Papel do usuário
   * @param form - Indica se a ação ocorre ao carregar o formulário
   */
  managerAssociation(role: any, form: boolean = false) {
    if (form) {
      this.formUser.get('client').setValue([]);
      this.formUser.get('clientUnit').setValue([]);
      this.formUser.get('structure').setValue([]);

      this.clients = [];
      this.clientUnits = [];
      this.structures = [];
    }

    if (role != '' && role != null) {
      this.clientDisabled = false;
      this.clientUnitDisabled = false;
      this.structureDisabled = false;
    } else {
      this.clientDisabled = true;
      this.clientUnitDisabled = true;
      this.structureDisabled = true;
    }

    // Controlar o multiselect do Cliente:
    delete this.clientSettings.singleSelection;
    if (role == 'super-support' || role == 'support') {
      this.clientSettings = { singleSelection: false, ...this.clientSettings };
    } else {
      this.clientSettings = { singleSelection: true, ...this.clientSettings };
    }

    // Controlar exibicao do campo Associacao de Usuario
    if (role == 'super-support') {
      this.formUser.controls['client'].setErrors(null);
      this.formUser.controls['client'].clearValidators();

      this.formUser.controls['clientUnit'].setErrors(null);
      this.formUser.controls['clientUnit'].clearValidators();

      this.formUser.controls['structure'].setErrors(null);
      this.formUser.controls['structure'].clearValidators();
    } else {
      this.formUser.get('client').setValidators([Validators.required]);
      this.formUser.get('clientUnit').setValidators([Validators.required]);
      this.formUser.get('structure').setValidators([Validators.required]);
    }
    if (this.profile.description === accessLevelPermission.SuperSuporte) {
      this.getClient();
    } else {
      this.getUserById(this.profile.id);
    }
  }

  /**
   * Recupera um usuário pelo ID e popula as listas de clientes, unidades e estruturas associadas.
   * @param userId - ID do usuário a ser buscado
   */
  getUserById(userId: string) {
    this.usersServiceApi.getUserById(userId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.clients = dados.clients ? dados.clients : [];
      this.clientUnits = dados.client_units ? dados.client_units : [];
      this.structures = dados.structures ? dados.structures : [];
    });
  }

  /**
   * Recupera o histórico de ações de um usuário específico e formata os dados para exibição na tabela.
   * @param userId - ID do usuário a ser buscado
   */
  getHistory(userId: string) {
    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    this.usersServiceApi.getHistory(userId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados) {
        this.tableData = dados ? dados.data : [];
        this.collectionSize = dados.total_items_count;
        this.formatDataHistory();
      } else {
        this.tableData = [];
        this.collectionSize = 0;
        this.messageReturn.text = 'Não há registros de histórico.';
        this.messageReturn.status = true;
        this.message.class = 'alert-warning';
      }
    });
  }

  // Gerencia a seleção de unidades e estruturas ao retornar ao formulário, selecionando automaticamente as opções previamente escolhidas
  managerReturn() {
    this.formUser.get('client').value.forEach((item: any) => {
      this.getClientUnits(item, 'select');
    });
    this.formUser.get('clientUnit').value.forEach((item: any) => {
      this.getStructures(item, 'select');
    });
  }

  /**
   * Valida o nível de acesso do usuário e ajusta o estado do formulário conforme o perfil e a visualização
   * @param role - Nível de acesso do usuário
   * @returns Condição de acesso para visualização do formulário
   */
  validateAccess(role: number): any {
    if (this.view) {
      this.conditionLevel++;
      this.formUser.disable();
      this.clientDisabled = true;
      this.clientUnitDisabled = true;
      this.structureDisabled = true;
    }
  }

  // Formata os dados do histórico para exibição, incluindo data, usuário e ações executadas
  formatDataHistory() {
    this.actions = fn.objectToArray(this.actions, { id: 'id', value: 'value' }, false);
    this.tableData = this.tableData.map((item: any) => {
      let itemData = {
        created_date: moment(item.created_date).format('DD/MM/YYYY HH:mm:ss'),
        username: item.modified_by.username,
        actions: this.actions[item.action].value
      };
      itemData.actions += !fn.isEmpty(item.properties) ? ' ' + item.properties : '';

      return itemData;
    });
  }

  //Inicia o guia de tour para o usuário com base no arquivo JSON de configuração
  loadTourGuide() {
    this.customTourService.startTour(this.tourService, 'assets/tour-guide/register-user.tourguide.json');
  }

  // Atualiza o contador do campo específico
  onValueChange(event: any, field: string): void {
    this.charCounts[field] = event.target.value.length;
  }

  //Redireciona o usuário de volta para a página de lista de usuários
  goBack() {
    this.router.navigate(['/users']);
  }
}
