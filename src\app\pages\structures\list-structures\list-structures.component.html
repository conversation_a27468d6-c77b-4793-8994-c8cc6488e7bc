<div class="list-content">
  <!-- Cadastrar estrutura -->
  <div class="button-client">
    <app-button
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Cadastrar estrutura'"
      [routerLink]="['create']"
      *ngIf="permissaoUsuario?.create"
    ></app-button>
  </div>
  <!-- Cadastrar estrutura -->

  <div class="row g-3 mt-1">
    <!-- ID -->
    <div class="col-md-3">
      <label class="form-label">ID</label>
      <input
        [(ngModel)]="filter.SearchIdentifier"
        type="number"
        step="1"
        min="1"
        class="form-control"
        autocomplete="off"
        placeholder="ID Estrutura"
        (keypress)="
          func.controlNumber(
            $event,
            filter.SearchIdentifier,
            'positive',
            'ngModel'
          )
        "
        (keyup)="
          func.controlNumber($event, filter.SearchIdentifier, null, 'ngModel')
        "
      />
    </div>

    <!-- Selects Cliente, Unidade e Estrutura -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-9"
    ></app-hierarchy>

    <!-- Status -->
    <div class="col-md-3">
      <label class="form-label">Status</label>
      <select class="form-select" [(ngModel)]="filter.Active">
        <option value="">Selecione...</option>
        <option *ngFor="let item of status" [ngValue]="item.value">
          {{ item.status }}
        </option>
      </select>
    </div>
    <!-- Visualização -->
    <div class="col-md-3">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>

    <!-- Botões -->
    <div class="col-md-6 d-flex align-items-end justify-content-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        class="me-1"
        (click)="managerFilters(true)"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>
    <!-- Botões -->

    <!-- Alertas -->
    <div class="alert alert-warning" role="alert" *ngIf="messageReturn.status">
      {{ messageReturn.text }}
    </div>

    <div
      class="alert alert-success"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
    >
      {{ message.text }}
    </div>
    <!-- Alertas -->

    <!-- Tabela -->
    <app-table
      *ngIf="tableData.length > 0"
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeader"
      [tableData]="tableData"
      (sendToggleStatus)="toggleStatus($event)"
      (sendClickRowEvent)="clickRowEvent($event)"
      [permissaoUsuario]="permissaoUsuario"
      [menuMiniDashboard]="'miniDashboardStructure'"
    >
    </app-table>

    <!-- Paginação -->
    <app-paginator
      *ngIf="tableData.length > 0"
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event)"
      [enableItemPerPage]="true"
    ></app-paginator>

    <!-- Botão Voltar -->
    <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela inicial'"
        [click]="goBack.bind(this)"
      ></app-button>
    </div>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
