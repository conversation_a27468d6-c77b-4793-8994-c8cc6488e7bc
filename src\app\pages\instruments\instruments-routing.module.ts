import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ConsultSectionsComponent } from './consult-sections/consult-sections.component';
import { GroupInstrumentsComponent } from './group-instruments/group-instruments.component';
import { HistoryTabsComponent } from './history-tabs/history-tabs.component';
import { ImagesInstrumentComponent } from './images-instrument/images-instrument.component';
import { ListInstrumentsComponent } from './list-instruments/list-instruments.component';
import { RegisterInstrumentComponent } from './register-instrument/register-instrument.component';
import { ViewMapComponent } from './view-map/view-map.component';

//Grafico
import { ChartInstrumentsComponent } from './chart-instruments/chart-instruments.component';

import { AppGuard } from '../../guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListInstrumentsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastrarInstrumento,
    component: RegisterInstrumentComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarInstrumento,
    component: RegisterInstrumentComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarInstrumento,
    component: RegisterInstrumentComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EdicaoInstrumentoMassa,
    component: RegisterInstrumentComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastroInstrumentoPlanilha,
    component: RegisterInstrumentComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.GruposInstrumentos,
    component: GroupInstrumentsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.HistoricoInstrumentacao,
    component: HistoryTabsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.ConsultarSecoes,
    component: ConsultSectionsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.GraficoInstrumento,
    component: ChartInstrumentsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.MapaInstrumentacao,
    component: ViewMapComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.Mapa,
    component: ViewMapComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.ImagensInstrumentacao,
    component: ImagesInstrumentComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class InstrumentsRoutingModule {}
