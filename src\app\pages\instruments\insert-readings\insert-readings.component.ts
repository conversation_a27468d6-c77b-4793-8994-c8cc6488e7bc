import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MultiSelectDefault } from 'src/app/constants/app.constants';

import { UserService } from 'src/app/services/user.service';

import { ClientService } from 'src/app/services/api/client.service';
import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { StructuresService } from 'src/app/services/api/structure.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-insert-readings',
  templateUrl: './insert-readings.component.html',
  styleUrls: ['./insert-readings.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class InsertReadingsComponent implements OnInit {
  public formInsertReadings: FormGroup = new FormGroup({
    client: new FormControl([], [Validators.required]),
    client_unit: new FormControl([], [Validators.required]),
    structure: new FormControl([], [Validators.required])
  });

  public clientSettings = MultiSelectDefault.Single;
  public structureSettings = MultiSelectDefault.Single;
  public unitSettings = MultiSelectDefault.Single;

  public clients: any = [];
  public structures: any = [];
  public units: any = [];

  public message: any = [{ text: '', status: false }];
  public messageReturn: any = [{ text: '', status: false }];
  public messagesError: any = null;

  public profile: any = null;
  public permissaoUsuario: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public func = fn;

  public tableData: any = [];

  public tableHeader: any = [
    {
      label: 'Instrumento',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    },
    {
      label: 'Célula de pressão',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    },
    {
      label: 'Data',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    },
    {
      label: 'Horário',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    },
    {
      label: 'Cota NA',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    },
    {
      label: 'Profundidade',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    },
    {
      label: 'Pressão',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    },
    {
      label: 'Seco',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    }
  ];

  constructor(
    private router: Router,
    private clientService: ClientService,
    private clientUnitService: ClientUnitService,
    private structuresService: StructuresService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;

    this.getClients();
  }

  // Select de clientes
  getClients() {
    this.clients = [];
    this.units = [];
    this.structures = [];

    this.formInsertReadings.get('client').setValue('');
    this.formInsertReadings.get('client_unit').setValue('');
    this.formInsertReadings.get('structure').setValue('');

    this.clientService.getClientsList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;
    });
  }

  // Select de unidades
  getUnits(client, action: string = 'select') {
    this.units = [];
    this.structures = [];

    this.formInsertReadings.get('client_unit').setValue('');
    this.formInsertReadings.get('structure').setValue('');

    if (action === 'select') {
      this.clientUnitService.getClientUnitsId({ clientId: client.id }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.units = dados;
      });
    }
  }

  //Select de estruturas
  getStructures(clientUnit, action: string = 'select') {
    this.structures = [];

    this.formInsertReadings.get('structure').setValue('');

    if (action === 'select') {
      this.structuresService.getStructureList({ clientUnitId: clientUnit.id }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.structures = dados;
      });
    }
  }

  // Metodo que recebe a pagina selecionada
  loadPage(selectPage: number) {
    this.page = selectPage;
  }

  goBack() {
    this.router.navigate(['/']);
  }
}
