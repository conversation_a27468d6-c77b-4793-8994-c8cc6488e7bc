<form [formGroup]="formStructureStability">
  <div class="row">
    <!-- Evaluate Condition -->
    <div class="col-md-6">
      <label class="form-label">Condições</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="dropdownSettings"
        [data]="conditions"
        formControlName="should_evaluate"
        [required]="true"
        (click)="formStructureStability.get('should_evaluate').markAsTouched()"
        [disabled]="view"
      >
      </ng-multiselect-dropdown>
      <small
        class="form-text text-danger"
        *ngIf="
          formStructureStability.get('should_evaluate').value.length == 0 &&
          formStructureStability.get('should_evaluate').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Gravity -->
    <div class="col-md-2">
      <label class="form-label">Gravidade (m/s²)</label>
      <input
        type="number"
        formControlName="gravity"
        min="9.00"
        max="10.00"
        step="0.01"
        (keypress)="
          func.controlNumber(
            $event,
            formStructureStability.get('gravity'),
            'positiveDecimalLimit'
          )
        "
        (keyup)="
          func.controlNumber($event, formStructureStability.get('gravity'))
        "
        maxlength="5"
        class="form-control"
        [required]="true"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formStructureStability.get('gravity').valid &&
          formStructureStability.get('gravity').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Sismo h -->
    <div class="col-md-2">
      <label class="form-label">Sismo horizontal</label>
      <input
        type="number"
        formControlName="seismic_coefficient_horizontal"
        min="-9999999999999"
        max="9999999999999"
        step="1"
        (keypress)="func.controlNumber($event, null, 'notE')"
        (keyup)="
          func.controlNumber(
            $event,
            formStructureStability.get('seismic_coefficient_horizontal')
          )
        "
        maxlength="9999999999999"
        class="form-control"
        [required]="true"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formStructureStability.get('seismic_coefficient_horizontal').valid &&
          formStructureStability.get('seismic_coefficient_horizontal').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Sismo v -->
    <div class="col-md-2">
      <label class="form-label">Sismo vertical</label>
      <input
        type="number"
        formControlName="seismic_coefficient_vertical"
        min="-9999999999999"
        max="9999999999999"
        step="1"
        (keypress)="func.controlNumber($event, null, 'notE')"
        (keyup)="
          func.controlNumber(
            $event,
            formStructureStability.get('seismic_coefficient_vertical')
          )
        "
        maxlength="9999999999999"
        class="form-control"
        [required]="true"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formStructureStability.get('seismic_coefficient_vertical').valid &&
          formStructureStability.get('seismic_coefficient_vertical').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <!-- Structure type -->
  <div class="row mt-2">
    <div class="col-md-10">
      <label class="form-label">Tipo de Estrutura</label>
      <select
        class="form-select"
        formControlName="structure_type"
        (change)="resetForm('structureType'); ctrlTypeStructure = false"
      >
        <option value="">Selecione...</option>
        <ng-container *ngFor="let item of structureTypeList">
          <option [value]="item.id">{{ item.name }}</option>
        </ng-container>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formStructureStability.get('structure_type').valid &&
          formStructureStability.get('structure_type').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <div class="col-md-2 mt-4 d-flex align-items-start">
      <!-- Adicionar novo tipo de estrutura -->
      <app-button
        [class]="'btn-logisoil-add'"
        [icon]="'fa fa-thin fa-plus'"
        class="me-1"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        title="Novo tipo de estrutura"
        (click)="resetForm('structureType', true)"
        *ngIf="
          formStructureStability.get('structure_type').value == '' && !view
        "
        [disabled]="view"
      >
      </app-button>
      <!-- Editar tipo de estrutura -->
      <app-button
        [class]="'btn-logisoil-editItem select'"
        [icon]="'fa fa-thin fa-pencil'"
        class="me-1"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        title="Editar tipo de estrutura"
        (click)="
          ctrlTypeStructure = true;
          getStructureType(formStructureStability.get('structure_type').value)
        "
        *ngIf="
          formStructureStability.get('structure_type').value != '' && !view
        "
        [disabled]="view"
      >
      </app-button>
    </div>
  </div>
  <!-- Structure type -->
</form>

<!-- Alerta -->
<div class="row mt-4">
  <div class="col-md-12">
    <div class="alert alert-success" role="alert" *ngIf="message.status">
      {{ message.text }}
    </div>
  </div>
</div>
<!-- Alerta -->

<!-- Add Structure -->
<form [formGroup]="formStructureType" (ngSubmit)="validate('structureType')">
  <div class="row mt-4" *ngIf="ctrlTypeStructure">
    <!-- Aba Novo Tipo de Estrutura  -->
    <ul class="nav nav-tabs px-2" *ngIf="!view">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page"
          >Novo Tipo de Estrutura</a
        >
      </li>
    </ul>
    <div class="row mt-4">
      <!-- Mensagem de erro -->
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesErrorTypeStructure"
      ></app-alert>
    </div>
    <div class="col-sm-12" *ngIf="!view">
      <div class="row">
        <!-- Name -->
        <div class="col-sm-6">
          <label class="form-label">Nome</label>
          <input
            type="text"
            class="form-control"
            formControlName="name"
            autocomplete="off"
            maxlength="255"
            [required]="true"
          />
          <small
            class="form-text text-danger"
            *ngIf="
              !formStructureType.get('name').valid &&
              formStructureType.get('name').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <!-- Description -->
        <div class="col-sm-6">
          <label class="form-label">Descrição</label>
          <input
            type="text"
            class="form-control"
            formControlName="description"
            maxlength="255"
            autocomplete="off"
          />
        </div>
      </div>
    </div>
    <!-- Activities -->
    <div class="col-sm-12">
      <div class="row">
        <div class="col-sm-6">
          <label class="form-label">Atividades</label>
          <small
            class="form-text text-danger"
            *ngIf="
              !formStructureType.get('activities').valid &&
              formStructureType.get('activities').value == 0
            "
          >
            Selecione ao menos um item.</small
          >
          <div class="accordion accordion-flush" id="accordionActivities">
            <div class="accordion-item">
              <div class="accordion-header" id="flush-headingOne">
                <button
                  class="accordion-button"
                  type="button"
                  data-bs-target="#flush-collapseOne"
                  [ngClass]="activitiesCollapse ? '' : 'collapsed'"
                  (click)="
                    activitiesCollapse = !activitiesCollapse;
                    !activitiesCollapse &&
                    formStructureType.get('activities').value == ''
                      ? formStructureType.get('activities').setValue(0)
                      : null
                  "
                >
                  Selecione...
                </button>
              </div>
              <div
                id="flush-collapseOne"
                class="accordion-collapse collapse"
                data-bs-parent="#accordionActivities"
                [ngClass]="activitiesCollapse ? 'show' : ''"
              >
                <div class="accordion-body mt-1">
                  <app-sortable-list
                    [dataList]="activitiesList"
                    [checkbox]="true"
                    [orderField]="'index'"
                    [textField]="'name'"
                    [selected]="activitiesNumberSelected"
                    [filter]="true"
                    (sendNumberSelected)="
                      getNumberSelected(
                        $event,
                        formStructureType.get('activities')
                      )
                    "
                    [dragDrop]="view"
                  ></app-sortable-list>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="buttons col-sm-4" *ngIf="!view">
          <app-button
            [class]="'btn-logisoil-red'"
            [icon]="'fa fa-thin fa-xmark'"
            [label]="'Cancelar'"
            [type]="false"
            class="me-1"
            (click)="ctrlTypeStructure = false; resetForm('structureType')"
          >
          </app-button>
          <app-button
            [class]="'btn-logisoil-green'"
            [icon]="'fa fa-thin fa-floppy-disk'"
            [label]="'Salvar'"
            [type]="false"
            [disabled]="!formStructureType.valid"
          >
          </app-button>
        </div>
      </div>
    </div>
  </div>
  <!-- Add Structure -->
</form>
