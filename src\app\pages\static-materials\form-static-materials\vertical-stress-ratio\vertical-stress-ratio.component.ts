import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-vertical-stress-ratio',
  templateUrl: './vertical-stress-ratio.component.html',
  styleUrls: ['./vertical-stress-ratio.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class VerticalStressRatioComponent implements OnInit, OnChanges {
  @Input() public data: any = null;
  @Input() public view: boolean = false;

  public formVerticalStressRatio: FormGroup = new FormGroup({
    resistance_ratio: new FormControl('', [Validators.required]),
    minimum_shear_strength: new FormControl('', [Validators.required]),
    maximum_shear_strength: new FormControl({ value: null, disabled: true }),
    is_maximum_shear_strength: new FormControl(false),
    tensile_strength: new FormControl({ value: null, disabled: true }),
    is_tensile_strength: new FormControl(false)
  });

  public func = fn;

  constructor(public formService: FormService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  splitData($dados) {
    this.formVerticalStressRatio.controls['resistance_ratio'].setValue($dados.resistance_ratio);

    this.formVerticalStressRatio.controls['minimum_shear_strength'].setValue($dados.minimum_shear_strength);

    if ($dados.maximum_shear_strength != null) {
      this.formVerticalStressRatio.controls['is_maximum_shear_strength'].setValue(true);
      this.formVerticalStressRatio.controls['maximum_shear_strength'].setValue($dados.maximum_shear_strength);
      this.formService.checkboxControlValidate(this.formVerticalStressRatio, 'maximum_shear_strength');
    }

    if ($dados.tensile_strength != null) {
      this.formVerticalStressRatio.controls['is_tensile_strength'].setValue(true);
      this.formVerticalStressRatio.controls['tensile_strength'].setValue($dados.tensile_strength);
      this.formService.checkboxControlValidate(this.formVerticalStressRatio, 'tensile_strength');
    }

    if (this.view) {
      this.formVerticalStressRatio.disable();
    }
  }

  validate() {
    let formValid = this.formService.validateForm(this.formVerticalStressRatio);

    if (!formValid) {
      this.formVerticalStressRatio.markAllAsTouched();
    }

    return formValid;
  }
}
