import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';
import { ReadingService } from 'src/app/services/reading.service';

import { fieldsReading } from 'src/app/constants/readings.constants';
import { Datum } from 'src/app/constants/app.constants';

import fn from 'src/app/utils/function.utils';
import Decimal from 'decimal.js';

@Component({
  selector: 'app-ms-pr',
  templateUrl: './ms-pr.component.html',
  styleUrls: ['./ms-pr.component.scss']
})
export class MsPrComponent implements OnInit, OnChanges {
  @Input() public instrumentsList: any = [];
  @Input() public index: number = null;
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public units: any = null;
  @Input() public typeInstrument: any = null;
  @Input() public datetime: any = null;
  @Input() public references: any = null;

  @Output() public setInstrument = new EventEmitter();
  @Output() public executeCalc = new EventEmitter();

  public formReading: FormGroup = new FormGroup({
    instrument: new FormControl('', [Validators.required]),
    date: new FormControl({ value: '', disabled: true }, [Validators.required]),
    datum: new FormControl('', [Validators.required]),
    east_coordinate: new FormControl('', [Validators.required]),
    north_coordinate: new FormControl('', [Validators.required]),
    quota: new FormControl('', [Validators.required]),
    east_displacement: new FormControl({ value: '', disabled: true }),
    north_displacement: new FormControl({ value: '', disabled: true }),
    z_displacement: new FormControl({ value: '', disabled: true }),
    total_planimetric_displacement: new FormControl({ value: '', disabled: true }),
    a_displacement: new FormControl({ value: '', disabled: true }),
    b_displacement: new FormControl({ value: '', disabled: true }),
    //Para calcular
    coordinate_setting: new FormControl({ value: '', disabled: true }),
    azimuth: new FormControl({ value: '', disabled: true }),
    //Para edicao
    id: new FormControl({ value: '', disabled: true })
  });

  public controls: any = null;

  public fieldsReading = fieldsReading;

  public datum: any = Datum;

  public func = fn;

  constructor(private formService: FormService, private readingService: ReadingService) { }

  //Inicializa o componente e obtém os controles do formulário.
  ngOnInit(): void {
    this.controls = this.formReading.controls;
  }

  /**
   * Monitora as mudanças nas propriedades vinculadas e atualiza os controles do formulário conforme necessário.
   * @param {SimpleChanges} changes - As alterações detectadas nas propriedades vinculadas.
   */
  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReading.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }

    if (changes.units && changes.units.previousValue != undefined && !(changes.units.previousValue === changes.units.currentValue)) {
      this.recalculate(changes.units.previousValue, changes.units.currentValue);
    }

    if (changes.datetime && changes.datetime.currentValue != null) {
      this.controls['date'].setValue(this.datetime);
    }
  }

  /**
   * Emite o evento de mudança de instrumento.
   * @param {any} instrument - O instrumento selecionado.
   */
  changeInstrument(instrument) {
    this.setInstrument.emit(instrument);
  }

  /**
   * Popula os controles do formulário com os dados fornecidos e ajusta o estado dos campos com base no modo de visualização ou edição.
   * @param {any} $dados - Os dados a serem exibidos no formulário.
   */
  splitData($dados) {
    if (!this.edit && !this.view && !this.spreadsheet) {
      this.controls['instrument'].enable();
    } else {
      this.controls['instrument'].disable();
    }

    this.formService.toggleFormList(this.formReading, this.fieldsReading[this.typeInstrument.id]);

    this.controls['instrument'].setValue($dados.instrument.id);
    this.controls['coordinate_setting'].setValue($dados.coordinate_setting);
    this.controls['azimuth'].setValue($dados.azimuth);

    if ($dados.edit) {
      this.controls['id'].setValue($dados.edit.id);

      let date = $dados.edit.date.split('.');
      this.controls['date'].setValue(date[0]);

      this.controls['datum'].setValue($dados.edit.datum);
      this.controls['east_coordinate'].setValue($dados.edit.east_coordinate);
      this.controls['north_coordinate'].setValue($dados.edit.north_coordinate);
      this.controls['quota'].setValue($dados.edit.quota);
      this.controls['east_displacement'].setValue($dados.edit.east_displacement);
      this.controls['north_displacement'].setValue($dados.edit.north_displacement);
      this.controls['z_displacement'].setValue($dados.edit.z_displacement);
      this.controls['total_planimetric_displacement'].setValue($dados.edit.total_planimetric_displacement);
      this.controls['a_displacement'].setValue($dados.edit.a_displacement);
      this.controls['b_displacement'].setValue($dados.edit.b_displacement);
    }

    if (this.view) {
      this.formReading.disable();
    }
  }

  /**
   * Calcula o deslocamento com base nas coordenadas fornecidas.
   * @param {string} [option=''] - O tipo de cálculo a ser realizado ('east', 'north', 'z', 'planimetric', 'a', 'b').
   */
  calcDisplacement(option: string = '') {
    if (option == 'east' || option == 'north') {
      let coordinate = this.controls[option + '_coordinate'].value;

      if (!fn.isEmpty(coordinate)) {
        //Northing Easting
        let coordinateDecimal: any = new Decimal(coordinate);
        let coordinateReferenceDecimal: any;
        if (this.references && this.references.values && this.references.values.length > 0) {
          coordinateReferenceDecimal = new Decimal(this.references.values[0][option + '_coordinate']);
          //Deslocamento E e N
          let displacementDecimal: any = coordinateDecimal.sub(coordinateReferenceDecimal);

          displacementDecimal = fn.convertLengthDecimal(displacementDecimal, 'm', 'mm');
          this.controls[option + '_displacement'].setValue(displacementDecimal);
        } else {
          this.controls[option + '_displacement'].setValue(0);
        }
      }
      this.calcDisplacement('planimetric');
      this.calcDisplacement('a');
      this.calcDisplacement('b');
    } else if (option == 'z') {
      //Deslocamento Z
      let quota = this.controls['quota'].value;
      if (!fn.isEmpty(quota)) {
        let quotaDecimal: any = fn.convertLengthDecimal(quota, this.units[0], 'm');

        let quotaReferenceDecimal: any;
        if (this.references && this.references.values && this.references.values.length > 0) {
          quotaReferenceDecimal = new Decimal(this.references.values[0].quota);

          let displacementDecimal: any = quotaDecimal.sub(quotaReferenceDecimal).mul(1000);
          this.controls[option + '_displacement'].setValue(displacementDecimal);
        } else {
          this.controls[option + '_displacement'].setValue(0);
        }
      }
    } else if (option == 'planimetric') {
      //Deslocamento planimetrico total
      let north_displacement = this.controls['north_displacement'].value;
      let east_displacement = this.controls['east_displacement'].value;

      if (!fn.isEmpty(north_displacement) && !fn.isEmpty(east_displacement)) {
        let north_displacementDecimal: any = new Decimal(north_displacement).pow(2);
        let east_displacementDecimal: any = new Decimal(east_displacement).pow(2);

        let total_planimetric_displacementDecimal = Decimal.add(north_displacementDecimal, east_displacementDecimal).pow(0.5);
        this.controls['total_planimetric_displacement'].setValue(total_planimetric_displacementDecimal);
      }
    } else if (option == 'a') {
      //Deslocamento A
      let north_displacement = this.controls['north_displacement'].value;
      let east_displacement = this.controls['east_displacement'].value;

      let azimuth = this.controls['azimuth'].value;
      if (!fn.isEmpty(north_displacement) && !fn.isEmpty(east_displacement) && !fn.isEmpty(azimuth)) {
        let azimutDecimal: any = new Decimal(azimuth);
        azimutDecimal = azimutDecimal.mul(new Decimal(Math.PI));
        azimutDecimal = azimutDecimal.div(new Decimal(180));

        let north_displacementDecimal: any = new Decimal(north_displacement);
        north_displacementDecimal = Decimal.cos(azimutDecimal).mul(north_displacementDecimal);

        let east_displacementDecimal: any = new Decimal(east_displacement);
        east_displacementDecimal = Decimal.sin(azimutDecimal).mul(east_displacementDecimal);

        let a_displacementDecimal: any = Decimal.add(north_displacementDecimal, east_displacementDecimal);
        this.controls['a_displacement'].setValue(a_displacementDecimal);
      } else {
        this.controls['a_displacement'].setValue('-');
      }
    } else if (option == 'b') {
      //Deslocamento B
      let north_displacement = this.controls['north_displacement'].value;
      let east_displacement = this.controls['east_displacement'].value;

      let azimuth = this.controls['azimuth'].value;
      if (!fn.isEmpty(north_displacement) && !fn.isEmpty(east_displacement) && !fn.isEmpty(azimuth)) {
        let azimutDecimal: any = new Decimal(azimuth);
        azimutDecimal = azimutDecimal.mul(new Decimal(Math.PI));
        azimutDecimal = azimutDecimal.div(new Decimal(180));

        let north_displacementDecimal: any = new Decimal(north_displacement);

        north_displacementDecimal = Decimal.sin(azimutDecimal).mul(north_displacementDecimal).mul(-1);

        let east_displacementDecimal: any = new Decimal(east_displacement);
        east_displacementDecimal = Decimal.cos(azimutDecimal).mul(east_displacementDecimal);

        let b_displacementDecimal: any = Decimal.add(north_displacementDecimal, east_displacementDecimal);
        this.controls['b_displacement'].setValue(b_displacementDecimal);
      } else {
        this.controls['b_displacement'].setValue('-');
      }
    }
  }

  /**
   * Recalcula os valores do formulário ao alterar a unidade de medida.
   * @param {any} previousUnit - A unidade de medida anterior.
   * @param {any} currentUnit - A nova unidade de medida.
   */
  recalculate(previousUnit, currentUnit) {
    if (this.controls['quota'].value != '' && previousUnit[0] != currentUnit[0]) {
      let quota = this.controls['quota'].value;
      quota = fn.convertLengthDecimal(quota, previousUnit[0], currentUnit[0]);
      this.controls['quota'].setValue(quota);
      this.calcDisplacement('z');
    }
  }

  //Realiza o cálculo de deslocamento após o carregamento de uma planilha.
  calcAfterLoadSpreadsheet() {
    this.calcDisplacement('east');
    this.calcDisplacement('north');
    this.calcDisplacement('z');
  }
}
