import { Component, OnInit, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

import { MessageCadastro } from 'src/app/constants/message.constants';

import { FormService } from 'src/app/services/form.service';
import { SectionsService } from 'src/app/services/api/section.service';
import { StructuresService } from 'src/app/services/api/structure.service';
import { StructureTypeService } from 'src/app/services/api/structureType.service';
import { UserService } from 'src/app/services/user.service';

//Componentes filhos
import { GeneralTabComponent } from '../tabs/general-tab/general-tab.component';
import { ReviewTabComponent } from '../tabs/review-tab/review-tab.component';

import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-register-section',
  templateUrl: './register-section.component.html',
  styleUrls: ['./register-section.component.scss']
})
export class RegisterSectionComponent implements OnInit {
  @ViewChild(GeneralTabComponent) generalTab: GeneralTabComponent;
  @ViewChild(ReviewTabComponent) reviewTab: ReviewTabComponent;

  public generalTabData: any = {};
  public reviewTabData: any = {};

  public generalTabConfig: any = { styleColor: false, active: true };
  public reviewTabConfig: any = { styleColor: false, active: false };

  public edit: boolean = false;
  public view: boolean = false;

  public profile: any = null;
  public permissaoUsuario: any = null;

  public sectionRequest: any = {};
  public sectionId: any = null;

  public crtlSaveSection: string = '';

  public messageSection: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public formCrtl: boolean = false;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private formService: FormService,
    private sectionsService: SectionsService,
    private structuresService: StructuresService,
    private structureTypeService: StructureTypeService,
    private userService: UserService,
    private ngxSpinnerService: NgxSpinnerService
  ) {}

  /**
   * Método de inicialização do componente.
   * Carrega o perfil do usuário, permissões e configura o formulário.
   * Se uma seção for encontrada na rota, carrega os dados da seção para edição.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.formCrtl = true;

    if (this.activatedRoute.snapshot.params.sectionId) {
      this.edit = true;
      this.sectionId = this.activatedRoute.snapshot.params.sectionId;
      this.getSection(this.activatedRoute.snapshot.params.sectionId);
      if (this.activatedRoute.snapshot.url && this.activatedRoute.snapshot.url[1] && this.activatedRoute.snapshot.url[1].path == 'view') {
        this.edit = false;
        this.view = true;
      }
    }
  }

  /**
   * Método para registrar uma nova seção.
   * Exibe um spinner enquanto o registro é realizado e lida com mensagens de sucesso ou erro.
   */
  registerSection() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.sectionsService.postSection(this.sectionRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageSection.text = MessageCadastro.SucessoCadastro;
        this.messageSection.status = true;

        setTimeout(() => {
          this.messageSection.status = false;
          this.router.navigate(['/sections']);
        }, 4000);

        this.controlForm('create');
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
          this.formCrtl = true;
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  /**
   * Método para buscar os dados de uma seção específica pelo seu ID.
   * Carrega os dados da seção e os distribui entre as abas do formulário.
   * @param {string} sectionId - ID da seção a ser buscada.
   */
  getSection(sectionId: string) {
    this.ngxSpinnerService.show();

    this.sectionsService.getSectionById(sectionId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      //mock (não apagar até validação da Walm 23/06/2025)
      //-------------------------------------------------------------
      // dados.reviews.forEach((review) => {
      //   if (Array.isArray(review.construction_stages)) {
      //     review.construction_stages.forEach((stage) => {
      //       stage.length_data = {
      //         dxf_length_in_meters: 30,
      //         coordinates_length_in_meters: 40,
      //         difference_in_meters: 10,
      //         length_is_consistent: false
      //       };
      //     });
      //   }
      // });
      //-------------------------------------------------------------

      this.splitTabDatas(dados);
      if (this.view) {
        this.reviewTab.tableHeader.pop();
        this.reviewTab.tableHeader.pop();

        this.reviewTab.tableHeader.push({
          label: 'Documento DXF',
          width: '160px',
          rowspan: '',
          colspan: '',
          show: true,
          referent: ['drawing_name'],
          type: ['button_item'],
          config: {
            class: 'btn-logisoil-whitegreen',
            icon: 'fa fa-eye',
            type: true,
            option: 'view_dxf',
            // label_column: true,
            title: 'Visualizar DXF'
          }
        });
      }
      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Método para editar uma seção existente.
   * Exibe um spinner durante a atualização e lida com mensagens de sucesso ou erro.
   */
  editSection() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.sectionsService.putSections(this.sectionRequest.id, this.sectionRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageSection.text = MessageCadastro.EdicaoCadastro;
        this.messageSection.status = true;
        this.messageSection.class = 'alert-success';

        setTimeout(() => {
          this.messageSection.status = false;
          this.router.navigate(['/sections']);
        }, 4000);

        this.controlForm('edit');
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
          this.formCrtl = true;
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  /**
   * Método para controlar o comportamento do formulário.
   * Seleciona a aba 'general' por padrão.
   * @param {string} action - Ação atual, pode ser 'create' ou 'edit'.
   */
  controlForm(action: string = 'create') {
    this.selectTab('general');
  }

  /**
   * Método para selecionar uma aba específica no formulário.
   * @param {string} option - Nome da aba a ser selecionada.
   */
  selectTab(option: any = '') {
    switch (option) {
      case 'general':
        this.generalTabConfig.active = true;
        this.reviewTabConfig.active = false;
        this.generalTab.selectTab('map');
        break;
      case 'review':
        this.generalTabConfig.active = false;
        this.reviewTabConfig.active = true;
        break;
      default:
        break;
    }
  }

  /**
   * Método para distribuir os dados recebidos entre as abas do formulário.
   * Configura a aba 'general' e a aba 'review' com os dados recebidos.
   * @param {any} dados - Dados da seção recebidos do servidor.
   */
  splitTabDatas(dados: any) {
    //general-tab//
    this.generalTabData.id = dados.id;
    this.generalTabData.name = dados.name;
    this.generalTabData.client = dados.client;
    this.generalTabData.client_unit = dados.client_unit.id;
    this.generalTabData.structure = dados.structure.id;
    this.generalTabData.is_skew = dados.is_skew;
    this.generalTabData.normal_line_azimuth = dados.normal_line_azimuth;
    this.generalTabData.skew_line_azimuth = dados.skew_line_azimuth;
    this.generalTabData.datum = dados.coordinates.datum;
    this.generalTabData.instruments = dados.instruments;
    this.generalTabData.skew_line_azimuth = dados.skew_line_azimuth;
    this.generalTabData.minimum_drained_depth = dados.minimum_drained_depth;
    this.generalTabData.minimum_undrained_depth = dados.minimum_undrained_depth;
    this.generalTabData.minimum_pseudo_static_depth = dados.minimum_pseudo_static_depth;

    // Montante
    this.generalTabData.upstream_coordinate_format = dados.coordinates.upstream_coordinate_setting.coordinate_format.toString();
    this.generalTabData.upstream_zone_letter = dados.coordinates.upstream_coordinate_setting.coordinate_systems.utm.zone_letter;
    this.generalTabData.upstream_zone_number = dados.coordinates.upstream_coordinate_setting.coordinate_systems.utm.zone_number;
    this.generalTabData.upstream_northing = dados.coordinates.upstream_coordinate_setting.coordinate_systems.utm.northing;
    this.generalTabData.upstream_easting = dados.coordinates.upstream_coordinate_setting.coordinate_systems.utm.easting;
    this.generalTabData.upstream_latitude = dados.coordinates.upstream_coordinate_setting.coordinate_systems.decimal_geodetic.latitude;
    this.generalTabData.upstream_longitude = dados.coordinates.upstream_coordinate_setting.coordinate_systems.decimal_geodetic.longitude;

    // Jusante
    this.generalTabData.downstream_coordinate_format = dados.coordinates.downstream_coordinate_setting.coordinate_format.toString();
    this.generalTabData.downstream_zone_letter = dados.coordinates.downstream_coordinate_setting.coordinate_systems.utm.zone_letter;
    this.generalTabData.downstream_zone_number = dados.coordinates.downstream_coordinate_setting.coordinate_systems.utm.zone_number;
    this.generalTabData.downstream_northing = dados.coordinates.downstream_coordinate_setting.coordinate_systems.utm.northing;
    this.generalTabData.downstream_easting = dados.coordinates.downstream_coordinate_setting.coordinate_systems.utm.easting;
    this.generalTabData.downstream_latitude = dados.coordinates.downstream_coordinate_setting.coordinate_systems.decimal_geodetic.latitude;
    this.generalTabData.downstream_longitude = dados.coordinates.downstream_coordinate_setting.coordinate_systems.decimal_geodetic.longitude;

    // Intermediário
    if (dados.coordinates.midpoint_coordinate_setting?.coordinate_format != null) {
      this.generalTabData.midpoint_coordinate_format =
        dados.coordinates.midpoint_coordinate_setting != null ? dados.coordinates.midpoint_coordinate_setting.coordinate_format.toString() : null;
      this.generalTabData.midpoint_zone_letter =
        !dados.coordinates.midpoint_coordinate_setting || dados.coordinates.midpoint_coordinate_setting.coordinate_systems.utm.zone_letter;
      this.generalTabData.midpoint_zone_number =
        !dados.coordinates.midpoint_coordinate_setting || dados.coordinates.midpoint_coordinate_setting.coordinate_systems.utm.zone_number;
      this.generalTabData.midpoint_northing =
        !dados.coordinates.midpoint_coordinate_setting || dados.coordinates.midpoint_coordinate_setting.coordinate_systems.utm.northing;
      this.generalTabData.midpoint_easting =
        !dados.coordinates.midpoint_coordinate_setting || dados.coordinates.midpoint_coordinate_setting.coordinate_systems.utm.easting;
      this.generalTabData.midpoint_latitude =
        !dados.coordinates.midpoint_coordinate_setting || dados.coordinates.midpoint_coordinate_setting.coordinate_systems.decimal_geodetic.latitude;
      this.generalTabData.midpoint_longitude =
        !dados.coordinates.midpoint_coordinate_setting || dados.coordinates.midpoint_coordinate_setting.coordinate_systems.decimal_geodetic.longitude;
    }

    //Polyline
    this.generalTabData.map_line_setting_color = dados.map_line_setting.color;
    this.generalTabData.map_line_setting_type = dados.map_line_setting.type;
    this.generalTabData.map_line_setting_width = dados.map_line_setting.width;

    this.generalTab.setData(this.generalTabData);
    this.generalTab.displayDXF(dados.reviews);

    //review-tab//
    this.reviewTab.tableData = dados.reviews;
    this.reviewTab.formatData();
    this.getStructureTypeList();
    this.setStructure(dados.structure.id);
  }

  /**
   * Método para juntar os dados das abas do formulário em um único objeto.
   * Envia os dados para criação ou edição da seção.
   * @param {string} action - Ação atual, pode ser 'create' ou 'edit'.
   */
  joinTabDatas(action: string = 'create') {
    this.sectionRequest = {
      id: null,
      name: null,
      client_unit: {
        id: null
      },
      structure: {
        id: null
      },
      is_skew: false,
      normal_line_azimuth: null,
      skew_line_azimuth: null,
      minimum_drained_depth: null,
      minimum_undrained_depth: null,
      minimum_pseudo_static_depth: null,
      coordinates: {
        datum: null,
        upstream_coordinate_setting: {
          coordinate_format: null,
          coordinate_systems: {
            utm: {
              zone_number: null,
              zone_letter: null,
              northing: null,
              easting: null
            },
            decimal_geodetic: {
              latitude: null,
              longitude: null
            }
          }
        },
        downstream_coordinate_setting: {
          coordinate_format: null,
          coordinate_systems: {
            utm: {
              zone_number: null,
              zone_letter: null,
              northing: null,
              easting: null
            },
            decimal_geodetic: {
              latitude: null,
              longitude: null
            }
          }
        },
        midpoint_coordinate_setting: {
          coordinate_format: null,
          coordinate_systems: {
            utm: {
              zone_number: null,
              zone_letter: null,
              northing: null,
              easting: null
            },
            decimal_geodetic: {
              latitude: null,
              longitude: null
            }
          }
        }
      },
      map_line_setting: {
        color: null,
        type: null,
        width: null
      },
      instruments: [
        {
          id: null,
          name: null
        }
      ],
      reviews: []
    };

    //general-tab//
    this.generalTabData = this.generalTab.getData();

    this.sectionRequest.client_unit.id = this.generalTabData.client_unit;
    this.sectionRequest.name = this.generalTabData.name;
    this.sectionRequest.structure.id = this.generalTabData.structure;
    this.sectionRequest.coordinates.datum = this.generalTabData.datum;

    if (this.generalTabData.instruments !== '' && this.generalTabData.instruments.length > 0) {
      this.sectionRequest.instruments = this.generalTabData.instruments;
    } else {
      this.sectionRequest.instruments = [];
    }

    this.sectionRequest.minimum_drained_depth = this.generalTabData.minimum_drained_depth;
    this.sectionRequest.minimum_undrained_depth = this.generalTabData.minimum_undrained_depth;
    this.sectionRequest.minimum_pseudo_static_depth = this.generalTabData.minimum_pseudo_static_depth;

    this.sectionRequest.is_skew = this.generalTabData.is_skew;
    this.sectionRequest.normal_line_azimuth = this.generalTabData.normal_line_azimuth;
    this.sectionRequest.skew_line_azimuth = this.generalTabData.skew_line_azimuth == '' ? null : this.generalTabData.skew_line_azimuth;
    this.sectionRequest.map_line_setting.color = this.generalTabData.map_line_setting_color;
    this.sectionRequest.map_line_setting.type = this.generalTabData.map_line_setting_type;
    this.sectionRequest.map_line_setting.width = this.generalTabData.map_line_setting_width;

    // Jusante
    this.sectionRequest.coordinates.upstream_coordinate_setting.coordinate_format = parseInt(this.generalTabData.upstream_coordinate_format);
    this.sectionRequest.coordinates.upstream_coordinate_setting.coordinate_systems.utm.zone_number = this.generalTabData.upstream_zone_number;
    this.sectionRequest.coordinates.upstream_coordinate_setting.coordinate_systems.utm.zone_letter = this.generalTabData.upstream_zone_letter;
    this.sectionRequest.coordinates.upstream_coordinate_setting.coordinate_systems.utm.northing = this.generalTabData.upstream_northing;
    this.sectionRequest.coordinates.upstream_coordinate_setting.coordinate_systems.utm.easting = this.generalTabData.upstream_easting;
    this.sectionRequest.coordinates.upstream_coordinate_setting.coordinate_systems.decimal_geodetic.latitude = this.generalTabData.upstream_latitude;
    this.sectionRequest.coordinates.upstream_coordinate_setting.coordinate_systems.decimal_geodetic.longitude = this.generalTabData.upstream_longitude;

    // Montante
    this.sectionRequest.coordinates.downstream_coordinate_setting.coordinate_format = parseInt(this.generalTabData.downstream_coordinate_format);
    this.sectionRequest.coordinates.downstream_coordinate_setting.coordinate_systems.utm.zone_number = this.generalTabData.downstream_zone_number;
    this.sectionRequest.coordinates.downstream_coordinate_setting.coordinate_systems.utm.zone_letter = this.generalTabData.downstream_zone_letter;
    this.sectionRequest.coordinates.downstream_coordinate_setting.coordinate_systems.utm.northing = this.generalTabData.downstream_northing;
    this.sectionRequest.coordinates.downstream_coordinate_setting.coordinate_systems.utm.easting = this.generalTabData.downstream_easting;
    this.sectionRequest.coordinates.downstream_coordinate_setting.coordinate_systems.decimal_geodetic.latitude = this.generalTabData.downstream_latitude;
    this.sectionRequest.coordinates.downstream_coordinate_setting.coordinate_systems.decimal_geodetic.longitude = this.generalTabData.downstream_longitude;

    // Intermediário
    if (this.generalTabData.is_skew && this.generalTabData.midpoint_coordinate_format != null && this.generalTabData.midpoint_coordinate_format !== '') {
      this.sectionRequest.coordinates.midpoint_coordinate_setting.coordinate_format = parseInt(this.generalTabData.midpoint_coordinate_format);
      this.sectionRequest.coordinates.midpoint_coordinate_setting.coordinate_systems.utm.zone_number = this.generalTabData.midpoint_zone_number;
      this.sectionRequest.coordinates.midpoint_coordinate_setting.coordinate_systems.utm.zone_letter = this.generalTabData.midpoint_zone_letter;
      this.sectionRequest.coordinates.midpoint_coordinate_setting.coordinate_systems.utm.northing = this.generalTabData.midpoint_northing;
      this.sectionRequest.coordinates.midpoint_coordinate_setting.coordinate_systems.utm.easting = this.generalTabData.midpoint_easting;
      this.sectionRequest.coordinates.midpoint_coordinate_setting.coordinate_systems.decimal_geodetic.latitude = this.generalTabData.midpoint_latitude;
      this.sectionRequest.coordinates.midpoint_coordinate_setting.coordinate_systems.decimal_geodetic.longitude = this.generalTabData.midpoint_longitude;
    } else {
      delete this.sectionRequest.coordinates.midpoint_coordinate_setting; // mais seguro que setar null
    }

    //review-tab//
    this.reviewTabData = this.reviewTab.getData();
    this.sectionRequest.reviews = this.reviewTabData;

    if (action === 'create') {
      delete this.sectionRequest.id;
      this.registerSection();
    } else if (action === 'edit') {
      this.sectionRequest.id = this.activatedRoute.snapshot.params.sectionId;
      this.editSection();
    }
  }

  /**
   * Método para configurar a estrutura no formulário com base em seu ID.
   * @param {any} structureId - ID da estrutura a ser configurada.
   */
  setStructure(structureId: any) {
    this.getStructureById(structureId);
  }

  /**
   * Método para buscar os dados básicos de uma estrutura pelo seu ID.
   * Carrega as configurações de coordenadas e gerencia as condições da estrutura.
   * @param {string} structureId - ID da estrutura a ser buscada.
   */
  getStructureById(structureId: string) {
    if (structureId) {
      this.structuresService.getStructureByIdBasicInfo(structureId).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;

        this.generalTab.formSection.get('datum').setValue(dados.coordinate_setting.datum);
        this.getStructureTypeList();

        this.generalTab.updatePositionMaps(dados.coordinate_setting, dados.name);
        this.generalTab.setCoordinatesByStructure(dados.coordinate_setting);

        let conditions = {
          drained: dados.should_evaluate_drained_condition,
          pseudo_static: dados.should_evaluate_pseudo_static_condition,
          undrained: dados.should_evaluate_undrained_condition
        };

        this.generalTab.managerConditions(conditions);
        this.reviewTab.formReview.get('structure_type').setValue(dados.structure_type.id);
        this.reviewTab.structureTypeDefault = dados.structure_type.id;
      });
    } else {
      this.generalTab.resetCoordinates();
    }
  }

  /**
   * Método para buscar a lista de tipos de estrutura.
   * Atualiza a lista de tipos de estrutura na aba 'review'.
   */
  getStructureTypeList() {
    this.structureTypeService.getStructureTypesList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.reviewTab.structureTypeList = dados;
    });
  }

  /**
   * Método para validar os formulários das abas 'general' e 'review'.
   * Se ambos forem válidos, envia os dados para criação ou edição da seção.
   */
  validate() {
    const action = !this.edit ? 'create' : 'edit';
    const generalTabValid = this.formService.validateForm(this.generalTab.formSection);

    this.generalTabConfig.styleColor = !this.generalTab.formSection.valid;
    this.reviewTabConfig.styleColor = this.reviewTab.tableData.length > 0 ? false : true;

    this.reviewTab.reviewsValidate = this.reviewTabConfig.styleColor;

    if (!this.generalTab.formSection.valid) {
      this.generalTab.formSection.markAllAsTouched();
    }

    if (!this.generalTabConfig.styleColor && !this.reviewTabConfig.styleColor) {
      this.joinTabDatas(action);
    }
  }

  onNext(): void {
    // Verifica qual aba está ativa e navega para a próxima
    if (this.generalTabConfig.active) {
      this.selectTab('review');
      this.crtlSaveSection = 'review';
    }
  }

  onBack(): void {
    // Verifica se a aba Revisão Seção está ativa e retorna para a aba Geral
    if (this.reviewTabConfig.active) {
      this.selectTab('general');
      this.crtlSaveSection = '';
    }
  }
}
