import { AbstractControl } from '@angular/forms';
import { Decimal } from 'decimal.js';
import { formatCPF, isValidCPF } from '@brazilian-utils/brazilian-utils';

/**
 * Remove uma ou mais propriedades especificadas de cada objeto em um array.
 *
 * @param {any} object - O array de objetos.
 * @param {any} index - A(s) propriedade(s) a ser(em) removida(s).
 * @returns {any} - O array de objetos sem as propriedades especificadas.
 */
const removeIndexObject = (object: any, index: any = ''): any => {
  object = object.map((item: any) => {
    if (typeof index === 'string') {
      delete item[index];
    }
    if (typeof index === 'object') {
      index.map((removeIdx: any) => {
        delete item[removeIdx];
      });
    }
    return item;
  });
  return object;
};

/**
 * Converte um enum em um array de objetos, onde cada objeto contém um id e um label baseado no alias fornecido.
 *
 * @param {any} en - O enum a ser convertido.
 * @param {object} alias - O alias para mapear as chaves do enum (id e value).
 * @returns {any} - Um array de objetos com id e label.
 */
const enumToArray = (en: any, alias = { id: 'id', value: 'value' }): any => {
  return Object.entries(en)
    .filter((i) => typeof i[1] == 'object')
    .map((item) => {
      return { id: item[0], label: (item[1] as any)[alias.value] };
    });
};

/**
 * Extrai um índice específico de cada objeto em um array.
 *
 * @param {any} obj - O array de objetos.
 * @param {string} index - O índice a ser extraído.
 * @returns {any} - Um array com os valores extraídos.
 */
const extractIndex = (obj: any, index: string): any => {
  obj = obj.map((item: any) => {
    return item[index];
  });
  return obj;
};

/**
 * Converte um objeto em um array com base em alias fornecido e adiciona labels se especificado.
 * Trata a constante de niveis de acesso e localizacao (editar e visualizar)
 * @param {any} obj - O objeto a ser convertido.
 * @param {object} alias - O alias para mapear as chaves do objeto.
 * @param {boolean} label - Se deve ou não adicionar labels.
 * @returns {any} - Um array de objetos convertidos.
 */
const objectToArray = (obj: any, alias = { id: 'id', value: 'value' }, label = true): any => {
  let array: any = [];
  for (let key in obj) {
    if (obj[key][alias.id] !== undefined) {
      if (label) {
        array[obj[key][alias.id]] = {
          label: obj[key][alias.value],
          ...obj[key]
        };
      } else {
        array[obj[key][alias.id]] = { ...obj[key] };
      }
    }
  }
  return array;
};

/**
 * Verifica se um valor é vazio (NaN, undefined, null, string vazia ou 'null').
 *
 * @param {any} value - O valor a ser verificado.
 * @returns {boolean} - Retorna true se o valor for vazio, caso contrário, false.
 */
const isEmpty = (value: any): boolean => {
  if (Number.isNaN(value) || value === undefined || value === null || value === '' || value === 'null') {
    return true;
  }
  return false;
};

/**
 * Converte um enum em um array completo de objetos, incluindo todas as suas propriedades, opcionalmente adicionando um id.
 *
 * @param {any} en - O enum a ser convertido.
 * @param {boolean} setId - Se deve ou não adicionar um id ao objeto.
 * @returns {any} - Um array de objetos com todas as propriedades do enum.
 */
const enumToArrayComplete = (en: any, setId: boolean = true): any => {
  return Object.entries(en)
    .filter((i) => typeof i[1] == 'object')
    .map((item) => {
      let element = setId ? { id: item[0] } : {};
      const obj: any = item[1];
      for (const key in obj) {
        if (key !== 'id') {
          element[key] = obj[key];
        }
      }
      return element;
    });
};

/**
 * Ordena um array de objetos com base em uma propriedade e critério especificados.
 *
 * @param {any[]} value - O array de objetos a ser ordenado.
 * @param {any} criteria - O critério de ordenação.
 * @returns {any} - O array ordenado.
 */
const sortObject = (value: any[], criteria: any): any => {
  if (!value || !criteria) return value;

  let p: string = criteria.property;

  let sortFn: (a: any, b: any) => any = (a, b) => {
    let value: number = 0;
    if (a[p] === undefined) value = -1;
    else if (b[p] === undefined) value = 1;
    else value = a[p] > b[p] ? 1 : b[p] > a[p] ? -1 : 0;
    return criteria.descending ? value * -1 : value;
  };

  value.sort(sortFn);
  return value;
};

/**
 * Calcula a diferença entre dois arrays de objetos.
 *
 * @param {any} objA - O primeiro array de objetos.
 * @param {any} objB - O segundo array de objetos.
 * @returns {any} - Os objetos que estão em objA, mas não em objB.
 */
const difference = (objA: any, objB: any): any => {
  return objA.filter((object1) => {
    return !objB.some((object2) => {
      return JSON.stringify(object1) === JSON.stringify(object2);
    });
  });
};

/**
 * Calcula a diferença entre dois arrays de objetos com base em um índice específico.
 *
 * @param {any} objA - O primeiro array de objetos.
 * @param {any} objB - O segundo array de objetos.
 * @param {string} index - O índice a ser usado para comparar os objetos.
 * @returns {any} - Os objetos que estão em objA, mas não em objB com base no índice.
 */
const differenceByIndex = (objA: any, objB: any, index: string): any => {
  return objA.filter((object1) => {
    return !objB.some((object2) => {
      return object1[index] === object2[index];
    });
  });
};

/**
 * Filtra um array de objetos com base em uma chave e um valor especificados.
 *
 * @param {any} obj - O array de objetos a ser filtrado.
 * @param {any} key - A chave usada para filtrar.
 * @param {any} value - O valor usado para filtrar.
 * @param {boolean} like - Se deve usar uma correspondência aproximada (like).
 * @returns {any} - Um array de objetos filtrados.
 */
const filterByKeyAndValue = (obj: any, key: any, value: any, like: boolean = false): any => {
  return obj.filter((item) => {
    if (!like) {
      return item[key] == value;
    } else {
      return item[key].toLowerCase().includes(value.toLowerCase());
    }
  });
};

/**
 * Busca o índice de um objeto em um array de objetos com base em uma chave e um valor especificados.
 *
 * @param {any} arr - O array de objetos.
 * @param {any} idx - A chave usada para buscar.
 * @param {any} value - O valor usado para buscar.
 * @param {any} rtnValue - O valor a ser retornado (opcional).
 * @param {boolean} objAll - Se deve retornar todo o objeto correspondente (opcional).
 * @returns {any} - O índice ou valor encontrado, ou o objeto correspondente.
 */
const findIndexInArrayofObject = (arr: any, idx: any, value: any, rtnValue: any = '', objAll: boolean = false): any => {
  if (rtnValue == '') {
    return arr.findIndex((item) => item[idx] === value);
  } else {
    if (arr.findIndex((item) => item[idx] === value) !== -1) {
      if (!objAll) {
        return arr[arr.findIndex((item) => item[idx] === value)][rtnValue];
      } else {
        return arr[arr.findIndex((item) => item[idx] === value)];
      }
    } else {
      return '';
    }
  }
};

/**
 * Remove um item de um array com base em um índice especificado.
 *
 * @param {any} arr - O array do qual o item será removido.
 * @param {any} idx - O índice do item a ser removido.
 * @returns {any} - O array atualizado.
 */
const removeIndexArray = (arr: any, idx: any): any => {
  return arr.splice(idx, 1);
};

/**
 * Remove itens de um array com base em um filtro de índice.
 *
 * @param {any} arr - O array do qual os itens serão removidos.
 * @param {any} idx - O índice usado para filtrar os itens a serem removidos.
 * @returns {any} - O array atualizado.
 */
const removeIndexArrayByFilter = (arr: any, idx: any): any => {
  arr = arr.filter(function (value, index, arr) {
    return idx != index;
  });
  return arr;
};

/**
 * Controla a entrada de números em campos de formulário, incluindo tratamento de decimais, inteiros, valores positivos e negativos.
 *
 * @param {any} $event - O evento de entrada.
 * @param {any} element - O elemento de formulário relacionado.
 * @param {string} options - As opções de controle (e.g., positive, positiveDecimal).
 * @param {string} typeInput - O tipo de entrada (opcional).
 * @returns {any} - O valor formatado ou false para bloquear a entrada.
 */
const controlNumber = ($event: any, element: any = null, options: string = null, typeInput: string = null): any => {
  let value = $event.target.value;
  let type = $event.type;
  let maxLength = parseInt($event.target.maxLength);
  let min = parseInt($event.target.min);
  let max = parseInt($event.target.max);

  let charCode = $event.which ? $event.which : $event.keyCode;
  let decimalPlaces = null;

  if ($event.target.getAttribute('decimalplaces') != null) {
    decimalPlaces = $event.target.getAttribute('decimalplaces');
  } else {
    decimalPlaces = $event.target.step;
  }

  let valueCondition = decimalPlaces.indexOf('.') !== -1 ? parseFloat(value) : parseInt(value);

  if (type == 'keypress') {
    if (options === 'positiveDecimalLimit') {
      let stepLength = decimalPlaces.indexOf('.') !== -1 ? decimalPlaces.split('.')[1].length : 0;
      let valueLength = value.indexOf('.') !== -1 ? value.split('.')[1].length : -1;
      if (valueLength >= stepLength) {
        return false;
      }
      return [43, 45, 46, 69, 101].indexOf(charCode) == -1;
    }

    if (options === 'positiveDecimalDot') {
      let stepLength = decimalPlaces.indexOf('.') !== -1 ? decimalPlaces.split('.')[1].length : 0;
      let valueLength = value.indexOf('.') !== -1 ? value.split('.')[1].length : -1;
      if (valueLength >= stepLength) {
        return false;
      }
      return [43, 45, 69, 101].indexOf(charCode) == -1;
    }

    if (value.length == maxLength) {
      return false;
    }

    if (options === 'positive') {
      return charCode >= 48 && charCode <= 57;
    }

    if (options === 'positiveDecimal') {
      return [43, 45, 46, 69, 101].indexOf(charCode) == -1;
    }

    if (options === 'notE') {
      return [69, 101].indexOf(charCode) == -1;
    }
  } else if (type == 'keyup') {
    if (valueCondition > max && value != '') {
      if (typeInput === null) {
        element.setValue(max);
      } else {
        element = max;
      }
    } else if (valueCondition < min && value != '') {
      if (typeInput === null) {
        element.setValue(min);
      } else {
        element = min;
      }
    }
  } else if (type == 'paste') {
    return false;
  }
};

/**
 * Converte um enum em um array simples de objetos contendo apenas id e valor.
 *
 * @param {any} obj - O enum a ser convertido.
 * @returns {any} - Um array de objetos com id e valor.
 */
const enumToArraySimple = (obj: any): any => {
  let objKeys: any = Object.keys(obj);
  let arr: any = [];
  let increment: any = objKeys.length / 2;
  for (let i = 0; i < increment; i++) {
    arr.push({ id: objKeys[i], value: objKeys[i + increment] });
  }
  return arr;
};

/**
 * Ajusta a quantidade de casas decimais de um valor.
 *
 * @param {any} value - O valor a ser ajustado.
 * @param {boolean} option - Se deve converter a string para número antes de ajustar.
 * @param {number} length - O número de casas decimais desejado.
 * @returns {any} - O valor ajustado com o número especificado de casas decimais.
 */
const fixed = (value: any, option: boolean = false, length: number = 2): any => {
  if (typeof value == 'string' && option) {
    value = parseFloat(value);
  }
  return option && !isEmpty(value) ? value.toFixed(length) : value;
};

/**
 * Substitui ocorrências de um valor em uma string ou array.
 *
 * @param {any} value - A string ou array de valores a ser modificada.
 * @param {any} n - O novo valor a ser inserido.
 * @param {any} o - O valor original a ser substituído.
 * @param {boolean} arr - Se o valor é um array.
 * @returns {any} - A string ou array modificada.
 */
const replacement = (value: any, n: any, o: any, arr = false): any => {
  if (arr) {
    n.forEach((item, index) => {
      value = value.replace(/o[index]/g, n[index]);
    });
  } else {
    value = value.replace(/o/g, n);
  }
  return value;
};

/**
 * Formata o tipo de entrada de um campo de formulário ao focar ou desfocar o campo.
 *
 * @param {any} $event - O evento de entrada (focus ou blur).
 * @param {any} $show - O número de casas decimais a ser exibido (opcional).
 * @returns {any} - O valor formatado.
 */
const formatType = ($event: any, $show = null): any => {
  if ($event.type == 'blur') {
    $event.target.type = 'text';
    if ($show != null) {
      $event.target.ariaValueNow = $event.target.value;
      $event.target.value = parseFloat($event.target.ariaValueNow).toFixed(parseFloat($show));
    }
  } else if ($event.type == 'focus') {
    $event.target.type = 'number';
    if ($event.target.ariaValueNow != null && $event.target.ariaValueNow != '') {
      $event.target.value = parseFloat($event.target.ariaValueNow);
    }
  }
};

/**
 * Gerencia um array de objetos, adicionando ou removendo itens com base em uma ação especificada.
 *
 * @param {any} arr - O array de objetos.
 * @param {any} value - O valor a ser adicionado ou removido.
 * @param {string} action - A ação a ser executada ('addIfNotExist' ou 'removeIfExist').
 * @returns {any} - O array atualizado.
 */
const managerArrayObject = (arr: any, value: any, action: string = 'addIfNotExist'): any => {
  let arrReturn: any = false;
  switch (action) {
    case 'addIfNotExist':
      if (!arr.some((item) => JSON.stringify(item) === JSON.stringify(value))) {
        arr.push(value);
        arrReturn = arr;
      }
      break;
    case 'removeIfExist':
      arr.some((item, index) => {
        if (JSON.stringify(item) === JSON.stringify(value)) {
          arr.splice(index, 1);
        }
      });
      arrReturn = arr;
      break;
    default:
      break;
  }
  return arrReturn;
};

/**
 * Gera um código hash a partir de uma string.
 *
 * @param {any} value - A string a ser convertida em hash.
 * @returns {any} - O valor do hash gerado.
 */
const hashCode = (value: any): any => {
  let hash = 0,
    i,
    chr;
  if (value.length === 0) return hash;
  for (i = 0; i < value.length; i++) {
    chr = value.charCodeAt(i);
    hash = (hash << 5) - hash + chr;
    hash |= 0; //Convert to 32bit integer
  }

  return hash;
};

/**
 * Converte uma string base64 em um arquivo.
 *
 * @param {any} base64 - A string base64 a ser convertida.
 * @param {string} filename - O nome do arquivo a ser gerado.
 * @returns {File} - O arquivo gerado.
 */
const base64ToFile = (base64: any, filename: string) => {
  let bstr = atob(base64),
    n = bstr.length,
    u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: 'application/octet-stream' });
};

/**
 * Converte uma string base64 em um Blob.
 *
 * @param {string} base64Data - A string base64 a ser convertida.
 * @returns {Blob} - O Blob gerado.
 */
const base64toBlob = (base64Data) => {
  const byteCharacters = atob(base64Data);
  const byteArrays = [];
  const sliceSize = 512;

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays, { type: 'application/octet-stream' });
};

/**
 * Tela de Leituras - Converte unidades de comprimento entre milímetros, centímetros e metros.
 *
 * @param {number} value - O valor a ser convertido.
 * @param {string} length_unit - A unidade de medida atual.
 * @param {string} targetUnit - A unidade de medida alvo.
 * @returns {number} - O valor convertido.
 */
const convertLength = (value: number, length_unit: string, targetUnit: string) => {
  if (length_unit == targetUnit) {
    return value;
  }

  switch (length_unit) {
    case 'mm':
      return targetUnit == 'cm' ? value / 10 : value / 1000;
    case 'cm':
      return targetUnit == 'mm' ? value * 10 : value / 100;
    case 'm':
      return targetUnit == 'mm' ? value * 1000 : value * 100;
  }
};

/**
 * Tela de Leituras - Converte unidades de comprimento entre milímetros, centímetros e metros, tratando problemas de ponto flutuante.
 *
 * @param {any} value - O valor a ser convertido.
 * @param {string} length_unit - A unidade de medida atual.
 * @param {string} targetUnit - A unidade de medida alvo.
 * @returns {Decimal} - O valor convertido.
 */
const convertLengthDecimal = (value: any, length_unit: string, targetUnit: string) => {
  value = new Decimal(value);

  if (length_unit == targetUnit) {
    return value;
  }

  switch (length_unit) {
    case 'mm':
      return targetUnit == 'cm' ? value.div(10) : value.div(1000);
    case 'cm':
      return targetUnit == 'mm' ? value.mul(10) : value.div(100);
    case 'm':
      return targetUnit == 'mm' ? value.mul(1000) : value.mul(100);
  }
};

/**
 * Tela de Leituras - Converte unidades de pressão entre kPa, bar e mca.
 *
 * @param {number} value - O valor a ser convertido.
 * @param {string} pressure_unit - A unidade de pressão atual.
 * @param {string} targetUnit - A unidade de pressão alvo.
 * @returns {number} - O valor convertido.
 */
const convertToPressure = (value: number, pressure_unit: string, targetUnit: string) => {
  if (pressure_unit == targetUnit) {
    return value;
  }

  switch (pressure_unit) {
    case 'kPa':
      return targetUnit == 'bar' ? value * 0.01 : value * 0.10197;
    case 'bar':
      return targetUnit == 'kPa' ? value * 100 : value * 10.197;
    case 'mca':
      return targetUnit == 'kPa' ? value * 9.80638 : value * 0.0980638;
  }
};

/**
 * Tela de Leituras - Converte unidades de pressão entre kPa, bar e mca, tratando problemas de ponto flutuante.
 *
 * @param {any} value - O valor a ser convertido.
 * @param {string} pressure_unit - A unidade de pressão atual.
 * @param {string} targetUnit - A unidade de pressão alvo.
 * @returns {Decimal} - O valor convertido.
 */
const convertToPressureDecimal = (value: any, pressure_unit: string, targetUnit: string) => {
  value = new Decimal(value);

  if (pressure_unit == targetUnit) {
    return value;
  }

  switch (pressure_unit) {
    case 'kPa':
      return targetUnit == 'bar' ? value.mul(0.01) : value.mul(0.10197);
    case 'bar':
      return targetUnit == 'kPa' ? value.mul(100) : value.mul(10.197);
    case 'mca':
      return targetUnit == 'kPa' ? value.mul(9.80638) : value.mul(0.0980638);
  }
};

/**
 * Tela de Leituras - IPI - Converte unidades de leitura de inclinação entre graus e mm/m.
 *
 * @param {number} value - O valor a ser convertido.
 * @param {string} reading_unit - A unidade de leitura atual.
 * @param {string} targetUnit - A unidade de leitura alvo.
 * @returns {number} - O valor convertido.
 */
const convertToDegree = (value: number, reading_unit: string, targetUnit: string) => {
  if (reading_unit == targetUnit) {
    return value;
  }

  switch (reading_unit) {
    case 'graus(°)':
      return (value * (100 * Math.PI)) / 18;
    case 'mm/m':
      return value / ((100 * Math.PI) / 18);
  }
};

/**
 * Tela de Leituras - IPI - Converte unidades de leitura de inclinação entre graus e mm/m, tratando problemas de ponto flutuante.
 *
 * @param {any} value - O valor a ser convertido.
 * @param {string} reading_unit - A unidade de leitura atual.
 * @param {string} targetUnit - A unidade de leitura alvo.
 * @returns {Decimal} - O valor convertido.
 */
const convertToDegreeDecimal = (value: any, reading_unit: string, targetUnit: string) => {
  value = new Decimal(value);

  if (reading_unit == targetUnit) {
    return value;
  }

  let calc: any = new Decimal(100).mul(Math.PI);

  switch (reading_unit) {
    case 'graus(°)':
      value = value.mul(calc);
      value = value.div(new Decimal(18));
      return value;
    case 'mm/m':
      calc = calc.div(new Decimal(18));
      value = value.div(calc);
      return value;
  }
};

/**
 * Retorna a quantidade de casas decimais em um valor.
 *
 * @param {any} value - O valor a ser analisado.
 * @returns {number} - O número de casas decimais.
 */
const fixedDecimaisPlaces = (value: any) => {
  let decimalPlaces = value.toString().replace(',', '.').split('.');
  decimalPlaces = decimalPlaces.length > 1 ? decimalPlaces[1].length : 0;
  return decimalPlaces;
};

/**
 * Remove duplicatas de um array de objetos.
 *
 * @param {any} arr - O array de objetos.
 * @returns {any} - O array sem duplicatas.
 */
const uniqueArray = (arr: any) => {
  const parsed_array = arr.map((val) => {
    return JSON.stringify(val);
  });
  const unique_array = parsed_array
    .filter((value, ind) => parsed_array.indexOf(value) == ind)
    .map((val) => {
      return JSON.parse(val);
    });
  return unique_array;
};

/**
 * Obtém a maior quantidade de casas decimais entre dois números.
 *
 * @param {any} num1 - O primeiro número.
 * @param {any} num2 - O segundo número.
 * @returns {number} - A maior quantidade de casas decimais.
 */
const getDecimalPlaces = (num1: any, num2: any) => {
  let strNum1 = num1.toString();
  let strNum2 = num2.toString();

  let decimalPlacesNum1 = strNum1.indexOf('.') !== -1 ? strNum1.length - strNum1.indexOf('.') - 1 : 0;
  let decimalPlacesNum2 = strNum2.indexOf('.') !== -1 ? strNum2.length - strNum2.indexOf('.') - 1 : 0;

  return Math.max(decimalPlacesNum1, decimalPlacesNum2);
};

/**
 * Converte um objeto constante para um objeto JavaScript padrão.
 *
 * @param {any} object - O objeto constante.
 * @returns {any} - O objeto JavaScript convertido.
 */
const constToObject = (object: any) => {
  return Object.keys(object).reduce((obj, key) => {
    obj[key] = object[key];
    return obj;
  }, {});
};

/**
 * Converte um objeto para um array, mantendo as propriedades como atributos do objeto.
 *
 * @param {any} object - O objeto a ser convertido.
 * @returns {any} - Um array de objetos.
 */
const objToArray = (object: any) => {
  return Object.entries(object).map(([key, value]: any) => {
    return { id: key, ...value };
  });
};

/**
 * Encontra o valor máximo de uma chave específica em um array de objetos.
 *
 * @param {any} obj - O array de objetos.
 * @param {any} key - A chave a ser verificada.
 * @returns {any} - O valor máximo encontrado.
 */
const maxValue = (obj: any, key: any) => {
  return obj.reduce((max, objeto) => {
    return objeto[key] > max ? objeto[key] : max;
  }, Number.NEGATIVE_INFINITY);
};

/**
 * Encontra o índice de um objeto em um array com base em uma chave e valor específicos.
 *
 * @param {any} obj - O array de objetos.
 * @param {any} value - O valor a ser encontrado.
 * @param {any} key - A chave a ser usada na busca.
 * @param {boolean} full - Se deve retornar o objeto completo (opcional).
 * @returns {any} - O índice ou objeto encontrado.
 */
const findIndexByValue = (obj: any, value: any, key: any, full: boolean = false) => {
  let idx = obj.findIndex((item) => item[key] === value);
  if (!full) {
    return idx;
  } else {
    return obj[idx];
  }
};

/**
 * Conta a quantidade de itens em um array de objetos que possuem uma propriedade com um valor específico.
 *
 * @param {any} array - O array de objetos.
 * @param {any} property - A propriedade a ser verificada.
 * @param {any} value - O valor a ser contado.
 * @param {any} property2 - Uma segunda propriedade para refinar a contagem (opcional).
 * @param {any} arrayFind - Um array de valores para refinar a contagem (opcional).
 * @returns {number} - A contagem de itens que atendem aos critérios.
 */
const totalItemInObject = (array, property, value, property2 = null, arrayFind = []) => {
  if (arrayFind.length == 0) {
    return array.reduce((contador, objeto) => {
      if (objeto[property] === value) {
        contador++;
      }
      return contador;
    }, 0);
  } else if (arrayFind.length > 0) {
    return array.reduce((contador, objeto) => {
      if (objeto[property] === value && arrayFind.includes(objeto[property2])) {
        contador++;
      }
      return contador;
    }, 0);
  }
};

/**
 * Remove duplicatas de um array.
 *
 * @param {any[]} array - O array do qual as duplicatas serão removidas.
 * @returns {any[]} - O array sem duplicatas.
 */
const arrayUnique = (array) => {
  const uArray = [];
  const seeDates = {};

  for (const date of array) {
    if (!seeDates[date]) {
      uArray.push(date);
      seeDates[date] = true;
    }
  }

  return uArray;
};

/**
 * Retorna os valores de um objeto em ordem de suas chaves numéricas.
 *
 * @param {any} obj - O objeto a ser processado.
 * @returns {any[]} - Um array de valores do objeto.
 */
const valuesOfObject = (obj) => {
  // Obtém as chaves como números e as ordena
  let numberKeys = Object.keys(obj)
    .map(Number)
    .sort((a, b) => a - b);

  // Itera sobre as chaves ordenadas e obtém os valores do objeto
  let arr = [];
  numberKeys.forEach(function (key) {
    arr.push(obj[key]);
  });

  return arr;
};

/**
 * Valida um CPF usando a biblioteca @brazilian-utils.
 *
 * @param {AbstractControl} control - O controle de formulário contendo o CPF.
 * @returns {object | null} - Um objeto de erro ou null se o CPF for válido.
 */
const validateCpf = (control: AbstractControl): { [key: string]: boolean } | null => {
  if (control.value == '') return null;

  if (control.value !== undefined && isValidCPF(control.value)) {
    return null;
  }
  return { isValidCPF: true };
};

/**
 * Detecta a extensão de um arquivo base64 com base em seus primeiros bytes.
 *
 * @param {string} base64String - A string base64 a ser analisada.
 * @returns {object} - Um objeto contendo a extensão e o mimeType.
 */
const base64Extension = (base64String) => {
  if (!base64String) return { extension: null, mimeType: null };

  // Decodifica os primeiros bytes da string base64 para verificar o tipo de arquivo
  const decoded = atob(base64String.slice(0, 100));

  // Mapeamento de assinaturas para extensões e MIME types
  const fileSignatures = [
    { signature: '\x89PNG', extension: 'png', mimeType: 'image/png' },
    { signature: '\xFF\xD8\xFF', extension: 'jpg', mimeType: 'image/jpeg' },
    { signature: 'BM', extension: 'bmp', mimeType: 'image/bmp' },
    { signature: 'GIF', extension: 'gif', mimeType: 'image/gif' },
    { signature: '%PDF', extension: 'pdf', mimeType: 'application/pdf' },
    { signature: 'AC10', extension: 'dxf', mimeType: 'application/vnd.dxf' }, // DXF
    { signature: 'ID3', extension: 'mp3', mimeType: 'audio/mpeg' }, // MP3
    { signature: 'OggS', extension: 'ogg', mimeType: 'audio/ogg' },
    { signature: 'RIFF', extension: 'wav', mimeType: 'audio/wav' },
    { signature: '\x00\x00\x01\xBA', extension: 'mpg', mimeType: 'video/mpeg' },
    { signature: 'ftyp', extension: 'mp4', mimeType: 'video/mp4' },
    { signature: '\x1F\x8B', extension: 'gz', mimeType: 'application/gzip' },
    { signature: 'PK', extension: 'zip', mimeType: 'application/zip' }
  ];

  // Verifica as assinaturas conhecidas
  for (const { signature, extension, mimeType } of fileSignatures) {
    if (decoded.startsWith(signature)) {
      return { extension, mimeType };
    }
  }

  // Verifica JPEG com marcação JFIF
  if (decoded.substring(6, 10) === 'JFIF') {
    return { extension: 'jpeg', mimeType: 'image/jpeg' };
  }

  // Se não identificado, retorna null
  return { extension: null, mimeType: null };
};
/**
 * Mapa - Converte uma cor hexadecimal para RGBA.
 *
 * @param {string} hex - A cor hexadecimal a ser convertida.
 * @param {number} alpha - O valor alpha (opcional).
 * @returns {object} - Um objeto RGBA.
 */
const hexToRGBA = (hex, alpha = 1.0) => {
  hex = hex.replace(/^#/, '');

  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);

  if (alpha === undefined) {
    alpha = 1;
  }

  return {
    r: r,
    g: g,
    b: b,
    a: alpha
  };
};

/**
 * Mapa - Cria classes CSS dinâmicas para um efeito de pulso no mapa, com base em uma cor hexadecimal.
 *
 * @param {string} name - O nome da classe.
 * @param {string} hex - A cor hexadecimal.
 * @param {number} alpha - O valor alpha (opcional).
 * @returns {string} - A classe CSS gerada.
 */
const classPulse = (name: string, hex: string, alpha = 1) => {
  let rgba = hexToRGBA(hex, alpha);
  let cls = `
  .pulse-pin-${name} {
    background: transparent;
    border-radius: 50%;
    box-shadow: 0 0 0 0 rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, 1);
    transform: scale(1);
    animation: pulse-${name} 2s infinite;
  }
  @keyframes pulse-${name} {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, 0.7);
    }
    70% {
      transform: scale(1);
      box-shadow: 0 0 0 20px rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, 0);
    }
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, 0);
    }
  }
  `;
  return cls;
};

/**
 * Compara o valor de uma constante com outra e retorna o valor correspondente na segunda constante.
 *
 * @param {any} value - O valor a ser comparado.
 * @param {any} constSource - A constante de origem.
 * @param {any} constTarget - A constante de destino.
 * @param {any} fieldSource - O campo de comparação na constante de origem.
 * @param {any} fieldTarget - O campo de retorno na constante de destino.
 * @returns {any} - O valor correspondente na constante de destino ou undefined.
 */
const compareConst = (value: any, constSource: any, constTarget: any, fieldSource: any, fieldTarget: any) => {
  // Verifique se o valor fornecido existe na constante constSource
  const foundSource = constSource.find((item) => item[fieldSource] == value);
  if (foundSource) {
    const index = constSource.indexOf(foundSource);
    const related = constTarget[index];
    return related ? related : undefined;
  } else {
    return undefined;
  }
};

/**
 * Converte valores HSL para RGB.
 *
 * @param {number} h - O valor de hue (matiz).
 * @param {number} s - O valor de saturation (saturação).
 * @param {number} l - O valor de lightness (luminosidade).
 * @returns {object} - Um objeto contendo os valores RGB.
 */
const hslToRgb = (h, s, l) => {
  s /= 100;
  l /= 100;

  let c = (1 - Math.abs(2 * l - 1)) * s;
  let x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  let m = l - c / 2;
  let r = 0,
    g = 0,
    b = 0;

  if (0 <= h && h < 60) {
    r = c;
    g = x;
    b = 0;
  } else if (60 <= h && h < 120) {
    r = x;
    g = c;
    b = 0;
  } else if (120 <= h && h < 180) {
    r = 0;
    g = c;
    b = x;
  } else if (180 <= h && h < 240) {
    r = 0;
    g = x;
    b = c;
  } else if (240 <= h && h < 300) {
    r = x;
    g = 0;
    b = c;
  } else if (300 <= h && h < 360) {
    r = c;
    g = 0;
    b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  return { r, g, b };
};

/**
 * Converte valores RGB para uma string hexadecimal.
 *
 * @param {number} r - O valor de red (vermelho).
 * @param {number} g - O valor de green (verde).
 * @param {number} b - O valor de blue (azul).
 * @returns {string} - A string hexadecimal correspondente.
 */
const rgbToHex = (r, g, b) => {
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
};

/**
 * Gera uma lista de cores aleatórias em hexadecimal.
 *
 * @param {number} count - A quantidade de cores a serem geradas.
 * @returns {string[]} - Um array de strings de cores em hexadecimal.
 */
const generateRandomColors = (count) => {
  let colors = [];

  for (let i = 0; i < count; i++) {
    let hue = Math.floor((360 / count) * i);
    let saturation = 70 + Math.floor(Math.random() * 30); // entre 70% e 100%
    let lightness = 50 + Math.floor(Math.random() * 20); // entre 50% e 70%

    let { r, g, b } = hslToRgb(hue, saturation, lightness);
    colors.push(rgbToHex(r, g, b));
  }

  return colors;
};

/**
 * Converte uma string em um nome de arquivo válido.
 * O método realiza as seguintes operações:
 * - Converte todos os caracteres para minúsculas.
 * - Remove caracteres especiais, mantendo apenas letras, números, espaços e hífens.
 * - Substitui espaços por hífens.
 * - Remove hífens duplicados.
 * @param {string} str - A string a ser convertida em um nome de arquivo válido.
 * @returns {string} - A string formatada como um nome de arquivo válido.
 */
const convertToValidFilename = (str) => {
  return str
    .toLowerCase() // Converte para minúsculas
    .replace(/[^\w\s-]/g, '') // Remove caracteres especiais (mantém letras, números, espaços e hífens)
    .replace(/\s+/g, '-') // Substitui espaços por hífens
    .replace(/-+/g, '-'); // Remove hífens duplicados
};

/**
 * Abre um arquivo codificado em Base64 em uma nova aba do navegador.
 *
 * - Sanitiza a string Base64 para garantir a segurança.
 * - Detecta o tipo MIME do arquivo a partir da string Base64.
 * - Converte o Base64 para um `Blob` e gera uma URL temporária.
 * - Abre o arquivo em uma nova aba do navegador.
 * - Revoga a URL temporária após 10 segundos para liberar memória.
 *
 * @param base64 - A string Base64 contendo os dados do arquivo.
 * @param sanitizer - Serviço utilizado para sanitizar a string Base64 e garantir a segurança.
 */
const openInNewTab = (base64: any, sanitizer: any): void => {
  // Valida e converte o Base64 para uma string
  const sanitizedUrl = sanitizer.sanitize(4, base64); // 4 = SecurityContext.RESOURCE_URL
  if (!sanitizedUrl || typeof sanitizedUrl !== 'string') {
    console.error('Sanitized Base64 string is invalid:', base64);
    return;
  }

  // Detecta o MIME type a partir do Base64
  const mimeMatch = sanitizedUrl.match(/^data:(.*?);base64,/);
  if (!mimeMatch || mimeMatch.length < 2) {
    console.error('Invalid MIME type in Base64:', sanitizedUrl);
    return;
  }

  const mimeType = mimeMatch[1]; // Extrai o tipo MIME
  const base64Data = sanitizedUrl.split(',')[1];

  // Converte Base64 para Blob
  const binaryData = atob(base64Data);
  const byteArray = new Uint8Array(binaryData.length);

  for (let i = 0; i < binaryData.length; i++) {
    byteArray[i] = binaryData.charCodeAt(i);
  }

  const blob = new Blob([byteArray], { type: mimeType });
  const blobUrl = URL.createObjectURL(blob);

  // Abre o arquivo em uma nova aba
  window.open(blobUrl);

  // Limpa a URL após 10 segundos
  setTimeout(() => URL.revokeObjectURL(blobUrl), 10000);
};

/**
 * Calcula o número adequado de linhas e colunas para distribuir os itens proporcionalmente.
 *
 * @param {number} count - A quantidade de itens a serem distribuídos.
 * @returns {{ rows: number, columns: number }} - Um objeto contendo o número de linhas e colunas.
 */
const calculateGridSize = (count: number): { rows: number; columns: number } => {
  let rows = 2;
  let columns = 2;

  if (count === 1) {
    rows = 1;
    columns = 1;
  } else if (count === 2) {
    rows = 1;
    columns = 2;
  } else if (count <= 4) {
    rows = 2;
    columns = 2;
  } else if (count <= 6) {
    rows = 2;
    columns = 3;
  } else if (count <= 9) {
    rows = 3;
    columns = 3;
  } else if (count <= 12) {
    rows = 4;
    columns = 3;
  } else if (count <= 16) {
    rows = 4;
    columns = 4;
  } else if (count <= 20) {
    rows = 5;
    columns = 4;
  } else if (count <= 25) {
    rows = 5;
    columns = 5;
  } else {
    rows = Math.ceil(count / 5);
    columns = Math.min(5, Math.ceil(count / rows));
  }

  return { rows, columns };
};

export default {
  removeIndexObject,
  enumToArray,
  extractIndex,
  objectToArray,
  isEmpty,
  enumToArrayComplete,
  sortObject,
  difference,
  differenceByIndex,
  filterByKeyAndValue,
  findIndexInArrayofObject,
  removeIndexArray,
  removeIndexArrayByFilter,
  controlNumber,
  enumToArraySimple,
  fixed,
  replacement,
  formatType,
  managerArrayObject,
  hashCode,
  base64ToFile,
  base64toBlob,
  convertLength,
  convertLengthDecimal,
  convertToPressure,
  convertToPressureDecimal,
  convertToDegree,
  convertToDegreeDecimal,
  fixedDecimaisPlaces,
  uniqueArray,
  getDecimalPlaces,
  constToObject,
  objToArray,
  maxValue,
  findIndexByValue,
  totalItemInObject,
  arrayUnique,
  valuesOfObject,
  validateCpf,
  base64Extension,
  hexToRGBA,
  classPulse,
  compareConst,
  generateRandomColors,
  convertToValidFilename,
  openInNewTab,
  calculateGridSize
};
