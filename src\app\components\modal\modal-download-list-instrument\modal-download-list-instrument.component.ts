import { Component, ElementRef, EventEmitter, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FileFormat, typeInstruments } from 'src/app/constants/instruments.constants';

@Component({
  selector: 'app-modal-download-list-instrument',
  templateUrl: './modal-download-list-instrument.component.html',
  styleUrls: ['./modal-download-list-instrument.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalDownloadListInstrumentComponent implements OnInit {
  @ViewChild('modalDownloadListInstrument') ModalDownloadListInstrument: ElementRef;
  @Output() public sendClickEvent = new EventEmitter();

  public formDownloadInformation: FormGroup = new FormGroup({
    file_format: new FormControl([], [Validators.required]),
    type_instrument: new FormControl([], [Validators.required]),
    axis: new FormControl('')
  });

  public singleSettingsModal = null;
  public multipleSettingsModal = null;
  public fileFormats = [];
  public typeInstruments = [];
  public messagesError = [];
  public labelAlert = '';

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {
    this.singleSettingsModal = {
      singleSelection: true,
      idField: 'value',
      textField: 'label',
      selectAllText: 'Selecionar todos',
      unSelectAllText: 'Desmarcar seleção',
      searchPlaceholderText: 'Pesquisar...',
      itemsShowLimit: 5,
      allowSearchFilter: false,
      enableCheckAll: false,
      closeDropDownOnSelection: true,
      noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
      noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
    };

    this.multipleSettingsModal = {
      singleSelection: false,
      idField: 'id',
      textField: 'name',
      selectAllText: 'Selecionar todos',
      unSelectAllText: 'Desmarcar seleção',
      searchPlaceholderText: 'Pesquisar...',
      itemsShowLimit: 5,
      allowSearchFilter: true,
      enableCheckAll: true,
      closeDropDownOnSelection: true,
      noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
      noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
    };

    this.fileFormats = FileFormat;
    this.typeInstruments = typeInstruments;
  }

  openModal() {
    this.modalService.open(this.ModalDownloadListInstrument);
  }

  clickRowEvent(action: any = null) {
    this.sendClickEvent.emit(action);
  }

  resetForm() {
    this.formDownloadInformation.get('file_format').setValue([]);
    this.formDownloadInformation.get('file_format').setErrors(null);
    this.formDownloadInformation.get('file_format').markAsUntouched();

    this.formDownloadInformation.get('type_instrument').setValue([]);
    this.formDownloadInformation.get('type_instrument').setErrors(null);
    this.formDownloadInformation.get('type_instrument').markAsUntouched();

    this.formDownloadInformation.get('axis').setValue('');
    this.formDownloadInformation.get('axis').setErrors(null);
    this.formDownloadInformation.get('axis').markAsUntouched();

    this.messagesError = [];
  }
}
