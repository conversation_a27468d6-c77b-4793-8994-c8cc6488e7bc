import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'app-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss']
})
export class ButtonComponent implements OnInit {
  @Input() class: string = '';
  @Input() type: boolean = true;
  @Input() disabled: boolean = false;
  @Input() icon: any;
  @Input() iconColor: string = '';
  @Input() customBtn: boolean = false;
  @Input() label: string = '';
  @Input() title: string = '';
  @Input() click: any;
  @Input() id: any = null;
  @Input() svg: boolean = false;

  constructor() {}

  ngOnInit(): void {}

  onClick() {
    if (this.click) this.click();
  }
}
