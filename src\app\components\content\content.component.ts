import { Component, OnInit, ViewChild } from '@angular/core';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-content',
  templateUrl: './content.component.html',
  styleUrls: ['./content.component.scss']
})
export class ContentComponent implements OnInit {
  @ViewChild(BreadcrumbComponent) breadcrumb: BreadcrumbComponent;

  private loadedComponent: any = '';

  constructor() {}

  ngOnInit(): void {}

  setStructure($event) {
    if (this.loadedComponent['loadFromStorage']) {
      this.loadedComponent['loadFromStorage']($event);
    }
  }

  onActivate($event) {
    this.loadedComponent = $event;
  }

  onStartTour(): void {
    if (this.loadedComponent && this.loadedComponent.loadTourGuide) {
      this.loadedComponent.loadTourGuide();
    }
  }

  setFilter() {
    if (this.loadedComponent && this.loadedComponent.loadFilterByHeader) {
      this.loadedComponent.loadFilterByHeader();
    }
  }
}
