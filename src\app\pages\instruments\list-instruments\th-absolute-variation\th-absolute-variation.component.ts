import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { PeriodsAbs, Ordenation } from 'src/app/constants/instruments.constants';

@Component({
  selector: 'app-th-absolute-variation',
  templateUrl: './th-absolute-variation.component.html',
  styleUrls: ['./th-absolute-variation.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ThAbsoluteVariationComponent implements OnInit {
  @Input() public filter: any = null;

  @Output() public sendClickEvent = new EventEmitter();

  public formFilter: FormGroup = new FormGroup({
    variation_period_days: new FormControl(''),
    order_by: new FormControl(1),

    period: new FormControl('')
  });

  public periods: any = PeriodsAbs;
  public ordenations: any = Ordenation;

  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.filter && changes.filter.currentValue != null) {
      this.formFilter.controls['variation_period_days'].setValue(
        changes.filter['VariationPeriodDays'] === undefined ? '' : changes.filter['VariationPeriodDays']
      );
      this.formFilter.controls['order_by'].setValue(changes.filter['OrderBy'] === undefined ? '' : changes.filter['OrderBy']);
      this.formFilter.controls['period'].setValue(changes.filter['Period'] === undefined ? '' : changes.filter['Period']);
    }
  }

  changePeriod(days) {
    this.formFilter.controls['variation_period_days'].setValue(days);
  }

  applyFilter() {
    let params = {
      variation_period_days: this.formFilter.controls['variation_period_days'].value,
      order_by: this.formFilter.controls['order_by'].value
    };
    this.sendClickEvent.emit({ action: 'absolute_variation', params: params });
  }

  resetFilter() {
    this.sendClickEvent.emit({ action: 'reset', params: {} });
  }
}
