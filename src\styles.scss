/* You can add global styles to this file, and also import other style files */

@import '~font-awesome/css/font-awesome.min.css';
@import '~@fortawesome/fontawesome-free/css/all.css';
@import '~ngx-spinner/animations/ball-clip-rotate-pulse.css';
@import '../src/assets/theme/variables.scss';
@import '~@ng-select/ng-select/themes/default.theme.css';
@import '~bootstrap/scss/bootstrap.scss';

@font-face {
  font-family: averta;
  src: url(assets/fonts/averta-demo-pe-regular.otf) format('opentype');
}

@font-face {
  font-family: averta-bold;
  src: url(assets/fonts/averta-bold.otf) format('opentype');
}

@font-face {
  font-family: roboto;
  src: url(assets/fonts/Roboto-LightItalic.ttf) format('truetype');
}

// @font-face {
//   font-family: hanamin;
//   src: url(assets/fonts/HanaMinA.ttf) format('truetype');
// }

// @font-face {
//   font-family: nanum-gothic;
//   src: url(assets/fonts/NanumGothic-Regular.ttf) format('truetype');
// }

// @font-face {
//   font-family: notosans;
//   src: url(assets/fonts/NotoSansDisplay-SemiCondensedLightItalic.ttf)
//     format('truetype');
// }

@font-face {
  font-family: opensans;
  src: url(assets/fonts/OpenSans-Regular.ttf) format('truetype');
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: averta;
}

html,
body {
  height: 100%;
  min-height: 100%;
}

app-root {
  display: block;
  height: 100%;
}

.grid-container {
  min-height: calc(100% - 71px);
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: 71px 100%;
  grid-template-areas:
    'menu header'
    'menu content';
}

/* GRID */

.grid-header {
  background: rgba(236, 244, 250, 0.5);
  //#ecf4fa;
  grid-area: header;
}

.grid-menu {
  background: #88aeb0;
  grid-area: menu;
}

.grid-content {
  background: rgba(236, 244, 250, 0.5);
  grid-area: content;
}
/* HACKS */

.form-check-input:checked {
  background-color: #34b575;
  border-color: #def7eb;
}

$base-color: #34b575 !important;
$disable-background-color: #eceeef;

.dropdown-menu {
  margin: 4rem auto;
}

.dropdown-item-danger {
  color: var(--bs-red);
}
.dropdown-item-danger:hover,
.dropdown-item-danger:focus {
  color: #fff;
  background-color: var(--bs-red);
}
.dropdown-item-danger.active {
  background-color: var(--bs-red);
}

.multiselect-dropdown {
  position: relative;
  width: 100%;
  font-size: 0.875em !important;
  .dropdown-btn {
    display: inline-block;
    border: 1px solid #ccc !important;
    width: 100%;
    padding: 6px 12px;
    margin-bottom: 0;
    font-weight: normal;
    line-height: 1.52857143;
    text-align: left;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    border-radius: 4px;
    .selected-item {
      border: 1px solid $base-color;
      margin-right: 4px;
      background: $base-color;
      padding: 0px 5px;
      color: #fff;
      border-radius: 2px;
      float: left;
      max-width: 300px !important;
      a {
        text-decoration: none;
      }
    }
    .selected-item:hover {
      box-shadow: 1px 1px #959595;
    }
    .dropdown-down {
      display: inline-block;
      top: 10px;
      width: 0;
      height: 0;
      border-top: 10px solid #ccc !important;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
    }
    .dropdown-up {
      display: inline-block;
      width: 0;
      height: 0;
      border-bottom: 10px solid #ccc !important;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
    }
  }
  .disabled {
    & > span {
      background-color: $disable-background-color;
    }
  }
}

.dropdown-list {
  position: absolute;
  padding-top: 6px;
  width: 100%;
  z-index: 9999;
  border: 1px solid #ccc;
  border-radius: 3px;
  background: #fff;
  margin-top: 10px;
  box-shadow: 0px 1px 5px #959595;
  font-size: 1rem;
  // font-family: 'Open Sans', sans-serif;
  ul {
    padding: 0px;
    list-style: none;
    overflow: auto;
    margin: 0px;
  }
  li {
    padding: 6px 10px;
    cursor: pointer;
    text-align: left;
    h5 {
      font-size: 1rem !important;
    }
  }
  .filter-textbox {
    border-bottom: 1px solid #ccc;
    position: relative;
    padding: 10px;
    input {
      border: 0px;
      width: 100%;
      padding: 0px 0px 0px 26px;
    }
    input:focus {
      outline: none;
    }
  }
}

.multiselect-item-checkbox input[type='checkbox'] {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.multiselect-item-checkbox input[type='checkbox']:focus + div:before,
.multiselect-item-checkbox input[type='checkbox']:hover + div:before {
  border-color: $base-color;
  background-color: #f2f2f2;
}

.multiselect-item-checkbox input[type='checkbox']:active + div:before {
  transition-duration: 0s;
}

.multiselect-item-checkbox input[type='checkbox'] + div {
  position: relative;
  padding-left: 2em;
  vertical-align: middle;
  user-select: none;
  cursor: pointer;
  margin: 0px;
  color: #000;
}

.multiselect-item-checkbox input[type='checkbox'] + div:before {
  box-sizing: content-box;
  content: '';
  color: $base-color;
  position: absolute;
  top: 50%;
  left: 0;
  width: 14px;
  height: 14px;
  margin-top: -9px;
  border: 2px solid $base-color;
  text-align: center;
  transition: all 0.4s ease;
}

.multiselect-item-checkbox input[type='checkbox'] + div:after {
  box-sizing: content-box;
  content: '';
  background-color: $base-color;
  position: absolute;
  top: 50%;
  left: 4px;
  width: 10px;
  height: 10px;
  margin-top: -5px;
  transform: scale(0);
  transform-origin: 50%;
  transition: transform 200ms ease-out;
}

.multiselect-item-checkbox input[type='checkbox']:disabled + div:before {
  border-color: #cccccc;
}

.multiselect-item-checkbox
  input[type='checkbox']:disabled:focus
  + div:before
  .multiselect-item-checkbox
  input[type='checkbox']:disabled:hover
  + div:before {
  background-color: inherit;
}

.multiselect-item-checkbox
  input[type='checkbox']:disabled:checked
  + div:before {
  background-color: #cccccc;
}

.multiselect-item-checkbox input[type='checkbox'] + div:after {
  background-color: transparent;
  top: 50%;
  left: 4px;
  width: 8px;
  height: 3px;
  margin-top: -4px;
  border-style: solid;
  border-color: #ffffff;
  border-width: 0 0 3px 3px;
  border-image: none;
  transform: rotate(-45deg) scale(0);
}

.multiselect-item-checkbox input[type='checkbox']:checked + div:after {
  content: '';
  transform: rotate(-45deg) scale(1);
  transition: transform 200ms ease-out;
}

.multiselect-item-checkbox input[type='checkbox']:checked + div:before {
  animation: borderscale 200ms ease-in;
  background: $base-color;
}

.multiselect-item-checkbox input[type='checkbox']:checked + div:after {
  transform: rotate(-45deg) scale(1);
}

@keyframes borderscale {
  50% {
    box-shadow: 0 0 0 2px $base-color;
  }
}

.color-picker-container {
  position: absolute;
  z-index: 999;
}

.disabled-dropdown {
  pointer-events: none;
  opacity: 1;
}

/* Seleciona .popover-header dentro de <ngb-popover-window> */
ngb-popover-window .popover-header {
  padding: var(--bs-popover-header-padding-y) var(--bs-popover-header-padding-x);
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--bs-popover-header-color);
  background-color: var(--bs-popover-header-bg);
  border-bottom: var(--bs-popover-border-width) solid
    var(--bs-popover-border-color);
  border-top-left-radius: var(--bs-popover-inner-border-radius);
  border-top-right-radius: var(--bs-popover-inner-border-radius);
  background-color: #34b575;
  color: #f2f2f2;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

ngb-popover-window {
  width: auto;
  min-width: 320px;
  max-width: 280px;
}

/* Seleciona .btn-sm dentro de <ngb-popover-window> */
ngb-popover-window .btn-sm,
ngb-popover-window .btn-group-sm > .btn {
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.5rem;
  --bs-btn-font-size: 0.875rem;
  --bs-btn-border-radius: 0.25rem;
  background-color: rgba(52, 181, 117, 1);
  color: #f2f2f2;
  margin-right: 14px;

  &:hover {
    background-color: rgba(52, 181, 117, 0.9);
    color: #f2f2f2;
  }

  &:last-child {
    margin-right: 0;
  }
}

ngb-popover-window .tour-step-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
}

.blink {
  animation: blinker 1s linear infinite;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

.pulse-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  position: relative;
}

.pulse-wrapper::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  background-color: red;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
  opacity: 0.6;
  transform: scale(1);
  z-index: 0;
}

.pulse-circle {
  position: relative;
  width: 8px;
  height: 8px;
  background-color: red;
  border-radius: 50%;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(2);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

//Inspeções - Planos de ação
.badge-farol {
  font-size: 1rem;
  font-weight: bold;
  padding: 0.4rem 0.6rem;
  border-radius: 1.2rem;
  display: inline-block;
  min-width: 2rem;
  text-align: center;
  line-height: 1;
}
