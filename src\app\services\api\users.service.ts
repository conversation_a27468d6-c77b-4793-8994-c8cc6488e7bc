import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class UsersService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  getUsersList(params: any = {}) {
    const url = '/users';
    return this.api.get<any>(url, params, false, 'user');
  }

  // Cadastro de usuarios
  postUser(params: any) {
    const url = '/users';
    return this.api.post<any>(url, params, {}, 'user');
  }

  // Retorna os usuarios para uso em filtro
  getUsers(params: any = {}, qparams: any = {}) {
    const url = '/users/search';
    return this.api.post<any>(url, params, qparams, 'user');
  }

  // Busca o usuario por ID
  getUserById(id: string) {
    const url = `/users/${id}`;
    return this.api.get<any>(url, null, false, 'user');
  }

  patchUsers(id: string, params: any) {
    const url = `/users/${id}`;
    return this.api.patch<any>(url, params, 'user');
  }

  // Atualizara todos dados do usuario
  putUsers(id: string, params: any) {
    const url = `/users/${id}`;
    return this.api.put<any>(url, params, 'user');
  }

  getClientUnits(id: string, params: any) {
    const url = `/users/${id}/client-units`;
    return this.api.get<any>(url, params, false, 'user');
  }

  getUsersMe() {
    const url = `/users/me`;
    return this.api.get<any>(url, {}, false, 'user');
  }

  // Editar cadastro de usuario
  putUsersMe(params: any) {
    const url = `/users/me`;
    return this.api.put<any>(url, params, 'user');
  }

  //O usuário recebera um e-mail para criacao de nova senha.
  patchPassword(params: any) {
    const url = `/users/me/password`;
    return this.api.patch<any>(url, params, 'user');
  }

  patchUpdateAttributes(params: any) {
    const url = `/users/me/attributes`;
    return this.api.patch<any>(url, params, 'user');
  }

  putExecuteActionsEmail(id: string, params: any) {
    const url = `/users/${id}/execute-actions-email`;
    return this.api.put<any>(url, params, 'user');
  }

  //Quando os termos sao modificados, esse endpoint eh acionado para forcar o envio de um email a todos os usuarios
  putReacceptTerms(params: any) {
    const url = `/users/reaccept-terms`;
    return this.api.put<any>(url, params, 'user');
  }
 

  // Termo de servico
  getTerms(params: any = {}) {
    const url = `/terms`;
    return this.api.get<any>(url, params, false, 'user');
  }

  //Histórico de alteracao de cadastro do usuario
  getHistory(id: string, params: any) {
    const url = `/users/${id}/history`;
    return this.api.get<any>(url, params, false, 'user');
  }

  //Auxiliar
  getRestriction(user: any = []) {
    user.clients = user.clients.map((item: any) => {
      return item.id;
    });

    user.client_units = user.client_units.map((item: any) => {
      return item.id;
    });

    user.structures = user.structures.map((item: any) => {
      return item.id;
    });

    return {
      units: user.clients,
      client_units: user.client_units,
      structures: user.structures
    };
  }
}
