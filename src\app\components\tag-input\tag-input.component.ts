import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-tag-input',
  templateUrl: './tag-input.component.html',
  styleUrls: ['./tag-input.component.scss']
})
export class TagInputComponent implements OnInit {
  @Input() public items: any = [];
  @Input() public itemLabel: any = 'name';
  @Input() public itemValue: any = 'name';
  @Input() public readonly: boolean = false;

  @Output() public sendTags = new EventEmitter();

  public selectedItems: any = [];

  constructor() {}

  ngOnInit(): void {}

  addTag(tag) {
    let item = { tag: true };
    item[this.itemLabel] = tag;

    return item;
  }

  onSelectionChange(event: any) {
    this.sendTags.emit(this.selectedItems);
  }
}
