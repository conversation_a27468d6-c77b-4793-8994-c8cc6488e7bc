import { AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl, FormGroup, Validators, FormBuilder, FormArray } from '@angular/forms';

import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';
import { PeriodicityType, TypeReports as SubjectType, WeeklyEmissionDays } from 'src/app/constants/reports.constants';

import { FormService } from 'src/app/services/form.service';
import { UserService } from 'src/app/services/user.service';
import { ReportsService as ReportsServiceApi } from 'src/app/services/api/reports.service';

import { NgxSpinnerService } from 'ngx-spinner';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-create-new-schedule',
  templateUrl: './create-new-schedule.component.html',
  styleUrls: ['./create-new-schedule.component.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class CreateNewScheduleComponent implements OnInit, AfterViewInit {
  @ViewChild('hierarchy') hierarchy: any;

  public formCreateNewSchedule: FormGroup;

  public periodicityType: any = PeriodicityType;
  public subjectType: any = SubjectType;
  public weeklyEmissionDays: any = WeeklyEmissionDays;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public edit: boolean = false;
  public isFormDirty: boolean = false;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public controls: any = null;

  public reportId: string = null;

  public func = fn;

  constructor(
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private formService: FormService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private reportsServiceApi: ReportsServiceApi,
    private userService: UserService
  ) {}

  /**
   * Método chamado na inicialização do componente. Configura o formulário de criação de novo agendamento,
   * gerencia o estado do formulário e verifica se há um relatório para edição.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    // Configurar o formulário
    this.formCreateNewSchedule = this.formBuilder.group({
      SubjectType: new FormControl(0),
      Title: new FormControl('', [Validators.required]),
      ResponsibleName: new FormControl('', [Validators.required]),
      DaysToAnalyze: new FormControl('', [Validators.required]),
      PeriodicityType: new FormControl(1),
      DestinationEmails: new FormControl('', [Validators.required]),
      DailyPeriodicity: new FormControl('', [Validators.required]),
      DailyRecurrencePattern: new FormControl('everyday'),
      WeeklyEmissionDays: this.formBuilder.array(this.weeklyEmissionDays.map(() => new FormControl(false))),
      MonthlyEmissionDay: new FormControl('', [Validators.required]),
      MonthlyPeriodicity: new FormControl('', [Validators.required])
    });

    this.controls = this.formCreateNewSchedule.controls;

    this.managerFormGroup();
    this.managerFormGroup('daily');

    if (this.activatedRoute.snapshot.params.reportId) {
      this.edit = true;
      this.getReport(this.activatedRoute.snapshot.params.reportId);
      if (this.activatedRoute.snapshot.url && this.activatedRoute.snapshot.url[1] && this.activatedRoute.snapshot.url[1].path == 'view') {
        this.edit = false;
      }
    }

    // Monitorar alterações no formulário
    this.formCreateNewSchedule.valueChanges.subscribe(() => {
      this.checkFormDirty();
    });
  }

  ngAfterViewInit() {
    // Força o alinhamento e estilo dos elementos
    const elements = document.querySelectorAll('.card-header, .form-control, .form-select');
    elements.forEach((element) => {
      const el = element as HTMLElement;
      el.style.textAlign = 'left';
      el.style.fontSize = '0.875em';
    });

    // Garante que as mudanças no DOM sejam refletidas
    this.cdr.detectChanges();
  }

  //Atualiza a variável `isFormDirty` para indicar se o formulário foi modificado.
  private checkFormDirty(): void {
    this.isFormDirty = !this.formCreateNewSchedule.pristine;
  }

  /**
   * Retorna o `FormArray` correspondente aos dias de emissão semanal.
   * @returns {FormArray} O array de controles para os dias de emissão semanal.
   */
  get weeklyEmissionDaysArray() {
    return this.formCreateNewSchedule.get('WeeklyEmissionDays') as FormArray;
  }

  /**
   * Define os dias de emissão semanal selecionados no `FormArray` correspondente.
   * @param {number[]} values - Os valores que representam os dias da semana a serem selecionados.
   */
  setWeeklyEmissionDays(values: number[]) {
    const formArray = this.weeklyEmissionDaysArray;
    values.forEach((value) => {
      if (value >= 0 && value < formArray.length) {
        formArray.at(value).setValue(true);
      }
    });
  }

  /**
   * Gerencia os grupos de controles do formulário com base na opção de periodicidade selecionada.
   * Habilita ou desabilita os campos conforme a periodicidade escolhida (diária, semanal, mensal).
   * @param {string} option - A opção de periodicidade a ser gerenciada (padrão é '').
   */
  managerFormGroup(option = '') {
    let fields = [];

    if (option !== 'daily') {
      let daily: boolean = false;
      let weekly: boolean = false;
      let monthly: boolean = false;

      switch (this.controls['PeriodicityType'].value) {
        case 1: // Diário
          daily = true;
          weekly = false;
          monthly = false;
          break;
        case 2: // Semanal
          daily = false;
          weekly = true;
          monthly = false;
          break;
        case 3: // Mensal
          daily = false;
          weekly = false;
          monthly = true;
          break;
      }

      fields = ['DailyPeriodicity', 'DailyRecurrencePattern'];
      this.formService.controlValidate(this.formCreateNewSchedule, fields, daily);

      this.formService.toggleCheckbox(this.weeklyEmissionDaysArray, weekly);

      fields = ['MonthlyEmissionDay', 'MonthlyPeriodicity'];
      this.formService.controlValidate(this.formCreateNewSchedule, fields, monthly);
    }

    switch (this.controls['DailyRecurrencePattern'].value) {
      case 'everyday':
      case '':
        fields = ['DailyPeriodicity'];
        this.formService.controlValidate(this.formCreateNewSchedule, fields, false);
        break;
      case 'every_n_days':
        fields = ['DailyPeriodicity'];
        this.formService.controlValidate(this.formCreateNewSchedule, fields, true);
        break;
    }
  }

  /**
   * Obtém os dados de um relatório específico pelo seu ID e os preenche no formulário.
   * @param {string} reportId - O ID do relatório a ser obtido.
   */
  getReport(reportId: string) {
    this.reportsServiceApi.getReportsById(reportId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.splitData(dados);
      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Preenche os controles do formulário com os dados recebidos.
   * @param {any} $dados - Os dados que serão utilizados para preencher o formulário.
   */
  splitData($dados) {
    this.reportId = $dados.id;

    this.hierarchy.setClients([{ id: $dados.client.id, name: $dados.client.name }], false);
    this.hierarchy.setUnits([{ id: $dados.client_unit.id, name: $dados.client_unit.name }], false);
    this.hierarchy.setStructures([{ id: $dados.structure.id, name: $dados.structure.name }], false);

    this.controls['Title'].setValue($dados.title);
    this.controls['ResponsibleName'].setValue($dados.responsible_name);
    this.controls['DestinationEmails'].setValue($dados.destination_emails.join(','));
    this.controls['DaysToAnalyze'].setValue($dados.days_to_analyze);
    this.controls['PeriodicityType'].setValue($dados.periodicity_type);

    if ($dados.daily_report_parameters) {
      this.controls['DailyPeriodicity'].setValue($dados.daily_report_parameters.daily_periodicity);
    }

    if ($dados.weekly_report_parameters) {
      this.setWeeklyEmissionDays($dados.weekly_report_parameters.weekly_emission_days);
    }

    if ($dados.monthly_report_parameter) {
      this.controls['MonthlyEmissionDay'].setValue($dados.monthly_report_parameter.monthly_emission_day);
      this.controls['MonthlyPeriodicity'].setValue($dados.monthly_report_parameter.monthly_periodicity);
    }

    this.managerFormGroup();
    this.managerFormGroup('daily');
  }

  /**
   * Cria um novo agendamento de relatório com os parâmetros fornecidos.
   * @param {any} params - Os parâmetros necessários para a criação do novo agendamento.
   */
  createNewSchedule(params) {
    this.ngxSpinnerService.show();
    this.messagesError = [];

    this.reportsServiceApi.postReports(params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessagePadroes.SendReport;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/reports']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        this.messagesError = [];
        error.error.forEach((msgError) => {
          this.messagesError.push(msgError);
        });

        setTimeout(() => {
          this.messagesError = [];
        }, 4000);

        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Edita um agendamento de relatório existente com os parâmetros fornecidos.
   * @param {any} params - Os parâmetros necessários para a edição do agendamento existente.
   */
  editReportScheduling(params) {
    this.ngxSpinnerService.show();

    this.reportsServiceApi.putReports(this.reportId, params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/reports']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        this.messagesError = [];
        error.error.forEach((msgError) => {
          this.messagesError.push(msgError);
        });

        setTimeout(() => {
          this.messagesError = [];
        }, 4000);

        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Gera os parâmetros necessários para criar ou editar um agendamento de relatório com base nos filtros e no formulário.
   * @returns {any} Os parâmetros configurados para o agendamento de relatório.
   */
  getParams() {
    let filterHierarchy = this.hierarchy.getFilters();

    const params = {
      structure_id: filterHierarchy.structures[0].id,
      title: this.controls['Title'].value,
      responsible_name: this.controls['ResponsibleName'].value,
      destination_emails: this.controls['DestinationEmails'].value.split(','),
      days_to_analyze: parseInt(this.controls['DaysToAnalyze'].value),
      subject_type: this.controls['SubjectType'].value,
      periodicity_type: parseInt(this.controls['PeriodicityType'].value)
    };

    switch (parseInt(this.controls['PeriodicityType'].value)) {
      case 1: //Diário
        params['daily_report_parameters'] = {
          daily_periodicity: this.controls['DailyPeriodicity'].value
        };
        break;
      case 2: //Semanal
        params['weekly_report_parameters'] = {
          weekly_emission_days: this.getSelectedWeeklyEmissionDays()
        };
        break;
      case 3: //Mensal
        params['monthly_report_parameter'] = {
          monthly_emission_day: this.controls['MonthlyEmissionDay'].value,
          monthly_periodicity: this.controls['MonthlyPeriodicity'].value
        };
        break;
    }

    if (this.edit) {
      params['id'] = this.reportId;
    }
    return params;
  }

  /**
   * Retorna os índices dos dias de emissão semanal que foram selecionados no `FormArray`.
   * @returns {number[]} Um array com os índices dos dias de emissão semanal selecionados.
   */
  getSelectedWeeklyEmissionDays(): number[] {
    return this.weeklyEmissionDaysArray.controls.map((control, index) => (control.value ? index : null)).filter((index) => index !== null) as number[];
  }

  /**
   * Verifica se pelo menos um dia de emissão semanal foi selecionado.
   * @returns {boolean} Verdadeiro se pelo menos um dia de emissão foi selecionado, falso caso contrário.
   */
  hasAtLeastOneWeeklyEmissionDay(): boolean {
    return this.weeklyEmissionDaysArray.controls.some((control) => control.value === true);
  }

  /**
   * Verifica se o botão de envio deve estar desabilitado com base na periodicidade,
   * na validade do formulário e no estado "dirty".
   * @returns {boolean} Verdadeiro se o botão deve estar desabilitado, falso caso contrário.
   */
  get isButtonDisabled(): boolean {
    const periodicityType = this.controls['PeriodicityType'].value;
    const formValid = this.formCreateNewSchedule.valid;

    if (periodicityType === 1 || periodicityType === 3) {
      return !formValid || !this.isFormDirty;
    }

    if (periodicityType === 2) {
      return !formValid || !this.hasAtLeastOneWeeklyEmissionDay() || !this.isFormDirty;
    }

    return true; // Caso padrão
  }

  /**
   * Valida o formulário e, se válido, cria ou edita um agendamento de relatório com base nos parâmetros fornecidos.
   * Exibe mensagens de erro caso algum campo obrigatório não esteja preenchido.
   */
  validate() {
    if (this.controls['PeriodicityType'].value === 2) {
      // Verificar se é semanal
      if (!this.hasAtLeastOneWeeklyEmissionDay()) {
        this.messagesError.push('Pelo menos um dia de emissão semanal deve ser selecionado.');
      }
    }

    if (this.formCreateNewSchedule.valid) {
      const params = this.getParams();
      if (!this.edit) {
        this.createNewSchedule(params);
      } else {
        if (this.isFormDirty) {
          this.editReportScheduling(params);
        }
      }
    }
  }
}
