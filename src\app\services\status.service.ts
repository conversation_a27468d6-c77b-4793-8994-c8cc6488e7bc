import { Injectable } from '@angular/core';
import { OccurrenceActionPlanStatusList } from '../constants/inspections.constants';

@Injectable({
  providedIn: 'root'
})
export class StatusService {
  private statusIcons: { [key: number]: string } = {
    1: '<i class="fa fa-circle text-dark me-1" title="Sem plano de ação"></i>', // HasNoActionPlan
    2: '<i class="fa fa-circle text-secondary me-1" title="Concluído"></i>', // Completed
    3: '<i class="fa fa-circle text-success me-1" title="Pendente > 3 dias"></i>', // PendingMoreThanThreeDays
    4: '<i class="fa fa-circle text-warning me-1" title="Pendente até 3 dias"></i>', // PendingThreeDaysOrLess
    5: '<i class="fa fa-circle text-danger me-1" title="Em atraso"></i>', // Overdue
    6: `
  <div class="pulse-wrapper me-1" title="Em atraso e em reincidência">
    <span class="pulse-circle"></span>
  </div>
` // OverdueAndRecurring
  };

  private statusColors: { [key: number]: { hex: string; rgb: string } } = {
    1: { hex: '#212529', rgb: 'rgb(33, 37, 41)' }, // text-dark
    2: { hex: '#6c757d', rgb: 'rgb(108, 117, 125)' }, // text-secondary
    3: { hex: '#198754', rgb: 'rgb(25, 135, 84)' }, // text-success
    4: { hex: '#ffc107', rgb: 'rgb(255, 193, 7)' }, // text-warning
    5: { hex: '#dc3545', rgb: 'rgb(220, 53, 69)' }, // text-danger
    6: { hex: '#dc3545', rgb: 'rgb(220, 53, 69)' } // mesmo do 5, com efeito pulsante
  };

  /**
   * Retorna o HTML de ícone com base no status numérico.
   * @param statusValue - Código numérico do status
   * @returns Objeto com status label e HTML do ícone
   */
  formatStatus(statusValue: number): { status: string; extra: any; color: any } {
    const found = OccurrenceActionPlanStatusList.find((item) => item.value === statusValue);
    const label = found?.label || 'Desconhecido';
    const icon = this.statusIcons[statusValue] || '';
    const color = this.statusColors[statusValue] || { hex: '#000000', rgb: 'rgb(0, 0, 0)' };

    return {
      status: label,
      extra: { status: icon, show_label: false },
      color: color.hex
    };
  }
}
