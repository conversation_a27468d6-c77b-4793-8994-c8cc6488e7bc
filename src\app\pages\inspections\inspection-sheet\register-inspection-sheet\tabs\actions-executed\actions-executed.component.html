<div class="table-responsive-md mt-4">
  <form [formGroup]="actionsForm">
    <table class="table table-bordered table-hover align-middle">
      <thead class="table-light">
        <tr>
          <th style="width: 30%">Identificação da Anomalia</th>
          <th style="width: 40%">Ações Executadas</th>
          <th style="width: 30%">Classificação</th>
        </tr>
      </thead>
      <tbody formArrayName="anomalies">
        <tr
          *ngFor="let anomaly of anomalies.controls; let i = index"
          [formGroupName]="i"
        >
          <!-- Coluna com o nome da anomalia -->
          <td>
            {{ getAnomalyName(anomaly.get('Anomaly')?.value) }}
          </td>

          <!-- Coluna com ações executadas -->
          <td>
            <div *ngIf="!view; else readModeExecutedActions">
              <textarea
                rows="2"
                class="form-control"
                formControlName="ExecutedActions"
                placeholder="Descreva as ações executadas"
                (blur)="onBlur('ExecutedActions', i)"
              ></textarea>
            </div>
            <ng-template #readModeExecutedActions>
              {{ anomaly.get('ExecutedActions')?.value }}
            </ng-template>
          </td>

          <!-- Coluna com classificação do resultado da ação -->
          <td>
            <div *ngIf="!view; else readModeActionResult">
              <select
                class="form-select"
                formControlName="ActionResultClassification"
                (change)="onChange('ActionResultClassification', i)"
              >
                <option
                  *ngFor="let option of actionResultClassificationOptions"
                  [value]="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
            </div>
            <ng-template #readModeActionResult>
              {{
                getActionResultLabel(
                  anomaly.get('ActionResultClassification')?.value
                )
              }}
            </ng-template>
          </td>
        </tr>
      </tbody>
    </table>
  </form>
</div>
