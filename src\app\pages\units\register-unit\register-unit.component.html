<div class="list-content">
  <form
    class="row g-3 mt-1"
    [formGroup]="formClientUnit"
    (ngSubmit)="validate()"
  >
    <!-- Unidade -->
    <div class="col-md-4" tourAnchor="unit_name">
      <label class="form-label">Nome da Unidade</label>
      <input
        type="text"
        formControlName="name"
        class="form-control"
        [attr.maxlength]="32"
        autocomplete="off"
        (input)="onValueChange($event, 'name')"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formClientUnit.get('name').valid &&
          formClientUnit.get('name').touched
        "
        >Campo Obrigatório.</small
      >
      <!-- Contador de caracteres -->
      <small class="form-text text-muted d-block"
        >Caracteres {{ charCounts['name'] || 0 }} de 32
      </small>
    </div>
    <!-- Cliente -->
    <div class="col-md-4" tourAnchor="client_select">
      <label class="form-label">Cliente</label>
      <select class="form-select" formControlName="clients">
        <option value="">Selecione...</option>
        <option *ngFor="let item of clients" [ngValue]="item.id">
          {{ item.name }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formClientUnit.get('clients').valid &&
          formClientUnit.get('clients').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- DATUM -->
    <div class="col-md-4" tourAnchor="datum_select">
      <label class="form-label">DATUM</label>
      <select
        class="form-select"
        formControlName="datum"
        (change)="coordinatesConversion()"
      >
        <option value="">Selecione...</option>
        <option *ngFor="let item of datum" [ngValue]="item.id">
          {{ item.value }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formClientUnit.get('datum').valid &&
          formClientUnit.get('datum').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Coordenadas UTM -->
    <div class="col-md-8" tourAnchor="utm_coordinates">
      <div class="card">
        <div class="card-header form-control-bg">
          <div class="form-check form-check-inline">
            <input
              type="radio"
              class="form-check-input"
              formControlName="coordinate_format"
              value="{{ coordinateFormatList[1].id }}"
              (change)="onCoordinateFormatChange(coordinateFormatList[1])"
            />
            <label class="form-check-label" for="gridRadios2">
              Coordenadas UTM
            </label>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <label class="form-label">Zona (Fuso)</label>
              <select
                class="form-select"
                formControlName="zone_number"
                (change)="coordinatesConversion()"
              >
                <option value="">Selecione...</option>
                <option *ngFor="let item of zoneNumberUTM" [ngValue]="item.id">
                  {{ item.value }}
                </option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Letra</label>
              <select
                class="form-select"
                formControlName="zone_letter"
                (change)="coordinatesConversion()"
              >
                <option value="">Selecione...</option>
                <option *ngFor="let item of zoneLetterUTM" [ngValue]="item.id">
                  {{ item.value }}
                </option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Coordenada Norte</label>
              <input
                type="text"
                formControlName="northing"
                class="form-control"
                (blur)="coordinatesConversion(); func.formatType($event)"
                (focus)="func.formatType($event)"
                appDisableScroll
              />
            </div>
            <div class="col-md-3">
              <label class="form-label">Coordenada Leste</label>
              <input
                type="text"
                formControlName="easting"
                class="form-control"
                (blur)="coordinatesConversion(); func.formatType($event)"
                (focus)="func.formatType($event)"
                appDisableScroll
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Coordenadas UTM -->

    <!-- Coordenadas Geográficas -->
    <div class="col-md-4" tourAnchor="geo_coordinates">
      <div class="card">
        <div class="card-header form-control-bg">
          <div class="form-check form-check-inline">
            <input
              type="radio"
              class="form-check-input"
              formControlName="coordinate_format"
              value="{{ coordinateFormatList[0].id }}"
              (change)="onCoordinateFormatChange(coordinateFormatList[0])"
            />
            <label class="form-check-label" for="gridRadios2">
              Coordenadas Geográficas
            </label>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <label class="form-label">Latitude</label>
              <input
                type="text"
                formControlName="latitude"
                class="form-control"
                (blur)="coordinatesConversion(); func.formatType($event)"
                (focus)="func.formatType($event)"
                appDisableScroll
              />
            </div>
            <div class="col-md-6">
              <label class="form-label">Longitude</label>
              <input
                type="text"
                formControlName="longitude"
                class="form-control"
                (blur)="coordinatesConversion(); func.formatType($event)"
                (focus)="func.formatType($event)"
                appDisableScroll
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Alerta -->
    <div
      class="alert mt-3"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
      [innerHTML]="message.text"
    ></div>
    <!-- Mensagens de erro -->
    <div class="row mt-2">
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesError"
      ></app-alert>
    </div>

    <!-- Botões -->
    <div class="col-md-12 d-flex justify-content-end mb-3">
      <app-button
        tourAnchor="save_button"
        [class]="'btn-logisoil-green'"
        [icon]="'fa fa-floppy-o'"
        [label]="'Salvar'"
        [type]="false"
        [disabled]="!formClientUnit.valid"
        class="me-1"
        *ngIf="formCrtl"
      >
      </app-button>
      <app-button
        tourAnchor="back_button"
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [routerLink]="['/units']"
      ></app-button>
    </div>
  </form>
  <!-- Mapa -->
  <div class="col-md-6 offset-md-3">
    <app-google-maps></app-google-maps>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>

<tour-step-template></tour-step-template>
