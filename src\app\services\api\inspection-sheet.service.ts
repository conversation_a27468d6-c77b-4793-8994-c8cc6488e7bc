import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class InspectionSheetService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  /**
   * Busca a lista de fichas de inspeção com base nos parâmetros fornecidos.
   *
   * @param {any} params - Parâmetros de busca (filtros, paginação etc).
   * @returns {Observable<any>} - Lista de fichas de inspeção.
   */
  getInspectionSheet(params: any) {
    const url = '/inspection-sheets/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  /**
   * Busca uma ficha de inspeção específica pelo seu ID.
   *
   * @param {string} id - ID da ficha de inspeção.
   * @returns {Observable<any>} - Dados da ficha de inspeção.
   */
  getInspectionSheetById(id: string) {
    const url = `/inspection-sheets/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Cria uma nova ficha de inspeção.
   *
   * @param {any} params - Dados da ficha a ser criada.
   * @returns {Observable<any>} - Resposta da API.
   */
  postInspectionSheet(params: any) {
    const url = '/inspection-sheets';
    return this.api.post<any>(url, params, {}, 'client');
  }

  /**
   * Atualiza uma ficha de inspeção existente.
   *
   * @param {string} id - ID da ficha.
   * @param {boolean} [userCompletedTheInspection=false] - Indica se o usuário finalizou o preenchimento da ficha.
   * @param {any} params - Dados a serem atualizados.
   * @returns {Observable<any>} - Resposta da API.
   */
  putInspectionSheet(id: string, userCompletedTheInspection: boolean = false, params: any) {
    const url = `/inspection-sheets/${id}?userCompletedTheInspection=${userCompletedTheInspection}`;
    return this.api.put<any>(url, params, 'client');
  }

  /**
   * Busca as ocorrências que podem ser vinculadas a uma determinada ficha de inspeção.
   *
   * @param {string} id - ID da ficha de inspeção.
   * @returns {Observable<any>} - Lista de ocorrências vinculáveis.
   */
  getOccurrencesLinkable(id: string) {
    const url = `/inspection-sheets/${id}/occurrences/linkable`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Exclui uma ficha de inspeção com base no ID fornecido.
   *
   * @param {string} id - ID da ficha de inspeção a ser excluída.
   * @returns {Observable<any>} - Resposta da API.
   */
  deleteInspectionSheet(id: string) {
    const url = `/inspection-sheets/${id}`;
    return this.api.delete<any>(url, 'client', {});
  }

  /**
   * Recupera o histórico de alterações de uma ficha de inspeção.
   *
   * @param {string} id - ID da ficha.
   * @param {any} params - Filtros opcionais para o histórico.
   * @returns {Observable<any>} - Histórico da ficha.
   */
  getInspectionSheetHistory(id: string, params = {}) {
    const url = `/inspection-sheets/${id}/history`;
    return this.api.get<any>(url, params, false, 'client');
  }

  /**
   * Busca os dados de uma ocorrência específica da ficha de inspeção.
   *
   * @param {string} id - ID da ocorrência.
   * @returns {Observable<any>} - Dados detalhados da ocorrência.
   */
  getInspectionSheetOccurrenceById(id: string) {
    const url = `/inspection-sheets/occurrences/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Busca ocorrências de fichas de inspeção com base nos filtros informados.
   *
   * @param {any} params - Parâmetros de busca (filtros como datas, status, IDs etc).
   * @returns {Observable<any>} - Lista de ocorrências encontradas.
   */
  getInspectionSheetOccurrences(params: any) {
    const url = '/inspection-sheets/occurrences/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  /**
   * Recupera a lista de planos de ação com base nos filtros fornecidos.
   *
   * @param params Objeto contendo os parâmetros de busca.
   * @returns Observable com os dados da API.
   */
  getActionPlans(params: any) {
    const url = '/action-plans/search';
    return this.api.get<any>(url, params, false, 'client', true);
  }

  /**
   * Recupera os detalhes de um plano de ação específico.
   *
   * @param id Identificador do plano de ação.
   * @returns Observable com os dados do plano.
   */
  getActionPlanById(id: string) {
    const url = `/action-plans/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Cadastra um novo plano de ação.
   *
   * @param params Objeto contendo os dados do novo plano.
   * @returns Observable com a resposta da API após o cadastro.
   */
  postActionPlans(params: any) {
    const url = '/action-plans';
    return this.api.post<any>(url, params, {}, 'client', false, false, true);
  }

  /**
   * Atualiza os dados de um plano de ação existente.
   *
   * @param id Identificador do plano a ser atualizado.
   * @param params Objeto contendo os dados atualizados.
   * @returns Observable com a resposta da API após a atualização.
   */
  putActionPlans(id: string, params: any) {
    const url = `/action-plans/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  /**
   * Exclui um plano de ação pelo seu identificador.
   *
   * @param id Identificador do plano a ser removido.
   * @returns Observable com a resposta da API após a exclusão.
   */
  deleteActionPlan(id: string) {
    const url = `/action-plans/${id}`;
    return this.api.delete<any>(url, 'client');
  }

  /**
   * Valida dados de um plano de ação com base em regras específicas do backend.
   *
   * @param id Identificador do plano a ser validado.
   * @param params Parâmetros para validação.
   * @returns Observable com o resultado da validação.
   */
  patchActionPlans(id: string) {
    const url = `/action-plans/${id}/validate`;
    return this.api.patch<any>(url, null, 'client');
  }

  /**
   * Busca o histórico de alterações de um plano de ação específico.
   *
   * @param id Identificador do plano de ação.
   * @param params Parâmetros opcionais de paginação ou filtro (default: objeto vazio).
   * @returns Observable com os dados do histórico.
   */
  getActionPlanHistory(id: string, params = {}) {
    const url = `/action-plans/${id}/history`;
    return this.api.get<any>(url, params, false, 'client', true);
  }

  /**
   * Recupera os anexos associados a um plano de ação específico.
   *
   * - Realiza uma requisição GET para o endpoint `/action-plans/{id}/attachments`.
   * - Utiliza a API do tipo `client` e não requer parâmetros adicionais nem resposta em blob.
   *
   * @param {string} id - Identificador do plano de ação.
   * @returns {Observable<any>} Observable com os dados dos anexos.
   */
  getActionPlanAttachments(id: string) {
    const url = `/action-plans/${id}/attachments`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Envia arquivos anexos para um plano de ação específico.
   *
   * @param id UUID do plano de ação.
   * @param formData Objeto FormData contendo os arquivos.
   * @returns Observable com a resposta da API.
   */
  postActionPlansAttachments(id: string, formData: FormData) {
    const url = `/action-plans/${id}/attachments`;
    return this.api.post<any>(url, formData, {}, 'client', true, false, true);
  }

  /**
   * Remove um anexo específico de um plano de ação.
   *
   * - Realiza uma requisição DELETE no endpoint `/action-plans/{id}/attachments/{id}`.
   * - O mesmo `id` é usado para o plano de ação e para o anexo, assumindo que ambos compartilham o mesmo identificador.
   * - Utiliza a API do tipo `client`.
   *
   * @param {string} id - Identificador do anexo a ser removido (e do plano, se for o mesmo).
   * @returns {Observable<any>} Observable com a resposta da API após a exclusão.
   */
  deleteActionPlanAttachments(id: string, attachmentId: string) {
    const url = `/action-plans/${id}/attachments/${attachmentId}`;
    return this.api.delete<any>(url, 'client');
  }
}
