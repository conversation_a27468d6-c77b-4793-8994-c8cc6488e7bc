.drag-drop-container {
  box-sizing: border-box;
}
.drag-list {
  display: grid;
  grid-template-columns: 166px 166px 166px;
  grid-auto-rows: 50px;
  grid-gap: 24px;
  justify-content: space-evenly;
}

.drag-list-flex {
  display: flex;
  justify-content: space-between;
}

.drag-item {
  display: flex;
  align-items: center;
  cursor: move;
  background: #fff;
  width: 166px;
  height: 50px;
  border-radius: 5px;
  border: 1px solid #34b575;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.29);
}

.drag-icon {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-right: 1px solid #34b575 !important;
  img {
    filter: invert(53%) sepia(92%) saturate(368%) hue-rotate(99deg)
      brightness(94%) contrast(50%);
    height: 25px;
  }
  i {
    color: #34b575;
  }
}

.drag-text {
  font-size: 0.75em;
  font-weight: bold;
  color: #34b575;
  margin-left: 16px;
}
