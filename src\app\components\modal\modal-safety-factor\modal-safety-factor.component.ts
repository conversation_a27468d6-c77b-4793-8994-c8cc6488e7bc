import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild, ViewEncapsulation } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormControl, FormGroup } from '@angular/forms';

import { Conditions } from 'src/app/constants/stability.constants';
import { MessagePadroes } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';

import { StabilityService as StabilityServiceApi } from 'src/app/services/api/stability.service';

import fn from 'src/app/utils/function.utils';

//Para colocar a URL do arquivo .dxf como seguro
import * as moment from 'moment';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-modal-safety-factor',
  templateUrl: './modal-safety-factor.component.html',
  styleUrls: ['./modal-safety-factor.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalSafetyFactorComponent implements OnInit, OnChanges {
  @ViewChild('modalSafetyFactor') ModalSafetyFactor: ElementRef;
  @Input() public title: string = '';
  @Input() public sections: any = [];
  @Input() public surfaceType: number = 0;
  @Input() public calculationMethod: number = 0;
  @Input() public mapSliFileType: any = {};
  @Input() public sectionResults: any = {};
  @Input() public selectedSection: any = [];
  @Input() public selectedItem: any = {};

  @Output() modalClosed = new EventEmitter<void>();

  public formSafetyFactor: FormGroup = new FormGroup({
    SectionId: new FormControl(null),
    Conditions: new FormControl(''),
    CreatedDate: new FormControl({ value: '', disabled: true })
  });

  public controls: any = [];

  public sectionSettings = MultiSelectDefault.Sections;

  public conditions = Conditions;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public imagesFiles: any = {
    DXF: {
      file: null,
      fileContent: '',
      fileName: '',
      fileContentDownload: '',
      fileURL: ''
    },
    PNG: {
      file: null,
      fileContent: '',
      fileName: '',
      fileContentDownload: '',
      fileURL: ''
    },
    ZIP: {
      file: null,
      fileContent: '',
      fileName: '',
      fileContentDownload: '',
      fileURL: ''
    }
  };

  public createdDate = [];
  public sliFileType: number = 0;

  public imagesUrl: any = null;
  public ctrlShowImage: boolean = false;

  constructor(private modalService: NgbModal, private sanitizer: DomSanitizer, private stabilityServiceApi: StabilityServiceApi) {}

  ngOnInit(): void {
    this.controls = this.formSafetyFactor.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.selectedItem && changes.selectedItem.currentValue != null) {
      this.setFormValuesFromSelectedItem(changes.selectedItem.currentValue);
    }
  }

  // Define os valores do formulário a partir do item selecionado.
  setFormValuesFromSelectedItem(selectedItem: any): void {
    // Setar os valores do formulário
    this.controls['SectionId'].setValue([
      {
        id: selectedItem.id,
        name: selectedItem.name
      }
    ]);

    this.controls['Conditions'].setValue(selectedItem.safety_factor_result.condition.value);

    // Popula as datas antes de definir o CreatedDate
    this.selectedSection = [{ id: selectedItem.id, name: selectedItem.name }];
    this.sliFileType = selectedItem.safety_factor_result.sli_file_type;
    this.calculationMethod = selectedItem.safety_factor_result.calculation_method;
    this.extractDate();

    // Definir o valor de CreatedDate após a população das datas
    setTimeout(() => {
      this.controls['CreatedDate'].setValue(selectedItem.safety_factor_result.id);
    });

    // Chamar o método showImage
    this.showImage(selectedItem.safety_factor_result.id);
  }

  /**
   * Obtém o tipo de arquivo SLI com base na condição e no tipo de superfície.
   * @param {any} $condition - A condição selecionada.
   */
  getSliFileType($condition) {
    if ($condition !== '' && this.surfaceType > 0) {
      this.sliFileType = 0;
      const item = this.findItemBySurfaceTypeAndCondition($condition, this.surfaceType);
      if (item.value !== null) {
        this.sliFileType = item.value;
      }
    } else {
      this.sliFileType = 0;
    }
    this.extractDate();
  }

  /**
   * Extrai as datas de criação dos fatores de segurança.
   */
  extractDate() {
    this.createdDate = [];
    if (this.selectedSection.length > 0 && this.calculationMethod > 0 && this.sliFileType > 0) {
      const sectionId = this.selectedSection[0].id;
      this.createdDate = this.getSafetyFactorDates(this.sectionResults, sectionId, this.calculationMethod, this.sliFileType);
      if (this.createdDate.length == 0) {
        this.message.text = MessagePadroes.NoRegister;
        this.message.status = true;
        this.message.class = 'alert-warning';

        setTimeout(() => {
          this.message.status = false;
        }, 3000);
      }
    }

    this.controls['CreatedDate'].disable();
    this.controls['CreatedDate'].setValue('');

    this.reset();

    if (this.createdDate.length > 0) {
      this.controls['CreatedDate'].enable();
    }
  }

  /**
   * Obtém as datas dos fatores de segurança.
   *
   * @param {any} sectionResults - Os resultados das seções.
   * @param {string} sectionId - O ID da seção.
   * @param {number} calculationMethod - O método de cálculo.
   * @param {number} sliFileType - O tipo de arquivo SLI.
   * @returns {any[]} - As datas dos fatores de segurança.
   */
  getSafetyFactorDates(sectionResults, sectionId, calculationMethod, sliFileType) {
    let dates = [];
    sectionResults.forEach((section) => {
      if (section.id === sectionId) {
        section.stability_analysis_results.forEach((stabilityResult) => {
          stabilityResult.safety_factor_results.forEach((safetyFactor) => {
            //Se precisar comparar com metodo de cálculo, adicionar na condicao abaixo: /*safetyFactor.calculation_method === calculationMethod &&*/
            if (safetyFactor.sli_file_type === sliFileType) {
              const createdDate = safetyFactor.created_date;
              const formattedDate = moment(createdDate).format('DD/MM/YYYY HH:mm:ss');

              dates.push({
                id: safetyFactor.id,
                date: createdDate,
                date_format: formattedDate
              });
            }
          });
        });
      }
    });

    return dates;
  }

  /**
   * Define a seção selecionada.
   *
   * @param {any} section - A seção selecionada.
   * @param {string} [action='select'] - A ação a ser executada ('select' ou 'deselect').
   */
  setSelectedSection(section: any, action: string = 'select') {
    switch (action) {
      case 'select':
        this.selectedSection = [section];
        break;
      case 'deselect':
        this.selectedSection = [];
        break;
    }
    this.extractDate();
  }

  /**
   * Encontra um item pelo tipo de superfície e condição.
   *
   * @param {number} conditionId - O ID da condição.
   * @param {number} surfaceTypeId - O ID do tipo de superfície.
   * @returns {any} - O item encontrado.
   */
  findItemBySurfaceTypeAndCondition(conditionId: number, surfaceTypeId: number) {
    for (let key in this.mapSliFileType) {
      const item = this.mapSliFileType[key];
      if (item.surfaceTypeId === surfaceTypeId && item.conditionId === conditionId) {
        return item;
      }
    }
    return null;
  }

  /**
   * Encontra um item pelos parâmetros especificados.
   *
   * @param {any[]} items - A lista de itens.
   * @param {string} sectionId - O ID da seção.
   * @param {number} calculationMethod - O método de cálculo.
   * @param {number} sliFileType - O tipo de arquivo SLI.
   * @param {string} [dateTime] - A data e hora (opcional).
   * @returns {any | null} - O item encontrado ou null se não encontrado.
   */
  findItem(items: any[], sectionId: string, calculationMethod: number, sliFileType: number, dateTime?: string): any | null {
    for (const item of items) {
      if (item.id === sectionId) {
        for (const result of item.safety_factor_results) {
          if (result.calculation_method === calculationMethod && result.sli_file_type === sliFileType) {
            if (dateTime) {
              const resultDate = new Date(result.created_date).getTime();
              const inputDate = new Date(dateTime).getTime();
              if (resultDate === inputDate) {
                return result;
              }
            } else {
              return result;
            }
          }
        }
      }
    }
    return null;
  }

  /**
   * Encontra o resultado do fator de segurança pelo ID.
   *
   * @param {any} sectionResults - Os resultados das seções.
   * @param {string} safetyFactorResultId - O ID do resultado do fator de segurança.
   * @returns {any | null} - O resultado do fator de segurança encontrado ou null se não encontrado.
   */
  findSafetyFactorResultById(sectionResults, safetyFactorResultId) {
    for (const section of sectionResults) {
      const stabilityResult = section.stability_analysis_results.find((stabilityResult) =>
        stabilityResult.safety_factor_results.some((safetyFactor) => safetyFactor.id === safetyFactorResultId)
      );

      if (stabilityResult) {
        return stabilityResult.safety_factor_results.find((safetyFactor) => safetyFactor.id === safetyFactorResultId);
      }
    }
    return null; // Retorna null se não encontrar o item
  }

  /**
   * Exibe a imagem do fator de segurança.
   *
   * @param {any} $safetyFactorResultId - O ID do resultado do fator de segurança.
   */
  showImage($safetyFactorResultId) {
    this.ctrlShowImage = false;
    this.imagesUrl = this.findSafetyFactorResultById(this.sectionResults, $safetyFactorResultId);
    if (this.imagesUrl !== null) {
      this.ctrlShowImage = true;
      this.getFilePNGorDXF(this.imagesUrl.dxf_file_url, 'DXF');
    }
  }

  /**
   * Obtém um arquivo PNG ou DXF a partir de uma URL.
   *
   * @param {string} url - A URL do arquivo.
   * @param {string} [type='DXF'] - O tipo de arquivo (DXF ou PNG).
   * @param {boolean} [download=false] - Indica se o arquivo deve ser baixado.
   */
  getFilePNGorDXF(url, type = 'DXF', download = false) {
    this.message.text = '';
    this.message.status = false;

    url = url.replace('api/v1', '');

    this.stabilityServiceApi.getSafetyFactorId('', {}, url).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.loadDrawing(dados, type);
      if (download === true) {
        this.downloadFile(type);
      }
    });
  }

  /**
   * Obtém um arquivo ZIP a partir de uma URL e força o download.
   *
   * @param {string} url - A URL do arquivo ZIP.
   */
  getFileZip(url) {
    url = url.replace('api/v1', '');
    this.stabilityServiceApi.getStabilityAnalysisZipFile(url).subscribe((resp: any) => {
      if (resp['status'] == 200) {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.imagesFiles['ZIP'].file = new Blob([resp['body']], { type: resp['body'].type });
        const blob = new Blob([resp['body']], { type: resp['body'].type });
        const url = window.URL.createObjectURL(blob);
        this.imagesFiles['ZIP'].fileURL = url;
        this.imagesFiles['ZIP'].fileName = 'filename.zip';
        this.forceDownload(url, 'filename.zip');
      }
    });
  }

  /**
   * Carrega o desenho a partir de um base64 e define os atributos do arquivo.
   *
   * @param {any} drawing - O desenho em base64.
   * @param {string} [type=''] - O tipo de arquivo.
   */
  loadDrawing(drawing, type = '') {
    if (drawing != null) {
      const blob = fn.base64toBlob(drawing.base64);
      const url = window.URL.createObjectURL(blob);
      this.imagesFiles[type].fileURL = url;
      this.imagesFiles[type].fileContent = drawing.base64;
      this.imagesFiles[type].fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl(
        'data:application/octet-stream;base64,' + this.imagesFiles[type].fileContent
      );
      this.imagesFiles[type].fileName = drawing.name;

      this.imagesFiles[type].file = fn.base64ToFile(this.imagesFiles[type].fileContent, this.imagesFiles[type].fileName);
    }
  }

  /**
   * Baixa o arquivo do tipo especificado.
   *
   * @param {string} type - O tipo de arquivo a ser baixado.
   */
  downloadFile(type) {
    if (this.imagesFiles[type].fileName != '') {
      this.forceDownload(this.imagesFiles[type].fileURL, this.imagesFiles[type].fileName);
    } else {
      switch (type) {
        case 'DXF':
        case 'PNG':
          this.getFilePNGorDXF(this.imagesUrl[type.toLowerCase() + '_file_url'], type, true);
          break;
        case 'ZIP':
          this.getFileZip(this.imagesUrl['zip_file_download_url']);
          break;
      }
    }
  }

  /**
   * Reseta os valores do componente.
   *
   * @param {boolean} [$resetForm=false] - Indica se o formulário deve ser resetado.
   */
  reset($resetForm = false) {
    this.imagesUrl = null;
    this.ctrlShowImage = false;
    this.imagesFiles = {
      DXF: {
        file: null,
        fileContent: '',
        fileName: '',
        fileContentDownload: '',
        fileURL: ''
      },
      PNG: {
        file: null,
        fileContent: '',
        fileName: '',
        fileContentDownload: '',
        fileURL: ''
      },
      ZIP: {
        file: null,
        fileContent: '',
        fileName: '',
        fileContentDownload: '',
        fileURL: ''
      }
    };

    if ($resetForm) {
      this.createdDate = [];
      this.controls['CreatedDate'].disable();
      this.controls['CreatedDate'].setValue('');
      this.controls['SectionId'].setValue('');
      this.controls['Conditions'].setValue('');
    }
  }

  /**
   * Força o download de um arquivo com um nome especificado.
   *
   * @param {any} file - O arquivo a ser baixado.
   * @param {string} name - O nome do arquivo.
   * @returns {void}
   */
  forceDownload(file, name) {
    const link: any = document.createElement('a');
    link.href = file;
    link.download = name;

    document.body.appendChild(link);

    link.click();

    document.body.removeChild(link);
  }

  //Abre o modal do fator de segurança.
  openModal() {
    this.modalService.open(this.ModalSafetyFactor, { size: 'xl' });
  }

  //Fecha o modal do fator de segurança e reseta os valores.
  closeModal(): void {
    this.reset(true);
    this.modalClosed.emit();
  }
}
