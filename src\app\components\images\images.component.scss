.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.text-instructions {
  color: #032561;
}

.form-control {
  font-size: 0.875em;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.description-image {
  color: #032561;
  font-family: averta-bold;
  font-size: 0.875em;
}

.card-header {
  background-color: #34b575;
  color: #ffffff;
  font-size: 0.875em;
}

#image-album .modal-footer {
  display: block;
}

.thumb {
  margin-top: 15px;
  margin-bottom: 15px;
}

.img-item {
  max-width: 100%; // Garante que as imagens não excedam a largura do contêiner
  max-height: 150px; // Define a altura fixa desejada
  width: auto; // Permite que a largura se ajuste com base na proporção da imagem
  display: block; // Permite centralizar a imagem horizontalmente
  margin: 0 auto; // Centraliza a imagem horizontalmente no contêiner
}
