import { Injectable } from '@angular/core';
import { acl } from 'src/app/constants/permissions.constants';
import { UserService } from 'src/app/services/user.service';
import {
  MenuPrincipal,
  MenuAdmin,
  MenuEstabilidade,
  MenuInstrumentacao,
  MenuProfile,
  MiniDashboardUnit,
  MiniDashboardStructure,
  MiniDashboardSections,
  MiniDashboardInstrument,
  MiniDashboardInspectionSheet,
  MiniDashboardReports,
  MiniDashboardStaticMaterials,
  MiniDashboardStability,
  MiniDashboardSimulations
} from 'src/app/constants/rotas.constants';

/**
 * Serviço para gerenciar os menus da aplicação.
 *
 * @export
 * @class MenuService
 */
@Injectable({
  providedIn: 'root'
})
export class MenuService {
  public menus: any = [];

  /**
   * Cria uma instância de MenuService.
   * @param {UserService} userService - Serviço de usuário para obter perfil e permissões.
   */
  constructor(private userService: UserService) {
    this.menus['principal'] = MenuPrincipal;
    this.menus['admin'] = MenuAdmin;
    this.menus['menuEstabilidade'] = MenuEstabilidade;
    this.menus['menuProfile'] = MenuProfile;
    this.menus['miniDashboardUnit'] = MiniDashboardUnit;
    this.menus['miniDashboardStructure'] = MiniDashboardStructure;
    this.menus['miniDashboardSections'] = MiniDashboardSections;
    this.menus['miniDashboardInstrument'] = MiniDashboardInstrument;
    this.menus['miniDashboardInspectionSheet'] = MiniDashboardInspectionSheet;
    this.menus['menuInstrumentacao'] = MenuInstrumentacao;
    this.menus['miniDashboardStaticMaterials'] = MiniDashboardStaticMaterials;
    this.menus['MiniDashboardReports'] = MiniDashboardReports;
    this.menus['miniDashboardStability'] = MiniDashboardStability;
    this.menus['miniDashboardSimulations'] = MiniDashboardSimulations;
  }

  /**
   * Define o menu com base no tipo de menu e configurações fornecidas.
   * @param {string} [menuType=''] - O tipo de menu a ser definido.
   * @param {any} [config=null] - Configurações adicionais para o menu.
   * @returns {any[]} - O menu configurado.
   */
  public setMenu(menuType: string = '', config = null) {
    const permissoes = acl[this.userService.getProfile().description];
    let menu = [];
    let show = true;

    if (this.menus[menuType]) {
      this.menus[menuType].forEach((item, index) => {
        if (config !== null && item.Click !== null && config.hasOwnProperty(item.Click)) {
          show = config[item.Click];
        }
        if (show) {
          if (item.Icone && item.Icone.substring(item.Icone.length - 4) === '.svg') {
            item['IconeType'] = 'svg';
          }
          if (item.Rota) {
            if (permissoes[item.Rota]) {
              if (permissoes[item.Rota].create || permissoes[item.Rota].edit || permissoes[item.Rota].list) {
                menu.push(item);
              }
            } else {
              menu.push(item);
            }
          } else {
            // Para outros menus no futuro (subs)
            menu.push(item);
          }
          if (item.Separador && item.Separador == true) {
            item['Separador'] = true;
          } else {
            item['Separador'] = false;
          }
        }
        show = true;
      });
    }
    return menu;
  }
}
