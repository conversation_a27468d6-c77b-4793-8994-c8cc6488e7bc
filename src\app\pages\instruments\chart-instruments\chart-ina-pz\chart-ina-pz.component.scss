.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-control {
  border-color: #d4d2d2;
  font-size: 0.875em;
}

.form-select {
  font-size: 0.875em !important;
  line-height: 1.52857143 !important;
}

.card-header {
  background-color: #34b575;
  color: #ffffff;
  font-size: 0.875em;
  font-weight: 400;
}

.color {
  width: 40%;
  height: 35px;
  border-radius: 5px;
  border: 1px solid #ced4da;
  cursor: pointer;
}

.color-picker-container {
  position: absolute;
  z-index: 9999; /* Define um valor adequado para a ordem de empilhamento */
  top: 0;
  left: 0;
  background: white;
  border: 1px solid #ccc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 200px;
}

.range {
  width: 100%;
  margin-top: 4px;
  margin-bottom: 20px;
  cursor: pointer;
}
