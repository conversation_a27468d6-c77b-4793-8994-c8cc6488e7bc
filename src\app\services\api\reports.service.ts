import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class ReportsService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  getReportsById(id: string) {
    const url = `/reports/${id}`;
    return this.api.get<any>(url, {}, false, 'client');
  }

  postReports(params: any) {
    const url = `/reports`;
    return this.api.post<any>(url, params, {}, 'client');
  }

  putReports(id: string, params: any) {
    const url = `/reports/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  deleteReports(id: string) {
    const url = `/reports/${id}`;
    return this.api.delete<any>(url, 'client', {});
  }

  getReportsPending(params: any) {
    const url = `/reports/pending`;
    return this.api.get<any>(url, params, false, 'client');
  }

  getReportsList(params: any) {
    const url = '/reports/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  patchReports(id: string, params: any) {
    const url = `/reports/${id}/emission-dates`;
    return this.api.patch<any>(url, params, 'client');
  }
}
