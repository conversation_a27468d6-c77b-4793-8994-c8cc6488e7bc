<div class="list-content py-1" id="view-inspection-sheet" *ngIf="load !== null">
  <ng-container *ngIf="load && dados; else noData">
    <div>
      <div class="row mt-1 mb-3">
        <div class="col-md-3 d-flex justify-content-start">
          <img
            src="/assets/images/logoLogisoil.png"
            alt="Logisoil"
            title="Logisoil"
            class="logo-inspection"
          />
        </div>
        <div
          class="col-md-6 d-flex justify-content-center align-items-center fs-5 fw-bold"
        >
          Ocorrência {{ dados?.search_identifier }}
        </div>
        <div class="col-md-3 d-flex justify-content-end">
          <img
            *ngIf="!dados?.client?.logo?.content"
            src="/assets/ico/WL_logo_walm.svg"
            height="50"
          />
          <img
            *ngIf="dados?.client?.logo?.content"
            [src]="dados?.client?.logo?.content"
            height="50"
          />
        </div>
      </div>
      <div class="row mt-2 px-1">
        <div
          class="col-md-12 subheader d-flex align-items-center justify-content-start"
        >
          Dados cadastrais
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <label class="title d-block"> Ficha correspondente:</label>
          <span class="content d-block">
            {{ dados?.inspection_sheet_search_identifier }}
            <a
              href="#"
              class="alert-link"
              (click)="openInspectionSheet(dados?.inspection_sheet_id, $event)"
            >
              <i
                class="fa fa-external-link"
                style="color: #0d6efd"
                aria-hidden="true"
              ></i
            ></a>
          </span>
        </div>

        <div class="col-md-4">
          <label class="title d-block">Período de Inspeção</label>
          <span class="content d-block">{{ dados?.period }}</span>
        </div>

        <div class="col-md-4">
          <label class="title d-block">Status</label>
          <span class="content d-block">
            {{ dados?.occurrence_and_action_plan_status }}
          </span>
        </div>

        <div class="col-md-4">
          <label class="title d-block">Área</label>
          <span class="content d-block">{{ dados?.aspect?.area?.name }}</span>
        </div>

        <div class="col-md-4">
          <label class="title d-block"> Aspecto</label>
          <span class="content d-block">
            {{ dados?.aspect?.description }}
          </span>
        </div>

        <div class="col-md-4">
          <label class="title d-block"> Ocorrência?</label>
          <span class="content d-block">
            {{ dados?.response }}
          </span>
        </div>

        <div class="col-md-4">
          <label class="title d-block"> Coordenada E (m)</label>
          <span class="content d-block"> {{ dados?.northing }} </span>
        </div>

        <div class="col-md-4">
          <label class="title d-block"> Coordenada N (m)</label>
          <span class="content d-block"> {{ dados?.easting }} </span>
        </div>

        <div class="col-md-4">
          <label class="title d-block"> Data de abertura</label>
          <span class="content d-block"> {{ dados?.created_date }}</span>
        </div>
      </div>
    </div>

    <!-- Mapa -->
    <div class="row">
      <div class="col-md-12">
        <app-google-maps
          #mapOccurrence
          [id]="'mapOccurrence'"
        ></app-google-maps>
      </div>
    </div>

    <!-- Anexos da ocorrência -->
    <div class="row mt-2 px-1">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Anexos da ocorrência
      </div>

      <!-- Alerta -->
      <div
        *ngIf="dados?.occurrence_attachments?.length === 0"
        class="alert alert-warning d-flex align-items-center mt-2"
        role="alert"
      >
        <i class="fa fa-info-circle me-2"></i>
        A ocorrência não possui nenhum anexo.
      </div>
    </div>

    <div class="row px-1" *ngIf="dados?.occurrence_attachments.length > 0">
      <table
        class="table table-bordered table-striped table-hover align-middle"
      >
        <thead>
          <tr>
            <th>Anexo</th>
            <th class="text-center align-middle">Coordenadas</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="
              let attachment of occurrenceAttachmentsSanitized;
              let i = index
            "
          >
            <td>
              <img
                [src]="attachment.file.base64"
                alt="thumbnail"
                style="width: 80px; height: 80px; cursor: pointer"
                (click)="openFile(attachment.file.base64)"
              />
              <br />
              <span>{{ attachment.file.name }}</span>
              <br />
            </td>
            <td class="text-center align-middle">
              Coordenada N (m) {{ attachment.northing }}, <br />
              Coordenada E (m) {{ attachment.easting }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Registro histórico adaptado com timeline animada -->
    <div class="row mt-2 px-1">
      <div
        class="col-md-12 subheader d-flex align-items-center justify-content-start"
      >
        Registro histórico
      </div>
      <div
        class="col-md-12 mt-2"
        *ngIf="historicalOccurrences && historicalOccurrences.length === 0"
      >
        <div class="alert alert-warning d-flex align-items-center" role="alert">
          <i class="fa fa-info-circle me-2"></i>
          Nenhuma ocorrência anterior foi registrada para esta ficha.
        </div>
      </div>
    </div>

    <!-- Timeline -->
    <div class="timeline" *ngIf="historicalOccurrences?.length > 0">
      <div
        class="timeline-row"
        *ngFor="let item of historicalOccurrences; let i = index"
      >
        <!-- Data à direita quando i for par -->
        <div class="timeline-time left-time" *ngIf="i % 2 === 0">
          {{ item.created_date | date: 'HH:mm' }}
          <small>{{ item.created_date | date: 'dd/MM/yyyy' }}</small>
        </div>

        <!-- Ponto central -->
        <div class="timeline-dot"></div>

        <!-- Card à esquerda quando i for par -->
        <div class="timeline-content right" *ngIf="i % 2 === 0">
          <div class="card border rounded shadow-sm" style="max-width: 360px">
            <div class="card-body p-2">
              <div class="d-flex justify-content-between">
                <div>
                  Ocorrência {{ item.search_identifier }}
                  <a href="#" (click)="openOccurrence(item.id, $event)">
                    <i class="fa fa-external-link text-primary ms-1"></i>
                  </a>
                </div>
                <div>
                  Ficha {{ item.inspection_sheet_search_identifier }}
                  <a
                    href="#"
                    (click)="
                      openInspectionSheet(item.inspection_sheet_id, $event)
                    "
                  >
                    <i class="fa fa-external-link text-primary ms-1"></i>
                  </a>
                </div>
              </div>

              <div class="text-center fw-bold my-2">
                {{ i === 0 ? 'Registro inicial' : 'Observado novamente' }}
              </div>

              <div
                *ngIf="i === 0"
                class="d-flex align-items-center justify-content-between mb-1"
              >
                <div class="d-flex align-items-center">
                  <span class="me-1 square-icon"></span>
                  <small class="ms-1"
                    >A primeira ocorrência não possui plano de ação.</small
                  >
                </div>
              </div>

              <div
                *ngIf="item.remaining_days !== null"
                class="d-flex justify-content-between align-items-center mt-2"
              >
                <div class="d-flex align-items-center">
                  <span class="me-1 square-icon"></span>
                  <small class="ms-1"
                    >Dias restantes: {{ item.remaining_days }}</small
                  >
                </div>

                <div *ngIf="item.occurrence_attachments?.length">
                  <button
                    type="button"
                    class="btn btn-sm p-0 border-0 bg-transparent"
                    title="Visualizar anexos"
                  >
                    <img
                      src="https://cdn-icons-png.flaticon.com/512/685/685655.png"
                      alt="Ícone"
                      width="20"
                      height="20"
                    />
                  </button>
                </div>
              </div>

              <div
                class="mt-2 d-flex flex-wrap"
                *ngIf="item.occurrence_attachments?.length"
              >
                <div
                  *ngFor="let anexo of item.occurrence_attachments"
                  class="me-2 mb-2"
                  style="width: 80px"
                >
                  <img
                    [src]="anexo.file.base64"
                    class="img-thumbnail"
                    alt="Anexo"
                    style="cursor: pointer; width: 80px; height: 80px"
                    (click)="openFile(anexo.file.base64)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Card à direita quando i for ímpar -->
        <div class="timeline-content left" *ngIf="i % 2 !== 0">
          <div class="card border rounded shadow-sm" style="max-width: 360px">
            <div class="card-body p-2">
              <div class="d-flex justify-content-between">
                <div>
                  Ocorrência {{ item.search_identifier }}
                  <a href="#" (click)="openOccurrence(item.id, $event)">
                    <i class="fa fa-external-link text-primary ms-1"></i>
                  </a>
                </div>
                <div>
                  Ficha {{ item.inspection_sheet_search_identifier }}
                  <a
                    href="#"
                    (click)="
                      openInspectionSheet(item.inspection_sheet_id, $event)
                    "
                  >
                    <i class="fa fa-external-link text-primary ms-1"></i>
                  </a>
                </div>
              </div>

              <div class="text-center fw-bold my-2">
                {{ i === 0 ? 'Registro inicial' : 'Observado novamente' }}
              </div>

              <div
                *ngIf="i === 0"
                class="d-flex align-items-center justify-content-between mb-1"
              >
                <div class="d-flex align-items-center">
                  <span class="me-1 square-icon"></span>
                  <small class="ms-1"
                    >A primeira ocorrência não possui plano de ação.</small
                  >
                </div>
              </div>

              <div
                *ngIf="item.remaining_days !== null"
                class="d-flex justify-content-between align-items-center mt-2"
              >
                <div class="d-flex align-items-center">
                  <span class="me-1 square-icon"></span>
                  <small class="ms-1"
                    >Dias restantes: {{ item.remaining_days }}</small
                  >
                </div>

                <div *ngIf="item.occurrence_attachments?.length">
                  <button
                    type="button"
                    class="btn btn-sm p-0 border-0 bg-transparent"
                    title="Visualizar anexos"
                  >
                    <img
                      src="https://cdn-icons-png.flaticon.com/512/685/685655.png"
                      alt="Ícone"
                      width="20"
                      height="20"
                    />
                  </button>
                </div>
              </div>

              <div
                class="mt-2 d-flex flex-wrap"
                *ngIf="item.occurrence_attachments?.length"
              >
                <div
                  *ngFor="let anexo of item.occurrence_attachments"
                  class="me-2 mb-2"
                  style="width: 80px"
                >
                  <img
                    [src]="anexo.file.base64"
                    class="img-thumbnail"
                    alt="Anexo"
                    style="cursor: pointer; width: 80px; height: 80px"
                    (click)="openFile(anexo.file.base64)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Data à esquerda quando i for ímpar -->
        <div class="timeline-time right-time" *ngIf="i % 2 !== 0">
          {{ item.created_date | date: 'HH:mm' }}
          <small>{{ item.created_date | date: 'dd/MM/yyyy' }}</small>
        </div>
      </div>
    </div>
  </ng-container>

  <!-- Caso não tenha dados (204 ou vazio) -->
  <ng-template #noData>
    <div class="alert alert-warning mt-3" role="alert">
      Ocorrência não encontrada.
    </div>
  </ng-template>

  <!-- Voltar -->
  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/inspections']"
    ></app-button>
  </div>
</div>
