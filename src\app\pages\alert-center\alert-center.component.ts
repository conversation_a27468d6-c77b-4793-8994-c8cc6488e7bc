import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { TypeAlerts } from 'src/app/constants/app.constants';

import { UserService } from 'src/app/services/user.service';
import fn from 'src/app/utils/function.utils';

import * as moment from 'moment';
import { NotificationPeriod, NotificationTheme, NotificationThemeGroup } from 'src/app/constants/notifications.constants';
import { NotificationService } from 'src/app/services/notification.service';
import { MessagePadroes } from 'src/app/constants/message.constants';

@Component({
  selector: 'app-alert-center',
  templateUrl: './alert-center.component.html',
  styleUrls: ['./alert-center.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AlertCenterComponent implements OnInit {
  @ViewChild('hierarchy') hierarchy: any;

  public formAlertCenter: FormGroup = new FormGroup({
    type_alerts: new FormControl('', [Validators.required]),
    history_alerts_period: new FormControl('', [Validators.required]),
    start_date: new FormControl(''),
    end_date: new FormControl('')
  });

  public userNotificationsConfigurations: any = [];

  public notificationTheme = NotificationTheme;

  public instrumentsTabConfig: any = { styleColor: false, active: true };
  public readingsTabConfig: any = { styleColor: false, active: false };
  public stabilityTabConfig: any = { styleColor: false, active: false };
  public licenseTabConfig: any = { styleColor: false, active: false };
  public inspectionsTabConfig: any = { styleColor: false, active: false };
  public stabilityAnalysisTabConfig: any = { styleColor: false, active: false };
  public structuresTabConfig: any = { styleColor: false, active: false };

  public historyTabConfig: any = { styleColor: false, active: false };

  public formCrtl: boolean = false;

  public typeAlerts: any = TypeAlerts;
  public notificationHistoryPeriod: any = NotificationPeriod;

  public profile: any = null;
  public permissaoUsuario: any = null;

  public tabName: any = '';

  public selectedNotificationPeriod: number = 7;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public tableData: any = [];

  public tableHeader: any = [
    {
      label: 'Data/Hora',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Tema',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['theme']
    },
    {
      label: 'Mensagem',
      width: '65%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['message']
    }
  ];

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units'
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures'
    },
    structures: {
      single: true
    }
  };

  constructor(private router: Router, private userService: UserService, private notificationsService: NotificationService) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.formAlertCenter.controls['history_alerts_period'].setValue(this.notificationHistoryPeriod[0].value);
    this.getUserNotificationsConfigurations();
    this.formChanges();
  }

  formChanges() {
    // Observa as mudanças no campo history_alerts_period
    this.formAlertCenter.get('history_alerts_period').valueChanges.subscribe((value) => {
      if (value === 0) {
        // Se o valor for 0, ativamos as validações dos campos de data
        this.formAlertCenter.get('start_date').setValidators([Validators.required]);
        this.formAlertCenter.get('end_date').setValidators([Validators.required]);
      } else {
        // Se não for 0, removemos as validações
        this.formAlertCenter.get('start_date').clearValidators();
        this.formAlertCenter.get('end_date').clearValidators();
      }
      // Atualiza o estado de validação dos campos
      this.formAlertCenter.get('start_date').updateValueAndValidity();
      this.formAlertCenter.get('end_date').updateValueAndValidity();
    });
  }

  /**
   * Altera o nome da aba com base no índice encontrado em um array de objetos e carrega o histórico de notificações por período.
   * @returns {void}
   */
  changeTabName() {
    this.tabName = fn.findIndexInArrayofObject(TypeAlerts, 'value', this.formAlertCenter.controls['type_alerts'].value, 'value', true);
    this.loadNotificationsHistoryByPeriod();
  }

  /**
   * Seleciona uma aba específica com base na opção fornecida.
   * @param {any} [option=''] - A opção da aba a ser selecionada.
   * @returns {void}
   */
  selectTab(option: any = '') {
    switch (option) {
      case 'instruments':
        this.instrumentsTabConfig.active = true;
        this.readingsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.historyTabConfig.active = false;
        this.licenseTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityAnalysisTabConfig.active = false;
        this.structuresTabConfig.active = false;
        break;

      case 'readings':
        this.instrumentsTabConfig.active = false;
        this.readingsTabConfig.active = true;
        this.stabilityTabConfig.active = false;
        this.historyTabConfig.active = false;
        this.licenseTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityAnalysisTabConfig.active = false;
        this.structuresTabConfig.active = false;
        break;

      case 'stability':
        this.instrumentsTabConfig.active = false;
        this.readingsTabConfig.active = false;
        this.stabilityTabConfig.active = true;
        this.historyTabConfig.active = false;
        this.licenseTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityAnalysisTabConfig.active = false;
        this.structuresTabConfig.active = false;
        break;

      case 'history':
        this.instrumentsTabConfig.active = false;
        this.readingsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.historyTabConfig.active = true;
        this.licenseTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityAnalysisTabConfig.active = false;
        this.structuresTabConfig.active = false;
        break;

      case 'license':
        this.instrumentsTabConfig.active = false;
        this.readingsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.historyTabConfig.active = false;
        this.licenseTabConfig.active = true;
        this.inspectionsTabConfig.active = false;
        this.stabilityAnalysisTabConfig.active = false;
        this.structuresTabConfig.active = false;
        break;

      case 'inspections':
        this.instrumentsTabConfig.active = false;
        this.readingsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.historyTabConfig.active = false;
        this.licenseTabConfig.active = false;
        this.inspectionsTabConfig.active = true;
        this.stabilityAnalysisTabConfig.active = false;
        this.structuresTabConfig.active = false;
        break;

      case 'stabilityAnalysis':
        this.instrumentsTabConfig.active = false;
        this.readingsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.historyTabConfig.active = false;
        this.licenseTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityAnalysisTabConfig.active = true;
        this.structuresTabConfig.active = false;
        break;

      case 'structures':
        this.instrumentsTabConfig.active = false;
        this.readingsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.historyTabConfig.active = false;
        this.licenseTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityAnalysisTabConfig.active = false;
        this.structuresTabConfig.active = true;
        break;
      default:
        break;
    }
  }

  /**
   * Obtém os temas de notificação filtrados com base no nome da aba atual.
   * @returns {Array} - Uma lista de temas de notificação.
   */
  getNotificationThemesHistoryFilter() {
    switch (this.tabName.value) {
      case 1: //Instrumentacao
        return [
          NotificationTheme.InstrumentCreated,
          NotificationTheme.InstrumentUpdated,
          NotificationTheme.InstrumentDeleted,
          NotificationTheme.InstrumentDamaged,
          NotificationTheme.InstrumentRepaired
        ];
      case 2: //Leituras
        return [
          NotificationTheme.ReadingCreated,
          NotificationTheme.ReadingUpdated,
          NotificationTheme.ReadingDeleted,
          NotificationTheme.ControlLetterNewRegistry
        ];
      case 3: //Estabilidade
        return [NotificationTheme.SecurityLevelCreated, NotificationTheme.SecurityLevelAboveTolerance, NotificationTheme.DrainageAlert];
      case 4: //Licença Logisoil
        return [NotificationTheme.License];
      case 5: //Inspeções
        return [NotificationTheme.OverdueInspection];
      case 6: //Análise de estabilidade
        return [
          NotificationTheme.StabilityAnalysis,
          NotificationTheme.StabilityAnalysisCreated,
          NotificationTheme.StabilityAnalysisUpdated,
          NotificationTheme.StabilityAnalysisSafetyFactorBelowToleratedLevels
        ];
      case 7: //Estruturas
        return [NotificationTheme.StructureUpdated, NotificationTheme.StructureDeleted];
      default:
        return [];
    }
  }

  /**
   * Obtém o rótulo de um tema com base no valor do tema.
   * @param {number} themeValue - O valor do tema.
   * @returns {string} - O rótulo do tema.
   */
  getThemeLabel(themeValue: number): string {
    const theme = NotificationThemeGroup.find((theme) => theme.value === themeValue);
    return theme?.label || 'Tema desconhecido';
  }

  /**
   * Formata os dados da tabela, ajustando a data de criação e o tema, e simplificando a mensagem.
   * @returns {void}
   */
  formatData(): void {
    this.tableData = this.tableData.map((item: any) => {
      item.created_date = moment(item.created_date).format('DD/MM/YYYY HH:mm:ss');
      item.theme = this.getThemeLabel(item.theme);
      item.message = item.message.includes('@') ? item.message.split('@')[0] : item.message;
      return item;
    });
  }

  /**
   * Obtém a lista de notificações com base nos parâmetros definidos e atualiza os dados da tabela.
   * @returns {void}
   */
  getListNotifications(): void {
    this.message.text = '';
    this.message.status = false;

    let params = {
      Type: 3,
      Page: this.page,
      PageSize: this.pageSize,
      Period: this.selectedNotificationPeriod,
      Themes: this.getNotificationThemesHistoryFilter()
    };

    if (this.selectedNotificationPeriod == 0) {
      params['StartDate'] = this.formAlertCenter.get('start_date').value;
      params['EndDate'] = this.formAlertCenter.get('end_date').value;
    }

    this.notificationsService.getNotifications(params).subscribe({
      next: (resp) => {
        const dados: any = resp;
        if (dados.status == 200) {
          this.tableData = dados.body.data ? dados.body.data : [];
          this.collectionSize = dados.body.total_items_count;
          this.formatData();
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.message.text = MessagePadroes.NoNotifications;
          this.message.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.message.status = false;
          }, 4000);
        }
      },
      error: (error) => {
        console.log(error);
      }
    });
  }

  /**
   * Obtém as configurações de notificações do usuário.
   * @returns {void}
   */
  getUserNotificationsConfigurations(): void {
    this.notificationsService.fetchUserNotificationsConfigurations().subscribe({
      next: (resp) => {
        const dados: any = resp;
        if (dados) {
          this.userNotificationsConfigurations = dados.user_notification_configurations ? dados.user_notification_configurations : [];
        } else {
          this.userNotificationsConfigurations = [];
        }
      },
      error: (error) => {
        console.log(error);
      }
    });
  }

  /**
   * Carrega o histórico de notificações com base no período selecionado.
   * @returns {void}
   */
  loadNotificationsHistoryByPeriod(): void {
    let period = this.formAlertCenter.controls['history_alerts_period'].value;

    this.page = 1;
    this.selectedNotificationPeriod = period;

    if (period != 0) {
      this.formAlertCenter.get('start_date').setValue('');
      this.formAlertCenter.get('end_date').setValue('');
    }

    this.getListNotifications();
  }

  /**
   * Obtém a configuração do usuário para um determinado tema de notificação e tipo de notificação.
   *
   * @param {string} notificationType - O tipo de notificação ('banner' ou 'bell').
   * @param {any} notificationTheme - O tema da notificação.
   * @returns {boolean} - Verdadeiro se a configuração estiver ativa, falso caso contrário.
   */
  getUserConfiguration(notificationType: string, notificationTheme): boolean {
    let configuration = this.userNotificationsConfigurations.find((item: any) => item.notification_theme === notificationTheme);

    if (configuration) {
      return notificationType === 'banner' ? configuration.notification_banner : configuration.notification_bell;
    } else {
      return true;
    }
  }

  /**
   * Manipula a mudança de estado de uma checkbox e atualiza as configurações de notificações do usuário.
   *
   * @param {any} event - O evento de mudança da checkbox.
   * @param {any} notificationTheme - O tema da notificação.
   * @param {string} notificationType - O tipo de notificação ('banner' ou 'bell').
   * @returns {void}
   */
  onCheckboxChange(event: any, notificationTheme, notificationType: string): void {
    let configuration = this.userNotificationsConfigurations.find((item: any) => item.notification_theme === notificationTheme);

    if (configuration) {
      if (notificationType === 'banner') {
        configuration.notification_banner = event.target.checked;
      } else if (notificationType === 'bell') {
        configuration.notification_bell = event.target.checked;
      }
    } else {
      configuration = {
        notification_theme: notificationTheme,
        notification_bell: notificationType === 'bell' ? event.target.checked : true,
        notification_banner: notificationType === 'banner' ? event.target.checked : true
      };
      this.userNotificationsConfigurations.push(configuration);
    }

    this.notificationsService.setUserNotificationConfigurations(configuration);
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.pageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.page = page;
      }
    }

    this.getListNotifications();
  }

  /**
   * Navega de volta para a página inicial.
   *
   * @returns {void}
   */
  goBack() {
    this.router.navigate(['/']);
  }
}
