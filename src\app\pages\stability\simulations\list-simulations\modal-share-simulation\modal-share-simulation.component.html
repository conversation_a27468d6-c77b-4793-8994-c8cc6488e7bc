<ng-template #modalShareSimulation let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title" id="modal-share-simulation">
      <i class="fa fa-share-alt"></i>
      Compartilhar Simulação
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click'); resetForm()"
    ></button>
  </div>
  <form [formGroup]="formShareSimulation">
    <div class="modal-body">
      <div class="row">
        <div class="col-md-12">
          <label class="form-label">Nome da simulação:</label>
          <input
            type="text"
            formControlName="Name"
            class="form-control"
            maxlength="80"
          />
        </div>
      </div>
      <div class="row mt-2">
        <!-- Lista de usuários -->
        <div class="col-md-12">
          <label class="form-label">Compartilhar com:</label>
          <ng-multiselect-dropdown
            [placeholder]="'Selecione...'"
            [settings]="usersSettings"
            [data]="users"
            formControlName="UserId"
          >
          </ng-multiselect-dropdown>
        </div>
      </div>
    </div>
  </form>
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-red'"
      [label]="'Fechar'"
      (click)="c('Close click'); resetForm()"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-green'"
      [icon]="'fa fa-share-alt'"
      [label]="'Compartilhar'"
      [type]="false"
      (click)="getForm(); c('Close click')"
      *ngIf="controls['UserId'].value.length > 0"
    >
    </app-button>
  </div>
</ng-template>
