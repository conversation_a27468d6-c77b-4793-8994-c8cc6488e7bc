import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-chart',
  templateUrl: './modal-chart.component.html',
  styleUrls: ['./modal-chart.component.scss']
})
export class ModalChartComponent implements OnInit {
  @ViewChild('modalChart') ModalChart: ElementRef;
  @Input() public title: string = '';
  @Input() public instrumentInfo: any = null;

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  openModal() {
    this.modalService.open(this.Modal<PERSON>hart, { size: 'xl' });
  }
}
