import { After<PERSON>iewInit, ChangeDetectorRef, Component, ElementRef, Input, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';

import { DataService } from 'src/app/services/data.service';

import { DashboardService as DashboardServiceApi } from 'src/app/services/api/dashboard.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { UsersService as UsersServiceApi } from 'src/app/services/api/users.service';

import { MessagePadroes } from 'src/app/constants/message.constants';
import { groupInstruments, typeInstruments } from 'src/app/constants/instruments.constants';
import { LastestUpdateTypeIndex, LatestUpdateConstants } from 'src/app/constants/home.constants';

import { ECharts } from 'echarts';
import { EChartsComponent } from '@components/e-charts/e-charts.component';

import { GoogleMapsComponent } from '@components/google-maps/google-maps.component';

import { NgxSpinnerService } from 'ngx-spinner';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';
import { subMonths, format, endOfDay, startOfDay } from 'date-fns';

interface InstrumentMetrics {
  id: number;
  name: string;
  alias: string;
  count: number;
  background: string;
  text: string;
  border: string;
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit, AfterViewInit {
  @Input() mapStructure: any;

  @ViewChildren('chartFsRefs') chartRefs!: QueryList<EChartsComponent>;
  @ViewChild(GoogleMapsComponent, { static: false }) googleMaps!: GoogleMapsComponent;
  @ViewChild('mapWrapper', { static: false }) mapWrapperRef: ElementRef;
  @ViewChild('modalChartFs') ModalChartFs: any;

  // Mapa
  public dropdownOpen = false;
  public groupInstruments: any = groupInstruments;
  public legendItens: any = [];
  private mapInitialized = false;
  public sections: any[] = [];
  public structureName: string = '';
  public selectedItem: any = null;
  private _existingMarkerCoords = new Map<string, string>();
  private _loggedDuplicates = new Set<string>();

  public dataMapsStructure = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  //Gráfico FS
  public chartFS: any[] = [];
  public messagesErrorChartFS: any[] = [];
  public activeIndex = 0; // Índice do carrossel
  public hasNoSections = false;
  public hasInvalidCoordinates = false;

  // Seções e gráficos
  public sectionGraph: any = [];

  //Instrumentos ativos
  public structureId: string = '';
  public instruments: InstrumentMetrics[] = [];
  public totalInstruments: number = 0;
  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;
  public gridTemplateColumns: string = 'repeat(2, 1fr)';

  public latestUpdates: any = [];

  public rows = 2;
  public columns = 2;
  public maxWidth = 100;

  public filterHierarchy: any;
  public structure: any;
  public client: any;

  public dashboardLoaded = false;

  public isModalOpen = false;
  public titleModal: string = '';
  public dadosFS: any = null; // Se for reutilizar o mesmo objeto com section_result

  constructor(
    private dataService: DataService,
    private dashboardServiceApi: DashboardServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private sectionsServiceApi: SectionsServiceApi,
    public userServiceApi: UsersServiceApi,
    private cdr: ChangeDetectorRef
  ) {}

  /**
   * Inicializa o componente ao carregar.
   *
   * - Configura os atributos de atualização.
   * - Limpa e prepara os itens da legenda de instrumentos.
   */
  ngOnInit(): void {
    this.setUpdateAtributtes();
    // Inicializa a legenda com os tipos de instrumentos da API
    this.legendItens = [];

    this.loadFromStorage();
  }

  /**
   * Método do ciclo de vida Angular chamado após a inicialização da view.
   * Inicializa a instância do gráfico ECharts no elemento com o `id` fornecido
   * e aplica as opções definidas em `dataChart.options`.
   * Emite o evento `chartInit` com a instância do gráfico.
   */
  ngAfterViewInit(): void {
    this.mapInitialized = true;
    this.sendDataMap('all'); // Atualiza o mapa após inicialização
    this.cdr.detectChanges();

    setTimeout(() => {
      this.googleMaps?.triggerResize();
    }, 500);
  }

  /**
   * Atualiza atributos do usuário no sistema via API.
   */
  setUpdateAtributtes() {
    this.userServiceApi.patchUpdateAttributes({}).subscribe((resp) => {});
  }

  /**
   * Carrega informações da estrutura e do cliente a partir do localStorage.
   *
   * - Verifica se há dados salvos sob a chave `filterHierarchy`.
   * - Se houver, extrai os IDs do cliente e da estrutura.
   * - Se os dados forem válidos, define o nome da estrutura e carrega o mapa com a estrutura correspondente.
   */
  loadFromStorage(): void {
    this.filterHierarchy = localStorage.getItem('filterHierarchy');
    this.resetMap(true);

    if (this.filterHierarchy) {
      const filterData = JSON.parse(this.filterHierarchy);
      this.structure = filterData.StructureId;
      this.client = filterData.ClientId;

      if (this.structure && this.structure.id) {
        this.structureName = this.structure.name;
        this.loadSectionsAndInstruments(this.structure.id);
        this.loadInstrumentMetrics(this.structure.id);
        this.getLatestUpdates(this.structure.id);
        this.getHomeSafetyFactor(this.structure.id);
        this.getSectionsForChart(this.structure.id);
      }
    }
  }

  /**
   * Envia dados para atualização do mapa.
   *
   * - Verifica se o mapa foi inicializado antes de definir os dados.
   * - Caso o mapa não esteja pronto, aguarda 500ms e tenta novamente.
   *
   * @param option - Opção de atualização para o mapa (ex.: `'markers'`).
   */
  sendDataMap(option: string): void {
    if (this.mapInitialized && this.googleMaps) {
      this.googleMaps.setDataMap(this.dataMapsStructure, option, true);
    }
  }

  /**
   * Carrega todas as seções e instrumentos de uma estrutura específica no mapa,
   * atualizando as polylines, marcadores e centro da visualização.
   *
   * - Reseta o mapa e as polylines antes de carregar novos dados.
   * - Atualiza o centro com base nas coordenadas da estrutura.
   * - Gera e adiciona polylines para as seções com estilos personalizados.
   * - Cria marcadores SVG para cada instrumento e atualiza a legenda conforme os tipos.
   * - Dispara a atualização visual do mapa após o carregamento.
   *
   * @param {string} structureId - ID da estrutura a ser carregada.
   */
  loadSectionsAndInstruments(structureId: string): void {
    this.ngxSpinnerService.show();

    // Reseta dados do mapa
    this.resetMap();
    this.resetPolylines();

    if (!structureId) {
      console.error("Erro: 'Structure Id' não encontrado no localStorage.");
      this.ngxSpinnerService.hide();
      return;
    }

    this.dashboardServiceApi.getStructureMap({ StructureId: structureId }).subscribe(
      (resp: any) => {
        const data = resp?.body ?? resp;
        if (!data) {
          console.warn('⚠️ Nenhum dado retornado da API.');
          this.ngxSpinnerService.hide();
          return;
        }

        const { structure_coordinate, sections, instruments } = data;

        if (
          !structure_coordinate ||
          typeof structure_coordinate.latitude !== 'number' ||
          typeof structure_coordinate.longitude !== 'number' ||
          (structure_coordinate.latitude === 0 && structure_coordinate.longitude === 0)
        ) {
          console.warn('⚠️ Atenção! Estrutura com coordenadas nulas (0,0). Verificar dados.');
          this.hasNoSections = true;
          this.hasInvalidCoordinates = true;
          this.ngxSpinnerService.hide();
          return;
        }

        // Centraliza o mapa na estrutura
        this.dataMapsStructure.center = {
          lat: structure_coordinate.latitude,
          lng: structure_coordinate.longitude
        };

        // Adiciona marcador da estrutura
        const structureMarker = {
          position: this.dataMapsStructure.center,
          title: 'Estrutura',
          options: {}
        };
        this.dataMapsStructure.markers = [structureMarker];

        // Gera polylines das seções, se houver
        this.sections = Array.isArray(sections) ? sections : [];
        this.hasNoSections = this.sections.length === 0;

        if (this.sections.length > 0) {
          this.dataMapsStructure.polylines = this.sections.map((section) => ({
            path: [
              { lat: section.section_upstream_coordinate.latitude, lng: section.section_upstream_coordinate.longitude },
              ...(section.section_midpoint_coordinate
                ? [{ lat: section.section_midpoint_coordinate.latitude, lng: section.section_midpoint_coordinate.longitude }]
                : []),
              { lat: section.section_downstream_coordinate.latitude, lng: section.section_downstream_coordinate.longitude }
            ],
            strokeColor: section.map_line_setting.color,
            strokeOpacity: section.map_line_setting.type === 2 ? 0 : 1,
            strokeWeight: section.map_line_setting.width,
            icons: [
              {
                icon: {
                  path: 'M 0,-1 0,1',
                  strokeOpacity: section.map_line_setting.type === 2 ? 1 : 0,
                  scale: 4,
                  strokeWeight: section.map_line_setting.width
                },
                offset: '0',
                repeat: '20px'
              }
            ],
            id: section.id,
            name: section.name,
            infoWindowPolyline: {
              content: section.name,
              ariaLabel: section.name,
              id: section.id,
              data: section,
              contentConfig: []
            }
          }));
        } else {
          this.dataMapsStructure.polylines = [];
          this.chartFS = [];
          this.sectionGraph = [];
        }

        // Gera marcadores dos instrumentos, se houver
        const markers: any[] = [];
        const instrumentTypes = new Set<number>();

        if (Array.isArray(instruments) && instruments.length > 0) {
          instruments.forEach((instrument) => {
            const coordsKey = `${instrument.coordinate.latitude},${instrument.coordinate.longitude}`;
            const existing = this._existingMarkerCoords.get(coordsKey);

            // Cria uma chave de log ordenada, tipo "A|B"
            if (existing && existing !== instrument.identifier) {
              const orderedPair = [instrument.identifier, existing].sort().join('|');
              if (!this._loggedDuplicates.has(orderedPair)) {
                console.warn(`⚠️ Coordenadas duplicadas: "${instrument.identifier}" está no mesmo ponto de "${existing}"`);
                this._loggedDuplicates.add(orderedPair);
              }
            }

            this._existingMarkerCoords.set(coordsKey, instrument.identifier);

            const fillColor = fn.findIndexInArrayofObject(groupInstruments, 'type', instrument.type, 'color');
            const svgMarker = {
              path: 'M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0',
              fillColor,
              fillOpacity: 1.0,
              strokeWeight: 1.5,
              strokeColor: 'white',
              rotation: 0,
              scale: 0.3,
              anchor: new google.maps.Point(0, 0)
            };

            markers.push({
              position: {
                lat: instrument.coordinate.latitude,
                lng: instrument.coordinate.longitude
              },
              title: instrument.identifier,
              icon: svgMarker,
              options: {},
              id: 'mk-' + instrument.identifier,
              zIndex: -999,
              infoWindowMarker: {
                content: instrument.identifier,
                ariaLabel: instrument.identifier,
                id: instrument.identifier,
                data: instrument,
                contentConfig: []
              }
            });

            instrumentTypes.add(instrument.type);
          });
        }

        // Atualiza legenda e marcadores
        this.legendItens = groupInstruments.filter((item) => instrumentTypes.has(item.type));
        this.dataMapsStructure.markers.push(...markers);

        // Renderiza mapa conforme conteúdo
        if (this.dataMapsStructure.polylines.length > 0) {
          this.sendDataMap('polylinesMultiple');
        }
        if (markers.length > 0) {
          this.sendDataMap('markersMultiple');
        }
        if (this.dataMapsStructure.polylines.length === 0 && markers.length === 0) {
          this.sendDataMap('markers');
        }

        this.waitAndTriggerMapResize();
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.error('Erro ao buscar seções e instrumentos:', error);
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Reseta os marcadores do mapa antes de carregar novos dados.
   */
  resetMap(options = false): void {
    const marker0 = this.dataMapsStructure.markers.shift();
    this.dataMapsStructure.markers = [];

    this.dataMapsStructure.markers.push(marker0);

    if (options) {
      this.dataMapsStructure = {
        height: '500px',
        width: '100%',
        zoom: 16,
        center: { lat: -17.930178, lng: -43.7908453 },
        options: {
          mapTypeId: 'satellite',
          zoomControl: true,
          scrollwheel: true,
          disableDoubleClickZoom: true,
          maxZoom: 22,
          minZoom: 1
        },
        markers: [
          {
            position: {
              lat: -17.930178,
              lng: -43.7908453
            },
            title: '',
            options: {}
          }
        ],
        polylines: []
      };
    }

    this.sendDataMap('clearMarkers'); // 'clearMarkers' é um comando personalizado para limpar marcadores no mapa
  }

  /**
   * Reseta as polylines do mapa antes de carregar novos dados.
   */
  resetPolylines(): void {
    this.dataMapsStructure.polylines = [];
    this.sendDataMap('clearPolylines'); // 'clearPolylines' remove as polylinhas do mapa
  }

  /**
   * Alterna a exibição do dropdown de opções.
   */
  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  /**
   * Carrega as métricas dos instrumentos ativos para uma estrutura específica.
   *
   * - Faz requisição à API para buscar os dados de instrumentos.
   * - Se não houver dados, exibe mensagem padrão e limpa os instrumentos.
   * - Caso existam dados, processa os instrumentos para exibição no painel.
   * - Aplica cores, calcula layout em grid e atualiza totais.
   *
   * @param {string} structureId - ID da estrutura a ser consultada.
   */
  loadInstrumentMetrics(structureId): void {
    this.messagesError = [];

    const params = { StructureId: structureId };

    this.dashboardServiceApi.getInstrumentMetrics(params).subscribe(
      (resp: any) => {
        let dados = resp?.body ?? resp;

        // Se a resposta for 204 ou não houver dados, exibir a mensagem no padrão de array
        if (resp.status === 204 || !dados?.data || dados.data.length === 0) {
          this.instruments = [];
          this.totalInstruments = 0;

          this.messagesError = [{ message: MessagePadroes.NoInstrumentMetrics }];
          return;
        }

        // Se houver instrumentos, limpar mensagens de erro
        this.messagesError = [];

        this.instruments = dados.data
          .map((item) => {
            const instrument = typeInstruments.find((inst) => inst.id === item.type);
            if (instrument) {
              const colors = this.getColor(instrument.id);
              return {
                id: instrument.id,
                name: instrument.name,
                count: item.count,
                background: colors.background,
                text: colors.text,
                alias: instrument.alias,
                border: colors.border
              };
            }
            return null;
          })
          .filter(Boolean);

        this.totalInstruments = this.instruments.reduce((sum, i) => sum + i.count, 0);

        const { rows, columns } = fn.calculateGridSize(this.instruments.length);
        this.rows = rows;
        this.columns = columns;

        const baseWidth = 400; // Largura máxima quando há apenas 1 linha
        this.maxWidth = Math.max(baseWidth / this.rows, 50); // Garante um mínimo de 50px
      },
      (error) => {
        console.error('Erro ao buscar instrumentos ativos:', error);
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError: string) => {
            this.messagesError.push({ message: msgError });
          });
        }
      }
    );
  }

  /**
   * Retorna as cores de fundo, borda e texto associadas ao tipo de instrumento informado.
   *
   * - A cor de fundo é suavizada com transparência.
   * - A cor da borda é baseada na cor original do instrumento.
   * - A cor do texto é fixa para garantir legibilidade.
   *
   * @param {number} instrumentId - ID do tipo de instrumento.
   * @returns {{ background: string; border: string; text: string }} - Objeto com as cores configuradas.
   */
  getColor(instrumentId: number): { background: string; border: string; text: string } {
    const backgroundColors: { [key: number]: string } = {
      1: '#0000FF', // Blue
      2: '#FF0000', // Red
      3: '#FF6347', // Tomato
      4: '#FFFF00', // Yellow
      5: '#FFFF00', // Yellow
      6: '#800080', // Purple
      7: '#9370DB', // MediumPurple
      8: '#A52A2A', // Brown
      9: '#008000', // Green
      10: '#00FFFF', // Cyan
      12: '#008080', // Teal
      13: '#008080' // Teal
    };
    const border = backgroundColors[instrumentId] || '#808080'; // Cor exata da borda
    const background = this.applyAlpha(border, 0.25); // Suaviza o fundo
    const text = '#333'; // Texto escuro para ótima legibilidade

    return { background, border, text };
  }

  /**
   * Converte HEX em RGBA e aplica transparência.
   */
  applyAlpha(hex: string, alpha: number): string {
    const bigint = parseInt(hex.slice(1), 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return `rgba(${r}, ${g}, ${b}, ${alpha})`; // Retorna cor mais clara para fundo
  }

  /**
   * Recupera a lista de seções ativas vinculadas à estrutura especificada
   * e armazena na propriedade `sections`.
   *
   * @param {string} structureId - ID da estrutura associada.
   */
  getSectionsForChart(structureId: string): void {
    this.sectionsServiceApi.getSectionList({ structureId, active: true }).subscribe((resp: any) => {
      const dados = resp?.body ?? resp;
      this.sections = Array.isArray(dados) ? dados : [];
    });
  }

  /**
   * Obtém e processa o gráfico de Fator de Segurança da seção selecionada.
   * @param {any} section - Seção selecionada.
   * @param {string} [action='select'] - Define a ação de carregamento ou remoção.
   */
  getHomeSafetyFactor(structureId): void {
    if (this.dashboardLoaded) return;
    this.dashboardLoaded = true;
    this.ngxSpinnerService.show();
    this.messagesErrorChartFS = [];
    this.resetChartFS();

    const today = new Date();

    const params = {
      StructureId: structureId,
      StartDate: format(startOfDay(subMonths(today, 3)), 'yyyy-MM-dd HH:mm:ss'),
      EndDate: format(endOfDay(today), 'yyyy-MM-dd HH:mm:ss')
    };

    this.dashboardServiceApi.getStabilityChart(params).subscribe(
      (resp: any) => {
        if (resp && resp.status == 200) {
          let dataResp = resp;
          dataResp = dataResp.body === undefined ? dataResp : dataResp.body;
          const base = this.buildSectionGraphBase(dataResp);
          this.sectionGraph = Array.isArray(base) ? base : [];

          setTimeout(() => {
            this.initChartsFS();
          }, 100);
        }
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesErrorChartFS = [];
          error.error.forEach((msgError) => {
            this.messagesErrorChartFS.push(msgError);
          });
        }
      }
    );
  }

  /**
   * Retorna o gráfico da seção correspondente ao `sectionId`, se ele estiver presente em `sectionGraph`.
   * Caso o dashboard já tenha sido carregado (`dashboardLoaded`), interrompe a execução.
   *
   * @param {string} sectionId - ID da seção desejada.
   * @returns {any} - Objeto contendo os dados do gráfico da seção ou `undefined` se não encontrado.
   */
  getSectionGraphById(sectionId: string): any {
    if (this.dashboardLoaded) return;
    if (!Array.isArray(this.sectionGraph)) return null;
    return this.sectionGraph.find((g) => g.id === sectionId);
  }

  /**
   * Constrói a base de dados para exibição dos gráficos FS (Fator de Segurança) por seção.
   *
   * - Cada item do array de retorno representa uma seção e seus dados gráficos.
   * - Se a seção não tiver resultados de FS, retorna um gráfico vazio para ela.
   * - Caso `dashboardLoaded` esteja true, interrompe a construção.
   *
   * @param {any} dataResp - Objeto de resposta da API contendo `safety_factor_metric_results` e `sections`.
   * @returns {any[]} - Lista de objetos contendo dados gráficos por seção.
   */
  buildSectionGraphBase(dataResp: any): any[] {
    if (this.dashboardLoaded) return [];

    const safetyFactorMetricResults = dataResp.safety_factor_metric_results || [];
    const sections = Array.isArray(dataResp.sections) ? dataResp.sections : [];

    return sections.map((section) => {
      if (!section.safety_factor_results || section.safety_factor_results.length === 0) {
        return {
          id: section.id,
          name: section.name,
          chartFS: null,
          xAxisFS: [],
          yAxisFS: [],
          chartSeriesFS: []
        };
      }

      const dadosFS = {
        safety_factor_metric_results: safetyFactorMetricResults,
        section_result: {
          id: section.id,
          name: section.name,
          safety_factor_results: section.safety_factor_results
        }
      };

      const xAxisFS = this.constructXAxisFS(dadosFS);
      const { series, legends } = this.constructSeriesFS(dadosFS, xAxisFS);
      const yAxisFS = this.constructYAxisFS();
      const chartFS = {
        options: this.generateChartFS(section.name, xAxisFS, yAxisFS, series, legends)
      };

      return {
        id: section.id,
        name: section.name,
        chartFS,
        xAxisFS,
        yAxisFS,
        chartSeriesFS: series
      };
    });
  }

  /**
   * Constrói os dados do eixo X do gráfico de Fator de Segurança.
   * @param {any} data - Dados do gráfico.
   */
  constructXAxisFS(data): string[] {
    const dates = data.section_result.safety_factor_results.map((result) => moment(result.reading_created_date).format('DD/MM/YYYY'));
    const uniqueDates: string[] = Array.from(new Set(dates)) as string[];
    return uniqueDates.sort((a, b) => moment(a, 'DD/MM/YYYY').diff(moment(b, 'DD/MM/YYYY')));
  }

  /**
   * Constrói as séries de dados do gráfico de Fator de Segurança.
   * @param {any} data - Dados do gráfico.
   */
  constructSeriesFS(data, xAxisFS: string[]): { series: any[]; legends: string[] } {
    const initializeDateObject = (dates) => {
      return dates.reduce((acc, date) => {
        acc[date] = null;
        return acc;
      }, {});
    };

    const legends: string[] = [];
    const series: any[] = [];

    const CircularDrainedSeries = { ...initializeDateObject(xAxisFS) };
    const CircularUndrainedSeries = { ...initializeDateObject(xAxisFS) };
    const CircularPseudoStaticSeries = { ...initializeDateObject(xAxisFS) };
    const NonCircularDrainedSeries = { ...initializeDateObject(xAxisFS) };
    const NonCircularUndrainedSeries = { ...initializeDateObject(xAxisFS) };
    const NonCircularPseudoStaticSeries = { ...initializeDateObject(xAxisFS) };

    data.section_result.safety_factor_results.forEach((safetyResult) => {
      const date = moment(safetyResult.reading_created_date).format('DD/MM/YYYY');
      const value = safetyResult.value;
      switch (safetyResult.sli_file_type) {
        case 1:
          CircularDrainedSeries[date] = { value, safetyResult };
          break;
        case 2:
          CircularUndrainedSeries[date] = { value, safetyResult };
          break;
        case 3:
          CircularPseudoStaticSeries[date] = { value, safetyResult };
          break;
        case 4:
          NonCircularDrainedSeries[date] = { value, safetyResult };
          break;
        case 5:
          NonCircularUndrainedSeries[date] = { value, safetyResult };
          break;
        case 6:
          NonCircularPseudoStaticSeries[date] = { value, safetyResult };
          break;
      }
    });

    const addSeries = (type, dataMap, color) => {
      const itemSeries = {
        name: type,
        type: 'line',
        allData: Object.values(dataMap),
        data: Object.values(dataMap).map((item: any) => (item ? item.value : null)),
        itemStyle: { color },
        connectNulls: true,
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 7,
        hoverAnimation: false,
        sampling: 'none'
      };
      legends.push(type);
      series.push(itemSeries);
    };

    const seriesData = [
      { name: 'Circular Drenada', data: CircularDrainedSeries, color: '#1F77B4' },
      { name: 'Circular Não Drenada', data: CircularUndrainedSeries, color: '#9467BD' },
      { name: 'Circular Pseudo Estática', data: CircularPseudoStaticSeries, color: '#7F7F7F' },
      { name: 'Não Circular Drenada', data: NonCircularDrainedSeries, color: '#5DA5E0' },
      { name: 'Não Circular Não Drenada', data: NonCircularUndrainedSeries, color: '#B491E4' },
      { name: 'Não Circular Pseudo Estática', data: NonCircularPseudoStaticSeries, color: '#B0B0B0' }
    ];

    seriesData.forEach(({ name, data, color }) => {
      if (!Object.values(data).every((value) => value === null)) {
        addSeries(name, data, color);
      }
    });

    const referenceValues = Array.from(new Set(data.safety_factor_metric_results.map((item) => item.reference_value)));
    const dates = xAxisFS.map(() => referenceValues);

    referenceValues.forEach((rawValue) => {
      const value = Number(rawValue); // força ser number

      const dashedSeries = {
        name: `FS = ${value}`,
        type: 'line',
        data: dates,
        lineStyle: {
          type: 'dashed',
          color: this.getInitialMarkLineColor(value)
        },
        showSymbol: false,
        markLine: {
          data: [
            {
              yAxis: value,
              label: { formatter: `FS = ${value}`, position: 'insideEndTop' }
            }
          ],
          lineStyle: {
            type: 'dashed',
            color: this.getInitialMarkLineColor(value),
            width: 3
          },
          symbol: 'none'
        }
      };
      series.push(dashedSeries);
    });

    return { series, legends };
  }

  /**
   * Retorna a cor inicial da linha de referência (markLine) com base no valor do Fator de Segurança (FS).
   *
   * - `1.1` → vermelho (`#ff0000`)
   * - `1.3` → laranja (`#ff7f00`)
   * - `1.5` → amarelo (`#ffff00`)
   * - Qualquer outro valor → preto (`#000000`)
   *
   * @param {number} value - Valor do Fator de Segurança da linha de referência.
   * @returns {string} Código hexadecimal da cor correspondente à linha de referência.
   */
  getInitialMarkLineColor(value: number): string {
    if (value === 1.1) return '#ff0000'; // vermelho
    if (value === 1.3) return '#ff7f00'; // laranja
    if (value === 1.5) return '#ffff00'; // amarelo
    return '#000000'; // fallback
  }

  /**
   * Configura os dados do eixo Y para exibição no gráfico de Fator de Segurança.
   */
  constructYAxisFS(): any {
    return {
      name: '',
      type: 'value',
      axisLine: { show: true },
      nameRotate: 90,
      nameLocation: 'center',
      nameGap: 55,
      nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
      alignTicks: true,
      axisLabel: {
        formatter: (value, index) => value.toFixed(1)
      },
      interval: 0.5,
      show: true
    };
  }

  /**
   * Gera a configuração completa do gráfico ECharts com base nos dados fornecidos.
   *
   * @param {string} sectionName - Nome da seção para título do gráfico.
   * @param {string[]} xAxisFS - Datas para o eixo X.
   * @param {any} yAxisFS - Configuração do eixo Y.
   * @param {any[]} chartSeriesFS - Séries do gráfico.
   * @param {string[]} chartLegendsFS - Legendas exibidas.
   * @returns {any} - Objeto de configuração do gráfico.
   */
  generateChartFS(sectionName: string, xAxisFS: string[], yAxisFS: any, chartSeriesFS: any[], chartLegendsFS: string[]): any {
    return {
      title: '',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: (params) => (typeof params.value === 'number' ? params.value.toFixed(2) : params.value)
          }
        }
      },
      legend: {
        data: chartLegendsFS,
        icon: 'rect',
        left: 'center',
        top: 'bottom'
      },
      grid: {
        containLabel: true,
        top: 50,
        left: 50,
        right: 50,
        height: 300
      },
      toolbox: {
        feature: {
          dataZoom: { yAxisIndex: 'none' },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisFS,
        axisLabel: {
          interval: Math.floor(xAxisFS.length / 35),
          rotate: 60
        }
      },
      yAxis: yAxisFS,
      series: chartSeriesFS
    };
  }

  /**
   * Inicializa os eventos de clique nos marcadores e resize nos gráficos FS de todas as seções.
   * Garante que cada gráfico seja configurado apenas uma vez.
   */
  initChartsFS(): void {
    if (!Array.isArray(this.sectionGraph) || this.sectionGraph.length === 0) return;

    this.chartRefs.forEach((chartComp, index) => {
      const section = this.sectionGraph[index];
      const chartInstance = chartComp.chartInstance;

      if (chartInstance && !section.chartFS?.initialized) {
        this.initMarkerClickEventFS(chartInstance, section);
        section.chartFS.initialized = true;
        chartInstance.resize();
      }
    });
  }

  /**
   * Inicializa os eventos de clique e mudança de legenda no gráfico de Fator de Segurança (FS).
   *
   * - O evento `click` permite extrair os dados detalhados do ponto clicado e exibir o modal associado.
   * - O evento `legendselectchanged` ajusta dinamicamente as cores das linhas de referência (`markLine`)
   *   com base nas legendas visíveis, conforme regras específicas para drenagem, pseudo-estática e não drenada.
   *
   * @param {ECharts} chartInstance - Instância do gráfico ECharts.
   * @param {any} section - Objeto da seção associada ao gráfico.
   */
  initMarkerClickEventFS(chartInstance: ECharts, section: any): void {
    chartInstance.off('click');
    chartInstance.off('legendselectchanged');

    chartInstance.on('click', (params: any) => {
      if (params.componentType === 'series' && params.seriesType === 'line') {
        const series = chartInstance.getOption().series[params.seriesIndex];
        const allData = series.allData[params.dataIndex];
        const clickedData = this.extractDataFSBySection(params.seriesIndex, params.dataIndex, allData, section);
        this.handleMarkerClickFS(clickedData, section);
      }
    });

    const updateColor = (event) => {
      if (!chartInstance?.getOption()?.series) return;

      const { selected } = event;
      const activeLegends = Object.keys(selected).filter((key) => selected[key]);

      const isDrenada = activeLegends.some((legend) => legend.includes('Drenada'));
      const isNaoDrenada = activeLegends.some((legend) => legend.includes('Não Drenada'));
      const isPseudoEstatica = activeLegends.some((legend) => legend.includes('Pseudo Estática'));

      const currentSeries: any = chartInstance.getOption().series ?? [];

      const updatedSeries = currentSeries.map((series) => {
        if (series.markLine) {
          const value = series.markLine?.data?.[0]?.yAxis;
          const labelText = `FS = ${value}`;
          series.markLine.data[0].label.formatter = labelText;

          if (value === 1.5) {
            if (isDrenada && !isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ffff00';
            } else if (isDrenada && (isNaoDrenada || isPseudoEstatica)) {
              series.markLine.lineStyle.color = '#000000';
            } else {
              series.markLine.lineStyle.color = 'transparent';
            }
          }

          if (value === 1.3) {
            if (isDrenada && !isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ff7f00';
            } else if (!isDrenada && isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ff0000';
            } else if ((isNaoDrenada && (isDrenada || isPseudoEstatica)) || (!isNaoDrenada && isDrenada && isPseudoEstatica)) {
              series.markLine.lineStyle.color = '#000000';
            } else {
              series.markLine.lineStyle.color = 'transparent';
            }
          }

          if (value === 1.1) {
            if (isDrenada && !isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ff0000';
            } else if (!isDrenada && isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ff7f00';
            } else if (!isDrenada && !isNaoDrenada && isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ffff00';
            } else if ((isPseudoEstatica && (isDrenada || isNaoDrenada)) || (isDrenada && isNaoDrenada && !isPseudoEstatica)) {
              series.markLine.lineStyle.color = '#000000';
            } else {
              series.markLine.lineStyle.color = 'transparent';
            }
          }

          if (series.markLine.lineStyle.color === 'transparent') {
            series.markLine.data[0].label.formatter = '';
          }
        }
        return series;
      });

      const fullOption = chartInstance.getOption();
      fullOption.series = updatedSeries;
      chartInstance.setOption(fullOption, false); // Garante que NÃO vai sobrescrever nada
      chartInstance.resize(); // Força re-render com o novo estilo
    };

    chartInstance.on('legendselectchanged', (event: any) => {
      updateColor(event);
    });

    setTimeout(() => {
      const legendOption = chartInstance.getOption()?.legend as any;
      const initialLegendData = Array.isArray(legendOption) ? legendOption[0]?.data ?? [] : legendOption?.data ?? [];
      const initialSelected: Record<string, boolean> = initialLegendData.reduce((acc, legend) => {
        acc[legend] = true;
        return acc;
      }, {});
      updateColor({ selected: initialSelected });
    }, 50);
  }

  /**
   * Extrai os dados de Fator de Segurança (FS) do ponto clicado no gráfico para a seção informada.
   *
   * @param {number} seriesIndex - Índice da série clicada no gráfico.
   * @param {number} dataIndex - Índice do ponto clicado na série.
   * @param {any} allData - Objeto completo de dados associado ao ponto clicado.
   * @param {any} section - Objeto da seção atual do gráfico.
   * @returns {any} Objeto contendo `id`, `name` da seção e o resultado de FS (`safety_factor_result`).
   */
  extractDataFSBySection(seriesIndex: number, dataIndex: number, allData: any, section: any): any {
    return {
      id: section.id,
      name: section.name,
      safety_factor_result: allData
    };
  }

  /**
   * Lida com o clique em um ponto do gráfico FS, abrindo o modal detalhado com os dados da seção clicada.
   *
   * - Define o item selecionado (`selectedItem`) com os dados clicados.
   * - Atualiza o título do modal com o nome da estrutura e seção.
   * - Abre o modal após um pequeno delay para garantir renderização correta.
   *
   * @param {any} eventData - Dados extraídos do gráfico ao clicar no ponto.
   * @param {any} section - Objeto da seção associada ao ponto clicado.
   */
  handleMarkerClickFS(eventData: any, section): void {
    if (!this.isModalOpen && eventData) {
      this.isModalOpen = true;
      this.selectedItem = eventData;

      this.titleModal = `- Estrutura: ${this.structureName} | Seção: ${section.name}`;

      setTimeout(() => {
        this.ModalChartFs.openModal();
        this.ModalChartFs.modalClosed.subscribe(() => {
          this.isModalOpen = false;
        });
      }, 50);
    }
  }

  /**
   * Reseta completamente os dados e gráficos do FS na Dashboard.
   *
   * - Limpa os arrays `chartFS` e `sectionGraph`.
   * - Destroi os gráficos ECharts existentes para liberar memória.
   * - Reseta o índice ativo e a flag de carregamento da dashboard (`dashboardLoaded`).
   */
  resetChartFS(): void {
    this.chartFS = [];
    this.sectionGraph = [];
    this.chartRefs?.forEach((chart) => {
      chart?.chartInstance?.dispose(); // Libera memória dos gráficos antigos
    });
    this.activeIndex = 0;
    this.dashboardLoaded = false;
  }

  /**
   * Navega para o gráfico anterior.
   */
  prevChart(): void {
    if (this.sections.length > 1) {
      this.activeIndex = (this.activeIndex - 1 + this.sections.length) % this.sections.length;
    }
  }

  /**
   * Navega para o próximo gráfico.
   */
  nextChart(): void {
    if (this.sections.length > 1) {
      this.activeIndex = (this.activeIndex + 1) % this.sections.length;
    }
  }

  /**
   * Busca as últimas atualizações associadas à estrutura informada.
   * Trata os dados retornados pela API e os adapta para exibição no painel.
   *
   * @param {string} structureId - ID da estrutura para buscar as atualizações.
   */
  getLatestUpdates(structureId): void {
    this.messagesError = [];

    const params = { StructureId: structureId };

    this.dashboardServiceApi.getLatestUpdates(params).subscribe(
      (resp: any) => {
        let dados = resp?.body ?? resp;
        // Se a resposta for 204 ou não houver dados, exibir a mensagem no padrão de array
        if (resp.status === 204 || !dados?.data || dados.data.length === 0) {
          this.messagesError = [{ message: MessagePadroes.NoInstrumentMetrics }];
          return;
        }
        // Se houver instrumentos, limpar mensagens de erro
        this.messagesError = [];
        this.latestUpdates = dados.data.map((item) => {
          const typeKey = parseInt(item.type, 10); // Converte `type` para inteiro
          const enumKey = LastestUpdateTypeIndex[typeKey] as keyof typeof LatestUpdateConstants; // Obtém a chave correspondente

          return {
            title: LatestUpdateConstants[enumKey] || 'Tipo desconhecido',
            subtitle: item.title || 'Sem título',
            data: item.date ? new Date(item.date).toLocaleDateString('pt-BR') : 'Sem data'
          };
        });
      },
      (error) => {
        console.error('Erro nas últimas atualizações:', error);
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError: string) => {
            this.messagesError.push({ message: msgError });
          });
        }
      }
    );
  }

  waitAndTriggerMapResize(): void {
    const MAX_ATTEMPTS = 10;
    let attempts = 0;

    const interval = setInterval(() => {
      const wrapperVisible = this.mapWrapperRef?.nativeElement?.offsetHeight > 0;
      const mapReady = !!this.googleMaps?.newMap;

      if (wrapperVisible && mapReady) {
        clearInterval(interval);
        google.maps.event.trigger(this.googleMaps.newMap, 'resize');
        this.googleMaps.newMap.setCenter(this.dataMapsStructure.center); // Reaplica centro
      }

      if (++attempts >= MAX_ATTEMPTS) {
        clearInterval(interval);
        console.warn('[Google Maps] Trigger resize falhou após várias tentativas.');
      }
    }, 300);
  }
}
