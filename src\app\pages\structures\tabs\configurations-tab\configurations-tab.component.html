<form [formGroup]="formConfigurations">
  <div class="row">
    <div class="col-md-12">
      <small
        class="form-text text-danger"
        *ngIf="
          !formConfigurations.get('surface_type').valid &&
          formConfigurations.get('surface_type').value == 0 &&
          formConfigurations.get('surface_type').touched
        "
        >Selecione ao menos um tipo de Superfície (Circular ou Não
        Circular)</small
      >
    </div>
  </div>
  <div class="row mt-2">
    <!-- Tipo de superfície - Circular  -->
    <div class="col-md-3">
      <label class="form-label">Tipo de superfície - Circular</label>
      <select
        class="form-select"
        formControlName="surface_type_circular"
        #surfaceTypeCircularSelect
        (change)="getSurfacesTypesMethods('surface_type_circular')"
      >
        <option value="">Selecione o método de busca</option>
        <option
          *ngFor="
            let item of surfacesTypes['surface_type_circular'].search;
            let i = index
          "
          [ngValue]="i"
        >
          {{ item.method }}
        </option>
      </select>
    </div>
    <!-- Circular - Método de cálculo -->
    <div class="col-md-3">
      <label class="form-label">Método de cálculo</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="dropdownSettings"
        [data]="calculationMethods"
        formControlName="calculation_methods_circular"
        [required]="true"
        (click)="
          formConfigurations.get('calculation_methods_circular').markAsTouched()
        "
        [ngClass]="{
          'disabled-dropdown': formConfigurations.get(
            'calculation_methods_circular'
          )?.disabled
        }"
      >
      </ng-multiselect-dropdown>
      <small
        class="form-text text-danger"
        *ngIf="
          formConfigurations.get('calculation_methods_circular').value.length ==
            0 &&
          formConfigurations.get('calculation_methods_circular').touched &&
          !formConfigurations.get('calculation_methods_circular').valid
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Tipo de superfície - Não Circular -->
    <div class="col-md-3">
      <label class="form-label">Tipo de superfície - Não Circular</label>
      <select
        class="form-select"
        formControlName="surface_type_non_circular"
        #surfaceTypeNonCircularSelect
        (change)="getSurfacesTypesMethods('surface_type_non_circular')"
      >
        <option value="">Selecione o método de busca</option>
        <option
          *ngFor="
            let item of surfacesTypes['surface_type_non_circular'].search;
            let i = index
          "
          [ngValue]="i"
        >
          {{ item.method }}
        </option>
      </select>
    </div>
    <!-- Não Circular - Método de cálculo  -->
    <div class="col-md-3">
      <label class="form-label">Método de cálculo</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="dropdownSettings"
        [data]="calculationMethods"
        formControlName="calculation_methods_non_circular"
        [required]="true"
        (click)="
          formConfigurations
            .get('calculation_methods_non_circular')
            .markAsTouched()
        "
        [ngClass]="{
          'disabled-dropdown': formConfigurations.get(
            'calculation_methods_non_circular'
          )?.disabled
        }"
      >
      </ng-multiselect-dropdown>
      <small
        class="form-text text-danger"
        *ngIf="
          formConfigurations.get('calculation_methods_non_circular').value
            .length == 0 &&
          formConfigurations.get('calculation_methods_non_circular').touched &&
          !formConfigurations.get('calculation_methods_non_circular').valid
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <div class="row mt-2">
    <!-- Parâmetros circulares - dinâmico -->
    <div class="col-sm-6">
      <div class="row">
        <div class="col-sm-12">
          <div
            class="card"
            *ngIf="methodsSelected.surface_type_circular.subtitle != null"
          >
            <div class="card-header">
              {{ methodsSelected.surface_type_circular.title }} -
              {{ methodsSelected.surface_type_circular.subtitle }}
            </div>
            <div class="card-body">
              <div class="row">
                <div
                  class="col-6"
                  *ngFor="
                    let surfaceTypeField of methodsSelected
                      .surface_type_circular.fields;
                    let i = index
                  "
                >
                  {{ surfacesTypeMethodsFields[surfaceTypeField].name }}
                  <input
                    type="number"
                    [min]="surfacesTypeMethodsFields[surfaceTypeField].min"
                    [max]="surfacesTypeMethodsFields[surfaceTypeField].max"
                    [maxlength]="
                      surfacesTypeMethodsFields[surfaceTypeField].length
                    "
                    [step]="surfacesTypeMethodsFields[surfaceTypeField].step"
                    (keypress)="
                      func.controlNumber(
                        $event,
                        formConfigurations.get('circular_' + surfaceTypeField),
                        surfacesTypeMethodsFields[surfaceTypeField].option
                      )
                    "
                    (keyup)="
                      func.controlNumber(
                        $event,
                        formConfigurations.get('circular_' + surfaceTypeField)
                      )
                    "
                    class="form-control"
                    [formControlName]="'circular_' + surfaceTypeField"
                    [required]="true"
                  />
                  <small
                    class="form-text text-danger"
                    *ngIf="
                      !formConfigurations.get('circular_' + surfaceTypeField)
                        .valid &&
                      formConfigurations.get('circular_' + surfaceTypeField)
                        .touched
                    "
                  >
                    Campo Obrigatório.
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Parâmetros não circulares - dinâmico -->
    <div class="col-sm-6">
      <div class="row">
        <div class="col-sm-12">
          <div
            class="card"
            *ngIf="methodsSelected.surface_type_non_circular.subtitle != null"
          >
            <div class="card-header">
              {{ methodsSelected.surface_type_non_circular.title }} -
              {{ methodsSelected.surface_type_non_circular.subtitle }}
            </div>
            <div class="card-body">
              <div class="row">
                <div
                  class="col-6"
                  *ngFor="
                    let surfaceTypeField of methodsSelected
                      .surface_type_non_circular.fields;
                    let i = index
                  "
                >
                  {{ surfacesTypeMethodsFields[surfaceTypeField].name }}
                  <input
                    type="number"
                    [min]="surfacesTypeMethodsFields[surfaceTypeField].min"
                    [max]="surfacesTypeMethodsFields[surfaceTypeField].max"
                    [maxlength]="
                      surfacesTypeMethodsFields[surfaceTypeField].length
                    "
                    [step]="surfacesTypeMethodsFields[surfaceTypeField].step"
                    (keypress)="
                      func.controlNumber(
                        $event,
                        formConfigurations.get(
                          'non_circular_' + surfaceTypeField
                        ),
                        surfacesTypeMethodsFields[surfaceTypeField].option
                      )
                    "
                    (keyup)="
                      func.controlNumber(
                        $event,
                        formConfigurations.get(
                          'non_circular_' + surfaceTypeField
                        )
                      )
                    "
                    class="form-control"
                    [formControlName]="'non_circular_' + surfaceTypeField"
                    [required]="true"
                  />
                  <small
                    class="form-text text-danger"
                    *ngIf="
                      !formConfigurations.get(
                        'non_circular_' + surfaceTypeField
                      ).valid &&
                      formConfigurations.get('non_circular_' + surfaceTypeField)
                        .touched
                    "
                  >
                    Campo Obrigatório.
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
