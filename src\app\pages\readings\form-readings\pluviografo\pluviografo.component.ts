import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';
import { fieldsReading } from 'src/app/constants/readings.constants';

import fn from 'src/app/utils/function.utils';
import Decimal from 'decimal.js';

@Component({
  selector: 'app-pluviografo',
  templateUrl: './pluviografo.component.html',
  styleUrls: ['./pluviografo.component.scss']
})
export class PluviografoComponent implements OnInit, OnChanges {
  @Input() public instrumentsList: any = [];
  @Input() public index: number = null;
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public units: any = null;
  @Input() public typeInstrument: any = null;
  @Input() public datetime: any = null;

  @Output() public setInstrument = new EventEmitter();

  public formReading: FormGroup = new FormGroup({
    instrument: new FormControl('', [Validators.required]),
    date: new FormControl({ value: '', disabled: true }, [Validators.required]),
    pluviometry: new FormControl({ value: '', disabled: true }, [Validators.required]),
    intensity: new FormControl({ value: '', disabled: true }),
    //Para edicao
    id: new FormControl({ value: '', disabled: true }),
    //Para calculo
    measurement_frequency: new FormControl({ value: '', disabled: true })
  });

  public controls: any = null;
  public fieldsReading = fieldsReading;
  public message: any = [{ text: '', status: false }];
  public messagesError: any = null;
  public func = fn;

  constructor(private formService: FormService) {}

  ngOnInit(): void {
    this.controls = this.formReading.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReading.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }

    if (changes.datetime && changes.datetime.currentValue != null) {
      this.controls['date'].setValue(this.datetime);
    }
  }

  changeInstrument(instrument) {
    this.setInstrument.emit(instrument);
  }

  splitData($dados) {
    if (!this.edit && !this.view && !this.spreadsheet) {
      this.controls['instrument'].enable();
    } else {
      this.controls['instrument'].disable();
    }

    this.formService.toggleFormList(this.formReading, this.fieldsReading[this.typeInstrument.id]);

    this.controls['instrument'].setValue($dados.instrument.id);
    this.controls['measurement_frequency'].setValue($dados.measurement_frequency);

    if ($dados.edit) {
      this.controls['id'].setValue($dados.edit.id);

      let date = $dados.edit.date.split('.');
      this.controls['date'].setValue(date[0]);
      this.controls['pluviometry'].setValue($dados.edit.pluviometry);
      this.controls['intensity'].setValue($dados.edit.intensity);
    }

    if (this.view) {
      this.formReading.disable();
    }

    //Após importar a planilha, não é mais necessário fazer o recálculo
    // if (this.spreadsheet) {
    //   setTimeout(() => {
    //     this.calcIntensity();
    //   }, 200);
    // }
  }

  calcIntensity() {
    let frequency: any = this.controls['measurement_frequency'].value.split(':');
    let frequencyHourDecimal: any = new Decimal(frequency[0]);
    let frequencyMinuteDecimal: any = new Decimal(frequency[1]);

    frequencyMinuteDecimal = frequencyMinuteDecimal.div(new Decimal(60));

    let frequencyDecimal: any = Decimal.add(frequencyHourDecimal, frequencyMinuteDecimal);
    let pluviometry = this.controls['pluviometry'].value;

    if (!fn.isEmpty(pluviometry)) {
      let pluviometryDecimal: any = new Decimal(pluviometry);
      let intensityDecimal: any = Decimal.div(pluviometryDecimal, frequencyDecimal);
      this.controls['intensity'].setValue(intensityDecimal);
    }
  }
}
