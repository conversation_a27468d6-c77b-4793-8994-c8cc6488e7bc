<ng-template #modalViewDxf let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">{{ title }}</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <div class="modal-body">
    <!-- Tabela Informativa -->
    <div class="row mt-3" *ngIf="this.tableData.length > 0">
      <div class="col-md-12">
        <app-table [tableHeader]="tableHeader" [tableData]="tableData">
        </app-table>
      </div>
    </div>
    <!-- DXF -->
    <div class="row mt-2">
      <div class="col-md-12">
        <app-dxf-viewer [fileDxf]="fileDxf"></app-dxf-viewer>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-gray'"
      [label]="'Fechar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>
