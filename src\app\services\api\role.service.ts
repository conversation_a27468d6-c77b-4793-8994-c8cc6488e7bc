import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class RoleService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  // Cadastro de Funções
  postRoles(params: any) {
    const url = '/roles';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna as funções para uso em filtro
  getRoles(params: any) {
    const url = '/roles/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getRolesList(params: any = {}) {
    const url = '/roles';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca a função por ID
  getRolesById(id: string) {
    const url = `/roles/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putRoles(id: string, params: any) {
    const url = `/roles/${id}`;
    return this.api.put<any>(url, params, 'client');
  }
}
