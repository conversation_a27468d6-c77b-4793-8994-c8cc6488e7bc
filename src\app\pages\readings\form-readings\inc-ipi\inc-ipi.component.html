<form [formGroup]="formReading" class="mb-3">
  <div class="row">
    <div class="col-md-3">
      <label class="form-label">Instrumento</label>
      <select
        class="form-select"
        formControlName="instrument"
        (change)="changeInstrument(formReading.controls['instrument'].value)"
      >
        <option value="" *ngIf="formReading.controls['instrument'].value == ''">
          Selecione...
        </option>
        <option
          *ngFor="let instrumentItem of instrumentsList"
          [ngValue]="instrumentItem.id"
        >
          {{ instrumentItem.identifier }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('instrument').valid &&
          formReading.get('instrument').touched &&
          !formReading.get('instrument').disabled
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Ponto de medição (id/identifier) Campo bloqueado -->
    <div class="col-md-3">
      <label class="form-label">Ponto de medição</label>
      <input type="text" class="form-control" formControlName="measure" />
    </div>

    <!-- Depth - campo bloqueado -->
    <div class="col-md-3">
      <label class="form-label">Profundidade</label>
      <div class="input-group">
        <input type="text" class="form-control" formControlName="depth" />
        <span class="input-group-text">{{ units[1] }}</span>
      </div>
    </div>

    <!-- Data e hora -->
    <div class="col-md-3">
      <label class="form-label">Data e hora</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('date').valid && formReading.get('date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <div class="row mt-2">
    <!-- a_axis_reading Campo opcional-->
    <div class="col-md-3">
      <label class="form-label">Leitura eixo A</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="a_axis_reading"
          placeholder="Campo opcional"
          (blur)="func.formatType($event); calcDisplacement('a')"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber($event, formReading.get('a_axis_reading'))
          "
          appDisableScroll
        />
        <span class="input-group-text">{{ units[0] }}</span>
      </div>
    </div>
    <!-- b_axis_reading Campo opcional -->
    <div class="col-md-3">
      <label class="form-label">Leitura eixo B</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="b_axis_reading"
          placeholder="Campo opcional"
          (blur)="func.formatType($event); calcDisplacement('b')"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber($event, formReading.get('b_axis_reading'))
          "
          appDisableScroll
        />
        <span class="input-group-text">{{ units[0] }}</span>
      </div>
    </div>

    <!-- average_displacement_a Campo opcional -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento médio A</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="average_displacement_a"
          (blur)="
            func.formatType($event); calculate('accumulated_displacement', 'a')
          "
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber(
              $event,
              formReading.get('average_displacement_a')
            )
          "
          appDisableScroll
        />
        <span class="input-group-text">{{ units[1] }}</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('average_displacement_a').valid &&
          formReading.get('average_displacement_a').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- average_displacement_b -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento médio B</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="average_displacement_b"
          (blur)="
            func.formatType($event); calculate('accumulated_displacement', 'b')
          "
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber(
              $event,
              formReading.get('average_displacement_b')
            )
          "
          appDisableScroll
        />
        <span class="input-group-text">{{ units[1] }}</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('average_displacement_b').valid &&
          formReading.get('average_displacement_b').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <div class="row mt-2">
    <!-- accumulated_displacement_a -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento acumulado A</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="accumulated_displacement_a"
        />
        <span class="input-group-text">{{ units[1] }}</span>
      </div>
    </div>
    <!-- accumulated_displacement_b -->
    <div class="col-md-3">
      <label class="form-label">Deslocamento acumulado B</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="accumulated_displacement_b"
        />
        <span class="input-group-text">{{ units[1] }}</span>
      </div>
    </div>
    <!-- deviation_a Campo bloqueado -->
    <div class="col-md-3">
      <label class="form-label">Desvio A</label>
      <div class="input-group">
        <input type="text" class="form-control" formControlName="deviation_a" />
        <span class="input-group-text">{{ units[1] }}</span>
      </div>
    </div>
    <!-- deviation_b Campo bloqueado -->
    <div class="col-md-3">
      <label class="form-label">Desvio B</label>
      <div class="input-group">
        <input type="text" class="form-control" formControlName="deviation_b" />
        <span class="input-group-text">{{ units[1] }}</span>
      </div>
    </div>
  </div>
</form>
