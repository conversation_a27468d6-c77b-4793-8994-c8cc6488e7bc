import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';

import { classifications, environmentalDamagePotential } from 'src/app/constants/structure.constants';
import { accessLevel as accessLevelPermission } from 'src/app/constants/permissions.constants';

import { StructuresService } from 'src/app/services/api/structure.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-datasheet-tab',
  templateUrl: './datasheet-tab.component.html',
  styleUrls: ['./datasheet-tab.component.scss']
})
export class DatasheetTabComponent implements OnInit {
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  public func = fn;

  public formDatasheet: FormGroup = new FormGroup({
    countries: new FormControl(''),
    states: new FormControl(''),
    cities: new FormControl(''),
    purpose: new FormControl(''),
    //year_of_construction_initial_dike: new FormControl(''),
    construction_stages: new FormControl(''),
    crest_dimension_width: new FormControl(''),
    crest_dimension_length: new FormControl(''),
    total_height: new FormControl(''),
    downstream_slope: new FormControl(''),
    upstream_slope: new FormControl(''),
    classification: new FormControl(''),
    section_type: new FormControl(''),
    foundation_type: new FormControl(''),
    raising_method: new FormControl(''),
    expected_elevations: new FormControl(''),
    elevations_made: new FormControl(''),
    reservoir_design_volume: new FormControl(''),
    current_reservoir_volume: new FormControl(''),
    internal_drainage: new FormControl(''),
    superficial_drainage: new FormControl(''),
    basin_area_in_square_kilometers: new FormControl(''),
    project_precipitation: new FormControl(''),
    maximum_influent_flow: new FormControl(''),
    project_flow: new FormControl(''),
    full_of_project: new FormControl(''),
    normal_maximum_water_level: new FormControl(''),
    maximum_water_level_maximorum: new FormControl(''),
    freeboard_normal_maximum_water_level: new FormControl(''),
    freeboard_maximum_water_level_maximorum: new FormControl(''),
    spillway: new FormControl(''),
    // constitutive_stage: new FormControl(''),
    construction_year: new FormControl(''),
    start_of_operation_date: new FormControl(''),
    end_of_operation_date: new FormControl(''),
    designing_company: new FormControl(''),
    current_status_of_dam: new FormControl(''),
    crest_quota: new FormControl(''),
    // surface_area: new FormControl(''),
    spillway_sill_quota: new FormControl(''),
    intercepted_watercourse: new FormControl(''),
    //Tabela
    total_reservoir_volume: new FormControl(1),
    population_downstream: new FormControl(0),
    environmental_impact: new FormControl(0),
    socioeconomic_impact: new FormControl(0),
    environmental_damage_potential_total: new FormControl('')
  });

  public environmentalDamagePotential: any = environmentalDamagePotential;
  public classifications: any = classifications;
  public classificationsList: any = [];

  public countries: any = [];
  public states: any = [];
  public cities: any = [];

  public country: any = {
    id: null,
    name: null
  };

  public state: any = {
    id: null,
    name: null
  };

  public city: any = {
    id: null,
    name: null
  };

  constructor(private structuresService: StructuresService) {}

  /**
   * Lifecycle hook executado após a criação do componente.
   * Carrega a lista de países e executa a validação de acesso.
   */
  ngOnInit(): void {
    this.getCountriesList();
    this.validateAccess();
  }

  /**
   * Obtém a lista de países via service e armazena no array `countries`.
   */
  getCountriesList() {
    this.structuresService.getCountries().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.countries = dados;
    });
  }

  /**
   * Limpa os estados e cidades atuais, reseta os campos relacionados,
   * e busca a lista de estados com base no país selecionado.
   */
  getStatesList() {
    this.states = [];
    this.cities = [];

    this.formDatasheet.get('states').setValue('');
    this.formDatasheet.get('cities').setValue('');

    const countryId = this.formDatasheet.get('countries').value;
    if (countryId != '') {
      this.structuresService.getStates({ countryId: countryId }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.states = dados;
      });
    }
  }

  /**
   * Limpa as cidades atuais, reseta o campo correspondente,
   * e busca a lista de cidades com base no estado selecionado.
   */
  getCitiesList() {
    this.cities = [];

    this.formDatasheet.get('cities').setValue('');

    const stateId = this.formDatasheet.get('states').value;
    if (stateId != '') {
      this.structuresService.getCities({ stateId: stateId }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.cities = dados;
      });
    }
  }

  /**
   * Valida as permissões de acesso e desativa campos do formulário conforme o perfil do usuário.
   *
   * @param role - (Opcional) Nível de permissão para validação. Padrão é 0.
   */
  validateAccess(role: number = 0): any {
    if (this.profile.description === accessLevelPermission.SuperAdministrador) {
      this.formDatasheet.disable();
      this.formDatasheet.get('current_reservoir_volume').enable();
    }
    if (this.view) {
      this.formDatasheet.disable();
    }
  }

  /**
   * Popula o formulário `formDatasheet` com os dados fornecidos.
   * Também dispara a busca de estados e cidades conforme os campos preenchidos.
   *
   * @param dataTab - Objeto com os dados a serem inseridos no formulário.
   */
  setData(dataTab: any = []) {
    for (var index in dataTab) {
      this.formDatasheet.get(index).setValue(dataTab[index]);
      switch (index) {
        case 'countries':
          this.getStatesList();
          break;
        case 'states':
          this.getCitiesList();
          break;
      }
    }
  }

  /**
   * Retorna os valores do formulário `formDatasheet`,
   * somando os campos específicos para calcular `environmental_damage_potential_total`.
   *
   * @returns Objeto com os dados preenchidos do formulário, incluindo a pontuação total calculada.
   */
  getData() {
    let dataTab = {};
    for (let index in this.formDatasheet.controls) {
      dataTab[index] = this.formDatasheet.get(index).value;
    }
    //Somatório pontuação tabela
    const total_reservoir_volume = !fn.isEmpty(dataTab['total_reservoir_volume']) ? parseInt(dataTab['total_reservoir_volume']) : 0;
    const population_downstream = !fn.isEmpty(dataTab['population_downstream']) ? parseInt(dataTab['population_downstream']) : 0;
    const environmental_impact = !fn.isEmpty(dataTab['environmental_impact']) ? parseInt(dataTab['environmental_impact']) : 0;
    const socioeconomic_impact = !fn.isEmpty(dataTab['socioeconomic_impact']) ? parseInt(dataTab['socioeconomic_impact']) : 0;

    dataTab['environmental_damage_potential_total'] = total_reservoir_volume + population_downstream + environmental_impact + socioeconomic_impact;

    return dataTab;
  }

  /**
   * Limita a entrada numérica em campos do formulário com base em regras de tamanho, mínimo e máximo.
   *
   * @param $event - Evento do input (keypress ou keyup).
   * @param element - (Opcional) FormControl associado ao campo.
   * @param dotComma - (Opcional) Permite ou não ponto/vírgula. Padrão `false`.
   * @param negativo - (Opcional) Permite número negativo. Padrão `true`.
   */
  limitNumber($event, element: any = null, dotComma = false, negativo = true) {
    let value = $event.target.value;
    let type = $event.type;
    let maxLength = parseInt($event.target.maxLength);
    let min = parseInt($event.target.min);
    let max = parseInt($event.target.max);
    let valueCondition = $event.target.step.indexOf('.') !== -1 ? parseInt(value) : parseFloat(value);
    let charCode = $event.which ? $event.which : $event.keyCode;

    if (type == 'keypress') {
      if (dotComma && (charCode <= 48 || charCode >= 57) && charCode != 45) {
        return false;
      }
      if (value.length == maxLength) {
        return false;
      }
    } else if (type == 'keyup') {
      if (valueCondition > max && value != '') {
        element.setValue(max);
      } else if (valueCondition < min && value != '') {
        element.setValue(min);
      }
    }
  }

  /**
   * Retorna as chaves (colunas) do objeto `environmentalDamagePotential`.
   *
   * @returns Array com as chaves disponíveis.
   */
  getColumnKeys() {
    return Object.keys(this.environmentalDamagePotential);
  }

  /**
   * Retorna um array com o maior número de pesos por coluna,
   * para controle visual ou lógico de campos.
   *
   * @returns Array com índices de 0 até o maior número de pesos existente.
   */
  getMaxWeights() {
    return Array.from({ length: Math.max(...this.getColumnKeys().map((key) => this.environmentalDamagePotential[key].weight.length)) }, (_, i) => i);
  }
}
