* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.sidebar {
  top: 0;
  left: 0;
  height: 100%;
  width: 71px;
  background-color: #88aeb0;
  padding: 0;
  z-index: 9999;
  transition: all 0.5s ease;
  .logo_content {
    .logo {
      position: fixed;
      z-index: 9;
      color: #f2f2f2;
      display: flex;
      height: 71px;
      width: 71px;
      opacity: 1;
      transition: all 0.5s ease;
      a {
        line-height: 29px;
        background-color: #f2f2f2;
        width: 100%;
        box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.29);
        border-radius: 8px;
        img {
          height: 29px;
          position: absolute;
          top: calc((71px - 29px) / 2);
          left: calc((71px - 21px) / 2);
        }
        img:nth-child(1) {
          transition: all 0.3s ease;
          opacity: 0;
        }
        img:nth-child(2) {
          transition: all 0.3s ease;
          opacity: 1;
        }
      }
    }
  }
  ul.nav_list {
    position: relative;
    top: 71px;
  }
  ul.nav_list,
  ul.nav_list_submenu {
    margin-top: 0px;
    li {
      position: relative;
      height: 40px;
      width: 100%;
      margin: 0px;
      list-style: none;
      line-height: 40px;
      a {
        color: #f2f2f2;
        display: flex;
        align-items: center;
        text-decoration: none;
        transition: all 0.4s ease;
        line-height: 40px;
        white-space: nowrap;
        i,
        img {
          height: 40px;
          min-width: 40px;
          line-height: 40px;
          text-align: center;
          margin-left: calc((71px - 40px) / 2);
        }
        img {
          height: 26px;
          filter: invert(100%) sepia(0%) saturate(100%) hue-rotate(0deg)
            brightness(100%) contrast(100%);
        }
        .links_name {
          opacity: 0;
          pointer-events: none;
          transition: all 0.5s ease;
          font-size: 0.875em;
          font-weight: 500;
          margin-left: 20px;
        }
        &:hover {
          background-color: #34b575;
          color: #f2f2f2;
          border-left: 7px solid #88aeb0;
          i,
          img {
            transition: none;
            margin-left: calc((71px - 40px) / 2 - 7px);
          }
        }
      }

      .tooltip-menu,
      .tooltip-menu-submenu {
        position: absolute;
        left: 71px;
        top: 0;
        transform: translate(-50%, -50%);
        border-radius: 11px;
        line-height: 22px;
        text-align: center;
        height: 22px;
        width: 22px;
        background-color: #f2f2f2;
        box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.29);
        transition: 0s;
        opacity: 0;
        pointer-events: none;
        display: block;
        font-size: 12px;
        color: #34b575;
      }

      ul.nav_list_submenu {
        pointer-events: none;
        opacity: 0;
        width: 71px;
        background-color: #88aeb0;
        position: absolute;
        left: 71px;
        bottom: 0px;
        transition: 0s;
        border-left: 1px solid #f2f2f2;
        z-index: 99;
      }

      ul.nav_list_submenu.active {
        opacity: 0;
        left: 196px;
        width: 220px;
        > li {
          > a {
            > .links_name {
              pointer-events: none;
              margin-left: 15px;
            }
            i,
            img {
              margin-left: calc((71px - 40px) / 2 - 10px);
            }
            &:hover {
              i,
              img {
                transition: none;
                margin-left: calc((71px - 40px) / 2 - 7px - 10px);
              }
            }
          }
          > .tooltip-menu {
            left: 220px;
          }
        }
      }
    }
    > li:hover {
      > .tooltip-menu {
        transition: all 0.5s ease;
        top: 50%;
        opacity: 1;
      }
      ul.nav_list_submenu {
        transition: all 0.5s ease;
        opacity: 1;
        pointer-events: all;
      }
    }
  }
}
.sidebar.active {
  width: 196px;
  .logo_content {
    .logo {
      width: 196px;
      a {
        img {
          left: calc((196px - 151px) / 2);
        }
        img:nth-child(1) {
          opacity: 1;
        }
        img:nth-child(2) {
          opacity: 0;
        }
      }
    }
  }

  ul.nav_list {
    > li {
      > .tooltip-menu {
        left: 196px;
      }
      a {
        .links_name {
          opacity: 1;
          pointer-events: auto;
        }
      }
    }
  }
}

@media (max-width: 900px) {
}
