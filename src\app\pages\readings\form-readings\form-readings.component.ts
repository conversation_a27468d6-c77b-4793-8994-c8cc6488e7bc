import {
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnInit,
  QueryList,
  SimpleChanges,
  ViewChildren,
  Output,
  EventEmitter,
  ViewEncapsulation
} from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';

import { ReadingService } from 'src/app/services/reading.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { ReadingService as ReadingServiceApi } from 'src/app/services/api/reading.service';
import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';

import fn from 'src/app/utils/function.utils';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ModalMapComponent } from '@components/modal/modal-map/modal-map.component';

export class NgbdModalContent {
  constructor(public activeModal: NgbActiveModal) {}
}

@Component({
  selector: 'app-form-readings',
  templateUrl: './form-readings.component.html',
  styleUrls: ['./form-readings.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class FormReadingsComponent implements OnInit, OnChanges {
  @ViewChildren('readingInstrumentRef') readingInstrumentRef: QueryList<ElementRef>;

  @Input() public typeInstrument: any = null;
  @Input() public instruments: any = [];
  @Input() public sections: any = [];
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public unitField: any = [null, null];
  @Input() public uidForm: string = '';
  @Input() public datetime: any = null;
  @Input() public structure: any = null;
  @Input() public data: any = null;

  @Output() public sendRemoveForm = new EventEmitter();

  public formReadings: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: false }),
    is_referencial: new FormControl({ value: null, disabled: false })
  });

  public controls: any = null;
  public instrumentsReading: any = [null];
  public selectInstrument: any = null;
  public selectSection: any = null;
  public formItemReading: any = null;
  public references: any = null;

  public func = fn;

  constructor(
    private instrumentsServiceApi: InstrumentsServiceApi,
    private modalService: NgbModal,
    private readingService: ReadingService,
    private readingServiceApi: ReadingServiceApi,
    private structuresServiceApi: StructuresServiceApi
  ) {}

  /**
   * Método de inicialização do componente.
   * Define os controles do formulário ao inicializar o componente.
   */
  ngOnInit(): void {
    this.controls = this.formReadings.controls;
  }

  /**
   * Método chamado quando há alterações nas propriedades ligadas ao componente.
   * Atualiza os controles do formulário e processa os dados recebidos.
   * @param {SimpleChanges} changes - Mudanças nas propriedades do componente.
   */
  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReadings.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  /**
   * Define o instrumento selecionado na primeira linha de leitura.
   * Realiza a busca de referências ou informações adicionais conforme o tipo de instrumento.
   * @param {number} instrumentId - ID do instrumento selecionado.
   */
  selectedInstrument(instrumentId) {
    this.selectInstrument = fn.findIndexInArrayofObject(this.instruments, 'id', instrumentId, 'identifier', true);
    //Instrumentos que possuem o checkbox Referencia
    if ([4, 5, 6, 7].includes(this.typeInstrument.id) && !this.view) {
      this.getReference(instrumentId);
    } else {
      this.getInstrument(this.selectInstrument);
      this.controls['is_referencial'].disable();
    }
  }

  /**
   * Comprimento de praia - Define a seção selecionada para o preenchimento dos dados de leitura.
   * @param {number} sectionId - ID da seção selecionada.
   */
  selectedSection(sectionId) {
    this.selectSection = fn.findIndexInArrayofObject(this.sections, 'id', sectionId, 'identifier', true);
    this.setSectionReading(this.selectSection);
  }

  /**
   * Busca os dados do instrumento selecionado e popula as linhas de leitura correspondentes.
   * @param {any} instrument - Objeto do instrumento selecionado.
   * @param {string} [action='select'] - Ação de seleção ou desseleção do instrumento.
   */
  getInstrument(instrument, action: string = 'select') {
    if (this.spreadsheet) {
      this.selectInstrument = this.data.instrument;
      this.checkReference();
    } else {
      this.instrumentsServiceApi.getInstrumentsById(instrument.id).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.selectInstrument = dados;
        this.checkReference();
      });
    }
  }

  //Verifica se o instrumento possui referências e atualiza os campos do formulário conforme necessário.
  checkReference() {
    let ctrlReference = true;

    if (this.references && this.references.values) {
      this.selectInstrument.measurements.forEach((measure, index) => {
        let reference = this.readingService.findReference(measure.id, this.references);
        if (ctrlReference && reference === undefined) {
          ctrlReference = false;
        }
      });
    }

    if (!ctrlReference) {
      this.references = {};
      this.controls['is_referencial'].setValue(true);
      this.controls['is_referencial'].disable();
    }

    this.setInstrumentReading(this.selectInstrument);
  }

  /**
   * Busca a referência de leitura para o instrumento selecionado.
   * @param {number} instrumentId - ID do instrumento para busca de referência.
   */
  getReference(instrumentId) {
    this.readingServiceApi.getReadingReference(instrumentId).subscribe((resp: any) => {
      if (resp.status == 200) {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.references = dados;
        if (!this.edit) {
          this.controls['is_referencial'].setValue(false);
        }
        this.controls['is_referencial'].enable();
      } else if (resp.status == 204) {
        this.references = {};
        this.controls['is_referencial'].setValue(true);
        this.controls['is_referencial'].disable();
      }
      this.getInstrument(this.selectInstrument);
      if (this.view) {
        this.formReadings.disable();
      }
    });
  }

  /**
   * Popula os campos do formulário com os dados de leitura do instrumento selecionado.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setInstrumentReading(selectInstrument) {
    switch (selectInstrument.type) {
      case 1: //INA
      case 2: //PZ
        this.setInaPzReading(selectInstrument);
        break;
      case 3: //PZE
        this.setPzeReading(selectInstrument);
        break;
      case 4: //Inclinometro convencional
        this.setIncConvReading(selectInstrument);
        break;
      case 5: //IPI
        this.setIpiReading(selectInstrument);
        break;
      case 6: //MS
      case 7: //PR
        this.setMsPrReading(selectInstrument);
        break;
      case 8: //Medidor de recalque
        this.setMrReading(selectInstrument);
        break;
      case 9: //Geofone
        this.setGeoReading(selectInstrument);
        break;
      case 10: //Regua linimetrica
        this.setRlReading(selectInstrument);
        break;
      case 12: //Pluviometro
        this.setPluviometerReading(selectInstrument);
        break;
      case 13: //Pluviografo
        this.setPluviographReading(selectInstrument);
        break;
      default:
        break;
    }
  }

  /**
   * Comprimento de praia - Popula os campos do formulário com os dados de leitura da seção selecionada.
   * @param {any} selectSection - Objeto da seção selecionada.
   */
  setSectionReading(selectSection) {
    switch (selectSection.type) {
      case 11: //Praia
        this.setBeachLengthReading(selectSection);
        break;
      default:
        break;
    }
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo INA-PZ.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setInaPzReading(selectInstrument) {
    this.instrumentsReading = [];
    let item = {};
    item['instrument'] = selectInstrument;
    item['top_quota'] = selectInstrument.top_quota;
    item['base_quota'] = selectInstrument.base_quota;

    if (this.edit || this.view) {
      item['edit'] = this.data.values[0];
      //Dados do momento do cadastro
      item['top_quota'] = this.data.instrument_top_quota;
      item['base_quota'] = this.data.instrument_base_quota;
    } else if (this.spreadsheet) {
      item['edit'] = this.data.values[0];
      item['top_quota'] = this.data.instrument.top_quota;
      item['base_quota'] = this.data.instrument.base_quota;
    }

    this.instrumentsReading.push(item);
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo PZE.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setPzeReading(selectInstrument) {
    this.instrumentsReading = [];

    if (selectInstrument?.measurements?.length > 0) {
      selectInstrument.measurements.forEach((measure, index) => {
        if (measure.active) {
          let data = null;
          let item = {};
          item['instrument'] = selectInstrument;
          item['measure'] = measure;
          item['top_quota'] = selectInstrument.top_quota;
          item['base_quota'] = selectInstrument.base_quota;

          item['instrumentsList'] = [{ id: selectInstrument.id, identifier: selectInstrument.identifier }];

          if (this.edit || this.view) {
            data = this.readingService.findReference(measure.id, this.data);
            item['edit'] = data;
            //Dados do momento do cadastro
            item['top_quota'] = this.data.instrument_top_quota;
            item['base_quota'] = this.data.instrument_base_quota;
          } else if (this.spreadsheet) {
            data = this.readingService.findReference(measure.id, this.data);
            item['edit'] = data;
            item['top_quota'] = this.data.instrument.top_quota;
            item['base_quota'] = this.data.instrument.base_quota;
          }
          if (data !== undefined && (this.edit || this.view || this.spreadsheet)) {
            this.instrumentsReading.push(item);
          } else if (data === null) {
            this.instrumentsReading.push(item);
          }
        }
      });
    }
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo INC_CONV.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setIncConvReading(selectInstrument) {
    this.instrumentsReading = [];

    if (selectInstrument?.measurements?.length > 0) {
      let sortedMeasures = selectInstrument.measurements;
      sortedMeasures.sort(function (a, b) {
        return a.quota - b.quota;
      });

      sortedMeasures.forEach((measure, index) => {
        if (measure.active) {
          let data = null;
          let item = {};
          item['instrument'] = selectInstrument;
          item['measure'] = measure;
          item['depth'] = measure.depth;
          item['elevation'] = measure.quota;

          item['instrumentsList'] = [{ id: selectInstrument.id, identifier: selectInstrument.identifier }];

          if (this.edit || this.view) {
            data = this.readingService.findReference(measure.id, this.data);
            if (data !== undefined) {
              item['edit'] = data;
              //Dados do momento do cadastro
              item['depth'] = data.depth;
            }
          } else if (this.spreadsheet) {
            data = this.readingService.findReference(measure.id, this.data);
            if (data !== undefined) {
              item['edit'] = data;
              item['depth'] = data.depth;
              if (!this.controls['is_referencial'].disabled) {
                this.controls['is_referencial'].setValue(this.data.is_referential);
              }
            } else {
              item['edit'] = [];
              item['depth'] = null;
            }
          }

          if (data !== undefined && (this.edit || this.view || this.spreadsheet)) {
            this.instrumentsReading.push(item);
          } else if (data === null) {
            this.instrumentsReading.push(item);
          }
        }
      });
    }
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo IPI.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setIpiReading(selectInstrument) {
    this.instrumentsReading = [];

    if (selectInstrument?.measurements?.length > 0) {
      let sortedMeasures = selectInstrument.measurements;

      sortedMeasures.sort(function (a, b) {
        return a.quota - b.quota;
      });

      sortedMeasures.forEach((measure, index) => {
        if (measure.active) {
          let data = null;
          let item = {};
          item['instrument'] = selectInstrument;
          item['measure'] = measure;
          item['depth'] = measure.depth;
          item['elevation'] = measure.quota;
          item['length'] = measure.length;

          item['instrumentsList'] = [{ id: selectInstrument.id, identifier: selectInstrument.identifier }];

          if (this.edit || this.view) {
            data = this.readingService.findReference(measure.id, this.data);
            if (data !== undefined) {
              item['edit'] = data;
              //Dados do momento do cadastro
              item['length'] = data.measurement_length;
              item['depth'] = data.depth;
            }
          } else if (this.spreadsheet) {
            data = this.readingService.findReference(measure.id, this.data);
            if (data !== undefined) {
              item['edit'] = data;
              item['depth'] = data.depth;
              let dataInstrument = fn.findIndexInArrayofObject(this.data.instrument.measurements, 'id', measure.id, 'id', true);
              item['length'] = dataInstrument.length;
              if (!this.controls['is_referencial'].disabled) {
                this.controls['is_referencial'].setValue(this.data.is_referential);
              }
            }
          }

          if (data !== undefined && (this.edit || this.view || this.spreadsheet)) {
            this.instrumentsReading.push(item);
          } else if (data === null) {
            this.instrumentsReading.push(item);
          }
        }
      });
    }
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo MS-PR.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setMsPrReading(selectInstrument) {
    this.instrumentsReading = [];
    let item = {};
    item['instrument'] = selectInstrument;
    item['coordinate_setting'] = selectInstrument.coordinate_setting;
    item['azimuth'] = selectInstrument.azimuth;

    item['instrumentsList'] = [{ id: selectInstrument.id, identifier: selectInstrument.identifier }];

    if (this.edit || this.view) {
      item['edit'] = this.data.values[0];
      //Dados do momento do cadastro
      item['azimuth'] = this.data.instrument_azimuth;
    } else if (this.spreadsheet) {
      item['edit'] = this.data.values[0];
      item['coordinate_setting'] = this.data.instrument.coordinate_setting;
      item['azimuth'] = this.data.instrument.azimuth;
    }

    this.instrumentsReading.push(item);
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo Medidor de Recalque.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setMrReading(selectInstrument) {
    this.instrumentsReading = [];
    if (selectInstrument?.measurements?.length > 0) {
      let idx = this.readingService.findIndexItemByIdandValue(selectInstrument.measurements, 'is_referencial', true);
      let measure_referencial = selectInstrument.measurements[idx];

      selectInstrument.measurements.forEach((measure, index) => {
        if (measure.active) {
          let data = null;
          let item = {};
          item['instrument'] = selectInstrument;
          item['measure'] = measure;
          item['top_quota'] = selectInstrument.top_quota;
          item['measure_referencial'] = measure_referencial;

          item['instrumentsList'] = [{ id: selectInstrument.id, identifier: selectInstrument.identifier }];

          if (this.edit || this.view) {
            data = this.readingService.findReference(measure.id, this.data);
            if (data != undefined) {
              item['edit'] = data;
              //Dados do momento do cadastro
              item['top_quota'] = this.data.instrument_top_quota;
              item['measure_referencial'] = data.measurement_is_referential;
              item['measure'].delta_ref = data.measurement_delta_ref;
              item['measure'].quota = data.measurement_quota;
            }
          } else if (this.spreadsheet) {
            data = this.readingService.findReference(measure.id, this.data);
            if (data != undefined) {
              item['edit'] = data;
              item['top_quota'] = this.data.instrument.top_quota;
            }
          }

          if (data !== undefined && (this.edit || this.view || this.spreadsheet)) {
            this.instrumentsReading.push(item);
          } else if (data === null) {
            this.instrumentsReading.push(item);
          }
        }
      });
    }
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo Geofone.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setGeoReading(selectInstrument) {
    this.instrumentsReading = [];
    let item = {};
    item['instrument'] = selectInstrument;

    if (this.edit || this.view) {
      item['edit'] = this.data.values[0];
      this.getStructure(selectInstrument.structure.id);
    } else if (this.spreadsheet) {
      item['edit'] = this.data.values[0];
    }

    this.instrumentsReading.push(item);
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo RL.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setRlReading(selectInstrument) {
    this.instrumentsReading = [];
    let item = {};
    item['instrument'] = selectInstrument;

    if (this.edit || this.view) {
      item['edit'] = this.data.values[0];
    } else if (this.spreadsheet) {
      item['edit'] = this.data.values[0];
    }

    this.instrumentsReading.push(item);
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo Pluviômetro.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setPluviometerReading(selectInstrument) {
    this.instrumentsReading = [];
    let item = {};
    item['instrument'] = selectInstrument;

    if (this.edit || this.view) {
      item['edit'] = this.data.values[0];
    } else if (this.spreadsheet) {
      item['edit'] = this.data.values[0];
    }

    this.instrumentsReading.push(item);
  }

  /**
   * Preenche os campos do formulário para instrumentos do tipo Pluviógrafo.
   * @param {any} selectInstrument - Objeto do instrumento selecionado.
   */
  setPluviographReading(selectInstrument) {
    this.instrumentsReading = [];
    let item = {};

    item['instrument'] = selectInstrument;
    item['measurement_frequency'] = selectInstrument.measurement_frequency;

    if (this.edit || this.view) {
      item['edit'] = this.data.values[0];
    } else if (this.spreadsheet) {
      item['edit'] = this.data.values[0];
    }

    this.instrumentsReading.push(item);
  }

  /**
   * Preenche os campos do formulário para o tipo de seção Comprimento de Praia.
   * @param {any} selectSection - Objeto da seção selecionada.
   */
  setBeachLengthReading(selectSection) {
    this.instrumentsReading = [];
    let item = {};
    item['section'] = selectSection;

    if (this.edit || this.view) {
      item['edit'] = this.data;
    }

    this.instrumentsReading.push(item);
  }

  /**
   * Realiza cálculos específicos com base no tipo de instrumento e na operação selecionada.
   * @param {any} $event - Evento com informações sobre o cálculo a ser realizado.
   */
  calcReading($event) {
    switch ($event.typeInstrument) {
      case 4: //INC_CONV
        switch ($event.calc) {
          case 'accumulated_displacement':
            this.readingService.calcAccumulatedDisplacement(this.readingInstrumentRef, this.unitField);
            this.readingService.calcDesviation(this.readingInstrumentRef, this.unitField, $event.references);
            break;
        }
        break;
      case 5: //IPI
        switch ($event.calc) {
          case 'accumulated_displacement':
            this.readingService.calcAccumulatedDisplacement(this.readingInstrumentRef, this.unitField, 1);
            this.readingService.calcDesviation(this.readingInstrumentRef, this.unitField, $event.references, 1);
            break;
        }
        break;
      case 8: //MR
        switch ($event.calc) {
          case 'delta_ref':
            this.readingService.calcDeltaRef(this.readingInstrumentRef, this.unitField);
            break;
          case 'quota':
            this.readingService.calcQuotaMr(this.readingInstrumentRef, this.unitField);
            break;
        }
        break;
    }
  }

  /**
   * Remove um formulário específico emitindo um evento para o componente pai.
   * @param {string} uidForm - Identificador do formulário a ser removido.
   */
  removeForm(uidForm) {
    this.sendRemoveForm.emit(uidForm);
  }

  /**
   * Mapa - Geofone - Abre o modal do mapa para exibir a localização do Geofone.
   * @param {any} formItemReading - Objeto contendo os dados de leitura do formulário.
   */
  showMap(formItemReading) {
    this.formItemReading = formItemReading;
    const modalRef = this.modalService.open(ModalMapComponent, { size: 'xl' });
    modalRef.componentInstance.title = 'da Estrutura ' + this.structure.name;
    modalRef.componentInstance.coordinates = this.structure.coordinate_setting;
    modalRef.componentInstance.sendClickEvent.subscribe(($event) => this.clickEvent($event));
  }

  /**
   * Manipula o evento de clique no mapa para atualizar as coordenadas no formulário.
   * @param {any} $event - Evento contendo as coordenadas selecionadas no mapa.
   */
  clickEvent($event) {
    switch (this.typeInstrument.id) {
      case 9:
        if (($event.type = 'coordinates')) {
          this.formItemReading.controls['east_coordinate'].setValue($event.coordinates.easting);
          this.formItemReading.controls['north_coordinate'].setValue($event.coordinates.northing);
        }
        break;
      default:
        break;
    }
  }

  /**
   * Divide e popula os dados do formulário com base nas informações recebidas.
   * @param {any} $dados - Dados recebidos para popular o formulário.
   */
  splitData($dados) {
    this.controls['is_referencial'].setValue($dados.is_referential);
    this.controls['id'].setValue($dados.id);

    if (this.typeInstrument.id <= 10 || this.typeInstrument.id == 12 || this.typeInstrument.id == 13) {
      this.selectedInstrument($dados.instrument.id);
    } else {
      this.selectedSection($dados.section.id);
    }
  }

  /**
   * Busca os dados de uma estrutura específica para edição de Geofone.
   * @param {string} structureId - ID da estrutura a ser buscada.
   */
  getStructure(structureId: string) {
    this.structuresServiceApi.getStructureByIdBasicInfo(structureId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.structure = {};
      this.structure['id'] = dados.id;
      this.structure['name'] = dados.name;
      this.structure['coordinate_setting'] = dados.coordinate_setting;
    });
  }

  /**
   * Verifica se o instrumento é referencial e atualiza os componentes de leitura conforme necessário.
   * @param {any} $event - Evento que indica a mudança no status de referencial.
   */
  checkIsReferencial($event) {
    if ([4, 5, 6, 7].includes(this.typeInstrument.id)) {
      let isReferencial = $event.target.checked;
      let allComponent = this.readingInstrumentRef.toArray();
      allComponent.forEach((component) => {
        if (isReferencial) {
          component['references'] = {};
        } else {
          component['references'] = this.references;
        }

        component['calcAfterLoadSpreadsheet']();
      });
    }
  }
}
