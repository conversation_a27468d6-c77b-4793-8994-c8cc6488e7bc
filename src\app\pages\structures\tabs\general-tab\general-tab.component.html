<form class="row g-3" [formGroup]="formStructure">
  <!-- Nome da Estrutura -->
  <div class="col-md-4">
    <div>
      <label class="form-label">Nome da Estrutura</label>
      <input
        type="text"
        formControlName="name"
        class="form-control"
        [attr.maxlength]="32"
        autocomplete="off"
        (input)="onValueChange($event, 'name')"
        appDisableScroll
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formStructure.get('name').valid && formStructure.get('name').touched
        "
        >Campo Obrigatório.</small
      >
      <!-- Contador de caracteres -->
      <small class="form-text text-muted d-block"
        >Caracteres {{ charCounts['name'] || 0 }} de 32
      </small>
    </div>
  </div>
  <div class="col-md-6"></div>

  <!-- Unidade -->
  <div class="col-md-4">
    <div>
      <label class="form-label">Unidade</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="unitSettings"
        [data]="units"
        formControlName="client_unit"
        (click)="formStructure.get('client_unit').markAsTouched()"
        [disabled]="view"
      >
      </ng-multiselect-dropdown>
      <small
        class="form-text text-danger"
        *ngIf="
          formStructure.get('client_unit').value.length == 0 &&
          formStructure.get('client_unit').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <!-- Situação atual -->
  <div class="col-md-4">
    <label class="form-label">Situação Atual</label>
    <select class="form-select" formControlName="status">
      <option value="">Selecione...</option>
      <option *ngFor="let item of status" [ngValue]="item.value">
        {{ item.status }}
      </option>
    </select>
    <small
      class="form-text text-danger"
      *ngIf="
        !formStructure.get('status').valid &&
        formStructure.get('status').touched
      "
      >Campo Obrigatório.</small
    >
  </div>

  <!-- DATUM -->
  <div class="col-md-4">
    <label class="form-label">DATUM</label>
    <select
      class="form-select"
      formControlName="datum"
      (change)="coordinatesConversion()"
    >
      <option value="">Selecione...</option>
      <option *ngFor="let item of datum" [ngValue]="item.id">
        {{ item.value }}
      </option>
    </select>
    <small
      class="form-text text-danger"
      *ngIf="
        !formStructure.get('datum').valid && formStructure.get('datum').touched
      "
      >Campo Obrigatório.</small
    >
  </div>

  <!-- Coordenadas UTM -->
  <div class="col-md-8">
    <div class="card">
      <div class="card-header form-control-bg">
        <div class="form-check form-check-inline">
          <input
            type="radio"
            class="form-check-input"
            formControlName="coordinate_format"
            value="{{ coordinateFormatList[1].id }}"
            (change)="onCoordinateFormatChange(coordinateFormatList[1])"
          />
          <label class="form-check-label" for="gridRadios2">
            Coordenadas UTM
          </label>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">Zona (Fuso)</label>
            <select
              class="form-select"
              formControlName="zone_number"
              (change)="coordinatesConversion()"
            >
              <option value="">Selecione...</option>
              <option *ngFor="let item of zoneNumberUTM" [ngValue]="item.id">
                {{ item.value }}
              </option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Letra</label>
            <select
              class="form-select"
              formControlName="zone_letter"
              (change)="coordinatesConversion()"
            >
              <option value="">Selecione...</option>
              <option *ngFor="let item of zoneLetterUTM" [ngValue]="item.id">
                {{ item.value }}
              </option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Coordenada Norte</label>
            <input
              type="text"
              formControlName="northing"
              class="form-control"
              (blur)="coordinatesConversion(); func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
          </div>
          <div class="col-md-3">
            <label class="form-label">Coordenada Leste</label>
            <input
              type="text"
              formControlName="easting"
              class="form-control"
              (blur)="coordinatesConversion(); func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Coordenadas Geográficas -->
  <div class="col-md-4">
    <div class="card">
      <div class="card-header form-control-bg">
        <div class="form-check form-check-inline">
          <input
            type="radio"
            class="form-check-input"
            formControlName="coordinate_format"
            value="{{ coordinateFormatList[0].id }}"
            (change)="onCoordinateFormatChange(coordinateFormatList[0])"
          />
          <label class="form-check-label" for="gridRadios2">
            Coordenadas Geográficas
          </label>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <label class="form-label">Latitude</label>
            <input
              type="text"
              formControlName="latitude"
              class="form-control"
              (blur)="coordinatesConversion(); func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
          </div>
          <div class="col-md-6">
            <label class="form-label">Longitude</label>
            <input
              type="text"
              formControlName="longitude"
              class="form-control"
              (blur)="coordinatesConversion(); func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-12 mt-0">
    <small
      class="form-text text-danger"
      *ngIf="
        !formStructure.get('coordinate_format').valid &&
        formStructure.get('coordinate_format').touched
      "
      >Selecione uma das Coordenadas.</small
    >
  </div>

  <!-- Mensagens de erro -->
  <div class="col-md-12 mt-2">
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <!-- Maps  -->
  <div class="row g-3 mt-1">
    <!-- Mapa da Estrutura -->
    <div class="col-md-6">
      <label class="form-label">Mapa da Estrutura</label>
      <app-google-maps #mapStructure [id]="'mapStructure'"></app-google-maps>
      <!-- Zoom Estrutura -->
      <label class="form-label mt-2"
        >Relação do zoom: {{ formStructure.get('general_zoom').value }}</label
      >
      <input
        type="range"
        class="range"
        #zoomStructure
        min="0"
        max="22"
        formControlName="general_zoom"
        (input)="setZoom('structure', zoomStructure.value)"
      />
    </div>
    <!-- Mapa dos Instrumentos -->
    <div class="col-md-6">
      <label class="form-label">Mapa do Instrumento</label>
      <app-google-maps
        #mapInstruments
        [id]="'mapInstruments'"
      ></app-google-maps>
      <!-- Zoom Instrumentos -->
      <label class="form-label mt-2"
        >Relação do zoom:
        {{ formStructure.get('instruments_zoom').value }}</label
      >
      <input
        type="range"
        class="range"
        #zoomInstruments
        min="0"
        max="22"
        formControlName="instruments_zoom"
        (input)="setZoom('instruments', zoomInstruments.value)"
      />
    </div>
  </div>
</form>

<!-- Modal genérico para componentes -->
<app-modal-components
  #modalComponents
  [config]="configModal"
  [component]="'app-images'"
  [title]="titleModal"
></app-modal-components>
