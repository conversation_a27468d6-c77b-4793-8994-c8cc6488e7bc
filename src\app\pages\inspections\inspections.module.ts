import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgSelectModule } from '@ng-select/ng-select';
import { SharedModule } from '@components/shared.module';

import { InspectionsRoutingModule } from './inspections-routing.module';

//Inspeções
import { ListInspectionsComponent } from './list-inspections/list-inspections.component';
import { InspectionSheetComponent } from './inspection-sheet/inspection-sheet.component';
import { RegisterInspectionSheetComponent } from './inspection-sheet/register-inspection-sheet/register-inspection-sheet.component';
import { ModalInsertInspectionSheetComponent } from './inspection-sheet/modal-insert-inspection-sheet/modal-insert-inspection-sheet.component';
import { ModalHistoryInspectionSheetComponent } from './inspection-sheet/modal-history-inspection-sheet/modal-history-inspection-sheet.component';
import { ViewInspectionSheetComponent } from './inspection-sheet/view-inspection-sheet/view-inspection-sheet.component';

//Ocorrências
import { OccurrencesComponent } from './occurrences/occurrences.component';
import { ViewOccurrenceComponent } from './occurrences/view-occurrence/view-occurrence.component';
import { ModalAttachmentsTrailComponent } from './occurrences/modal-attachments-trail/modal-attachments-trail.component';
import { ModalOccurrenceDetailsComponent } from './occurrences/modal-occurrence-details/modal-occurrence-details.component';

//Planos de ação
import { ActionPlansComponent } from './action-plans/action-plans.component';
import { RegisterActionPlanComponent } from './action-plans/register-action-plan/register-action-plan.component';

//Abas - FIE
import { GeneralDataComponent } from './inspection-sheet/register-inspection-sheet/tabs/general-data/general-data.component';
import { PreviousSituationComponent } from './inspection-sheet/register-inspection-sheet/tabs/previous-situation/previous-situation.component';
import { AspectsObservedComponent } from './inspection-sheet/register-inspection-sheet/tabs/aspects-observed/aspects-observed.component';
import { AspectsItemComponent } from './inspection-sheet/register-inspection-sheet/tabs/aspects-observed/aspects-item/aspects-item.component';
import { ModalInsertAspectComponent } from './inspection-sheet/register-inspection-sheet/tabs/aspects-observed/modal-insert-aspect/modal-insert-aspect.component';
import { ConservationStatusComponent } from './inspection-sheet/register-inspection-sheet/tabs/conservation-status/conservation-status.component';
import { GeneralObservationsComponent } from './inspection-sheet/register-inspection-sheet/tabs/general-observations/general-observations.component';
import { ActionsExecutedComponent } from './inspection-sheet/register-inspection-sheet/tabs/actions-executed/actions-executed.component';
import { CurrentSituationComponent } from './inspection-sheet/register-inspection-sheet/tabs/current-situation/current-situation.component';
import { ModalAttachmentsComponent } from './inspection-sheet/register-inspection-sheet/tabs/aspects-observed/modal-attachments/modal-attachments.component';

@NgModule({
  declarations: [
    ListInspectionsComponent,
    InspectionSheetComponent,
    OccurrencesComponent,
    ActionPlansComponent,
    ModalInsertInspectionSheetComponent,
    RegisterInspectionSheetComponent,
    GeneralDataComponent,
    PreviousSituationComponent,
    AspectsObservedComponent,
    AspectsItemComponent,
    ModalInsertAspectComponent,
    ModalAttachmentsComponent,
    ConservationStatusComponent,
    GeneralObservationsComponent,
    ActionsExecutedComponent,
    CurrentSituationComponent,
    ModalHistoryInspectionSheetComponent,
    ViewInspectionSheetComponent,
    ViewOccurrenceComponent,
    ModalAttachmentsTrailComponent,
    ModalOccurrenceDetailsComponent,
    RegisterActionPlanComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    InspectionsRoutingModule,
    NgbModule,
    ReactiveFormsModule,
    SharedModule,
    NgMultiSelectDropDownModule.forRoot(),
    NgSelectModule
  ]
})
export class InspectionsModule {}
