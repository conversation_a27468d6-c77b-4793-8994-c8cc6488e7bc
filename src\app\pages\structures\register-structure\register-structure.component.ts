import { Component, OnInit, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

import { conditions as conditionsEnum, calculationMethods as calculationMethodsEnum } from 'src/app/constants/structure.constants';
import { MessageCadastro } from 'src/app/constants/message.constants';

import { UserService } from 'src/app/services/user.service';
import { StructuresService } from 'src/app/services/api/structure.service';

//Componentes filhos
import { GeneralTabComponent } from '../tabs/general-tab/general-tab.component';
import { ResponsibleTabComponent } from '../tabs/responsible-tab/responsible-tab.component';
import { DatasheetTabComponent } from '../tabs/datasheet-tab/datasheet-tab.component';
import { InspectionsTabComponent } from '../tabs/inspections-tab/inspections-tab.component';
import { StabilityTabComponent } from '../tabs/stability-tab/stability-tab.component';
import { AvancedTabComponent } from '../tabs/avanced-tab/avanced-tab.component';
import { ConfigurationsTabComponent } from '../tabs/configurations-tab/configurations-tab.component';
import { LayersTabComponent } from '../tabs/layers-tab/layers-tab.component';

import fn from 'src/app/utils/function.utils';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-register-structure',
  templateUrl: './register-structure.component.html',
  styleUrls: ['./register-structure.component.scss']
})
export class RegisterStructureComponent implements OnInit {
  @ViewChild(GeneralTabComponent) generalTab: GeneralTabComponent;
  @ViewChild(ResponsibleTabComponent) responsibleTab: ResponsibleTabComponent;
  @ViewChild(DatasheetTabComponent) datasheetTab: DatasheetTabComponent;
  @ViewChild(InspectionsTabComponent) inspectionsTab: InspectionsTabComponent;
  @ViewChild(StabilityTabComponent) stabilityTab: StabilityTabComponent;
  @ViewChild(AvancedTabComponent) avancedTab: AvancedTabComponent;
  @ViewChild(ConfigurationsTabComponent)
  configurationsTab: ConfigurationsTabComponent;
  @ViewChild(LayersTabComponent) layersTab: LayersTabComponent;

  public generalTabData: any = {};
  public responsibleTabData: any = {};
  public datasheetTabData: any = {};
  public inspectionsTabData: any = {};
  public stabilityTabData: any = {};
  public avancedTabData: any = {};
  public configurationsTabData: any = {};
  public layersTabData: any = {};

  public generalTabConfig: any = { styleColor: false, active: true };
  public responsibleTabConfig: any = { styleColor: false, active: false };
  public datasheetTabConfig: any = { styleColor: false, active: false };
  public inspectionsTabConfig: any = { styleColor: false, active: false };
  public stabilityTabConfig: any = { styleColor: false, active: false };
  public avancedTabConfig: any = { styleColor: false, active: false };
  public configurationsTabConfig: any = { styleColor: false, active: false };
  public layersTabConfig: any = { styleColor: false, active: false };

  public conditions = conditionsEnum;
  public calculationMethods = calculationMethodsEnum;

  public edit: boolean = false;
  public view: boolean = false;

  public profile: any = null;
  public permissaoUsuario: any = null;

  public structureRequest: any = {};

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public crtlSaveStructure: string = '';

  public formCrtl: boolean = false;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private userService: UserService,
    private structuresService: StructuresService,
    private ngxSpinnerService: NgxSpinnerService
  ) {}

  /**
   * Método de inicialização do componente.
   * Verifica se o perfil do usuário está carregado e determina se o componente está em modo de edição ou visualização.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;

    this.formCrtl = true;

    if (this.activatedRoute.snapshot.params.structureId) {
      this.edit = true;
      this.getStructure(this.activatedRoute.snapshot.params.structureId);
      if (this.activatedRoute.snapshot.url && this.activatedRoute.snapshot.url[1] && this.activatedRoute.snapshot.url[1].path == 'view') {
        this.edit = false;
        this.view = true;
      }
    }
  }

  /**
   * Método para buscar os dados de uma estrutura existente para edição ou visualização.
   * @param {string} structureId - O ID da estrutura a ser carregada.
   */
  getStructure(structureId: string) {
    this.structuresService.getStructureById(structureId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.splitTabDatas(dados);
      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Método para registrar uma nova estrutura.
   * Envia os dados da estrutura para a API e controla o estado do formulário durante o processo.
   */
  registerStructure() {
    this.ngxSpinnerService.show();

    this.formCrtl = false;

    this.messagesError = [];

    this.structuresService.postStructure(this.structureRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.SucessoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/structures']);
        }, 4000);

        this.controlForm('create');
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          if (error.error && error.error.errors) {
            const index = Object.keys(error.error.errors);
            this.messagesError = error.error.errors[index[0]][0];
          } else {
            this.messagesError = [];
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
            setTimeout(() => {
              this.messagesError = [];
            }, 4000);
            this.formCrtl = true;
            this.ngxSpinnerService.hide();
          }
        }
      }
    );
  }

  /**
   * Método para editar uma estrutura existente.
   * Envia os dados atualizados da estrutura para a API e controla o estado do formulário durante o processo.
   */
  editStructure() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.structuresService.putStructures(this.structureRequest.id, this.structureRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/structures']);
        }, 4000);
        this.controlForm('edit');
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status === 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);

          this.formCrtl = true;
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  /**
   * Método para controlar a seleção e navegação entre as abas do formulário.
   * @param {string} action - A ação a ser realizada ('create' ou 'edit').
   */
  controlForm(action: string = 'create') {
    this.selectTab('general');
  }

  /**
   * Método para selecionar uma aba específica no formulário.
   * @param {string} option - O nome da aba a ser ativada.
   */
  selectTab(option: any = '') {
    switch (option) {
      case 'general':
        this.generalTabConfig.active = true;
        this.responsibleTabConfig.active = false;
        this.datasheetTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.avancedTabConfig.active = false;
        this.layersTabConfig.active = false;
        this.configurationsTabConfig.active = false;
        break;
      case 'responsible':
        this.generalTabConfig.active = false;
        this.responsibleTabConfig.active = true;
        this.datasheetTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.avancedTabConfig.active = false;
        this.layersTabConfig.active = false;
        this.configurationsTabConfig.active = false;
        break;
      case 'datasheet':
        this.generalTabConfig.active = false;
        this.responsibleTabConfig.active = false;
        this.datasheetTabConfig.active = true;
        this.inspectionsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.avancedTabConfig.active = false;
        this.layersTabConfig.active = false;
        this.configurationsTabConfig.active = false;
        break;
      case 'inspections':
        this.generalTabConfig.active = false;
        this.responsibleTabConfig.active = false;
        this.datasheetTabConfig.active = false;
        this.inspectionsTabConfig.active = true;
        this.stabilityTabConfig.active = false;
        this.avancedTabConfig.active = false;
        this.layersTabConfig.active = false;
        this.configurationsTabConfig.active = false;
        break;
      case 'stability':
        this.generalTabConfig.active = false;
        this.responsibleTabConfig.active = false;
        this.datasheetTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityTabConfig.active = true;
        this.avancedTabConfig.active = false;
        this.layersTabConfig.active = false;
        this.configurationsTabConfig.active = false;
        break;
      case 'avanced':
        this.generalTabConfig.active = false;
        this.responsibleTabConfig.active = false;
        this.datasheetTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.avancedTabConfig.active = true;
        this.layersTabConfig.active = false;
        this.configurationsTabConfig.active = false;
        break;
      case 'layers':
        this.generalTabConfig.active = false;
        this.responsibleTabConfig.active = false;
        this.datasheetTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.avancedTabConfig.active = false;
        this.layersTabConfig.active = true;
        this.configurationsTabConfig.active = false;
        break;
      case 'configurations':
        this.generalTabConfig.active = false;
        this.responsibleTabConfig.active = false;
        this.datasheetTabConfig.active = false;
        this.inspectionsTabConfig.active = false;
        this.stabilityTabConfig.active = false;
        this.avancedTabConfig.active = false;
        this.layersTabConfig.active = false;
        this.configurationsTabConfig.active = true;
        break;
      default:
        break;
    }
  }

  /**
   * Método para separar e distribuir os dados da estrutura entre as diferentes abas do formulário.
   * @param {any} dados - Os dados da estrutura carregada da API.
   */
  splitTabDatas(dados: any) {
    //general-tab
    this.generalTabData.id = dados.id;
    this.generalTabData.name = dados.name;
    this.generalTabData.status = dados.status.toString();
    this.generalTabData.datum = dados.coordinate_setting.datum;
    this.generalTabData.coordinate_format = dados.coordinate_setting.coordinate_format.toString();
    this.generalTabData.zone_number = dados.coordinate_setting.coordinate_systems.utm.zone_number;
    this.generalTabData.zone_letter = dados.coordinate_setting.coordinate_systems.utm.zone_letter;
    this.generalTabData.northing = dados.coordinate_setting.coordinate_systems.utm.northing;
    this.generalTabData.easting = dados.coordinate_setting.coordinate_systems.utm.easting;
    this.generalTabData.latitude = dados.coordinate_setting.coordinate_systems.decimal_geodetic.latitude;
    this.generalTabData.longitude = dados.coordinate_setting.coordinate_systems.decimal_geodetic.longitude;
    //this.generalTabData.general_zoom = dados.map_configuration.general_zoom;
    //this.generalTabData.instruments_zoom = dados.map_configuration.instruments_zoom;
    this.generalTabData.client_unit = [dados.client_unit];
    this.generalTab.setData(this.generalTabData);

    //responsible-tab
    this.responsibleTabData.responsibles = dados.responsibles;
    this.responsibleTab.setData(this.responsibleTabData);

    //datasheet-tab
    this.datasheetTabData.countries = fn.isEmpty(dados.country) ? '' : dados.country.id;
    this.datasheetTabData.states = fn.isEmpty(dados.state) ? '' : dados.state.id;
    this.datasheetTabData.cities = fn.isEmpty(dados.city) ? '' : dados.city.id;
    this.datasheetTabData.purpose = dados.purpose;
    // this.datasheetTabData.year_of_construction_initial_dike = dados.year_of_construction_initial_dike;
    this.datasheetTabData.construction_stages = dados.construction_stages;
    this.datasheetTabData.crest_dimension_width = dados.crest_dimension.width;
    this.datasheetTabData.crest_dimension_length = dados.crest_dimension.length;
    this.datasheetTabData.total_height = dados.total_height;
    this.datasheetTabData.downstream_slope = dados.downstream_slope;
    this.datasheetTabData.upstream_slope = dados.upstream_slope;
    this.datasheetTabData.classification = fn.isEmpty(dados.classification) ? '' : dados.classification.toString();
    this.datasheetTabData.section_type = dados.section_type;
    this.datasheetTabData.foundation_type = dados.foundation_type;
    this.datasheetTabData.raising_method = dados.raising_method;
    this.datasheetTabData.expected_elevations = dados.expected_elevations;
    this.datasheetTabData.elevations_made = dados.elevations_made;
    this.datasheetTabData.reservoir_design_volume = dados.reservoir_design_volume;
    this.datasheetTabData.current_reservoir_volume = dados.current_reservoir_volume;
    this.datasheetTabData.internal_drainage = dados.internal_drainage;
    this.datasheetTabData.superficial_drainage = dados.superficial_drainage;
    this.datasheetTabData.basin_area_in_square_kilometers = dados.basin_area_in_square_kilometers;
    this.datasheetTabData.project_precipitation = dados.project_precipitation;
    this.datasheetTabData.maximum_influent_flow = dados.maximum_influent_flow;
    this.datasheetTabData.project_flow = dados.project_flow;
    this.datasheetTabData.full_of_project = dados.full_of_project;
    this.datasheetTabData.normal_maximum_water_level = dados.normal_maximum_water_level;
    this.datasheetTabData.maximum_water_level_maximorum = dados.maximum_water_level_maximorum;
    this.datasheetTabData.freeboard_normal_maximum_water_level = dados.freeboard_normal_maximum_water_level;
    this.datasheetTabData.freeboard_maximum_water_level_maximorum = dados.freeboard_maximum_water_level_maximorum;
    this.datasheetTabData.spillway = dados.spillway;
    // this.datasheetTabData.constitutive_stage = dados.constitutive_stage;
    this.datasheetTabData.construction_year = dados.construction_year;
    this.datasheetTabData.start_of_operation_date = dados.start_of_operation_date;
    this.datasheetTabData.end_of_operation_date = dados.end_of_operation_date;
    this.datasheetTabData.designing_company = dados.designing_company;
    this.datasheetTabData.current_status_of_dam = dados.current_status_of_dam;
    this.datasheetTabData.crest_quota = dados.crest_quota;
    // this.datasheetTabData.surface_area = dados.surface_area;
    this.datasheetTabData.spillway_sill_quota = dados.spillway_sill_quota;
    this.datasheetTabData.intercepted_watercourse = dados.intercepted_watercourse;

    if (dados.environmental_damage_potential != null) {
      //Tabela Potencial Dano Ambiental
      this.datasheetTabData.total_reservoir_volume = dados.environmental_damage_potential.total_reservoir_volume;
      this.datasheetTabData.population_downstream = dados.environmental_damage_potential.population_downstream;
      this.datasheetTabData.environmental_impact = dados.environmental_damage_potential.environmental_impact;
      this.datasheetTabData.socioeconomic_impact = dados.environmental_damage_potential.socioeconomic_impact;
      this.datasheetTabData.environmental_damage_potential_total = dados.environmental_damage_potential.environmental_damage_potential_total;
    }

    this.datasheetTab.setData(this.datasheetTabData);

    //inspections-tab
    this.inspectionsTabData.aspects = dados.aspects;
    this.inspectionsTab.setData(this.inspectionsTabData);

    //stability-tab
    const conditionsConst = fn.objectToArray(this.conditions, { id: 'id', value: 'name' }, false);
    this.stabilityTabData.should_evaluate = [];
    dados.should_evaluate_drained_condition == true ? this.stabilityTabData.should_evaluate.push(conditionsConst['should_evaluate_drained_condition']) : null;
    dados.should_evaluate_undrained_condition == true
      ? this.stabilityTabData.should_evaluate.push(conditionsConst['should_evaluate_undrained_condition'])
      : null;
    dados.should_evaluate_pseudo_static_condition == true
      ? this.stabilityTabData.should_evaluate.push(conditionsConst['should_evaluate_pseudo_static_condition'])
      : null;

    this.stabilityTabData.seismic_coefficient_horizontal = dados.seismic_coefficient.horizontal;
    this.stabilityTabData.seismic_coefficient_vertical = dados.seismic_coefficient.vertical;
    this.stabilityTabData.gravity = dados.gravity;
    this.stabilityTabData.structure_type = dados.structure_type.id;
    this.stabilityTab.setData(this.stabilityTabData);

    //avanced-tab
    this.avancedTabData.has_auto_update = dados.has_auto_update;
    this.avancedTabData.protocol = fn.isEmpty(dados.protocol) ? '' : dados.protocol.toString();
    this.avancedTabData.frequency_to_fetch_data = dados.frequency_to_fetch_data.interval;
    this.avancedTabData.last_data_fetch = dados.frequency_to_fetch_data.last_update;
    this.avancedTabData.frequency_to_generate_packages = dados.frequency_to_generate_packages.interval;
    this.avancedTabData.last_generated_package = dados.frequency_to_generate_packages.last_update;

    this.avancedTab.setData(this.avancedTabData);

    //configurations-tab
    if (dados.slide2_configuration.circular_parameters != null) {
      this.configurationsTabData.surface_type_circular = dados.slide2_configuration.circular_parameters.circular_search_method - 1;
      this.configurationsTabData.circular_divisions_along_slope = dados.slide2_configuration.circular_parameters.divisions_along_slope;
      this.configurationsTabData.circular_circles_per_division = dados.slide2_configuration.circular_parameters.circles_per_division;
      this.configurationsTabData.circular_number_of_iterations = dados.slide2_configuration.circular_parameters.number_of_iterations;
      this.configurationsTabData.circular_divisions_next_iteration = dados.slide2_configuration.circular_parameters.divisions_next_iteration;
      this.configurationsTabData.circular_radius_increment = dados.slide2_configuration.circular_parameters.radius_increment;
      this.configurationsTabData.circular_number_of_surfaces = dados.slide2_configuration.circular_parameters.number_of_surfaces;
    }

    if (dados.slide2_configuration.non_circular_parameters != null) {
      this.configurationsTabData.surface_type_non_circular = dados.slide2_configuration.non_circular_parameters.non_circular_search_method - 1;
      this.configurationsTabData.non_circular_divisions_along_slope = dados.slide2_configuration.non_circular_parameters.divisions_along_slope;
      this.configurationsTabData.non_circular_surfaces_per_division = dados.slide2_configuration.non_circular_parameters.surfaces_per_division;
      this.configurationsTabData.non_circular_number_of_iterations = dados.slide2_configuration.non_circular_parameters.number_of_iterations;
      this.configurationsTabData.non_circular_divisions_next_iteration = dados.slide2_configuration.non_circular_parameters.divisions_next_iteration;
      this.configurationsTabData.non_circular_number_of_vertices_along_surface =
        dados.slide2_configuration.non_circular_parameters.number_of_vertices_along_surface;
      this.configurationsTabData.non_circular_number_of_surfaces = dados.slide2_configuration.non_circular_parameters.number_of_surfaces;
      this.configurationsTabData.non_circular_number_of_nests = dados.slide2_configuration.non_circular_parameters.number_of_nests;
      this.configurationsTabData.non_circular_maximum_iterations = dados.slide2_configuration.non_circular_parameters.maximum_iterations;
      this.configurationsTabData.non_circular_initial_number_of_surface_vertices = dados.slide2_configuration.non_circular_parameters
        .initial_number_of_surface_vertices
        ? dados.slide2_configuration.non_circular_parameters.initial_number_of_surface_vertices
        : 0;
      this.configurationsTabData.non_circular_initial_number_of_iterations = dados.slide2_configuration.non_circular_parameters.initial_number_of_iterations;
      this.configurationsTabData.non_circular_maximum_number_of_steps = dados.slide2_configuration.non_circular_parameters.maximum_number_of_steps;
      this.configurationsTabData.non_circular_number_of_factors_safety_compared_before_stopping =
        dados.slide2_configuration.non_circular_parameters.number_of_factors_safety_compared_before_stopping;
      this.configurationsTabData.non_circular_tolerance_for_stopping_criterion =
        dados.slide2_configuration.non_circular_parameters.tolerance_for_stopping_criterion;
      this.configurationsTabData.non_circular_number_of_particles = dados.slide2_configuration.non_circular_parameters.number_of_particles;
    }

    const calculationMethodsConst = fn.objectToArray(this.calculationMethods, { id: 'id', value: 'name' }, false);

    this.configurationsTabData.calculation_methods_circular = [];
    if (dados.slide2_configuration.circular_parameters != null) {
      dados.slide2_configuration.circular_parameters.calculation_methods.map((item: any) => {
        this.configurationsTabData.calculation_methods_circular.push(calculationMethodsConst[item]);
      });
    }

    this.configurationsTabData.calculation_methods_non_circular = [];
    if (dados.slide2_configuration.non_circular_parameters != null) {
      dados.slide2_configuration.non_circular_parameters.calculation_methods.map((item: any) => {
        this.configurationsTabData.calculation_methods_non_circular.push(calculationMethodsConst[item]);
      });
    }

    this.configurationsTab.setData(this.configurationsTabData);

    //layers-tab
    this.layersTab.setData(dados.layers);
  }

  /**
   * Método para reunir os dados de todas as abas em um único objeto para envio.
   * @param {string} action - A ação a ser realizada ('create' ou 'edit').
   */
  joinTabDatas(action: string = 'create') {
    this.structureRequest = {
      id: null,
      client_unit: {
        id: null,
        name: null,
        active: null
      },
      name: null,
      status: null,
      coordinate_setting: {
        datum: null,
        coordinate_format: null,
        coordinate_systems: {
          utm: {
            zone_number: null,
            zone_letter: null,
            northing: null,
            easting: null
          },
          decimal_geodetic: {
            latitude: null,
            longitude: null
          }
        }
      },
      map_configuration: {
        general_zoom: null,
        instruments_zoom: null
      },
      responsibles: [],
      country: {
        id: null
      },
      state: {
        id: null
      },
      city: {
        id: null
      },
      purpose: null,
      // year_of_construction_initial_dike: null,
      construction_stages: null,
      crest_dimension: {
        width: null,
        length: null
      },
      total_height: null,
      downstream_slope: null,
      upstream_slope: null,
      classification: null,
      section_type: null,
      foundation_type: null,
      raising_method: null,
      expected_elevations: null,
      elevations_made: null,
      reservoir_design_volume: null,
      current_reservoir_volume: null,
      internal_drainage: null,
      superficial_drainage: null,
      basin_area_in_square_kilometers: null,
      project_precipitation: null,
      full_of_project: null,
      maximum_influent_flow: null,
      project_flow: null,
      normal_maximum_water_level: null,
      maximum_water_level_maximorum: null,
      freeboard_normal_maximum_water_level: null,
      freeboard_maximum_water_level_maximorum: null,
      spillway: null,
      aspects: [],
      structure_type: {
        id: null
      },
      should_evaluate_drained_condition: false,
      should_evaluate_undrained_condition: false,
      should_evaluate_pseudo_static_condition: false,
      seismic_coefficient: {
        horizontal: null,
        vertical: null
      },
      gravity: null,
      has_auto_update: false,
      protocol: null,
      frequency_to_fetch_data: {
        interval: null,
        last_update: null
      },
      frequency_to_generate_packages: {
        interval: null,
        last_update: null
      },
      slide2_configuration: {
        circular_parameters: {
          calculation_methods: [],
          circular_search_method: null,
          divisions_along_slope: null,
          circles_per_division: null,
          number_of_iterations: null,
          divisions_next_iteration: null,
          radius_increment: null,
          number_of_surfaces: null
        },
        non_circular_parameters: {
          calculation_methods: [],
          non_circular_search_method: null,
          divisions_along_slope: null,
          surfaces_per_division: null,
          number_of_iterations: null,
          divisions_next_iteration: null,
          number_of_vertices_along_surface: null,
          number_of_surfaces: null,
          number_of_nests: null,
          maximum_iterations: null,
          initial_number_of_surface_vertices: null,
          initial_number_of_iterations: null,
          maximum_number_of_steps: null,
          number_of_factors_safety_compared_before_stopping: null,
          tolerance_for_stopping_criterion: null,
          number_of_particles: null
        }
      },
      //Aba Ficha Técnica
      // constitutive_stage: null,
      construction_year: null,
      start_of_operation_date: null,
      end_of_operation_date: null,
      designing_company: null,
      current_status_of_dam: null,
      crest_quota: null,
      // surface_area: null,
      spillway_sill_quota: null,
      intercepted_watercourse: null,
      environmental_damage_potential: {
        total_reservoir_volume: null,
        population_downstream: null,
        environmental_impact: null,
        socioeconomic_impact: null,
        environmental_damage_potential_total: null
      },
      //Aba Layers
      layers: []
    };

    //general-tab
    this.generalTabData = this.generalTab.getData();
    this.structureRequest.client_unit.id = this.generalTabData.client_unit[0].id;
    this.structureRequest.client_unit.name = this.generalTabData.client_unit[0].name;
    this.structureRequest.name = this.generalTabData.name;
    this.structureRequest.status = parseInt(this.generalTabData.status);
    this.structureRequest.coordinate_setting.datum = this.generalTabData.datum;
    this.structureRequest.coordinate_setting.coordinate_format = parseInt(this.generalTabData.coordinate_format);
    this.structureRequest.coordinate_setting.coordinate_systems.utm.zone_number = this.generalTabData.zone_number;
    this.structureRequest.coordinate_setting.coordinate_systems.utm.zone_letter = this.generalTabData.zone_letter;
    this.structureRequest.coordinate_setting.coordinate_systems.utm.northing = this.generalTabData.northing;
    this.structureRequest.coordinate_setting.coordinate_systems.utm.easting = this.generalTabData.easting;
    this.structureRequest.coordinate_setting.coordinate_systems.decimal_geodetic.latitude = this.generalTabData.latitude;
    this.structureRequest.coordinate_setting.coordinate_systems.decimal_geodetic.longitude = this.generalTabData.longitude;
    this.structureRequest.map_configuration.general_zoom = this.generalTabData.general_zoom;
    this.structureRequest.map_configuration.instruments_zoom = this.generalTabData.instruments_zoom;

    //responsible-tab
    this.responsibleTabData = this.responsibleTab.getData();
    this.structureRequest.responsibles = this.responsibleTabData.responsibles;

    //datasheet-tab
    this.datasheetTabData = this.datasheetTab.getData();

    if (this.datasheetTabData.countries == '') {
      this.structureRequest.country = null;
    } else {
      this.structureRequest.country.id = this.datasheetTabData.countries;
    }

    if (this.datasheetTabData.states == '') {
      this.structureRequest.state = null;
    } else {
      this.structureRequest.state.id = this.datasheetTabData.states;
    }

    if (this.datasheetTabData.cities == '') {
      this.structureRequest.city = null;
    } else {
      this.structureRequest.city.id = this.datasheetTabData.cities;
    }

    this.structureRequest.purpose = this.datasheetTabData.purpose;
    // this.structureRequest.year_of_construction_initial_dike = fn.isEmpty(this.datasheetTabData.year_of_construction_initial_dike)
    //   ? 0
    //   : this.datasheetTabData.year_of_construction_initial_dike;
    this.structureRequest.construction_stages = fn.isEmpty(this.datasheetTabData.construction_stages)
      ? 0
      : this.datasheetTabData.construction_stages.toString();
    this.structureRequest.crest_dimension.width = fn.isEmpty(this.datasheetTabData.crest_dimension_width) ? 0 : this.datasheetTabData.crest_dimension_width;
    this.structureRequest.crest_dimension.length = fn.isEmpty(this.datasheetTabData.crest_dimension_length) ? 0 : this.datasheetTabData.crest_dimension_length;
    this.structureRequest.total_height = fn.isEmpty(this.datasheetTabData.total_height) ? 0 : this.datasheetTabData.total_height;
    this.structureRequest.downstream_slope = this.datasheetTabData.downstream_slope;
    this.structureRequest.upstream_slope = this.datasheetTabData.upstream_slope;
    this.structureRequest.classification = fn.isEmpty(this.datasheetTabData.classification) ? null : parseInt(this.datasheetTabData.classification);
    this.structureRequest.section_type = this.datasheetTabData.section_type;
    this.structureRequest.foundation_type = this.datasheetTabData.foundation_type;
    this.structureRequest.raising_method = this.datasheetTabData.raising_method;
    this.structureRequest.expected_elevations = fn.isEmpty(this.datasheetTabData.expected_elevations) ? 0 : this.datasheetTabData.expected_elevations;
    this.structureRequest.elevations_made = fn.isEmpty(this.datasheetTabData.elevations_made) ? 0 : this.datasheetTabData.elevations_made;
    this.structureRequest.reservoir_design_volume = fn.isEmpty(this.datasheetTabData.reservoir_design_volume)
      ? 0
      : this.datasheetTabData.reservoir_design_volume;
    this.structureRequest.current_reservoir_volume = fn.isEmpty(this.datasheetTabData.current_reservoir_volume)
      ? 0
      : this.datasheetTabData.current_reservoir_volume;
    this.structureRequest.internal_drainage = this.datasheetTabData.internal_drainage;
    this.structureRequest.superficial_drainage = this.datasheetTabData.superficial_drainage;
    this.structureRequest.basin_area_in_square_kilometers = fn.isEmpty(this.datasheetTabData.basin_area_in_square_kilometers)
      ? 0
      : this.datasheetTabData.basin_area_in_square_kilometers;
    this.structureRequest.project_precipitation = fn.isEmpty(this.datasheetTabData.project_precipitation) ? 0 : this.datasheetTabData.project_precipitation;
    this.structureRequest.full_of_project = fn.isEmpty(this.datasheetTabData.full_of_project) ? 0 : this.datasheetTabData.full_of_project;
    this.structureRequest.maximum_influent_flow = fn.isEmpty(this.datasheetTabData.maximum_influent_flow) ? 0 : this.datasheetTabData.maximum_influent_flow;
    this.structureRequest.project_flow = fn.isEmpty(this.datasheetTabData.project_flow) ? 0 : this.datasheetTabData.project_flow;
    this.structureRequest.normal_maximum_water_level = fn.isEmpty(this.datasheetTabData.normal_maximum_water_level)
      ? 0
      : this.datasheetTabData.normal_maximum_water_level;
    this.structureRequest.maximum_water_level_maximorum = fn.isEmpty(this.datasheetTabData.maximum_water_level_maximorum)
      ? 0
      : this.datasheetTabData.maximum_water_level_maximorum;
    this.structureRequest.freeboard_normal_maximum_water_level = fn.isEmpty(this.datasheetTabData.freeboard_normal_maximum_water_level)
      ? 0
      : this.datasheetTabData.freeboard_normal_maximum_water_level;
    this.structureRequest.freeboard_maximum_water_level_maximorum = fn.isEmpty(this.datasheetTabData.freeboard_maximum_water_level_maximorum)
      ? 0
      : this.datasheetTabData.freeboard_maximum_water_level_maximorum;
    this.structureRequest.spillway = this.datasheetTabData.spillway;
    // this.structureRequest.constitutive_stage = this.datasheetTabData.constitutive_stage;
    this.structureRequest.construction_year = fn.isEmpty(this.datasheetTabData.construction_year) ? 0 : this.datasheetTabData.construction_year;
    this.structureRequest.start_of_operation_date = fn.isEmpty(this.datasheetTabData.start_of_operation_date)
      ? 0
      : this.datasheetTabData.start_of_operation_date;
    this.structureRequest.end_of_operation_date = fn.isEmpty(this.datasheetTabData.end_of_operation_date) ? 0 : this.datasheetTabData.end_of_operation_date;
    this.structureRequest.designing_company = this.datasheetTabData.designing_company;
    this.structureRequest.current_status_of_dam = this.datasheetTabData.current_status_of_dam;
    this.structureRequest.crest_quota = fn.isEmpty(this.datasheetTabData.crest_quota) ? 0 : this.datasheetTabData.crest_quota;
    // this.structureRequest.surface_area = fn.isEmpty(this.datasheetTabData.surface_area) ? 0 : this.datasheetTabData.surface_area;
    this.structureRequest.spillway_sill_quota = fn.isEmpty(this.datasheetTabData.spillway_sill_quota) ? 0 : this.datasheetTabData.spillway_sill_quota;
    this.structureRequest.intercepted_watercourse = fn.isEmpty(this.datasheetTabData.intercepted_watercourse)
      ? 0
      : this.datasheetTabData.intercepted_watercourse;
    this.structureRequest.environmental_damage_potential.total_reservoir_volume = fn.isEmpty(this.datasheetTabData.total_reservoir_volume)
      ? 0
      : this.datasheetTabData.total_reservoir_volume;
    this.structureRequest.environmental_damage_potential.population_downstream = fn.isEmpty(this.datasheetTabData.population_downstream)
      ? 0
      : this.datasheetTabData.population_downstream;
    this.structureRequest.environmental_damage_potential.environmental_impact = fn.isEmpty(this.datasheetTabData.environmental_impact)
      ? 0
      : this.datasheetTabData.environmental_impact;
    this.structureRequest.environmental_damage_potential.socioeconomic_impact = fn.isEmpty(this.datasheetTabData.socioeconomic_impact)
      ? 0
      : this.datasheetTabData.socioeconomic_impact;
    this.structureRequest.environmental_damage_potential.environmental_damage_potential_total = fn.isEmpty(
      this.datasheetTabData.environmental_damage_potential_total
    )
      ? 0
      : this.datasheetTabData.environmental_damage_potential_total;

    //inspections-tab
    this.inspectionsTabData = this.inspectionsTab.getData();
    this.structureRequest.aspects = this.inspectionsTabData.aspects;

    //stability-tab
    this.stabilityTabData = this.stabilityTab.getData();
    this.structureRequest.structure_type.id = this.stabilityTabData.structure_type;
    this.structureRequest.should_evaluate_drained_condition = this.stabilityTabData.should_evaluate_drained_condition
      ? this.stabilityTabData.should_evaluate_drained_condition
      : false;
    this.structureRequest.should_evaluate_pseudo_static_condition = this.stabilityTabData.should_evaluate_pseudo_static_condition
      ? this.stabilityTabData.should_evaluate_pseudo_static_condition
      : false;
    this.structureRequest.should_evaluate_undrained_condition = this.stabilityTabData.should_evaluate_undrained_condition
      ? this.stabilityTabData.should_evaluate_undrained_condition
      : false;
    this.structureRequest.seismic_coefficient.horizontal = this.stabilityTabData.seismic_coefficient_horizontal;
    this.structureRequest.seismic_coefficient.vertical = this.stabilityTabData.seismic_coefficient_vertical;
    this.structureRequest.gravity = this.stabilityTabData.gravity;

    //avanced-tab
    this.avancedTabData = this.avancedTab.getData();
    this.structureRequest.has_auto_update = this.avancedTabData.has_auto_update;
    this.structureRequest.protocol = fn.isEmpty(this.avancedTabData.protocol) ? null : parseInt(this.avancedTabData.protocol);
    this.structureRequest.frequency_to_fetch_data.interval = this.avancedTabData.frequency_to_fetch_data;
    this.structureRequest.frequency_to_generate_packages.interval = this.avancedTabData.frequency_to_generate_packages;

    //configurations-tab
    this.configurationsTabData = this.configurationsTab.getData();

    if (this.configurationsTabData.calculation_methods_circular.length > 0) {
      this.structureRequest.slide2_configuration.circular_parameters.calculation_methods = this.configurationsTabData.calculation_methods_circular;
      this.structureRequest.slide2_configuration.circular_parameters.circular_search_method = parseInt(this.configurationsTabData.surface_type_circular) + 1;
      this.structureRequest.slide2_configuration.circular_parameters.divisions_along_slope = fn.isEmpty(
        this.configurationsTabData.circular_divisions_along_slope
      )
        ? 0
        : this.configurationsTabData.circular_divisions_along_slope;

      this.structureRequest.slide2_configuration.circular_parameters.circles_per_division = fn.isEmpty(this.configurationsTabData.circular_circles_per_division)
        ? 0
        : this.configurationsTabData.circular_circles_per_division;

      this.structureRequest.slide2_configuration.circular_parameters.number_of_iterations = fn.isEmpty(this.configurationsTabData.circular_number_of_iterations)
        ? 0
        : this.configurationsTabData.circular_number_of_iterations;

      this.structureRequest.slide2_configuration.circular_parameters.divisions_next_iteration = fn.isEmpty(
        this.configurationsTabData.circular_divisions_next_iteration
      )
        ? 0
        : this.configurationsTabData.circular_divisions_next_iteration;

      this.structureRequest.slide2_configuration.circular_parameters.radius_increment = fn.isEmpty(this.configurationsTabData.circular_radius_increment)
        ? 0
        : this.configurationsTabData.circular_radius_increment;

      this.structureRequest.slide2_configuration.circular_parameters.number_of_surfaces = fn.isEmpty(this.configurationsTabData.circular_number_of_surfaces)
        ? 0
        : this.configurationsTabData.circular_number_of_surfaces;
    } else {
      this.structureRequest.slide2_configuration.circular_parameters = null;
    }

    if (this.configurationsTabData.calculation_methods_non_circular.length > 0) {
      this.structureRequest.slide2_configuration.non_circular_parameters.calculation_methods = this.configurationsTabData.calculation_methods_non_circular;
      this.structureRequest.slide2_configuration.non_circular_parameters.non_circular_search_method =
        parseInt(this.configurationsTabData.surface_type_non_circular) + 1;

      this.structureRequest.slide2_configuration.non_circular_parameters.divisions_along_slope = fn.isEmpty(
        this.configurationsTabData.non_circular_divisions_along_slope
      )
        ? 0
        : this.configurationsTabData.non_circular_divisions_along_slope;

      this.structureRequest.slide2_configuration.non_circular_parameters.divisions_next_iteration = fn.isEmpty(
        this.configurationsTabData.non_circular_divisions_next_iteration
      )
        ? 0
        : this.configurationsTabData.non_circular_divisions_next_iteration;

      this.structureRequest.slide2_configuration.non_circular_parameters.initial_number_of_iterations = fn.isEmpty(
        this.configurationsTabData.non_circular_initial_number_of_iterations
      )
        ? 0
        : this.configurationsTabData.non_circular_initial_number_of_iterations;

      this.structureRequest.slide2_configuration.non_circular_parameters.initial_number_of_surface_vertices = fn.isEmpty(
        this.configurationsTabData.non_circular_initial_number_of_surface_vertices
      )
        ? 0
        : this.configurationsTabData.non_circular_initial_number_of_surface_vertices;

      this.structureRequest.slide2_configuration.non_circular_parameters.maximum_iterations = fn.isEmpty(
        this.configurationsTabData.non_circular_maximum_iterations
      )
        ? 0
        : this.configurationsTabData.non_circular_maximum_iterations;

      this.structureRequest.slide2_configuration.non_circular_parameters.maximum_number_of_steps = fn.isEmpty(
        this.configurationsTabData.non_circular_maximum_number_of_steps
      )
        ? 0
        : this.configurationsTabData.non_circular_maximum_number_of_steps;

      this.structureRequest.slide2_configuration.non_circular_parameters.number_of_factors_safety_compared_before_stopping = fn.isEmpty(
        this.configurationsTabData.non_circular_number_of_factors_safety_compared_before_stopping
      )
        ? 0
        : this.configurationsTabData.non_circular_number_of_factors_safety_compared_before_stopping;

      this.structureRequest.slide2_configuration.non_circular_parameters.number_of_iterations = fn.isEmpty(
        this.configurationsTabData.non_circular_number_of_iterations
      )
        ? 0
        : this.configurationsTabData.non_circular_number_of_iterations;

      this.structureRequest.slide2_configuration.non_circular_parameters.number_of_nests = fn.isEmpty(this.configurationsTabData.non_circular_number_of_nests)
        ? 0
        : this.configurationsTabData.non_circular_number_of_nests;

      this.structureRequest.slide2_configuration.non_circular_parameters.number_of_particles = fn.isEmpty(
        this.configurationsTabData.non_circular_number_of_particles
      )
        ? 0
        : this.configurationsTabData.non_circular_number_of_particles;

      this.structureRequest.slide2_configuration.non_circular_parameters.number_of_surfaces = fn.isEmpty(
        this.configurationsTabData.non_circular_number_of_surfaces
      )
        ? 0
        : this.configurationsTabData.non_circular_number_of_surfaces;

      this.structureRequest.slide2_configuration.non_circular_parameters.number_of_vertices_along_surface = fn.isEmpty(
        this.configurationsTabData.non_circular_number_of_vertices_along_surface
      )
        ? 0
        : this.configurationsTabData.non_circular_number_of_vertices_along_surface;

      this.structureRequest.slide2_configuration.non_circular_parameters.surfaces_per_division = fn.isEmpty(
        this.configurationsTabData.non_circular_surfaces_per_division
      )
        ? 0
        : this.configurationsTabData.non_circular_surfaces_per_division;

      this.structureRequest.slide2_configuration.non_circular_parameters.tolerance_for_stopping_criterion = fn.isEmpty(
        this.configurationsTabData.non_circular_tolerance_for_stopping_criterion
      )
        ? 0
        : this.configurationsTabData.non_circular_tolerance_for_stopping_criterion;
    } else {
      this.structureRequest.slide2_configuration.non_circular_parameters = null;
    }

    //Coleta de dados de cada aba
    this.layersTabData = this.layersTab.getData();
    this.structureRequest.layers = this.layersTabData;

    if (action === 'create') {
      if (this.structureRequest && this.structureRequest.layers) {
        delete this.structureRequest.id;
        if (this.structureRequest.layers.length > 0) {
          delete this.structureRequest.layers[0].id;
        }
        this.registerStructure();
      } else {
        console.error('structureRequest or its properties are undefined');
      }
    } else if (action === 'edit') {
      if (this.structureRequest) {
        this.structureRequest.id = this.activatedRoute.snapshot.params.structureId;
        this.editStructure();
      } else {
        console.error('structureRequest is undefined');
      }
    }
  }

  /**
   * Método para validar o formulário antes de enviar os dados.
   * Verifica se todas as abas possuem dados válidos antes de prosseguir.
   */
  validate() {
    const action = !this.edit ? 'create' : 'edit';

    // Verificação de validade para cada aba
    this.generalTabConfig.styleColor = !this.generalTab.formStructure.valid;
    this.stabilityTabConfig.styleColor = !this.stabilityTab.formStructureStability.valid;
    this.avancedTabConfig.styleColor = !this.avancedTab.formAvancedTab.valid;
    this.configurationsTabConfig.styleColor = !this.configurationsTab.formConfigurations.valid;

    // Marcar todos os campos como tocados se inválidos
    if (!this.generalTab.formStructure.valid) {
      this.generalTab.formStructure.markAllAsTouched();
    }

    if (!this.stabilityTab.formStructureStability.valid) {
      this.stabilityTab.formStructureStability.markAllAsTouched();
    }

    if (!this.avancedTab.formAvancedTab.valid) {
      this.avancedTab.formAvancedTab.markAllAsTouched();
    }

    if (!this.configurationsTab.formConfigurations.valid) {
      this.configurationsTab.managerValidateSurfacesType();
      this.configurationsTab.formConfigurations.markAllAsTouched();
    }
    // Se todas as abas forem válidas, reunir os dados e enviar
    if (
      !this.generalTabConfig.styleColor &&
      !this.stabilityTabConfig.styleColor &&
      !this.avancedTabConfig.styleColor &&
      !this.configurationsTabConfig.styleColor
    ) {
      this.joinTabDatas(action);
    }
  }

  onNext(): void {
    this.crtlSaveStructure = '';

    if (this.generalTabConfig.active) {
      this.selectTab('responsible');
    } else if (this.responsibleTabConfig.active) {
      this.selectTab('datasheet');
    } else if (this.datasheetTabConfig.active) {
      this.selectTab('inspections');
    } else if (this.inspectionsTabConfig.active) {
      this.selectTab('stability');
    } else if (this.stabilityTabConfig.active) {
      this.selectTab('avanced');
    } else if (this.avancedTabConfig.active) {
      this.selectTab('layers');
    } else if (this.layersTabConfig.active) {
      this.selectTab('configurations');
      this.crtlSaveStructure = 'configurations-tab';
    }
  }

  onPrevious(): void {
    this.crtlSaveStructure = '';

    if (this.configurationsTabConfig.active) {
      this.selectTab('layers');
    } else if (this.layersTabConfig.active) {
      this.selectTab('avanced');
    } else if (this.avancedTabConfig.active) {
      this.selectTab('stability');
    } else if (this.stabilityTabConfig.active) {
      this.selectTab('inspections');
    } else if (this.inspectionsTabConfig.active) {
      this.selectTab('datasheet');
    } else if (this.datasheetTabConfig.active) {
      this.selectTab('responsible');
    } else if (this.responsibleTabConfig.active) {
      this.selectTab('general');
    }
  }
}
