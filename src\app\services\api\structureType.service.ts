import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class StructureTypeService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  // Cadastro tipo de estruturas
  postStructureTypes(params: any) {
    const url = '/structure-types';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna os tipos de estruturas para uso em filtro
  getStructureTypes(params: any) {
    const url = '/structure-types/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getStructureTypesList(params: any = {}) {
    const url = '/structure-types';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca o tipo de estrutura por ID
  getStructureTypesById(id: string) {
    const url = `/structure-types/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putStructureTypes(id: string, params: any) {
    const url = `/structure-types/${id}`;
    return this.api.put<any>(url, params, 'client');
  }
}
