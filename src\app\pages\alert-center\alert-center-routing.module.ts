import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { AlertCenterComponent } from './alert-center.component';

import { AppGuard } from '../../guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: AlertCenterComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AlertCenterRoutingModule {}
