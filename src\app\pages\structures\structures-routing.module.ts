import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListStructuresComponent } from './list-structures/list-structures.component';
import { RegisterStructureComponent } from './register-structure/register-structure.component';
import { ImagesStructureComponent } from './images-structure/images-structure.component';
import { HistoryStructureComponent } from './history-structure/history-structure.component';

import { AppGuard } from './../../guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListStructuresComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastrarEstrutura,
    component: RegisterStructureComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarEstrutura,
    component: RegisterStructureComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarEstrutura,
    component: RegisterStructureComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.ImagensEstrutura,
    component: ImagesStructureComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.HistoricoEstrutura,
    component: HistoryStructureComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StructuresRoutingModule {}
