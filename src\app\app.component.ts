import { Component, OnInit } from '@angular/core';
import { AuthService } from './services/auth.service';
import { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  public isAuthChecked: boolean = false;

  constructor(public authService: AuthService, private router: Router, private spinner: NgxSpinnerService) {}

  ngOnInit(): void {
    // Verificação de autenticação
    this.authService.checkAuth().subscribe(
      (auth) => {
        if (auth) {
          this.isAuthChecked = true;
        } else {
          this.authService.login();
        }
      },
      (error) => {
        console.error('Erro durante a verificação de autenticação:', error);
      }
    );

    //Gerenciamento do spinner durante navegação
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.spinner.show(); // Mostra o spinner no início da navegação
      }

      if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {
        this.spinner.hide(); // Esconde o spinner ao término da navegação
      }
    });
  }
}
