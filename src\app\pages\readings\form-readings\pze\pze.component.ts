import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';

import { fieldsReading } from 'src/app/constants/readings.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-pze',
  templateUrl: './pze.component.html',
  styleUrls: ['./pze.component.scss']
})
export class PzeComponent implements OnInit, OnChanges {
  @Input() public instrumentsList: any = [];
  @Input() public index: number = null;
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public units: any = null;
  @Input() public typeInstrument: any = null;
  @Input() public datetime: any = null;

  @Output() public setInstrument = new EventEmitter();

  public formReading: FormGroup = new FormGroup({
    instrument: new FormControl([], [Validators.required]),
    pressure: new FormControl({ value: '', disabled: true }),
    date: new FormControl({ value: '', disabled: true }, [Validators.required]),
    quota: new FormControl({ value: '', disabled: true }),
    depth: new FormControl({ value: '', disabled: true }),
    dry: new FormControl({ value: false, disabled: true }),
    measure: new FormControl({ value: '', disabled: true }), //Celula de pressao
    //Para calcular
    top_quota: new FormControl(''),
    base_quota: new FormControl(''),
    //Para edicao
    id: new FormControl({ value: '', disabled: true })
  });

  public controls: any = null;
  public fieldsReading = fieldsReading;
  public func = fn;

  constructor(private formService: FormService) {}

  ngOnInit(): void {
    this.controls = this.formReading.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReading.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }

    if (changes.units && changes.units.previousValue != undefined && !(changes.units.previousValue === changes.units.currentValue)) {
      this.recalculate(changes.units.previousValue, changes.units.currentValue);
    }

    if (changes.datetime && changes.datetime.currentValue != null) {
      this.controls['date'].setValue(this.datetime);
    }
  }

  changeInstrument(instrument) {
    this.setInstrument.emit(instrument);
  }

  splitData($dados) {
    if (this.index > 0 || this.edit || this.view || this.spreadsheet) {
      this.controls['instrument'].disable();
      this.instrumentsList = $dados.instrumentsList;
    } else {
      this.controls['instrument'].enable();
    }

    this.formService.toggleFormList(this.formReading, this.fieldsReading[this.typeInstrument.id]);

    this.controls['instrument'].setValue($dados.instrument.id);
    this.controls['measure'].setValue($dados.measure.identifier);
    this.controls['top_quota'].setValue($dados.top_quota);
    this.controls['base_quota'].setValue($dados.base_quota);

    if ($dados.edit) {
      this.controls['id'].setValue($dados.edit.id);

      let date = $dados.edit.date.split('.');
      this.controls['date'].setValue(date[0]);

      this.controls['quota'].setValue($dados.edit.quota);
      this.controls['depth'].setValue($dados.edit.depth);
      this.controls['pressure'].setValue($dados.edit.pressure);
      this.controls['dry'].setValue($dados.edit.dry);
      this.toogleDry();
    }

    if (this.view) {
      this.formReading.disable();
    }
  }

  toogleDry() {
    if (this.controls['dry'].value) {
      this.controls['quota'].setValue('');
      this.controls['depth'].setValue('');
      this.controls['pressure'].setValue('');

      this.controls['quota'].disable('');
      this.controls['depth'].disable('');
      this.controls['pressure'].disable('');

      this.formReading.markAsUntouched();
    } else {
      this.controls['quota'].enable('');
      this.controls['depth'].enable('');
      this.controls['pressure'].enable('');
    }
  }

  recalculate(previousUnit, currentUnit) {
    if (!fn.isEmpty(this.controls['quota'].value) && previousUnit[0] != currentUnit[0]) {
      let quota = this.controls['quota'].value;
      quota = fn.convertLengthDecimal(quota, previousUnit[0], currentUnit[0]);
      this.controls['quota'].setValue(quota);
    }
  }
}
