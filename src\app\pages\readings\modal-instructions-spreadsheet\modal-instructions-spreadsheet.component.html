<ng-template #modalInstructionsSpreadsheet let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title" id="modal-profile-title">
      <i class="fa fa-info-circle"></i>
      Instruções de preenchimento
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <div class="modal-body scroll">
    <app-inapz-instructions
      *ngIf="
        typeInstrument.id == 1 ||
        typeInstrument.id == 2 ||
        typeInstrument.id == 3
      "
    ></app-inapz-instructions>
    <app-inc-conv-instructions
      *ngIf="typeInstrument.id == 4"
    ></app-inc-conv-instructions>
    <app-ipi-instructions *ngIf="typeInstrument.id == 5"></app-ipi-instructions>
    <app-ms-pr-instructions
      *ngIf="typeInstrument.id == 6 || typeInstrument.id == 7"
    ></app-ms-pr-instructions>
    <app-mr-instructions *ngIf="typeInstrument.id == 8"></app-mr-instructions>
    <app-geo-instructions *ngIf="typeInstrument.id == 9"></app-geo-instructions>
    <app-rl-instructions *ngIf="typeInstrument.id == 10"></app-rl-instructions>
  </div>
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-gray'"
      [label]="'Fechar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>
