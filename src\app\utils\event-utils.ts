import { EventInput } from '@fullcalendar/core';

let eventGuid = 0;
const TODAY_STR = new Date().toISOString().replace(/T.*$/, ''); // YYYY-MM-DD of today

export const INITIAL_EVENTS: EventInput[] = [
  // {
  //   id: createEventId(),
  //   title: ' [Logisoil] Daily',
  //   daysOfWeek: [1, 2, 3, 4, 5],
  //   start: TODAY_STR + 'T09:45:00',
  //   end: TODAY_STR + 'T10:00:00',
  //   constraint: 'businessHours',
  //   color: '#3788d8'
  // }
  // {
  //   id: createEventId(),
  //   title: 'Timed event',
  //   start: TODAY_STR + 'T00:00:00',
  //   end: TODAY_STR + 'T03:00:00'
  // },
  // {
  //   id: createEventId(),
  //   title: 'Almoço',
  //   start: TODAY_STR + 'T13:00:00',
  //   end: TODAY_STR + 'T14:00:00'
  // }
];

export function createEventId() {
  return String(eventGuid++);
}
