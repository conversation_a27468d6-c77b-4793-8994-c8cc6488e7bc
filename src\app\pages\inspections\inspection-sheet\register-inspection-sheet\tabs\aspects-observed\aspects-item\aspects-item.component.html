<div class="row" [formGroup]="formGroup">
  <div class="col-md-12">
    {{ index }} - {{ formGroup.get('description')?.value }}
  </div>

  <div class="col-md-12">
    <div class="row py-1 h-100" formArrayName="occurrences">
      <div class="col-md-12">
        <table class="table align-middle" style="width: 100%">
          <thead>
            <tr>
              <!-- <PERSON>abeçal<PERSON> das colunas -->
              <th class="text-start" style="width: 8.33% !important">
                <label
                  [class.form-label]="!view"
                  [class.form-label-view]="view"
                  class="form-label fw-bold d-block"
                  >ID</label
                >
              </th>
              <th class="text-start" style="width: 16.66% !important">
                <label
                  [class.form-label]="!view"
                  [class.form-label-view]="view"
                  class="form-label fw-bold d-block"
                  >Ocorrência</label
                >
              </th>
              <th class="text-start" style="width: 33.34% !important">
                <label
                  [class.form-label]="!view"
                  [class.form-label-view]="view"
                  class="form-label fw-bold d-block"
                  >Observações</label
                >
              </th>
              <th class="text-start" style="width: 33.34% !important">
                <label
                  [class.form-label]="!view"
                  [class.form-label-view]="view"
                  class="form-label fw-bold"
                  >Coordenadas
                  {{
                    hierarchy?.structure?.coordinate_setting?.datum_description
                  }}</label
                >
              </th>
              <th style="width: 4.16% !important"></th>
              <th style="width: 4.17% !important"></th>
            </tr>
          </thead>
          <tbody>
            <ng-container
              *ngFor="let occurrence of occurrences.controls; let i = index"
              [formGroupName]="i"
            >
              <tr>
                <td>
                  {{ occurrence.get('search_identifier')?.value }}
                </td>
                <td style="vertical-align: middle; padding: 0">
                  <div
                    *ngIf="!view; else viewResponse"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      height: 100%;
                      gap: 5px;
                      padding: 10px;
                    "
                  >
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        id="Sim"
                        formControlName="response"
                        [value]="1"
                        (change)="onChange('response', i)"
                      />
                      <label class="form-check-label" for="Sim">Sim</label>
                    </div>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        id="Nao"
                        formControlName="response"
                        [value]="2"
                        (change)="onChange('response', i)"
                      />
                      <label class="form-check-label" for="Nao">Não</label>
                    </div>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        id="NaoSeAplica"
                        formControlName="response"
                        [value]="3"
                        (change)="onChange('response', i)"
                      />
                      <label class="form-check-label" for="NaoSeAplica"
                        >Não se aplica</label
                      >
                    </div>
                  </div>
                  <ng-template #viewResponse>
                    <span>
                      {{
                        occurrence.get('response')?.value === 1
                          ? 'Sim'
                          : occurrence.get('response')?.value === 2
                          ? 'Não'
                          : occurrence.get('response')?.value === 3
                          ? 'Não se aplica'
                          : ''
                      }}
                    </span>
                  </ng-template>
                </td>
                <td>
                  <textarea
                    *ngIf="!view; else viewNote"
                    class="form-control"
                    rows="4"
                    formControlName="note"
                    (blur)="onBlur('note', i)"
                  ></textarea>
                  <ng-template #viewNote>
                    <span>{{ occurrence.get('note')?.value }}</span>
                  </ng-template>
                </td>
                <td>
                  <div class="input-group mb-2">
                    <span *ngIf="!view" class="input-group-text">E (m)</span>
                    <input
                      *ngIf="!view; else viewEasting"
                      type="text"
                      class="form-control"
                      formControlName="easting"
                      (blur)="onBlur('easting', i)"
                    />
                    <ng-template #viewEasting>
                      <span>E (m): {{ occurrence.get('easting')?.value }}</span>
                    </ng-template>
                  </div>
                  <div class="input-group">
                    <span *ngIf="!view" class="input-group-text">N (m)</span>
                    <input
                      *ngIf="!view; else viewNorthing"
                      type="text"
                      class="form-control"
                      formControlName="northing"
                      (blur)="onBlur('northing', i)"
                    />
                    <ng-template #viewNorthing>
                      <span
                        >N (m): {{ occurrence.get('northing')?.value }}</span
                      >
                    </ng-template>
                  </div>
                </td>
                <td>
                  <div style="height: 100px" class="py-2">
                    <app-button
                      *ngIf="!view"
                      [class]="'btn-logisoil-blue h-100 me-1'"
                      [icon]="'fa fa-map-location-dot'"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      title="Vincular localização"
                      (click)="callMap(occurrence, i)"
                    ></app-button>
                  </div>
                </td>
                <td>
                  <div class="d-flex flex-column align-items-center">
                    <app-button
                      [class]="'btn-sm btn-logisoil-green btn-36x36'"
                      [icon]="'fa fa-paperclip'"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      title="Anexos"
                      (click)="openModal('attachments', i)"
                      [disabled]="
                        occurrence.get('occurrence_attachments')?.value
                          ?.length === 0 && view
                      "
                    ></app-button>

                    <app-button
                      *ngIf="!view"
                      [class]="'btn-sm btn-logisoil-green btn-36x36 my-1'"
                      [icon]="'fas fa-link'"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      title="Vincular"
                      (click)="linkOccurrence(formGroup.get('id')?.value, i)"
                    ></app-button>

                    <app-button
                      *ngIf="!view"
                      [class]="'btn-sm btn-logisoil-red btn-36x36'"
                      [icon]="'fa fa-trash'"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      title="Excluir ocorrência"
                      (click)="onRemoveOccurrence(i)"
                    ></app-button>
                  </div>
                </td>
              </tr>
              <tr *ngIf="isOccurrenceLinked(i)">
                <td colspan="6">
                  <div class="alert alert-secondary my-1" role="alert">
                    Vinculada à ocorrência id
                    <a
                      href="#"
                      class="alert-link"
                      (click)="openOccurrence(i, $event)"
                      >{{ getOccurrenceLink(i) }}
                      <i
                        class="fa fa-external-link"
                        style="color: #0d6efd"
                        aria-hidden="true"
                      ></i
                    ></a>
                    da ficha anterior.
                  </div>
                </td>
              </tr>
              <tr *ngIf="linkErrorFlags[i]">
                <td colspan="6">
                  <div class="alert alert-info my-1" role="alert">
                    {{ messagePadroes.NoOccurrences }}
                  </div>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <!-- Botão para adicionar nova ocorrência -->
  <div class="col-md-12 d-flex justify-content-end" *ngIf="!view">
    <app-button
      [class]="'btn-logisoil-green'"
      [icon]="'fa fa-plus-circle'"
      [label]="'Adicionar Ocorrência'"
      (click)="onAddOccurrence()"
    ></app-button>
  </div>
</div>

<app-modal-attachments
  #modalAttachments
  [occurrenceAttachments]="modalAttachmentsProps.occurrenceAttachments"
  [hierarchy]="hierarchy"
  (saveAttachments)="onSaveAttachments($event)"
  [status]="status"
  [locked]="locked"
  [view]="view"
></app-modal-attachments>
