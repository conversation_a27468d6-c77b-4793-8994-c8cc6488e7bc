<div class="list-content">
  <!-- Filtro Cliente, Unidade, Estrutura -->
  <div class="row mt-1">
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-9"
      (sendEventHierarchy)="getEventHierarchy($event)"
    ></app-hierarchy>
    <div class="col-md-3 d-flex align-items-center" *ngIf="ctrlBtnFilter">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        class="me-1"
        (click)="generateMap()"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilters()"
      ></app-button>
    </div>
  </div>
  <!-- Configuração das cores dos instrumentos -->
  <div class="row mt-3" *ngIf="ctrlColors">
    <div class="col-md-12 d-flex align-items-end">
      <!-- INA's -->
      <div (clickOutside)="onClickedOutside('colorPicker', 'ina')">
        <app-button
          [class]="'btn-logisoil-white'"
          [icon]="'fa fa-square fa-xl'"
          [iconColor]="selectedColor.ina"
          [label]="'INA'"
          class="me-2"
          (click)="showColorPicker.ina = !showColorPicker.ina"
        ></app-button>
        <div
          class="d-flex justify-content-center justify-content-md-start color-picker-container"
          *ngIf="showColorPicker.ina"
        >
          <div style="width: 220px; display: inline-block">
            <color-sketch
              [color]="selectedColor.ina"
              (onChangeComplete)="changeComplete($event, 'ina')"
            ></color-sketch>
          </div>
        </div>
      </div>
      <!-- PZ's -->
      <div (clickOutside)="onClickedOutside('colorPicker', 'pz')">
        <app-button
          [class]="'btn-logisoil-white'"
          [icon]="'fa fa-square fa-xl'"
          [iconColor]="selectedColor.pz"
          [label]="'PZ'"
          class="me-2"
          (click)="showColorPicker.pz = !showColorPicker.pz"
        ></app-button>
        <div
          class="d-flex justify-content-center justify-content-md-start color-picker-container"
          *ngIf="showColorPicker.pz"
        >
          <div style="width: 220px; display: inline-block">
            <color-sketch
              [color]="selectedColor.pz"
              (onChangeComplete)="changeComplete($event, 'pz')"
            ></color-sketch>
          </div>
        </div>
      </div>
      <div>
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-floppy-disk'"
          [label]="'Salvar'"
          [type]="false"
          style="margin-bottom: 7px"
          (click)="postStabilityMapConfiguration()"
        >
        </app-button>
      </div>
    </div>
  </div>

  <div
    class="col-md-12 mt-2 alert"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status"
  >
    {{ message.text }}
  </div>

  <app-alert
    class="mt-2"
    [class]="'alert-danger'"
    [messages]="messagesError"
  ></app-alert>

  <!-- Mapa -->
  <div class="row mt-3">
    <div class="col-md-12 maps">
      <app-google-maps [id]="'mapStability'" #mapStability></app-google-maps>
    </div>
  </div>

  <!-- Botão Voltar -->
  <div class="col-md-12 mt-4 d-flex align-items-end justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/stability']"
    ></app-button>
  </div>
</div>

<!-- Botão Ver Análise -->
<app-modal-analysis
  #modalAnalysis
  [title]="titleModal"
  [sectionInfo]="configModal"
></app-modal-analysis>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
