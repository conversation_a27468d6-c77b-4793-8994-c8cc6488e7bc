import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ClientsRoutingModule } from './clients-routing.module';
import { SharedModule } from 'src/app/components/shared.module';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';

import { ListClientsComponent } from './list-clients/list-clients.component';
import { RegisterClientComponent } from './register-client/register-client.component';

@NgModule({
  declarations: [ListClientsComponent, RegisterClientComponent],
  imports: [CommonModule, ClientsRoutingModule, FormsModule, NgbModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, SharedModule]
})
export class ClientsModule {}
