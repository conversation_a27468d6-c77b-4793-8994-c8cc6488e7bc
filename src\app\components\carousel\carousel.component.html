<!--Carousel-->
<div
  id="carouselImages"
  class="carousel slide"
  data-bs-ride="carousel"
  *ngIf="showCarousel"
>
  <div class="carousel-indicators">
    <ng-container *ngFor="let image of images; let i = index">
      <button
        type="button"
        data-bs-target="#carouselImages"
        [attr.data-bs-slide-to]="image.index"
        class="active"
        [attr.aria-current]="selectedImage === image.index ? true : false"
        [attr.aria-label]="'Slide ' + (image.index + 1)"
      ></button>
    </ng-container>
  </div>
  <div class="carousel-inner">
    <ng-container *ngFor="let image of images; let i = index">
      <div
        class="carousel-item"
        [ngClass]="selectedImage === image.index ? 'active' : ''"
      >
        <div
          class="d-flex justify-content-center position-relative full-width-div"
          style="width: 100%; height: inherit"
        >
          <img
            class="carousel-auto"
            [src]="image.path"
            [attr.alt]="'Slide ' + (image.index + 1)"
          />
        </div>
        <div class="image-overlay">{{ image.description }}</div>
      </div>
    </ng-container>
  </div>
  <!-- Custom button -->
  <div class="top-0 mt-2 d-flex justify-content-end w-100 position-absolute">
    <app-button
      [class]="'btn-logisoil-red me-2'"
      [label]="'Fechar'"
      (click)="closeCarousel()"
    >
    </app-button>
  </div>

  <button
    class="carousel-control-prev"
    type="button"
    data-bs-target="#carouselImages"
    data-bs-slide="prev"
  >
    <i class="icon-navigation fa-fw fa fa-chevron-circle-left fa-3x"></i>
  </button>
  <button
    class="carousel-control-next"
    type="button"
    data-bs-target="#carouselImages"
    data-bs-slide="next"
  >
    <i class="icon-navigation fa-fw fa fa-chevron-circle-right fa-3x"></i>
  </button>
</div>
<!-- Carousel-->
