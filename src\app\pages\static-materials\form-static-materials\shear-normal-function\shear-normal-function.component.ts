import { Component, ElementRef, Input, OnChanges, OnInit, QueryList, SimpleChanges, ViewChildren, ViewEncapsulation } from '@angular/core';

import { Subject } from 'rxjs';

import { FormService } from 'src/app/services/form.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-shear-normal-function',
  templateUrl: './shear-normal-function.component.html',
  styleUrls: ['./shear-normal-function.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ShearNormalFunctionComponent implements OnInit, OnChanges {
  @ViewChildren('materialPointsRef') materialPointsRef: QueryList<ElementRef>;

  @Input() public data: any = null;
  @Input() public view: boolean = false;

  public func = fn;

  public paramsChart: any = null;

  public chart: Subject<any> = new Subject<any>();

  public dataPoints = null;

  constructor(public formService: FormService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  getDataChart($event) {
    this.paramsChart = $event;
    let x = [];
    let y = [];

    this.paramsChart.data.forEach((item) => {
      x.push(item.point_x);
      y.push(item.point_y);
    });

    this.paramsChart.data = { x: x, y: y };

    this.chart.next(this.paramsChart);
  }

  getPoints() {
    let pointsValue = [];
    this.materialPointsRef.toArray()[0]['formPoints'].value.points.forEach((point, index) => {
      pointsValue.push({
        id: point.id,
        value_1: point.point_x,
        value_2: point.point_y,
        index: index + 1
      });
    });
    return pointsValue;
  }

  splitData($dados) {
    this.dataPoints = $dados.point_values;
  }

  validate() {
    let formPointValid = this.materialPointsRef.toArray()[0]['validate']();
    return formPointValid;
  }
}
