import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>w<PERSON>ni<PERSON>, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';

import { DataService } from 'src/app/services/data.service';
import { FileLoaderService } from 'src/app/services/file-loader.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { UserService } from 'src/app/services/user.service';

import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { Conditions, SafetyFactorAlertLevel, SliFileType, ZipFile } from 'src/app/constants/stability.constants';

import { ChartService as ChartServiceApi } from 'src/app/services/api/chart.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { StabilityService as StabilityServiceApi } from 'src/app/services/api/stability.service';
import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';

import { EChartsComponent } from '@components/e-charts/e-charts.component';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';

import { ECharts } from 'echarts';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-chart-stability',
  templateUrl: './chart-stability.component.html',
  styleUrls: ['./chart-stability.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ChartStabilityComponent implements OnInit, AfterViewChecked {
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalSafetyFactor') ModalSafetyFactor: any;

  @ViewChild('chartUndrained', { static: false }) chartUndrained: EChartsComponent;
  @ViewChild('chartDrained', { static: false }) chartDrained: EChartsComponent;
  @ViewChild('chartPseudoStatic', { static: false }) chartPseudoStatic: EChartsComponent;

  public formStabilityChart: FormGroup = new FormGroup({
    StartDate: new FormControl(''),
    EndDate: new FormControl(''),
    SurfaceType: new FormControl(''),
    CalculationMethod: new FormControl('')
  });

  public filterHierarchy: any = {};

  public sectionSettings = MultiSelectDefault.Sections;
  public sections: any = [];

  public calculationMethods = [];
  public conditions = Conditions;
  public safetyFactorAlertLevel = SafetyFactorAlertLevel;

  public sliFileType = {};
  public mapSliFileType = SliFileType.reduce((acc, item) => {
    acc[item.value] = item;
    return acc;
  }, {});

  public surfaceType = [];
  public zipFile = ZipFile;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageSurfaceType: any = { text: '', status: false, class: 'alert-success' };
  public messageCalculationMethods: any = { text: '', status: false, class: 'alert-success' };

  public messageReturn: any = { text: '', status: false };
  public messagesError: any = [];

  public showColorPicker = {};
  public selectedColor = {};

  public controls: any = [];
  public ctrlBtnFilter: boolean = false;

  public func = fn;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Seção',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'FS ND',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['fsnd']
    },
    {
      label: 'FS D',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['fsd']
    },
    {
      label: 'FS SIS',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['fssis']
    },
    {
      label: 'Método de Cálculo',
      width: '35%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['calculationMethod']
    },
    {
      label: 'Data',
      width: '15%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['date']
    }
  ];

  //Modal
  public configModal: any = null;
  public titleModal: string = '';

  //Construção do gráfico
  public chart: any = {
    undrained: {},
    drained: {},
    pseudoStatic: {}
  };

  public xAxis: any = { undrained: [], drained: [], pseudoStatic: [] };
  public yAxis: any = { undrained: [], drained: [], pseudoStatic: [] };

  public yAxisDescription: any = '';

  public chartSeries: any = { undrained: [], drained: [], pseudoStatic: [] };
  public chartLegends: any = { undrained: [], drained: [], pseudoStatic: [] };

  public chartLegendsTop: number = 50;
  public chartLegendsBottom: number = 0;

  public minMax: any = {
    undrained: { min: null, max: null },
    drained: { min: null, max: null },
    pseudoStatic: { min: null, max: null }
  };

  public dados: any = null;

  public selectedItem: any = null;

  private chartsInitialized = {
    undrained: false,
    drained: false,
    pseudoStatic: false
  };

  public isModalOpen: boolean = false;
  private subscriptions: Subscription[] = [];

  constructor(
    private chartServiceApi: ChartServiceApi,
    private dataService: DataService,
    private fileLoaderService: FileLoaderService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private sectionsServiceApi: SectionsServiceApi,
    private stabilityServiceApi: StabilityServiceApi,
    private structuresServiceApi: StructuresServiceApi,
    private userService: UserService
  ) {}

  /**
   * Inicializa o componente, definindo o perfil do usuário, permissões e configurações iniciais de seleção.
   * Também monitora as mudanças no formulário `formStabilityChart`.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.sectionSettings.singleSelection = true;
    this.controls = this.formStabilityChart.controls;

    //Monitora mudanças no formFIlter
    this.formStabilityChart.valueChanges.subscribe((value) => {
      this.checkAllFields();
      this.searchManager();
    });
  }

  checkAllFields(): void {
    // Verifica se todos os campos do formulário estão preenchidos
    const formFilled = Object.values(this.formStabilityChart.value).every((value) => value !== '');

    // Verifica se todos os elementos do objeto hierarchy estão preenchidos e não são null
    const hierarchyFilled = ['clients', 'units', 'structures'].every(
      (key) =>
        this.hierarchy && this.hierarchy[key] && this.hierarchy[key].length > 0 && this.hierarchy[key].every((item) => item.id !== null && item.name !== null)
    );

    // Atualiza ctrlBtnFilter se todas as condições forem atendidas
    this.ctrlBtnFilter = formFilled && hierarchyFilled;
  }

  /**
   * Inicializa um gráfico específico baseado na condição fornecida, evitando múltiplas inicializações.
   * @param {string} condition - A condição do gráfico a ser inicializado (undrained, drained, pseudoStatic).
   */
  initCharts(condition: any): void {
    const chartMap = {
      undrained: {
        chart: this.chartUndrained,
        initialized: this.chartsInitialized.undrained
      },
      drained: {
        chart: this.chartDrained,
        initialized: this.chartsInitialized.drained
      },
      pseudoStatic: {
        chart: this.chartPseudoStatic,
        initialized: this.chartsInitialized.pseudoStatic
      }
    };

    const selectedChart = chartMap[condition];

    if (selectedChart.chart && !selectedChart.initialized) {
      this.initChart(selectedChart.chart, condition);
      this.chartsInitialized[condition] = true;
    }
  }

  /**
   * Inicializa o gráfico fornecido e configura o evento de clique para marcador de dados.
   * @param {EChartsComponent} chartComponent - Componente de gráfico a ser inicializado.
   * @param {string} chartType - Tipo do gráfico (undrained, drained, pseudoStatic).
   */
  initChart(chartComponent: EChartsComponent, chartType: string): void {
    if (chartComponent) {
      const subscription = chartComponent.chartInit.subscribe((chartInstance: ECharts) => {
        this.initMarkerClickEvent(chartInstance, chartType);
      });
      this.subscriptions.push(subscription);
    } else {
      console.error(`Chart component not found: ${chartType}`);
    }
  }

  /**
   * Obtém os métodos de cálculo para a estrutura selecionada.
   * @param {any} $structure - A estrutura para a qual obter os métodos de cálculo.
   */
  getStructureCalculationMethods($structure) {
    this.ngxSpinnerService.show();

    this.structuresServiceApi.getStructureCalculationMethods($structure.id).subscribe((resp) => {
      let dados: any = resp;

      if (dados != null) {
        dados = dados.body === undefined ? dados : dados.body;
        this.setDataSelect(dados, 'calculationMethod');
      } else {
        this.messageCalculationMethods.text = MessagePadroes.NoCalculationMethods;
        this.messageCalculationMethods.status = true;
        this.messageCalculationMethods.class = 'alert-warning';

        setTimeout(() => {
          this.messageCalculationMethods.status = false;
        }, 5000);
      }
      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Obtém os tipos de superfície para a estrutura selecionada.
   * @param {any} $structure - A estrutura para a qual obter os tipos de superfície.
   */
  getStructureSurfaceType($structure) {
    this.ngxSpinnerService.show();

    this.structuresServiceApi.getStructureSurfaceTypes($structure.id).subscribe((resp) => {
      let dados: any = resp;

      if (dados != null) {
        dados = dados.body === undefined ? dados : dados.body;
        this.setDataSelect(dados, 'surfaceType');
      } else {
        this.messageSurfaceType.text = MessagePadroes.NoSurfaceType;
        this.messageSurfaceType.status = true;
        this.messageSurfaceType.class = 'alert-warning';

        setTimeout(() => {
          this.messageSurfaceType.status = false;
        }, 5000);
      }
      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Define os dados para seleção de métodos de cálculo e tipos de superfície.
   * @param {any} $dados - Dados a serem processados e atribuídos.
   * @param {string} $type - Tipo de dados para definir (calculationMethod, surfaceType).
   */
  setDataSelect($dados, $type) {
    const transformedArray = $dados.map((item) => ({
      value: item.id,
      label: item.name
    }));

    switch ($type) {
      case 'calculationMethod':
        this.calculationMethods = transformedArray;
        this.calculationMethods.push({ value: 11, label: 'Menor FS' });
        break;
      case 'surfaceType':
        this.surfaceType = transformedArray;
        break;
      case 'period':
        if (this.formStabilityChart.get('SurfaceType').value && this.formStabilityChart.get('CalculationMethod').value) {
          this.formStabilityChart.get('StartDate').setValue('');
          this.formStabilityChart.get('EndDate').setValue('');

          const structureId = this.filterHierarchy.structures[0].id;
          let params = {
            SurfaceType: this.formStabilityChart.get('SurfaceType').value
          };

          if (parseInt(this.formStabilityChart.get('CalculationMethod').value) != 11) {
            params['CalculationMethod'] = this.formStabilityChart.get('CalculationMethod').value;
          }

          this.ngxSpinnerService.show();
          this.stabilityServiceApi.getStabilityAnalysisDate(structureId, params).subscribe((resp) => {
            let dados: any = resp;
            dados = dados.body === undefined ? dados : dados.body;
            if (dados) {
              this.formStabilityChart.get('StartDate').setValue(moment(dados.first_analysis).format('YYYY-MM-DD HH:mm:ss'));
              this.formStabilityChart.get('EndDate').setValue(moment(dados.last_analysis).format('YYYY-MM-DD HH:mm:ss'));
            }
            this.ngxSpinnerService.hide();
          });
        }
        break;
    }
  }

  /**
   * Gerencia o estado do botão de filtro com base nos valores dos filtros e controles.
   */
  searchManager() {
    if (
      this.filterHierarchy.clients &&
      this.filterHierarchy.clients.length > 0 &&
      this.filterHierarchy.units &&
      this.filterHierarchy.units.length > 0 &&
      this.filterHierarchy.structures &&
      this.filterHierarchy.structures.length > 0 &&
      this.controls['SurfaceType'].value != '' &&
      this.controls['CalculationMethod'].value != ''
    ) {
      this.ctrlBtnFilter = true;
    } else {
      this.ctrlBtnFilter = false;
    }
  }

  /**
   * Obtém a lista de fatores de segurança com base nos filtros aplicados e gera o gráfico.
   */
  getSafetyFactorList() {
    this.ngxSpinnerService.show();

    this.resetChart();
    this.messagesError = [];

    const params = {
      ClientId: this.filterHierarchy.clients && this.filterHierarchy.clients[0] ? this.filterHierarchy.clients[0].id : '',
      ClientUnitId: this.filterHierarchy.units && this.filterHierarchy.units[0] ? this.filterHierarchy.units[0].id : '',
      StructureId: this.filterHierarchy.structures && this.filterHierarchy.structures[0] ? this.filterHierarchy.structures[0].id : '',
      StartDate: this.controls['StartDate'].value,
      EndDate: this.controls['EndDate'].value,
      SurfaceType: this.controls['SurfaceType'].value,
      CalculationMethod: this.controls['CalculationMethod'].value < 11 ? this.controls['CalculationMethod'].value : 1,
      ShouldGetLowestSafetyFactor: this.controls['CalculationMethod'].value < 11 ? false : true
    };

    this.chartServiceApi.getChartStabilityAnalysis(params).subscribe(
      (resp) => {
        this.dados = resp;
        this.dados = this.dados.body === undefined ? this.dados : this.dados.body;

        if (resp['status'] == 200) {
          this.constructChart(this.dados);
        } else {
          this.message.text = MessagePadroes.NoSafetyFactor;
          this.message.status = true;
          this.message.class = 'alert-warning';
          setTimeout(() => {
            this.message.status = false;
          }, 5000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });

          // Constrói a mensagem de erro consolidada
          const consolidatedErrors = this.messagesError.map((err) => err.message).join('<br/>');

          // Exibe a mensagem de erro usando o componente de alerta existente
          this.message.text = consolidatedErrors;
          this.message.status = true;
          this.message.class = 'alert-danger';
          setTimeout(() => {
            this.message.status = false;
          }, 6000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Edita a cor da linha do gráfico para as seções selecionadas e atualiza o gráfico.
   */
  editSectionsChartLineColor() {
    let params = this.sections.map((section) => {
      return {
        id: section.id,
        chart_line_color: this.selectedColor[section.name]
      };
    });

    this.sectionsServiceApi.patchSectionsChartLineColor(params).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.AlteracaoCor;
      this.message.status = true;
      this.message.class = 'alert-success';

      this.getSafetyFactorList();

      setTimeout(() => {
        this.message.status = false;
      }, 4000);
    });
  }

  /**
   * Executa ações quando um clique ocorre fora de um elemento específico, como esconder o seletor de cor.
   * @param {string} element - O elemento que foi clicado fora.
   * @param {string} [property] - A propriedade a ser modificada.
   */
  onClickedOutside(element: string, property: string = '') {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker[property] = false;
        break;
    }
  }

  /**
   * Atualiza a cor selecionada quando a cor é alterada no seletor de cor.
   * @param {any} $event - O evento de mudança de cor.
   * @param {string} [property] - A propriedade a ser modificada.
   */
  changeComplete($event, property: string = '') {
    this.selectedColor[property] = $event.color.hex;
    this.updateChartSeriesColor(property);
  }

  /**
   * Atualiza a cor da série do gráfico para uma seção específica.
   * @param {string} sectionName - Nome da seção para a qual atualizar a cor.
   */
  updateChartSeriesColor(sectionName: string) {
    ['undrained', 'drained', 'pseudoStatic'].forEach((condition) => {
      const chartSeries = this.chartSeries[condition];
      chartSeries.forEach((series) => {
        if (series.name === sectionName) {
          series.itemStyle.color = this.selectedColor[sectionName];
        }
      });

      if (this.chart[condition] && this.chart[condition].chartInstance) {
        this.chart[condition].chartInstance.setOption({
          series: chartSeries
        });
      }
    });
    this.generateChart();
  }

  /**
   * Processa eventos da hierarquia de filtros, atualizando as seções ou cores com base no evento.
   * @param {any} $event - O evento da hierarquia de filtros.
   */
  getEventHierarchy($event) {
    switch ($event.type) {
      case 'units':
        this.selectedColor = {};
        this.showColorPicker = {};
        break;
      case 'structures':
        this.selectedColor = {};
        this.showColorPicker = {};
        if ($event.action === 'select') {
          this.getStructureCalculationMethods($event.element);
          this.getStructureSurfaceType($event.element);
        }
        this.checkAllFields();
        break;
    }

    if ($event.action === 'deselect') {
      this.ctrlBtnFilter = false;
      this.resetFilter();
    }
  }

  //Atualiza a hierarquia de filtros com base no evento de filtro.
  filterEventHierarchy($event) {
    this.filterHierarchy = $event;
  }

  //Redefine os filtros para os valores iniciais.
  resetFilter() {
    this.hierarchy.resetFilters();

    this.controls['StartDate'].setValue('');
    this.controls['EndDate'].setValue('');
    this.controls['SurfaceType'].setValue('');
    this.controls['CalculationMethod'].setValue('');

    this.calculationMethods = [];
    this.surfaceType = [];
  }

  /**
   * Define os valores mínimos e máximos para um array de dados.
   * @param {any[]} array - O array de dados.
   * @param {string} [letter='a'] - A letra identificadora.
   * @param {any} [index=null] - O índice opcional.
   */
  defineMinMax(array: any, letter = 'a', index = null) {
    array = array.filter((item) => item !== null && item !== undefined);

    array = array.map((item) => {
      if (item != null && item != undefined) {
        return typeof item == 'number' ? item : item.value;
      }
    });

    const min = Math.min(...array);
    const max = Math.max(...array);
    let previous = min - (min % 10);
    let next = max + (10 - (max % 10));

    this.minMax[letter].min = this.minMax[letter].min == null ? previous : this.minMax[letter].min;
    this.minMax[letter].min = Math.min(this.minMax[letter].min, previous);
    previous = this.minMax[letter].min;

    this.minMax[letter].max = this.minMax[letter].max == null ? next : this.minMax[letter].max;
    this.minMax[letter].max = Math.max(this.minMax[letter].max, next);
    next = this.minMax[letter].max;
  }

  //Define a altura do gráfico.
  setHeight(height: string): void {
    this.controls['ChartHeight'].setValue(parseInt(height));
    // this.generateChart();
  }

  //Carrega o conteúdo do arquivo do caminho e nome do arquivo especificados.
  loadFileContent(path: string, fileName: string, index = 0) {
    this.fileLoaderService.loadFile(path, fileName).subscribe(
      (data) => {
        // this.markers[index].text = data;
      },
      (error) => {
        console.error('Erro ao carregar o arquivo:', error);
      }
    );
  }

  /**
   * Converte um arquivo SVG para texto e define suas cores.
   * @param {string} svg - O conteúdo do arquivo SVG.
   * @param {string} [color='#000000'] - A cor a ser aplicada.
   * @returns {string} - O SVG convertido para uma string base64.
   */
  getSvgWithReplacedValue(svg, color = '#000000') {
    svg = this.replaceMultipleOccurrences(svg, ['rgb(0,0,0)', 'rgb(101,101,101)'], [color, color]);
    const svgBase64 = btoa(svg);
    return `data:image/svg+xml;base64,${svgBase64}`;
  }

  //Substitui todas as ocorrências de uma determinada string em um texto.
  replaceMultipleOccurrences(text, oldValues, newValues) {
    if (oldValues.length !== newValues.length) {
      throw new Error('Os arrays devem ter o mesmo comprimento.');
    }

    let newText = text;
    for (let i = 0; i < oldValues.length; i++) {
      const oldValue = oldValues[i];
      const newValue = newValues[i];
      newText = newText.split(oldValue).join(newValue);
    }

    return newText;
  }

  //Gera uma cor hexadecimal aleatória.
  randomHexColor() {
    const randomColorComponent = () => {
      const component = Math.floor(Math.random() * 256); //Valor aleatorio entre 0  e 255
      return component.toString(16).padStart(2, '0'); //Converte para hexadecimal e completa com zero se necessario
    };

    const r = randomColorComponent();
    const g = randomColorComponent();
    const b = randomColorComponent();

    return `#${r}${g}${b}`;
  }

  //Remove elementos duplicados de um array.
  uniqueArray(array) {
    const uniqueArray = [];
    const seeDates = {};

    for (const date of array) {
      if (!seeDates[date]) {
        uniqueArray.push(date);
        seeDates[date] = true;
      }
    }
    return uniqueArray;
  }

  //Constrói o gráfico com base nos dados fornecidos.
  constructChart(data) {
    this.formatData(data);
    this.getSectionsColors(data);
  }

  /**
   * Formata os dados fornecidos para apresentação no gráfico.
   * @param {any} data - Dados a serem formatados.
   */
  formatData(data) {
    const output = [];

    const getLabelForCalculationMethod = (value: number) => {
      const method = this.calculationMethods.find((method) => method.value === value);
      return method ? method.label : 'Unknown';
    };

    data.section_results.forEach((sectionResult) => {
      const { name, last_stability_analysis_by_soil_condition_result } = sectionResult;
      const values = last_stability_analysis_by_soil_condition_result.values;
      const date = moment(last_stability_analysis_by_soil_condition_result.readingCreatedDate).format('DD/MM/YYYY HH:mm:ss');

      const fsnd = values.find((v) => v.sliFileType === 2)?.value || null;
      const fsd = values.find((v) => v.sliFileType === 1)?.value || null;
      const fssis = values.find((v) => v.sliFileType === 3)?.value || null;
      const calculationMethod = getLabelForCalculationMethod(values[0]?.calculationMethod || null);

      const entry = {
        name: name,
        fsnd: fsnd,
        fsd: fsd,
        fssis: fssis,
        calculationMethod: calculationMethod,
        date: date
      };

      output.push(entry);
    });

    this.tableData = output;
  }

  //Obtém as cores das seções com base nos dados fornecidos.
  getSectionsColors($dados) {
    this.sections = [];
    const colors = fn.generateRandomColors($dados.section_results.length);

    $dados.section_results.forEach((section, index) => {
      this.selectedColor[section.name] = section.chart_line_color == null ? colors[index] : section.chart_line_color;
      this.showColorPicker[section.name] = false;
      this.sections.push({ id: section.id, name: section.name });
    });
    this.constructXAxis($dados);
  }

  //Constrói o eixo X do gráfico com base nos dados fornecidos.
  constructXAxis(data) {
    let dates = [];

    data.section_results.forEach((section) => {
      section.stability_analysis_results.forEach((result) => {
        result.safety_factor_results.forEach((safetyResult) => {
          if (safetyResult.created_date) {
            dates.push(moment(safetyResult.created_date).format('DD/MM/YYYY'));
          }
        });
      });
    });

    const orderedDates = dates
      .map((data) => {
        const [dia, mes, ano] = data.split('/').map(Number);
        return new Date(ano, mes - 1, dia);
      })
      .sort((a: any, b: any) => a - b)
      .map((data) => data.toLocaleDateString('pt-BR'));

    this.xAxis.undrained = this.uniqueArray(orderedDates);
    this.xAxis.drained = this.uniqueArray(orderedDates);
    this.xAxis.pseudoStatic = this.uniqueArray(orderedDates);

    this.constructSeries(data);
  }

  //Constrói as séries do gráfico com base nos dados fornecidos.
  constructSeries(data) {
    const initializeDateObject = (dates) => {
      return dates.reduce((acc, date) => {
        acc[date] = null;
        return acc;
      }, {});
    };

    const datesObjectUndrained = initializeDateObject(this.xAxis.undrained);
    const datesObjectDrained = initializeDateObject(this.xAxis.drained);
    const datesObjectPseudoStatic = initializeDateObject(this.xAxis.pseudoStatic);

    data.section_results.forEach((section) => {
      let undrainedSeries = { ...datesObjectUndrained };
      let drainedSeries = { ...datesObjectDrained };
      let pseudoStaticSeries = { ...datesObjectPseudoStatic };

      section.stability_analysis_results.forEach((result) => {
        result.safety_factor_results.forEach((safetyResult) => {
          if (safetyResult.created_date) {
            const date = moment(safetyResult.created_date).format('DD/MM/YYYY');
            const value = safetyResult.value;
            const condition = this.mapSliFileType[safetyResult.sli_file_type].condition;

            if (condition === 'undrained') {
              safetyResult['condition'] = { value: 2, name: 'undrained' };
              undrainedSeries[date] = { value, safetyResult };
            } else if (condition === 'drained') {
              safetyResult['condition'] = { value: 1, name: 'drained' };
              drainedSeries[date] = { value, safetyResult };
            } else if (condition === 'pseudoStatic') {
              safetyResult['condition'] = { value: 1, name: 'pseudoStatic' };
              pseudoStaticSeries[date] = { value, safetyResult };
            }
          }
        });
      });

      const addSeries = (condition, seriesData) => {
        const itemSeries = {
          name: section.name,
          type: 'line',
          allData: Object.values(seriesData).map((item: any) => item),
          data: Object.values(seriesData).map((item: any) => (item ? item.value : null)),
          itemStyle: {
            color: this.selectedColor[section.name]
          },
          connectNulls: true,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 7,
          hoverAnimation: false, // Desativa a animação ao passar o mouse
          sampling: 'none' // Desativa o 'sampling' para garantir que todos os pontos sejam mostrados
        };
        this.chartLegends[condition].push(section.name);
        this.chartSeries[condition].push(itemSeries);
      };

      addSeries('undrained', undrainedSeries);
      addSeries('drained', drainedSeries);
      addSeries('pseudoStatic', pseudoStaticSeries);
    });

    // Adicionar as séries baseadas em safety_factor_metric_results
    data.safety_factor_metric_results.forEach((metric) => {
      const referenceValue = metric.reference_value;
      const condition = this.mapSliFileType[metric.soil_condition_type].condition;
      const getAlertColor = (alertLevel) => {
        const alert = SafetyFactorAlertLevel.find((item) => item.value === alertLevel);
        return alert ? alert.color : '#000000'; // Retorna preto como padrão se não encontrar
      };

      const color = getAlertColor(metric.alert_level);

      const dates = this.xAxis[condition].map((date) => referenceValue);

      const dashedSeries = {
        name: `FS = ${referenceValue}`,
        type: 'line',
        data: dates,
        lineStyle: {
          type: 'dashed',
          color: color
        },
        showSymbol: false,
        markLine: {
          data: [
            {
              yAxis: referenceValue,
              label: {
                formatter: `FS = ${referenceValue}`,
                position: 'insideEndTop'
              }
            }
          ],
          lineStyle: {
            type: 'dashed',
            color: color,
            width: 3
          },
          symbol: 'none'
        }
      };

      this.chartSeries[condition].push(dashedSeries);
    });

    this.constructYAxis();
  }

  /**
   * Obtém as datas para uma condição específica a partir dos dados fornecidos.
   * @param {any} data - Os dados para extrair as datas.
   * @param {string} condition - A condição para filtrar as datas.
   * @returns {string[]} - As datas filtradas.
   */
  getDatesForCondition(data, condition) {
    // Função auxiliar para extrair datas com base na condição
    return data.section_results
      .flatMap((section) => section.stability_analysis_results || [])
      .flatMap((result) => result.safety_factor_results || [])
      .filter((safetyResult) => {
        const conditionData = this.mapSliFileType[safetyResult.sli_file_type];
        return conditionData && conditionData.condition === condition;
      })
      .map((safetyResult) => moment(safetyResult.created_date).format('DD/MM/YYYY'));
  }

  //Constrói o eixo Y do gráfico com base nos dados fornecidos.
  constructYAxis() {
    this.yAxis = {
      undrained: [],
      drained: [],
      pseudoStatic: []
    };

    Object.keys(this.yAxis).forEach((condition) => {
      let itemYAxis = {
        name: '',
        type: 'value',
        axisLine: {
          show: true
        },
        nameRotate: 90,
        nameLocation: 'center',
        nameGap: 55,
        nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
        alignTicks: true,
        axisLabel: {
          formatter: function (value, index) {
            return value.toFixed(1);
          }
        },
        interval: 0.5,
        show: true
      };
      this.yAxis[condition] = itemYAxis;
    });

    this.generateChart();
  }

  //Gera o gráfico com base nas séries e eixos construídos.
  generateChart() {
    Object.keys(this.chart).forEach((condition) => {
      this.chart[condition]['options'] = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              formatter: function (params) {
                if (typeof params.value === 'number') {
                  return params.value.toFixed(2); //Formata o valor para duas casas decimais
                } else {
                  return params.value;
                }
              }
            }
          }
        },
        legend: {
          data: this.chartLegends[condition],
          icon: 'rect',
          left: 'center',
          top: 'top'
        },
        grid: {
          containLabel: true,
          top: this.chartLegendsTop,
          left: 50,
          right: 50,
          height: 300
        },
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: { title: 'Download' }
          }
        },
        xAxis: {
          type: 'category',
          interval: 0.1,
          boundaryGap: false,
          data: this.xAxis[condition],
          axisLabel: {
            interval: Math.floor(this.xAxis[condition].length / 35), // Define o intervalo para exibir todos os valores do eixo X
            rotate: 60
          }
        },
        yAxis: this.yAxis[condition],
        series: this.chartSeries[condition]
      };
      setTimeout(() => {
        this.initCharts(condition);
      }, 100);
    });
  }

  /**
   * Redefine o gráfico e seus dados associados.
   */
  resetChart() {
    this.chart = {
      undrained: {},
      drained: {},
      pseudoStatic: {}
    };

    this.xAxis = { undrained: [], drained: [], pseudoStatic: [] };
    this.yAxis = { undrained: [], drained: [], pseudoStatic: [] };

    this.yAxisDescription = '';

    this.chartSeries = { undrained: [], drained: [], pseudoStatic: [] };
    this.chartLegends = { undrained: [], drained: [], pseudoStatic: [] };

    this.minMax = {
      undrained: { min: null, max: null },
      drained: { min: null, max: null },
      pseudoStatic: { min: null, max: null }
    };
  }

  ngAfterViewChecked(): void {
    //this.initCharts();
    // if (this.chartUndrained && !this.chartInitialized.undrained) {
    //   this.chartUndrained.chartInit.subscribe((chartInstance: ECharts) => {
    //     this.initMarkerClickEvent(chartInstance, 'undrained');
    //   });
    //   this.chartInitialized.undrained = true;
    // }
    // if (this.chartDrained && !this.chartInitialized.drained) {
    //   this.chartDrained.chartInit.subscribe((chartInstance: ECharts) => {
    //     this.initMarkerClickEvent(chartInstance, 'drained');
    //   });
    //   this.chartInitialized.drained = true;
    // }
    // if (this.chartPseudoStatic && !this.chartInitialized.pseudoStatic) {
    //   this.chartPseudoStatic.chartInit.subscribe((chartInstance: ECharts) => {
    //     this.initMarkerClickEvent(chartInstance, 'pseudoStatic');
    //   });
    //   this.chartInitialized.pseudoStatic = true;
    // }
  }

  /**
   * Inicializa o evento de clique do marcador para o gráfico.
   * @param {ECharts} chartInstance - Instância do gráfico ECharts.
   * @param {string} chartType - Tipo do gráfico.
   */
  initMarkerClickEvent(chartInstance: ECharts, chartType: string): void {
    chartInstance.off('click'); // Remove qualquer evento 'click' registrado anteriormente

    chartInstance.on('click', (params: any) => {
      if (params.componentType === 'series' && params.seriesType === 'line') {
        const series = chartInstance.getOption().series[params.seriesIndex];
        const allData = series.allData[params.dataIndex];
        const clickedData = this.extractData(params.seriesIndex, params.dataIndex, allData);
        this.handleMarkerClick(clickedData);
      }
    });
  }

  /**
   * Manipula o clique no marcador de dados, abrindo um modal com as informações.
   * @param {any} eventData - Dados do evento de clique.
   */
  handleMarkerClick(eventData: any): void {
    if (!this.isModalOpen && eventData) {
      this.isModalOpen = true;
      this.selectedItem = eventData;
      setTimeout(() => {
        this.ModalSafetyFactor.openModal();
        // Redefinir a flag quando o modal for fechado
        this.ModalSafetyFactor.modalClosed.subscribe(() => {
          this.isModalOpen = false;
        });
      }, 50);
    }
  }

  /**
   * Extrai os dados da série e índice fornecidos.
   * @param {number} seriesIndex - Índice da série.
   * @param {number} dataIndex - Índice dos dados.
   * @param {any} allData - Todos os dados da série.
   * @returns {any} - Dados extraídos.
   */
  extractData(seriesIndex: number, dataIndex: number, allData: any): any {
    const section = this.dados.section_results[seriesIndex];
    if (section) {
      return {
        id: section.id,
        name: section.name,
        safety_factor_result: allData.safetyResult
      };
    }
    return null;
  }

  /**
   * Método chamado ao destruir o componente, desinscreve todas as assinaturas.
   */
  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  /**
   * Navega de volta para a página inicial da aplicação.
   */
  goBack() {
    this.router.navigate(['/']);
  }
}
