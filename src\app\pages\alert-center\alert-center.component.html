<form [formGroup]="formAlertCenter">
  <!-- Select de Cliente, Unidade e Estrutura -->
  <div class="list-content">
    <div class="row">
      <div class="col-md-3 mt-2">
        <label class="form-label"><PERSON><PERSON></label>
        <select
          class="form-select"
          formControlName="type_alerts"
          (change)="changeTabName()"
        >
          <option value="">Selecione...</option>
          <option *ngFor="let item of typeAlerts" [ngValue]="item.value">
            {{ item.label }}
          </option>
        </select>
        <small
          class="form-text text-danger"
          *ngIf="
            !formAlertCenter.get('type_alerts').valid &&
            formAlertCenter.get('type_alerts').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <div
        class="row mt-2"
        *ngIf="formAlertCenter.controls['type_alerts'].value != ''"
      >
        <ul class="nav nav-tabs" id="myTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="primary-tab"
              type="button"
              role="tab"
              aria-controls="primary"
              aria-selected="true"
              (click)="selectTab('instruments')"
              [style.background-color]="
                instrumentsTabConfig.styleColor ? '#dc3545' : ''
              "
              [style.color]="instrumentsTabConfig.styleColor ? '#ffffff' : ''"
            >
              {{ tabName.label }}
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="history-tab"
              type="button"
              role="tab"
              aria-controls="history"
              aria-selected="true"
              (click)="selectTab('history')"
              [style.background-color]="
                historyTabConfig.styleColor ? '#dc3545' : ''
              "
              [style.color]="historyTabConfig.styleColor ? '#ffffff' : ''"
            >
              Histórico
            </button>
          </li>
        </ul>

        <div class="tab-content" id="myTabContent">
          <div
            class="tab-pane fade"
            [ngClass]="instrumentsTabConfig.active ? 'show active' : ''"
            id="primay"
            role="tabpanel"
            aria-labelledby="primary-tab"
          >
            <div class="col-md-12 mt-2">
              <span class="fw-bolder">
                <i class="fa fa-exclamation-circle"></i>
                Selecione como você será notificado quando ocorrerem as
                seguintes alterações:
              </span>
            </div>

            <!-- Tabela Instrumentação -->
            <div
              class="row mt-2"
              *ngIf="formAlertCenter.controls['type_alerts'].value == 1"
            >
              <div class="table-responsive-md">
                <table class="table table-bordered table-hover align-middle">
                  <thead>
                    <tr>
                      <th scope="col" class="col-md-3">Evento</th>
                      <th scope="col" class="col-md-3">Forma de recebimento</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Cadastro de novo Instrumento -->
                    <tr>
                      <td scope="col">Cadastro de novo Instrumento</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationInstrumentCreated"
                            id="checkboxNotificationInstrumentCreated"
                            [checked]="getUserConfiguration('bell', 1)"
                            (change)="onCheckboxChange($event, 1, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationInstrumentCreated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerInstrumentCreated"
                            id="checkboxBannerInstrumentCreated"
                            [checked]="getUserConfiguration('banner', 1)"
                            (change)="onCheckboxChange($event, 1, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerInstrumentCreated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Edição de Instrumentos -->
                    <tr>
                      <td scope="col">Edição de Instrumentos</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationInstrumentUpdated"
                            id="checkboxNotificationInstrumentUpdated"
                            [checked]="getUserConfiguration('bell', 2)"
                            (change)="onCheckboxChange($event, 2, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationInstrumentUpdated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerInstrumentUpdated"
                            id="checkboxBannerInstrumentUpdated"
                            [checked]="getUserConfiguration('banner', 2)"
                            (change)="onCheckboxChange($event, 2, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerInstrumentUpdated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Inativação de Instrumentos -->
                    <tr>
                      <td scope="col">Inativação de Instrumentos</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationInstrumentInactivated"
                            id="checkboxNotificationInstrumentInactivated"
                            [checked]="getUserConfiguration('bell', 3)"
                            (change)="onCheckboxChange($event, 3, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationInstrumentInactivated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerInstrumentInactivated"
                            id="checkboxBannerInstrumentInactivated"
                            [checked]="getUserConfiguration('banner', 3)"
                            (change)="onCheckboxChange($event, 3, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerInstrumentInactivated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Marcar Instrumento como avariado-->
                    <tr>
                      <td scope="col">Instrumento marcado como avariado</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationInstrumentDamaged"
                            id="checkboxNotificationInstrumentDamaged"
                            [checked]="getUserConfiguration('bell', 4)"
                            (change)="onCheckboxChange($event, 4, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationInstrumentDamaged"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerInstrumentDamaged"
                            id="checkboxBannerInstrumentDamaged"
                            [checked]="getUserConfiguration('banner', 4)"
                            (change)="onCheckboxChange($event, 4, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerInstrumentDamaged"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>

                    <!-- Marcar Instrumento como avariado-->
                    <tr>
                      <td scope="col">Instrumento marcado como reparado</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationInstrumentRepaired"
                            id="checkboxNotificationInstrumentRepaired"
                            [checked]="getUserConfiguration('bell', 5)"
                            (change)="onCheckboxChange($event, 5, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationInstrumentRepaired"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerInstrumentRepaired"
                            id="checkboxBannerInstrumentRepaired"
                            [checked]="getUserConfiguration('banner', 5)"
                            (change)="onCheckboxChange($event, 5, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerInstrumentRepaired"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Tabela Leituras -->
            <div
              class="row mt-2"
              *ngIf="formAlertCenter.controls['type_alerts'].value == 2"
            >
              <div class="table-responsive-md">
                <table class="table table-bordered table-hover align-middle">
                  <thead>
                    <tr>
                      <th scope="col" class="col-md-6">Evento</th>
                      <th scope="col">Forma de recebimento</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Cadastro de Leituras -->
                    <tr>
                      <td scope="col">Cadastro de Leituras</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationReadingCreated"
                            id="checkboxNotificationReadingCreated"
                            [checked]="getUserConfiguration('bell', 6)"
                            (change)="onCheckboxChange($event, 6, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationReadingCreated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerReadingCreated"
                            id="checkboxBannerReadingCreated"
                            [checked]="getUserConfiguration('banner', 6)"
                            (change)="onCheckboxChange($event, 6, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerReadingCreated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Edição de Leituras -->
                    <tr>
                      <td scope="col">Edição de Leituras</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationReadingUpdated"
                            id="checkboxNotificationReadingUpdated"
                            [checked]="getUserConfiguration('bell', 7)"
                            (change)="onCheckboxChange($event, 7, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationReadingUpdated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerReadingUpdated"
                            id="checkboxBannerReadingUpdated"
                            [checked]="getUserConfiguration('banner', 7)"
                            (change)="onCheckboxChange($event, 7, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerReadingUpdated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Exclusão de Leituras -->
                    <tr>
                      <td scope="col">Exclusão de Leituras</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationReadingDeleted"
                            id="checkboxNotificationReadingDeleted"
                            [checked]="getUserConfiguration('bell', 8)"
                            (change)="onCheckboxChange($event, 8, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationReadingDeleted"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerReadingDeleted"
                            id="checkboxBannerReadingDeleted"
                            [checked]="getUserConfiguration('banner', 8)"
                            (change)="onCheckboxChange($event, 8, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerReadingDeleted"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Alertas de carta de controle -->
                    <tr>
                      <td scope="col">Alertas de carta de controle</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationControlLetter"
                            id="checkboxNotificationControlLetter"
                            [checked]="getUserConfiguration('bell', 9)"
                            (change)="onCheckboxChange($event, 9, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationControlLetter"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerControlLetter"
                            id="checkboxBannerControlLetter"
                            [checked]="getUserConfiguration('banner', 9)"
                            (change)="onCheckboxChange($event, 9, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerControlLetter"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Tabela Estabilidade -->
            <div
              class="row mt-2"
              *ngIf="formAlertCenter.controls['type_alerts'].value == 3"
            >
              <div class="table-responsive-md">
                <table class="table table-bordered table-hover align-middle">
                  <thead>
                    <tr>
                      <th scope="col" class="col-md-6">Evento</th>
                      <th scope="col">Forma de recebimento</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Criação de fator de segurança -->
                    <tr>
                      <td scope="col">Criação de fator de segurança (FS)</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationSecurityLevelCreated"
                            id="checkboxNotificationSecurityLevelCreated"
                            [checked]="getUserConfiguration('bell', 10)"
                            (change)="onCheckboxChange($event, 10, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationSecurityLevelCreated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerSecurityLevelCreated"
                            id="checkboxBannerSecurityLevelCreated"
                            [checked]="getUserConfiguration('banner', 10)"
                            (change)="onCheckboxChange($event, 10, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerSecurityLevelCreated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Fator de segurança acima dos níveis tolerados -->
                    <tr>
                      <td scope="col">
                        Fator de segurança acima dos níveis tolerados
                      </td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationSecurityLevelAboveTolerance"
                            id="checkboxNotificationSecurityLevelAboveTolerance"
                            [checked]="getUserConfiguration('bell', 11)"
                            (change)="onCheckboxChange($event, 11, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationSecurityLevelAboveTolerance"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerSecurityLevelAboveTolerance"
                            id="checkboxBannerSecurityLevelAboveTolerance"
                            [checked]="getUserConfiguration('banner', 11)"
                            (change)="onCheckboxChange($event, 11, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerSecurityLevelAboveTolerance"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Alerta de Gradiente Hidráulico -->
                    <tr>
                      <td scope="col">Alerta de Gradiente Hidráulico</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationForDrainage"
                            id="checkboxNotificationForDrainage"
                            [checked]="getUserConfiguration('bell', 14)"
                            (change)="onCheckboxChange($event, 14, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationForDrainage"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerForDrainage"
                            id="checkboxBannerForDrainage"
                            [checked]="getUserConfiguration('banner', 14)"
                            (change)="onCheckboxChange($event, 14, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerForDrainage"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Tabela Licenças -->
            <div
              class="row mt-2"
              *ngIf="formAlertCenter.controls['type_alerts'].value == 4"
            >
              <div class="table-responsive-md">
                <table class="table table-bordered table-hover align-middle">
                  <thead>
                    <tr>
                      <th scope="col" class="col-md-6">Evento</th>
                      <th scope="col">Forma de recebimento</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Alerta de licença a expirar -->
                    <tr>
                      <td scope="col">Alerta de licença a expirar</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationLicenseExpiration"
                            id="checkboxNotificationLicenseExpiration"
                            [checked]="getUserConfiguration('bell', 13)"
                            (change)="onCheckboxChange($event, 13, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationLicenseExpiration"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerLicenseExpiration"
                            id="checkboxBannerLicenseExpiration"
                            [checked]="getUserConfiguration('banner', 13)"
                            (change)="onCheckboxChange($event, 13, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerLicenseExpiration"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Tabela de Inspeções -->
            <div
              class="row mt-2"
              *ngIf="formAlertCenter.controls['type_alerts'].value == 5"
            >
              <div class="table-responsive-md">
                <table class="table table-bordered table-hover align-middle">
                  <thead>
                    <tr>
                      <th scope="col" class="col-md-6">Evento</th>
                      <th scope="col">Forma de recebimento</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Alerta de licença a expirar -->
                    <tr>
                      <td scope="col">Alerta de Inspeção em atraso</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationInspectionOverdue"
                            id="checkboxNotificationInspectionOverdue"
                            [checked]="getUserConfiguration('bell', 12)"
                            (change)="onCheckboxChange($event, 12, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationInspectionOverdue"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerInspectionOverdue"
                            id="checkboxBannerInspectionOverdue"
                            [checked]="getUserConfiguration('banner', 12)"
                            (change)="onCheckboxChange($event, 12, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerInspectionOverdue"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Tabela de Análise de Estabilidade -->
            <div
              class="row mt-2"
              *ngIf="formAlertCenter.controls['type_alerts'].value == 6"
            >
              <div class="table-responsive-md">
                <table class="table table-bordered table-hover align-middle">
                  <thead>
                    <tr>
                      <th scope="col" class="col-md-6">Evento</th>
                      <th scope="col">Forma de recebimento</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Cálculo de nova análise de estabilidade -->
                    <tr>
                      <td scope="col">
                        Cálculo de nova análise de estabilidade
                      </td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStabilityAnalysisCreated"
                            id="checkboxNotificationStabilityAnalysisCreated"
                            [checked]="getUserConfiguration('bell', 16)"
                            (change)="onCheckboxChange($event, 16, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStabilityAnalysisCreated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStabilityAnalysisCreated"
                            id="checkboxNotificationStabilityAnalysisCreated"
                            [checked]="getUserConfiguration('banner', 16)"
                            (change)="onCheckboxChange($event, 16, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStabilityAnalysisCreated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Edição de análise de estabilidade -->
                    <tr>
                      <td scope="col">Edição de análise de estabilidade</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            id="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            [checked]="getUserConfiguration('bell', 17)"
                            (change)="onCheckboxChange($event, 17, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            id="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            [checked]="getUserConfiguration('banner', 17)"
                            (change)="onCheckboxChange($event, 17, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Gradiente hidráulico no pé da estrutura -->
                    <tr>
                      <td scope="col">
                        Gradiente hidráulico no pé da estrutura
                      </td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            id="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            [checked]="getUserConfiguration('bell', 17)"
                            (change)="onCheckboxChange($event, 17, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            id="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            [checked]="getUserConfiguration('banner', 17)"
                            (change)="onCheckboxChange($event, 17, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Fator de segurança inferior ao recomendado -->
                    <tr>
                      <td scope="col">
                        Fator de segurança inferior ao recomendado
                      </td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            id="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            [checked]="getUserConfiguration('bell', 17)"
                            (change)="onCheckboxChange($event, 17, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            id="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            [checked]="getUserConfiguration('banner', 17)"
                            (change)="onCheckboxChange($event, 17, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStabilityStabilityAnalysisUpdated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Tabela Estrutura -->
            <div
              class="row mt-2"
              *ngIf="formAlertCenter.controls['type_alerts'].value == 7"
            >
              <div class="table-responsive-md">
                <table class="table table-bordered table-hover align-middle">
                  <thead>
                    <tr>
                      <th scope="col" class="col-md-3">Evento</th>
                      <th scope="col" class="col-md-3">Forma de recebimento</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Edição de Estrutura -->
                    <tr>
                      <td scope="col">Edição de Estruturas</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStructureUpdated"
                            id="checkboxNotificationStructureUpdated"
                            [checked]="getUserConfiguration('bell', 2)"
                            (change)="onCheckboxChange($event, 2, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStructureUpdated"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerStructureUpdated"
                            id="checkboxBannerStructureUpdated"
                            [checked]="getUserConfiguration('banner', 2)"
                            (change)="onCheckboxChange($event, 2, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerStructureUpdated"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                    <!-- Inativação de Estrutura -->
                    <tr>
                      <td scope="col">Inativação de Estruturas</td>
                      <td scope="col">
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxNotificationStructureDeleted"
                            id="checkboxNotificationStructureDeleted"
                            [checked]="getUserConfiguration('bell', 3)"
                            (change)="onCheckboxChange($event, 3, 'bell')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxNotificationStructureDeleted"
                            >Notificação</label
                          >
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            name="checkboxBannerStructureDeleted"
                            id="checkboxBannerStructureDeleted"
                            [checked]="getUserConfiguration('banner', 3)"
                            (change)="onCheckboxChange($event, 3, 'banner')"
                          />
                          <label
                            class="form-check-label"
                            for="checkboxBannerStructureDeleted"
                            >Banner</label
                          >
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Aba Histórico -->
        <div class="tab-content" id="myTabContent">
          <div
            class="tab-pane fade mt-2"
            [ngClass]="historyTabConfig.active ? 'show active' : ''"
            id="history"
            role="tabpanel"
            aria-labelledby="history-tab"
          >
            <div class="row mt-2">
              <div class="col-md-3">
                <label class="form-label">Período</label>
                <select
                  class="form-select"
                  formControlName="history_alerts_period"
                  (change)="loadNotificationsHistoryByPeriod()"
                >
                  <option
                    *ngFor="let item of notificationHistoryPeriod"
                    [ngValue]="item.value"
                  >
                    {{ item.label }}
                  </option>
                </select>
              </div>

              <!-- Data inicial -->
              <div
                class="col-md-3"
                *ngIf="formAlertCenter.get('history_alerts_period').value == 0"
              >
                <label class="form-label">Data inicial:</label>
                <input
                  type="date"
                  class="form-control"
                  formControlName="start_date"
                />
              </div>

              <!-- Data  final -->
              <div
                class="col-md-3"
                *ngIf="formAlertCenter.get('history_alerts_period').value == 0"
              >
                <label class="form-label">Data final:</label>
                <input
                  type="date"
                  class="form-control"
                  formControlName="end_date"
                />
              </div>
              <div
                class="col-md-3 d-flex align-items-end justify-content-start mb-3"
                style="padding: 30px"
                *ngIf="formAlertCenter.get('history_alerts_period').value == 0"
              >
                <app-button
                  *ngIf="
                    formAlertCenter.get('start_date').valid &&
                    formAlertCenter.get('end_date').valid
                  "
                  [class]="'btn-logisoil-blue'"
                  [icon]="'fa fa-search'"
                  [label]="'Buscar'"
                  class="me-1"
                  (click)="loadNotificationsHistoryByPeriod()"
                ></app-button>
              </div>
            </div>

            <!-- Tabela -->
            <div class="row mt-2" *ngIf="tableData.length > 0">
              <label class="form-title mt-2 mb-3">Histórico de Alertas:</label>
              <app-table
                [messageReturn]="message"
                [tableHeader]="tableHeader"
                [tableData]="tableData"
                [permissaoUsuario]="permissaoUsuario"
              >
              </app-table>

              <!-- Paginação -->
              <app-paginator
                [collectionSize]="collectionSize"
                [page]="page"
                [maxSize]="10"
                [boundaryLinks]="true"
                [pageSize]="pageSize"
                (sendPageChange)="loadPage($event)"
                [enableItemPerPage]="true"
              ></app-paginator>
            </div>

            <!-- Alerta -->
            <div
              class="alert mt-3"
              [ngClass]="message.class"
              role="alert"
              *ngIf="message.status"
              [innerHTML]="message.text"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Botão Voltar -->
    <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [label]="'Voltar à tela inicial'"
        [icon]="'fa fa-arrow-left'"
        [click]="goBack.bind(this)"
        class="me-1"
      ></app-button>
    </div>
  </div>
</form>
