# README

Este repositório contém o código fonte do projeto Logisoil - Versão 3.

Tecnologias:

- NodeJs versão 22.14.0;
- Angular versão 13.1.4;

## Configuração do projeto

Para executar este projeto, siga os passos a seguir:

1. Pré-requisitos

   - Ter o Git instalado. Link: https://git-scm.com/downloads;
   - Ter o NodeJs instalado. Link: https://nodejs.org/en/download/;
   - Ter o Angular CLI em sua versão 13.1.4.

2. Iniciando o projeto

   - Após clonar o projeto, navegue até o diretório do mesmo e execute os comandos no terminal:
     - npm install;
     - Caso o Angular CLI não esteja instalado, executar o comando: npm i -g @angular/cli@13.1.4

## Características importantes em projetos angular

1. Variáveis e funções:

   - Variáveis e funções acessadas em component.html (exemplo) precisam ser declaradas como públicas.

## Servidor de desenvolvimento

Execute `ng serve --open` para rodar o projeto em desenvolvimento, o navegador se abrirá automaticamente.

Develop:

```sh
$ ng serve
```

Homolog:

```sh
$ ng serve -c homolog --optimization=false
ou
$ ng serve --configuration=homolog
```

Production:

```sh
$ ng serve -c production --optimization=false
ou
$ ng serve --configuration=production
```

## Code scaffolding

Execute `ng generate component component-name` para gerar um novo componente.

```sh
$ ng generate component component-name
```

## Build

Execute `ng build` para gerar os arquivos de distribuição. Os artefatos de build ficam na pasta `www/`.

Develop:

```sh
$ ng build
```

Homolog:

```sh
$ ng build -c homolog
ou
$ ng build --configuration=homolog
```

Production:

```sh
$ ng build -c production
ou
$ ng build --configuration=production

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
```
