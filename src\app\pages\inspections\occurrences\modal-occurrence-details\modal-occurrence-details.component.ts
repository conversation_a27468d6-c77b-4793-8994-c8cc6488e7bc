import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-occurrence-details',
  templateUrl: './modal-occurrence-details.component.html',
  styleUrls: ['./modal-occurrence-details.component.scss']
})
export class ModalOccurrenceDetailsComponent implements OnInit {
  @ViewChild('modalOccurrenceDetails') ModalOccurrenceDetails: ElementRef;

  @Output() public sendClickEvent = new EventEmitter();

  @Input() public config: any = null;
  @Input() public component: any = null;
  @Input() public title: any = '';

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  openModal() {
    this.modalService.open(this.ModalOccurrenceDetails, { size: 'xl' });
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  clickEvent(action: any = null) {
    this.sendClickEvent.emit(action);
  }
}
