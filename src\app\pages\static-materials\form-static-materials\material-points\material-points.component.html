<div class="col-md-12">
  <form [formGroup]="formPoints">
    <div class="row mt-2">
      <div class="col-md-12">
        <div class="card">
          <div class="card-body list-points">
            <!-- Tabela -->
            <div class="row">
              <div class="col-md-1">
                <label
                  class="form-table d-flex align-items-center justify-content-center"
                  >Ponto</label
                >
              </div>
              <div class="col-md-4">
                <label
                  class="form-table d-flex align-items-center justify-content-center"
                  >{{ pointsInput.x }}</label
                >
              </div>
              <div class="col-md-4">
                <label
                  class="form-table d-flex align-items-center justify-content-center"
                  >{{ pointsInput.y }}</label
                >
              </div>
              <div class="col-md-1"></div>
            </div>
            <!-- Grafico -->
            <div formArrayName="points">
              <ng-template
                ngFor
                let-point
                [ngForOf]="points().controls"
                let-pointIndex="index"
              >
                <div [formGroupName]="pointIndex" class="row mt-1">
                  <div
                    class="col-md-1 d-flex align-items-center justify-content-center"
                  >
                    <label class="form-table">{{ pointIndex + 1 }}</label>
                  </div>

                  <div class="col-md-4">
                    <input
                      type="text"
                      class="form-control"
                      min="0"
                      step="0.000001"
                      formControlName="point_x"
                      (keypress)="
                        func.controlNumber(
                          $event,
                          points().controls[pointIndex].get('point_x'),
                          'positiveDecimalDot'
                        )
                      "
                      (keyup)="
                        func.controlNumber(
                          $event,
                          points().controls[pointIndex].get('point_x')
                        )
                      "
                      (blur)="func.formatType($event, 4)"
                      (focus)="func.formatType($event)"
                      aria-valuenow=""
                      appDisableScroll
                    />
                    <small
                      class="form-text text-danger"
                      *ngIf="
                        !points().controls[pointIndex].get('point_x').valid &&
                        points().controls[pointIndex].get('point_x').touched
                      "
                      >Campo Obrigatório.</small
                    >
                  </div>
                  <div class="col-md-4">
                    <input
                      type="text"
                      class="form-control"
                      min="0"
                      step="0.000001"
                      formControlName="point_y"
                      (keypress)="
                        func.controlNumber(
                          $event,
                          points().controls[pointIndex].get('point_y'),
                          'positiveDecimalDot'
                        )
                      "
                      (keyup)="
                        func.controlNumber(
                          $event,
                          points().controls[pointIndex].get('point_y')
                        )
                      "
                      (blur)="func.formatType($event, 4)"
                      (focus)="func.formatType($event)"
                      aria-valuenow=""
                      appDisableScroll
                    />
                    <small
                      class="form-text text-danger"
                      *ngIf="
                        !points().controls[pointIndex].get('point_y').valid &&
                        points().controls[pointIndex].get('point_y').touched
                      "
                      >Campo Obrigatório.</small
                    >
                  </div>
                  <div
                    class="col-md-1 d-flex align-items-center justify-content-center"
                  >
                    <app-button
                      [class]="'btn-logisoil-remove-item'"
                      [icon]="'fa fa-trash'"
                      (click)="removePoint(pointIndex)"
                      ngbTooltip="Remover ponto"
                      placement="right"
                      *ngIf="pointIndex > 0 && !view"
                    ></app-button>
                  </div>
                </div>
              </ng-template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Botoes -->
    <div class="row mt-2">
      <div class="col-md-4 d-flex justify-content-start">
        <app-button
          [class]="'btn-logisoil-blue'"
          [customBtn]="true"
          [icon]="'fas fa-plus-circle'"
          [label]="'Novo ponto'"
          (click)="addPoint()"
          *ngIf="points().controls.length < 100 && !view"
        ></app-button>
      </div>
      <div class="col-md-8 d-flex align-items-end justify-content-end">
        <app-button
          [class]="'btn-logisoil-chart'"
          [customBtn]="true"
          [icon]="'fa fa-line-chart'"
          [label]="'Gerar gráfico'"
          [type]="true"
          (click)="generateChart()"
          *ngIf="points().valid && buttonChart"
        ></app-button>
      </div>
    </div>
  </form>
</div>
