import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListUnitsComponent } from './list-units/list-units.component';
import { RegisterUnitComponent } from './register-unit/register-unit.component';

import { AppGuard } from 'src/app/guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListUnitsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastrarUnidade,
    component: RegisterUnitComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarUnidade,
    component: RegisterUnitComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarUnidade,
    component: RegisterUnitComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UnitsRoutingModule {}
