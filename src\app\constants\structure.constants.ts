//Protocolo de transferência
const transferProtocol = [
  { protocol: 'HTTPS', value: '1' },
  { protocol: 'SFTP', value: '2' },
  { protocol: 'FTPS', value: '3' }
];

// Cadastro de estrutura - aba Geral
const currentSituation = [
  { status: 'Operando', value: '1' },
  { status: 'Em obra de alteamento', value: '2' },
  { status: 'Em obra de reforço', value: '3' },
  { status: 'Desativada', value: '4' }
];

// Cadastro de estrutura - aba Estabilidade
const conditions = [
  {
    name: 'Avaliar condição drenada',
    id: 'should_evaluate_drained_condition'
  },
  {
    name: 'Avaliar condição não drenada',
    id: 'should_evaluate_undrained_condition'
  },
  {
    name: 'Avaliar condição pseudo estática',
    id: 'should_evaluate_pseudo_static_condition'
  }
];

const occurrenceDecision = [
  { label: 'Sim', value: '1' },
  { label: 'N<PERSON>', value: '2' }
];

// Cadastro de estrutura - aba Estabilidade
const activities = {
  TailingsBeach: 1,
  1: {
    activity: 1,
    name: 'Praia de rejeitos',
    index: 1
  },
  CreateReservoir: 2,
  2: {
    activity: 2,
    name: 'Criar Reservatório',
    index: 2
  },
  SeparateInstruments: 3,
  3: {
    activity: 3,
    name: 'Instrumentos separados',
    index: 3
  }
};

// Cadastro de estrutura - aba Configurações de Análise
const calculationMethods = [
  { name: 'Bishop simplified', id: '1' },
  { name: 'Corps of engineers 1', id: '2' },
  { name: 'Corps of engineers 2', id: '3' },
  { name: 'GLE/Morgenstern-Price', id: '4' },
  { name: 'Janbu simplified', id: '5' },
  { name: 'Janbu corrected', id: '6' },
  { name: 'Lowe karafiath', id: '7' },
  { name: 'Ordinary or fellenius', id: '8' },
  { name: 'Spencer', id: '9' },
  { name: 'Sarma', id: '10' }
];

// Cadastro de estrutura - aba Configurações de Análise
const surfacesType = {
  surface_type_circular: {
    name: 'Circular',
    id: '1',
    search: [
      {
        method: 'Auto Refine Search C',
        value: '1',
        fields: ['divisions_along_slope', 'circles_per_division', 'number_of_iterations', 'divisions_next_iteration']
      },
      { method: 'Grid Search', value: '2', fields: ['radius_increment'] },
      { method: 'Slope Search', value: '3', fields: ['number_of_surfaces'] }
    ]
  },
  surface_type_non_circular: {
    name: 'Não Circular',
    id: '2',
    search: [
      {
        method: 'Auto Refine Search NC',
        value: '1',
        fields: ['divisions_along_slope', 'surfaces_per_division', 'number_of_iterations', 'divisions_next_iteration', 'number_of_vertices_along_surface']
      },
      {
        method: 'Cuckoo Search',
        value: '2',
        fields: ['maximum_iterations', 'number_of_nests', 'number_of_vertices_along_surface']
      },
      {
        method: 'Simulated Annealing',
        value: '3',
        fields: [
          'initial_number_of_surface_vertices',
          'initial_number_of_iterations',
          'maximum_number_of_steps',
          'tolerance_for_stopping_criterion',
          'number_of_factors_safety_compared_before_stopping'
        ]
      },
      {
        method: 'Particle Swarm Search',
        value: '4',
        fields: ['initial_number_of_surface_vertices', 'maximum_iterations', 'number_of_particles']
      },
      { method: 'Block Search', value: '5', fields: ['number_of_surfaces'] },
      { method: 'Path Search', value: '6', fields: ['number_of_surfaces'] }
    ]
  }
};

// Cadastro de estrutura - aba Ficha Técnica
const classifications = [
  { classification: 'AA', value: '1' },
  { classification: 'A', value: '2' },
  { classification: 'B', value: '3' },
  { classification: 'C', value: '4' },
  { classification: 'D', value: '5' }
];

const surfacesTypeMethodsFields = {
  divisions_along_slope: {
    name: 'Divisões ao longo do talude:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  circles_per_division: {
    name: 'Círculos por divisão:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  number_of_iterations: {
    name: 'Número de iterações:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  divisions_next_iteration: {
    name: 'Divisões para próxima iteração (%):',
    min: 0,
    max: 100,
    step: 1,
    length: 3
  },
  surfaces_per_division: {
    name: 'Superfícíes por divisão:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  number_of_vertices_along_surface: {
    name: 'Número de vértices ao longo da superfíce:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  radius_increment: {
    name: 'Incremento de raio:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  number_of_surfaces: {
    name: 'Número de superfícies:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  initial_number_of_surface_vertices: {
    name: 'Número inicial de vértices das cunhas:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  maximum_iterations: {
    name: 'Máximo de iterações:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  number_of_nests: {
    name: 'Número de ninhos:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  initial_number_of_iterations: {
    name: 'Número inicial de iterações:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  maximum_number_of_steps: {
    name: 'Número máximo de etapas:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  number_of_factors_safety_compared_before_stopping: {
    name: 'Número de fatores de segurança comparados antes de parar:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10
  },
  tolerance_for_stopping_criterion: {
    name: 'Tolerância para critério de parada:',
    min: 0,
    max: 1,
    step: 0.00001,
    length: 7
  },
  number_of_particles: {
    name: 'Número de partículas:',
    min: 0,
    max: 9999999999,
    step: 1,
    length: 10,
    option: 'positive'
  }
};

const environmentalDamagePotential = {
  total_reservoir_volume: {
    name: 'Volume Total do reservatório (a)',
    weight: [
      {
        value: 1,
        description: 'Muito pequeno: <= 1 milhão m³'
      },
      {
        value: 2,
        description: 'Pequeno: 1 milhão a 5 milhões m³'
      },
      {
        value: 3,
        description: 'Médio: 5 milhões a 25 milhões m³'
      },
      {
        value: 4,
        description: 'Grande: 25 milhões a 50 milhões m³'
      },
      {
        value: 5,
        description: 'Muito grande: >= 50 milhões m³'
      }
    ]
  },
  population_downstream: {
    name: 'Existência de população a Jusante (b)',
    weight: [
      {
        value: 0,
        description: 'INEXISTENTE (não existem pessoas permanentes/residentes ou temporárias/transitando na área afetada a jusante da barragem)'
      },
      {
        value: 3,
        description:
          'POUCO FREQUENTE (não existem pessoas ocupando permanentemente a área afetada a jusante da barragem, mas existe estrada vicinal de uso local)'
      },
      {
        value: 5,
        description:
          'FREQUENTE (não existem pessoas ocupando permanentemente a área afetada a jusante da barragem, mas existe rodovia municipal ou estadual ou federal ou outro local e/ou empreendimento de permanência eventual de pessoas que poderão ser atingidas'
      },
      {
        value: 10,
        description: 'EXISTENTE (existem pessoas ocupando permanentemente a área afetada a jusante da barragem, portanto, vidas humanas poderão ser atingidas)'
      }
    ]
  },
  environmental_impact: {
    name: 'Impacto Ambiental (c)',
    weight: [
      {
        value: 0,
        description:
          'INSIGNIFICANTE (área afetada a jusante da barragem encontra-se totalmente descaracterizada de suas condições naturais e a estrutura armazena apenas resíduos Classe 11 0 - Inertes, segundo a NBR 10004 da ABNT)'
      },
      {
        value: 2,
        description:
          'POUCO SIGNIFICATIVO (área afetada a jusante da barragem - (não apresenta área de interesse ambiental relevante ou áreas protegidas em legislação específica, excluídas APPs, e armazena apenas resíduos Classe 11 8 - Inertes, segundo a NBR 10004 da ABNT)'
      },
      {
        value: 6,
        description:
          'SIGNIFICATIVO (área afetada a jusante da barragem apresenta área de interesse ambiental relevante ou áreas protegidas em legislação específica, excluídas APPs, e armazena apenas resíduos Classe 118 - Inertes, segundo a NBR 10004 da ABNT)'
      },
      {
        value: 8,
        description:
          'Mirro SIGNIFICATIVO (barragem armazena rejeitos ou resíduos sólidos classificados na Classe IIA - Não Inertes, segundo a NBR 10004 da ABNT)'
      },
      {
        value: 10,
        description:
          'MUITO SIGNIFICATIVO AGRAVADO (barragem armazena rejeitos ou resíduos sólidos classificados na Classe I - Perigosos segundo a NBR 10004 da ABNT)'
      }
    ]
  },
  socioeconomic_impact: {
    name: 'Impacto socioeconômico (d)',
    weight: [
      {
        value: 0,
        description: 'INEXISTENTE (não existem quaisquer instalações na área afetada a jusante da barragem'
      },
      {
        value: 1,
        description:
          'BAIXO (existe pequena concentração de instalações residenciais, agrícolas, industriais ou de infraestrutura de relevância socioeconômica cultural na área afetada a jusante da barragem'
      },
      {
        value: 3,
        description:
          'MÉDIO (existe moderada concentração de instalações residenciais, agrícolas, industriais ou de infraestrutura de relevância socioeconômico cultural na área afetada a jusante da barragem)'
      },
      {
        value: 5,
        description:
          'ALTO (existe alta concentração de instalações residenciais, agrícolas, industriais ou de infraestrutura de relevância solo-econômico cultural na área afetada a jusante da barragem'
      }
    ]
  }
};

export {
  transferProtocol,
  currentSituation,
  conditions,
  activities,
  occurrenceDecision,
  calculationMethods,
  surfacesType,
  classifications,
  surfacesTypeMethodsFields,
  environmentalDamagePotential
};
