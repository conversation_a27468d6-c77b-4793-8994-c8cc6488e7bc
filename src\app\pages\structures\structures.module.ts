import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { LogisoilDirectivesModule } from 'src/app/shared/logisoil-directives.module';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { StructuresRoutingModule } from './structures-routing.module';
import { SharedModule } from '@components/shared.module';

import { ListStructuresComponent } from './list-structures/list-structures.component';
import { RegisterStructureComponent } from './register-structure/register-structure.component';
import { ImagesStructureComponent } from './images-structure/images-structure.component';
import { HistoryStructureComponent } from './history-structure/history-structure.component';

// Tela de Cadastro de Estruturas
import { GeneralTabComponent } from './tabs/general-tab/general-tab.component';
import { AvancedTabComponent } from './tabs/avanced-tab/avanced-tab.component';
import { StabilityTabComponent } from './tabs/stability-tab/stability-tab.component';
import { DatasheetTabComponent } from './tabs/datasheet-tab/datasheet-tab.component';
import { InspectionsTabComponent } from './tabs/inspections-tab/inspections-tab.component';
import { ConfigurationsTabComponent } from './tabs/configurations-tab/configurations-tab.component';
import { ResponsibleTabComponent } from './tabs/responsible-tab/responsible-tab.component';
import { LayersTabComponent } from './tabs/layers-tab/layers-tab.component';

//Máscara de CPF
import { BrMaskerModule } from 'br-mask';

@NgModule({
  declarations: [
    ListStructuresComponent,
    RegisterStructureComponent,
    ImagesStructureComponent,
    HistoryStructureComponent,
    GeneralTabComponent,
    AvancedTabComponent,
    StabilityTabComponent,
    DatasheetTabComponent,
    InspectionsTabComponent,
    ConfigurationsTabComponent,
    ResponsibleTabComponent,
    LayersTabComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    LogisoilDirectivesModule,
    NgbModule,
    ReactiveFormsModule,
    SharedModule,
    NgMultiSelectDropDownModule.forRoot(),
    StructuresRoutingModule,
    BrMaskerModule
  ]
})
export class StructuresModule {}
