import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-standard',
  templateUrl: './modal-standard.component.html',
  styleUrls: ['./modal-standard.component.scss']
})
export class ModalStandardComponent implements OnInit {
  @ViewChild('modalStandard') modalStandard: ElementRef;
  @Output() public sendClickEvent = new EventEmitter();
  @Input() public title: string = '';
  @Input() public message: string = '';
  @Input() public instruction: string = '';
  @Input() public id: string = '';
  @Input() public data: any = {};
  @Input() public modalConfig: any = {};

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  /**
   * Abre o modal de confirmação utilizando o serviço de modais (`modalService`).
   *
   * Este método é responsável por exibir a caixa de diálogo referenciada em `modalStandard`.
   */
  openModal() {
    this.modalService.open(this.modalStandard, { size: 'lg' });
  }

  /**
   * Emite um evento de clique contendo os dados da ação configurada no modal.
   *
   * - O evento emitido inclui:
   *   - Ação (`this.modalConfig.action`)
   *   - Identificador (`this.id`)
   *   - Dados relacionados (`this.data`)
   *
   * Este método pode ser chamado ao clicar em botões de confirmação, cancelamento ou outros controles do modal.
   *
   * @param {any} action - (opcional) Ação alternativa ou sobrescrita.
   */
  clickRowEvent(action: any = null) {
    this.sendClickEvent.emit({ action: this.modalConfig.action, id: this.id, data: this.data });
  }

  /**
   * Emite o evento de confirmação com os dados da modal.
   * O `context` deve ser definido em `data.context` no componente pai.
   */
  confirm(): void {
    this.sendClickEvent.emit({
      data: this.data,
      id: this.id
    });
  }
}
