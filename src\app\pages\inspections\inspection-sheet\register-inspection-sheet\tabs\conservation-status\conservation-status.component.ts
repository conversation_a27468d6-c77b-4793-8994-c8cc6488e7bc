import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { EnvironmentalConservationStatus } from 'src/app/constants/inspections.constants';

@Component({
  selector: 'app-conservation-status',
  templateUrl: './conservation-status.component.html',
  styleUrls: ['./conservation-status.component.scss']
})
export class ConservationStatusComponent implements OnInit, OnChanges {
  @Input() public inspectionSheetType: number = null;
  @Input() public status: number = null;
  @Input() public locked: boolean = false;
  @Input() public view: boolean = false;
  @Output() public formChanged = new EventEmitter<any>();

  public conservationStatusForm: FormGroup = this.fb.group({
    spillway_structures_reliability: '',
    percolation: '',
    deformations_and_settlements: '',
    slope_deterioration: '',
    surface_drainage: ''
  });

  public environmentalConservationStatus: any = EnvironmentalConservationStatus;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {}

  /**
   * Detecta e processa mudanças nos inputs vinculados ao componente.
   * @param {SimpleChanges} changes - Contém as mudanças detectadas nos inputs do componente.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.status || changes.locked) {
      this.toggleForm();
    }
  }

  /**
   * Configura os dados recebidos do componente pai no formulário de estado de conservação.
   * @param {any} dados - Dados do estado de conservação.
   */
  setData(dados: any): void {
    if (dados) {
      this.conservationStatusForm.patchValue({
        spillway_structures_reliability: dados.spillway_structures_reliability ?? null,
        percolation: dados.percolation ?? null,
        deformations_and_settlements: dados.deformations_and_settlements ?? null,
        slope_deterioration: dados.slope_deterioration ?? null,
        surface_drainage: dados.surface_drainage ?? null
      });
    }
    this.toggleForm();
  }

  /**
   * Obtém as chaves das colunas do estado de conservação ambiental.
   * @returns {string[]} - Lista de chaves das colunas.
   */
  getColumnKeys() {
    return Object.keys(this.environmentalConservationStatus);
  }

  /**
   * Obtém o maior peso de classificação dentro dos critérios ambientais.
   * @returns {number[]} - Lista de índices de pesos.
   */
  getMaxWeights() {
    return Array.from({ length: Math.max(...this.getColumnKeys().map((key) => this.environmentalConservationStatus[key].weight.length)) }, (_, i) => i);
  }

  /**
   * Seleciona um valor em um botão de rádio no formulário.
   * @param {string} key - Chave do campo no formulário.
   * @param {any} value - Valor selecionado.
   */
  selectRadio(key: string, value: any): void {
    if (this.conservationStatusForm.disabled) {
      return; // Ignora cliques quando o formulário está desabilitado
    }
    if (value !== undefined && value !== null) {
      const control = this.conservationStatusForm.get(key);
      if (control) {
        const currentValue = control.value;
        // Alterna o valor ao clicar na célula
        control.setValue(currentValue === value ? null : value);
        this.triggerSave();
      }
    }
  }

  /**
   * Dispara o evento de alteração do formulário quando um campo for modificado.
   * @param {string} controlName - Nome do campo modificado.
   */
  onFormChange(controlName: string): void {
    if (this.conservationStatusForm.disabled) {
      return; // Ignora mudanças quando o formulário está desabilitado
    }
    this.triggerSave();
  }

  /**
   * Dispara o evento de salvamento do formulário.
   */
  triggerSave(): void {
    this.formChanged.emit();
  }

  /**
   * Alterna entre habilitar ou desabilitar o formulário de estado de conservação.
   */
  toggleForm() {
    const isLocked = this.locked || [2, 3].includes(this.status) || this.view;
    if (isLocked) {
      this.conservationStatusForm.disable();
    } else {
      this.conservationStatusForm.enable();
    }
  }
}
