import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-reload-timer',
  templateUrl: './reload-timer.component.html',
  styleUrls: ['./reload-timer.component.scss']
})
export class ReloadTimerComponent implements OnInit {
  @Input() progressColor: string = '#4caf50'; // Cor padrão da barra de progresso
  @Input() showButton: boolean = true; // Define se o botão será exibido
  @Input() intervalDuration: number = 5000; // Duração total do intervalo (em milissegundos)
  @Input() reverse: boolean = false; // Define se a barra será decrescente (direita para esquerda)
  @Input() startTrigger: boolean = false;
  @Input() message: string = 'Você será redirecionado para a tela de listagem em'; // Mensagem opcional para exibição abaixo da barra
  @Output() timerComplete = new EventEmitter<void>();

  public progress = this.reverse ? 100 : 0; // Inicializa o progresso de acordo com a direção
  public intervalStep = 100; // Tempo de atualização do progresso (100ms)
  public intervalId: any; // Referência ao intervalo
  public isProgressVisible: boolean = false;
  public remainingTime: number = 0; // Tempo restante (decremental)
  public elapsedTime: number = 0; // Tempo decorrido (incremental)
  public displayTime: any = 0; // Tempo exibido no contador

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.startTrigger) {
      if (this.startTrigger) {
        this.startProgressBar();
      } else {
        this.resetProgress();
      }
    }
  }

  get formattedTime(): string {
    const time = this.reverse
      ? this.remainingTime / 1000 // Decremental
      : this.elapsedTime / 1000; // Incremental
    return time.toFixed(0); // Formata para décimos de segundo
  }

  startProgressBar(): void {
    this.resetProgress();
    this.isProgressVisible = true;

    const stepIncrement = (this.intervalStep / this.intervalDuration) * 100;
    const totalSteps = this.intervalDuration / this.intervalStep;
    let currentStep = 0;

    this.intervalId = setInterval(() => {
      currentStep++;
      const time = this.reverse ? ((totalSteps - currentStep) * this.intervalStep) / 1000 : (currentStep * this.intervalStep) / 1000;
      this.displayTime = time.toFixed(0);

      if (this.reverse) {
        this.progress -= stepIncrement;
        if (this.progress <= 0) {
          this.completeProgress();
        }
      } else {
        this.progress += stepIncrement;
        if (this.progress >= 100) {
          this.completeProgress();
        }
      }
    }, this.intervalStep);
  }

  completeProgress(): void {
    clearInterval(this.intervalId);

    this.progress = this.reverse ? 0 : 100;
    const time = this.reverse ? 0 : this.intervalDuration / 1000;
    this.displayTime = time.toFixed(0);
    this.isProgressVisible = false;

    this.timerComplete.emit(); // Emite evento ao completar
  }

  resetProgress(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    this.progress = this.reverse ? 100 : 0;
    this.isProgressVisible = false;
    const time = this.reverse ? this.intervalDuration / 1000 : 0;
    this.displayTime = time.toFixed(0);
  }

  reload(): void {
    if ((!this.reverse && this.progress >= 100) || (this.reverse && this.progress <= 0)) {
      this.startProgressBar();
    }
  }

  stopProgressBar(): void {
    clearInterval(this.intervalId);
    this.isProgressVisible = false;
  }
}
