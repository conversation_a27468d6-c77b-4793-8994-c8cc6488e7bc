import { Component, ElementRef, Input, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-instructions-spreadsheet',
  templateUrl: './modal-instructions-spreadsheet.component.html',
  styleUrls: ['./modal-instructions-spreadsheet.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalInstructionsSpreadsheetComponent implements OnInit {
  @ViewChild('modalInstructionsSpreadsheet') modalInstructionsSpreadsheet: ElementRef;
  @Input() public typeInstrument = null;

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  openModal() {
    this.modalService.open(this.modalInstructionsSpreadsheet, { size: 'lg' });
  }
}
