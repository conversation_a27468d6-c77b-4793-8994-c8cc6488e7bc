<form class="list-content" [formGroup]="formInstruments">
  <div class="alert alert-success mt-3" role="alert" *ngIf="message.status">
    {{ message.text }}
  </div>

  <!-- Editar instrumentos em massa -->
  <div
    class="row g-3 mt-1 instructions"
    *ngIf="!edit && !view && editingBatch && !insertSpreadsheet"
  >
    <!-- Instruções -->
    <p class="fw-bold text-instructions">
      <i class="fa fa-exclamation-circle me-2" aria-hidden="true"></i
      >Instruções:
    </p>
    <p>
      <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
      Utilize sempre a planilha disponível na opção
      <strong class="text-decoration-underline">“Baixar Informações”</strong> da
      tela principal de Instrumentação para editar os dados dos instrumentos.
    </p>
    <p>
      <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
      Preencha a planilha de acordo com o cabeçalho criado. Lembre-se de manter
      o formato do arquivo!
    </p>
    <p>
      <i class="fa fa-exclamation-circle me-2" aria-hidden="true"></i
      ><strong>Importante: nunca altere a ordem das colunas!</strong>
    </p>
    <p>
      <i class="fa fa-exclamation-circle me-2" aria-hidden="true"></i>Não altere
      a informação da <strong>Estrutura</strong> do instrumento.
    </p>
    <p>
      <i class="fa fa-exclamation-circle me-2" aria-hidden="true"></i>
      Nunca altere o <strong>"Identificador"</strong> do instrumento ou dos
      pontos de medição/células de pressão/anéis magnéticos.
    </p>
    <p>
      <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
      Preencha apenas um dos formatos de coordenadas para todos os instrumentos
      <strong class="text-decoration-underline">(UTM ou geográficas)</strong>.
      Caso escolha informar coordenadas UTM, as colunas referentes aos dados das
      Coordenadas Geográficas devem ficar em branco e vice-versa.
    </p>
  </div>
  <!-- Tipo de instrumento -->
  <div
    class="row g-3 mt-1"
    *ngIf="!edit && !view && (editingBatch || insertSpreadsheet)"
  >
    <div class="col-md-4">
      <label class="form-label">Selecione o tipo de instrumento:</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="instrumentSettings"
        [data]="typeInstrumentsList"
        formControlName="file_type_instrument"
        [disabled]="formInstruments.controls['file_type_instrument'].disabled"
      >
      </ng-multiselect-dropdown>
    </div>
  </div>
  <div
    class="row g-3 mt-1"
    *ngIf="
      !edit &&
      !view &&
      (editingBatch || insertSpreadsheet) &&
      formInstruments.controls['file_type_instrument'].value != ''
    "
  >
    <!-- Upload arquivo -->
    <div
      class="col-md-4"
      *ngIf="formInstruments.controls['file_name'].value == ''"
    >
      <label class="form-label">Selecionar Arquivo:</label>
      <input
        type="file"
        class="form-control"
        accept=".csv, .xlsx"
        (change)="uploadFile($event)"
      />
      <em class="form-text">Formatos permitidos: .csv e .xlsx </em>
      <br />
      <em class="form-text">Tamanho máximo permitido: 10MB. </em>
    </div>
    <div
      class="col-md-4"
      *ngIf="formInstruments.controls['file_name'].value != ''"
    >
      <label class="form-label">Arquivo Selecionado: </label><br />
      <span>{{ formInstruments.controls['file_name'].value }}</span>
    </div>
  </div>
  <div class="row mt-2">
    <div class="col-md-4">
      <app-button
        [class]="'btn-logisoil-gray me-2'"
        [customBtn]="true"
        [label]="'Remover arquivo'"
        [icon]="'fa fa-upload'"
        (click)="removeFile()"
        *ngIf="formInstruments.controls['file_name'].value != ''"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-blue'"
        [customBtn]="true"
        [label]="'Enviar arquivo'"
        [icon]="'fa fa-upload'"
        (click)="getInstrumentGroup()"
        *ngIf="fileValid && formInstruments.controls['file_name'].value != ''"
      ></app-button>
    </div>
  </div>

  <div class="row g-3 mt-2" *ngIf="!editingBatch && !insertSpreadsheet">
    <!-- Cliente -->
    <div class="col-md-4">
      <label class="form-label">Cliente</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="clientSettings"
        [data]="clients"
        (onSelect)="getUnits($event, 'select')"
        (onDeSelect)="getUnits($event, 'deselect')"
        formControlName="client"
        [ngClass]="{
          'disabled-dropdown': formInstruments.get('client')?.disabled
        }"
      >
      </ng-multiselect-dropdown>
      <small
        class="form-text text-danger"
        *ngIf="
          !formInstruments.get('client').valid &&
          formInstruments.get('client').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Unidade -->
    <div class="col-md-4">
      <label class="form-label">Unidade</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="unitSettings"
        [data]="units"
        (onSelect)="getStructures($event, 'select')"
        (onDeSelect)="getStructures($event, 'deselect')"
        formControlName="client_unit"
        [ngClass]="{
          'disabled-dropdown': formInstruments.get('client_unit')?.disabled
        }"
      >
      </ng-multiselect-dropdown>
      <small
        class="form-text text-danger"
        *ngIf="
          !formInstruments.get('client_unit').valid &&
          formInstruments.get('client_unit').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Estrutura -->
    <div class="col-md-4">
      <label class="form-label">Estrutura</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="structureSettings"
        [data]="structures"
        (onSelect)="setDatum($event, 'select')"
        (onDeSelect)="setDatum($event, 'deselect')"
        formControlName="structure"
        [ngClass]="{
          'disabled-dropdown': formInstruments.get('structure')?.disabled
        }"
      >
      </ng-multiselect-dropdown>
      <small
        class="form-text text-danger"
        *ngIf="
          !formInstruments.get('structure').valid &&
          formInstruments.get('structure').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>

  <!-- Preencher Múltiplos-->
  <div
    class="row g-3 mt-2"
    *ngIf="
      !ctrlFillMultiples &&
      !edit &&
      !view &&
      !editingBatch &&
      !insertSpreadsheet
    "
  >
    <div class="col-md-2 mt-2 mb-2">
      <app-button
        [class]="'btn-logisoil-blue'"
        [customBtn]="true"
        [label]="'Preencher Múltiplos'"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        (click)="ctrlFillMultiples = !ctrlFillMultiples"
      ></app-button>
    </div>
  </div>

  <div
    class="row g-3 mt-2 mb-2"
    *ngIf="
      ctrlFillMultiples && !edit && !view && !editingBatch && !insertSpreadsheet
    "
  >
    <ul class="nav nav-tabs px-2">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page">Preencher Múltiplos</a>
      </li>
    </ul>
    <!-- Data instalação -->
    <div class="col-md-2">
      <label class="form-label">Data de instalação</label>
      <input
        type="date"
        class="form-control"
        formControlName="multiple_installation_date"
      />
    </div>
    <!-- Resp. instalação (Opcional) -->
    <div class="col-md-3">
      <label class="form-label">Resp. instalação</label>
      <input
        type="text"
        class="form-control"
        formControlName="multiple_responsible_for_installation"
        autocomplete="off"
        [attr.maxlength]="32"
        (input)="onValueChange($event, 'multiple_responsible_for_installation')"
        placeholder="Opcional"
      />
      <small class="form-text text-muted"
        >Caracteres
        {{ charCounts['multiple_responsible_for_installation'] || 0 }} de 32
      </small>
    </div>
    <!-- Modelo  (Opcional) -->
    <div class="col-md-3">
      <label class="form-label">Modelo</label>
      <input
        type="text"
        class="form-control"
        formControlName="multiple_model"
        autocomplete="off"
        [attr.maxlength]="32"
        (input)="onValueChange($event, 'multiple_model')"
        placeholder="Opcional"
      />
      <small class="form-text text-muted"
        >Caracteres {{ charCounts['multiple_model'] || 0 }} de 32
      </small>
    </div>
    <!-- Automatizado? -->
    <div class="col-md-2">
      <label class="form-label">Automatizado?</label>
      <select class="form-select" formControlName="multiple_automated">
        <option value="">Selecione...</option>
        <option *ngFor="let item of automated" [ngValue]="item.value">
          {{ item.label }}
        </option>
      </select>
    </div>
    <!-- Online -->
    <div class="col-md-2">
      <label class="form-label">Online</label>
      <select class="form-select" formControlName="multiple_online">
        <option value="">Selecione...</option>
        <option *ngFor="let item of onLine" [ngValue]="item.value">
          {{ item.label }}
        </option>
      </select>
    </div>
    <!-- Botões -->
    <div class="col-md-12 mt-2 d-flex align-items-end justify-content-end">
      <app-button
        [class]="'btn-logisoil-red'"
        [icon]="'fa fa-thin fa-xmark'"
        [label]="'Cancelar'"
        [type]="true"
        (click)="ctrlFillMultiples = !ctrlFillMultiples; fillMutipleUpdate()"
        class="me-1"
      >
      </app-button>
      <app-button
        [class]="'btn-logisoil-green'"
        [label]="'Preencher Múltiplos'"
        [type]="true"
        (click)="fillMutipleUpdate()"
      >
      </app-button>
    </div>
  </div>

  <!-- Bloco para cadastro de instrumento(s) -->
  <div class="row g-3 mt-2">
    <!-- Mensagens de info -->
    <app-alert [class]="'alert-danger'" [messages]="messagesInfo"></app-alert>
    <ng-template
      ngFor
      let-instrumentItem
      [ngForOf]="instrumentsReference"
      let-i="index"
    >
      <app-form-instruments
        #instrumentRef
        [item]="instrumentItem"
        [index]="i"
        (sendRemove)="removeInstrument($event)"
        [data]="instrumentsData[i] ? instrumentsData[i] : null"
        [edit]="edit"
        [view]="view"
        [editingBatch]="editingBatch"
        [insertSpreadsheet]="insertSpreadsheet"
        [coordinateStructure]="coordinateStructure"
      ></app-form-instruments>
    </ng-template>
    <div
      class="col-md-12 mt-2 d-flex align-items-end justify-content-end"
      *ngIf="!edit && !view && !editingBatch && !insertSpreadsheet"
    >
      <app-button
        [class]="'btn-logisoil-blue'"
        [customBtn]="true"
        [icon]="'fas fa-plus-circle'"
        [label]="'Novo Instrumento'"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        (click)="addInstrument()"
      ></app-button>
    </div>
  </div>

  <div class="row mt-2">
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <div class="alert alert-success mt-3" role="alert" *ngIf="message.status">
    {{ message.text }}
  </div>

  <!-- Botões -->
  <div class="col-md-12 mt-3 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-green'"
      [icon]="'fa fa-thin fa-floppy-disk'"
      [label]="'Salvar'"
      [type]="true"
      class="me-1"
      (click)="validate()"
      *ngIf="!view && formCrtl"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/instruments']"
    ></app-button>
  </div>
</form>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
