import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HistoryReadingComponent } from './history-reading.component';

describe('HistoryInstrumentComponent', () => {
  let component: HistoryReadingComponent;
  let fixture: ComponentFixture<HistoryReadingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ HistoryReadingComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HistoryReadingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
