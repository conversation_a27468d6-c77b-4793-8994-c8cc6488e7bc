import { AfterViewInit, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup } from '@angular/forms';

import { MessageCadastro, MessagePadroes, ModalConfirm } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';

import { DataService } from 'src/app/services/data.service';
import { MenuService } from 'src/app/services/menu.service';
import { SharedService } from 'src/app/services/shared.service';
import { UserService } from 'src/app/services/user.service';

import { NotificationService } from 'src/app/services/notification.service';
import { PackagesService as PackagesServiceApi } from 'src/app/services/api/packages.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';

import fn from 'src/app/utils/function.utils';
import * as _ from 'lodash';
import * as moment from 'moment';
import { NgxSpinnerService } from 'ngx-spinner';
import { catchError, forkJoin, map, of } from 'rxjs';

@Component({
  selector: 'app-list-stability',
  templateUrl: './list-stability.component.html',
  styleUrls: ['./list-stability.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListStabilityComponent implements OnInit, AfterViewInit {
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalConfirm') ModalConfirm: any;

  public formFilter: FormGroup = new FormGroup({
    ClientId: new FormControl([]),
    ClientUnitId: new FormControl([]),
    StructureId: new FormControl([]),
    StartDate: new FormControl(''),
    EndDate: new FormControl(''),
    PageSize: new FormControl(5)
  });

  public menu: any = [];

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };
  public messagesError: any = null;

  public multipleSettings = MultiSelectDefault.Multiple;
  public viewSettings = MultiSelectDefault.View;

  public bannerNotifications: any = [];
  public showNotificationBanner: boolean = true;

  public page: number = 1;
  public pageSize: number = 5;
  public collectionSize: number = 0;

  public tableData: any = [];

  public controlsFilter: any = null;
  public openTabs: boolean = false;
  public func = fn;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public modalData: any = {};
  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  constructor(
    private router: Router,
    private dataService: DataService,
    private menuService: MenuService,
    private packagesServiceApi: PackagesServiceApi,
    private sectionsServiceApi: SectionsServiceApi,
    private sharedService: SharedService,
    private ngxSpinnerService: NgxSpinnerService,
    private userService: UserService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.ngxSpinnerService.show();
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;
    this.menu = this.menuService.setMenu('menuEstabilidade');
    this.controlsFilter = this.formFilter.controls;

    this.notificationService.notificationsBanner$.subscribe(({ stabilityNotifications }) => {
      this.bannerNotifications = stabilityNotifications;
    });

    this.notificationService.bannerVisibility$.subscribe(({ stabilityBannerStatus }) => {
      this.showNotificationBanner = stabilityBannerStatus;
    });
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros após um pequeno delay.
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.searchPackages();
    }, 1000);
  }

  //Lista de pacotes - Recupera a lista de pacotes com base nos parâmetros fornecidos
  getPackagesList(params) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    params['Page'] = this.page;
    params['PageSize'] = this.formFilter.controls['PageSize'].value;

    this.packagesServiceApi.getPackagesSearch(params).subscribe(
      (resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;

        if (resp['status'] == 200) {
          this.tableData = dados ? dados.data : [];
          this.collectionSize = dados.total_items_count;
          this.formatData(this.tableData);
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
          this.messageReturn.class = 'alert-warning';

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  /**
   * Calcula pacotes.
   * Realiza o cálculo de pacotes com base no ID do pacote e seção.
   *
   * @param {number} packageId - O ID do pacote.
   * @param {number} [sectionId=null] - O ID da seção, opcional.
   * @param {string} action - A ação a ser realizada.
   * @returns {void}
   */
  calculatePackages(packageId, sectionId = null, action) {
    this.ngxSpinnerService.show();

    let params = { id: packageId };
    if (sectionId != null) {
      params['sectionId'] = sectionId;
    }

    this.packagesServiceApi.postPackagesCalculate(params, packageId).subscribe(
      (resp) => {
        this.message.text = MessageCadastro.CalculatePackages;
        this.message.status = true;
        this.message.class = 'alert-warning';
        this.searchPackages();

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Exclui pacotes.
   * Exclui pacotes com base no ID do pacote e seção.
   *
   * @param {number} packageId - O ID do pacote.
   * @param {number} [sectionId=null] - O ID da seção, opcional.
   * @returns {void}
   */
  deletePackages(packageId, sectionId = null) {
    this.ngxSpinnerService.show();

    let params = { id: packageId };
    if (sectionId != null) {
      params['sectionId'] = sectionId;
    }

    this.packagesServiceApi.deletePackages(params, packageId).subscribe(
      (resp) => {
        this.message.text = MessageCadastro.IgnorePackages;
        this.message.status = true;
        this.message.class = 'alert-warning';
        this.searchPackages();

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  // Pesquisa pacotes com base nos filtros aplicados
  searchPackages() {
    let filterHierarchy = this.hierarchy.getFilters();
    let params = this.formFilter.getRawValue();

    params.ClientId = filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '';
    params.ClientUnitId = filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '';
    params.StructureId = filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].id : '';

    this.getPackagesList(params);
  }

  /**
   * Formata os dados da tabela de pacotes para exibição na interface.
   * Este método processa cada pacote recebido, ajustando as informações
   * conforme necessário para exibição. Especificamente:
   * - Traduz o valor numérico do status para um valor descritivo em português.
   * - Configura a cota da régua limnimétrica montante como "-" caso o valor seja nulo.
   * - Define a primeira seção como a aba ativa (tab) para cada pacote.
   * - Atribui o nome da estrutura ao pacote.
   *
   * @param $data Array de objetos que representam os dados dos pacotes recebidos da API.
   * @returns void
   */
  formatData($data: any[]): void {
    const packagesStatusMap = {
      1: 'Pendente',
      2: 'Processando',
      3: 'Falhou'
    };

    const itemObservables = $data.map((row) => {
      let item = _.cloneDeep(row);

      item['upstream_linimetric_ruler_quota'] = row.upstream_linimetric_ruler?.quota == null ? '-' : row.upstream_linimetric_ruler.quota;
      item['tab'] = false;
      item['message'] = { text: '', status: false };
      item['structure_name'] = row.structure.name;
      item['status'] = packagesStatusMap[row.status] || 'Desconhecido';

      const sectionObservables = row.sections.map((section, index) => {
        return this.sectionsServiceApi.getSectionsReviews(section.id).pipe(
          map((reviews: any) => {
            let itemSection = _.cloneDeep(section);
            itemSection['tab'] = index === 0;

            const sortedReviews = [...reviews].sort((a, b) => a.index - b.index);
            itemSection['reviews'] = sortedReviews;

            // Aqui adicionamos os campos de controle para os selects:
            itemSection['selectedReviewId'] = '';
            itemSection['selectedStages'] = [];
            itemSection['selectedStageId'] = '';

            return itemSection;
          }),
          catchError((err) => {
            let itemSection = _.cloneDeep(section);
            itemSection['tab'] = index === 0;
            itemSection['reviews'] = [];
            itemSection['selectedReviewId'] = '';
            itemSection['selectedStages'] = [];
            return of(itemSection);
          })
        );
      });

      return forkJoin(sectionObservables).pipe(
        map((resolvedSections) => {
          item['sections'] = resolvedSections;
          return item;
        })
      );
    });

    forkJoin(itemObservables).subscribe((results) => {
      this.tableData = results;
    });
  }

  /**
   * Gerencia as abas da tabela.
   *
   * @param {number} row - A linha selecionada.
   * @param {number} [col=0] - A coluna selecionada.
   * @param {string} [option='col'] - A opção de gerenciamento.
   * @returns {void}
   */
  managerTabs(row, col = 0, option = 'col') {
    this.tableData.map((itemRow, idxRow) => {
      if (option == 'col') {
        itemRow.sections.map((itemCol, idxCol) => {
          if (row == idxRow && col == idxCol) {
            this.tableData[idxRow].sections[idxCol].tab = true;
          } else {
            this.tableData[idxRow].sections[idxCol].tab = false;
          }
        });
      } else if (option == 'row') {
        if (row == idxRow && this.tableData[idxRow].tab) {
          this.tableData[idxRow].tab = false;
        } else if (row == idxRow) {
          this.tableData[idxRow].tab = true;
          this.tableData[idxRow].sections[col].tab = true;
        } else {
          this.tableData[idxRow].tab = false;
        }
      }

      if (this.tableData[idxRow].tab) {
        this.getPackagesById(idxRow);
      }
    });
  }

  /**
   * Recupera pacotes com base no ID da linha.
   *
   * @param {number} idxRow - O índice da linha.
   * @returns {void}
   */
  getPackagesById(idxRow) {
    this.packagesServiceApi.getPackagesById(this.tableData[idxRow].id).subscribe(
      (resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;

        if (resp['status'] == 200) {
          this.formatDataPackages(dados, idxRow);
        } else {
          this.tableData[idxRow].message.text = MessagePadroes.NoRegister;
          this.tableData[idxRow].message.status = true;

          setTimeout(() => {
            this.tableData[idxRow].message.status = false;
          }, 3000);
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * Formata os dados dos pacotes com base na linha.
   *
   * @param {object} $dados - Os dados a serem formatados.
   * @param {number} idxRow - O índice da linha.
   * @returns {void}
   */
  formatDataPackages($dados, idxRow) {
    let reading_date = this.tableData[idxRow].date;

    this.tableData[idxRow].sections.map((section, idxCol) => {
      let dataSection = fn.findIndexByValue($dados.sections, section.id, 'id', true);
      const instrumentWith = fn.filterByKeyAndValue(dataSection.instruments, 'reading_value_linked_to_package', true); //Com leitura
      const instrumentWithout = fn.filterByKeyAndValue(dataSection.instruments, 'reading_value_linked_to_package', false); //Sem leitura

      let itemSection = {};
      itemSection['data'] = {
        upstream_linimetric_ruler: $dados.upstream_linimetric_ruler,
        beach_length: { length: null, date: null },
        work_quota: dataSection.work_quota != null ? dataSection.work_quota.split(',') : null,
        downstream_linimetric: { quota: null, date: null }
      };

      if (dataSection.beach_length && dataSection.beach_length.length == null) {
        itemSection['data'].beach_length.length = '-';
      } else if (dataSection.beach_length && dataSection.beach_length.length && dataSection.beach_length.date != reading_date) {
        itemSection['data'].beach_length.length = `${dataSection.beach_length.length} - (Última leitura em: ${moment(dataSection.beach_length.date).format(
          'DD/MM/YYYY HH:mm:ss)'
        )}`;
      } else {
        itemSection['data'].beach_length.length = dataSection.beach_length.length;
      }

      if ($dados.downstream_linimetric && $dados.quota == null) {
        itemSection['data'].downstream_linimetric.quota = '-';
      } else if ($dados.downstream_linimetric && $dados.downstream_linimetric.quota && $dados.downstream_linimetric.date != reading_date) {
        itemSection['data'].downstream_linimetric.quota = `${$dados.downstream_linimetric.quota} - Última leitura em: ${moment(
          $dados.downstream_linimetric.date
        ).format('DD/MM/YYYY HH:mm:ss')}`;
      } else {
        itemSection['data'].downstream_linimetric.quota = $dados.downstream_linimetric.quota;
      }

      itemSection['instrumentWith'] = instrumentWith.map((instrument) => {
        if (instrument.reading_value && instrument.reading_value.quota == null) {
          instrument.reading_value = { quota: '-', quota_variation: '-', quota_variation_color: '#000000' };
        } else if (instrument.reading_value && instrument.reading_value.date != reading_date) {
          instrument.reading_value['quota'] = `${instrument.reading_value['quota']} - Última leitura em: ${moment(instrument.reading_value.date).format(
            'DD/MM/YYYY HH:mm:ss'
          )}`;
          instrument.reading_value['quota_variation_color'] =
            instrument.reading_value['quota_variation'] == 0 ? 'orange' : instrument.reading_value['quota_variation'] < 0 ? '#34b575' : 'red';
        } else {
          instrument.reading_value['quota_variation_color'] =
            instrument.reading_value['quota_variation'] == 0 ? 'orange' : instrument.reading_value['quota_variation'] < 0 ? '#34b575' : 'red';
        }
        return instrument;
      });

      itemSection['instrumentWithout'] = instrumentWithout.map((instrument) => {
        if (instrument.reading_value == null) {
          instrument.reading_value = { quota: '-', quota_variation: '-' };
        } else if (instrument.reading_value && instrument.reading_value.date != reading_date) {
          // let quota_complement = instrument.reading_value['quota'] == null ? 'Seco' : instrument.reading_value['quota'].toFixed(2);
          let quota_complement =
            instrument.reading_value['dry'] == false
              ? 'Leitura Seca - Cota: ' + instrument.reading_value['quota'].toFixed(2)
              : instrument.reading_value['quota'].toFixed(2);
          instrument.reading_value['quota'] = `${quota_complement} - (Última leitura em: ${moment(instrument.reading_value.date).format(
            'DD/MM/YYYY HH:mm:ss'
          )})`;
        }
        return instrument;
      });

      // itemSection['process_water_level_downstream'] = $dados.structure_have_activity_process_water_level_downstream;

      this.tableData[idxRow].sections[idxCol]['package'] = itemSection;
    });
  }

  /**
   * Gerencia os eventos de clique com base na ação.
   *
   * @param {object} [$event=null] - O evento de clique.
   * @returns {void}
   */
  clickEvent($event: any = null) {
    switch ($event.action) {
      case 'delete':
        this.deletePackages($event.data.package_id, $event.data.section_id);
        break;
      case 'calculate':
      case 'forceCalculation':
        this.calculatePackages($event.data.package_id, $event.data.section_id, $event.action);
        break;
      case 'stabilityMap':
        this.router.navigate(['stability/stabilityMap']);
        break;
      case 'stabilityAnalysis':
        this.router.navigate(['stability/stabilityAnalysis']);
        break;
    }
  }

  // Reseta os filtros aplicados na pesquisa de pacotes
  resetFilter() {
    this.hierarchy.resetFilters();

    this.formFilter.controls['ClientId'].setValue([]);
    this.formFilter.controls['ClientUnitId'].setValue([]);
    this.formFilter.controls['StructureId'].setValue([]);
    this.formFilter.controls['StartDate'].setValue('');
    this.formFilter.controls['EndDate'].setValue('');
    this.formFilter.controls['PageSize'].setValue(5);
    this.page = 1;
    this.searchPackages();
  }

  /**
   * Abre um modal com base na ação e nos dados fornecidos.
   *
   * @param {string} action - A ação a ser realizada.
   * @param {object} [data={}] - Os dados para o modal.
   * @returns {void}
   */
  openModal(action: string, data: any = {}) {
    switch (action) {
      case 'delete':
        this.modalTitle = 'Ignorar pacote';
        this.modalMessage = ModalConfirm.ConfirmarOperacao;
        this.modalInstruction = null;
        this.modalConfig.iconHeader = null;
        this.modalConfig.action = 'delete';
        this.modalData = data;
        this.ModalConfirm.openModal();
        break;
      case 'calculate':
        this.modalTitle = 'Calcular pacote';
        this.modalMessage = ModalConfirm.ConfirmarOperacao;
        this.modalInstruction = null;
        this.modalConfig.iconHeader = null;
        this.modalConfig.action = 'calculate';
        this.modalData = data;
        this.ModalConfirm.openModal();
        break;
      case 'forceCalculation':
        this.modalTitle = 'Forçar cálculo';
        this.modalMessage = ModalConfirm.ForcarCalculo;
        this.modalInstruction = ``;
        this.modalConfig.iconHeader = null;
        this.modalConfig.action = 'forceCalculation';
        this.ModalConfirm.openModal();
        this.modalData = data;
        break;
    }
  }

  onReviewChange(section: any): void {
    const selectedReview = section.reviews.find((r) => r.id === section.selectedReviewId);
    section.selectedStages = selectedReview?.construction_stages ?? [];
  }

  /**
   * Atualiza a seção do pacote com a revisão e etapa selecionadas.
   *
   * @param row Objeto da linha (pacote)
   * @param section Objeto da seção dentro do pacote
   */
  onUpdateSection(row: any, section: any): void {
    // Verifica se revisão e etapa estão selecionadas
    if (!section.selectedReviewId) {
      return;
    }

    const payload = {
      id: row.id,
      sections: [
        {
          id: section.id,
          section_review_id: section.selectedReviewId,
          construction_stage_id: section.selectedStageId || null
        }
      ]
    };

    this.ngxSpinnerService.show();

    this.packagesServiceApi
      .patchPackages(payload, row.id)
      .subscribe(
        () => {
          this.getPackagesById(this.tableData.findIndex((r) => r.id === row.id));
        },
        (error) => {
          console.error('Erro ao atualizar pacote', error);
        }
      )
      .add(() => this.ngxSpinnerService.hide());
  }

  // Metodo que recebe a pagina selecionada
  loadPage(selectPage: number) {
    this.page = selectPage;
    this.searchPackages();
  }

  //Fecha o banner de notificação de estabilidade.
  handleCloseNotificationBanner() {
    this.showNotificationBanner = false;
    this.notificationService.handleCloseStabilityBanner();
  }

  goBack() {
    this.router.navigate(['/']);
  }
}
