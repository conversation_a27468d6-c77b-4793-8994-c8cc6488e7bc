<div class="col-md-12">
  <form [formGroup]="formInstrument">
    <!-- Bloco para cadastro de instrumento(s) -->
    <div class="row g-3">
      <div class="col-sm-12">
        <div class="card" [ngClass]="!formValid ? '' : 'not-valid'">
          <div class="card-body">
            <label
              class="label-title"
              [ngClass]="!formValid ? '' : 'not-valid'"
              >{{ instrumentName }}</label
            >
            <app-button
              [class]="
                'btn-logisoil-remove-item-outside position-absolute top-0 end-0'
              "
              [icon]="'fa fa-trash'"
              (click)="removeMe()"
              *ngIf="index != 0"
            >
            </app-button>
            <div class="row mt-1">
              <!-- Identificador -->
              <div class="col-md-3">
                <label class="form-label">Identificador</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="identifier"
                  autocomplete="off"
                  [attr.maxlength]="32"
                  (input)="onValueChange($event, 'identifier')"
                  [readonly]="edit || editingBatch"
                />

                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous d-block"
                  *ngIf="_current['identifier'] != null"
                  >{{ _current['identifier'] }}<br
                /></small>
                <!-- Campo obrigatório -->
                <small
                  class="form-text text-danger d-block"
                  *ngIf="
                    !formInstrument.get('identifier').valid &&
                    formInstrument.get('identifier').touched &&
                    formInstrument.get('identifier').errors != null
                  "
                  >Campo Obrigatório.</small
                >
                <!-- Contador de caracteres -->
                <small class="form-text text-muted d-block"
                  >Caracteres {{ charCounts['identifier'] || 0 }} de 32
                </small>
              </div>
              <!-- Nome alternativo (Opcional) -->
              <div class="col-md-3">
                <label class="form-label">Nome alternativo</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="alternative_name"
                  autocomplete="off"
                  [attr.maxlength]="32"
                  (input)="onValueChange($event, 'alternative_name')"
                  placeholder="Opcional"
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous d-block"
                  *ngIf="_current['alternative_name'] != null"
                  >{{ _current['alternative_name'] }}<br
                /></small>
                <small class="form-text text-muted"
                  >Caracteres {{ charCounts['alternative_name'] || 0 }} de 32
                </small>
              </div>
              <!-- Frequência de medição - Pluviógrafo -->
              <div
                class="col-md-2"
                *ngIf="fields.includes('measurement_frequency')"
              >
                <label class="form-label text-nowrap"
                  >Frequência de medição</label
                >
                <input
                  type="time"
                  class="form-control"
                  formControlName="measurement_frequency"
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['measurement_frequency'] != null"
                  >{{ _current['measurement_frequency'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('measurement_frequency').valid &&
                    formInstrument.get('measurement_frequency').touched &&
                    formInstrument.get('measurement_frequency').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Cota topo -->
              <div class="col-md-3" *ngIf="fields.includes('top_quota')">
                <label class="form-label">Cota de topo (m)</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="top_quota"
                  autocomplete="off"
                  min="-9999999999999"
                  max="9999999999999"
                  maxlength="9999999999999"
                  (keypress)="func.controlNumber($event, null, 'notE')"
                  (keyup)="
                    func.controlNumber(
                      $event,
                      formInstrument.get('quota_pressure_cell')
                    )
                  "
                  (blur)="
                    func.formatType($event);
                    calcElevation({
                      item: '',
                      index: '',
                      value: formInstrument.controls['top_quota'].value,
                      type: typeMeasure,
                      component: 'instrument'
                    })
                  "
                  (focus)="func.formatType($event)"
                  appDisableScroll
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['top_quota'] != null"
                  >{{ _current['top_quota'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('top_quota').valid &&
                    formInstrument.get('top_quota').touched &&
                    formInstrument.get('top_quota').errors != null
                  "
                  >Campo Obrigatório.</small
                >
                <div
                  class="form-check"
                  *ngIf="
                    (edit || editingBatch) && typeMeasure == 'measure_point'
                  "
                >
                  <input
                    class="form-check-input"
                    type="checkbox"
                    value=""
                    formControlName="realocation"
                    (change)="
                      calcElevation({
                        item: '',
                        index: '',
                        value: formInstrument.controls['top_quota'].value,
                        type: typeMeasure,
                        component: 'instrument'
                      })
                    "
                  />
                  <label class="form-label">
                    É realocação de instrumento?
                  </label>
                </div>
              </div>
              <!-- Cota base -->
              <div class="col-md-3" *ngIf="fields.includes('base_quota')">
                <label class="form-label">Cota de base (m)</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="base_quota"
                  autocomplete="off"
                  min="-9999999999999"
                  max="9999999999999"
                  maxlength="9999999999999"
                  (keypress)="func.controlNumber($event, null, 'notE')"
                  (keyup)="
                    func.controlNumber(
                      $event,
                      formInstrument.get('quota_pressure_cell')
                    )
                  "
                  (blur)="func.formatType($event)"
                  (focus)="func.formatType($event)"
                  appDisableScroll
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['base_quota'] != null"
                  >{{ _current['base_quota'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('base_quota').valid &&
                    formInstrument.get('base_quota').touched &&
                    formInstrument.get('base_quota').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Cota -->
              <div class="col-md-3" *ngIf="fields.includes('quota')">
                <label class="form-label">Cota (m)</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="quota"
                  autocomplete="off"
                  min="-9999999999999"
                  max="9999999999999"
                  maxlength="9999999999999"
                  (keypress)="func.controlNumber($event, null, 'notE')"
                  (keyup)="
                    func.controlNumber($event, formInstrument.get('quota'))
                  "
                  (blur)="func.formatType($event)"
                  (focus)="func.formatType($event)"
                  appDisableScroll
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['quota'] != null"
                  >{{ _current['quota'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('quota').valid &&
                    formInstrument.get('quota').touched &&
                    formInstrument.get('quota').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Azimute -->
              <div class="col-md-3" *ngIf="fields.includes('azimuth')">
                <label class="form-label">Azimute (graus)</label>
                <input
                  type="text"
                  formControlName="azimuth"
                  class="form-control"
                  min="0"
                  max="360"
                  step="0.0000001"
                  maxlength="100000000"
                  placeholder="Digite um valor entre 0 e 360. Opcional"
                  autocomplete="off"
                  (keypress)="
                    func.controlNumber(
                      $event,
                      formInstrument.get('azimuth'),
                      'positiveDecimalDot'
                    )
                  "
                  (keyup)="
                    func.controlNumber($event, formInstrument.get('azimuth'))
                  "
                  (blur)="func.formatType($event)"
                  (focus)="func.formatType($event)"
                  appDisableScroll
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['azimuth'] != null"
                  >{{ _current['azimuth'] }}<br
                /></small>
              </div>
              <!-- Geofone -->
              <div class="col-md-3" *ngIf="fields.includes('geophone_type')">
                <label class="form-label">Tipo Geofone</label>
                <select class="form-select" formControlName="geophone_type">
                  <option value="">Selecione...</option>
                  <option
                    *ngFor="let item of geophoneType"
                    [ngValue]="item.value"
                  >
                    {{ item.label }}
                  </option>
                </select>
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['geophone_type'] != null"
                  >{{ _current['geophone_type'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('geophone_type').valid &&
                    formInstrument.get('geophone_type').touched &&
                    formInstrument.get('').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Elevação -->
              <div class="col-md-3" *ngIf="fields.includes('elevation')">
                <label class="form-label">Elevação (m)</label>
                <input
                  type="number"
                  formControlName="elevation"
                  class="form-control"
                  autocomplete="off"
                  min="-9999999999999"
                  max="9999999999999"
                  (keypress)="func.controlNumber($event, null, 'notE')"
                  (keyup)="
                    func.controlNumber($event, formInstrument.get('elevation'))
                  "
                  maxlength="9999999999999"
                  [required]="true"
                  appDisableScroll
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['elevation'] != null"
                  >{{ _current['elevation'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('elevation').valid &&
                    formInstrument.get('elevation').touched &&
                    formInstrument.get('elevation').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Posição  -->
              <div
                class="col-md-3"
                *ngIf="fields.includes('linimetric_ruler_position')"
              >
                <label class="form-label">Posição em relação à estrutura</label>
                <select
                  class="form-select"
                  formControlName="linimetric_ruler_position"
                >
                  <option value="">Selecione...</option>
                  <option *ngFor="let item of position" [ngValue]="item.value">
                    {{ item.label }}
                  </option>
                </select>
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['linimetric_ruler_position'] != null"
                  >{{ _current['linimetric_ruler_position'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('linimetric_ruler_position').valid &&
                    formInstrument.get('linimetric_ruler_position').touched &&
                    formInstrument.get('linimetric_ruler_position').errors !=
                      null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Profundidade -->
              <div class="col-md-3" *ngIf="fields.includes('depth')">
                <label class="form-label">Profundidade (m)</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="depth"
                  autocomplete="off"
                  min="-9999999999999"
                  max="9999999999999"
                  maxlength="9999999999999"
                  (keypress)="func.controlNumber($event, null, 'notE')"
                  (keyup)="
                    func.controlNumber($event, formInstrument.get('depth'))
                  "
                  (blur)="func.formatType($event)"
                  (focus)="func.formatType($event)"
                  appDisableScroll
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['depth'] != null"
                  >{{ _current['depth'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('depth').valid &&
                    formInstrument.get('depth').touched &&
                    formInstrument.get('depth').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
            </div>
            <div class="row mt-2">
              <!-- Datum -->
              <div class="col-md-3">
                <label class="form-label">DATUM</label>
                <select
                  class="form-select"
                  formControlName="datum"
                  (change)="coordinatesConversion()"
                >
                  <option value="">Selecione...</option>
                  <option *ngFor="let item of datum" [ngValue]="item.id">
                    {{ item.value }}
                  </option>
                </select>
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['datum'] != null"
                  >{{ _current['datum'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('datum').valid &&
                    formInstrument.get('datum').touched &&
                    formInstrument.get('datum').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Limite superior -->
              <div class="col-md-3" *ngIf="fields.includes('upper_limit')">
                <label class="form-label">Limite superior (mm)</label>
                <input
                  type="text"
                  formControlName="upper_limit"
                  class="form-control"
                  min="0"
                  step="0.01"
                  max="9999999999999"
                  maxlength="9999999999999"
                  autocomplete="off"
                  (keypress)="
                    func.controlNumber(
                      $event,
                      formInstrument.get('upper_limit'),
                      'positiveDecimal'
                    )
                  "
                  (keyup)="
                    func.controlNumber(
                      $event,
                      formInstrument.get('upper_limit')
                    )
                  "
                  (blur)="func.formatType($event)"
                  (focus)="func.formatType($event)"
                  appDisableScroll
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['upper_limit'] != null"
                  >{{ _current['upper_limit'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('upper_limit').valid &&
                    formInstrument.get('upper_limit').touched &&
                    formInstrument.get('upper_limit').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Limite inferior -->
              <div class="col-md-3" *ngIf="fields.includes('lower_limit')">
                <label class="form-label">Limite inferior (mm)</label>
                <input
                  type="text"
                  formControlName="lower_limit"
                  class="form-control"
                  min="0"
                  step="0.01"
                  max="9999999999999"
                  maxlength="9999999999999"
                  autocomplete="off"
                  (keypress)="
                    func.controlNumber(
                      $event,
                      formInstrument.get('lower_limit'),
                      'positiveDecimal'
                    )
                  "
                  (keyup)="
                    func.controlNumber(
                      $event,
                      formInstrument.get('lower_limit')
                    )
                  "
                  (blur)="func.formatType($event)"
                  (focus)="func.formatType($event)"
                  appDisableScroll
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['lower_limit'] != null"
                  >{{ _current['lower_limit'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('lower_limit').valid &&
                    formInstrument.get('lower_limit').touched &&
                    formInstrument.get('lower_limit').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Tipo Seco -->
              <div class="col-md-3" *ngIf="fields.includes('dry_type')">
                <label class="form-label">Tipo Seco</label>
                <select class="form-select" formControlName="dry_type">
                  <option *ngFor="let item of dryType" [ngValue]="item.value">
                    {{ item.label }}
                  </option>
                </select>
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['dry_type'] != null"
                  >{{ _current['dry_type'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('dry_type').valid &&
                    formInstrument.get('dry_type').touched &&
                    formInstrument.get('').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
            </div>

            <div class="row mt-2">
              <!-- Coordenadas UTM -->
              <div class="col-md-8">
                <div class="card">
                  <div class="card-header form-control-bg">
                    <div class="form-check form-check-inline">
                      <input
                        type="radio"
                        class="form-check-input"
                        formControlName="coordinate_format"
                        value="{{ coordinateFormatList[1].id }}"
                        (change)="
                          onCoordinateFormatChange(coordinateFormatList[1])
                        "
                      />
                      <label class="form-check-label" for="gridRadios2">
                        Coordenadas UTM
                      </label>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-3">
                        <label class="form-label">Zona (Fuso)</label>
                        <select
                          class="form-select"
                          formControlName="zone_number"
                          (change)="coordinatesConversion()"
                        >
                          <option value="">Selecione...</option>
                          <option
                            *ngFor="let item of zoneNumberUTM"
                            [ngValue]="item.id"
                          >
                            {{ item.value }}
                          </option>
                        </select>
                        <!-- Valor anterior -->
                        <small
                          class="form-text text-secondary previous"
                          *ngIf="_current['zone_number'] != null"
                          >{{ _current['zone_number'] }}<br
                        /></small>
                        <small
                          class="form-text text-danger"
                          *ngIf="
                            !formInstrument.get('zone_number').valid &&
                            formInstrument.get('zone_number').touched &&
                            formInstrument.get('zone_number').errors != null &&
                            formInstrument.get('coordinate_format').value == 2
                          "
                          >Campo Obrigatório.</small
                        >
                      </div>
                      <div class="col-md-3">
                        <label class="form-label">Letra</label>
                        <select
                          class="form-select"
                          formControlName="zone_letter"
                          (change)="coordinatesConversion()"
                        >
                          <option value="">Selecione...</option>
                          <option
                            *ngFor="let item of zoneLetterUTM"
                            [ngValue]="item.id"
                          >
                            {{ item.value }}
                          </option>
                        </select>
                        <!-- Valor anterior -->
                        <small
                          class="form-text text-secondary previous"
                          *ngIf="_current['zone_letter'] != null"
                          >{{ _current['zone_letter'] }}<br
                        /></small>
                        <small
                          class="form-text text-danger"
                          *ngIf="
                            !formInstrument.get('zone_letter').valid &&
                            formInstrument.get('zone_letter').touched &&
                            formInstrument.get('zone_letter').errors != null &&
                            formInstrument.get('coordinate_format').value == 2
                          "
                          >Campo Obrigatório.</small
                        >
                      </div>
                      <div class="col-md-3">
                        <label class="form-label">Coordenada Norte</label>
                        <input
                          type="text"
                          formControlName="northing"
                          class="form-control"
                          (blur)="
                            coordinatesConversion(); func.formatType($event)
                          "
                          (focus)="func.formatType($event)"
                          appDisableScroll
                        />
                        <!-- Valor anterior -->
                        <small
                          class="form-text text-secondary previous"
                          *ngIf="_current['northing'] != null"
                          >{{ _current['northing'] }}<br
                        /></small>
                        <small
                          class="form-text text-danger"
                          *ngIf="
                            !formInstrument.get('northing').valid &&
                            formInstrument.get('northing').touched &&
                            formInstrument.get('northing').errors != null &&
                            formInstrument.get('coordinate_format').value == 2
                          "
                          >Campo Obrigatório.</small
                        >
                      </div>
                      <div class="col-md-3">
                        <label class="form-label">Coordenada Leste</label>
                        <input
                          type="text"
                          formControlName="easting"
                          class="form-control"
                          (blur)="
                            coordinatesConversion(); func.formatType($event)
                          "
                          (focus)="func.formatType($event)"
                          appDisableScroll
                        />
                        <!-- Valor anterior -->
                        <small
                          class="form-text text-secondary previous"
                          *ngIf="_current['easting'] != null"
                          >{{ _current['easting'] }}<br
                        /></small>
                        <small
                          class="form-text text-danger"
                          *ngIf="
                            !formInstrument.get('easting').valid &&
                            formInstrument.get('easting').touched &&
                            formInstrument.get('easting').errors != null &&
                            formInstrument.get('coordinate_format').value == 2
                          "
                          >Campo Obrigatório.</small
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Coordenadas Geográficas -->
              <div class="col-md-4">
                <div class="card">
                  <div class="card-header form-control-bg">
                    <div class="form-check form-check-inline">
                      <input
                        type="radio"
                        class="form-check-input"
                        formControlName="coordinate_format"
                        value="{{ coordinateFormatList[0].id }}"
                        (change)="
                          onCoordinateFormatChange(coordinateFormatList[0])
                        "
                      />
                      <label class="form-check-label" for="gridRadios2">
                        Coordenadas Geográficas
                      </label>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6">
                        <label class="form-label">Latitude</label>
                        <input
                          type="text"
                          formControlName="latitude"
                          class="form-control"
                          (blur)="
                            coordinatesConversion(); func.formatType($event)
                          "
                          (focus)="func.formatType($event)"
                          appDisableScroll
                        />
                        <!-- Valor anterior -->
                        <small
                          class="form-text text-secondary previous"
                          *ngIf="_current['latitude'] != null"
                          >{{ _current['latitude'] }}<br
                        /></small>
                        <small
                          class="form-text text-danger"
                          *ngIf="
                            !formInstrument.get('latitude').valid &&
                            formInstrument.get('latitude').touched &&
                            formInstrument.get('latitude').errors != null &&
                            formInstrument.get('coordinate_format').value == 1
                          "
                          >Campo Obrigatório.</small
                        >
                      </div>
                      <div class="col-md-6">
                        <label class="form-label">Longitude</label>
                        <input
                          type="text"
                          formControlName="longitude"
                          class="form-control"
                          (blur)="
                            coordinatesConversion(); func.formatType($event)
                          "
                          (focus)="func.formatType($event)"
                          appDisableScroll
                        />
                        <!-- Valor anterior -->
                        <small
                          class="form-text text-secondary previous"
                          *ngIf="_current['longitude'] != null"
                          >{{ _current['longitude'] }}<br
                        /></small>
                        <small
                          class="form-text text-danger"
                          *ngIf="
                            !formInstrument.get('longitude').valid &&
                            formInstrument.get('longitude').touched &&
                            formInstrument.get('longitude').errors != null &&
                            formInstrument.get('coordinate_format').value == 1
                          "
                          >Campo Obrigatório.</small
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-12 mt-0">
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('coordinate_format').valid &&
                    formInstrument.get('coordinate_format').touched &&
                    formInstrument.get('coordinate_format').errors != null
                  "
                  >Selecione uma das Coordenadas.</small
                >
              </div>
            </div>
            <!-- Mensagens de erro -->
            <div class="col-md-12 mt-2">
              <app-alert
                [class]="'alert-danger'"
                [messages]="messagesError"
              ></app-alert>
            </div>
            <div class="row mt-2">
              <!-- Data instalação -->
              <div class="col-md-2">
                <label class="form-label">Data de instalação</label>
                <input
                  type="date"
                  class="form-control"
                  formControlName="installation_date"
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['installation_date'] != null"
                  >{{ _current['installation_date'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('installation_date').valid &&
                    formInstrument.get('installation_date').touched &&
                    formInstrument.get('installation_date').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Resp. instalação (Opcional) -->
              <div class="col-md-3">
                <label class="form-label">Resp. instalação</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="responsible_for_installation"
                  autocomplete="off"
                  [attr.maxlength]="32"
                  (input)="
                    onValueChange($event, 'responsible_for_installation')
                  "
                  placeholder="Opcional"
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous d-block"
                  *ngIf="_current['responsible_for_installation'] != null"
                  >{{ _current['responsible_for_installation'] }}<br
                /></small>
                <small class="form-text text-muted d-block"
                  >Caracteres {{ charCounts['multiple_model'] || 0 }} de 32
                </small>
              </div>
              <!-- Modelo  (Opcional) -->
              <div class="col-md-3">
                <label class="form-label">Modelo</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="model"
                  autocomplete="off"
                  [attr.maxlength]="32"
                  (input)="onValueChange($event, 'model')"
                  placeholder="Opcional"
                />
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous d-block"
                  *ngIf="_current['model'] != null"
                  >{{ _current['model'] }}<br
                /></small>
                <small class="form-text text-muted d-block"
                  >Caracteres {{ charCounts['multiple_model'] || 0 }} de 32
                </small>
              </div>
              <!-- Automatizado? -->
              <div class="col-md-2">
                <label class="form-label">Automatizado?</label>
                <select class="form-select" formControlName="automated">
                  <option value="">Selecione...</option>
                  <option *ngFor="let item of automated" [ngValue]="item.value">
                    {{ item.label }}
                  </option>
                </select>
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['automated'] != null"
                  >{{ _current['automated'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('automated').valid &&
                    formInstrument.get('automated').touched &&
                    formInstrument.get('automated').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
              <!-- Online -->
              <div class="col-md-2">
                <label class="form-label">Online</label>
                <select class="form-select" formControlName="online">
                  <option value="">Selecione...</option>
                  <option *ngFor="let item of onLine" [ngValue]="item.value">
                    {{ item.label }}
                  </option>
                </select>
                <!-- Valor anterior -->
                <small
                  class="form-text text-secondary previous"
                  *ngIf="_current['online'] != null"
                  >{{ _current['online'] }}<br
                /></small>
                <small
                  class="form-text text-danger"
                  *ngIf="
                    !formInstrument.get('online').valid &&
                    formInstrument.get('online').touched &&
                    formInstrument.get('online').errors != null
                  "
                  >Campo Obrigatório.</small
                >
              </div>
            </div>

            <!-- Níveis de segurança -->
            <div class="row mt-3" *ngIf="typeMeasure == ''">
              <app-security-levels
                [data]="datasecurityLevels"
                [edit]="edit"
                [view]="view"
                [editingBatch]="editingBatch"
                [typeInstrument]="formInstrument.controls['type'].value"
              ></app-security-levels>
            </div>

            <div class="row mt-2">
              <!-- Mensagens de info -->
              <app-alert
                [class]="'alert-danger'"
                [messages]="messagesInfo"
              ></app-alert>
            </div>

            <!-- Measurements -->
            <div class="row g-3" *ngIf="typeMeasure != ''">
              <ng-template
                ngFor
                let-measureItem
                [ngForOf]="measuresReference"
                let-i="index"
              >
                <app-measurements
                  [typeMeasure]="typeMeasure"
                  [nameMeasure]="nameMeasure"
                  #measureRef
                  [item]="measureItem"
                  [index]="i"
                  (sendRemove)="removeMeasure($event)"
                  (sendIsReferencial)="managerIsReferencial($event)"
                  (sendCalcQuota)="calcQuota($event)"
                  [data]="measuresData[i] ? measuresData[i] : null"
                  [edit]="edit"
                  [view]="view"
                  [editingBatch]="editingBatch"
                ></app-measurements>
              </ng-template>
              <div
                class="col-md-12 mt-2 d-flex align-items-end justify-content-end"
              >
                <app-button
                  [class]="'btn-logisoil-blue'"
                  [customBtn]="true"
                  [icon]="'fas fa-plus-circle'"
                  [label]="'Nova medição'"
                  data-bs-toggle="tooltip"
                  data-bs-placement="bottom"
                  (click)="addMeasure()"
                  *ngIf="hasMeasureMultiple && !view"
                ></app-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<tour-step-template></tour-step-template>
