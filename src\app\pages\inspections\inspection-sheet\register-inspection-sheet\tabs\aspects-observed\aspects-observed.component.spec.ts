import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AspectsObservedComponent } from './aspects-observed.component';

describe('AspectsObservedComponent', () => {
  let component: AspectsObservedComponent;
  let fixture: ComponentFixture<AspectsObservedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AspectsObservedComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AspectsObservedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
