<div class="col-md-12">
  <form [formGroup]="formMohrColoumb">
    <div class="row">
      <label class="form-label" style="font-style: italic">Fórmula:</label>
      <div class="col-md-3">
        <img
          src="assets/images/static-materials/mohr-coulomb.png"
          class="img-fluid img-thumbnail"
          style="max-height: 80px; width: auto"
        />
      </div>
    </div>
    <div class="row mt-1">
      <!-- Coesão -->
      <div class="col-md-4">
        <label class="form-label">Co<PERSON><PERSON></label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="cohesion"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formMohrColoumb.get('cohesion'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formMohrColoumb.get('cohesion'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formMohrColoumb.get('cohesion').valid &&
            formMohrColoumb.get('cohesion').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Ângulo de atrito -->
      <div class="col-md-4">
        <label class="form-label">Ângulo de atrito</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="friction_angle"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formMohrColoumb.get('friction_angle'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formMohrColoumb.get('friction_angle'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">°</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formMohrColoumb.get('friction_angle').valid &&
            formMohrColoumb.get('friction_angle').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Resistência a tração (kPa) -->
      <div class="col-md-4">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_tensile_strength"
          (change)="
            formService.checkboxControlValidate(
              formMohrColoumb,
              'tensile_strength'
            )
          "
        />
        <label class="form-label">Resistência a tração</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="tensile_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formMohrColoumb.get('tensile_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formMohrColoumb.get('tensile_strength')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formMohrColoumb.get('tensile_strength').valid &&
            formMohrColoumb.get('tensile_strength').touched &&
            formMohrColoumb.get('is_tensile_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
  </form>
</div>
