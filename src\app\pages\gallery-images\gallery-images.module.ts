import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { SharedModule } from '@components/shared.module';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';

import { GalleryImagesRoutingModule } from './gallery-images-routing.module';
import { GalleryImagesComponent } from './gallery-images.component';

@NgModule({
  declarations: [GalleryImagesComponent],
  imports: [CommonModule, FormsModule, NgbModule, ReactiveFormsModule, SharedModule, NgMultiSelectDropDownModule.forRoot(), GalleryImagesRoutingModule]
})
export class GalleryImagesModule {}
