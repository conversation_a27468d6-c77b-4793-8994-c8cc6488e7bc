#!/usr/bin/env node

/**
 * Test Runner Script for Logisoil Playwright Tests
 * 
 * This script provides an easy way to run the Playwright tests with different configurations.
 * Usage: node run-tests.js [options]
 */

const { spawn } = require('child_process');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  headed: args.includes('--headed') || args.includes('-h'),
  ui: args.includes('--ui') || args.includes('-u'),
  debug: args.includes('--debug') || args.includes('-d'),
  help: args.includes('--help') || args.includes('--h'),
  browser: args.find(arg => arg.startsWith('--browser='))?.split('=')[1] || 'chromium'
};

// Help text
if (options.help) {
  console.log(`
Logisoil Playwright Test Runner

Usage: node run-tests.js [options]

Options:
  --headed, -h     Run tests in headed mode (browser visible)
  --ui, -u         Run tests in UI mode (interactive)
  --debug, -d      Run tests in debug mode
  --browser=NAME   Specify browser (chromium, firefox, webkit)
  --help           Show this help message

Examples:
  node run-tests.js --headed
  node run-tests.js --ui
  node run-tests.js --browser=firefox --headed
  node run-tests.js --debug

Note: Make sure the Angular application is running on http://localhost:4200 before running tests.
`);
  process.exit(0);
}

// Build the command
let command = 'npx';
let commandArgs = ['playwright', 'test'];

if (options.ui) {
  commandArgs.push('--ui');
} else {
  if (options.headed) {
    commandArgs.push('--headed');
  }
  
  if (options.debug) {
    commandArgs.push('--debug');
  }
  
  if (options.browser !== 'chromium') {
    commandArgs.push('--project', options.browser);
  }
}

// Add the test file
commandArgs.push('tests/login.spec.js');

console.log('🚀 Starting Playwright tests...');
console.log(`Command: ${command} ${commandArgs.join(' ')}`);
console.log('');

// Check if Angular app is running
console.log('📋 Pre-flight checks:');
console.log('   ✓ Make sure Angular app is running on http://localhost:4200');
console.log('   ✓ Make sure Playwright browsers are installed (npm run playwright:install)');
console.log('');

// Run the command
const testProcess = spawn(command, commandArgs, {
  stdio: 'inherit',
  shell: true,
  cwd: process.cwd()
});

testProcess.on('close', (code) => {
  if (code === 0) {
    console.log('');
    console.log('✅ Tests completed successfully!');
    console.log('📊 View the HTML report: npx playwright show-report');
  } else {
    console.log('');
    console.log('❌ Tests failed with exit code:', code);
    console.log('📊 View the HTML report: npx playwright show-report');
    console.log('📸 Check screenshots in: tests/screenshots/');
  }
});

testProcess.on('error', (error) => {
  console.error('❌ Error running tests:', error.message);
  process.exit(1);
});
