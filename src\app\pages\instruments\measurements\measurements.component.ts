import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { SecurityLevelsComponent } from '@pages/instruments/security-levels/security-levels.component';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-measurements',
  templateUrl: './measurements.component.html',
  styleUrls: ['./measurements.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class MeasurementsComponent implements OnInit, OnChanges {
  @ViewChild(SecurityLevelsComponent) securityLevels: SecurityLevelsComponent;

  @Input() public item: any = '';
  @Input() public index: any = '';

  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public editingBatch: boolean = false;

  @Input() public typeMeasure: any = '';
  @Input() public nameMeasure: any = '';

  @Output() public sendRemove = new EventEmitter();
  @Output() public sendIsReferencial = new EventEmitter();
  @Output() public sendCalcQuota = new EventEmitter();

  public formMeasure: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    identifier: new FormControl('', [Validators.required, Validators.maxLength(32)]),
    alternative_name: new FormControl(''),
    quota: new FormControl('', [Validators.required]), //Cota cel de pressao ou Cota ponto de medicao
    lithotype: new FormControl(''), //Inclinometros e Medidor de Recalque
    limit: new FormControl('', [Validators.required]),
    delta_ref: new FormControl({ value: '', disabled: true }),
    is_referencial: new FormControl(false),
    sl_measure_attention: new FormControl(''),
    sl_measure_alert: new FormControl(''),
    sl_measure_emergency: new FormControl(''),
    sl_measure_abrupt_variation_between_readings: new FormControl(''),
    elevation: new FormControl({ value: null, disabled: true }), //Inclinometros
    length: new FormControl({ value: null, disabled: true }), //Inclinometros
    active: new FormControl(true)
  });

  public formValid: boolean = false;
  public datasecurityLevels = null;

  public func = fn;

  public _current: any = {};

  constructor() {}

  ngOnInit(): void {}

  /**
   * Detecta mudanças nos dados de entrada e executa a divisão dos dados e validações necessárias.
   * @param {SimpleChanges} changes - As mudanças detectadas nos dados de entrada.
   */
  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
    this.setFormValidate();
  }

  //Remove o item atual e emite um evento para notificar o componente pai.
  removeMe() {
    this.sendRemove.emit({ item: this.item, index: this.index });
  }

  /**
   * Ativa ou desativa o estado ativo do item e recalcula a cota com base no valor da cota atual.
   * @param {boolean} $active - O estado ativo atual do item.
   */
  activeMe($active) {
    $active = !$active;
    this.toggleActive($active);
    this.formMeasure.controls['active'].setValue($active);
    this.calcQuota(this.formMeasure.controls['quota'].value);
  }

  /**
   * Alterna o estado ativo do item, habilitando ou desabilitando os controles do formulário conforme necessário.
   * @param {boolean} $active - O estado ativo atual do item.
   */
  toggleActive($active) {
    if ($active) {
      this.formMeasure.controls['alternative_name'].enable();
      this.formMeasure.controls['quota'].enable();
      this.formMeasure.controls['lithotype'].enable();
      this.formMeasure.controls['limit'].enable();
      this.formMeasure.controls['delta_ref'].enable();
      this.formMeasure.controls['is_referencial'].enable();
      this.formMeasure.controls['sl_measure_attention'].enable();
      this.formMeasure.controls['sl_measure_alert'].enable();
      this.formMeasure.controls['sl_measure_emergency'].enable();
      this.formMeasure.controls['sl_measure_abrupt_variation_between_readings'].enable();
    } else {
      this.formMeasure.controls['alternative_name'].disable();
      this.formMeasure.controls['quota'].disable();
      this.formMeasure.controls['lithotype'].disable();
      this.formMeasure.controls['limit'].disable();
      this.formMeasure.controls['delta_ref'].disable();
      this.formMeasure.controls['is_referencial'].disable();
      this.formMeasure.controls['sl_measure_attention'].disable();
      this.formMeasure.controls['sl_measure_alert'].disable();
      this.formMeasure.controls['sl_measure_emergency'].disable();
      this.formMeasure.controls['sl_measure_abrupt_variation_between_readings'].disable();
    }
  }

  //Define se o item é referencial e emite um evento para notificar o componente pai.
  setIsReferencial() {
    if (this.formMeasure.controls['is_referencial'].value) {
      // this.formMeasure.controls['delta_ref'].disable();
      // this.formMeasure.controls['delta_ref'].setErrors(null);
      // this.formMeasure.controls['delta_ref'].clearValidators();
    } else {
      // this.formMeasure.controls['delta_ref'].enable();
      // this.formMeasure.controls['delta_ref'].setValidators([Validators.required]);
      // this.formMeasure.controls['delta_ref'].updateValueAndValidity();
    }
    this.sendIsReferencial.emit({ item: this.item, index: this.index, is_referencial: this.formMeasure.controls['is_referencial'].value });
  }

  //Configura as validações do formulário com base no tipo de medição.
  setFormValidate() {
    switch (this.typeMeasure) {
      case 'pressure_cell':
        this.formMeasure.controls['lithotype'].disable();
        this.formMeasure.controls['lithotype'].setErrors(null);
        this.formMeasure.controls['lithotype'].clearValidators();

        this.formMeasure.controls['limit'].disable();
        this.formMeasure.controls['limit'].setErrors(null);
        this.formMeasure.controls['limit'].clearValidators();

        this.formMeasure.controls['delta_ref'].disable();
        this.formMeasure.controls['delta_ref'].setErrors(null);
        this.formMeasure.controls['delta_ref'].clearValidators();

        this.formMeasure.controls['is_referencial'].disable();
        this.formMeasure.controls['is_referencial'].setErrors(null);
        this.formMeasure.controls['is_referencial'].clearValidators();
        break;
      case 'measure_point':
        this.formMeasure.controls['limit'].disable();
        this.formMeasure.controls['limit'].setErrors(null);
        this.formMeasure.controls['limit'].clearValidators();

        this.formMeasure.controls['delta_ref'].disable();
        this.formMeasure.controls['delta_ref'].setErrors(null);
        this.formMeasure.controls['delta_ref'].clearValidators();

        this.formMeasure.controls['is_referencial'].disable();
        this.formMeasure.controls['is_referencial'].setErrors(null);
        this.formMeasure.controls['is_referencial'].clearValidators();
        break;
    }
  }

  /**
   * Divide os dados recebidos e os atribui aos controles do formulário.
   * @param {any} $dados - Os dados a serem divididos e atribuídos.
   */
  splitData($dados) {
    this.formMeasure.controls['id'].setValue($dados.id);
    this.formMeasure.controls['identifier'].setValue($dados.identifier);
    this.formMeasure.controls['alternative_name'].setValue($dados.alternative_name);
    this.formMeasure.controls['quota'].setValue(fn.fixed($dados.quota, this.view));
    this.formMeasure.controls['lithotype'].setValue($dados.lithotype);
    this.formMeasure.controls['limit'].setValue(fn.fixed($dados.limit, this.view));
    this.formMeasure.controls['delta_ref'].setValue(fn.fixed($dados.delta_ref, this.view));
    this.formMeasure.controls['is_referencial'].setValue($dados.is_referencial);
    // this.formMeasure.controls['elevation'].setValue(fn.fixed($dados.elevation, this.view));
    this.formMeasure.controls['length'].setValue(fn.fixed($dados.length, this.view));
    this.formMeasure.controls['active'].setValue($dados.active);

    if (this.typeMeasure == 'measure_point') {
      this.formMeasure.controls['elevation'].setValue(fn.fixed($dados.quota, this.view));
      this.formMeasure.controls['quota'].setValue(fn.fixed($dados.depth, this.view));
    }

    this.toggleActive($dados.active);

    if ($dados.is_referencial) {
      this.formMeasure.controls['delta_ref'].disable();
    }

    if ($dados.hasOwnProperty('_current')) {
      this._current['id'] = $dados.id != $dados._current.id ? $dados._current.id : null;
      this._current['identifier'] = $dados.identifier != $dados._current.identifier ? $dados._current.identifier : null;
      this._current['alternative_name'] = $dados.alternative_name != $dados._current.alternative_name ? $dados._current.alternative_name : null;
      this._current['quota'] = $dados.quota != $dados._current.quota ? $dados._current.quota : null;
      this._current['lithotype'] = $dados.lithotype != $dados._current.lithotype ? $dados._current.lithotype : null;
      this._current['limit'] = $dados.limit != $dados._current.limit ? $dados._current.limit : null;
      this._current['delta_ref'] = $dados.delta_ref != $dados._current.delta_ref ? $dados._current.delta_ref : null;
      this._current['is_referencial'] = $dados.is_referencial != $dados._current.is_referencial ? $dados._current.is_referencial : null;
      // this._current['elevation'] = $dados.elevation != $dados._current.elevation ? $dados._current.elevation : null;
      this._current['length'] = $dados.length != $dados._current.length ? $dados._current.length : null;
      this._current['active'] = $dados.active != $dados._current.active ? $dados._current.active : null;

      //Modificação: elevation => quota e quota => depth
      if (this.typeMeasure == 'measure_point') {
        this._current['elevation'] = $dados.quota != $dados._current.quota ? $dados._current.quota : null;
        this._current['quota'] = $dados.depth != $dados._current.depth ? $dados._current.depth : null;
      }

      if ($dados.security_levels !== null || $dados._current.security_levels !== null) {
        $dados.security_levels['_current'] = $dados._current.security_levels;
      }
    }

    this.datasecurityLevels = $dados.security_levels;

    if (this.edit || this.editingBatch) {
      this.formMeasure.controls['identifier'].disable();
    } else if (this.view) {
      this.formMeasure.disable();
    }
  }

  /**
   * Calcula a cota com base no tipo de medição e emite um evento para notificar o componente pai.
   * @param {any} $event - O valor da cota atual.
   */
  calcQuota($event) {
    if (this.typeMeasure == 'measure_point' || this.typeMeasure == 'magnetic_ring' || this.typeMeasure == 'pressure_cell') {
      this.sendCalcQuota.emit({ item: this.item, index: this.index, value: $event, type: this.typeMeasure, component: 'measure' });
    }
  }
}
