<div class="list-content">
  <form [formGroup]="formActionPlan">
    <div class="row" *ngIf="structureId != ''">
      <div class="col-md-12 mt-2">
        <div
          *ngIf="view"
          class="alert alert-warning d-flex align-items-center mt-3"
          role="alert"
        >
          <i class="fa fa-eye fa-lg text-warning me-2"></i>
          <div><strong>Plano de Ação Concluído</strong></div>
        </div>

        <strong *ngIf="!view">Adicionar novo plano de ação</strong>
        <br />
        <span class="descricao mt-2 mb-3" *ngIf="!view">
          <i class="fa fa-exclamation-circle me-2"></i>
          Preencha o formulário e envie o plano de ação. Após o envio bem
          sucedido, será habilitado o anexo de arquivos.
        </span>
      </div>

      <!-- Ocorrência -->
      <div class="col-md-12">
        <label class="form-label">Ocorrência</label>
        <textarea
          pInputTextArea
          rows="4"
          class="form-control"
          formControlName="occurrence_description"
        >
        </textarea>
      </div>

      <!-- Origem e Área -->
      <div class="col-md-6 mt-3">
        <label class="form-label">Origem</label>
        <select class="form-select" formControlName="inspection_sheet_type">
          <option value=""></option>
          <option *ngFor="let type of inspectionSheetType" [value]="type.value">
            {{ type.label }}
          </option>
        </select>
      </div>

      <div class="col-md-6 mt-3">
        <label class="form-label">Área</label>
        <select class="form-select" formControlName="area">
          <option value="">Selecione</option>
          <option *ngFor="let area of areaList" [value]="area.value">
            {{ area.label }}
          </option>
        </select>
        <small
          class="form-text text-danger"
          *ngIf="
            !formActionPlan.get('area').valid &&
            formActionPlan.get('area').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <!-- Recomendação -->
      <div class="col-md-12 mt-3">
        <label class="form-label">Recomendação (Opcional)</label>
        <textarea
          class="form-control"
          formControlName="recommendation"
          [attr.maxlength]="32"
          autocomplete="off"
          rows="3"
          (input)="onValueChange($event, 'recommendation')"
        ></textarea>

        <!-- Contador de caracteres -->
        <small class="form-text text-muted d-block"
          >Caracteres {{ charCounts['recommendation'] || 0 }} de 512
        </small>
      </div>

      <!-- Prazo e Status -->
      <div class="col-md-6 mt-3">
        <label class="form-label">Prazo</label>
        <input
          type="date"
          class="form-control"
          formControlName="expiration_date"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formActionPlan.get('expiration_date').valid &&
            formActionPlan.get('expiration_date').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div class="col-md-6 mt-3">
        <label class="form-label">Status</label>
        <select class="form-select" formControlName="status">
          <option value="">Selecione</option>
          <option *ngFor="let status of statusList" [value]="status.value">
            {{ status.label }}
          </option>
        </select>
        <small
          class="form-text text-danger"
          *ngIf="
            !formActionPlan.get('status').valid &&
            formActionPlan.get('status').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <!-- GUT: Gravidade, Urgência, Tendência -->
      <div class="col-md-4 mt-3">
        <label class="form-label">Gravidade</label>
        <select class="form-select" [disabled]="view">
          <option *ngFor="let item of severityList" [value]="item.value">
            {{ item.label }}
          </option>
        </select>
      </div>

      <div class="col-md-4 mt-3">
        <label class="form-label">Urgência</label>
        <select class="form-select" [disabled]="view">
          <option *ngFor="let item of urgencyList" [value]="item.value">
            {{ item.label }}
          </option>
        </select>
      </div>

      <div class="col-md-4 mt-3">
        <label class="form-label">Tendência</label>
        <select class="form-select" [disabled]="view">
          <option *ngFor="let item of tendencyList" [value]="item.value">
            {{ item.label }}
          </option>
        </select>
      </div>

      <!-- Botão Salvar -->
      <div class="col-md-12 text-end mt-4 mb-3" *ngIf="!view">
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-floppy-disk'"
          [label]="'Salvar'"
          [type]="false"
          (click)="onSave()"
          [disabled]="formActionPlan.invalid"
        ></app-button>
      </div>
    </div>
  </form>

  <!-- Anexos -->
  <div class="row mt-3" *ngIf="showAttachmentsSection">
    <div class="col-md-12">
      <h5><strong>Anexar novo arquivo</strong></h5>

      <div class="d-flex align-items-center gap-2 my-3" *ngIf="!view">
        <input
          type="file"
          class="form-control w-auto"
          #fileInput
          id="fileInput"
          multiple
          (change)="onFilesSelected($event)"
        />

        <app-button
          [class]="'btn-logisoil-blue'"
          [label]="'Anexar'"
          (click)="sendAllAttachments()"
          [disabled]="selectedFiles.length === 0"
        ></app-button>

        <span *ngIf="selectedFiles.length > 0" class="text-muted small">
          Arquivos selecionados:
          <span *ngFor="let file of selectedFiles; let isLast = last">
            {{ file.name }}<span *ngIf="!isLast">, </span>
          </span>
        </span>
      </div>

      <!-- Alerta -->
      <div
        *ngIf="message.status"
        class="alert d-flex align-items-center mt-3"
        [ngClass]="message.class"
        role="alert"
      >
        <i class="fa fa-info-circle me-2"></i>
        {{ message.text }}
      </div>

      <!-- Resultado dos anexos -->
      <div class="mt-4">
        <!-- Caso não tenha anexos -->
        <div
          *ngIf="attachments.length === 0"
          class="alert alert-warning d-flex align-items-center gap-2"
          role="alert"
        >
          <i class="fa fa-folder-open fa-lg text-warning"></i>
          <span>Nenhum arquivo anexado.</span>
        </div>

        <!-- Tabela de anexos se houver -->
        <table class="table table-bordered" *ngIf="attachments.length > 0">
          <thead>
            <tr>
              <th>ID</th>
              <th>Arquivo</th>
              <th>Download</th>
              <th>Excluir</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let file of attachments; let i = index">
              <td>{{ i + 1 }}</td>
              <td>{{ file.name }}</td>
              <td>
                <a [href]="file.base64" [download]="file.name" target="_blank">
                  <i class="fa fa-download text-primary pointer"></i>
                </a>
              </td>
              <td>
                <app-button
                  [class]="'btn-logisoil-red btn-sm me-2'"
                  [icon]="'fa fa-trash'"
                  [disabled]="view"
                  [title]="
                    view
                      ? 'Plano de ação concluído. Anexo não pode ser removido.'
                      : ''
                  "
                  (click)="openModalDeleteAttachment(file.id, file.name)"
                ></app-button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Histórico -->
      <div class="col-md-12 mt-5">
        <h5><strong>Plano de ação - histórico</strong></h5>

        <!-- Mensagem caso não tenha histórico -->
        <div
          *ngIf="tableData.length === 0 && message.status"
          class="alert alert-warning d-flex align-items-center gap-2 mt-3"
          role="alert"
        >
          <i class="fa fa-folder-open fa-lg text-warning"></i>
          <span>{{ message.text }}</span>
        </div>

        <!-- Tabela de histórico, se houver dados -->
        <ng-container *ngIf="tableData.length > 0">
          <app-table
            [tableHeader]="tableHeader"
            [tableData]="tableData"
          ></app-table>

          <!-- Paginação -->
          <app-paginator
            [collectionSize]="collectionSize"
            [page]="page"
            [maxSize]="10"
            [boundaryLinks]="true"
            [pageSize]="pageSize"
            (sendPageChange)="loadPage($event)"
          ></app-paginator>
        </ng-container>
      </div>
    </div>

    <!-- Voltar -->
    <div class="mt-3 mb-3 col-md-12 d-flex justify-content-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [routerLink]="['/inspections']"
        [queryParams]="{ tab: 'planosDeAcao' }"
      ></app-button>
    </div>
  </div>
</div>

<app-modal-confirm
  #modalConfirmAttachment
  (sendClickEvent)="confirmDeleteAttachment($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
  [data]="modalData"
></app-modal-confirm>
