import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { MessageInputInvalid } from 'src/app/constants/message.constants';
import { ChartLine, XAxisRotate, YAxisLabel, Markers, PeriodsInaPz } from 'src/app/constants/chart.constants';
import { PeriodsAbs } from 'src/app/constants/instruments.constants';

import { ChartService as ChartServiceApi } from 'src/app/services/api/chart.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { FileLoaderService } from 'src/app/services/file-loader.service';
import { FormService } from 'src/app/services/form.service';

import fn from 'src/app/utils/function.utils';
import * as _ from 'lodash';
import * as moment from 'moment';

@Component({
  selector: 'app-chart-ina-pz',
  templateUrl: './chart-ina-pz.component.html',
  styleUrls: ['./chart-ina-pz.component.scss']
})
export class ChartInaPzComponent implements OnInit {
  public formChart: FormGroup = new FormGroup({
    line_type: new FormControl('solid'),
    line_stroke: new FormControl(2),
    //Leituras
    marker: new FormControl('circle'),
    marker_length: new FormControl(1),
    //Leituras secas
    dry_marker: new FormControl('x'),
    dry_marker_length: new FormControl(1),
    color0: new FormControl('#000000'),
    //Somente marcadores
    only_markers: new FormControl(false),
    // Intervalo eixo X
    xAxis_interval: new FormControl(1),
    xAxis_rotate: new FormControl(45),
    chart_height: new FormControl(300),
    // Intervalo eixo Y
    yAxis0_interval: new FormControl(),
    yAxis0_min: new FormControl(null),
    yAxis0_max: new FormControl(null),
    // Intervalo eixo Y NA Reservatorio
    yAxis1_interval: new FormControl({ value: null, disabled: true }),
    yAxis1_min: new FormControl(null),
    yAxis1_max: new FormControl(null),
    marker1: new FormControl('rectangle'),
    marker1_length: new FormControl(1),
    color1: new FormControl('#121FE6'),
    only1_markers: new FormControl(false),
    instrument_upstream: new FormControl([]), // montante
    instrument_downstream: new FormControl([]), //jusante
    // Intervalo eixo Y Pluviometria
    instrument_climate: new FormControl([]),
    yAxis2_interval: new FormControl({ value: null, disabled: true }),
    yAxis2_min: new FormControl(null),
    yAxis2_max: new FormControl(null),
    marker2: new FormControl('triangle'),
    marker2_length: new FormControl(1),
    color2: new FormControl('#13EED2'),
    only2_markers: new FormControl(false),
    graphic2_type: new FormControl('line'),

    instrument: new FormControl([]),
    period: new FormControl(4, [Validators.required])
  });

  public chart: any = {};
  public chartLineSetting = ChartLine;
  public xAxisRotate = XAxisRotate;
  public yAxisLabel = YAxisLabel;
  public markers = Markers;
  public periods = PeriodsInaPz;
  public periodsAbs = PeriodsAbs;

  public showColorPicker: boolean[] = [false, false, false];
  public selectedColor: string[] = ['#000000', '#121FE6', '#13EED2'];

  public ctrlConfiguration: boolean = false;
  public controls: any = null;

  public instruments: any = [];
  public instrumentsSettings = MultiSelectDefault.Instruments;
  public linimetricSettings = _.cloneDeep(MultiSelectDefault.Instruments);
  public climateSettings = _.cloneDeep(MultiSelectDefault.Instruments);
  public periodSettings: any = MultiSelectDefault.Single;

  public instrument_upstream: any = [];
  public instrument_downstream: any = [];
  public instrument_climate: any = [];

  public xAxis: any = [];
  public yAxis: any = [];

  public chartSeries: any = [];
  public chartSeriesType: string = 'line';
  public chartLegends: any = [];

  public min: number = null;
  public max: number = null;

  public messageReturn: any = { text: '', status: false };

  public func = fn;

  constructor(
    private activatedRoute: ActivatedRoute,
    private chartServiceApi: ChartServiceApi,
    private fileLoaderService: FileLoaderService,
    private formService: FormService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private router: Router
  ) {}

  /**
   * Método de inicialização do componente.
   * Configura os controles, carrega marcadores, e obtém os instrumentos, réguas linimétricas e instrumentos climáticos.
   */
  ngOnInit(): void {
    this.controls = this.formChart.controls;
    this.linimetricSettings.singleSelection = true;
    this.climateSettings.singleSelection = true;

    this.loadMarkers();
    this.getInstruments();
    this.getLinimetricRulers();
    this.getClimateInstruments();

    this.formService.monitorAllFormControls(this.formChart, this.onFormControlChange.bind(this));
  }

  ngOnDestroy() {
    // Cancela as assinaturas ao destruir o componente
    this.formService.formDestroySubscribe();
  }

  /**
   * Método chamado quando um controle é alterado.
   * @param {string} controlName - Nome (ou caminho) do controle alterado.
   * @param {any} value - Novo valor do controle.
   */
  onFormControlChange(controlName: string, value: any): void {
    if (controlName === 'period') {
      if (value !== '') {
        this.getChart();
      } else {
        this.resetConfigurations();
      }
    }
    if (controlName === 'instrument' && value.length > 0 && this.formChart.get('period').value != '') {
      this.countTypesInstruments(this.controls['instrument'].value, this.instruments);
      this.getChart();
    }
  }

  countTypesInstruments(array1, array2) {
    const typeCounts = { INA: 0, PZ: 0 };

    // Mapear os objetos do segundo array para otimizar a busca
    const map = new Map(array2.map((item) => [item.id, item.type]));

    // Contar os tipos com base nos IDs do primeiro array
    for (const item of array1) {
      const type = map.get(item.id);
      if (type === 1) typeCounts.INA++;
      else if (type === 2) typeCounts.PZ++;
      else if (type === 3) typeCounts.PZ++;
    }

    if (typeCounts.INA == 0 && typeCounts.PZ > 0) {
      this.yAxisLabel[0].label = 'Cota Piezométrica (m)';
    } else if (typeCounts.INA > 0 && typeCounts.PZ == 0) {
      this.yAxisLabel[0].label = 'Cota NA (m)';
    } else if (typeCounts.INA > 0 && typeCounts.PZ > 0) {
      this.yAxisLabel[0].label = 'Cota (m)';
    }
  }

  //Obtém a lista de instrumentos com base na estrutura selecionada e no tipo especificado.
  getInstruments() {
    let params = { StructureId: this.activatedRoute.snapshot.queryParams.structure, Type: [1, 2, 3] };
    this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
      let dados: any = resp;

      if (dados.status == 200) {
        dados = dados.body === undefined ? dados : dados.body;
        this.instruments = dados;
        let instrumentInfo = fn.findIndexInArrayofObject(this.instruments, 'id', this.activatedRoute.snapshot.params.instrumentId, 'identifier', true);
        this.controls['instrument'].setValue([instrumentInfo]);

        if (this.activatedRoute.snapshot.queryParams && this.activatedRoute.snapshot.queryParams.period) {
          this.callChart(this.activatedRoute.snapshot.queryParams.period);
        }
      }
    });
  }

  //Obtém a lista de réguas linimétricas associadas à estrutura selecionada.
  getLinimetricRulers() {
    let params = { StructureId: this.activatedRoute.snapshot.queryParams.structure, Type: 10 };
    this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
      let dados: any = resp;
      if (dados.status == 200) {
        dados = dados.body === undefined ? dados : dados.body;
        this.getPositionInstrument(dados);
      }
    });
  }

  /**
   * Define a posição dos instrumentos de acordo com os dados recebidos.
   * @param {any} dados - Dados dos instrumentos.
   */
  getPositionInstrument(dados) {
    let selectedInstrument = this.activatedRoute.snapshot.params.instrumentId;
    dados.forEach((instrument, index) => {
      this.instrumentsServiceApi.getInstrumentsById(instrument.id).subscribe((resp) => {
        let dadosInstrument: any = resp;
        dadosInstrument = dadosInstrument.body === undefined ? dadosInstrument : dadosInstrument.body;
        let obj = {
          id: dadosInstrument.id,
          identifier: dadosInstrument.identifier,
          type: dadosInstrument.type
        };

        //Montante
        if (dadosInstrument.linimetric_ruler_position === 1) {
          if (obj.id == selectedInstrument) {
            this.controls['instrument_upstream'].setValue([obj]);
          }
          this.instrument_upstream.push(obj);
        }
        //Jusante
        if (dadosInstrument.linimetric_ruler_position === 2) {
          if (obj.id == selectedInstrument) {
            this.controls['instrument_downstream'].setValue([obj]);
          }
          this.instrument_downstream.push(obj);
        }
      });
    });
  }

  //Obtém a lista de instrumentos climáticos (pluviometria) associados à estrutura selecionada.
  getClimateInstruments() {
    let params = { StructureId: this.activatedRoute.snapshot.queryParams.structure, Type: [12, 13] };
    this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
      let dados: any = resp;

      if (dados.status == 200) {
        dados = dados.body === undefined ? dados : dados.body;
        this.instrument_climate = dados;
      }
    });
  }

  /**
   * Chama o gráfico com base no período selecionado.
   * @param {string} find - Período selecionado.
   */
  callChart(find) {
    let paramsPerid = fn.compareConst(find, this.periodsAbs, this.periods, 'value', 'label');
    if (paramsPerid !== undefined) {
      this.controls['period'].setValue(paramsPerid.value);
      this.getChart();
    }
  }

  /**
   * Obtém os dados do gráfico de percolação com base nos instrumentos selecionados.
   * @param {string|null} id - ID opcional para o gráfico.
   */
  getChart(id = null) {
    this.resetConfigurations();
    this.messageReturn.text = '';
    this.messageReturn.status = false;

    let params = [];
    let queryParams = {};

    params = this.controls['instrument'].value.map((objeto) => objeto.id);

    if (this.controls['instrument_upstream'].value.length > 0) {
      params.push(this.controls['instrument_upstream'].value[0].id);
    }

    if (this.controls['instrument_downstream'].value.length > 0) {
      params.push(this.controls['instrument_downstream'].value[0].id);
    }

    if (this.controls['instrument_climate'].value.length > 0) {
      params.push(this.controls['instrument_climate'].value[0].id);
    }

    queryParams['period'] = this.controls['period'].value;

    this.chartServiceApi.postChartPercolation(params, queryParams).subscribe((resp) => {
      let dados: any = resp;

      if (dados) {
        dados = dados.body === undefined ? dados : dados.body;
        this.formatData(dados);
      } else {
        this.messageReturn.text = MessageInputInvalid.NoChart;
        this.messageReturn.status = true;

        setTimeout(() => {
          this.messageReturn.status = false;
        }, 4000);
      }
    });
  }

  //Constrói o gráfico a partir dos dados fornecidos.
  constructChart(data) {
    this.constructXAxis(data);
  }

  //Constrói o eixo X do gráfico com base nas datas dos dados fornecidos.
  constructXAxis(data) {
    let dates = data.instruments.map((item) => moment(item.date).format('DD/MM/YYYY'));
    this.xAxis.push(...dates);

    dates = data.water_level_reservoir.map((item) => moment(item.date).format('DD/MM/YYYY'));
    this.xAxis.push(...dates);

    dates = data.pluviometry.map((item) => moment(item.date).format('DD/MM/YYYY'));
    this.xAxis.push(...dates);

    const datasOrdenadas = this.xAxis
      .map((data) => {
        const [dia, mes, ano] = data.split('/').map(Number);
        return new Date(ano, mes - 1, dia);
      })
      .sort((a, b) => a - b)
      .map((data) => data.toLocaleDateString('pt-BR'));

    this.xAxis = datasOrdenadas;
    this.xAxis = this.uniqueArray(this.xAxis);

    if (!this.controls['xAxis_interval'].touched) {
      this.controls['xAxis_interval'].setValue(Math.floor(this.xAxis.length / 35));
    }

    this.constructSeries(data);
  }

  //Constrói as séries de dados do gráfico.
  constructSeries(data) {
    const datesObject = {};

    for (const date of this.xAxis) {
      datesObject[date] = null;
    }

    let series = {};

    series['NA Reservatório (m)'] = { ...datesObject };
    series['Pluviometria (mm)'] = { ...datesObject };

    const listInstruments = [...new Set(data.instruments.map((instrument) => instrument.identifier))];

    listInstruments.forEach((instrument) => {
      series[instrument + ''] = { ...datesObject };
      series[instrument + ' - CT'] = { ...datesObject };
      series[instrument + ' - CB'] = { ...datesObject };
    });

    data.instruments.forEach((element) => {
      let date = moment(element.date).format('DD/MM/YYYY');
      const quota = element.quota;
      const top_quota = element.top_quota;
      const base_quota = element.base_quota;
      if (element.dry) {
        let markerDry = fn.findIndexInArrayofObject(this.markers, 'marker', this.controls['dry_marker'].value, 'marker', true);
        let config = {};
        config['value'] =
          series.hasOwnProperty(element.identifier) && series[element.identifier].hasOwnProperty(date)
            ? typeof quota == 'string'
              ? parseFloat(quota)
              : quota
            : null;
        config['symbol'] = `image://${this.getSvgWithReplacedValue(markerDry.text, this.controls['color0'].value)}`;
        config['symbolSize'] = parseInt(this.controls['dry_marker_length'].value) * 120;
        series[element.identifier][date] = config;
      } else {
        series[element.identifier][date] =
          series.hasOwnProperty(element.identifier) && series[element.identifier].hasOwnProperty(date)
            ? typeof quota == 'string'
              ? parseFloat(quota)
              : quota
            : null;
      }

      series[element.identifier + ' - CT'][date] =
        series.hasOwnProperty(element.identifier) && series[element.identifier].hasOwnProperty(date)
          ? typeof top_quota == 'string'
            ? parseFloat(top_quota)
            : top_quota
          : null;
      series[element.identifier + ' - CB'][date] =
        series.hasOwnProperty(element.identifier) && series[element.identifier].hasOwnProperty(date)
          ? typeof base_quota == 'string'
            ? parseFloat(base_quota)
            : base_quota
          : null;
    });

    data.water_level_reservoir.forEach((element) => {
      let date = moment(element.date).format('DD/MM/YYYY');
      const quota = element.quota;

      series['NA Reservatório (m)'][date] = series['NA Reservatório (m)'].hasOwnProperty(date) ? (typeof quota == 'string' ? parseFloat(quota) : quota) : null;
    });

    data.pluviometry.forEach((element) => {
      let date = moment(element.date).format('DD/MM/YYYY');
      const pluviometry = element.pluviometry;

      series['Pluviometria (mm)'][date] = series['Pluviometria (mm)'].hasOwnProperty(date)
        ? typeof pluviometry == 'string'
          ? parseFloat(pluviometry)
          : pluviometry
        : null;
    });

    this.chartSeries = [];
    this.chartLegends = [];

    for (const key in series) {
      let item = fn.findIndexInArrayofObject(this.yAxisLabel, 'label', key, 'id', true);
      let idx = item === '' ? 0 : item.id;
      if ((item.show && idx > 0) || idx == 0) {
        //Marcador, cor e tamanho
        let typeIcon = idx > 0 ? this.controls['marker' + idx].value : this.controls['marker'].value;
        let colorIcon = idx > 0 ? this.controls['color' + idx].value : this.randomHexColor();
        let lengthIcon = idx > 0 ? this.controls['marker' + idx + '_length'].value : this.controls['marker_length'].value;
        let marker = fn.findIndexInArrayofObject(this.markers, 'marker', typeIcon, 'marker', true);

        const itemSeries = {
          name: key,
          type: idx === 2 ? this.controls['graphic' + idx + '_type'].value : this.chartSeriesType,
          data: Object.values(series[key]),
          lineStyle: {
            width: this.controls['line_stroke'].value,
            type: this.controls['line_type'].value,
            opacity: idx > 0 ? (this.controls['only' + idx + '_markers'].value ? 0 : 1) : this.controls['only_markers'].value ? 0 : 1
          },
          yAxisIndex: idx,
          barWidth: 20,
          itemStyle: {
            color: colorIcon
          },
          showSymbol: true,
          symbol: `image://${this.getSvgWithReplacedValue(marker.text, colorIcon)}`,
          symbolSize: parseInt(lengthIcon) * 120,
          connectNulls: true
        };
        this.chartSeries.push(itemSeries);
        this.chartLegends.push(idx === 0 ? key : item.label);
        this.defineMinMax(itemSeries.data, itemSeries.yAxisIndex);
      }
    }
    this.constructYAxis();
  }

  //Prepara os dados para os eixos Y do gráfico.
  constructYAxis() {
    this.yAxis = [];
    this.yAxisLabel.forEach((item) => {
      if (item.label != 'Cota topo (m)' && item.label != 'Cota base (m)') {
        let itemYAxis = {
          name: item.label,
          type: 'value',
          position: item.position,
          offset: item.offset,
          axisLine: {
            show: true
          },
          nameRotate: item.nameRotate,
          nameLocation: item.nameLocation,
          nameGap: item.nameGap,
          nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
          alignTicks: true,
          axisLabel: {
            formatter: function (value, index) {
              return value.toFixed(1);
            }
          },
          show: item.show
        };
        if (item.id == 0) {
          itemYAxis['interval'] = this.controls[`yAxis${item.id}_interval`].value;
        }

        itemYAxis['min'] = this.controls[`yAxis${item.id}_min`].value;
        itemYAxis['max'] = this.controls[`yAxis${item.id}_max`].value;

        if (fn.isEmpty(this.controls[`yAxis${item.id}_interval`].value)) {
          itemYAxis['interval'] = Math.ceil((itemYAxis['max'] - itemYAxis['min']) / 20);
          this.controls[`yAxis${item.id}_interval`].setValue(itemYAxis['interval']);
        } else {
          itemYAxis['interval'] = this.controls[`yAxis${item.id}_interval`].value;
        }

        this.yAxis.push(itemYAxis);
      }
    });

    this.generateChart();
  }

  /**
   * Define os valores mínimos e máximos dos dados da série para o eixo Y.
   * @param {any} array - Dados da série.
   * @param {number} index - Índice do eixo Y.
   */
  defineMinMax(array: any, index) {
    array = array.filter((item) => item !== null);

    array = array.map((item) => {
      if (item != null) {
        return typeof item == 'number' ? item : item.value;
      }
    });

    const min = Math.min(...array);
    const max = Math.max(...array);
    let previous = min - (min % 10);
    let next = max + (10 - (max % 10));

    if (index == 0) {
      this.min = this.min == null ? previous : this.min;
      this.min = Math.min(this.min, previous);
      previous = this.min;

      this.max = this.max == null ? next : this.max;
      this.max = Math.max(this.max, next);
      next = this.max;
    }

    // const touchedMin = this.controls[`yAxis${index}_min`].touched;
    // const touchedMax = this.controls[`yAxis${index}_max`].touched;

    if (!this.controls[`yAxis${index}_min`].touched) {
      this.controls[`yAxis${index}_min`].setValue(previous);
    }

    if (!this.controls[`yAxis${index}_max`].touched) {
      this.controls[`yAxis${index}_max`].setValue(next);
    }
  }

  /**
   * Executa ações quando um clique é detectado fora de um elemento específico.
   * @param {string} element - Elemento que detectou o clique fora.
   * @param {number} index - Índice do elemento.
   */
  onClickedOutside(element: string, index = 0) {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker[index] = false;
        break;
    }
  }

  /**
   * Atualiza a cor selecionada após a conclusão da seleção de cor.
   * @param {any} $event - Evento de seleção de cor.
   * @param {number} index - Índice do controle de cor.
   */
  changeComplete($event, index = 0) {
    this.selectedColor[index] = $event.color.hex;
    this.controls[`color${index}`].setValue(this.selectedColor[index]);
  }

  //Limpa o formulário das configurações do gráfico.
  resetConfigurations() {
    this.xAxis = [];
    this.yAxis = [];

    this.chart = {};
    this.chartSeries = [];
    this.chartSeriesType = 'line';
    this.chartLegends = [];

    this.ctrlConfiguration = false;

    this.min = null;
    this.max = null;
  }

  //Define a altura do gráfico.
  setHeight(height: string): void {
    this.controls['chart_height'].setValue(parseInt(height));
    this.generateChart();
  }

  /**
   * Remove elementos repetidos de um array.
   * @param {any[]} array - Array de elementos.
   * @returns {any[]} - Array com elementos únicos.
   */
  uniqueArray(array) {
    const uniqueArray = [];
    const seeDates = {};

    for (const date of array) {
      if (!seeDates[date]) {
        uniqueArray.push(date);
        seeDates[date] = true;
      }
    }

    return uniqueArray;
  }

  //Carrega os marcadores para o gráfico a partir dos arquivos SVG.
  loadMarkers() {
    this.markers.forEach((marker, index) => {
      this.loadFileContent('assets/markers/', marker.icon, index);
    });
  }

  /**
   * Carrega o conteúdo de um arquivo a partir de um caminho específico.
   * @param {string} path - Caminho para o arquivo.
   * @param {string} fileName - Nome do arquivo.
   * @param {number} index - Índice do marcador.
   */
  loadFileContent(path: string, fileName: string, index = 0) {
    this.fileLoaderService.loadFile(path, fileName).subscribe(
      (data) => {
        this.markers[index].text = data;
      },
      (error) => {
        console.error('Erro ao carregar o arquivo:', error);
      }
    );
  }

  /**
   * Converte o arquivo SVG para texto e substitui suas cores.
   * @param {string} svg - Conteúdo SVG.
   * @param {string} color - Cor para substituição.
   * @returns {string} - SVG convertido para base64.
   */
  getSvgWithReplacedValue(svg, color = '#000000') {
    svg = this.replaceMultipleOccurrences(svg, ['rgb(0,0,0)', 'rgb(101,101,101)'], [color, color]);
    const svgBase64 = btoa(svg);
    return `data:image/svg+xml;base64,${svgBase64}`;
  }

  /**
   * Substitui todas as ocorrências de uma string por outra em um texto.
   * @param {string} text - Texto original.
   * @param {string[]} oldValues - Array de valores a serem substituídos.
   * @param {string[]} newValues - Array de novos valores para substituição.
   * @returns {string} - Texto com as substituições aplicadas.
   */
  replaceMultipleOccurrences(text, oldValues, newValues) {
    if (oldValues.length !== newValues.length) {
      throw new Error('Os arrays devem ter o mesmo comprimento.');
    }

    let newText = text;
    for (let i = 0; i < oldValues.length; i++) {
      const oldValue = oldValues[i];
      const newValue = newValues[i];
      newText = newText.split(oldValue).join(newValue);
    }

    return newText;
  }

  /**
   * Gera uma cor hexadecimal aleatória.
   * @returns {string} - Cor hexadecimal.
   */
  randomHexColor() {
    const randomColorComponent = () => {
      const component = Math.floor(Math.random() * 256); //Valor aleatorio entre 0  e 255
      return component.toString(16).padStart(2, '0'); //Converte para hexadecimal e completa com zero se necessario
    };

    const r = randomColorComponent();
    const g = randomColorComponent();
    const b = randomColorComponent();

    return `#${r}${g}${b}`;
  }

  //Formata os dados recebidos e os prepara para a construção do gráfico.
  formatData($dados) {
    let dados: any = {
      instruments: [],
      water_level_reservoir: [],
      pluviometry: []
    };

    $dados.percolation_instruments.forEach((instrument, index) => {
      instrument.readings.forEach((reading, index) => {
        dados.instruments.push({
          identifier: instrument.instrument_identifier,
          quota: reading.quota,
          top_quota: reading.top_quota,
          base_quota: reading.base_quota,
          dry: reading.dry ? 1 : 0,
          date: reading.date
        });
      });

      dados.water_level_reservoir = $dados.linimetric_rulers_readings;
      dados.pluviometry = $dados.climate_instrument_readings;
    });
    this.constructChart(dados);
  }

  //Gera e configura o gráfico usando as opções definidas.
  generateChart() {
    this.chart['options'] = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegends,
        icon: 'rect',
        left: 'center',
        top: 'bottom'
      },
      grid: {
        containLabel: true,
        top: '50', //Espaco para a legenda
        height: this.controls['chart_height'].value
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: this.xAxis,
        axisLabel: {
          interval: this.controls['xAxis_interval'].value - 1, // Define o intervalo para exibir todos os valores do eixo X
          rotate: this.controls['xAxis_rotate'].value
        }
      },
      yAxis: this.yAxis,
      series: this.chartSeries
    };
  }

  //Navega de volta para a tela de instrumentos.
  goBack() {
    this.router.navigate(['/instruments']);
  }
}
