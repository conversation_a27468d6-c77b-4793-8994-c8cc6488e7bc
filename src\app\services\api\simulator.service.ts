import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { forkJoin, of } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { StructuresService as StructuresServiceApi } from './structure.service';
import { SectionsService as SectionsServiceApi } from './section.service';

@Injectable({
  providedIn: 'root'
})
export class SimulatorService {
  [x: string]: any;
  constructor(private api: ApiService, private structuresServiceApi: StructuresServiceApi, private sectionsServiceApi: SectionsServiceApi) {}

  getStructuresSimulationData(params: any = {}) {
    const url = '/structures/simulation-data';
    return this.api.get<any>(url, params, false, 'client');
  }

  postSimulationsSearch(params: any = {}, qparams: any = {}) {
    const url = '/simulations/search';
    return this.api.post<any>(url, params, qparams, 'client');
  }

  postSimulations(params: any) {
    const url = '/simulations';
    return this.api.post<any>(url, params, {}, 'client');
  }

  getSimulationsById(id: string) {
    const url = `/simulations/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putSimulationsById(id: string, params: any) {
    const url = `/simulations/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  deleteSimulationsById(id: string) {
    const url = `/simulations/${id}`;
    return this.api.delete<any>(url, 'client');
  }

  postShareSimulations(id: string, params: any) {
    const url = `/simulations/${id}/share`;
    return this.api.post<any>(url, params, {}, 'client');
  }

  getSimulationsResultZipFile(url: string = '', id: string, resultId: string = '') {
    if (url == '') {
      url = `/simulations/${id}/result/${resultId}/zip-file`;
    }
    return this.api.get<any>(url, {}, true, 'client');
  }

  getSimulationsZipFile(url: string = '', id: string) {
    if (url == '') {
      url = `/simulations/${id}/zip-file`;
    }
    return this.api.get<any>(url, {}, true, 'client');
  }

  getSimulationData(structureId: string) {
    return this.structuresServiceApi.getStructureById(structureId).pipe(
      switchMap((structure: any) => {
        structure = structure.body === undefined ? structure : structure.body;
        return this.sectionsServiceApi.getSectionList({ structureId }).pipe(
          switchMap((sections: any) => {
            sections = sections.body === undefined ? sections : sections.body;
            const sectionDetails$ = sections.map((section: any) =>
              this.sectionsServiceApi.getSectionById(section.id).pipe(
                map((sectionDetails: any) => {
                  sectionDetails = sectionDetails.body === undefined ? sectionDetails : sectionDetails.body;
                  return {
                    id: section.id,
                    name: section.name,
                    minimum_drained_depth: sectionDetails.minimum_drained_depth,
                    minimum_undrained_depth: sectionDetails.minimum_undrained_depth,
                    minimum_pseudo_static_depth: sectionDetails.minimum_pseudo_static_depth
                  };
                }),
                catchError(() => of(null))
              )
            );

            const sectionIds = sections.map((section: any) => section.id);
            return forkJoin(sectionDetails$).pipe(
              switchMap((sectionDetails: any) => {
                sectionDetails = sectionDetails.filter((detail: any) => detail !== null);
                return this.sectionsServiceApi.postSectionInstrumentsLatestReadings({ section_ids: sectionIds }).pipe(
                  map((latestReadings: any) => {
                    latestReadings = latestReadings.body === undefined ? latestReadings : latestReadings.body;
                    const enrichedSections = sectionDetails.map((sectionDetail: any) => {
                      const latestReading = latestReadings.find((reading: any) => reading.id === sectionDetail.id);
                      return {
                        ...sectionDetail,
                        beach_length: latestReading ? latestReading.beach_length : null,
                        linimetric_rulers: latestReading ? latestReading.linimetric_rulers : [],
                        instruments: latestReading ? latestReading.instruments : []
                      };
                    });

                    // Extrair linimetric_rulers das sections e garantir que não haja duplicatas
                    const linimetricRulersSet = new Set<string>();
                    enrichedSections.forEach((section: any) => {
                      if (section.linimetric_rulers && section.linimetric_rulers.length > 0) {
                        section.linimetric_rulers.forEach((ruler: any) => linimetricRulersSet.add(JSON.stringify(ruler)));
                      }
                    });

                    const linimetricRulers = Array.from(linimetricRulersSet).map((ruler) => JSON.parse(ruler));

                    // Remover linimetric_rulers de cada seção no resultado final
                    const finalSections = enrichedSections.map(({ linimetric_rulers, ...rest }: any) => rest);

                    return {
                      structure: {
                        id: structure.id,
                        name: structure.name,
                        slide2_configuration: structure.slide2_configuration,
                        linimetric_rulers: linimetricRulers
                      },
                      sections: finalSections
                    };
                  })
                );
              }),
              catchError(() => of([]))
            );
          }),
          catchError(() => of([]))
        );
      }),
      catchError(() => of(null))
    );
  }
}
