import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import { StaticMaterialsService } from 'src/app/services/static-materials.service';
import { StaticMaterialsService as StaticMaterialsServiceApi } from 'src/app/services/api/staticMaterials.service';

import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';

import { TabsConditionsComponent } from '@pages/static-materials/conditions-static-materials/tabs-conditions/tabs-conditions.component';
import { NgxSpinnerService } from 'ngx-spinner';

import * as moment from 'moment-timezone';
import { format } from 'date-fns';
import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-review-tab',
  templateUrl: './review-tab.component.html',
  styleUrls: ['./review-tab.component.scss'],
  providers: [StaticMaterialsService]
})
export class ReviewTabComponent implements OnInit {
  @ViewChild(TabsConditionsComponent) tabConditionsReview: TabsConditionsComponent;

  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public materialId: any = null;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  @Output() public sendActiveReview = new EventEmitter();

  public formStaticMaterialsReview: FormGroup = new FormGroup({
    review_id: new FormControl(''),
    description: new FormControl(''),
    name: new FormControl(''),
    start_date: new FormControl(''),
    structures: new FormControl([])
  });

  public editReview: boolean = false;
  public viewReview: boolean = false;

  public reviewsValidate: boolean = false;
  public ctrlReview: boolean = false;

  public activeReviewId: any = -1;
  public activeReviewIndex: any = 0;

  public materialIdDefault: string = '';
  public staticMaterialId: string = '';

  public maxlength: number = 100;
  public charachtersCount: number = 0;
  public counter: string;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false, class: 'alert-warning' };
  public messagesError: any = null;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Revisão',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['index']
    },
    {
      label: 'Material',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'Usuário',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Data/Hora',
      width: '240px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['start_date_format']
    },
    {
      label: 'Descrição',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['description']
    },
    {
      label: 'Ações',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom']
    }
  ];

  public actionCustom: any = [
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-pencil',
      label: '',
      title: 'Editar',
      type: 'true',
      option: 'edit'
    },
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-eye',
      label: '',
      title: 'Visualizar',
      type: 'true',
      option: 'view'
    }
  ];

  constructor(
    private activatedRoute: ActivatedRoute,
    private staticMaterialsService: StaticMaterialsService,
    private staticMaterialsServiceApi: StaticMaterialsServiceApi,
    private ngxSpinnerService: NgxSpinnerService
  ) {}

  ngOnInit(): void {
    this.staticMaterialId = this.activatedRoute.snapshot.params.staticMaterialId;
    this.setDateDefault();

    if (this.view) {
      this.actionCustom = [
        {
          class: 'btn-logisoil-edit',
          icon: 'fa fa-eye',
          label: '',
          title: 'Visualizar',
          type: 'true',
          option: 'view'
        }
      ];
    }
  }

  setDateDefault() {
    this.formStaticMaterialsReview.get('start_date').setValue(format(new Date(), 'yyyy-MM-dd HH:mm:ss'));
  }

  /**
   * Valida o formulário de revisão de materiais estáticos e, se válido, realiza o registro ou a edição da revisão.
   */
  validateReview() {
    const data = this.staticMaterialsService.validate(this, 'tabConditionsReview', 'formStaticMaterialsReview', this.editReview, this.viewReview);

    if (data !== false) {
      if (!this.editReview) {
        delete data['review_id'];
        this.registerMaterialsReview(data);
      } else {
        data['id'] = this.staticMaterialId;
        data['review_id'] = this.formStaticMaterialsReview.controls['review_id'].value;
        this.registerMaterialsReview(data);
      }
    }
  }

  /**
   * Registra uma nova revisão de materiais estáticos no banco de dados.
   * Exibe mensagens de erro ou sucesso conforme a resposta da API.
   *
   * @param {any} params - Parâmetros da revisão a ser registrada.
   */
  registerMaterialsReview(params: any) {
    this.ngxSpinnerService.show();
    this.messagesError = [];

    this.staticMaterialsServiceApi.postStaticMaterialsReview(this.staticMaterialId, params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = this.editReview ? MessageCadastro.EditReviewMaterial : MessageCadastro.ReviewMaterial;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.message.class = 'alert-success';
        }, 4000);

        this.getStaticMaterial();
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });

          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  formatData($dados) {
    this.tableData = $dados.static_material_reviews.map((review) => {
      let itemReview: any = {};
      itemReview.index = review.index;
      itemReview.name = $dados.name;
      itemReview.index = review.index;
      itemReview.user = `${review.created_by.first_name} ${review.created_by.surname}`;
      itemReview.start_date = review.start_date;
      itemReview.start_date_format = moment(review.start_date).tz('America/Sao_Paulo').subtract(3, 'hours').format('DD/MM/YYYY HH:mm:ss');
      itemReview.description = review.description;
      itemReview.id = review.id; //Para edição e visualização da revisão

      return itemReview;
    });
  }

  getStaticMaterialReview($review = null) {
    this.ngxSpinnerService.show();

    this.staticMaterialsServiceApi.getStaticMaterialsReviewById($review.id).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.splitData(dados, $review);

      this.ngxSpinnerService.hide();
    });
  }

  splitData($dados, $review) {
    this.editReview = true;
    this.ctrlReview = true;

    this.formStaticMaterialsReview.controls['name'].setValue($dados.name);
    this.formStaticMaterialsReview.controls['start_date'].setValue(
      moment($review.start_date).tz('America/Sao_Paulo').subtract(3, 'hours').format('YYYY-MM-DD HH:mm:ss')
    );
    this.formStaticMaterialsReview.controls['review_id'].setValue($review.id);
    this.formStaticMaterialsReview.controls['structures'].setValue([$dados.structure]);
    this.formStaticMaterialsReview.controls['description'].setValue($review.description);

    setTimeout(() => {
      this.tabConditionsReview.getStaticMaterialsList($dados.structure);
      this.tabConditionsReview.dataDrained = $dados.drained_static_material_value;
      this.tabConditionsReview.dataUndrained = $dados.undrained_static_material_value;
      this.tabConditionsReview.dataPseudo = $dados.pseudo_static_material_value;

      this.formStaticMaterialsReview.enable();

      if (this.viewReview) {
        this.formStaticMaterialsReview.disable();
      }
    }, 50);
  }

  /**
   * Obtém os dados de um material estático específico pelo seu ID e preenche o formulário com esses dados.
   */
  getStaticMaterial() {
    this.ngxSpinnerService.show();

    this.staticMaterialsServiceApi.getStaticMaterialsById(this.staticMaterialId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.resetForm('review');
      this.formatData(dados);

      this.ngxSpinnerService.hide();
    });
  }

  resetForm(type: string = '', option: boolean = false) {
    if (type === 'review') {
      this.ctrlReview = false;
      this.formStaticMaterialsReview.controls['review_id'].setValue('');
      this.formStaticMaterialsReview.controls['description'].setValue('');
      this.formStaticMaterialsReview.controls['start_date'].setValue('');

      if (option) {
        this.ctrlReview = true;

        setTimeout(() => {
          this.tabConditionsReview.getStaticMaterialsList(this.formStaticMaterialsReview.controls['structures'].value[0]);
        }, 50);
        this.setDateDefault();
      }
      this.editReview = false;
    }
  }

  clickRowEvent($event: any = null) {
    switch ($event.action) {
      case 'edit':
        this.viewReview = false;
        break;
      case 'view':
        this.viewReview = true;
        break;
    }
    this.getStaticMaterialReview(this.tableData[$event.index]);
  }

  //Atualiza o contador de caracteres conforme o usuário digita na área de nota.
  onValueChange(event: any): void {
    this.charachtersCount = event.target.value.length;
    this.counter = `${this.charachtersCount} de ${this.maxlength}`;
  }
}
