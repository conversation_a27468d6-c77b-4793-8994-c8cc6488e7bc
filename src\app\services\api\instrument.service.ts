import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class InstrumentsService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  getInstrumentsList(params: any = {}) {
    const url = `/instruments`;
    return this.api.get<any>(url, params, false, 'client');
  }

  // Cadastro de Instrumentos
  postInstruments(params: any) {
    const url = '/instruments';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna os instrumentos para uso em filtro
  getInstrumentsSearch(params: any) {
    const url = `/instruments/search`;
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca o Instrumento por ID
  getInstrumentsById(id: string) {
    const url = `/instruments/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  patchInstruments(id: string, params: any) {
    const url = `/instruments/${id}`;
    return this.api.patch<any>(url, params, 'client');
  }

  putInstruments(params: any) {
    const url = `/instruments`;
    return this.api.put<any>(url, params, 'client');
  }

  //Cadastro de Grupo de Instrumentos
  postGroupInstruments(params: any) {
    const url = `/instruments/groups`;
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna o grupo de instrumentos na tabela de listagem
  getGroupInstrumentsSearch(params: any) {
    const url = `/instruments/groups/search`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Edicao de Grupo de Instrumentos
  putGroupInstruments(id: string, params: any) {
    const url = `/instruments/groups/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  // Exclui o registro do Grupo de Instrumentos no banco de dados
  deleteGroupInstruments(id: string) {
    const url = `/instruments/groups/${id}`;
    return this.api.delete<any>(url, 'client');
  }

  getGroupInstrumentsMaps(params: any) {
    const url = `/instruments/maps/search`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Historico da instrumentacao
  getInstrumentsHistory(id: string, params: any) {
    const url = `/instruments/${id}/history`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Historico do registro manual
  getInstrumentsNotes(id: string, params: any) {
    const url = `/instruments/${id}/notes`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Cadastro de registro
  postInstrumentsNotes(id: string, params: any) {
    const url = `/instruments/${id}/notes`;
    return this.api.post<any>(url, params, false, 'client');
  }

  //Edicao do historico dos registros manuais
  putInstrumentsNotes(idInstrument: string, idNote: string, params: any) {
    const url = `/instruments/${idInstrument}/notes/${idNote}`;
    return this.api.put<any>(url, params, 'client');
  }

  getInstrumentsNoteHistory(idInstrument: string, idNote: string, params: any) {
    const url = `/instruments/${idInstrument}/notes/${idNote}/history`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Baixar informacoes
  getInstrumentsFileDownload(params: any) {
    const url = `/instruments/file/download`;
    return this.api.get<any>(url, params, true, 'client');
  }

  //Download planilha de acordo com o tipo de instrumento
  getInstrumentsFileDownloadTemplate(params: any) {
    const url = `/instruments/file/download/template`;
    return this.api.get<any>(url, params, true, 'client');
  }

  postInstrumentsFileConvertToUpdate(params: any, queryParams: any = {}) {
    const url = `/instruments/file/convert/to/update`;
    return this.api.post<any>(url, params, queryParams, 'client', true);
  }

  //Cadastro via planilha
  postInstrumentsFileConvertToAdd(params: any, queryParams: any = {}) {
    const url = `/instruments/file/convert/to/add`;
    return this.api.post<any>(url, params, queryParams, 'client', true);
  }

  //Section por instrumento
  getInstrumentsSections(id: string, params: any = {}) {
    const url = `/instruments/${id}/sections`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Historico de alertas por instrumento
  getSecurityLevelAlerts(id: string, params: any) {
    const url = `/instruments/${id}/security-level-alerts/search`;
    return this.api.get<any>(url, params, false, 'client');
  }
}
