import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  OnInit,
  QueryList,
  SimpleChanges,
  ViewEncapsulation,
  ViewChild,
  ViewChildren
} from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import { accessLevel, coordinateFormat, Datum, zoneLetterUTM, zoneNumberUTM } from 'src/app/constants/app.constants';
import { Automated, GeophoneType, OnLine, Position, setFieldsInstruments, DryType } from 'src/app/constants/instruments.constants';
import { ErrosInput } from 'src/app/constants/message.constants';
import { SecurityLevelsComponent } from '@pages/instruments/security-levels/security-levels.component';

import { CoordinateService } from 'src/app/services/coordinate.service';
import { InstrumentsService } from 'src/app/services/instruments.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';

import { FormService } from 'src/app/services/form.service';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import * as _ from 'lodash';

@Component({
  selector: 'app-form-instruments',
  templateUrl: './form-instruments.component.html',
  styleUrls: ['./form-instruments.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class FormInstrumentsComponent implements OnInit, OnChanges {
  @ViewChildren('measureRef') measureRef: QueryList<ElementRef>;
  @ViewChild(SecurityLevelsComponent) securityLevels: SecurityLevelsComponent;

  @Input() public item: any = '';
  @Input() public index: any = '';
  @Input() public data: any = null;
  @Input() public coordinateStructure: any = null;

  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public editingBatch: boolean = false;
  @Input() public insertSpreadsheet: boolean = false;

  @Output() public sendRemove = new EventEmitter();

  public formInstrument: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    type: new FormControl(null, [Validators.required]),
    identifier: new FormControl('', [Validators.required]),
    alternative_name: new FormControl(''),
    top_quota: new FormControl(''),
    base_quota: new FormControl(''),
    installation_date: new FormControl('', [Validators.required]),
    responsible_for_installation: new FormControl(''),
    model: new FormControl(''),
    automated: new FormControl(false),
    online: new FormControl(true),
    azimuth: new FormControl(null),
    geophone_type: new FormControl(''),
    dry_type: new FormControl(1),
    elevation: new FormControl(null),
    datum: new FormControl('', [Validators.required]),
    coordinate_format: new FormControl(null, [Validators.required]),
    zone_number: new FormControl({ value: '', disabled: true }, [Validators.required]),
    zone_letter: new FormControl({ value: '', disabled: true }, [Validators.required]),
    northing: new FormControl({ value: null, disabled: true }, [Validators.required]),
    easting: new FormControl({ value: null, disabled: true }, [Validators.required]),
    latitude: new FormControl({ value: null, disabled: true }, [Validators.required]),
    longitude: new FormControl({ value: null, disabled: true }, [Validators.required]),
    coordinate_valid: new FormControl(true, [Validators.required]),
    sl_instrument_id: new FormControl(''),
    sl_instrument_attention: new FormControl(''),
    sl_instrument_alert: new FormControl(''),
    sl_instrument_emergency: new FormControl(''),
    sl_instrument_abrupt_variation_between_readings: new FormControl(''),
    sl_instrument_maximum_daily_rainfall: new FormControl(''),
    sl_instrument_rain_intensity: new FormControl(''),
    sl_instrument_axis: new FormControl(''), //Deslocamentos Marco Superficial e Prisma

    sl_instrument_id_1: new FormControl(''),
    sl_instrument_attention_1: new FormControl(''),
    sl_instrument_alert_1: new FormControl(''),
    sl_instrument_emergency_1: new FormControl(''),
    sl_instrument_abrupt_variation_between_readings_1: new FormControl(''),
    sl_instrument_maximum_daily_rainfall_1: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_rain_intensity_1: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_axis_1: new FormControl(1),

    sl_instrument_id_2: new FormControl(''),
    sl_instrument_attention_2: new FormControl(''),
    sl_instrument_alert_2: new FormControl(''),
    sl_instrument_emergency_2: new FormControl(''),
    sl_instrument_abrupt_variation_between_readings_2: new FormControl(''),
    sl_instrument_maximum_daily_rainfall_2: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_rain_intensity_2: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_axis_2: new FormControl(2),

    sl_instrument_id_3: new FormControl(''),
    sl_instrument_attention_3: new FormControl(''),
    sl_instrument_alert_3: new FormControl(''),
    sl_instrument_emergency_3: new FormControl(''),
    sl_instrument_abrupt_variation_between_readings_3: new FormControl(''),
    sl_instrument_maximum_daily_rainfall_3: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_rain_intensity_3: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_axis_3: new FormControl(3),

    sl_instrument_id_4: new FormControl(''),
    sl_instrument_attention_4: new FormControl(''),
    sl_instrument_alert_4: new FormControl(''),
    sl_instrument_emergency_4: new FormControl(''),
    sl_instrument_abrupt_variation_between_readings_4: new FormControl(''),
    sl_instrument_maximum_daily_rainfall_4: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_rain_intensity_4: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_axis_4: new FormControl(4),

    sl_instrument_id_5: new FormControl(''),
    sl_instrument_attention_5: new FormControl(''),
    sl_instrument_alert_5: new FormControl(''),
    sl_instrument_emergency_5: new FormControl(''),
    sl_instrument_abrupt_variation_between_readings_5: new FormControl(''),
    sl_instrument_maximum_daily_rainfall_5: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_rain_intensity_5: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_axis_5: new FormControl(5),

    sl_instrument_id_6: new FormControl(''),
    sl_instrument_attention_6: new FormControl(''),
    sl_instrument_alert_6: new FormControl(''),
    sl_instrument_emergency_6: new FormControl(''),
    sl_instrument_abrupt_variation_between_readings_6: new FormControl(''),
    sl_instrument_maximum_daily_rainfall_6: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_rain_intensity_6: new FormControl(null), //Pluviômetro e Pluviógrafo
    sl_instrument_axis_6: new FormControl(6),
    realocation: new FormControl(false), //Inclinometro convencional e IPI
    linimetric_ruler_position: new FormControl(''), //Regua linimetrica
    depth: new FormControl(''), //Medidor de Recalque
    upper_limit: new FormControl('', [Validators.required]), //Marco superficial e Prisma
    lower_limit: new FormControl('', [Validators.required]), //Marco superficial e Prisma
    measurement_frequency: new FormControl(null, [Validators.required]), //Pluviógrafo
    structure: new FormControl([]) //Edicao em massa
  });

  public typeMeasure: any = '';
  public nameMeasure: any = '';
  public hasMeasureMultiple: boolean = false;

  public formValid: boolean = false;

  public measuresReference: any = [];
  public measuresData: any = [];

  public automated: any = Automated;
  public datum: any = Datum;
  public geophoneType: any = GeophoneType;
  public dryType: any = DryType;
  public position: any = Position;
  public onLine: any = OnLine;

  public zoneLetterUTM: any = zoneLetterUTM;
  public zoneNumberUTM: any = zoneNumberUTM;

  public structures: any = [];
  public cordinateFormat: any = coordinateFormat;
  public coordinateFormatSel: any;
  public coordinateFormatSelected: string;
  public coordinateFormatString: string;
  public coordinateFormatList: any = coordinateFormat;

  public accessLevel: any = accessLevel;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesInfo: any = null;
  public messagesError: any = null;

  public instrumentName: any = '';

  public validateMeasure: boolean = true;
  public validateIsReferencial: boolean = false;

  public datasecurityLevels = null;

  public fields: any = [];

  public func = fn;

  public _current: any = {};

  public charCounts: { [key: string]: number } = {}; // Objeto para rastrear caracteres de vários campos

  constructor(
    private activatedRoute: ActivatedRoute,
    private coordinateService: CoordinateService,
    private formService: FormService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private instrumentsService: InstrumentsService
  ) {}

  ngOnInit(): void {
    // Inicializa o contador para cada campo do formulário
    for (const key of Object.keys(this.formInstrument.controls)) {
      const initialValue = this.formInstrument.get(key)?.value || '';
      this.charCounts[key] = initialValue.length;
    }
  }

  /**
   * Método acionado quando há mudanças em qualquer uma das propriedades vinculadas ao componente.
   * Verifica se há um novo valor para os dados e ajusta os campos do instrumento.
   * @param {SimpleChanges} changes - As mudanças detectadas nos dados vinculados.
   */
  ngOnChanges(changes: SimpleChanges) {
    if (changes.data?.currentValue != null) {
      this.setFieldsInstrument(changes.data.currentValue.type);
      const clonedData = _.cloneDeep(changes.data.currentValue);
      this.splitData(clonedData);
    } else {
      this.measuresReference = [1];
      this.setFieldsInstrument();
    }

    if (changes.coordinateStructure?.currentValue != null && !this.formInstrument.get('datum').value) {
      this.setCoordinatesByStructure(changes.coordinateStructure?.currentValue);
    }
  }

  /**
   * Obtém os detalhes de um instrumento específico pelo seu ID e preenche os campos do formulário com as informações obtidas.
   * @param {string} instrumentId - O ID do instrumento a ser buscado.
   */
  getInstruments(instrumentId: string) {
    this.instrumentsServiceApi.getInstrumentsById(instrumentId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.formInstrument.get('id').setValue(dados.search_identifier);
      this.formInstrument.get('identifier').setValue(dados.identifier);
      this.formInstrument.get('alternative_name').setValue(dados.alternative_name);
      this.formInstrument.get('top_quota').setValue(dados.top_quota);
      this.formInstrument.get('base_quota').setValue(dados.base_quota);
      this.formInstrument.get('installation_date').setValue(dados.installation_date);
      this.formInstrument.get('responsible_for_installation').setValue(dados.responsible_for_installation);
      this.formInstrument.get('model').setValue(dados.model);
      this.formInstrument.get('automated').setValue(dados.automated);
      this.formInstrument.get('azimuth').setValue(dados.azimuth);
      this.formInstrument.get('geophone_type').setValue(dados.geophone_type);
      this.formInstrument.get('dry_type').setValue(dados.dry_type);
      this.formInstrument.get('elevation').setValue(dados.elevation);
      this.formInstrument.get('linimetric_ruler_position').setValue(dados.linimetric_ruler_position);
      this.formInstrument.get('upper_limit').setValue(dados.upper_limit);
      this.formInstrument.get('lower_limit').setValue(dados.lower_limit);
      this.formInstrument.get('depth').setValue(dados.depth);
      this.formInstrument.get('measurement_frequency').setValue(dados.measurement_frequency);

      this.formInstrument.get('coordinate_format').setValue(dados.coordinate_setting.coordinate_format.toString());
      this.formInstrument.get('zone_number').setValue(dados.coordinate_setting.coordinate_systems.utm.zone_number);
      this.formInstrument.get('zone_letter').setValue(dados.coordinate_setting.coordinate_systems.utm.zone_letter);
      this.formInstrument.get('northing').setValue(dados.coordinate_setting.coordinate_systems.utm.northing);
      this.formInstrument.get('easting').setValue(dados.coordinate_setting.coordinate_systems.utm.easting);
      this.formInstrument.get('latitude').setValue(dados.coordinate_setting.coordinate_systems.decimal_geodetic.latitude);
      this.formInstrument.get('longitude').setValue(dados.coordinate_setting.coordinate_systems.decimal_geodetic.longitude);

      this.getSelectedCoordinateFormat();
    });
  }

  /**
   * Converte as coordenadas de acordo com o tipo especificado, atualizando os campos do formulário com os valores convertidos.
   * @param {string} [type=''] - O tipo de coordenada para a conversão (ex: UTM ou Geodetic).
   */
  coordinatesConversion(type: string = '') {
    this.coordinateService.coordinatesConversion(this.formInstrument, type).subscribe((coordinates) => {
      if (coordinates !== null && coordinates.hasOwnProperty('hasError') && coordinates.hasError) {
        const error = coordinates.error;
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        setTimeout(() => {
          this.messagesError = [];
        }, 4000);

        this.formInstrument.controls['coordinate_valid'].setValue('');
      } else {
        if (coordinates !== null) {
          if (coordinates.type == 'UTM') {
            this.formInstrument.get('zone_letter').setValue(coordinates.zone_letter);
            this.formInstrument.get('zone_number').setValue(coordinates.zone_number);
            this.formInstrument.get('northing').setValue(coordinates.northing);
            this.formInstrument.get('easting').setValue(coordinates.easting);
          } else if (coordinates.type == 'Geodetic') {
            this.formInstrument.get('latitude').setValue(coordinates.latitude);
            this.formInstrument.get('longitude').setValue(coordinates.longitude);
          }
          this.formInstrument.controls['coordinate_valid'].setValue(true);
        }
      }
    });
  }

  /**
   * Ação a ser tomada quando o formato de coordenada é alterado, ajustando os campos de acordo com o novo formato selecionado.
   * @param {any} coordinateFormat - O formato de coordenada selecionado.
   * @param {string} [type=''] - Tipo adicional para tratamento específico.
   */
  onCoordinateFormatChange(coordinateFormat: any, type: string = '') {
    this.getSelectedCoordinateFormat(type);
  }

  /**
   * Obtém o formato de coordenada selecionado e ajusta os campos do formulário de acordo com esse formato.
   * @param {string} [type=''] - Tipo adicional para tratamento específico.
   */
  getSelectedCoordinateFormat(type: string = '') {
    this.coordinateFormatSel = coordinateFormat.find((item) => item.id === parseInt(this.formInstrument.get('coordinate_format').value));
    this.coordinateFormatString = this.coordinateFormatSel.value;

    if (this.coordinateFormatString === 'UTM') {
      this.formInstrument.get('northing').enable();
      this.formInstrument.get('easting').enable();

      this.formInstrument.get('latitude').disable();
      this.formInstrument.get('longitude').disable();
    } else if (this.coordinateFormatString === 'Decimal Geodetic') {
      this.formInstrument.get('latitude').enable();
      this.formInstrument.get('longitude').enable();

      this.formInstrument.get('zone_number').disable();
      this.formInstrument.get('zone_letter').disable();
      this.formInstrument.get('northing').disable();
      this.formInstrument.get('easting').disable();
    }
  }

  /**
   * Configura os campos do instrumento selecionado para cadastro, habilitando ou desabilitando campos com base no tipo de instrumento.
   * @param {string} [type=''] - O tipo do instrumento para configuração dos campos.
   */
  setFieldsInstrument(type = '') {
    let queryParams = this.activatedRoute.snapshot.queryParams;

    if (queryParams.id || type != '') {
      type = queryParams.id ? queryParams.id : type;
      this.formInstrument.get('type').setValue(parseInt(type));

      this.fields = this.instrumentsService.getDataInstruments(type, 'fields');

      this.instrumentName = this.instrumentsService.getDataInstruments(type, 'name');

      this.typeMeasure = this.instrumentsService.getDataInstruments(type, 'typeMeasure');
      this.nameMeasure = this.instrumentsService.getDataInstruments(type, 'nameMeasure');

      if (queryParams.pressureCells !== 'false' || queryParams.measuringPoints !== 'false' || queryParams.magneticRings !== 'false') {
        this.hasMeasureMultiple = true;
      }

      setFieldsInstruments.forEach((field) => {
        if (this.fields.includes(field)) {
          this.formInstrument.get(field).enable();
          this.formInstrument.get(field).markAsUntouched();
          this.formInstrument.get(field).setValidators([Validators.required]);
          this.formInstrument.get(field).updateValueAndValidity();
        } else {
          this.formInstrument.get(field).setValue(null);
          this.formInstrument.get(field).setErrors(null);
          this.formInstrument.get(field).markAsUntouched();
          this.formInstrument.get(field).clearValidators();
          this.formInstrument.get(field).disable();
        }

        if (field == 'azimuth') {
          this.formInstrument.get(field).setErrors(null);
          this.formInstrument.get(field).markAsUntouched();
          this.formInstrument.get(field).clearValidators();
        }
      });
    }
  }

  //Remove o item associado ao componente atual, emitindo um evento para o componente pai.
  removeMe() {
    this.sendRemove.emit({ item: this.item, index: this.index });
  }

  //Adiciona uma nova medida à lista de medidas de referência.
  addMeasure() {
    let max = 0;
    if (this.measuresReference.length > 0) {
      max = Math.max(...this.measuresReference);
    }
    this.measuresReference.push(max + 1);
  }

  /**
   * Remove uma medida específica da lista de medidas de referência.
   * @param {any} $event - O evento que contém o índice da medida a ser removida.
   */
  removeMeasure($event) {
    this.measuresReference.splice($event.index, 1);
  }

  /**
   * Gerencia o estado referencial das medidas, garantindo que apenas uma medida seja marcada como referencial.
   * @param {any} $event - O evento que contém a medida a ser gerenciada.
   */
  managerIsReferencial($event) {
    this.measureRef.toArray().forEach((measureItem) => {
      let formMeasure = measureItem['formMeasure'];
      if ($event.item !== measureItem['item']) {
        formMeasure.controls['is_referencial'].setValue(false);
      } else {
        formMeasure.controls['delta_ref'].setValue('');
      }
    });
    this.calcDeltaRef();
  }

  //Obtém os formulários de medições e preenche os valores de níveis de segurança para cada medição.
  getFormsMeasurements() {
    this.validateIsReferencial = false;
    this.messagesInfo = null;
    this.measureRef.toArray().forEach((measureItem) => {
      let formMeasure = measureItem['formMeasure'];

      //Niveis de seguranca
      if (this.formInstrument.controls['type'].value != 12 && this.formInstrument.controls['type'].value != 13) {
        formMeasure.controls['sl_measure_attention'].setValue(measureItem['securityLevels'].formSecurityLevels.controls['attention'].value);
        formMeasure.controls['sl_measure_alert'].setValue(measureItem['securityLevels'].formSecurityLevels.controls['alert'].value);
        formMeasure.controls['sl_measure_emergency'].setValue(measureItem['securityLevels'].formSecurityLevels.controls['emergency'].value);
        formMeasure.controls['sl_measure_abrupt_variation_between_readings'].setValue(
          measureItem['securityLevels'].formSecurityLevels.controls['abrupt_variation_between_readings'].value
        );
      }
      if (this.formInstrument.controls['type'].value == 12 || this.formInstrument.controls['type'].value == 13) {
        //Pluviômetro e Pluviógrafo
        formMeasure.controls['sl_maximum_daily_rainfall'].setValue(measureItem['securityLevels'].formSecurityLevels.controls['attention'].value);
        if (this.formInstrument.controls['type'].value == 13) {
          formMeasure.controls['sl_rain_intensity'].setValue(measureItem['securityLevels'].formSecurityLevels.controls['attention'].value);
        }
      }

      if (this.typeMeasure != 'magnetic_ring') {
        formMeasure.controls['is_referencial'].setValue(null);
      }

      measureItem['formValid'] = !this.validateFormsMeasurements(formMeasure);
      if (!this.validateIsReferencial && measureItem['formMeasure'].controls['is_referencial'].value == true) {
        this.validateIsReferencial = true;
      }
    });
    if (this.typeMeasure == 'magnetic_ring' && !this.validateIsReferencial) {
      this.messagesInfo = [{ message: ErrosInput.isReferencial }];
    }
  }

  /**
   * Valida os formulários de medição e retorna se a validação foi bem-sucedida ou não.
   * @param {any} formMeasure - O formulário de medição a ser validado.
   * @returns {boolean} - True se o formulário for válido, caso contrário, false.
   */
  validateFormsMeasurements(formMeasure) {
    this.validateMeasure = this.formService.validateForm(formMeasure);

    if (!this.validateMeasure) {
      formMeasure.markAllAsTouched();
    }
    return this.validateMeasure;
  }

  /**
   * Divide os dados fornecidos em medidas de referência e os aplica ao formulário de instrumento.
   * @param {any} $dados - Os dados a serem divididos e aplicados.
   */
  splitData($dados) {
    this.measuresData = [];
    this.measuresReference = [];

    $dados.measurements = $dados.measurements.length == undefined ? [$dados.measurements] : $dados.measurements;
    if ($dados.hasOwnProperty('_current')) {
      $dados.measurements.forEach((measurement, index) => {
        let idx = fn.findIndexInArrayofObject($dados._current.measurements, 'id', measurement.id);
        if (idx !== -1) {
          $dados.measurements[index]['_current'] = $dados._current.measurements[idx];
        }
      });
    }

    this.measuresData = $dados.measurements;

    this.measuresData.forEach((measureItem) => {
      this.addMeasure();
    });

    //Dados na register e form instrument
    this.formInstrument.controls['id'].setValue($dados.id);
    this.formInstrument.controls['type'].setValue($dados.type);
    this.formInstrument.controls['identifier'].setValue($dados.identifier);
    this.formInstrument.controls['alternative_name'].setValue($dados.alternative_name);
    this.formInstrument.controls['top_quota'].setValue(fn.fixed($dados.top_quota, this.view));
    this.formInstrument.controls['base_quota'].setValue(fn.fixed($dados.base_quota, this.view));
    this.formInstrument.controls['installation_date'].setValue(moment($dados.installation_date).format('YYYY-MM-DD'));
    this.formInstrument.controls['responsible_for_installation'].setValue($dados.responsible_for_installation);
    this.formInstrument.controls['model'].setValue($dados.model);
    this.formInstrument.controls['automated'].setValue($dados.automated);
    this.formInstrument.controls['online'].setValue($dados.online);
    this.formInstrument.controls['azimuth'].setValue($dados.azimuth);
    this.formInstrument.controls['geophone_type'].setValue($dados.geophone_type);
    this.formInstrument.controls['dry_type'].setValue($dados.dry_type);
    this.formInstrument.controls['linimetric_ruler_position'].setValue($dados.linimetric_ruler_position);
    this.formInstrument.controls['upper_limit'].setValue($dados.upper_limit);
    this.formInstrument.controls['lower_limit'].setValue($dados.lower_limit);
    this.formInstrument.controls['elevation'].setValue($dados.elevation);
    this.formInstrument.controls['depth'].setValue(fn.fixed($dados.depth, this.view));
    this.formInstrument.controls['datum'].setValue($dados.coordinate_setting.datum);
    this.formInstrument.controls['coordinate_format'].setValue($dados.coordinate_setting.coordinate_format.toString());
    this.formInstrument.controls['zone_number'].setValue($dados.coordinate_setting.coordinate_systems.utm.zone_number);
    this.formInstrument.controls['zone_letter'].setValue($dados.coordinate_setting.coordinate_systems.utm.zone_letter);
    this.formInstrument.controls['northing'].setValue($dados.coordinate_setting.coordinate_systems.utm.northing);
    this.formInstrument.controls['easting'].setValue($dados.coordinate_setting.coordinate_systems.utm.easting);
    this.formInstrument.controls['latitude'].setValue($dados.coordinate_setting.coordinate_systems.decimal_geodetic.latitude);
    this.formInstrument.controls['longitude'].setValue($dados.coordinate_setting.coordinate_systems.decimal_geodetic.longitude);
    //Pluviógrafo
    this.formInstrument.controls['measurement_frequency'].setValue($dados.measurement_frequency);

    if ($dados.hasOwnProperty('structure')) {
      this.formInstrument.controls['structure'].setValue($dados.structure);
    }

    if ($dados.hasOwnProperty('_current')) {
      this._current['id'] = $dados.id != $dados._current.id ? $dados._current.id : null;
      this._current['type'] = $dados._current.type != $dados._current.type ? $dados._current.type : null;
      this._current['identifier'] = $dados.identifier != $dados._current.identifier ? $dados._current.identifier : null;
      this._current['alternative_name'] = $dados.alternative_name != $dados._current.alternative_name ? $dados._current.alternative_name : null;
      this._current['top_quota'] = $dados.top_quota != $dados._current.top_quota ? fn.fixed($dados._current.top_quota, this.view) : null;
      this._current['base_quota'] = $dados.base_quota != $dados._current.base_quota ? fn.fixed($dados._current.base_quota, this.view) : null;
      this._current['installation_date'] =
        $dados.installation_date != $dados._current.installation_date ? moment($dados._current.installation_date).format('DD/MM/YYYY') : null;
      this._current['responsible_for_installation'] =
        $dados.responsible_for_installation != $dados._current.responsible_for_installation ? $dados._current.responsible_for_installation : null;
      this._current['model'] = $dados.model != $dados._current.model ? $dados._current.model : null;
      this._current['automated'] = $dados.automated != $dados._current.automated ? ($dados._current.automated ? 'Sim' : 'Não') : null;
      this._current['online'] = $dados.online != $dados._current.online ? ($dados._current.online ? 'Sim' : 'Não') : null;
      this._current['azimuth'] = $dados.azimuth != $dados._current.azimuth ? $dados._current.azimuth : null;
      this._current['geophone_type'] =
        $dados.geophone_type != $dados._current.geophone_type
          ? fn.findIndexInArrayofObject(this.geophoneType, 'value', $dados._current.geophone_type, 'label')
          : null;
      this._current['dry_type'] =
        $dados.dry_type != $dados._current.dry_type ? fn.findIndexInArrayofObject(this.geophoneType, 'value', $dados._current.dry_type, 'label') : 1;
      this._current['linimetric_ruler_position'] =
        $dados.linimetric_ruler_position != $dados._current.linimetric_ruler_position ? $dados._current.linimetric_ruler_position : null;
      this._current['upper_limit'] = $dados.upper_limit != $dados._current.upper_limit ? $dados._current.upper_limit : null;
      this._current['lower_limit'] = $dados.lower_limit != $dados._current.lower_limit ? $dados._current.lower_limit : null;
      this._current['elevation'] = $dados.elevation != $dados._current.elevation ? $dados._current.elevation : null;
      this._current['depth'] = $dados.depth != $dados._current.depth ? fn.fixed($dados._current.depth, this.view) : null;
      this._current['datum'] =
        $dados.coordinate_setting.datum != $dados._current.coordinate_setting.datum
          ? fn.findIndexInArrayofObject(this.datum, 'id', $dados._current.coordinate_setting.datum, 'value')
          : null;
      this._current['coordinate_format'] =
        $dados.coordinate_setting.coordinate_format != $dados._current.coordinate_setting.coordinate_format
          ? $dados._current.coordinate_setting.coordinate_format.toString()
          : null;
      this._current['zone_number'] =
        $dados.coordinate_setting.coordinate_systems.utm.zone_number != $dados._current.coordinate_setting.coordinate_systems.utm.zone_number
          ? $dados._current.coordinate_setting.coordinate_systems.utm.zone_number
          : null;
      this._current['zone_letter'] =
        $dados.coordinate_setting.coordinate_systems.utm.zone_letter != $dados._current.coordinate_setting.coordinate_systems.utm.zone_letter
          ? $dados._current.coordinate_setting.coordinate_systems.utm.zone_letter
          : null;
      this._current['northing'] =
        $dados.coordinate_setting.coordinate_systems.utm.northing != $dados._current.coordinate_setting.coordinate_systems.utm.northing
          ? $dados._current.coordinate_setting.coordinate_systems.utm.northing
          : null;
      this._current['easting'] =
        $dados.coordinate_setting.coordinate_systems.utm.easting != $dados._current.coordinate_setting.coordinate_systems.utm.easting
          ? $dados._current.coordinate_setting.coordinate_systems.utm.easting
          : null;
      this._current['latitude'] =
        $dados.coordinate_setting.coordinate_systems.decimal_geodetic.latitude !=
        $dados._current.coordinate_setting.coordinate_systems.decimal_geodetic.latitude
          ? $dados._current.coordinate_setting.coordinate_systems.decimal_geodetic.latitude
          : null;
      this._current['longitude'] =
        $dados.coordinate_setting.coordinate_systems.decimal_geodetic.longitude !=
        $dados._current.coordinate_setting.coordinate_systems.decimal_geodetic.longitude
          ? $dados._current.coordinate_setting.coordinate_systems.decimal_geodetic.longitude
          : null;

      //Pluviógrafo
      this._current['measurement_frequency'] =
        $dados.measurement_frequency != $dados._current.measurement_frequency ? $dados._current.measurement_frequency : null;

      if ($dados.security_levels !== null || $dados._current.security_levels !== null) {
        $dados.security_levels['_current'] = $dados._current.security_levels;
      }
    }

    this.getSelectedCoordinateFormat();

    this.datasecurityLevels = $dados.security_levels;

    if (this.edit || this.editingBatch || this.insertSpreadsheet) {
      this.formInstrument.controls['identifier'].disable();
      this.formInstrument.controls['identifier'].markAsTouched();
      this.formInstrument.controls['identifier'].clearValidators();
      this.formInstrument.controls['identifier'].setErrors(null);
      this.formInstrument.controls['identifier'].updateValueAndValidity();
    } else if (this.view) {
      this.formInstrument.disable();
    }
  }

  /**
   * Calcula a cota (quota) com base em um evento fornecido, aplicando a lógica específica para pontos de medição e células de pressão.
   * @param {any} $event - O evento que contém os dados para o cálculo da cota.
   */
  calcQuota($event) {
    if ($event.type == 'measure_point' || $event.type == 'pressure_cell') {
      this.managerActive($event);
    } else if ($event.type == 'magnetic_ring') {
      this.calcDeltaRef();
    }
  }

  /**
   * Calcula a elevação com base nos valores de cota e topografia para instrumentos como inclinômetros convencionais e IPI.
   * @param {any} $event - O evento que contém os dados para o cálculo da elevação.
   */
  calcElevation($event) {
    if ($event.type == 'measure_point') {
      this.measureRef.toArray().forEach((measureItem) => {
        if (
          !this.edit ||
          (this.edit && this.formInstrument.controls['realocation'].value) ||
          (this.edit && measureItem['formMeasure'].controls['id'].value == null)
        ) {
          if (
            (measureItem['item'] == $event.item && measureItem['index'] == $event.index && $event.component == 'measure') ||
            $event.component == 'instrument'
          ) {
            let formMeasure = measureItem['formMeasure'];
            if (formMeasure.controls['quota'].value != '' && this.formInstrument.controls['top_quota'].value != '') {
              let elevation = this.formInstrument.controls['top_quota'].value - formMeasure.controls['quota'].value;

              let quotaDecimalPlaces = formMeasure.controls['quota'].value.toString().replace(',', '.').split('.');
              quotaDecimalPlaces = quotaDecimalPlaces.length > 1 ? quotaDecimalPlaces[1].length : 0;

              let top_quotaDecimalPlaces = this.formInstrument.controls['top_quota'].value.toString().replace(',', '.').split('.');
              top_quotaDecimalPlaces = top_quotaDecimalPlaces.length > 1 ? top_quotaDecimalPlaces[1].length : 0;

              let fixedNumber = Math.max(quotaDecimalPlaces, top_quotaDecimalPlaces);
              formMeasure.controls['elevation'].setValue(fn.fixed(elevation, true, fixedNumber));
            }
          }
        }
      });

      this.calcLength();
    }
  }

  /**
   * Calcula o comprimento com base nas medidas de cota fornecidas, aplicando a lógica específica para instrumentos como inclinômetros convencionais e IPI.
   */
  calcLength() {
    let arrMeasures = [];

    this.measureRef.toArray().forEach((measureItem) => {
      let formMeasure = measureItem['formMeasure'];
      if (formMeasure.controls['quota'].value != '') {
        if (formMeasure.controls['active'].value) {
          let obj = { quota: formMeasure.controls['quota'].value, form: formMeasure };
          arrMeasures.push(obj);
        }
      }
    });

    if (arrMeasures.length > 0) {
      arrMeasures.sort((a: any, b: any) => a.quota - b.quota);
      let prevValue = 0;
      arrMeasures.forEach((element, index) => {
        if (index > 0) {
          let prevValueDecimalPlaces: any = prevValue.toString().replace(',', '.').split('.');
          prevValueDecimalPlaces = prevValueDecimalPlaces.length > 1 ? prevValueDecimalPlaces[1].length : 0;

          let elementDecimalPlaces: any = element.quota.toString().replace(',', '.').split('.');
          elementDecimalPlaces = elementDecimalPlaces.length > 1 ? elementDecimalPlaces[1].length : 0;

          let fixedNumber = Math.max(prevValueDecimalPlaces, elementDecimalPlaces);

          let lengthValue = prevValue - element.quota;

          element.form.controls['length'].setValue(fn.fixed(lengthValue * -1, true, fixedNumber));
        } else {
          element.form.controls['length'].setValue(element.form.controls['quota'].value);
        }
        prevValue = element.quota;
      });
    }
  }

  //Calcula a variação de referência (delta) com base nas medidas de anéis magnéticos.
  calcDeltaRef() {
    let magneticRingReferencial = null;

    //Localizar marcado como referencial
    this.measureRef.toArray().forEach((measureItem) => {
      let formMeasure = measureItem['formMeasure'];
      if (formMeasure.controls['active'].value) {
        if (formMeasure.controls['is_referencial'].value) {
          magneticRingReferencial = formMeasure.controls['quota'].value;
        }
      }
    });

    //Calculo
    if (magneticRingReferencial != null) {
      this.measureRef.toArray().forEach((measureItem) => {
        let formMeasure = measureItem['formMeasure'];
        if (formMeasure.controls['active'].value) {
          if (!formMeasure.controls['is_referencial'].value) {
            let quotaDecimalPlaces = fn.fixedDecimaisPlaces(formMeasure.controls['quota'].value);
            let magneticRingReferencialDecimalPlaces = fn.fixedDecimaisPlaces(magneticRingReferencial);
            let fixedNumber = Math.max(quotaDecimalPlaces, magneticRingReferencialDecimalPlaces);
            formMeasure.controls['delta_ref'].setValue(fn.fixed(formMeasure.controls['quota'].value - magneticRingReferencial, true, fixedNumber));
          }
        }
      });
    }
  }

  /**
   * Gerencia a ativação das medições com base em um evento, desativando ou ativando medições conforme necessário.
   * @param {any} $event - O evento que contém os dados para gerenciar as medições ativas.
   */
  managerActive($event) {
    let quotaRef = null;
    let activeRef = null;

    this.measureRef.toArray().forEach((measureItem) => {
      if (measureItem['item'] == $event.item && measureItem['index'] == $event.index) {
        quotaRef = measureItem['formMeasure'].controls['quota'].value;
        activeRef = measureItem['formMeasure'].controls['active'].value;
      }
    });

    this.measureRef.toArray().forEach((measureItem) => {
      if (
        quotaRef != null &&
        measureItem['formMeasure'].controls['quota'].value < quotaRef &&
        !activeRef &&
        measureItem['formMeasure'].controls['active'].value
      ) {
        measureItem['toggleActive'](false);
        measureItem['formMeasure'].controls['active'].setValue(false);
      } else if (
        quotaRef != null &&
        measureItem['formMeasure'].controls['quota'].value > quotaRef &&
        activeRef &&
        !measureItem['formMeasure'].controls['active'].value
      ) {
        measureItem['toggleActive'](true);
        measureItem['formMeasure'].controls['active'].setValue(true);
      }
    });

    if ($event.type == 'measure_point') {
      this.calcElevation($event);
    }
  }

  /**
   * Define os valores dos campos relacionados à coordenada do formulário de instrumento,
   * com base na estrutura selecionada (`coordinateSetting`).
   *
   * Este método:
   * - Define e desabilita o campo `datum` com o valor vindo da estrutura;
   * - Define os campos `zone_number` e `zone_letter` com base no sistema UTM;
   * - Define o formato de coordenada como '2' (padrão UTM);
   * - Reexecuta a lógica relacionada ao formato de coordenada selecionado.
   *
   * @param coordinateSetting Objeto contendo as informações da estrutura, incluindo:
   *  - `datum`: sistema de referência geodésico.
   *  - `coordinate_systems.utm`: objeto com `zone_number` e `zone_letter`.
   */
  setCoordinatesByStructure(coordinateSetting: any) {
    this.formInstrument.get('datum').setValue(coordinateSetting?.datum);
    this.formInstrument.get('datum').disable();

    if (coordinateSetting?.coordinate_systems?.utm) {
      const utm = coordinateSetting.coordinate_systems.utm;

      this.formInstrument.get('zone_number').setValue(utm.zone_number);
      this.formInstrument.get('zone_letter').setValue(utm.zone_letter);
      this.formInstrument.get('coordinate_format').setValue('2');

      this.getSelectedCoordinateFormat();
    }
  }

  // Atualiza o contador do campo específico
  onValueChange(event: any, field: string): void {
    this.charCounts[field] = event.target.value.length;
  }
}
