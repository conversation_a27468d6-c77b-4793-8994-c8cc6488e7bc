import { Component, OnInit, ElementRef, ViewChild, Input } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import {
  MessageCadastro,
  AlterarSenha
} from 'src/app/constants/message.constants';
//Services
import { UserService } from 'src/app/services/user.service';
import { UsersService as UsersServiceApi } from 'src/app/services/api/users.service';
//Models
import { UsuarioProfile } from 'src/app/models/user.model';

@Component({
  selector: 'app-modal-profile',
  templateUrl: './modal-profile.component.html',
  styleUrls: ['./modal-profile.component.scss']
})
export class ModalProfileComponent implements OnInit {
  @ViewChild('modalProfile') modalProfile: ElementRef;
  @ViewChild('modalEditProfile') modalEditProfile: ElementRef;
  @ViewChild('modalAlterarSenha') modalAlterarSenha: ElementRef;

  @Input() public user: any = {
    id: '',
    name: '',
    family_name: '',
    alias: '',
    email: '',
    username: '',
    role: '',
    locale: ''
  };

  public formProfile: FormGroup = new FormGroup({
    username: new FormControl(null, [Validators.required]),
    email_address: new FormControl(null, [Validators.required]),
    first_name: new FormControl(null, [Validators.required]),
    surname: new FormControl(null, [Validators.required]),
    locale: new FormControl('', [Validators.required])
  });

  public locales: any = [];

  public message: any = [{ text: '', status: false }];

  constructor(
    private modalService: NgbModal,
    private userService: UserService,
    private usersServiceApi: UsersServiceApi
  ) {}

  ngOnInit(): void {
    this.locales = this.userService.getLocale();
    this.getProfile();
  }

  openModal() {
    this.modalService.open(this.modalProfile);
  }

  editProfile() {
    this.modalService.open(this.modalEditProfile);
  }

  getProfile() {
    this.formProfile.get('username').setValue(this.user.username);
    this.formProfile.get('email_address').setValue(this.user.email);
    this.formProfile.get('first_name').setValue(this.user.name);
    this.formProfile.get('surname').setValue(this.user.family_name);
    this.formProfile.get('locale').setValue(this.user.locale.id);
  }

  alterarProfile() {
    let usuarioProfile: UsuarioProfile = new UsuarioProfile(
      this.user.id,
      this.formProfile.value.username,
      this.formProfile.value.email_address,
      this.formProfile.value.first_name,
      this.formProfile.value.surname,
      parseInt(this.formProfile.value.locale)
    );

    this.usersServiceApi.putUsersMe(usuarioProfile).subscribe((resp) => {
      this.message.text = MessageCadastro.AlteracaoProfile;
      this.message.status = true;
      setTimeout(() => {
        this.message.status = false;
      }, 6000);
    });
  }

  alterarSenha() {
    this.modalService.open(this.modalAlterarSenha);
  }

  enviarEmail() {
    this.usersServiceApi.patchPassword('').subscribe((resp) => {
      this.message.text = AlterarSenha.EnviarEmail;
      this.message.status = true;
      setTimeout(() => {
        this.message.status = false;
      }, 6000);
    });
  }
}
