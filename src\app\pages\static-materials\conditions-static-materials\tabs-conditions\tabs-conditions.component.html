<div class="d-flex flex-column">
  <span class="mb-2"
    ><i class="fa fa-exclamation-circle me-2"></i>
    {{ info }}
  </span>
  <small class="form-text text-danger" *ngIf="ctrlMsgTab == 0"
    >Selecione pelo menos um tipo de condição abaixo.
  </small>
</div>

<ul class="nav nav-tabs" id="myTab" role="tablist">
  <!-- Condição drenada -->
  <li class="nav-item" role="presentation">
    <button
      class="nav-link"
      [ngClass]="drainedTabConfig.active ? 'active' : ''"
      id="drained-tab"
      type="button"
      role="tab"
      aria-controls="drained"
      aria-selected="true"
      (click)="selectTab('drained')"
      [style.background-color]="drainedTabConfig.styleColor ? '#dc3545' : ''"
      [style.color]="drainedTabConfig.styleColor ? '#ffffff' : ''"
    >
      Condição drenada
    </button>
  </li>

  <!-- Condição não drenada -->
  <li class="nav-item" role="presentation">
    <button
      class="nav-link"
      [ngClass]="undrainedTabConfig.active ? 'active' : ''"
      id="undrained-tab"
      type="button"
      role="tab"
      aria-controls="undrained"
      aria-selected="true"
      (click)="selectTab('undrained')"
      [style.background-color]="undrainedTabConfig.styleColor ? '#dc3545' : ''"
      [style.color]="undrainedTabConfig.styleColor ? '#ffffff' : ''"
    >
      Condição não drenada
    </button>
  </li>

  <!-- Condição pseudo estática -->
  <li class="nav-item" role="presentation">
    <button
      class="nav-link"
      [ngClass]="pseudoTabConfig.active ? 'active' : ''"
      id="pseudo-tab"
      type="button"
      role="tab"
      aria-controls="pseudo"
      aria-selected="true"
      (click)="selectTab('pseudo')"
      [style.background-color]="pseudoTabConfig.styleColor ? '#dc3545' : ''"
      [style.color]="pseudoTabConfig.styleColor ? '#ffffff' : ''"
    >
      Condição pseudo-estática
    </button>
  </li>
</ul>
<div class="tab-content mt-2" id="myTabContent">
  <div
    class="tab-pane fade"
    [ngClass]="drainedTabConfig.active ? 'show active' : ''"
    id="drained"
    role="tabpanel"
    aria-labelledby="drained-tab"
  >
    <label class="form-label">Formulário para Condição Drenada</label>
    <br />
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-clone'"
      [label]="'Copiar dos parâmetros não-drenados do material'"
      class="mb-3 me-2"
      (click)="copyFromTo('undrainedRef', 'drainedRef')"
      *ngIf="!view"
    ></app-button>
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-clone'"
      [label]="'Copiar dos parâmetros pseudo-estáticos do material'"
      class="mb-3"
      (click)="copyFromTo('pseudoRef', 'drainedRef')"
      *ngIf="!view"
    ></app-button>
    <app-conditions-static-materials
      #drainedRef
      [materialsList]="materialsList"
      [data]="dataDrained"
      [edit]="edit"
      [view]="view"
      [title]="'Condição Drenada'"
    ></app-conditions-static-materials>
  </div>
</div>

<!-- Condição não drenada -->
<div class="tab-content" id="myTabContent">
  <div
    class="tab-pane fade"
    [ngClass]="undrainedTabConfig.active ? 'show active' : ''"
    id="undrained"
    role="tabpanel"
    aria-labelledby="undrained-tab"
  >
    <label class="form-label">Formulário para Condição Não Drenada</label>
    <br />
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-clone'"
      [label]="'Copiar dos parâmetros drenados do material'"
      class="mb-3 me-2"
      (click)="copyFromTo('drainedRef', 'undrainedRef')"
      *ngIf="!view"
    ></app-button>
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-clone'"
      [label]="'Copiar dos parâmetros pseudo-estáticos do material'"
      class="mb-3"
      (click)="copyFromTo('pseudoRef', 'undrainedRef')"
      *ngIf="!view"
    ></app-button>
    <app-conditions-static-materials
      #undrainedRef
      [materialsList]="materialsList"
      [data]="dataUndrained"
      [edit]="edit"
      [view]="view"
      [title]="'Condição Não Drenada'"
    ></app-conditions-static-materials>
  </div>
</div>

<!-- Condição pseudo estática -->
<div class="tab-content" id="myTabContent">
  <div
    class="tab-pane fade"
    [ngClass]="pseudoTabConfig.active ? 'show active' : ''"
    id="pseudo"
    role="tabpanel"
    aria-labelledby="pseudo-tab"
  >
    <label class="form-label">Formulário para Condição Pseudo-Estática</label>
    <br />
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-clone'"
      [label]="'Copiar dos parâmetros drenados do material'"
      class="mb-3 me-2"
      (click)="copyFromTo('drainedRef', 'pseudoRef')"
      *ngIf="!view"
    ></app-button>
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-clone'"
      [label]="'Copiar dos parâmetros não-drenados do material'"
      class="mb-3"
      (click)="copyFromTo('undrainedRef', 'pseudoRef')"
      *ngIf="!view"
    ></app-button>
    <app-conditions-static-materials
      #pseudoRef
      [materialsList]="materialsList"
      [data]="dataPseudo"
      [edit]="edit"
      [view]="view"
      [title]="'Condição Pseudo-estática'"
    ></app-conditions-static-materials>
  </div>
</div>
