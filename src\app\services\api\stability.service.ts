import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class StabilityService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  // Retorna as análises de estabilidade para uso em filtro
  getStabilityAnalysisSearch(params: any) {
    const url = `/stability-analysis/search`;
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca a análise de estabilidade por ID
  getStabilityAnalysisById(id: string) {
    const url = `/stability-analysis/${id}`;
    return this.api.get<any>(url, {}, false, 'client');
  }

  //Mapa de Estabilidade - Ver Análise
  getStabilityAnalysisSectionId(id: string) {
    const url = `/stability-analysis/section/${id}`;
    return this.api.get<any>(url, {}, false, 'client');
  }

  //Download arquivo .zip
  getStabilityAnalysisZipFile(url: string = '', id: string = '', id2: string = '') {
    if (url == '') {
      url = `/stability-analysis/${id}/safety-factor/${id2}/zip-file`;
    }
    return this.api.get<any>(url, {}, true, 'client');
  }

  //Gráfico de Estabilidade - Ver Análise - Exibir DXF
  getSafetyFactorId(id: string = '', params: any = {}, url: string = '') {
    if (url == '') {
      url = `/stability-analysis/safety-factor/${id}/base64`;
    }
    return this.api.get<any>(url, params, false, 'client');
  }

  getStabilityAnalysisDate(id: string, params: any = {}) {
    const url = `/structures/${id}/stability-analysis-date`;
    return this.api.get<any>(url, params, false, 'client');
  }
}
