<form [formGroup]="formReadings">
  <div class="d-flex mt-2">
    <div class="col align-self-start">
      <div
        class="form-check"
        [style.visibility]="
          [4, 5, 6, 7].includes(typeInstrument.id) ? 'show' : 'hidden'
        "
      >
        <input
          class="form-check-input"
          type="checkbox"
          value=""
          formControlName="is_referencial"
          (change)="checkIsReferencial($event)"
        />
        <label class="form-label">Referência</label>
      </div>
    </div>
    <div class="col align-self-end">
      <div class="form-check d-flex justify-content-end">
        <app-button
          [class]="'btn-logisoil-remove-item-outside'"
          [icon]="'fa fa-trash'"
          [ngbTooltip]="'Excluir'"
          placement="bottom-right"
          (click)="removeForm(uidForm)"
          *ngIf="!edit && !view"
        >
        </app-button>
      </div>
    </div>
  </div>
</form>
<!-- Formulários dos instrumentos -->
<ng-template
  ngFor
  let-instrumentItem
  [ngForOf]="instrumentsReading"
  let-i="index"
>
  <app-ina-pz
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 1 || typeInstrument.id == 2"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    [datetime]="datetime"
  ></app-ina-pz>
  <app-pze
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 3"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    [datetime]="datetime"
  ></app-pze>
  <app-inc-conv
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 4"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    (executeCalc)="calcReading($event)"
    [datetime]="datetime"
    [references]="references"
  ></app-inc-conv>
  <app-inc-ipi
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 5"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    (executeCalc)="calcReading($event)"
    [datetime]="datetime"
    [references]="references"
  ></app-inc-ipi>
  <app-ms-pr
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 6 || typeInstrument.id == 7"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    [datetime]="datetime"
    [references]="references"
  ></app-ms-pr>
  <app-mr
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 8"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    (executeCalc)="calcReading($event)"
    [datetime]="datetime"
    [references]="references"
  ></app-mr>
  <app-geo
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 9"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    [datetime]="datetime"
    [structure]="structure"
    (showMap)="showMap($event)"
  ></app-geo>
  <app-rl
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 10"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    [datetime]="datetime"
  ></app-rl>
  <app-praia
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 11"
    [sectionsList]="i == 0 ? sections : []"
    [index]="i"
    (setSection)="selectedSection($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    [datetime]="datetime"
  ></app-praia>
  <app-pluviometro
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 12"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    [datetime]="datetime"
  ></app-pluviometro>
  <app-pluviografo
    #readingInstrumentRef
    *ngIf="typeInstrument.id == 13"
    [instrumentsList]="i == 0 ? instruments : []"
    [index]="i"
    (setInstrument)="selectedInstrument($event)"
    [edit]="edit"
    [view]="view"
    [spreadsheet]="spreadsheet"
    [data]="instrumentItem"
    [units]="unitField"
    [typeInstrument]="typeInstrument"
    [datetime]="datetime"
  ></app-pluviografo>
</ng-template>
