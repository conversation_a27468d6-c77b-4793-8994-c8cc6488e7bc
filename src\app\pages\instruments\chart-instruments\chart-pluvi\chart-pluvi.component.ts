import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { MessageInputInvalid } from 'src/app/constants/message.constants';
import { PeriodsPluv, Aggregation } from 'src/app/constants/chart.constants';

import { ChartService as ChartServiceApi } from 'src/app/services/api/chart.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';

import fn from 'src/app/utils/function.utils';

import * as moment from 'moment';
import * as _ from 'lodash';

@Component({
  selector: 'app-chart-pluvi',
  templateUrl: './chart-pluvi.component.html',
  styleUrls: ['./chart-pluvi.component.scss']
})
export class ChartPluviComponent implements OnInit {
  public formChart: FormGroup = new FormGroup({
    instrument: new FormControl([]),
    start_date: new FormControl('', [Validators.required]),
    end_date: new FormControl('', [Validators.required]),
    interval: new FormControl('', [Validators.required]),
    aggregation: new FormControl('', [Validators.required]),
    graphic_type: new FormControl('line'),
    color: new FormControl('#000000'),
    chart_height: new FormControl(300),
    //Intervalo eixo X
    xAxis_rotate: new FormControl(45),
    xAxis_interval: new FormControl(1)
  });

  public instrumentId: any = null;
  public instruments: any = [];
  public instrumentsSettings = MultiSelectDefault.Instruments;
  public periods = PeriodsPluv;
  public aggregation = Aggregation;

  public showColorPicker: boolean[] = [false];
  public selectedColor: string[] = ['#000000'];

  public messageReturn: any = { text: '', status: false };

  public xAxis: any = [];
  public yAxis: any = [];

  public chart: any = {};
  public chartSeries: any = [];
  public chartLegends: any = [];
  public chartLegendsTop: number = 0;
  public chartLegendsBottom: number = 0;

  public minMax: any = {
    a: { min: null, max: null }
  };

  public controls: any = null;
  public ctrlConfiguration: boolean = false;
  public yAxisDescription: any = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private chartServiceApi: ChartServiceApi,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private router: Router
  ) {}

  /**
   * Método de inicialização do componente.
   * Configura os controles e carrega a lista de instrumentos.
   */
  ngOnInit(): void {
    this.controls = this.formChart.controls;
    this.instrumentsSettings.singleSelection = true;
    this.getInstruments();
  }

  /**
   * Obtém a lista de instrumentos com base na estrutura e tipo de instrumento especificados.
   * Define o instrumento selecionado nos controles do formulário.
   */
  getInstruments() {
    this.instrumentId = this.activatedRoute.snapshot.params.instrumentId;

    let params = { StructureId: this.activatedRoute.snapshot.queryParams.structure, Type: this.activatedRoute.snapshot.queryParams.typeInstrument };
    this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
      let dados: any = resp;

      if (dados.status == 200) {
        dados = dados.body === undefined ? dados : dados.body;
        this.instruments = dados;
        let instrumentInfo = fn.findIndexInArrayofObject(this.instruments, 'id', this.activatedRoute.snapshot.params.instrumentId, 'identifier', true);
        this.controls['instrument'].setValue([instrumentInfo]);
      }
    });
  }

  /**
   * Obtém os dados do gráfico de percolação com base nos instrumentos selecionados.
   * @param {string|null} id - ID opcional para o gráfico.
   */
  getChart(id = null) {
    this.resetConfigurations();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    let params = {};

    if (this.controls['start_date'].value != '') {
      params['StartDate'] = moment(this.controls['start_date'].value).format('YYYY-MM-DD');
    }

    if (this.controls['end_date'].value != '') {
      params['EndDate'] = moment(this.controls['end_date'].value).format('YYYY-MM-DD');
    }

    if (this.controls['interval'].value != '') {
      params['Interval'] = this.controls['interval'].value;
    }

    if (this.controls['aggregation'].value != '') {
      params['Aggregation'] = this.controls['aggregation'].value;
    }

    this.chartServiceApi.getChartPluv(this.instrumentId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      if (dados) {
        this.constructChart(dados);
      } else {
        this.messageReturn.text = MessageInputInvalid.NoChart;
        this.messageReturn.status = true;

        setTimeout(() => {
          this.messageReturn.status = false;
        }, 4000);
      }
    });
  }

  //Constrói o gráfico a partir dos dados fornecidos.
  constructChart(data) {
    this.minMax = {
      a: { min: 0, max: 0 }
    };

    this.constructXAxis(data);
  }

  //Prepara os dados para os eixos Y do gráfico.
  constructXAxis(data) {
    this.xAxis = [];

    data.readings.map((reading) => {
      this.xAxis.push(reading.date);
    });

    this.xAxis = fn.arrayUnique(this.xAxis);
    this.xAxis.sort(function (a, b) {
      return a - b; //Isso fará com que os elementos sejam ordenados de forma crescente
    });

    this.constructSeries(data);
  }

  //Constrói as séries de dados do gráfico.
  constructSeries(data) {
    const datesObject = {};
    this.chartSeries = [];
    this.chartLegends = [];

    for (const date of this.xAxis) {
      datesObject[date] = null;
    }

    let series = {};

    const typeInstrument = this.activatedRoute.snapshot.queryParams.typeInstrument;

    this.yAxisDescription = typeInstrument == 12 ? 'Pluviometria Diária (mm)' : 'Pluviometria por Instrumento (mm)';
    series[this.yAxisDescription] = _.cloneDeep(datesObject);

    data.readings.map((reading) => {
      const date = reading.date;
      const pluviometry = reading.pluviometry;
      series[this.yAxisDescription][date] = series[this.yAxisDescription].hasOwnProperty(date) ? pluviometry : null;
    });

    this.chartLegendsTop = 50;
    this.chartLegendsBottom = 20;
    this.controls['chart_height'].setValue(Math.floor(this.chartLegendsTop) + 720);

    for (const key in series) {
      const itemSeries = {
        name: this.yAxisDescription,
        type: this.controls['graphic_type'].value,
        data: Object.values(series[this.yAxisDescription]),
        connectNulls: true,
        itemStyle: {
          color: this.controls['color'].value
        },
        smooth: false
      };
      this.chartSeries.push(itemSeries);
      this.defineMinMax(itemSeries.data, 'a');
    }
    this.constructYAxis();
  }

  //Prepara os dados para os eixos Y do gráfico.
  constructYAxis() {
    this.yAxis = [];

    let itemYAxis = {
      name: this.yAxisDescription,
      type: 'value',
      axisLine: {
        show: true
      },
      nameRotate: 90,
      nameLocation: 'center',
      nameGap: 55,
      nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
      alignTicks: true,
      axisLabel: {
        formatter: function (value, index) {
          return value.toFixed(1);
        }
      },
      show: true,
      interval: Math.ceil((this.minMax.a.max - this.minMax.a.min) / 21),
      min: this.minMax.a.min,
      max: this.minMax.a.max
    };

    this.yAxis.push(itemYAxis);
    this.generateChart();
  }

  /**
   * Executa ações quando um clique é detectado fora de um elemento específico.
   * @param {string} element - Elemento que detectou o clique fora.
   * @param {number} index - Índice do elemento.
   */
  onClickedOutside(element: string, index = 0) {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker[index] = false;
        break;
    }
  }

  /**
   * Atualiza a cor selecionada após a conclusão da seleção de cor.
   * @param {any} $event - Evento de seleção de cor.
   * @param {number} index - Índice do controle de cor.
   */
  changeComplete($event, index = 0) {
    this.selectedColor[index] = $event.color.hex;
    this.controls[`color`].setValue(this.selectedColor[index]);
  }

  //Limpa o formulário das configurações do gráfico.
  resetConfigurations() {
    this.xAxis = [];
    this.yAxis = [];

    this.chart = {};
    this.chartSeries = [];
    this.chartLegends = [];
    this.chartLegendsTop = 0;
    this.chartLegendsBottom = 0;

    this.minMax = {
      a: { min: null, max: null }
    };
  }

  /**
   * Define os valores mínimos e máximos dos dados da série para o eixo Y.
   * @param {any} array - Dados da série.
   * @param {number} index - Índice do eixo Y.
   */
  defineMinMax(array: any, letter = 'a', index = null) {
    array = array.filter((item) => item !== null && item !== undefined);

    array = array.map((item) => {
      if (item != null && item != undefined) {
        return typeof item == 'number' ? item : item.value;
      }
    });

    const min = Math.min(...array);
    const max = Math.max(...array);
    let previous = min - (min % 10);
    let next = max + (10 - (max % 10));

    this.minMax[letter].min = this.minMax[letter].min == null ? previous : this.minMax[letter].min;
    this.minMax[letter].min = Math.min(this.minMax[letter].min, previous);
    previous = this.minMax[letter].min;

    this.minMax[letter].max = this.minMax[letter].max == null ? next : this.minMax[letter].max;
    this.minMax[letter].max = Math.max(this.minMax[letter].max, next);
    next = this.minMax[letter].max;
  }

  //Define a altura do gráfico.
  setHeight(height: string): void {
    this.controls['chart_height'].setValue(parseInt(height));
    this.generateChart();
  }

  /**
   * Converte o arquivo SVG para texto e substitui suas cores.
   * @param {string} svg - Conteúdo SVG.
   * @param {string} color - Cor para substituição.
   * @returns {string} - SVG convertido para base64.
   */
  getSvgWithReplacedValue(svg, color = '#000000') {
    svg = this.replaceMultipleOccurrences(svg, ['rgb(0,0,0)', 'rgb(101,101,101)'], [color, color]);
    const svgBase64 = btoa(svg);
    return `data:image/svg+xml;base64,${svgBase64}`;
  }

  /**
   * Substitui todas as ocorrências de uma string por outra em um texto.
   * @param {string} text - Texto original.
   * @param {string[]} oldValues - Array de valores a serem substituídos.
   * @param {string[]} newValues - Array de novos valores para substituição.
   * @returns {string} - Texto com as substituições aplicadas.
   */
  replaceMultipleOccurrences(text, oldValues, newValues) {
    if (oldValues.length !== newValues.length) {
      throw new Error('Os arrays devem ter o mesmo comprimento.');
    }

    let newText = text;
    for (let i = 0; i < oldValues.length; i++) {
      const oldValue = oldValues[i];
      const newValue = newValues[i];
      newText = newText.split(oldValue).join(newValue);
    }

    return newText;
  }

  /**
   * Gera uma cor hexadecimal aleatória.
   * @returns {string} - Cor hexadecimal.
   */
  randomHexColor() {
    const randomColorComponent = () => {
      const component = Math.floor(Math.random() * 256); //Valor aleatorio entre 0  e 255
      return component.toString(16).padStart(2, '0'); //Converte para hexadecimal e completa com zero se necessario
    };

    const r = randomColorComponent();
    const g = randomColorComponent();
    const b = randomColorComponent();

    return `#${r}${g}${b}`;
  }

  //Gera e configura o gráfico usando as opções definidas.
  generateChart() {
    this.chart['options'] = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegends,
        bottom: this.chartLegendsBottom,
        icon: 'rect',
        left: 'center'
      },
      grid: {
        containLabel: true,
        top: this.chartLegendsTop,
        left: '50',
        right: '50',
        bottom: '0',
        height: this.controls['chart_height'].value
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: this.controls['graphic_type'].value == 'bar' ? true : false,
        data: this.xAxis,
        axisLabel: {
          interval: this.controls['xAxis_interval'].value - 1, // Define o intervalo para exibir todos os valores do eixo X
          rotate: this.controls['xAxis_rotate'].value
        }
      },
      yAxis: this.yAxis,
      series: this.chartSeries
    };
  }

  /**
   * Altera o instrumento selecionado e redefine as configurações do gráfico.
   * @param {any} $event - Evento contendo o instrumento selecionado.
   * @param {string} action - Ação a ser tomada, padrão é 'select'.
   */
  changeInstrument($event, action: string = 'select') {
    if (action === 'select') {
      this.instrumentId = $event.id;
      this.resetConfigurations();
    }
  }

  //Navega de volta para a tela de instrumentos.
  goBack() {
    this.router.navigate(['/instruments']);
  }
}
