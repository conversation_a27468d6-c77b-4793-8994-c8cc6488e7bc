import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-fixed-simulation',
  templateUrl: './modal-fixed-simulation.component.html',
  styleUrls: ['./modal-fixed-simulation.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalFixedSimulationComponent implements OnInit, OnChanges {
  @ViewChild('modalFixedSimulation') ModalFixedSimulation: ElementRef;

  @Input() public simulationItem: any = '';
  @Input() public title: any = '';
  @Output() public sendClickEvent = new EventEmitter();

  public formFixedSimulation: FormGroup = new FormGroup({
    Name: new FormControl('', [Validators.required])
  });

  public controls: any = [];

  public messagesError: any = null;

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {
    this.controls = this.formFixedSimulation.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.simulationItem.currentValue != null) {
      this.controls['Name'].setValue(changes.simulationItem.currentValue.name);
    }
  }

  openModal() {
    this.modalService.open(this.ModalFixedSimulation, { size: 'md' });
  }

  getForm() {
    this.sendClickEvent.emit({ action: this.simulationItem.option, form: this.formFixedSimulation.controls, item: this.simulationItem });
  }
}
