import { Component, Input, OnInit } from '@angular/core';

import { ActionResultClassification, Anomaly } from 'src/app/constants/inspections.constants';

@Component({
  selector: 'app-previous-situation',
  templateUrl: './previous-situation.component.html',
  styleUrls: ['./previous-situation.component.scss']
})
export class PreviousSituationComponent implements OnInit {
  @Input() public inspectionSheetType: number = null;
  @Input() public view: boolean = false;

  public previousSituation = Anomaly;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Identificação',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['identifier']
    },
    {
      label: 'Situação',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['situation']
    },
    {
      label: 'Pontuação',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['score']
    },
    {
      label: 'Observações',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['notes']
    }
  ];
  constructor() {}

  ngOnInit(): void {}

  /**
   * Configura os dados recebidos e os mapeia para exibição na tabela de situação anterior.
   * @param {any} dados - Dados contendo a situação anterior e anomalias identificadas.
   */
  setData(dados) {
    if (dados?.previous_situation) {
      this.tableData = Object.entries(dados.previous_situation).map(([key, value]) => {
        const anomalyItem = this.getAnomalyDetails(dados.identified_anomalies, Anomaly[key].id);
        return { identifier: Anomaly[key].name, situation: anomalyItem.classificationLabel, status: '-', score: value ?? '-', notes: anomalyItem.note }; // Retorna um novo objeto ou algo que você precisar
      });
    }
  }

  /**
   * Obtém os detalhes de uma anomalia específica.
   * @param {any[]} anomalyArray - Lista de anomalias identificadas.
   * @param {any} anomalyValue - Valor da anomalia a ser buscada.
   * @returns {{ message: string; classificationLabel: string; note: string }} - Detalhes da anomalia, incluindo a classificação e nota.
   */
  getAnomalyDetails(anomalyArray, anomalyValue) {
    // Encontra o objeto correspondente no data
    const matchedObject = anomalyArray.find((item) => item.anomaly === anomalyValue);

    if (!matchedObject) {
      return { message: '-', classificationLabel: '-', note: '-' };
    }

    // Encontra o label correspondente no ActionResultClassification
    const classificationLabel = ActionResultClassification.find((item) => item.value === matchedObject.action_result_classification)?.label || '-';

    return {
      classificationLabel,
      note: matchedObject.note ?? '-'
    };
  }
}
