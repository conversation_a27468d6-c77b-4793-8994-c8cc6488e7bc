.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-control,
.form-select,
.form-check-label {
  font-size: 0.875em;
}

.nav,
.nav-tabs,
.nav-link active {
  font-size: 0.875em;
}

.buttons {
  margin-top: 29px;
}

.accordion-button {
  display: flex; /* Garante que o conteúdo interno seja flexível */
  align-items: center; /* Alinha verticalmente o conteúdo */
  justify-content: space-between; /* Mantém o texto e ícones bem posicionados */
  border: 1px solid #ced4da;
  height: auto !important; /* Permite que o botão ajuste sua altura dinamicamente */
  padding: 0.5rem 1rem; /* Ajuste de padding */
  border-radius: 0.25rem !important;
  font-size: 0.875em;
  &:not(.collapsed) {
    background-color: #fff;
  }
}

.accordion-body {
  padding: 1rem; /* Ajusta o espaçamento interno */
  margin-top: -1px; /* Remove espaço extra entre o botão e o corpo */
  border: 1px solid #ced4da;
  border-top: none; /* Remove a borda superior para continuidade com o botão */
  border-radius: 0 0 0.25rem 0.25rem !important;
  font-size: 0.875em;
}

.accordion-body > * {
  max-width: 100%; /* Garante que os elementos não ultrapassem os limites */
  overflow: hidden; /* Esconde conteúdo que ultrapasse os limites */
}
