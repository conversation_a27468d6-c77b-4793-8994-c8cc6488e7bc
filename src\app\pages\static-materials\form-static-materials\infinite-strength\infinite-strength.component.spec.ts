import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InfiniteStrengthComponent } from './infinite-strength.component';

describe('InfiniteStrengthComponent', () => {
  let component: InfiniteStrengthComponent;
  let fixture: ComponentFixture<InfiniteStrengthComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ InfiniteStrengthComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InfiniteStrengthComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
