import { Injectable } from '@angular/core';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { NotificationsService as NotificationsServiceApi } from '../services/api/notifications.service';
import { HttpResponse } from '@angular/common/http';
import { NotificationTheme } from '../constants/notifications.constants';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notificationsSubject = new BehaviorSubject<{ notifications: any[]; count: number }>({ notifications: [], count: 0 });
  public notifications$ = this.notificationsSubject.asObservable();

  private notificationsBannerSubject = new BehaviorSubject<{
    instrumentationNotifications: any[];
    readingNotifications: any[];
    stabilityNotifications: any[];
    licenseNotifications: any[];
    inspectionsNotifications: any[];
  }>({
    instrumentationNotifications: [],
    readingNotifications: [],
    stabilityNotifications: [],
    licenseNotifications: [],
    inspectionsNotifications: []
  });

  public notificationsBanner$ = this.notificationsBannerSubject.asObservable();

  public bannerVisibilitySubject = new BehaviorSubject<{
    instrumentationBannerStatus: boolean;
    readingBannerStatus: boolean;
    stabilityBannerStatus: boolean;
    licenseBannerStatus: boolean;
    inspectionsBannerStatus: boolean;
  }>({
    instrumentationBannerStatus: true,
    readingBannerStatus: true,
    stabilityBannerStatus: true,
    licenseBannerStatus: true,
    inspectionsBannerStatus: true
  });

  public bannerVisibility$ = this.bannerVisibilitySubject.asObservable();

  private page: number = 1;
  private pageSize: number = 10;
  private type: number = 2;

  constructor(private notificationsServiceApi: NotificationsServiceApi) {
    this.fetchNotifications();
    this.fetchBannerNotifications();
    this.intervalFetchNotifications();
  }

  getNotifications(params: any): Observable<any> {
    return this.notificationsServiceApi.getNotifications(params);
  }

  setUserNotificationConfigurations(configurations: any) {
    this.notificationsServiceApi.postUserNotificationConfigurations(configurations).subscribe((resp) => {
      if (resp instanceof HttpResponse && resp.status !== 200) {
        if (resp) {
        }
      }
    });
  }

  fetchNotifications(notificationType: number = this.type, notificationPage: number = this.page, notificationPageSize: number = this.pageSize) {
    this.page = notificationPage;
    this.pageSize = notificationPageSize;
    this.type = notificationType;

    const params = {
      Type: notificationType,
      Page: notificationPage,
      PageSize: notificationPageSize
    };

    this.notificationsServiceApi.getNotifications(params).subscribe((resp) => {
      if (resp instanceof HttpResponse) {
        if (resp.status == 200) {
          this.notificationsSubject.next({
            notifications: resp.body.data ? resp.body.data : [],
            count: resp.body.total_items_count ? resp.body.total_items_count : 0
          });
        } else {
          this.notificationsSubject.next({ notifications: [], count: 0 });
        }
      }
    });
  }

  fetchUserNotificationsConfigurations(): Observable<any> {
    return this.notificationsServiceApi.getUserNotificationConfigurations();
  }

  fetchBannerNotifications() {
    const params = {
      Type: 1,
      Page: 1,
      PageSize: 30
    };

    this.notificationsServiceApi.getNotifications(params).subscribe((resp) => {
      if (resp instanceof HttpResponse) {
        if (resp.status == 200 && resp.body) {
          let rawBannerNotifications = resp.body.data;

          let newInstrumentationBannerNotifications = this.processInstrumentationBannerNotification(rawBannerNotifications);
          let newReadingBannerNotifications = this.processReadingsBannerNotifications(rawBannerNotifications);
          let newStabilityBannerNotifications = this.processStabilityBannerNotifications(rawBannerNotifications);
          let newLicenseBannerNotifications = this.processLicenseBannerNotifications(rawBannerNotifications);
          let newInspectionsBannerNotifications = this.processInspectionsBannerNotifications(rawBannerNotifications);

          this.notificationsBannerSubject.next({
            instrumentationNotifications: newInstrumentationBannerNotifications,
            readingNotifications: newReadingBannerNotifications,
            stabilityNotifications: newStabilityBannerNotifications,
            licenseNotifications: newLicenseBannerNotifications,
            inspectionsNotifications: newInspectionsBannerNotifications
          });
        } else {
          this.notificationsBannerSubject.next({
            instrumentationNotifications: [],
            readingNotifications: [],
            stabilityNotifications: [],
            licenseNotifications: [],
            inspectionsNotifications: []
          });
        }
      }
    });
  }

  intervalFetchNotifications() {
    setInterval(() => {
      this.fetchNotifications();
      this.fetchBannerNotifications();
    }, 930000);
  }

  markAsRead(notificationsIds: any[]) {
    this.notificationsServiceApi.putReadNotifications(notificationsIds).subscribe(() => {
      this.page = 1;
      this.pageSize = 10;
      this.type = 2;
      this.fetchNotifications();
    });
  }

  markAllAsRead() {
    this.notificationsServiceApi.putAllReadNotifications({}).subscribe(() => {
      this.page = 1;
      this.pageSize = 10;
      this.type = 2;
      this.fetchNotifications();
    });
  }

  processInstrumentationBannerNotification(notifications: any[]): any[] {
    let instrumentationNotification: any[] = [];

    let createdInstruments = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.InstrumentCreated);

    if (createdInstruments.length > 0) {
      instrumentationNotification.push({
        theme: NotificationTheme.InstrumentCreated,
        message:
          createdInstruments.length > 1
            ? `${createdInstruments.length} novos instrumentos foram cadastrados!`
            : `${createdInstruments.length} novo instrumento cadastrado!`
      });
    }

    let updatedInstruments = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.InstrumentUpdated);

    if (updatedInstruments.length > 0) {
      instrumentationNotification.push({
        theme: NotificationTheme.InstrumentUpdated,
        message:
          updatedInstruments.length > 1
            ? `${updatedInstruments.length} instrumentos foram atualizados!`
            : `${updatedInstruments.length} instrumento atualizado!`
      });
    }

    let deletedInstruments = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.InstrumentDeleted);

    if (deletedInstruments.length > 0) {
      instrumentationNotification.push({
        theme: NotificationTheme.InstrumentDeleted,
        message:
          deletedInstruments.length > 1 ? `${deletedInstruments.length} instrumentos foram excluídos!` : `${deletedInstruments.length} instrumento excluído!`
      });
    }

    let damagedInstruments = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.InstrumentDamaged);

    if (damagedInstruments.length > 0) {
      instrumentationNotification.push({
        theme: NotificationTheme.InstrumentDamaged,
        message:
          damagedInstruments.length > 1
            ? `${damagedInstruments.length} instrumentos foram marcados como avariados!`
            : `${damagedInstruments.length} instrumento foi marcado como avariado!`
      });
    }

    let repairedInstruments = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.InstrumentRepaired);

    if (repairedInstruments.length > 0) {
      instrumentationNotification.push({
        theme: NotificationTheme.InstrumentRepaired,
        message:
          repairedInstruments.length > 1
            ? `${repairedInstruments.length} instrumentos foram marcados como reparados!`
            : `${repairedInstruments.length} instrumento foi marcado como restaurado!`
      });
    }
    return instrumentationNotification;
  }

  processReadingsBannerNotifications(notifications: any[]): any[] {
    let readingNotifications: any[] = [];

    let createdReadings = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.ReadingCreated);

    if (createdReadings.length > 0) {
      readingNotifications.push({
        theme: NotificationTheme.ReadingCreated,
        message: createdReadings.length > 1 ? `${createdReadings.length} novas leituras foram inseridas!` : `${createdReadings.length} nova leitura inserida!`
      });
    }

    let updatedReadings = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.ReadingUpdated);

    if (updatedReadings.length > 0) {
      readingNotifications.push({
        theme: NotificationTheme.ReadingUpdated,
        message: updatedReadings.length > 1 ? `${updatedReadings.length} leituras foram atualizadas!` : `${updatedReadings.length} leitura atualizada!`
      });
    }

    let deletedReadings = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.ReadingDeleted);

    if (deletedReadings.length > 0) {
      readingNotifications.push({
        theme: NotificationTheme.ReadingDeleted,
        message: deletedReadings.length > 1 ? `${deletedReadings.length} leituras foram excluídas!` : `${deletedReadings.length} leitura excluída!`
      });
    }

    let controlLetterAlerts = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.ControlLetterNewRegistry);

    if (controlLetterAlerts.length > 0) {
      readingNotifications.push({
        theme: NotificationTheme.ControlLetterNewRegistry,
        message:
          controlLetterAlerts.length > 1
            ? `${controlLetterAlerts.length} novos registros de Carta de Controle foram inseridos!`
            : `${controlLetterAlerts.length} novo registro de Carta de Controle inserido!`
      });
    }

    return readingNotifications;
  }

  processStabilityBannerNotifications(notifications: any[]): any[] {
    let stabilityNotifications: any[] = [];

    let securityLevelCreated = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.SecurityLevelCreated);

    if (securityLevelCreated.length > 0) {
      stabilityNotifications.push({
        theme: NotificationTheme.SecurityLevelCreated,
        message:
          securityLevelCreated.length > 1
            ? `${securityLevelCreated.length} novos registros de fator de Segurança foram criados!`
            : `${securityLevelCreated.length} novo registro de fator de Segurança criado!`
      });
    }

    let securityLevelAboveTolerance = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.SecurityLevelAboveTolerance);

    if (securityLevelAboveTolerance.length > 0) {
      stabilityNotifications.push({
        theme: NotificationTheme.SecurityLevelAboveTolerance,
        message:
          securityLevelAboveTolerance.length > 1
            ? `${securityLevelAboveTolerance.length} registros de fator de Segurança ultrapassaram a tolerância!`
            : `${securityLevelAboveTolerance.length} registro de fator de Segurança ultrapassou a tolerência!`
      });
    }

    let drainageAlert = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.DrainageAlert);

    if (drainageAlert.length > 0) {
      stabilityNotifications.push({
        theme: NotificationTheme.DrainageAlert,
        message:
          drainageAlert.length > 1
            ? `${drainageAlert.length} alertas de Gradiente Hidráulico foram criados!`
            : `${drainageAlert.length} alerta de Gradiente Hidráulico drenagem criado!`
      });
    }

    return stabilityNotifications;
  }

  processLicenseBannerNotifications(notifications: any[]): any[] {
    let licenseNotifications: any[] = [];

    let licenseCreated = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.License);

    if (licenseCreated.length > 0) {
      licenseNotifications.push({
        theme: NotificationTheme.License,
        message:
          licenseCreated.length > 1
            ? `Existem ${licenseCreated.length} licenças do Logisoil expirando.`
            : `Existe ${licenseCreated.length} licença do Logisoil expirando.`
      });
    }

    return licenseNotifications;
  }

  processInspectionsBannerNotifications(notifications: any[]): any[] {
    let inspectionsNotifications: any[] = [];

    let inspectionCreated = notifications.filter((x: { theme: number }) => x.theme === NotificationTheme.OverdueInspection);

    if (inspectionCreated.length > 0) {
      inspectionsNotifications.push({
        theme: NotificationTheme.OverdueInspection,
        message: inspectionCreated.length > 1 ? `${inspectionCreated.length} novas inspeções vencidas.` : `${inspectionCreated.length} nova inspeção vencida.`
      });
    }

    return inspectionsNotifications;
  }

  handleCloseInstrumentationBanner() {
    let updatedVisibility = {
      ...this.bannerVisibilitySubject.getValue(),
      instrumentationBannerStatus: false
    };

    this.bannerVisibilitySubject.next(updatedVisibility);
  }

  handleCloseReadingBanner() {
    let updatedVisibility = {
      ...this.bannerVisibilitySubject.getValue(),
      readingBannerStatus: false
    };

    this.bannerVisibilitySubject.next(updatedVisibility);
  }

  handleCloseStabilityBanner() {
    let updatedVisibility = {
      ...this.bannerVisibilitySubject.getValue(),
      stabilityBannerStatus: false
    };

    this.bannerVisibilitySubject.next(updatedVisibility);
  }

  handleCloseLicenseBanner() {
    let updatedVisibility = {
      ...this.bannerVisibilitySubject.getValue(),
      licenseBannerStatus: false
    };

    this.bannerVisibilitySubject.next(updatedVisibility);
  }

  handleCloseInspectionsBanner() {
    let updatedVisibility = {
      ...this.bannerVisibilitySubject.getValue(),
      inspectionsBannerStatus: false
    };

    this.bannerVisibilitySubject.next(updatedVisibility);
  }
}
