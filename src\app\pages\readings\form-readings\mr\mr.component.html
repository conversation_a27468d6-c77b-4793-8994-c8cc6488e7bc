<form [formGroup]="formReading" class="mb-3">
  <div class="row">
    <!-- Instrumento -->
    <div class="col-md-3">
      <label class="form-label">Instrumento</label>
      <select
        class="form-select"
        formControlName="instrument"
        (change)="changeInstrument(formReading.controls['instrument'].value)"
      >
        <option value="" *ngIf="formReading.controls['instrument'].value == ''">
          Selecione...
        </option>
        <option
          *ngFor="let instrumentItem of instrumentsList"
          [ngValue]="instrumentItem.id"
        >
          {{ instrumentItem.identifier }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('instrument').valid &&
          formReading.get('instrument').touched &&
          !formReading.get('instrument').disabled
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- measure -->
    <div class="col-md-3">
      <label class="form-label">An<PERSON></label>
      <input type="text" class="form-control" formControlName="measure" />
    </div>

    <!-- Data e hora -->
    <div class="col-md-3">
      <label class="form-label">Data e hora</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('date').valid && formReading.get('date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- absolute_depth -->
    <div class="col-md-3">
      <label class="form-label">Profundidade absoluta</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="absolute_depth"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber($event, formReading.get('absolute_depth'))
          "
          (blur)="calcAbsolute('absolute_depth'); func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <span class="input-group-text">{{ units[0] }}</span>
      </div>
    </div>
  </div>

  <div class="row mt-2">
    <!-- delta_ref  Campo informativo -->
    <div class="col-md-3">
      <label class="form-label">Delta referência</label>
      <div class="input-group">
        <input type="text" class="form-control" formControlName="delta_ref" />
        <span class="input-group-text">m</span>
      </div>
    </div>

    <!-- Recalque absoluto -->
    <div class="col-md-3">
      <label class="form-label">Recalque absoluto</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="absolute_settlement"
          (blur)="calcAbsolute('absolute_settlement'); func.formatType($event)"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber($event, formReading.get('absolute_settlement'))
          "
          appDisableScroll
        />
        <span class="input-group-text">{{ units[0] }}</span>
      </div>
    </div>

    <!-- Recalque relativo-  Campo informativo -->
    <div class="col-md-3">
      <label class="form-label">Recalque relativo</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="relative_settlement"
        />
        <span class="input-group-text">mm</span>
      </div>
    </div>

    <!-- Cota - Campo informativo-->
    <div class="col-md-3">
      <label class="form-label">Cota</label>
      <div class="input-group">
        <input type="text" class="form-control" formControlName="quota" /><span
          class="input-group-text"
          >m</span
        >
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('quota').valid && formReading.get('quota').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>
</form>
