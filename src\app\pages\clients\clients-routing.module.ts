import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListClientsComponent } from './list-clients/list-clients.component';
import { RegisterClientComponent } from './register-client/register-client.component';

import { AppGuard } from './../../guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListClientsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastrarCliente,
    component: RegisterClientComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarCliente,
    component: RegisterClientComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ClientsRoutingModule {}
