import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class CoordinateConversionsService {
  constructor(private api: ApiService) {}

  postCoordinateUTM(params: any) {
    const url = '/coordinate-conversions/utm';
    return this.api.post<any>(url, params, {}, 'client');
  }

  postCoordinateGeodetic(params: any) {
    const url = '/coordinate-conversions/decimal-geodetic';
    return this.api.post<any>(url, params, {}, 'client');
  }

  postCoordinateDatum(params: any) {
    const url = '/coordinate-conversions/datum';
    return this.api.post<any>(url, params, {}, 'client');
  }
}
