<form [formGroup]="formChart">
  <div class="row g-3">
    <div class="col-md-12 d-flex align-items-center justify-content-center">
      <div class="col-md-12 d-flex align-items-center justify-content-center">
        <div class="btn-group" role="group">
          <ng-template ngFor let-navbarItem [ngForOf]="navbar" let-i="index">
            <button
              type="button"
              class="btn btn-outline-primary"
              (click)="changePeriod(navbarItem.month)"
            >
              {{ navbarItem.label }}
            </button>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-2 mb-3">
    <div class="col-md-12" *ngIf="chart.options">
      <app-e-charts
        [dataChart]="chart"
        [height]="formChart.controls['chart_height'].value"
      ></app-e-charts>
    </div>
  </div>
  <div class="row mt-2" *ngIf="data != null">
    <div class="col-md-12 d-flex align-items-center justify-content-center">
      <table class="table">
        <thead>
          <tr>
            <th scope="col">Variação absoluta (m)</th>
            <th scope="col">Máximo</th>
            <th scope="col">Mínimo</th>
            <th scope="col">Média</th>
            <th scope="col">Desvio padrão</th>
            <th scope="col">Última leitura</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th
              scope="row"
              [style.color]="data.statistics.absolute_variation.color"
            >
              {{ data.statistics.absolute_variation.value }}
            </th>
            <td>{{ data.statistics.maximum }}</td>
            <td>{{ data.statistics.minimum }}</td>
            <td>{{ data.statistics.average }}</td>
            <td>{{ data.statistics.standard_deviation }}</td>
            <td>{{ data.statistics.last_reading }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</form>
