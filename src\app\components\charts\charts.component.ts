import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { ChartConfiguration, ChartOptions, ChartType } from 'chart.js';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-charts',
  templateUrl: './charts.component.html',
  styleUrls: ['./charts.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ChartsComponent implements OnInit {
  @Input() public dataChart: Subject<any>;

  public lineChartData: ChartConfiguration<'line'>['data'] = null;
  public lineChartOptions: ChartOptions<'line'> = null;
  public lineChartLegend: boolean = false;

  public paramsChart: any;
  public label_x: any = '';
  public label_y: any = '';

  constructor() {}

  ngOnInit() {
    this.dataChart.subscribe((params) => {
      this.showChart(params);
    });
  }

  showChart(params: any = {}) {
    this.lineChartData = {
      labels: params.data.x,
      datasets: [
        {
          data: params.data.y,
          label: params.label_y,
          fill: true,
          tension: 0.5,
          backgroundColor: 'rgba(77,83,96,0.2)',
          borderColor: 'rgba(77,83,96,1)',
          pointBackgroundColor: 'rgba(77,83,96,1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(77,83,96,1)'
          // fill: 'origin'
        }
      ]
    };
    this.lineChartOptions = {
      responsive: true
    };

    this.label_x = params.label_x;
    this.label_y = params.label_y;
  }
}
