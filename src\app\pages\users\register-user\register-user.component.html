<div class="list-content">
  <form class="row g-3 mt-1" [formGroup]="formUser" (ngSubmit)="validate()">
    <!-- Nome -->
    <div class="col-md-4" tourAnchor="first_name">
      <label class="form-label">Nome</label>
      <input
        type="text"
        formControlName="first_name"
        class="form-control"
        autocomplete="off"
        [attr.maxlength]="255"
        (input)="onValueChange($event, 'first_name')"
      />
      <small
        class="form-text text-danger d-block"
        *ngIf="
          !formUser.get('first_name').valid &&
          formUser.get('first_name').touched
        "
        >Campo Obrigatório.</small
      >
      <!-- Contador de caracteres -->
      <small class="form-text text-muted d-block"
        >Caracteres {{ charCounts['first_name'] || 0 }} de 255
      </small>
    </div>
    <!-- Sobrenome -->
    <div class="col-md-4" tourAnchor="surname">
      <label class="form-label">Sobrenome</label>
      <input
        type="text"
        formControlName="surname"
        class="form-control"
        autocomplete="off"
        [attr.maxlength]="255"
        (input)="onValueChange($event, 'surname')"
      />
      <small
        class="form-text text-danger d-block"
        *ngIf="
          !formUser.get('surname').valid && formUser.get('surname').touched
        "
        >Campo Obrigatório.</small
      >
      <!-- Contador de caracteres -->
      <small class="form-text text-muted d-block"
        >Caracteres {{ charCounts['surname'] || 0 }} de 255
      </small>
    </div>
    <!-- E-mail -->
    <div class="col-md-4" tourAnchor="email_address">
      <label class="form-label">Endereço de e-mail</label>
      <input
        type="email"
        formControlName="email_address"
        class="form-control"
        autocomplete="off"
        [attr.maxlength]="255"
        (input)="onValueChange($event, 'email_address')"
      />
      <small
        class="form-text text-danger d-block"
        *ngIf="
          !formUser.get('email_address').valid &&
          formUser.get('email_address').touched
        "
        >Digite um e-mail válido.</small
      >
      <!-- Contador de caracteres -->
      <small class="form-text text-muted d-block"
        >Caracteres {{ charCounts['email_address'] || 0 }} de 255
      </small>
    </div>
    <!-- Usuário -->
    <div class="col-md-4" tourAnchor="username">
      <label class="form-label">Usuário</label>
      <div class="input-group">
        <span class="input-group-text"><i class="fa fa-user"></i></span>
        <input
          type="text"
          class="form-control"
          formControlName="username"
          autocomplete="off"
          [attr.maxlength]="32"
          (input)="onValueChange($event, 'username')"
        />
      </div>
      <small
        class="form-text text-danger d-block"
        *ngIf="
          !formUser.get('username').valid && formUser.get('username').touched
        "
        >Campo Obrigatório.</small
      >
      <!-- Contador de caracteres -->
      <small class="form-text text-muted d-block"
        >Caracteres {{ charCounts['username'] || 0 }} de 32
      </small>
    </div>
    <!-- Nível de acesso -->
    <div class="col-md-4" tourAnchor="role">
      <label class="form-label">Nível de acesso</label>
      <select
        class="form-select"
        formControlName="role"
        #selectRole
        (change)="managerAssociation(selectRole.value, true)"
      >
        <option value="">Selecione...</option>
        <ng-container *ngFor="let item of roleUser">
          <option
            [value]="item.id"
            *ngIf="
              accessLevelEnum[item.id].level < conditionLevel &&
              (profile.description != 'super-support' && item.id == 'support'
                ? false
                : true)
            "
          >
            {{ item.label }}
          </option>
        </ng-container>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="!formUser.get('role').valid && formUser.get('role').touched"
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Idioma -->
    <div class="col-md-4" tourAnchor="locale">
      <label class="form-label">Idioma</label>
      <select class="form-select" formControlName="locale">
        <option value="">Selecione...</option>
        <ng-container *ngFor="let item of locales">
          <option [value]="item.id">
            {{ item.label }}
          </option>
        </ng-container>
      </select>
    </div>
    <!-- Associações -->
    <div
      class="alert alert-info"
      role="alert"
      *ngIf="this.formUser.get('role').value == 'super-support'"
    >
      <i class="fa fa-info-circle" aria-hidden="true"></i>
      Usuário do nível de acesso "Super Suporte", tem acesso a todas as
      associações.
    </div>
    <div
      class="col-md-12"
      *ngIf="this.formUser.get('role').value != 'super-support'"
    >
      <div class="card">
        <div class="card-header">Associar Usuário</div>
        <div class="card-body">
          <div class="row">
            <!-- Cliente -->
            <div class="col-md-4" tourAnchor="client">
              <label class="form-label">Cliente</label>
              <ng-multiselect-dropdown
                [placeholder]="'Selecione...'"
                [settings]="clientSettings"
                [data]="clients"
                formControlName="client"
                (onSelect)="managerClientUnits($event, 'select')"
                (onSelectAll)="managerClientUnits($event, 'selectAll')"
                (onDeSelect)="managerClientUnits($event, 'deselect')"
                (onDeSelectAll)="managerClientUnits($event, 'deselectAll')"
                [disabled]="clientDisabled"
              >
              </ng-multiselect-dropdown>
            </div>
            <!-- Unidades -->
            <div class="col-md-4" tourAnchor="clientUnit">
              <label class="form-label">Unidade(s)</label>
              <ng-multiselect-dropdown
                [placeholder]="'Selecione...'"
                [settings]="dropdownSettings"
                [data]="clientUnits"
                formControlName="clientUnit"
                (onSelect)="managerStructures($event, 'select')"
                (onSelectAll)="managerStructures($event, 'selectAll')"
                (onDeSelect)="managerStructures($event, 'deselect')"
                (onDeSelectAll)="managerStructures($event, 'deselectAll')"
                [disabled]="clientUnitDisabled"
              >
              </ng-multiselect-dropdown>
            </div>
            <!-- Estruturas -->
            <div class="col-md-4" tourAnchor="structure">
              <label class="form-label">Estrutura(s)</label>
              <ng-multiselect-dropdown
                [placeholder]="'Selecione...'"
                [settings]="dropdownSettings"
                [data]="structures"
                formControlName="structure"
                [disabled]="structureDisabled"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Alerta -->
    <div class="row mt-2">
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesError"
      ></app-alert>
    </div>
    <div
      class="alert mt-2"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
      [innerHTML]="message.text"
    ></div>

    <!-- Botões -->
    <div class="col-md-12 d-flex justify-content-end mb-3">
      <app-button
        tourAnchor="save_button"
        [class]="'btn-logisoil-green'"
        [icon]="'fa fa-floppy-o'"
        [label]="'Salvar'"
        [type]="false"
        [disabled]="!formUser.valid"
        *ngIf="
          (permissaoUsuario.edit || permissaoUsuario.create) &&
          !view &&
          formCrtl
        "
        class="me-1"
      >
      </app-button>
      <app-button
        tourAnchor="back_button"
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [routerLink]="['/users']"
      ></app-button>
    </div>
    <!-- Botões -->
  </form>

  <!-- Tabela de Histórico -->
  <ul class="nav nav-tabs px-2" *ngIf="view">
    <li class="nav-item">
      <a class="nav-link active" aria-current="page"
        >Histórico de alteração do usuário</a
      >
    </li>
  </ul>
  <div
    class="alert alert-warning mt-4"
    role="alert"
    *ngIf="messageReturn.status"
  >
    {{ messageReturn.text }}
  </div>
  <div class="mt-3">
    <app-table
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeader"
      [tableData]="tableData"
      [permissaoUsuario]="permissaoUsuario"
      *ngIf="view"
    >
    </app-table>
  </div>
  <!-- Tabela de Histórico -->
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>

<tour-step-template></tour-step-template>
