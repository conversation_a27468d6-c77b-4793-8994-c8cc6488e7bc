import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ClickOutsideModule } from 'ng4-click-outside';
import { ColorSketchModule } from 'ngx-color/sketch';

import { SharedModule } from '@components/shared.module';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgSelectModule } from '@ng-select/ng-select';
import { InstrumentsRoutingModule } from './instruments-routing.module';
import { LogisoilDirectivesModule } from 'src/app/shared/logisoil-directives.module';

import { ConsultSectionsComponent } from './consult-sections/consult-sections.component';
import { FormInstrumentsComponent } from './form-instruments/form-instruments.component';
import { GroupInstrumentsComponent } from './group-instruments/group-instruments.component';
import { HistoryInstrumentComponent } from './history-instrument/history-instrument.component';
import { HistoryReadingsComponent } from './history-readings/history-readings.component';
import { ImagesInstrumentComponent } from './images-instrument/images-instrument.component';
import { InsertReadingsComponent } from './insert-readings/insert-readings.component';
import { ListInstrumentsComponent } from './list-instruments/list-instruments.component';
import { MeasurementsComponent } from './measurements/measurements.component';
import { RegisterInstrumentComponent } from './register-instrument/register-instrument.component';
import { SecurityLevelsComponent } from './security-levels/security-levels.component';
import { HistoryTabsComponent } from './history-tabs/history-tabs.component';
import { ViewMapComponent } from './view-map/view-map.component';

//Grafico
import { ChartInstrumentsComponent } from './chart-instruments/chart-instruments.component';
import { ChartInaPzComponent } from './chart-instruments/chart-ina-pz/chart-ina-pz.component';
import { ChartIncIpiComponent } from './chart-instruments/chart-inc-ipi/chart-inc-ipi.component';
import { ChartRlComponent } from './chart-instruments/chart-rl/chart-rl.component';
import { ChartMrComponent } from './chart-instruments/chart-mr/chart-mr.component';
import { ChartMsPrComponent } from './chart-instruments/chart-ms-pr/chart-ms-pr.component';
import { ChartPluviComponent } from './chart-instruments/chart-pluvi/chart-pluvi.component';

@NgModule({
  declarations: [
    ConsultSectionsComponent,
    GroupInstrumentsComponent,
    FormInstrumentsComponent,
    HistoryInstrumentComponent,
    HistoryReadingsComponent,
    ImagesInstrumentComponent,
    InsertReadingsComponent,
    ListInstrumentsComponent,
    MeasurementsComponent,
    RegisterInstrumentComponent,
    SecurityLevelsComponent,
    HistoryTabsComponent,
    ViewMapComponent,
    //Grafico
    ChartInstrumentsComponent,
    ChartInaPzComponent,
    ChartIncIpiComponent,
    ChartRlComponent,
    ChartMrComponent,
    ChartMsPrComponent,
    ChartPluviComponent
  ],
  imports: [
    CommonModule,
    ClickOutsideModule,
    ColorSketchModule,
    FormsModule,
    NgbModule,
    ReactiveFormsModule,
    SharedModule,
    NgMultiSelectDropDownModule.forRoot(),
    NgSelectModule,
    InstrumentsRoutingModule,
    LogisoilDirectivesModule
  ]
})
export class InstrumentsModule {}
