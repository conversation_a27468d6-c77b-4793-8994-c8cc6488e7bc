import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class MapsService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  /*Mapa de Estabilidade*/
  //Busca os dados para o Mapa de Estabilidade por ID da estrutura
  getMapsStabilityById(id: string) {
    const url = `/maps/stability/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  postMapsStabilityConfiguration(params: any) {
    const url = `/maps/stability/stability-map-configuration`;
    return this.api.post<any>(url, params, {}, 'client');
  }

  /*Mapa de Deslocamento*/
  getMapsDisplacement(params: any) {
    const url = `/maps/displacement`;
    return this.api.get<any>(url, params, false, 'client');
  }

  /*Mapa de Percolação*/
  getMapsPercolation(params: any) {
    const url = `/maps/percolation`;
    return this.api.get<any>(url, params, false, 'client');
  }

  postPercolationAbsoluteVariationColor(params: any) {
    const url = '/maps/percolation/absolute-variation-color';
    return this.api.post<any>(url, params, {}, 'client');
  }

  postDisplacementMapConfiguration(params: any) {
    const url = '/maps/displacement/displacement-map-configuration';
    return this.api.post<any>(url, params, {}, 'client');
  }

  getURLKml(params: any = {}, url: string = '') {
    return this.api.get<any>(url, params, true, 'client');
  }
}
