<form [formGroup]="formSection">
  <div class="row g-3 mt-2">
    <!-- Nome da <PERSON>ção -->
    <div class="col-md-3">
      <label class="form-label">Nome da <PERSON></label>
      <input
        type="text"
        formControlName="name"
        class="form-control"
        [attr.maxlength]="32"
        autocomplete="off"
        (input)="onValueChange($event, 'name')"
      />
      <!-- Campo obrigatório -->
      <small
        class="form-text text-danger d-block"
        *ngIf="
          !formSection.get('name').valid && formSection.get('name').touched
        "
        >Campo Obrigatório.</small
      >
      <!-- Contador de caracteres -->
      <small class="form-text text-muted d-block"
        >Caracteres {{ charCounts['name'] || 0 }} de 32
      </small>
    </div>

    <!-- Cliente -->
    <div class="col-md-3">
      <label class="form-label">Cliente</label>
      <select
        class="form-select"
        formControlName="client"
        (change)="getUnits(formSection.get('client').value)"
      >
        <option value="">Selecione</option>
        <option *ngFor="let item of clients" [ngValue]="item.id">
          {{ item.name }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('client').valid && formSection.get('client').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Unidade -->
    <div class="col-md-3">
      <label class="form-label">Unidade</label>
      <select
        class="form-select"
        formControlName="client_unit"
        (change)="getStructureList(formSection.get('client_unit').value)"
      >
        <option value="">Selecione</option>
        <option *ngFor="let unit of units" [ngValue]="unit.id">
          {{ unit.name }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('client_unit').valid &&
          formSection.get('client_unit').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Estrutura -->
    <div class="col-md-3">
      <label class="form-label">Estrutura</label>
      <select
        class="form-select"
        formControlName="structure"
        (change)="getStructure(formSection.get('structure').value)"
      >
        <option value="">Selecione</option>
        <option *ngFor="let structure of structures" [ngValue]="structure.id">
          {{ structure.name }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('structure').valid &&
          formSection.get('structure').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Azimute -->
    <div class="col-md-3">
      <label class="form-label">Azimute (graus)</label>
      <input
        type="text"
        formControlName="normal_line_azimuth"
        class="form-control"
        min="0"
        max="360"
        step="0.0000001"
        maxlength="100000000"
        placeholder="Digite um valor entre 0 e 360"
        autocomplete="off"
        (keypress)="
          func.controlNumber(
            $event,
            formSection.get('normal_line_azimuth'),
            'positiveDecimalDot'
          )
        "
        (keyup)="
          func.controlNumber($event, formSection.get('normal_line_azimuth'))
        "
        (blur)="func.formatType($event)"
        (focus)="func.formatType($event)"
        appDisableScroll
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('normal_line_azimuth').valid &&
          formSection.get('normal_line_azimuth').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Datum -->
    <div class="col-md-4">
      <label class="form-label">DATUM</label>
      <select
        class="form-select"
        formControlName="datum"
        (change)="coordinatesConversion()"
      >
        <option value="">É necessário selecionar uma Estrutura</option>
        <option *ngFor="let item of datum" [ngValue]="item.id">
          {{ item.value }}
        </option>
      </select>
    </div>

    <!-- Coordenadas UTM - MONTANTE -->
    <div class="col-md-8">
      <div class="section-container">
        <div class="section-header d-flex align-items-center">
          <input
            type="radio"
            class="form-check-input"
            formControlName="upstream_coordinate_format"
            value="{{ coordinateFormatList['upstream'][1].id }}"
            (change)="
              onCoordinateFormatChange(
                coordinateFormatList['upstream'][1],
                'upstream'
              )
            "
          />
          <label class="form-check-label ms-2"
            >Coordenadas UTM - MONTANTE</label
          >
        </div>

        <div class="row p-3">
          <!-- Zona (Fuso) -->
          <div class="col-md-3">
            <label class="form-label">Zona (Fuso)</label>
            <select
              class="form-select"
              formControlName="upstream_zone_number"
              (change)="coordinatesConversion('upstream')"
            >
              <option value="">Selecione</option>
              <option *ngFor="let item of zoneNumberUTM" [ngValue]="item.id">
                {{ item.value }}
              </option>
            </select>
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('upstream_zone_number').valid &&
                formSection.get('upstream_zone_number').touched &&
                formSection.get('upstream_zone_number').enabled
              "
            >
              Campo Obrigatório.
            </small>
          </div>

          <!-- Letra -->
          <div class="col-md-3">
            <label class="form-label">Letra</label>
            <select
              class="form-select"
              formControlName="upstream_zone_letter"
              (change)="coordinatesConversion('upstream')"
            >
              <option value="">Selecione</option>
              <option *ngFor="let item of zoneLetterUTM" [ngValue]="item.id">
                {{ item.value }}
              </option>
            </select>
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('upstream_zone_letter').valid &&
                formSection.get('upstream_zone_letter').touched &&
                formSection.get('upstream_zone_letter').enabled
              "
            >
              Campo Obrigatório.
            </small>
          </div>

          <!-- Coordenada Norte -->
          <div class="col-md-3">
            <label class="form-label">Coordenada Norte</label>
            <input
              type="text"
              class="form-control"
              formControlName="upstream_northing"
              (blur)="
                coordinatesConversion('upstream'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('upstream_northing').valid &&
                formSection.get('upstream_northing').touched &&
                formSection.get('upstream_northing').enabled
              "
            >
              Campo Obrigatório.
            </small>
          </div>

          <!-- Coordenada Leste -->
          <div class="col-md-3">
            <label class="form-label">Coordenada Leste</label>
            <input
              type="text"
              class="form-control"
              formControlName="upstream_easting"
              (blur)="
                coordinatesConversion('upstream'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('upstream_easting').valid &&
                formSection.get('upstream_easting').touched &&
                formSection.get('upstream_easting').enabled
              "
            >
              Campo Obrigatório.
            </small>
          </div>
        </div>
      </div>

      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('upstream_coordinate_format').valid &&
          formSection.get('upstream_coordinate_format').touched &&
          formSection.get('upstream_coordinate_format').enabled
        "
      >
        É necessário informar as Coordenadas de Montante.
      </small>
    </div>

    <!-- Coordenadas Geográficas - MONTANTE -->
    <div class="col-md-4">
      <div class="section-container">
        <div class="section-header d-flex align-items-center">
          <input
            type="radio"
            class="form-check-input"
            formControlName="upstream_coordinate_format"
            value="{{ coordinateFormatList['upstream'][0].id }}"
            (change)="
              onCoordinateFormatChange(
                coordinateFormatList['upstream'][0],
                'upstream'
              )
            "
          />
          <label class="form-check-label ms-2">
            Coordenadas Geográficas - MONTANTE
          </label>
        </div>

        <div class="row p-3">
          <!-- Latitude -->
          <div class="col-md-6">
            <label class="form-label">Latitude</label>
            <input
              type="text"
              formControlName="upstream_latitude"
              class="form-control"
              (blur)="
                coordinatesConversion('upstream'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('upstream_latitude').valid &&
                formSection.get('upstream_latitude').touched &&
                formSection.get('upstream_latitude').enabled
              "
            >
              Campo Obrigatório.
            </small>
          </div>

          <!-- Longitude -->
          <div class="col-md-6">
            <label class="form-label">Longitude</label>
            <input
              type="text"
              formControlName="upstream_longitude"
              class="form-control"
              (blur)="
                coordinatesConversion('upstream'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('upstream_longitude').valid &&
                formSection.get('upstream_longitude').touched &&
                formSection.get('upstream_longitude').enabled
              "
            >
              Campo Obrigatório.
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Coordenadas UTM - JUSANTE -->
    <div class="col-md-8">
      <div class="section-container">
        <div class="section-header d-flex align-items-center">
          <input
            type="radio"
            class="form-check-input"
            formControlName="downstream_coordinate_format"
            value="{{ coordinateFormatList['downstream'][1].id }}"
            (change)="
              onCoordinateFormatChange(
                coordinateFormatList['downstream'][1],
                'downstream'
              )
            "
          />
          <label class="form-check-label ms-2">
            Coordenadas UTM - JUSANTE
          </label>
        </div>

        <div class="row p-3">
          <!-- Zona (Fuso) -->
          <div class="col-md-3">
            <label class="form-label">Zona (Fuso)</label>
            <select
              class="form-select"
              formControlName="downstream_zone_number"
              (change)="coordinatesConversion('downstream')"
            >
              <option value="">Selecione</option>
              <option *ngFor="let item of zoneNumberUTM" [ngValue]="item.id">
                {{ item.value }}
              </option>
            </select>
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('downstream_zone_number').valid &&
                formSection.get('downstream_zone_number').touched &&
                formSection.get('downstream_zone_number').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Letra -->
          <div class="col-md-3">
            <label class="form-label">Letra</label>
            <select
              class="form-select"
              formControlName="downstream_zone_letter"
              (change)="coordinatesConversion('downstream')"
            >
              <option value="">Selecione</option>
              <option *ngFor="let item of zoneLetterUTM" [ngValue]="item.id">
                {{ item.value }}
              </option>
            </select>
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('downstream_zone_letter').valid &&
                formSection.get('downstream_zone_letter').touched &&
                formSection.get('downstream_zone_letter').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Coordenada Norte -->
          <div class="col-md-3">
            <label class="form-label">Coordenada Norte</label>
            <input
              type="text"
              formControlName="downstream_northing"
              class="form-control"
              (blur)="
                coordinatesConversion('downstream'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('downstream_northing').valid &&
                formSection.get('downstream_northing').touched &&
                formSection.get('downstream_northing').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Coordenada Leste -->
          <div class="col-md-3">
            <label class="form-label">Coordenada Leste</label>
            <input
              type="text"
              formControlName="downstream_easting"
              class="form-control"
              (blur)="
                coordinatesConversion('downstream'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('downstream_easting').valid &&
                formSection.get('downstream_easting').touched &&
                formSection.get('downstream_easting').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>
        </div>
      </div>

      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('upstream_coordinate_format').valid &&
          formSection.get('upstream_coordinate_format').touched &&
          formSection.get('upstream_coordinate_format').enabled
        "
        >É necessário informar as Coordenadas de Jusante.</small
      >
    </div>

    <!-- Coordenadas Geográficas - JUSANTE -->
    <div class="col-md-4">
      <div class="section-container">
        <div class="section-header d-flex align-items-center">
          <input
            type="radio"
            class="form-check-input"
            formControlName="downstream_coordinate_format"
            value="{{ coordinateFormatList['downstream'][0].id }}"
            (change)="
              onCoordinateFormatChange(
                coordinateFormatList['downstream'][0],
                'downstream'
              )
            "
          />
          <label class="form-check-label ms-2">
            Coordenadas Geográficas - JUSANTE
          </label>
        </div>

        <div class="row p-3">
          <!-- Latitude -->
          <div class="col-md-6">
            <label class="form-label">Latitude</label>
            <input
              type="text"
              formControlName="downstream_latitude"
              class="form-control"
              (blur)="
                coordinatesConversion('downstream'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('downstream_latitude').valid &&
                formSection.get('downstream_latitude').touched &&
                formSection.get('downstream_latitude').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Longitude -->
          <div class="col-md-6">
            <label class="form-label">Longitude</label>
            <input
              type="text"
              formControlName="downstream_longitude"
              class="form-control"
              (blur)="
                coordinatesConversion('downstream'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('downstream_longitude').valid &&
                formSection.get('downstream_longitude').touched &&
                formSection.get('downstream_longitude').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>
        </div>
      </div>
    </div>

    <!-- Mensagens de erro -->
    <div class="row mt-2">
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesError"
      ></app-alert>
    </div>

    <!-- Profundidade mínima -->
    <div class="row mt-2">
      <!-- Profundidade mínima drenada (m) -->
      <div class="col-md-4 mt-2" *ngIf="conditions.drained">
        <label class="form-label">Profundidade mínima drenada (m)</label>
        <input
          type="text"
          class="form-control"
          formControlName="minimum_drained_depth"
          min="0"
          decimalplaces="0.00001"
          step="1"
          (keypress)="
            func.controlNumber(
              $event,
              formSection.get('minimum_drained_depth'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber($event, formSection.get('minimum_drained_depth'))
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSection.get('minimum_drained_depth').valid &&
            formSection.get('minimum_drained_depth').touched &&
            formSection.get('minimum_drained_depth').enabled
          "
          >Campo Obrigatório.</small
        >
      </div>

      <!-- Profundidade mínima não drenada (m) -->
      <div class="col-md-4 mt-2" *ngIf="conditions.undrained">
        <label class="form-label">Profundidade mínima não drenada (m)</label>
        <input
          type="text"
          class="form-control"
          formControlName="minimum_undrained_depth"
          decimalplaces="0.00001"
          step="1"
          min="0"
          (keypress)="
            func.controlNumber(
              $event,
              formSection.get('minimum_undrained_depth'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber(
              $event,
              formSection.get('minimum_undrained_depth')
            )
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSection.get('minimum_undrained_depth').valid &&
            formSection.get('minimum_undrained_depth').touched &&
            formSection.get('minimum_undrained_depth').enabled
          "
          >Campo Obrigatório.</small
        >
      </div>

      <!-- Profundidade mínima pseudo-estática (m) -->
      <div class="col-md-4 mt-2" *ngIf="conditions.pseudo_static">
        <label class="form-label"
          >Profundidade mínima pseudo-estática (m)</label
        >
        <input
          type="text"
          class="form-control"
          formControlName="minimum_pseudo_static_depth"
          decimalplaces="0.00001"
          step="1"
          min="0"
          (keypress)="
            func.controlNumber(
              $event,
              formSection.get('minimum_pseudo_static_depth'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber(
              $event,
              formSection.get('minimum_pseudo_static_depth')
            )
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSection.get('minimum_pseudo_static_depth').valid &&
            formSection.get('minimum_pseudo_static_depth').touched &&
            formSection.get('minimum_pseudo_static_depth').enabled
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>

    <!-- Esconsa -->
    <div class="col-md-1 mt-2">
      <label class="form-label">Esconsa</label>
      <input
        class="form-check-input form-control check-input me-2"
        formControlName="is_skew"
        type="checkbox"
        value=""
        checked
        (change)="isSkewValidate()"
      />
    </div>

    <!-- Azimute Esconsa -->
    <div class="col-md-3 mt-2">
      <label class="form-label">Azimute (graus)</label>
      <input
        type="text"
        formControlName="skew_line_azimuth"
        class="form-control"
        min="0"
        max="360"
        step="0.0000001"
        maxlength="100000000"
        placeholder="Digite um valor entre 0 e 360"
        autocomplete="off"
        (keypress)="
          func.controlNumber(
            $event,
            formSection.get('skew_line_azimuth'),
            'positiveDecimalDot'
          )
        "
        (keyup)="
          func.controlNumber($event, formSection.get('skew_line_azimuth'))
        "
        (blur)="func.formatType($event)"
        (focus)="func.formatType($event)"
        appDisableScroll
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('skew_line_azimuth').valid &&
          formSection.get('skew_line_azimuth').touched &&
          formSection.get('is_skew').value
        "
        >Campo Obrigatório.</small
      >
    </div>

    <div class="col-md-8 mt-2"></div>

    <!-- Coordenadas UTM - PONTO INTERMEDIÁRIO -->
    <div class="col-md-8" *ngIf="formSection.get('is_skew').value">
      <div class="section-container">
        <div class="section-header d-flex align-items-center">
          <input
            type="radio"
            class="form-check-input"
            formControlName="midpoint_coordinate_format"
            value="{{ coordinateFormatList['midpoint'][1].id }}"
            (change)="
              onCoordinateFormatChange(
                coordinateFormatList['midpoint'][1],
                'midpoint'
              )
            "
          />
          <label class="form-check-label ms-2">
            Coordenadas UTM - PONTO INTERMEDIÁRIO
          </label>
        </div>

        <div class="row p-3">
          <!-- Zona (Fuso) -->
          <div class="col-md-3">
            <label class="form-label">Zona (Fuso)</label>
            <select
              class="form-select"
              formControlName="midpoint_zone_number"
              (change)="coordinatesConversion('midpoint')"
            >
              <option value="">Selecione</option>
              <option *ngFor="let item of zoneNumberUTM" [ngValue]="item.id">
                {{ item.value }}
              </option>
            </select>
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('midpoint_zone_number').valid &&
                formSection.get('midpoint_zone_number').touched &&
                formSection.get('midpoint_zone_number').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Letra -->
          <div class="col-md-3">
            <label class="form-label">Letra</label>
            <select
              class="form-select"
              formControlName="midpoint_zone_letter"
              (change)="coordinatesConversion('midpoint')"
            >
              <option value="">Selecione</option>
              <option *ngFor="let item of zoneLetterUTM" [ngValue]="item.id">
                {{ item.value }}
              </option>
            </select>
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('midpoint_zone_letter').valid &&
                formSection.get('midpoint_zone_letter').touched &&
                formSection.get('midpoint_zone_letter').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Coordenada Norte -->
          <div class="col-md-3">
            <label class="form-label">Coordenada Norte</label>
            <input
              type="text"
              formControlName="midpoint_northing"
              class="form-control"
              (blur)="
                coordinatesConversion('midpoint'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('midpoint_northing').valid &&
                formSection.get('midpoint_northing').touched &&
                formSection.get('midpoint_northing').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Coordenada Leste -->
          <div class="col-md-3">
            <label class="form-label">Coordenada Leste</label>
            <input
              type="text"
              formControlName="midpoint_easting"
              class="form-control"
              (blur)="
                coordinatesConversion('midpoint'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('midpoint_easting').valid &&
                formSection.get('midpoint_easting').touched &&
                formSection.get('midpoint_easting').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>
        </div>
      </div>

      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('midpoint_coordinate_format').valid &&
          formSection.get('midpoint_coordinate_format').touched &&
          formSection.get('midpoint_coordinate_format').enabled
        "
        >É necessário informar as Coordenadas do Ponto Intermediário.</small
      >
    </div>

    <!-- Coordenadas Geográficas  - PONTO INTERMEDIÁRIO -->
    <div class="col-md-4" *ngIf="formSection.get('is_skew').value">
      <div class="section-container">
        <div class="section-header d-flex align-items-center">
          <input
            type="radio"
            class="form-check-input"
            formControlName="midpoint_coordinate_format"
            value="{{ coordinateFormatList['midpoint'][0].id }}"
            (change)="
              onCoordinateFormatChange(
                coordinateFormatList['midpoint'][0],
                'midpoint'
              )
            "
          />
          <label class="form-check-label ms-2">
            Coordenadas Geográficas - PONTO INTERMEDIÁRIO
          </label>
        </div>

        <div class="row p-3">
          <!-- Latitude -->
          <div class="col-md-6">
            <label class="form-label">Latitude</label>
            <input
              type="text"
              formControlName="midpoint_latitude"
              class="form-control"
              (blur)="
                coordinatesConversion('midpoint'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('midpoint_latitude').valid &&
                formSection.get('midpoint_latitude').touched &&
                formSection.get('midpoint_latitude').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>

          <!-- Longitude -->
          <div class="col-md-6">
            <label class="form-label">Longitude</label>
            <input
              type="text"
              formControlName="midpoint_longitude"
              class="form-control"
              (blur)="
                coordinatesConversion('midpoint'); func.formatType($event)
              "
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <small
              class="form-text text-danger"
              *ngIf="
                !formSection.get('midpoint_longitude').valid &&
                formSection.get('midpoint_longitude').touched &&
                formSection.get('midpoint_longitude').enabled
              "
              >Campo Obrigatório.</small
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Instrumentos associados -->
  <div class="row mt-2">
    <div class="col-md-4">
      <label class="form-label">Instrumentos associados (somente online)</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione'"
        [settings]="instrumentSettings"
        [data]="instrumentsOnline"
        formControlName="instruments"
        (click)="formSection.get('instruments').markAsTouched()"
        (onSelect)="getInstrumentsInList($event, 'select')"
        (onSelectAll)="getInstrumentsInList($event, 'selectAll')"
        (onDeSelect)="getInstrumentsInList($event, 'deselect')"
        (onDeSelectAll)="getInstrumentsInList($event, 'deselectAll')"
        [ngClass]="{
          'disabled-dropdown': formSection.get('instruments').disabled
        }"
      ></ng-multiselect-dropdown>
    </div>
    <!-- Customização da linha -->
    <div class="col-md-2">
      <label class="form-label">Tipo de linha</label>
      <select
        class="form-select"
        formControlName="map_line_setting_type"
        (change)="
          changeLineType(formSection.get('map_line_setting_type').value)
        "
      >
        <option value="">Selecione</option>
        <option *ngFor="let item of mapLineSetting" [ngValue]="item.id">
          {{ item.value }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('map_line_setting_type').valid &&
          formSection.get('map_line_setting_type').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <div class="col-md-2">
      <label class="form-label">Espessura da linha</label>
      <input
        type="number"
        formControlName="map_line_setting_width"
        class="form-control"
        min="1"
        max="5"
        step="1"
        maxlength="1"
        placeholder="Digite um valor entre 1 e 5"
        autocomplete="off"
        (change)="
          changeLineWeight(formSection.get('map_line_setting_width').value)
        "
        (keypress)="
          func.controlNumber(
            $event,
            formSection.get('map_line_setting_width'),
            'positiveDecimalLimit'
          )
        "
        (keyup)="
          func.controlNumber($event, formSection.get('map_line_setting_width'))
        "
        appDisableScroll
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('map_line_setting_width').valid &&
          formSection.get('map_line_setting_width').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <div class="col-md-2" (clickOutside)="onClickedOutside('colorPicker')">
      <label class="form-label">Cor da linha</label>
      <br />
      <button
        class="section-color"
        (click)="showColorPicker = !showColorPicker"
        [disabled]="view"
        [style.background]="selectedColor"
      ></button>
      <div
        class="d-flex justify-content-center justify-content-md-start color-picker-container"
        *ngIf="showColorPicker"
      >
        <div style="width: 220px; display: inline-block">
          <color-sketch
            [color]="selectedColor"
            (onChangeComplete)="changeComplete($event)"
          ></color-sketch>
        </div>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formSection.get('map_line_setting_color').valid &&
          formSection.get('map_line_setting_color').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Customização da linha -->
  </div>
  <div class="row mt-2">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
      <li class="nav-item" role="presentation">
        <button
          class="nav-link"
          id="map-tab"
          type="button"
          role="tab"
          aria-controls="map"
          aria-selected="true"
          (click)="selectTab('map')"
        >
          Mapa da Seção
        </button>
      </li>
      <li class="nav-item" role="presentation" *ngIf="edit || view">
        <button
          class="nav-link"
          id="dxf-tab"
          type="button"
          role="tab"
          aria-controls="dxf"
          aria-selected="true"
          (click)="selectTab('dxf')"
        >
          DXF da Seção
        </button>
      </li>
    </ul>
    <div class="tab-content" id="myTabContent">
      <!-- Aba Mapa -->
      <div
        class="tab-pane fade"
        [ngClass]="mapTabConfig.active ? 'show active' : ''"
        id="map"
        role="tabpanel"
        aria-labelledby="map-tab"
      >
        <div class="row mt-2">
          <!-- Mapa -->
          <div class="col-md-12">
            <app-google-maps
              #mapSections
              [id]="'mapSections'"
            ></app-google-maps>
          </div>
        </div>
      </div>
      <!-- Aba Revisão Seção -->
      <div
        class="tab-pane fade"
        [ngClass]="dxfTabConfig.active ? 'show active' : ''"
        id="dxf"
        role="tabpanel"
        aria-labelledby="dxf-tab"
      >
        <div class="row g-3 mt-2">
          <div
            class="alert mt-3"
            [ngClass]="message.class"
            role="alert"
            *ngIf="message.status"
            [innerHTML]="message.text"
          ></div>
          <!-- DXF -->
          <div class="col-md-12" *ngIf="dxfTabConfig.active && !message.status">
            <app-dxf-viewer
              [fileDxf]="fileDXF"
              [idCanvas]="'canvasDXFSection'"
            ></app-dxf-viewer>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>

<!-- Modal generico para componentes -->
<app-modal-components
  #modalComponents
  [config]="configModal"
  [component]="'app-images'"
  [title]="titleModal"
></app-modal-components>
