.table > thead > tr > th {
  background: rgba(3, 37, 97, 0.1);
  font-size: 0.875em !important;
  text-align: center;
  color: #032561;
  border: 1px solid #ffffff;
}

.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #ffffff;
}

tbody {
  font-size: 0.875em !important;
  text-align: center;
  border: 1px solid #d4d2d2;
  color: #032561;
  background-color: rgba(212, 210, 210, 0.1);
}

.exposed.select-cell {
  padding: 0px;
  vertical-align: middle;

  .form-switch {
    display: inline-block;
    margin: 0;
    vertical-align: middle;
    cursor: pointer;
  }
}

.form-switch-label {
  font-size: 0.875em !important;
  vertical-align: middle;
  text-align: center;
}

.form-check-input:checked {
  background-color: #34b575;
  border-color: #34b575;
}

.color {
  width: 50px;
  height: 24px;
  margin: 0 auto;
}

//Losango tela de leituras
.color-green {
  color: #34b575;
}

//Ignorar pacotes
.color-red {
  color: red;
}

.color-orange {
  color: #fdc12d;
}

.pointer {
  cursor: pointer;
}

.th-component {
  width: 200px;
  position: absolute;
  top: 28px;
  left: 0;
  z-index: 9999;
}

.only-icon {
  min-height: 48px;
}
