<div
  class="dropdown-notifications"
  (clickOutside)="onClickedOutside('notification')"
>
  <app-button
    [class]="'btn-notification'"
    [icon]="'fa fa-bell'"
    [title]="'Notificações'"
    (click)="dropdownNotification = !dropdownNotification"
  >
  </app-button>
  <span class="notification-badge" *ngIf="collectionSize > 0">
    {{ getFormattedCollectionSize() }}
  </span>

  <ul [ngClass]="dropdownNotification ? 'active' : ''">
    <li class="li-header">
      <h6 class="title">
        <span class="custom-icon"> <i class="fa fa-bell"></i> </span>
        Notificações
      </h6>
    </li>
    <li class="li-buttons">
      <app-button
        [class]="'btn-sm btn-logisoil-notification'"
        [label]="'Marcar como lido'"
        (click)="readNotifications()"
      >
      </app-button>
      <app-button
        [class]="'btn-sm btn-logisoil-notification'"
        [label]="'Marcar tudo como lido'"
        (click)="readAllNotifications()"
      >
      </app-button>
      <app-button
        [class]="'btn-sm btn-logisoil-notification'"
        [label]="'Histórico'"
        [routerLink]="'/alerts'"
      >
      </app-button>
    </li>
    <li class="li-content">
      <ng-template
        ngFor
        let-notification
        [ngForOf]="notificationsData"
        let-i="index"
      >
        <hr *ngIf="i > 0" />
        <div>
          <input
            class="form-check-input me-1"
            type="checkbox"
            [value]="notification.id"
            [checked]="notification.checked"
            #selectNotification
            (change)="
              changeNotification(
                selectNotification.value,
                selectNotification.checked
              )
            "
          />
          <span class="title">
            <strong>{{ getThemeLabel(notification.theme) }}</strong>
          </span>
          <span class="content">
            {{
              notification.message.includes('@')
                ? notification.message.split('@')[0]
                : notification.message
            }}
            <a
              *ngIf="notification.message.includes('@')"
              [href]="generateHistoryUrl(notification)"
              >Visualizar Histórico</a
            >
          </span>
          <small class="date">
            <i>{{ notification.created_date }}</i>
          </small>
        </div>
      </ng-template>
    </li>
    <li class="li-pagination">
      <div class="jump-to-page">
        <label for="jumpInput">Página</label>
        <input
          type="number"
          id="jumpInput"
          [(ngModel)]="inputPage"
          (keyup.enter)="jumpToPage()"
          min="1"
          [max]="totalPages"
        />
        <button type="button" (click)="jumpToPage()">Ir</button>
      </div>
      <!-- [collectionSize]="collectionSize"
        [page]="page" -->
      <app-paginator
        [collectionSize]="collectionSize"
        [page]="page"
        [maxSize]="2"
        [boundaryLinks]="true"
        [pageSize]="pageSize"
        (sendPageChange)="loadPage($event)"
        [rotate]="true"
        [enableItemPerPage]="false"
      ></app-paginator>
    </li>
  </ul>
</div>
