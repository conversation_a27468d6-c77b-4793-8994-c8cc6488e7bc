const geoTableHeader = [
  {
    label: 'Selecionar',
    width: '85px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['select'],
    type: 'check'
  },
  {
    label: 'ID',
    width: '60px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['reading_search_identifier']
  },
  {
    label: 'Instrumento',
    width: '50%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['identifier']
  },
  {
    label: 'Data e hora',
    width: '50%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['date_format']
  },
  {
    label: 'Natureza',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['nature']
  },
  {
    label: 'PGA eixo A (%g)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['a_axis_pga']
  },
  {
    label: 'PGA eixo B (%g)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['b_axis_pga']
  },
  {
    label: 'PGA eixo Z (%g)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['z_axis_pga']
  },
  {
    label: 'Coordenadas Epicentro E (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['east_coordinate']
  },
  {
    label: 'Coordenadas Epicentro N (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['north_coordinate']
  },
  {
    label: 'Ações',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['actionCustom']
  }
];

export { geoTableHeader };
