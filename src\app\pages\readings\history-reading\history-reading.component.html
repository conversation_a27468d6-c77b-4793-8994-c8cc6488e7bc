<form [formGroup]="formHistoryReading">
  <!-- <PERSON><PERSON><PERSON> Histórico -->
  <div class="col-md-12 mt-3" *ngIf="tableData.length > 0">
    <label class="form-title mt-2"
      >Histórico de cadastros e edições de leituras:</label
    >
    <app-table
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeader"
      [tableData]="tableData"
      [permissaoUsuario]="permissaoUsuario"
    >
    </app-table>

    <!-- Paginação -->
    <app-paginator
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event, 'history')"
    ></app-paginator>
  </div>

  <!-- Alertas -->
  <div
    class="alert alert-success mt-3"
    role="alert"
    *ngIf="messageReturn.status"
  >
    {{ messageReturn.text }}
  </div>

  <div class="row mt-2">
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <div class="col-md-12 mt-2 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/readings']"
    ></app-button>
  </div>
</form>
