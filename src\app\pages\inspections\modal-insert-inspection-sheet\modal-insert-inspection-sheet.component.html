<ng-template #modalInsertInspectionSheet let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">Selecione o tipo de inspeção a ser realizada 3:</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <div class="modal-body">
    <div *ngFor="let item of insertInspectionSheet" class="radio-option">
      <label>
        <input
          type="radio"
          name="inspectionSheet"
          [value]="item.value"
          [(ngModel)]="selectedOption"
        />
        {{ item.label }}
      </label>
    </div>
  </div>
  <!-- Botões -->
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-green'"
      [label]="'Inserir ficha de inspeção'"
      (click)="create()"
      [type]="false"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-red'"
      [label]="'Cancelar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>
