import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-gut-badge',
  templateUrl: './gut-badge.component.html',
  styleUrls: ['./gut-badge.component.scss']
})
export class GutBadgeComponent implements OnInit {
  @Input() gut: number | null = null;
  @Input() maxGUT: number = 125;

  public width = 0;

  ngOnInit(): void {
    if (this.gut !== null && this.gut !== undefined && this.gut > 0) {
      const percent = Math.min(this.gut / this.maxGUT, 1);
      this.width = percent * 100;
    }
  }
}
