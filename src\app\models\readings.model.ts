import { FormControl, FormGroup } from '@angular/forms';
import { lastValueFrom } from 'rxjs';
import { InstrumentsService } from '../services/api/instrument.service';

abstract class Readings {
  abstract instrumentControls: {
    measuring_units?: Record<string, Select>;
    fields: Record<string, Field | Select>;
  };

  measuring_units?: Record<string, Select>;
  fields: Record<string, Field | Select>;
  instrumentSelected?: any;
  measure_points?: any[];

  abstract instrumentChanged(reading: FormGroup, valor: string): void;

  getReadings(fields: Record<string, Field | Select>, measuring_units: Record<string, Select> = null) {
    this.fields = fields;
    this.measuring_units = measuring_units;
  }

  // Retorna a lista de campos de unidade de medida (caso possua). Necesário para iteração e formação dos campos em tela
  getMeasuringUnitsList(): Select[] {
    return this.measuring_units ? Object.values(this.measuring_units) : null;
  }
  // Retorna a lista dos campos de acordo com o instrumento selecionado. Necesário para iteração e formação dos campos em tela
  getFieldsList(): Field[] {
    return Object.values(this.fields);
  }

  async getInstrumentInfo(instrumentsService: InstrumentsService, id: string) {
    const instrument = await lastValueFrom(instrumentsService.getInstrumentsById(id));
    if (instrument) this.instrumentSelected = instrument;
  }

  // Transforma a lista de campos em FormGroup para formação do FormGroup da leitura
  arrayToFormGroup(array: { controlName: string; getControl: () => FormControl | FormGroup }[]): FormGroup {
    const formGroup = new FormGroup({});

    // Formado um controle por item da lista
    array.forEach((field) => {
      // Para controles FormGroup seu contolName será seguida por ponto. Ex.: 'measure_point.identifier'
      formGroup.addControl(field.controlName.split('.')[0], field.getControl());
    });
    return formGroup;
  }

  // Retorna nova leitura definindo valores de instrumento e pontos de medição
  getReadingGroup(instrument_id: string, measurement?: { controlName: string; value: string }) {
    const formGroup = this.arrayToFormGroup(this.getFieldsList());
    if (measurement) formGroup.get(measurement.controlName).patchValue(measurement.value);

    formGroup.get(this.fields.instrument.controlName).setValue(instrument_id);
    return formGroup;
  }
}
interface Field {
  controlName: string;
  label: string;
  getControl: () => FormControl | FormGroup;
  colSize?: number;
  type?: 'text' | 'number' | 'checkbox' | 'select' | 'datetime-local';
  actionWhenChanged?: (control: FormControl) => void;
  custom?: boolean;
  measuringUnit?: (measuring_unit_control?: FormGroup) => string;
}
interface Select extends Field {
  options: any[];
  valueField?: string;
  labelField?: string;
}

export { Readings, Field, Select };
