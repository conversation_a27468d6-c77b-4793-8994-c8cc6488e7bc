import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { SortableListComponent } from '@components/sortable-list/sortable-list.component';

import { MessageCadastro } from 'src/app/constants/message.constants';
import { accessLevel as accessLevelPermission } from 'src/app/constants/permissions.constants';
import { occurrenceDecision } from 'src/app/constants/structure.constants';

import { InspectionService } from 'src/app/services/api/inspection.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-inspections-tab',
  templateUrl: './inspections-tab.component.html',
  styleUrls: ['./inspections-tab.component.scss']
})
export class InspectionsTabComponent implements OnInit {
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  @ViewChild(SortableListComponent) sortableList: SortableListComponent;

  public formAspectStructure: FormGroup = new FormGroup({
    aspects: new FormControl('', [Validators.required])
  });

  public formAspects: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    description: new FormControl('', [Validators.required]),
    allow_option_not_applicable: new FormControl(true),
    response_for_occurrence: new FormControl(1, [Validators.required]),
    area: new FormControl('', [Validators.required]),
    selected: new FormControl(false),
    index: new FormControl(null)
  });

  public formArea: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    name: new FormControl('', [Validators.required])
  });

  public aspectsList: any = [];
  public areasList: any = [];

  public ctrlAspects: boolean = false;
  public ctrlArea: boolean = false;

  public aspect: any = {
    id: null,
    description: null,
    allow_option_not_applicable: null,
    response_for_occurrence: null,
    area: {
      id: null
    }
  };

  public area: any = {
    id: null,
    name: null
  };

  public occurrenceDecision = occurrenceDecision;

  public aspectRequest: any = {};
  public areaRequest: any = {};

  public editAspect: boolean = false;
  public editArea: boolean = false;

  public aspectsCollapse: boolean = false;
  public aspectsSelected: any = [];
  public aspectsSorted: any = [];
  public aspectsNumberSelected: number = 0;

  public messageAspect: any = [{ text: '', status: false }];
  public messagesErrorAspect: any = null;

  public messageArea: any = [{ text: '', status: false }];
  public messagesErrorArea: any = null;

  public dragDropAspects: boolean = false;

  public maxlength: number = 255;
  public charachtersCount: number = 0;
  public counter: string;

  constructor(private inspectionService: InspectionService) {}

  ngOnInit(): void {
    this.getAspectsList();
    this.validateAccess();
  }

  /**
   * Obtém a lista de aspectos via API e mapeia os índices.
   * Se `dataTab` for informado, chama `setAspectList` para marcar os aspectos selecionados.
   *
   * @param dataTab - (Opcional) Objeto contendo os aspectos previamente selecionados.
   */
  getAspectsList(dataTab: any = null) {
    this.inspectionService.getAspectsList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.aspectsList = dados.map((item: any, index: number) => {
        item.index = index;
        return item;
      });

      if (dataTab?.aspects?.length > 0) {
        this.setAspectList(dataTab, this.aspectsList);
      }
    });
  }

  /**
   * Define a lista de aspectos selecionados e não selecionados com base no `dataTab`.
   *
   * @param dataTab - Objeto com aspectos selecionados.
   * @param aspectList - Lista completa de aspectos disponíveis.
   */
  setAspectList(dataTab: any, aspectList: any): void {
    dataTab = dataTab.aspects.map((item: any) => {
      let retorno = item.aspect;
      retorno.index = item.index;
      retorno.selected = true;
      return retorno;
    });
    let aspectsNotSelected = fn.differenceByIndex(this.aspectsList, dataTab, 'id');

    let idx = dataTab.length;
    aspectsNotSelected = aspectsNotSelected.map((item: any) => {
      item.index = idx;
      item.selected = false;
      idx++;
      return item;
    });

    dataTab = dataTab.map((item: any) => {
      item.selected = true;
      return item;
    });
    this.aspectsList = dataTab.concat(aspectsNotSelected);

    this.aspectsCollapse = true;
  }

  /**
   * Cadastra um novo aspecto via API. Em caso de sucesso, limpa o formulário e exibe feedback.
   * Caso ocorra erro de validação (HTTP 400), exibe mensagens de erro temporárias.
   */
  registerAspects() {
    this.inspectionService.postAspects(this.aspectRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageAspect.text = MessageCadastro.SucessoCadastro;
        this.messageAspect.status = true;
        this.formAspects.reset();
        setTimeout(() => {
          this.messageAspect.status = false;
          this.messageAspect.text = '';
        }, 6000);
        this.getAspects(dados, 'list');
        this.ctrlAspects = false;
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorAspect = error.error;
          setTimeout(() => {
            this.messagesErrorAspect = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Busca um aspecto pelo ID e popula o formulário para edição ou adiciona à lista.
   *
   * @param aspectId - ID do aspecto.
   * @param type - Tipo de ação: 'edit' (editar formulário) ou 'list' (adicionar à lista).
   */
  getAspects(aspectId: string, type: string = 'edit') {
    this.inspectionService.getAspectsById(aspectId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      if (type == 'list') {
        this.aspectsList.push({
          ...dados,
          index: this.aspectsList.length,
          selected: true
        });
      }
      if (type == 'edit') {
        this.dragDropAspects = true;
        this.editAspect = true;
        this.formAspects.get('id').setValue(dados.id);
        this.formAspects.get('description').setValue(dados.description);
        this.formAspects.get('allow_option_not_applicable').setValue(dados.allow_option_not_applicable);
        this.formAspects.get('response_for_occurrence').setValue(dados.response_for_occurrence);
        this.formAspects.get('area').setValue(dados.area.id);
        this.ctrlAspects = true;
      }
    });
  }

  /**
   * Envia as alterações de um aspecto via PUT. Atualiza a lista de aspectos localmente.
   * Em caso de erro de validação, exibe mensagens temporárias.
   */
  editAspects() {
    this.inspectionService.putAspects(this.aspectRequest.id, this.aspectRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageAspect.text = MessageCadastro.EdicaoCadastro;
        this.messageAspect.status = true;
        this.formAspects.reset();
        this.aspectRequest.aspects = [];

        setTimeout(() => {
          this.messageAspect.text = '';
          this.messageAspect.status = false;
        }, 4000);

        this.ctrlAspects = false;
        let idx = fn.findIndexInArrayofObject(this.aspectsList, 'id', dados);
        const selected = this.aspectsList[idx].selected ? this.aspectsList[idx].selected : false;

        this.aspectsList[idx] = {
          ...this.aspectRequest,
          index: this.aspectsList[idx].index,
          selected: selected
        };
        this.dragDropAspects = false;
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorAspect = error.error;
          setTimeout(() => {
            this.messagesErrorAspect = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Busca a lista de áreas disponíveis para seleção no formulário de aspecto.
   */
  getAreasList() {
    this.inspectionService.getAreasList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.areasList = dados;
    });
  }

  /**
   * Cadastra uma nova área via API. Atualiza o formulário de aspectos com a nova área.
   * Em caso de erro (HTTP 400), exibe mensagens de erro temporárias.
   */
  registerArea() {
    this.inspectionService.postAreas(this.areaRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageArea.text = MessageCadastro.SucessoCadastro;
        this.messageArea.status = true;
        this.formArea.reset();

        setTimeout(() => {
          this.messageArea.text = '';
          this.messageArea.status = false;
        }, 4000);

        this.getAreasList();
        this.ctrlArea = false;
        this.formAspects.get('area').setValue(dados);
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorArea = error.error;
          setTimeout(() => {
            this.messagesErrorArea = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Busca uma área pelo ID e preenche o formulário de área para edição.
   *
   * @param areaId - ID da área a ser editada.
   */
  getArea(areaId: string) {
    this.inspectionService.getAreasById(areaId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.editArea = true;

      this.formArea.get('id').setValue(dados.id);
      this.formArea.get('name').setValue(dados.name);

      this.ctrlArea = true;
    });
  }

  /**
   * Atualiza os dados de uma área existente. Exibe feedback de sucesso ou mensagens de erro.
   */
  editAreaForm() {
    this.inspectionService.putAreas(this.areaRequest.id, this.areaRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.messageArea.text = MessageCadastro.EdicaoCadastro;
        this.messageArea.status = true;

        this.formArea.reset();

        setTimeout(() => {
          this.messageArea.status = false;
          this.messageArea.text = '';
        }, 4000);

        this.getAreasList();
        this.ctrlArea = false;
        this.formAspects.get('area').setValue(dados);
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorArea = error.error;
          setTimeout(() => {
            this.messagesErrorArea = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Atualiza a quantidade selecionada no campo de quantidade de aspectos.
   *
   * @param quantidade - Número a ser setado.
   * @param element - FormControl a ser atualizado.
   */
  getNumberSelected(quantidade: number, element: any) {
    element.setValue(quantidade);
  }

  /**
   * Valida os dados do formulário com base no tipo ('aspect' ou 'area').
   * Envia para cadastro ou edição conforme o estado do formulário.
   *
   * @param type - Tipo do formulário: 'aspect' ou 'area'.
   */
  validate(type: string = '') {
    this.formatData(type);
  }

  /**
   * Formata os dados dos formulários de aspecto ou área e envia para cadastro ou edição.
   *
   * @param type - Tipo do formulário: 'aspect' ou 'area'.
   */
  formatData(type: string = '') {
    if (type === 'aspect') {
      this.aspectRequest = this.aspect;
      this.aspectRequest.id = this.formAspects.get('id').value;
      this.aspectRequest.description = this.formAspects.get('description').value;
      this.aspectRequest.allow_option_not_applicable = this.formAspects.get('allow_option_not_applicable').value;
      this.aspectRequest.response_for_occurrence = this.formAspects.get('response_for_occurrence').value;
      this.aspectRequest.area.id = this.formAspects.get('area').value;
      if (!this.editAspect) {
        delete this.aspectRequest.id;
        this.registerAspects();
      } else {
        this.editAspects();
      }
    }
    if (type === 'area') {
      this.areaRequest = this.area;
      this.areaRequest.id = this.formArea.get('id').value;
      this.areaRequest.name = this.formArea.get('name').value;
      if (!this.editArea) {
        delete this.areaRequest.id;
        this.registerArea();
      } else {
        this.editAreaForm();
      }
    }
  }

  /**
   * Reseta os dados dos formulários de aspecto ou área.
   * Pode manter os painéis abertos com a opção `option = true`.
   *
   * @param type - Tipo do formulário: 'aspect' ou 'area'.
   * @param option - Se `true`, mantém os controles ativos.
   */
  resetForm(type: string = '', option: boolean = false) {
    if (type === 'aspect') {
      this.dragDropAspects = false;
      this.aspectsCollapse = false;
      if (option) {
        this.ctrlAspects = true;
      }
      this.editAspect = false;
      this.aspectsSelected = [];
      this.formAspects.get('description').setValue('');
      this.formAspects.get('allow_option_not_applicable').setValue(true);
      this.formAspects.get('response_for_occurrence').setValue(1);
      this.formAspects.get('area').setValue('');
    }
    if (type === 'area') {
      this.ctrlArea = false;
      if (option) {
        this.ctrlArea = true;
      }
      this.editArea = false;
      this.formArea.reset();
      this.formArea.get('name').setValue('');
    }
  }

  /**
   * Valida permissões com base no perfil do usuário.
   * Se for visualização, desativa os formulários. Caso contrário, busca áreas.
   *
   * @param role - (Opcional) Nível de permissão.
   */
  validateAccess(role: number = 0): any {
    if (this.profile.description !== accessLevelPermission.SuperSuporte && this.profile.description !== accessLevelPermission.Suporte) {
      this.view = true;
    }
    if (this.view) {
      this.formAspectStructure.disable();
      this.formAspects.disable();
      this.formArea.disable();
      this.dragDropAspects = true;
    } else {
      this.getAreasList();
    }
  }

  /**
   * Popula os dados de aspectos no formulário se houver dados no `dataTab`.
   *
   * @param dataTab - Objeto contendo dados a serem inseridos no formulário.
   */
  setData(dataTab: any = []) {
    if (dataTab?.aspects?.length > 0) {
      this.getAspectsList(dataTab);
    }
  }

  /**
   * Retorna os aspectos selecionados formatados para envio ao backend.
   *
   * @returns Objeto com a propriedade `aspects` contendo os aspectos formatados.
   */
  getData() {
    let dataTab = {};
    dataTab['aspects'] = fn.filterByKeyAndValue(this.aspectsList, 'selected', true).map((item: any, idx: number) => {
      return {
        aspect: {
          id: item.id,
          description: item.description,
          area: item.area,
          response_for_occurrence: item.response_for_occurrence
        },
        index: idx
      };
    });
    return dataTab;
  }

  /**
   * Atualiza a contagem de caracteres ao digitar na área de nota.
   *
   * @param event - Evento de input disparado pelo campo de texto.
   */
  onValueChange(event: any): void {
    this.charachtersCount = event.target.value.length;
    this.counter = `${this.charachtersCount} de ${this.maxlength}`;
  }
}
