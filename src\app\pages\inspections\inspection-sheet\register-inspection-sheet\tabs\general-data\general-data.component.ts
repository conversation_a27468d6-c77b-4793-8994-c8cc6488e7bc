import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';

import { InspectionSheetStatus } from 'src/app/constants/inspections.constants';

import { InspectionSheetService as InspectionSheetServiceApi } from 'src/app/services/api/inspection-sheet.service';
import { UserService } from 'src/app/services/user.service';
import { FormService } from 'src/app/services/form.service';

import { format } from 'date-fns';

@Component({
  selector: 'app-general-data',
  templateUrl: './general-data.component.html',
  styleUrls: ['./general-data.component.scss']
})
export class GeneralDataComponent implements OnInit {
  @Input() public inspectionSheetType: number = null;
  @Input() public status: number = null;
  @Input() public locked: boolean = false;
  @Output() public formChanged = new EventEmitter<any>();
  @Output() public validationChanged = new EventEmitter<any>();

  generalDataForm: FormGroup = this.fb.group({
    Client: [{ value: '', disabled: true }],
    ClientUnit: [{ value: '', disabled: true }],
    StructureCoordinateSetting: [{ value: '', disabled: true }],
    StartDate: [{ value: '', disabled: true }, Validators.required], // Data de Cadastro da Ficha (sempre bloqueada)
    EndDate: [{ value: null, disabled: true }], // Fim da Inspeção (habilitado no EoR)
    CreatedDate: [{ value: '', disabled: true }],
    CreatedBy: [{ value: '', disabled: true }],
    Structure: [{ value: '', disabled: true }],
    Status: [{ value: '', disabled: true }],
    ExecutedBy: [null],
    EvaluatorName: [null],
    EvaluatorPosition: [null],
    EvaluatorCrea: [null],
    EvaluatorArt: [null],
    Responsibles: this.fb.array([])
  });

  public startDateForm = {
    label: 'Início da inspeção',
    small: 'É necessário preencher a data de inspeção para continuar.'
  };

  public externalResponsibleLabel: string = '';

  constructor(
    private fb: FormBuilder,
    private formService: FormService,
    private inspectionSheetServiceApi: InspectionSheetServiceApi,
    private userService: UserService
  ) {}

  /**
   * Método de ciclo de vida do Angular executado ao inicializar o componente.
   * Utilizado aqui para adicionar automaticamente um responsável ao formulário.
   */
  ngOnInit(): void {
    this.addResponsible();
  }

  /**
   * Método de ciclo de vida do Angular acionado quando alguma `@Input()` é alterada.
   * Responsável por atualizar o estado do formulário com base nas mudanças de `status` ou `locked`.
   *
   * @param {SimpleChanges} changes - Objeto contendo as mudanças nas propriedades de entrada do componente.
   */
  ngOnChanges(changes: SimpleChanges): void {
    this.toggleForm();
    if (changes.status || changes.locked) {
      this.toggleForm();
    }
  }

  /**
   * Define a data padrão e o tipo de estrutura para o formulário de revisão.
   * A data é definida como a data e hora atuais no formato 'yyyy-MM-dd HH:mm:ss'.
   */
  setDateDefault() {
    return format(new Date(), 'yyyy-MM-dd HH:mm:ss');
  }

  /**
   * Configura as regras e validações do formulário de dados gerais.
   */
  private configureForm() {
    //"Data de cadastro da ficha" (sempre bloqueada e com a data atual)
    this.generalDataForm.get('CreatedDate').setValue(this.setDateDefault());
    this.generalDataForm.get('CreatedDate').disable();

    let validateField = ['StartDate'];
    let disabledField = ['Client', 'ClientUnit', 'StructureCoordinateSetting', 'CreatedBy', 'Structure', 'Status'];

    switch (this.inspectionSheetType) {
      case 1: //RISR
        break;
      case 2: //EOR
        validateField.push(...['EndDate']);
        disabledField.push(...['ExecutedBy', 'EvaluatorName', 'EvaluatorPosition', 'EvaluatorCrea', 'EvaluatorArt']);
        break;
      case 3: //FIE
        this.startDateForm.label = 'Data da vistoria';
        this.startDateForm.small = 'É necessário preencher a data da vistoria';
        validateField.push(...['ExecutedBy']);
        disabledField.push(...['Responsibles']);
        break;
      case 4: //FIR
        break;
    }

    this.formService.controlValidate(this.generalDataForm, validateField, true);
    this.formService.toggleFormList(this.generalDataForm, disabledField, false);

    this.generalDataForm.get('ExecutedBy').updateValueAndValidity();
    this.generalDataForm.get('Responsibles').updateValueAndValidity();
  }

  /**
   * Obtém o FormArray de responsáveis.
   * @returns {FormArray} - Lista de responsáveis cadastrados.
   */
  get Responsibles(): FormArray {
    return this.generalDataForm.get('Responsibles') as FormArray;
  }

  /**
   * Adiciona um novo responsável ao formulário.
   */
  addResponsible(): void {
    const responsibleGroup = this.fb.group({
      Id: [null], // Pode ser null ou gerar um ID único, se necessário
      Responsible: ['', [Validators.required]], // Nome do responsável (obrigatório)
      IsInternalResponsible: [true, [Validators.required]] // Indica se é interno (boolean)
    });
    this.Responsibles.push(responsibleGroup);
  }

  /**
   * Remove um responsável do formulário pelo índice especificado.
   * @param {number} index - Índice do responsável a ser removido.
   */
  removeResponsible(index: number): void {
    if (this.Responsibles.length > 1) {
      this.Responsibles.removeAt(index);
    } else {
      console.warn('Pelo menos um responsável deve ser mantido.');
    }
  }

  /**
   * Define a lista de responsáveis no formulário.
   * @param {Array<{ Id: string; Responsible: string; IsInternalResponsible: boolean }>} data - Lista de responsáveis.
   */
  setResponsibles(data: { Id: string; Responsible: string; IsInternalResponsible: boolean }[]): void {
    this.Responsibles.clear(); // Limpa os controles existentes

    data.forEach((item) => {
      const responsibleGroup = this.fb.group({
        Id: [item.Id],
        Responsible: [item.Responsible, [Validators.required]],
        IsInternalResponsible: [item.IsInternalResponsible, [Validators.required]]
      });
      this.Responsibles.push(responsibleGroup);
    });
  }

  /**
   * Configura os dados recebidos do componente pai no formulário de dados gerais.
   * @param {any} dados - Dados da ficha de inspeção.
   */
  setData(dados: any) {
    const startDate = dados.start_date ?? this.setDateDefault();
    const coordinate = `${dados.structure.coordinate_setting.coordinate_systems.decimal_geodetic.latitude} Lat ${dados.structure.coordinate_setting.coordinate_systems.decimal_geodetic.longitude} Lng`;
    const createdBy = `${dados.created_by.first_name} ${dados.created_by.surname}`;
    const status = InspectionSheetStatus.find((item) => item.value === dados.status)?.label || 'Valor não encontrado';

    this.generalDataForm.get('Client').setValue(dados.client?.name || 'Cliente não informado');
    this.generalDataForm.get('ClientUnit').setValue(dados.client_unit?.name || 'Unidade não informada');
    this.generalDataForm.get('StructureCoordinateSetting').setValue(coordinate);
    this.generalDataForm.get('StartDate').setValue(startDate);
    this.generalDataForm.get('EndDate').setValue(dados.end_date || null);
    this.generalDataForm.get('CreatedBy').setValue(createdBy);
    this.generalDataForm.get('Structure').setValue(dados.structure?.name || 'Estrutura não informada');
    this.generalDataForm.get('Status').setValue(status);

    // Preenchimento de campos opcionais
    this.generalDataForm.get('ExecutedBy').setValue(dados.executed_by || null);
    this.generalDataForm.get('EvaluatorName').setValue(dados.evaluator_name || null);
    this.generalDataForm.get('EvaluatorPosition').setValue(dados.evaluator_position || null);
    this.generalDataForm.get('EvaluatorCrea').setValue(dados.evaluator_crea || null);
    this.generalDataForm.get('EvaluatorArt').setValue(dados.evaluator_art || null);

    // Configuração dos responsáveis
    if (dados.responsibles && Array.isArray(dados.responsibles)) {
      this.setResponsibles(
        dados.responsibles.map((responsible: any) => ({
          Id: responsible.id || null,
          Responsible: responsible.responsible || 'Responsável não informado',
          IsInternalResponsible: responsible.is_internal_representative || false
        }))
      );
    } else {
      this.Responsibles.clear(); // Limpa se não houver responsáveis
    }

    // Atualização do rótulo de responsável externo
    this.externalResponsibleLabel = dados.client?.name || 'Responsável externo não informado';
    this.toggleForm();
  }

  /**
   * Valida os campos do formulário.
   * @returns {boolean} - Retorna `true` se o formulário for válido, caso contrário, `false`.
   */
  validateForm(): boolean {
    // Usa o método `validateForm` para validar apenas os campos habilitados
    const isValid = this.formService.validateForm(this.generalDataForm);
    if (!isValid) {
      this.generalDataForm.markAllAsTouched();
    }
    return isValid;
  }

  /**
   * Dispara o evento de salvamento ao remover o foco de um campo do formulário.
   * @param {string} controlName - Nome do campo modificado.
   * @param {number} [index] - Índice do item no FormArray, se aplicável.
   */
  onBlur(controlName: string, index?: number): void {
    //return;
    if (this.generalDataForm.disabled) {
      return;
    }
    if (index !== undefined) {
      const control = (this.Responsibles.at(index) as FormGroup).get(controlName);
      if (control?.dirty) {
        this.triggerSave();
      }
    } else {
      const control = this.generalDataForm.get(controlName);
      if (control?.dirty) {
        this.triggerSave();
      }
    }
  }

  /**
   * Dispara o evento de salvamento ao detectar mudanças em um campo do formulário.
   * @param {string} controlName - Nome do campo modificado.
   * @param {number} [index] - Índice do item no FormArray, se aplicável.
   */
  onChange(controlName: string, index?: number): void {
    //return;
    if (this.generalDataForm.disabled) {
      return;
    }
    const control = this.generalDataForm.get(controlName);
    if (control?.dirty) {
      this.triggerSave();
    }
  }

  /**
   * Emite o evento de salvamento quando alterações são detectadas no formulário.
   */
  triggerSave(): void {
    this.validationChanged.emit({ tab: 'general', valid: this.validateForm() });
  }

  /**
   * Alterna entre habilitar ou desabilitar o formulário de dados gerais.
   */
  toggleForm() {
    const isLocked = this.locked || [2, 3].includes(this.status);
    if (isLocked) {
      this.generalDataForm.disable();
    } else {
      this.generalDataForm.enable();
      this.configureForm();
    }
  }
}
