import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';

import { CustomValidators } from 'src/app/utils/custom-validators';
import { ModalConfirm } from 'src/app/constants/message.constants';

import { DxfService } from 'src/app/services/dxf.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';

import { format, parseISO, isAfter, isValid } from 'date-fns';
import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import * as _ from 'lodash';

//Para colocar a URL do arquivo .dxf como seguro
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-review-tab',
  templateUrl: './review-tab.component.html',
  styleUrls: ['./review-tab.component.scss']
})
export class ReviewTabComponent implements OnInit {
  @ViewChild('modalViewDxf') ModalViewDxf: any;
  @ViewChild('modalConfirm') ModalConfirm: any;
  @ViewChild('modalStandard') ModalStandard: any;
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('fileInputStage') fileInputStage!: ElementRef<HTMLInputElement>;

  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public sectionId: any = null;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  @Output() public sendActiveReview = new EventEmitter();

  public formReview: FormGroup = new FormGroup({
    id: new FormControl(null),
    section_id: new FormControl(null),
    start_date: new FormControl(''),
    structure_type: new FormControl([], [Validators.required]),
    drawing: new FormControl(null, Validators.compose([CustomValidators.validadorExtensaoArquivo(['dxf', 'DXF'])])),
    drawing_size: new FormControl('', [Validators.max(10485760)]),
    description: new FormControl(''),
    is_under_construction: new FormControl(false),
    construction_stages: new FormArray([])
  });

  public formConstructionStage: FormGroup = new FormGroup({
    description: new FormControl(''),
    stage: new FormControl('', Validators.required),
    is_current_stage: new FormControl(false),
    drawing: new FormGroup({
      base64: new FormControl(''),
      name: new FormControl('')
    })
  });

  public maxlength: number = 100;
  public charachtersCount: number = 0;
  public counter: string;
  public charCounts: { [key: string]: number } = {};

  public stageMaxlength = 100;
  public charCountsStage: { [key: string]: number } = {};

  public reviewsValidate: boolean = false;

  public structureTypeList: any = [];

  public ctrlReview: boolean = false;
  public editReview: boolean = false;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false, class: 'alert-warning' };
  public messageStage: any = { text: '', status: false, class: 'alert-success' };

  public fileContent: string = '';
  public fileName: string = '';
  public fileContentDownload: any = '';
  public fileDXF: any = null;

  public fileContentView: string = '';
  public fileNameView: string = '';
  public fileContentDownloadView: any = '';
  public fileDXFView: any = null;

  public tableHeader: any = [
    {
      label: 'Revisão',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['reviews'],
      extra: true
    },
    {
      label: 'Usuário',
      width: '160px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['username']
    },
    {
      label: 'Data/Hora de início',
      width: '160px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['start_date_format']
    },
    {
      label: 'Tipo de seção',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['structure_type_name']
    },
    {
      label: 'Descrição',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['description']
    },
    {
      label: 'Documento DXF',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['drawing_name']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom'],
      class: 'd-flex justify-content-center align-items-center'
    }
  ];

  public tableData: any = [];

  public actionCustom: any = [
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-pencil',
      label: '',
      title: 'Editar',
      type: 'true',
      option: 'edit'
    },
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-eye',
      label: '',
      title: 'Visualizar',
      type: 'true',
      option: 'view_dxf_transform',
      condition: { item: 'actionViewDXF', iconTrue: 'fa fa-eye' }
    }
  ];

  public structureTypeDefault = [];
  public activeReviewId: any = -1;
  public activeReviewIndex: any = 0;

  public sectionIdDefault: string = '';

  public dxfInfo = null;

  public stageFileContentView: string = '';
  public stageFileNameView: string = '';
  public stageFileContentDownloadView: any = '';
  public stageFileDXFView: any = null;

  // Tabela Etapa de Obra
  public tableHeaderStage: any = [
    {
      label: 'Etapa Atual',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['select'],
      type: 'check',
      configCheck: {
        disabled: 'check_disabled',
        selected: 'check_selected'
      }
    },
    {
      label: 'Etapa',
      width: '320px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['stage'],
      extra: true
    },
    {
      label: 'Descrição',
      width: '70%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['description']
    },
    {
      label: 'Documento DXF',
      width: '160px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['drawing_name']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom'],
      class: 'd-flex justify-content-center align-items-center'
    }
  ];

  public tableDataStage: any = [];

  // Ações da Tabela Etapa de Obra
  public actionCustomStage: any = [
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-pencil',
      label: '',
      title: 'Editar',
      type: 'true',
      option: 'edit_stage'
    },
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-eye',
      label: '',
      title: 'Visualizar',
      type: 'true',
      option: 'view_dxf_transform_stage',
      condition: { item: 'actionViewDXF', iconTrue: 'fa fa-eye' }
    },
    {
      class: 'btn-logisoil-remove-item',
      icon: 'fa fa-trash',
      label: '',
      title: 'Excluir Etapa',
      type: 'true',
      option: 'delete_stage'
    }
  ];

  public editingStageIndex: number | null = null;

  public modalData: any = {};
  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  public modalDataStandard: any = {};
  public modalTitleStandard: string = '';
  public modalMessageStandard: string = '';
  public modalInstructionStandard: string = '';
  public modalConfigStandard: any = {
    iconHeader: '',
    action: ''
  };

  //public tableDataGroupedStages: any[] = [];

  // Tabela master detail
  public detailHeaderStages: any = [
    { label: 'Etapa', width: '200px', show: true, referent: ['stage'], extra: true },
    { label: 'Descrição', width: '100%', show: true, referent: ['description'] },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom'],
      class: 'd-flex justify-content-center align-items-center',
      showDashWhenNoAction: true
    }
  ];

  // Tabela master detail
  public detailActionCustomStages: any = [
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-eye',
      label: '',
      title: 'Visualizar DXF',
      type: 'true',
      option: 'view_dxf_transform_stage',
      condition: { item: 'actionViewDXF', iconTrue: 'fa fa-eye' }
    },
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-download',
      label: '',
      title: 'Download DXF',
      type: 'true',
      option: 'download_dxf_stage',
      condition: { item: 'actionViewDXF', iconTrue: 'fa fa-download' }
    }
  ];

  public messageReviewsConsistent: any = { text: '', status: false, class: 'alert-warning' };
  public messageConstructionStagesConsistent: any = { text: '', status: false, class: 'alert-warning' };

  constructor(
    private cdr: ChangeDetectorRef,
    private dxfService: DxfService,
    private ngxSpinnerService: NgxSpinnerService,
    private sanitizer: DomSanitizer,
    private sectionsServiceApi: SectionsServiceApi
  ) { }

  /**
   * Método de ciclo de vida do Angular que é executado ao inicializar o componente.
   * Define a data padrão para o campo 'start_date' e o tipo de estrutura.
   */
  ngOnInit(): void {
    this.setDateDefault();
    this.initConstructionStageWatcher();
  }

  /**
   * Define a data padrão e o tipo de estrutura para o formulário de revisão.
   * A data é definida como a data e hora atuais no formato 'yyyy-MM-dd HH:mm:ss'.
   */
  setDateDefault() {
    this.formReview.get('start_date').setValue(format(new Date(), 'yyyy-MM-dd HH:mm:ss'));
    this.formReview.get('structure_type').setValue(this.structureTypeDefault);
  }

  /**
   * Formata os dados da tabela de revisões, aplicando formatação nas datas e tipos de estrutura.
   * Também define o campo de atividade de acordo com a revisão atual.
   * @param {string} [type=''] Tipo opcional para determinar a formatação.
   */
  formatData(type: string = '') {
    this.tableData = this.tableData.map((item: any, index: number) => {
      item.reviews = item.index;
      item.username = item.created_by ? `${item.created_by.first_name} ${item.created_by.surname}` : item.username ? item.username : '-';
      item.start_date_format = moment(item.start_date).format('DD/MM/YYYY HH:mm:ss');
      item.structure_type_name = item.structure_type.name;
      item.drawing_name = item.drawing !== null ? item.drawing.name : '-';
      item.description = item.description;
      item.actionViewDXF = item.drawing !== null ? true : false;
      item.is_under_construction = item.is_under_construction;
      item.is_under_construction_label = item.is_under_construction ? 'Sim' : 'Não';
      item.construction_stages = item.construction_stages;

      // Formata etapas como detalhe (detalhe colapsável)
      const detailData = (item.construction_stages || []).map((stage) => ({
        stage: stage.stage,
        description: stage.description || '-',
        drawing_name: stage.drawing?.name || '-',
        base64: stage.drawing?.base64 || '',
        actionViewDXF: !!stage.drawing?.base64,
        view_dxf_condition: !!stage.drawing?.base64,
        download_dxf_condition: !!stage.drawing?.base64,
        length_data: stage.length_data || null,
        extra:
          stage.length_data?.length_is_consistent === false
            ? {
              position: 'right',
              stage: '',
              custom_icons: [
                {
                  html: '<i class="fas fa-triangle-exclamation fa-lg color-orange ms-2 cursor-pointer" title="Inconsistência de comprimento detectada"></i>',
                  action: 'view_inconsistent_stage',
                  column: 'stage'
                }
              ]
            }
            : {}
      }));

      item.detail = [
        {
          detailHeader: this.detailHeaderStages,
          detailData: detailData,
          detailActionCustom: this.detailActionCustomStages,
          show: false
        }
      ];

      if (item.is_under_construction) {
        item.extra = {
          position: 'right',
          reviews: item.is_under_construction
            ? '<i class="fas fa-hard-hat fa-lg color-green ms-2" title="Esta revisão possui etapa de obra"  container="body" placement="top"></i>'
            : '',
          custom_icons: [
            item?.length_data?.length_is_consistent === false
              ? {
                html: '<i class="fas fa-triangle-exclamation fa-lg color-orange ms-2 cursor-pointer" title="Inconsistência de comprimento detectada"></i>',
                action: 'view_inconsistent',
                column: 'reviews'
              }
              : {}
          ]
        };
      } else {
        item.extra = {};
      }

      return item;
    });

    this.tableData.sort((a, b) => moment(b.start_date).diff(moment(a.start_date)));
    this.formatConstructionStagesFromReviews();
    this.formatMessages();
  }

  /**
   * Verifica e exibe inconsistências de comprimento entre os arquivos DXF e os dados de coordenadas
   * tanto nas revisões da seção quanto em suas etapas de obra.
   *
   * Observação: Nenhuma dessas mensagens é enviada ao backend; são apenas informativas para o usuário.
   */
  formatMessages() {
    let messageLengthData = '';

    const validItems = this.tableData.filter((item: any) => item?.start_date && isValid(parseISO(item.start_date)));

    const latestItem = validItems.reduce((latest: any, current: any) => {
      const currentDate = parseISO(current.start_date);
      const latestDate = parseISO(latest.start_date);
      return isAfter(currentDate, latestDate) ? current : latest;
    }, validItems[0]); // valor inicial seguro

    this.tableData.map((item: any) => {
      // Só processa a revisão mais atual
      const isLatest = item === latestItem;

      //Revisão Seção
      if (item?.length_data?.length_is_consistent === false) {
        if (isLatest) {
          messageLengthData += `<strong>Revisão ${item.index}</strong> - Inconsistência de comprimento detectada.`;
        }
      }

      // Inconsistência nas revisões
      if (messageLengthData != '') {
        this.messageReviewsConsistent = {
          text: `${messageLengthData}`,
          status: true,
          class: 'alert-warning'
        };
      }
    });
  }

  formatMessagesStages(stage) {
    let messageLengthDataStage = '';
    //Revisão Seção
    if (stage?.length_data?.length_is_consistent === false) {
      messageLengthDataStage += `<strong>${stage.stage}</strong> - Inconsistência de comprimento detectada.`;
    }

    // Inconsistência nas revisões
    if (messageLengthDataStage != '') {
      this.messageConstructionStagesConsistent = {
        text: `${messageLengthDataStage}`,
        status: true,
        class: 'alert-warning'
      };
    }
  }

  /**
   * Reseta o formulário de revisão, desabilitando ou habilitando os controles conforme necessário.
   * Também ajusta o estado de edição de revisões.
   * @param {string} [type=''] Tipo de formulário a ser resetado.
   * @param {boolean} [option=false] Define se o controle de revisão será ativado ou não.
   */
  resetForm(type: string = '', option: boolean = false) {
    if (type === 'review') {
      this.ctrlReview = option;
      this.editReview = false;

      // Limpa mensagens
      this.message = { text: '', status: false, class: 'alert-success' };
      this.messageStage = { text: '', status: false, class: 'alert-success' };

      // Limpa os campos do formulário de revisão
      this.formReview.reset({
        id: null,
        section_id: null,
        start_date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
        structure_type: this.structureTypeDefault,
        drawing: null,
        drawing_size: '',
        description: '',
        is_under_construction: false,
        construction_stages: new FormArray([])
      });

      this.formReview.get('is_under_construction')?.enable();

      // Limpa arquivos DXF
      this.clearDrawing();

      // Limpa etapas de obra
      this.constructionStages.clear();
      this.tableDataStage = [];

      // Limpa formulário da etapa atual
      this.formConstructionStage.reset({
        stage: '',
        description: '',
        is_current_stage: false,
        drawing: {
          base64: '',
          name: ''
        }
      });

      // Garante que está fora do modo edição de etapa
      this.editingStageIndex = null;

      // Reinicia os contadores
      this.charCounts['description'] = 0;
      this.charCountsStage['description'] = 0;

      // Prepara próxima etapa
      this.refreshStagesTableAndSuggestNext();

      // Exibir dados de inconsistência da última revisão (se houver)
      if (this.tableData.length > 0) {
        const lastReview = this.tableData[0]; // já ordenado pela data
        const lengthData = lastReview?.length_data;

        if (lengthData && !lengthData.length_is_consistent) {
          this.message = {
            text: `
              <strong>Inconsistência de comprimento detectada</strong>:<br>
              DXF = <strong>${lengthData.dxf_length_in_meters} metros</strong><br>
              Coordenadas = <strong>${lengthData.coordinates_length_in_meters} metros</strong><br>
              Diferença = <span class="text-danger fw-bold">${lengthData.difference_in_meters} metros</span>.
            `,
            status: true,
            class: 'alert-warning'
          };
        }
      }
    }
  }

  /**
   * Carrega o arquivo de desenho (DXF) enviado pelo usuário e inicia o processo de leitura do arquivo.
   * Atualiza os valores do formulário de acordo com o arquivo selecionado.
   *
   * @param {any} $event Evento de envio de arquivo.
   */
  uploadFile($event: any) {
    let file = $event.dataTransfer ? $event.dataTransfer.files[0] : $event.target.files[0];
    this.formReview.get('drawing_size').setValue(file.size);
    let reader = new FileReader();
    reader.onload = this.processFile.bind(this); // Altera para um novo método que fará o processamento
    reader.readAsDataURL(file);
  }

  /**
   * Processa o arquivo carregado pelo usuário.
   * Extrai o conteúdo em base64 e envia para o backend para tratamento.
   * Após a resposta do backend, o arquivo é processado e seu conteúdo é atualizado.
   * @param {any} $event Evento de carregamento de arquivo contendo o arquivo selecionado.
   */
  processFile($event: any) {
    let reader = $event.target;
    this.fileContent = reader.result.split(';base64,')[1]; // Extrai o base64 do arquivo

    this.fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl('data:aplication/octet-stream;base64,' + this.fileContent);
    const fileName = this.formReview.get('drawing').value.split('\\');
    this.fileName = fileName[fileName.length - 1];
    this.fileDXF = fn.base64ToFile(this.fileContent, this.fileName);

    this.fileContentView = this.fileContent;
    this.fileNameView = this.fileName;
    this.fileContentDownloadView = this.fileContentDownload;
    this.fileDXFView = this.fileDXF;
  }

  //Favor não apagar esse método
  // sectionsDXFTransformMaterial(fileInfo, shouldReturnResponseDirectly = false) {
  //   // Condição para retornar a resposta diretamente
  //   if (shouldReturnResponseDirectly) {
  //     return this.sectionsServiceApi.postSectionsDXFTransformMaterial({ base64: fileInfo.fileContent }).toPromise();
  //   }

  //   // Continua com o fluxo normal se a condição for falsa
  //   this.sectionsServiceApi.postSectionsDXFTransformMaterial({ base64: fileInfo.fileContent }).subscribe(
  //     (respTransform: any) => {
  //       // Quando a resposta chegar, substituímos o conteúdo pelo conteúdo tratado
  //       this.fileContent = respTransform.base64; // Usa o base64 tratado retornado pelo backend
  //       this.fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl('data:aplication/octet-stream;base64,' + this.fileContent);
  //       this.fileName = fileInfo.fileName;
  //       this.fileDXF = fn.base64ToFile(this.fileContent, this.fileName);
  //     },
  //     (errorTransform) => {
  //       if (errorTransform.status >= 400) {
  //         console.error('Erro ao tratar o arquivo:', errorTransform);
  //       }
  //     }
  //   );
  // }

  /**
   * Valida o acesso ao formulário de revisão com base nas permissões do usuário.
   * Se o modo de visualização estiver ativado, o formulário é desabilitado.
   * @param {number} [role=0] Papel do usuário, usado para definir permissões de acesso.
   * @returns {any} Retorna um valor relacionado ao papel do usuário.
   */
  validateAccess(role: number = 0): any {
    if (this.view) {
      this.formReview.disable();
    }
  }

  /**
   * Retorna os dados formatados da tabela de revisões.
   * Constrói uma lista de objetos com informações das revisões.
   * @returns {Array} Lista de revisões formatadas.
   */
  getData() {
    if (this.tableData.length > 0) {
      let reviews = this.tableData.map((item: any) => {
        let itemReview = {
          id: null,
          start_date: null,
          structure_type: null,
          drawing: null,
          index: 0,
          description: null,
          is_under_construction: false,
          construction_stages: []
        };
        itemReview.id = item.id;
        itemReview.start_date = item.start_date;
        itemReview.structure_type = item.structure_type;
        itemReview.drawing = item.index == 5 ? null : item.drawing;
        itemReview.index = item.index;
        itemReview.description = item.description;
        itemReview.is_under_construction = item.index == 5 ? true : item.is_under_construction;
        itemReview.construction_stages = item.construction_stages;

        return itemReview;
      });
      return reviews;
    } else {
      return [];
    }
  }

  /**
   * Formata os dados da revisão com base nos valores do formulário.
   * @param {string} [type='add'] Tipo da operação (adição ou edição).
   * @returns {Object} Objeto contendo os dados da revisão.
   */
  formatItemReview(type: string = 'add') {
    let strutureType = fn.filterByKeyAndValue(this.structureTypeList, 'id', this.formReview.get('structure_type').value);
    let start_date = this.formReview.get('start_date').value.replace(' ', 'T');
    let drawing = null;

    if (this.formReview.get('drawing').value) {
      drawing = {
        base64: this.fileContent,
        name: this.fileName
      };
    } else if (this.fileContent) {
      drawing = {
        base64: this.fileContent,
        name: this.fileName
      };
    }

    const username = type === 'add' ? `${this.profile.name} ${this.profile.family_name}` : '-';

    const itemReview = {
      id: type === 'add' ? null : this.formReview.get('id').value,
      section_id: type === 'add' ? null : this.formReview.get('section_id').value,
      start_date: start_date,
      structure_type: {
        id: strutureType[0] ? strutureType[0].id : null,
        name: strutureType[0] ? strutureType[0].name : null
      },
      drawing: drawing,
      active: false,
      description: this.formReview.get('description').value,
      username: username,
      is_under_construction: this.formReview.get('is_under_construction')?.value,
      construction_stages: this.formReview.get('is_under_construction')?.value ? _.cloneDeep(this.extractConstructionStagesFromForm()) : []
    };

    return itemReview;
  }

  /**
   * Adiciona uma nova revisão à tabela de revisões, formatando os dados e limpando o formulário para novas entradas.
   */
  addReviewSection() {
    if (this.formReview.valid) {
      const itemReview = _.cloneDeep(this.formatItemReview('add'));

      itemReview['index'] = this.tableData.length + 1;
      this.tableData.unshift(itemReview);
      if (!this.edit) {
        this.setDateDefault();
      }
      this.formatData();
      this.clearDrawing();

      this.editReview = false;
      this.ctrlReview = false;
      this.ctrlReview = false;
    }
  }

  /**
   * Edita uma revisão existente na tabela, atualizando os dados conforme o formulário e mantendo o status da revisão.
   */
  editReviewSection(): void {
    if (this.formReview.valid) {
      this.ngxSpinnerService.show();

      const itemReview = this.formatItemReview('edit');
      const idxTableData = fn.findIndexInArrayofObject(this.tableData, 'id', this.formReview.get('id').value);

      this.tableData[idxTableData] = {
        ...itemReview,
        index: this.tableData[idxTableData].index
      };

      this.formatData();
      this.setDateDefault();
      this.clearDrawing();

      this.editReview = false;
      this.ctrlReview = false;

      this.message = {
        text: 'Revisão da seção atualizada com sucesso!',
        status: true,
        class: 'alert-success'
      };

      setTimeout(() => {
        this.message.status = false;
      }, 4000);

      this.ngxSpinnerService.hide();
    }
  }

  /**
   * Carrega os dados de uma revisão selecionada na tabela para edição.
   * Atualiza o formulário com os valores correspondentes.
   * @param {number} idx Índice da revisão na tabela.
   * @param {Object} review Objeto contendo os dados da revisão.
   */
  loadReviewSection(idx: number, review: any) {
    this.editReview = true;
    this.ctrlReview = true;

    // Reseta mensagens de alerta
    this.message = { text: '', status: false, class: 'alert-success' };
    this.messageStage = { text: '', status: false, class: 'alert-success' };
    this.formReview.get('id').setValue(review.id);
    this.formReview.get('section_id').setValue(review.section_id);
    this.formReview.get('start_date').setValue(review.start_date);
    this.formReview.get('structure_type').setValue(review.structure_type.id);
    this.formReview.get('description').setValue(review.description);
    this.formReview.get('is_under_construction').setValue(review.is_under_construction ?? false);
    this.formReview.get('is_under_construction').disable();

    // Atualiza contador da descrição da revisão
    this.charCounts['description'] = review.description?.length || 0;

    this.loadDrawing(review.drawing);

    if (review.is_under_construction && review.construction_stages?.length > 0) {
      this.constructionStages.clear(); // zera o FormArray

      const inconsistenciasEtapas: string[] = [];

      review.construction_stages.forEach((stage) => {
        const newStage = this.createConstructionStage();
        newStage.patchValue({
          id: stage.id ?? null,
          stage: stage.stage,
          description: stage.description,
          is_current_stage: stage.is_current_stage,
          drawing: stage.drawing || { base64: '', name: '' }
        });

        // Desabilita campos se for a primeira etapa (Pré-obra)
        if (this.constructionStages.length === 0) {
          newStage.get('stage')?.disable();
          newStage.get('is_current_stage')?.disable();
        }

        this.constructionStages.push(newStage);
        (newStage as any)['length_data'] = stage.length_data || null;

        if (stage.is_current_stage) {
          this.formatMessagesStages(stage);
        }
      });

      this.refreshStagesTableAndSuggestNext();

      // Garante que o formulário da nova etapa esteja habilitado
      this.formConstructionStage.get('stage')?.enable();
      this.formConstructionStage.get('is_current_stage')?.enable();
    }
  }

  /**
   * Carrega o desenho associado a uma revisão, permitindo que ele seja exibido e baixado.
   * @param {Object} drawing Objeto contendo as informações do desenho.
   */
  loadDrawing(drawing) {
    if (drawing != null) {
      this.fileContentView = drawing.base64;
      this.fileContentDownloadView = this.sanitizer.bypassSecurityTrustResourceUrl('data:aplication/octet-stream;base64,' + drawing.base64);
      this.fileNameView = drawing.name;
      this.fileDXFView = fn.base64ToFile(this.fileContentView, this.fileNameView);

      this.fileContent = drawing.base64;
      this.fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl('data:aplication/octet-stream;base64,' + drawing.base64);
      this.fileName = drawing.name;
      this.fileDXF = fn.base64ToFile(this.fileContent, this.fileName);
    }
  }

  /**
   * Limpa os dados do desenho do formulário, removendo o conteúdo do arquivo e redefinindo os campos relacionados.
   */
  clearDrawing() {
    this.fileContent = '';
    this.fileContentDownload = '';
    this.fileName = '';
    this.formReview.get('drawing').setValue(null);
    this.fileDXF = null;

    this.fileContentView = '';
    this.fileContentDownloadView = '';
    this.fileNameView = '';
    this.fileDXFView = null;

    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }

    // Limpar etapas de obra se for revisão em obra
    if (this.formReview.get('is_under_construction')?.value) {
      this.constructionStages.clear();
      this.tableDataStage = [];
    }
  }

  /**
   * Inicializa o observador para o campo `is_under_construction`.
   * Quando ativado em modo de criação e não há etapas adicionadas,
   * define a primeira etapa como padrão com campos desabilitados.
   */
  private initConstructionStageWatcher(): void {
    this.formReview.get('is_under_construction')?.valueChanges.subscribe((value: boolean) => {
      const isCreating = !this.editReview;

      if (value && isCreating && this.constructionStages.length === 0) {
        this.formConstructionStage.patchValue({
          description: '',
          stage: 'Etapa anterior ao início da obra',
          is_current_stage: true
        });

        this.formConstructionStage.get('stage')?.disable();
        this.formConstructionStage.get('is_current_stage')?.disable();
      }

      if (!value && isCreating) {
        this.formConstructionStage.reset({
          description: '',
          stage: '',
          is_current_stage: false,
          drawing: {
            base64: '',
            name: ''
          }
        });

        this.formConstructionStage.get('stage')?.enable();
        this.formConstructionStage.get('is_current_stage')?.enable();
      }
    });
  }

  /**
   * Cria uma nova instância de `FormGroup` representando uma etapa de obra.
   *
   * @returns {FormGroup} - Formulário da etapa de obra.
   */
  createConstructionStage(): FormGroup {
    return new FormGroup({
      id: new FormControl(null),
      description: new FormControl(''),
      stage: new FormControl('', Validators.required),
      is_current_stage: new FormControl(false),
      drawing: new FormGroup({
        base64: new FormControl(''),
        name: new FormControl('')
      })
    });
  }

  /**
   * Getter para acessar o array de etapas de obra do formulário principal.
   *
   * @returns {FormArray} - Lista de etapas de obra.
   */
  get constructionStages(): FormArray {
    return this.formReview.get('construction_stages') as FormArray;
  }

  /**
   * Adiciona uma nova etapa de obra ao `FormArray` principal.
   * - A primeira etapa é automaticamente preenchida e bloqueada.
   * - Etapas subsequentes são preenchidas com base nos campos atuais do formulário.
   * - Após adicionar, limpa o formulário de entrada e atualiza a visualização da tabela.
   */
  addNewConstructionStage(): void {
    if (this.formConstructionStage.invalid) return;

    this.ngxSpinnerService.show();

    setTimeout(() => {
      const newStage = this.createConstructionStage();
      const isFirstStage = this.constructionStages.length === 0;

      if (isFirstStage) {
        newStage.get('stage').setValue('Etapa anterior ao início da obra');
        newStage.get('stage').disable();

        newStage.get('description').setValue(this.formConstructionStage.get('description')?.value || '');

        newStage.get('is_current_stage').setValue(true);
        newStage.get('is_current_stage').disable();

        // Copia o DXF da etapa
        newStage.get('drawing.base64')?.setValue(this.formConstructionStage.get('drawing.base64')?.value);
        newStage.get('drawing.name')?.setValue(this.formConstructionStage.get('drawing.name')?.value);
      } else {
        newStage.patchValue(this.formConstructionStage.value);
      }

      this.constructionStages.push(newStage);

      // Se acabou de adicionar a primeira etapa, libera os campos do formulário para próximas etapas
      if (this.constructionStages.length === 1) {
        this.formConstructionStage.get('stage')?.enable();
        this.formConstructionStage.get('is_current_stage')?.enable();
      }

      // Limpa visualizações do DXF da etapa
      this.stageFileContentView = '';
      this.stageFileNameView = '';
      this.stageFileContentDownloadView = '';
      this.stageFileDXFView = null;

      if (this.fileInputStage) {
        this.fileInputStage.nativeElement.value = '';
      }

      this.messageStage.text = 'Etapa de obra adicionada com sucesso!';
      this.messageStage.status = true;
      this.messageStage.class = 'alert-success';

      setTimeout(() => {
        this.messageStage.status = false;
      }, 4000);

      // Sugerir próxima etapa automaticamente
      this.refreshStagesTableAndSuggestNext();

      // Reinicia contador da descrição da etapa
      this.charCounts['stage_description'] = 0;

      this.ngxSpinnerService.hide();
    }, 300);
  }

  /**
   * Confirma a edição de uma etapa de obra selecionada.
   *
   * - Verifica se o formulário está válido e se há um índice de edição definido.
   * - Atualiza a etapa (`FormGroup`) correspondente no `FormArray` com os novos valores do formulário.
   * - Atualiza a visualização da tabela e sugestões de próxima etapa.
   * - Limpa o estado de edição e remove visualizações de arquivos DXF.
   */
  confirmEditConstructionStage(): void {
    if (this.formConstructionStage.invalid || this.editingStageIndex === null) return;

    this.ngxSpinnerService.show();

    setTimeout(() => {
      const currentStage = this.constructionStages.at(this.editingStageIndex) as FormGroup;
      currentStage.patchValue(this.formConstructionStage.value);

      // Atualiza visualizações
      this.refreshStagesTableAndSuggestNext();

      // Limpa estado de edição
      this.editingStageIndex = null;
      this.clearStageDrawing();

      this.messageStage = {
        text: 'Etapa de obra atualizada com sucesso!',
        status: true,
        class: 'alert-success'
      };

      setTimeout(() => {
        this.messageStage.status = false;
      }, 4000);

      this.ngxSpinnerService.hide();
    }, 300);
  }

  /**
   * Cancela o processo de edição da etapa de obra atual.
   *
   * - Remove o índice de edição (`editingStageIndex`).
   * - Limpa as visualizações de arquivos DXF da etapa.
   * - Atualiza a visualização da tabela e reseta sugestões para nova etapa.
   */
  cancelEditConstructionStage(): void {
    this.editingStageIndex = null;
    this.clearStageDrawing();
    this.refreshStagesTableAndSuggestNext();
  }

  /**
   * Atualiza a tabela de visualização das etapas de obra e prepara o formulário para sugerir a próxima etapa.
   */
  private refreshStagesTableAndSuggestNext(): void {
    this.formatConstructionStagesTable();

    const nextNumber = this.constructionStages.length;

    this.formConstructionStage.patchValue({
      stage: `Etapa ${nextNumber}`,
      description: '',
      is_current_stage: false,
      drawing: {
        base64: '',
        name: ''
      }
    });

    this.charCountsStage['description'] = 0;
  }

  /**
   * Formata os dados da tabela de etapas de obra com base nas informações do formulário.
   * Inclui a identificação da etapa atual para exibição de checkbox marcado.
   */
  private formatConstructionStagesTable(): void {
    this.tableDataStage = this.constructionStages.controls.map((group: any) => {
      const drawing = group.get('drawing')?.value || {};
      const isCurrent = group.get('is_current_stage')?.value || false;
      const lengthData = (group as any)['length_data'] || {};

      let item = {
        stage: group.get('stage')?.value || '-',
        description: group.get('description')?.value || '-',
        drawing_name: drawing.name || '-',
        drawing: drawing,
        is_current_stage: isCurrent,
        check_selected: isCurrent, // usado para refletir no checkbox
        actionViewDXF: Boolean(drawing?.base64 && drawing?.name),
        length_data: lengthData,
        extra: {}
      };

      if (lengthData?.length_is_consistent === false) {
        item.extra = {
          position: 'right',
          stage: '',
          custom_icons: [
            {
              html: '<i class="fas fa-triangle-exclamation fa-lg color-orange ms-2 cursor-pointer" title="Inconsistência de comprimento detectada"></i>',
              action: 'view_inconsistent_stage',
              column: 'stage'
            }
          ]
        };
      }

      return item;
    });
  }

  /**
   * Manipula o upload de arquivos para uma etapa de obra.
   * - Converte o arquivo para base64.
   * - Atualiza os campos do formulário e as visualizações associadas.
   *
   * @param {any} event - Evento de upload do arquivo.
   */
  onUploadStageFile(event: any): void {
    const file = event.dataTransfer ? event.dataTransfer.files[0] : event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader.result.toString().split(';base64,')[1];

      this.formConstructionStage.get('drawing.base64').setValue(base64);
      this.formConstructionStage.get('drawing.name').setValue(file.name);

      this.stageFileContentView = base64;
      this.stageFileNameView = file.name;
      this.stageFileContentDownloadView = this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + base64);
      this.stageFileDXFView = fn.base64ToFile(base64, file.name);
    };

    reader.readAsDataURL(file);
  }

  /**
   * Limpa os dados do desenho (DXF) da etapa atual.
   * Remove valores do formulário e reseta as visualizações associadas.
   */
  clearStageDrawing(): void {
    this.formConstructionStage.get('drawing.base64').setValue('');
    this.formConstructionStage.get('drawing.name').setValue('');

    this.stageFileContentView = '';
    this.stageFileNameView = '';
    this.stageFileContentDownloadView = '';
    this.stageFileDXFView = null;
  }

  /**
   * Define a etapa de obra ativa (atual) com base no índice fornecido.
   * - Garante que apenas uma etapa esteja marcada como atual.
   * - Atualiza a visualização da tabela para refletir a seleção.
   *
   * @param {number} idx - Índice da etapa a ser marcada como atual.
   */
  setActiveReviewStage(idx: number): void {
    if (this.constructionStages.length <= 1) {
      return;
    }

    this.constructionStages.controls.forEach((group, i) => {
      const isCurrent = i === idx;
      group.get('is_current_stage')?.setValue(isCurrent);
    });

    this.tableDataStage = this.tableDataStage.map((item, i) => {
      const selected = i === idx;
      return {
        ...item,
        check_selected: selected
      };
    });
  }

  /**
   * Extrai as etapas cadastradas atualmente no FormArray construction_stages.
   * Usado no momento de salvar ou validar a revisão.
   */
  private extractConstructionStagesFromForm(): any[] {
    return this.constructionStages.controls.map((control) => {
      const group = control as FormGroup;

      const base64 = group.get('drawing.base64')?.value;
      const name = group.get('drawing.name')?.value;

      const drawing = base64 && name ? { base64, name } : null;

      return {
        id: group.get('id')?.value,
        stage: group.get('stage')?.value,
        description: group.get('description')?.value,
        is_current_stage: group.get('is_current_stage')?.value,
        drawing: drawing
      };
    });
  }

  /**
   * Carrega os dados de uma etapa de obra para o formulário de criação/edição.
   * @param index Índice da etapa dentro do FormArray constructionStages.
   */
  editConstructionStage(index: number): void {
    const stageGroup = this.constructionStages.at(index) as FormGroup;

    // Reinicia o contador da descrição da etapa ANTES de popular o form
    this.charCountsStage['description'] = 0;

    // Atualiza os campos do formulário com os valores da etapa selecionada
    this.formConstructionStage.patchValue({
      stage: stageGroup.get('stage')?.value,
      description: stageGroup.get('description')?.value,
      is_current_stage: stageGroup.get('is_current_stage')?.value,
      drawing: {
        base64: stageGroup.get('drawing.base64')?.value,
        name: stageGroup.get('drawing.name')?.value
      }
    });

    // Prepara visualizações de DXF
    const base64 = stageGroup.get('drawing.base64')?.value;
    const name = stageGroup.get('drawing.name')?.value;

    this.stageFileContentView = base64 || '';
    this.stageFileNameView = name || '';
    this.stageFileContentDownloadView = base64 ? this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + base64) : '';
    this.stageFileDXFView = base64 && name ? fn.base64ToFile(base64, name) : null;

    // Desabilita edição de etapa se for a primeira
    const stageValue = stageGroup.get('stage')?.value?.toLowerCase();

    const isEtapaInicial = stageValue === 'etapa anterior ao início da obra';

    if (isEtapaInicial) {
      this.formConstructionStage.get('stage')?.disable();
      this.formConstructionStage.get('is_current_stage')?.disable();
    } else {
      this.formConstructionStage.get('stage')?.enable();
      this.formConstructionStage.get('is_current_stage')?.enable();
    }

    // Zera todos os contadores de etapa antes de editar
    Object.keys(this.charCounts).forEach((key) => delete this.charCounts[key]);

    // Atualiza contador após o form estar populado
    this.cdr.detectChanges();
    this.charCountsStage['description'] = this.formConstructionStage.get('description')?.value?.length || 0;

    // Armazena o índice para saber se é edição (sugiro criar uma nova variável de controle)
    this.editingStageIndex = index;
  }

  /**
   * Exclui uma etapa de obra da lista.
   * Atualiza o índice de edição caso necessário e remove visualmente a etapa.
   * @param index Índice da etapa a ser excluída.
   */
  deleteConstructionStage(index: number): void {
    // Caso estivesse editando essa etapa, cancela a edição
    if (this.editingStageIndex === index) {
      this.cancelEditConstructionStage();
    } else if (this.editingStageIndex !== null && this.editingStageIndex > index) {
      // Ajusta índice de edição se uma etapa anterior foi removida
      this.editingStageIndex--;
    }

    // Agora sim, remove do FormArray
    this.constructionStages.removeAt(index);

    // Atualiza visualmente a tabela e sugestões
    this.refreshStagesTableAndSuggestNext();

    // Feedback opcional
    this.messageStage = {
      text: 'Etapa de obra excluída com sucesso!',
      status: true,
      class: 'alert-success'
    };
    setTimeout(() => (this.messageStage.status = false), 3000);
  }

  /**
   * Gerencia todos os eventos de clique disparados pela tabela de revisões ou etapas de obra.
   * Lida com edição, exclusão, visualização de DXF, marcação de etapa atual, etc.
   * @param $event Objeto com ação e índices.
   */
  clickRowEvent($event: any = null) {
    console.log($event);
    switch ($event.action) {
      case 'clickedRow':
        const detail = this.tableData[$event.index].detail;
        if (detail && detail[0]?.detailData.length > 0) {
          const allHidden = detail.every((d) => !d.show);
          detail.forEach((d) => (d.show = allHidden));
        }
        break;
      case 'edit':
        this.loadReviewSection($event.index, this.tableData[$event.index]);
        break;
      case 'checkbox':
        this.setActiveReviewStage($event.index);
        break;
      case 'view_dxf': //Tela de visualização
      // this.dxfInfo = { dxf: this.tableData[$event.index].drawing };
      // this.ModalViewDxf.openModal();
      // break;
      case 'view_dxf_transform': //Tabela de histórico de revisões
        if (this.tableData[$event.index].actionViewDXF) {
          this.dxfService
            .processDxf({ fileContent: this.tableData[$event.index].drawing.base64, fileNmae: this.tableData[$event.index].drawing.name })
            .subscribe(
              (result) => {
                this.dxfInfo = { dxf: { base64: result.materialTransform.fileContent, name: result.materialTransform.fileName } };
                this.ModalViewDxf.openModal();
              },
              (error) => {
                console.error('Erro no processamento do arquivo:', error);
              }
            );
        }
        break;
      case 'edit_stage':
        this.editConstructionStage($event.index);
        break;
      case 'delete_stage':
        this.modalTitle = 'Excluir Etapa de Obra';
        this.modalMessage = ModalConfirm.ExcluirEtapaDeObra;
        this.modalInstruction = null;
        this.modalConfig = { iconHeader: null, action: 'confirmDelete' };
        this.modalData = { id: $event.index };
        this.ModalConfirm.openModal();
        break;
      case 'confirmDelete':
        if ($event?.data?.id !== undefined) {
          this.deleteConstructionStage($event.data.id);
        }
        break;
      case 'view_dxf_transform_stage':
        const rowIdxDxf = $event.rowIdx;
        const idxDxf = $event.index;
        let base64Stage = null;
        let fileNameStage = null;

        if (this.tableDataStage[$event.index]?.actionViewDXF) {
          base64Stage = this.tableDataStage[$event.index].drawing.base64;
          fileNameStage = this.tableDataStage[$event.index].drawing.name;
        }

        if (this.tableData[rowIdxDxf]?.detail[0]?.detailData[idxDxf]) {
          const stageItemDxf = this.tableData[rowIdxDxf].detail[0].detailData[idxDxf];
          console.log(stageItemDxf);
          base64Stage = stageItemDxf.base64;
          fileNameStage = stageItemDxf.drawing_name;
        }

        if (base64Stage != null && fileNameStage != null) {
          this.dxfService.processDxf({ fileContent: base64Stage, fileNmae: fileNameStage }).subscribe(
            (result) => {
              this.dxfInfo = { dxf: { base64: result.materialTransform.fileContent, name: result.materialTransform.fileName } };
              this.ModalViewDxf.openModal();
            },
            (error) => {
              console.error('Erro no processamento do arquivo:', error);
            }
          );
        }

        break;
      case 'download_dxf_stage':
        const rowIdx = $event.rowIdx;
        const idx = $event.index;
        const stageItem = this.tableData[rowIdx].detail[0].detailData[idx];
        const base64 = stageItem.base64;
        const name = stageItem.drawing_name || 'etapa.dxf';

        if (base64) {
          const blob = fn.base64toBlob(base64);
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
        break;
      case 'view_inconsistent':
        let review = this.tableData[$event.index];
        this.modalTitleStandard = `Inconsistência de comprimento detectada - Revisão ${review.index} `;
        this.modalMessageStandard = `<ul class="mt-2">
          <li>DXF = ${review.length_data.dxf_length_in_meters} metros</li>
          <li>Coordenadas = ${review.length_data.coordinates_length_in_meters} metros</li>
          <li>Diferença = <span class="text-danger fw-bold">${review.length_data.difference_in_meters} metros</span></li>
          <ul>
        `;
        this.modalInstructionStandard = null;
        this.modalConfigStandard = { iconHeader: null, action: '' };
        this.modalDataStandard = { id: $event.index };
        this.ModalStandard.openModal();
        break;
      case 'view_inconsistent_stage':
        let stage = null;
        const rowIdxStage = $event.rowIdx;
        const idxStage = $event.index;

        if (this.tableDataStage[$event.index]?.length_data) {
          stage = this.tableDataStage[$event.index];
        }

        if (this.tableData[rowIdxStage]?.detail[0]?.detailData[idxStage]) {
          stage = this.tableData[rowIdxStage]?.detail[0]?.detailData[idxStage];
        }

        this.modalTitleStandard = `Inconsistência de comprimento detectada -  ${stage.stage} `;
        this.modalMessageStandard = `<ul class="mt-2">
          <li>DXF = ${stage.length_data.dxf_length_in_meters} metros</li>
          <li>Coordenadas = ${stage.length_data.coordinates_length_in_meters} metros</li>
          <li>Diferença = <span class="text-danger fw-bold">${stage.length_data.difference_in_meters} metros</span></li>
          <ul>
        `;
        this.modalInstructionStandard = null;
        this.modalConfigStandard = { iconHeader: null, action: '' };
        this.modalDataStandard = { id: $event.index };
        this.ModalStandard.openModal();
        break;
      default:
        break;
    }
  }

  /**
   * Agrupa e formata as etapas de obra (`construction_stages`) associadas a cada revisão da seção (`review`)
   * para exibição em formato de tabela detalhada (master-detail).
   *
   * - Filtra apenas as revisões marcadas como `is_under_construction`.
   * - Para cada etapa encontrada, adiciona propriedades auxiliares como:
   *   - `index`: índice da etapa dentro da revisão
   *   - `reviewIndex`: índice da revisão principal
   *   - `reviewId`: identificador da revisão
   *   - `drawing_name`: nome do arquivo DXF ou '-'
   *   - `actionViewDXF`: flag booleana para exibir botão de visualização
   *   - `check_selected`: indica se a etapa é a atual
   * - Preenche `tableDataGroupedStages` com objetos contendo cabeçalho, dados detalhados e ações customizadas.
   */
  public formatConstructionStagesFromReviews(): void {
    // this.tableDataGroupedStages = [];

    this.tableData.forEach((review, reviewIdx) => {
      if (review.is_under_construction && Array.isArray(review.construction_stages) && review.construction_stages.length > 0) {
        const stages = review.construction_stages.map((stage, stageIdx) => ({
          ...stage,
          index: stageIdx,
          reviewIndex: review.index,
          reviewId: review.id,
          drawing_name: stage.drawing?.name || '-',
          actionViewDXF: !!(stage.drawing?.base64 && stage.drawing?.name),
          check_selected: stage.is_current_stage,
          length_data: stage.length_data || null
        }));

        // this.tableDataGroupedStages.push({
        //   reviewLabel: `Etapas da Revisão ${review.index}`,
        //   detailHeader: this.tableHeaderStage,
        //   detailData: stages,
        //   detailActionCustom: this.actionCustomStage,
        //   show: true // você pode controlar visibilidade com botão de toggle, se quiser
        // });
      }
    });
  }

  /**
   * Atualiza o contador de caracteres para campos da revisão.
   * @param field Campo alvo (ex: 'description').
   * @param event Evento de digitação.
   */
  onValueChange(field: string, event: any): void {
    this.charCounts[field] = event.target.value.length;
  }

  /**
   * Atualiza o contador de caracteres dos campos da etapa de obra.
   * @param field Campo alvo (ex: 'description').
   * @param event Evento de digitação.
   */
  onValueChangeStage(field: string, event: any): void {
    this.charCountsStage[field] = event.target.value.length;
  }
}
