import { Component, ElementRef, EventEmitter, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-calendar-activities',
  templateUrl: './modal-calendar-activities.component.html',
  styleUrls: ['./modal-calendar-activities.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalCalendarActivitiesComponent implements OnInit {
  @ViewChild('modalCalendarActivities') ModalCalendarActivities: ElementRef;
  @Output() public sendEvent = new EventEmitter();

  public formCalendarActivity: FormGroup = new FormGroup({
    title: new FormControl([], [Validators.required]),
    description: new FormControl(null, [Validators.maxLength(1000), Validators.required]),
    initial_date: new FormControl(null, [Validators.required]),
    final_date: new FormControl(null, [Validators.required]),
    repeat: new FormControl(false)
  });

  public maxlength: number = 1000;
  public charachtersCount: number = 0;
  public counter: string;

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  openModal() {
    this.modalService.open(this.ModalCalendarActivities);
  }

  onValueChange(event: any): void {
    this.charachtersCount = event.target.value.length;
    this.counter = `${this.charachtersCount} de ${this.maxlength}`;
  }

  getForm(option = 'addActivity') {
    this.sendEvent.emit({ option: option, form: this.formCalendarActivity.controls });
  }
}
