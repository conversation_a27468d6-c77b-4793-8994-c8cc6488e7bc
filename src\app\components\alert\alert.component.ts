import { Component, Input, OnInit, ElementRef, Renderer2, OnDestroy, Output, EventEmitter, OnChanges } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-alert',
  templateUrl: './alert.component.html',
  styleUrls: ['./alert.component.scss']
})
export class AlertComponent implements OnInit, OnDestroy, OnChanges {
  @Input() public class: string = 'alert-danger';
  @Input() public messages: any = [];
  @Input() public label: any = '';
  @Input() public showCloseButton: boolean = false;
  isVisible: boolean = true;
  @Input() public onClose: () => void;
  @Output() public sendClickEvent = new EventEmitter();

  public sanitizedMessages: SafeHtml[] = [];
  private clickListeners: (() => void)[] = [];

  constructor(private renderer: Renderer2, private el: ElementRef, private sanitizer: DomSanitizer) {}

  ngOnInit(): void {
    this.sanitizeMessages();
  }

  ngOnChanges(): void {
    this.sanitizeMessages();
  }

  ngOnDestroy(): void {
    this.removeClickListeners();
  }

  sanitizeMessages(): void {
    this.sanitizedMessages = [];
    if (this.messages && this.messages.length > 0) {
      this.sanitizedMessages = this.messages.map((msg: any) => this.sanitizer.bypassSecurityTrustHtml(msg.message));
    }
  }

  closeAlert(): void {
    this.isVisible = false;
    if (this.onClose) {
      this.onClose();
    }
  }

  clickEvent(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.sendClickEvent.emit({ action: 'downloadPlanilhaErros' });
  }

  ngAfterViewInit() {
    this.addClickListeners();
  }

  ngAfterViewChecked() {
    this.removeClickListeners();
    this.addClickListeners();
  }

  addClickListeners() {
    const alertElement = this.el.nativeElement.querySelector('.alert');
    if (alertElement) {
      const links = alertElement.querySelectorAll('a');
      links.forEach((link: HTMLAnchorElement) => {
        const listener = this.renderer.listen(link, 'click', (event) => this.clickEvent(event));
        this.clickListeners.push(listener);
      });
    }
  }

  removeClickListeners() {
    this.clickListeners.forEach((unlisten) => unlisten());
    this.clickListeners = [];
  }
}
