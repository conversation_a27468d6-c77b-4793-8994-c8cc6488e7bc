<div class="col-md-12">
  <form [formGroup]="formVerticalStressRatio">
    <div class="row">
      <label class="form-label" style="font-style: italic">Fórmula:</label>
      <div class="col-md-3">
        <img
          src="assets/images/static-materials/vertical-stress-ratio.png"
          class="img-fluid img-thumbnail"
          style="max-height: 80px; width: auto"
        />
      </div>
    </div>
    <div class="row mt-1">
      <!-- Raz<PERSON> de resistência -->
      <div class="col-md-4">
        <label class="form-label">Razão de resistência</label>
        <input
          type="text"
          class="form-control"
          formControlName="resistance_ratio"
          min="0"
          step="0.01"
          (keypress)="
            func.controlNumber(
              $event,
              formVerticalStressRatio.get('resistance_ratio'),
              'positiveDecimalDot'
            )
          "
          (keyup)="
            func.controlNumber(
              $event,
              formVerticalStressRatio.get('resistance_ratio')
            )
          "
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formVerticalStressRatio.get('resistance_ratio').valid &&
            formVerticalStressRatio.get('resistance_ratio').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Resistência mínima ao cisalhamento (kPa) -->
      <div class="col-md-4">
        <label class="form-label">Resistência mínima cisalhamento</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="minimum_shear_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formVerticalStressRatio.get('minimum_shear_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formVerticalStressRatio.get('minimum_shear_strength')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formVerticalStressRatio.get('minimum_shear_strength').valid &&
            formVerticalStressRatio.get('minimum_shear_strength').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Resistência máxima ao cisalhamento (kPa) -->
      <div class="col-md-4">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_maximum_shear_strength"
          (change)="
            formService.checkboxControlValidate(
              formVerticalStressRatio,
              'maximum_shear_strength'
            )
          "
        />
        <label class="form-label">Resistência máxima cisalhamento</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="maximum_shear_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formVerticalStressRatio.get('maximum_shear_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formVerticalStressRatio.get('maximum_shear_strength')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formVerticalStressRatio.get('maximum_shear_strength').valid &&
            formVerticalStressRatio.get('maximum_shear_strength').touched &&
            formVerticalStressRatio.get('is_maximum_shear_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <div class="row mt-1">
      <!-- Resistência a tração (kPa) -->
      <div class="col-md-4">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_tensile_strength"
          (change)="
            formService.checkboxControlValidate(
              formVerticalStressRatio,
              'tensile_strength'
            )
          "
        />
        <label class="form-label">Resistência a tração</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="tensile_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formVerticalStressRatio.get('tensile_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formVerticalStressRatio.get('tensile_strength')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>

        <small
          class="form-text text-danger"
          *ngIf="
            !formVerticalStressRatio.get('tensile_strength').valid &&
            formVerticalStressRatio.get('tensile_strength').touched &&
            formVerticalStressRatio.get('is_tensile_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
  </form>
</div>
