import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { AppGuard } from '../../guards/app.guard';

import { ListInspectionsComponent } from './list-inspections/list-inspections.component';
import { InspectionSheetComponent } from './inspection-sheet/inspection-sheet.component';
import { RegisterInspectionSheetComponent } from './inspection-sheet/register-inspection-sheet/register-inspection-sheet.component';
import { ViewInspectionSheetComponent } from './inspection-sheet/view-inspection-sheet/view-inspection-sheet.component';
import { ViewOccurrenceComponent } from './occurrences/view-occurrence/view-occurrence.component';
import { RegisterActionPlanComponent } from './action-plans/register-action-plan/register-action-plan.component';

const routes: Routes = [
  {
    path: '',
    component: ListInspectionsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.Inspecoes,
    component: InspectionSheetComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.FichaFIE,
    component: RegisterInspectionSheetComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.FichaEoR,
    component: RegisterInspectionSheetComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.FichaRISR,
    component: RegisterInspectionSheetComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.FichaFIR,
    component: RegisterInspectionSheetComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarFicha,
    component: ViewInspectionSheetComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarOcorrencia,
    component: ViewOccurrenceComponent,
    canActivate: []
  },
  {
    path: Rotas.PlanoDeAcao,
    component: RegisterActionPlanComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarPlanoDeAcao,
    component: RegisterActionPlanComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarPlanoDeAcao,
    component: RegisterActionPlanComponent,
    canActivate: [AppGuard]
  },
  //Breadcrumb
  {
    path: 'action-plan',
    component: RegisterActionPlanComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class InspectionsRoutingModule {}
