<div class="list-content">
  <div class="button-material">
    <app-button
      *ngIf="permissaoUsuario?.create"
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Cadastrar Material'"
      [routerLink]="['create']"
    ></app-button>
  </div>

  <div class="row mt-2">
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <div class="row g-3 mt-1">
    <!-- SearchIdentifier -->
    <div class="col-md-1">
      <label class="form-label">ID</label>
      <input
        [(ngModel)]="filter.SearchIdentifier"
        type="number"
        step="1"
        min="1"
        class="form-control"
        autocomplete="off"
        (keypress)="
          func.controlNumber(
            $event,
            filter.SearchIdentifier,
            'positive',
            'ngModel'
          )
        "
        (keyup)="
          func.controlNumber($event, filter.SearchIdentifier, null, 'ngModel')
        "
      />
    </div>
    <!-- Material -->
    <div class="col-md-2">
      <label class="form-label">Material</label>
      <input [(ngModel)]="filter.Name" type="text" class="form-control" />
    </div>

    <!-- Selects Cliente, Unidade e Estrutura -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-9"
    ></app-hierarchy>

    <!-- Visualizaçao -->
    <div class="col-md-3">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>

    <!-- Botoes -->
    <div class="col-md-9 d-flex align-items-end justify-content-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        class="me-1"
        (click)="managerFilters(true)"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>

    <!-- Alertas -->
    <div class="alert alert-warning" role="alert" *ngIf="messageReturn.status">
      {{ messageReturn.text }}
    </div>

    <div
      class="alert alert-success"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
    >
      {{ message.text }}
    </div>

    <!-- Tabela -->
    <app-table
      *ngIf="tableData.length > 0"
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeader"
      [tableSubheader]="tableSubheader"
      [tableData]="tableData"
      (sendToggleStatus)="toggleStatus($event)"
      (sendClickRowEvent)="clickRowEvent($event)"
      [permissaoUsuario]="permissaoUsuario"
      [menuMiniDashboard]="'miniDashboardStaticMaterials'"
    >
    </app-table>
  </div>

  <!-- Paginacao -->
  <app-paginator
    *ngIf="tableData.length > 0"
    [collectionSize]="collectionSize"
    [page]="page"
    [maxSize]="10"
    [boundaryLinks]="true"
    [pageSize]="pageSize"
    (sendPageChange)="loadPage($event)"
    [enableItemPerPage]="true"
  ></app-paginator>

  <!-- Botao Voltar -->
  <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela inicial'"
      [click]="goBack.bind(this)"
    ></app-button>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
