<ng-template #modalAnalysis let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">Análise de Estabilidade - Seção {{ title }}</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="resetVariables(); d('Cross click')"
    ></button>
  </div>
  <form [formGroup]="formStabilityAnalysis">
    <div class="modal-body">
      <div class="row" *ngIf="this.tableData.length > 0">
        <!-- Condições -->
        <div class="col-md-3">
          <label class="form-label">Condições</label>
          <select class="form-select" formControlName="SliFileType">
            <option value="">Selecione...</option>
            <ng-template ngFor let-item [ngForOf]="sliFileType">
              <option [ngValue]="item.value">
                {{ item.name }}
              </option>
            </ng-template>
          </select>
        </div>
        <!-- Método de Cáclulo -->
        <div class="col-md-3">
          <label class="form-label">Método de Cáclulo</label>
          <select class="form-select" formControlName="CalculationMethods">
            <option value="">Selecione...</option>
            <ng-template ngFor let-item [ngForOf]="calculationMethods">
              <option [ngValue]="item.value">
                {{ item.label }}
              </option>
            </ng-template>
          </select>
        </div>
        <!-- Exibir DXF -->
        <div
          class="col-md-3 d-flex align-items-end"
          *ngIf="
            formStabilityAnalysis.controls['SliFileType'].value != '' &&
            formStabilityAnalysis.controls['CalculationMethods'].value != ''
          "
        >
          <app-button
            [class]="'btn-logisoil-blue'"
            [icon]="'fa fa-file'"
            [label]="'Exibir DXF'"
            (click)="getSafetyFactorResult()"
          ></app-button>
        </div>
      </div>
      <!-- Mensagem de alerta -->
      <div
        class="col-md-12 mt-3 alert"
        [ngClass]="message.class"
        role="alert"
        *ngIf="message.status"
      >
        {{ message.text }}
      </div>
      <app-alert
        class="mt-3"
        [class]="'alert-danger'"
        [messages]="messagesError"
      ></app-alert>
      <!-- Tabela Informativa -->
      <div class="row mt-3" *ngIf="this.tableData.length > 0">
        <div class="col-md-12">
          <app-table [tableHeader]="tableHeader" [tableData]="tableData">
          </app-table>
        </div>
      </div>
      <!-- DXF -->
      <div class="row mt-2">
        <div class="col-md-12">
          <app-dxf-viewer [fileDxf]="fileDxf"></app-dxf-viewer>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <app-button
        [class]="'btn-logisoil-gray'"
        [label]="'Fechar'"
        (click)="resetVariables(); c('Close click')"
      >
      </app-button>
    </div>
  </form>
</ng-template>
