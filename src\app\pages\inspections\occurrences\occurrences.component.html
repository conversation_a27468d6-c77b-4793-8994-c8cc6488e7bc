<div class="occurrences-container">
  <!-- Selects Cliente, Unidade e Estrutura -->
  <div class="row filters">
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-12"
      (sendEventHierarchy)="getEventHierarchy($event)"
    ></app-hierarchy>
  </div>

  <div class="row filters mt-3">
    <!-- OccurrenceDateFilter -->
    <div class="col-md-3">
      <label class="form-label">Filtro de data</label>
      <select class="form-select" [(ngModel)]="selectedDateFilter">
        <option value="noFilter">Não filtrar</option>
        <option *ngFor="let status of dateFilter" [value]="status.value">
          {{ status.label }}
        </option>
      </select>
    </div>

    <!-- Periodo -->
    <div class="col-md-3" *ngIf="selectedDateFilter !== '3'">
      <label class="form-label">Período</label>
      <select
        class="form-select"
        [disabled]="selectedDateFilter === 'noFilter'"
        [(ngModel)]="filter.Period"
        (change)="calculatePeriod(filter.Period)"
      >
        <option *ngFor="let period of datePeriod" [value]="period.value">
          {{ period.label }}
        </option>
      </select>
    </div>

    <!-- Dias Restantes de Prazo -->
    <div class="col-md-3" *ngIf="selectedDateFilter === '3'">
      <label class="form-label">Dias restantes de prazo </label>
      <input
        type="number"
        class="form-control"
        min="0"
        max="99999999"
        step="1"
        [(ngModel)]="filter.DaysRemaining"
      />
    </div>

    <!-- Data inicial -->
    <div class="col-md-2">
      <label class="form-label">Data inicial</label>
      <input
        type="date"
        class="form-control"
        [(ngModel)]="filter.StartDate"
        [ngClass]="{ readonly: disableDateFields }"
        [disabled]="
          disableDateFields ||
          selectedDateFilter === '3' ||
          selectedDateFilter === 'noFilter'
        "
      />
      <!-- <div class="form-check">
        <input
          type="checkbox"
          class="form-check-input"
          id="allDatesCheckbox"
          [(ngModel)]="filter.AllDates"
        />
        <label class="form-check-label" for="allDatesCheckbox">
          Todas as datas
        </label>
      </div> -->
    </div>

    <!-- Data final -->
    <div class="col-md-2">
      <label class="form-label">Data final</label>
      <input
        type="date"
        class="form-control"
        [(ngModel)]="filter.EndDate"
        [ngClass]="{ readonly: disableDateFields }"
        [disabled]="
          disableDateFields ||
          selectedDateFilter === '3' ||
          selectedDateFilter === 'noFilter'
        "
      />
    </div>

    <!-- OccurrenceStatus -->
    <div class="col-md-2">
      <label class="form-label">Status</label>
      <select class="form-select" [(ngModel)]="selectedStatus">
        <option value="all">Todos</option>
        <option *ngFor="let status of occurrenceStatus" [value]="status.value">
          {{ status.label }}
        </option>
      </select>
    </div>
  </div>

  <div class="row filters mt-3 mb-3">
    <!-- InspectionSheetType -->
    <div class="col-md-3">
      <label class="form-label">Origem</label>
      <select class="form-select" [(ngModel)]="selectedOrigin">
        <option value="all">Todas</option>
        <option
          *ngFor="let origin of inspectionSheetType"
          [value]="origin.value"
        >
          {{ origin.label }}
        </option>
      </select>
    </div>

    <!-- WithActionPlan -->
    <div class="col-md-3">
      <label class="form-label">Plano de ação</label>
      <select class="form-select" [(ngModel)]="selectedActionPlan">
        <option value="">Todos</option>
        <option *ngFor="let plan of actionPlan" [value]="plan.value">
          {{ plan.label }}
        </option>
      </select>
    </div>

    <!-- Visualização -->
    <div class="col-md-3">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>

    <!-- Botões -->
    <div class="col-md-3 d-flex align-items-end">
      <app-button
        [class]="'btn-logisoil-blue me-2'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        (click)="searchOccurrences()"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>
  </div>

  <!-- Conversão de coordenadas -->
  <div class="row mt-3 mb-3">
    <label class="form-label"
      ><i class="fa fa-refresh"></i> Conversão de Coordenadas</label
    >
    <!-- Datum -->
    <div class="col-md-3">
      <label class="form-label">DATUM</label>
      <select class="form-select" [(ngModel)]="filter.Datum">
        <option value="">Selecione...</option>
        <option *ngFor="let item of OutputDatum" [ngValue]="item.id">
          {{ item.value }}
        </option>
      </select>
    </div>
    <!-- Coordenadas -->
    <div class="col-md-3">
      <label class="form-label">Coordenadas</label>
      <select class="form-select" [(ngModel)]="filter.CoordinateFormat">
        <option *ngFor="let item of coordinateFormat" [ngValue]="item.id">
          {{ item.label }}
        </option>
      </select>
    </div>
    <!-- Converter -->
    <div class="col-md-3 d-flex align-items-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-refresh'"
        [label]="'Converter'"
        class="me-1"
        (click)="searchOccurrences()"
      ></app-button>
    </div>
  </div>

  <!-- Mapa -->
  <div class="row mt-3 mb-3">
    <div class="col-md-12">
      <!-- Botão de Mapa -->
      <app-button
        [class]="'btn-logisoil-green me-2'"
        [customBtn]="true"
        [icon]="'fas fa fa-map-location-dot'"
        [label]="'Mapa'"
        [ngbTooltip]="'Expandir/recolher o mapa'"
        [tooltipClass]="'info-tooltip'"
        [placement]="'end'"
        (click)="toggleMap()"
      ></app-button>

      <!-- Botões visíveis quando mapa está aberto -->
      <div
        *ngIf="showMaps"
        class="d-flex flex-wrap gap-2 mt-2 align-items-center"
      >
        <!-- Botão: Remover marcadores (abre modal) -->
        <app-button
          [class]="'btn-logisoil-red'"
          [customBtn]="true"
          [icon]="'fa fa-map-marker'"
          [label]="'Remover marcadores'"
          [ngbTooltip]="'Remove todos os marcadores do mapa'"
          [placement]="'top'"
          (click)="confirmRemoveMarkers()"
        ></app-button>
        <!-- Botão: Remover fotos -->
        <app-button
          [class]="'btn-logisoil-red'"
          [customBtn]="true"
          [icon]="'fa fa-file-image-o'"
          [label]="'Remover fotos'"
          [ngbTooltip]="'Remove todas as fotos vinculadas ao mapa'"
          [placement]="'top'"
          (click)="confirmRemovePhotos()"
        ></app-button>
      </div>

      <!-- Aviso quando não há estrutura selecionada -->
      <div
        *ngIf="showMaps && !extractHierarchyFilters().StructureId"
        class="text-muted mt-2 small"
      >
        🔍 Para exibir o mapa de uma estrutura, selecione Cliente, Unidade,
        Estrutura e clique em Buscar.
      </div>
    </div>
  </div>

  <!-- Mapa (exibido ao clicar no botão) -->
  <div class="row mb-3">
    <div class="col-md-12 maps" [ngClass]="showMaps ? 'show' : ''">
      <app-google-maps [height]="showMaps ? '400px' : '0px'"> </app-google-maps>

      <!-- Legenda do status -->
      <div *ngIf="showMaps">
        <label class="mt-2">Legenda:</label>
        <div class="row">
          <div
            class="col-auto d-flex align-items-center"
            *ngFor="let item of statusLegendList"
          >
            <i
              class="{{ item.icon }} me-2"
              [style.color]="item.color"
              [class.blink]="item.value === 6"
            ></i>
            <span class="legend list-status">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Alerta -->
    <div
      class="alert"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
    >
      {{ message.text }}
    </div>

    <!-- Tabela -->
    <div class="row table-wrapper mt-3">
      <div class="col-md-12">
        <app-table
          *ngIf="tableData.length > 0"
          [tableHeader]="tableHeader"
          [tableData]="tableData"
          [permissaoUsuario]="permissaoUsuario"
          (sendClickRowEvent)="clickRowEvent($event)"
        ></app-table>
      </div>
    </div>

    <!-- Paginação -->
    <div class="row mt-3" *ngIf="tableData.length > 0">
      <app-paginator
        [collectionSize]="collectionSize"
        [page]="page"
        [maxSize]="10"
        [boundaryLinks]="true"
        [pageSize]="pageSize"
        (sendPageChange)="loadPage($event)"
        [enableItemPerPage]="true"
      ></app-paginator>
    </div>
  </div>

  <!-- Modal Exibir anexos -->
  <app-modal-attachments-trail
    *ngIf="showAttachmentsModal"
    [attachmentsTrail]="attachmentsTrail"
    [occurrenceSearchIdentifier]="selectedOccurrenceSearchIdentifier"
    (close)="showAttachmentsModal = false"
    (addToMap)="handleAddToMap($event)"
  ></app-modal-attachments-trail>

  <!-- Modal de confirmação -->
  <app-modal-confirm
    #modalConfirm
    (sendClickEvent)="clickEvent($event)"
    [title]="modalTitle"
    [message]="modalMessage"
    [instruction]="modalInstruction"
    [modalConfig]="modalConfig"
    [data]="modalData"
    [id]="modalId"
  ></app-modal-confirm>

  <app-modal-occurrence-details
    #modalOccurrenceDetails
    [config]="configModal"
  ></app-modal-occurrence-details>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
