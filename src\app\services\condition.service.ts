import { Injectable } from '@angular/core';
import { FormGroup, FormArray, FormControl } from '@angular/forms';
import _ from 'lodash';

@Injectable({
  providedIn: 'root'
})
export class ConditionService {
  /**
   * Copia os valores de um componente de condição (`fromElement`) para outro (`toElement`),
   * incluindo formulários, propriedades visuais, seleção de modelos constitutivos e estados relacionados.
   *
   * @param fromElement Elemento de origem da cópia.
   * @param toElement Elemento de destino da cópia.
   */
  copyElementFromTo(fromElement, toElement) {
    if (fromElement && toElement) {
      // Copia os valores do formulário do 'fromCondition' para o 'toCondition'
      toElement.formConditionStaticMaterials.patchValue(fromElement.formConditionStaticMaterials.getRawValue());

      toElement.showColorPicker = fromElement.showColorPicker;
      toElement.selectedColor = fromElement.selectedColor;
      toElement.dropdownSettings = fromElement.dropdownSettings;
      toElement.materialsListSettings = fromElement.materialsListSettings;
      toElement.selectedConstitutiveModel = fromElement.selectedConstitutiveModel;
      toElement.selectedHu = fromElement.selectedHu;
      toElement.constitutiveModel = fromElement.constitutiveModel;
      toElement.waterSurface = fromElement.waterSurface;
      toElement.hu = fromElement.hu;
      toElement.dataElement = fromElement.dataElement;
      toElement.use_drained_resistance_over_water_surface = fromElement.use_drained_resistance_over_water_surface;
      toElement.formService.checkboxControlValidate(toElement.formConditionStaticMaterials, 'saturated_specific_weight');

      if (toElement.formConditionStaticMaterials.get('hu').value) {
        toElement.itemEvent(null, 'select', 'hu');
        toElement.formService.dependencyControlValidate(toElement.formConditionStaticMaterials, 'hu', 'custom_hu_value', toElement.selectedHu == 1);
      } else {
        toElement.itemEvent(null, 'deselect', 'hu');
        toElement.formService.dependencyControlValidate(toElement.formConditionStaticMaterials, 'hu', 'custom_hu_value', toElement.selectedHu == 1);
      }

      setTimeout(() => {
        this.copyConstituiveModel(fromElement, toElement);
      }, 100);
    }
  }

  /**
   * Copia os dados do modelo constitutivo selecionado de um elemento para outro.
   * A cópia respeita o tipo de modelo (Mohr-Coulomb, Undrained, etc).
   *
   * @param fromElement Elemento de origem.
   * @param toElement Elemento de destino.
   */
  copyConstituiveModel(fromElement, toElement) {
    if (fromElement.selectedConstitutiveModel != '' && fromElement.selectedConstitutiveModel != null) {
      let constitutiveModelElementTo = toElement.getConstitutiveModelElement(toElement.selectedConstitutiveModel);
      let constitutiveModelElementFrom = fromElement.getConstitutiveModelElement(fromElement.selectedConstitutiveModel);

      switch (fromElement.selectedConstitutiveModel.toString()) {
        case '1': //mohrColoumbRef
          this.copyMohrColoumb(constitutiveModelElementFrom, constitutiveModelElementTo);
          break;
        case '2': //undrainedRef
          this.copyUndrained(constitutiveModelElementFrom, constitutiveModelElementTo);
          break;
        case '3': //noStrength
          break;
        case '4': //infiniteStrengthRef
          this.copyInfiniteStrength(constitutiveModelElementFrom, constitutiveModelElementTo);
          break;
        case '5': //shearNormalFunctionRef
          this.copyShearNormalFunction(constitutiveModelElementFrom, constitutiveModelElementTo);
          break;
        case '6': //hoekBrownRef
          this.copyhoekBrown(constitutiveModelElementFrom, constitutiveModelElementTo);
          break;
        case '7': //generalizedHoekBrownRef
          this.copyGeneralizedHoekBrown(constitutiveModelElementFrom, constitutiveModelElementTo);
          break;
        case '8': //verticalStressRatioRef
          this.copyVerticalStressRatio(constitutiveModelElementFrom, constitutiveModelElementTo);
          break;
        case '9': //shansepRef
          this.copyShansep(constitutiveModelElementFrom, constitutiveModelElementTo);
          break;
      }
    }
  }

  /**
   * Copia os valores do formulário Mohr-Coulomb de um elemento para outro,
   * incluindo a validação do checkbox `tensile_strength`.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyMohrColoumb(fromElement, toElement) {
    toElement.formMohrColoumb.patchValue(fromElement.formMohrColoumb.getRawValue());
    toElement.formService.checkboxControlValidate(toElement.formMohrColoumb, 'tensile_strength');
  }

  /**
   * Copia os valores do formulário Undrained e campos auxiliares,
   * incluindo checkboxes e dropdowns, entre dois elementos.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyUndrained(fromElement, toElement) {
    toElement.formUndrained.patchValue(fromElement.formUndrained.getRawValue());

    toElement.selectedTypeCohesion = fromElement.selectedTypeCohesion;
    toElement.fields = fromElement.fields;
    toElement.dropdownSettings = fromElement.dropdownSettings;

    toElement.formService.checkboxControlValidate(toElement.formUndrained, 'tensile_strength');
    toElement.formService.checkboxControlValidate(toElement.formUndrained, 'f_tensile_strength');
    toElement.formService.checkboxControlValidate(toElement.formUndrained, 'maximum');
    toElement.formService.checkboxControlValidate(toElement.formUndrained, 'datum_tensile_strength');
    toElement.formService.checkboxControlValidate(toElement.formUndrained, 'datum_maximum');
    toElement.formService.checkboxControlValidate(toElement.formUndrained, 'slope_tensile_strength');
    toElement.formService.checkboxControlValidate(toElement.formUndrained, 'maximum_slope');
  }

  /**
   * Copia os valores do formulário Infinite Strength entre dois elementos.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyInfiniteStrength(fromElement, toElement) {
    toElement.formInfiniteStrength.patchValue(fromElement.formInfiniteStrength.getRawValue());
  }

  /**
   * Copia dados de gráfico e pontos da função Tensão x Normal de um elemento para outro.
   * A cópia do gráfico de pontos é feita de forma assíncrona.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyShearNormalFunction(fromElement, toElement) {
    toElement.data = fromElement.data;
    toElement.sendDataChart = fromElement.sendDataChart;

    toElement.paramsChart = fromElement.paramsChart;
    toElement.dataPoints = fromElement.dataPoints;

    setTimeout(() => {
      this.copyMaterialPoints(fromElement, toElement);
    }, 100);
  }

  /**
   * Copia os valores do formulário Hoek-Brown entre dois elementos.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyhoekBrown(fromElement, toElement) {
    toElement.formHoekBrown.patchValue(fromElement.formHoekBrown.getRawValue());
  }

  /**
   * Copia os valores do formulário Generalized Hoek-Brown,
   * incluindo definição de resistência e configurações de dropdowns.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyGeneralizedHoekBrown(fromElement, toElement) {
    toElement.formGeneralizedHoekBrown.patchValue(fromElement.formGeneralizedHoekBrown.getRawValue());

    toElement.seletectedStrengthDefinition = fromElement.seletectedStrengthDefinition;
    toElement.dropdownSettings = fromElement.dropdownSettings;
  }

  /**
   * Copia os valores do formulário Vertical Stress Ratio e executa validações de checkbox.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyVerticalStressRatio(fromElement, toElement) {
    toElement.formVerticalStressRatio.patchValue(fromElement.formVerticalStressRatio.getRawValue());

    toElement.formService.checkboxControlValidate(toElement.formVerticalStressRatio, 'maximum_shear_strength');
    toElement.formService.checkboxControlValidate(toElement.formVerticalStressRatio, 'tensile_strength');
  }

  /**
   * Copia os valores do formulário SHANSEP, dados de gráfico e pontos materiais,
   * incluindo validação de campos como `tensile_strength` e `maximum_shear_strength`.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyShansep(fromElement, toElement) {
    toElement.formShansep.patchValue(fromElement.formShansep.getRawValue());

    toElement.stressHistoryMethodData = fromElement.stressHistoryMethodData;
    toElement.label_x = fromElement.label_x;
    toElement.label_y = fromElement.label_y;
    toElement.dataPoints = fromElement.dataPoints;
    toElement.dropdownSettings = fromElement.dropdownSettings;

    toElement.formService.checkboxControlValidate(toElement.formShansep, 'maximum_shear_strength');
    toElement.formService.checkboxControlValidate(toElement.formShansep, 'tensile_strength');

    setTimeout(() => {
      this.copyMaterialPoints(fromElement, toElement);
    }, 100);
  }

  /**
   * Copia o estado interno do componente de pontos materiais (`MaterialPointsComponent`)
   * de um elemento para outro, incluindo dados, inputs, visualização e gráficos.
   *
   * @param fromElement Origem.
   * @param toElement Destino.
   */
  copyMaterialPoints(fromElement, toElement) {
    const materialPointsTo = toElement.materialPointsRef.toArray()[0];
    const materialPointsFrom = fromElement.materialPointsRef.toArray()[0];

    materialPointsTo.pointsInput = materialPointsFrom.pointsInput;
    materialPointsTo.buttonChart = materialPointsFrom.buttonChart;
    materialPointsTo.data = materialPointsFrom.data;
    materialPointsTo.view = materialPointsFrom.view;
    materialPointsTo.sendDataChart = materialPointsFrom.sendDataChart;

    // Clona o formPoints como FormGroup
    const originalFormGroup = materialPointsFrom.formPoints;
    const originalPointsArray = originalFormGroup.get('points') as FormArray;

    // Limpa os IDs de cada ponto
    const cleanedPoints = originalPointsArray.controls.map((ctrl) => {
      const { id, ...rest } = ctrl.value;

      // Para cada campo (ex: point_x, point_y), criar um FormControl
      const pointGroup = new FormGroup({});

      for (const key in rest) {
        pointGroup.addControl(key, new FormControl(rest[key]));
      }

      return pointGroup;
    });
    const newPointsArray = new FormArray(cleanedPoints);

    // Cria novo FormGroup e adiciona o FormArray limpo
    const newFormGroup = new FormGroup({
      points: newPointsArray
    });

    materialPointsTo.formPoints = newFormGroup;
  }
}
