import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';
import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';

import { fieldsReading } from 'src/app/constants/readings.constants';
import { Datum } from 'src/app/constants/app.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-geo',
  templateUrl: './geo.component.html',
  styleUrls: ['./geo.component.scss']
})
export class GeoComponent implements OnInit, OnChanges {
  @Input() public instrumentsList: any = [];
  @Input() public index: number = null;
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public units: any = null;
  @Input() public typeInstrument: any = null;
  @Input() public datetime: any = null;
  @Input() public structure: any = null;

  @Output() public setInstrument = new EventEmitter();
  @Output() public showMap = new EventEmitter();

  public formReading: FormGroup = new FormGroup({
    instrument: new FormControl('', [Validators.required]),
    date: new FormControl({ value: '', disabled: true }, [Validators.required]),
    nature: new FormControl({ value: '', disabled: true }, [Validators.required]),
    a_axis_pga: new FormControl({ value: '', disabled: true }, [Validators.required]),
    b_axis_pga: new FormControl({ value: '', disabled: true }, [Validators.required]),
    z_axis_pga: new FormControl({ value: '', disabled: true }, [Validators.required]),
    east_coordinate: new FormControl({ value: '', disabled: true }, [Validators.required]),
    north_coordinate: new FormControl({ value: '', disabled: true }, [Validators.required]),
    //Para edicao
    id: new FormControl({ value: '', disabled: true })
  });

  public natures: any = [];

  public controls: any = null;

  public fieldsReading = fieldsReading;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;
  public messageNature: string = '';

  public datum: any = null;

  public func = fn;

  constructor(private formService: FormService, private structuresServiceApi: StructuresServiceApi) {}

  ngOnInit(): void {
    this.controls = this.formReading.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReading.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }

    if (changes.datetime && changes.datetime.currentValue != null) {
      this.controls['date'].setValue(this.datetime);
    }

    if (changes.structure && (changes.structure.previousValue == undefined || changes.structure.currentValue.id != changes.structure.previousValue.id)) {
      if (changes.structure.currentValue !== null) {
        this.getNatures(changes.structure.currentValue.id);
        if (this.structure != null) {
          this.datum = fn.findIndexInArrayofObject(Datum, 'id', changes.structure.currentValue.coordinate_setting.datum, 'id', true);
        }
      }
    }
  }

  changeInstrument(instrument) {
    this.setInstrument.emit(instrument);
  }

  splitData($dados) {
    if (!this.edit && !this.view) {
      this.controls['instrument'].enable();
    } else {
      this.controls['instrument'].disable();
    }

    this.formService.toggleFormList(this.formReading, this.fieldsReading[this.typeInstrument.id]);
    this.controls['instrument'].setValue($dados.instrument.id);

    if ($dados.edit) {
      this.controls['id'].setValue($dados.edit.id);
      let date = $dados.edit.date.split('.');
      this.controls['date'].setValue(date[0]);
      this.controls['nature'].setValue($dados.edit.nature.id);
      this.controls['a_axis_pga'].setValue($dados.edit.a_axis_pga);
      this.controls['b_axis_pga'].setValue($dados.edit.b_axis_pga);
      this.controls['z_axis_pga'].setValue($dados.edit.z_axis_pga);
      this.controls['east_coordinate'].setValue($dados.edit.east_coordinate);
      this.controls['north_coordinate'].setValue($dados.edit.north_coordinate);
    }

    if (this.view) {
      this.formReading.disable();
    }
  }

  getNatures(structureId) {
    if (structureId != '') {
      this.structuresServiceApi.getStructureNatures(structureId).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.natures = dados;
      });
    } else {
      this.natures = [];
    }
  }

  addNature(description) {
    let params = {
      structure: {
        id: this.structure.id
      },
      description: description
    };

    this.structuresServiceApi.postStructureNatures(this.structure.id, params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.controls['nature'].setValue(dados);
        this.getNatures(this.structure.id);
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        setTimeout(() => {
          this.messagesError = [];
        }, 4000);
      }
    );
  }

  editNature(params) {
    this.messageNature = '';

    this.structuresServiceApi.putStructureNatures(this.structure.id, params.id, params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.controls['nature'].setValue(dados);
        this.getNatures(this.structure.id);
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          this.messageNature = error.error[0].message;
          // error.error.forEach((msgError) => {
          //   this.messagesError.push(msgError);
          // });
        }
        setTimeout(() => {
          this.messageNature = '';
          // this.messagesError = [];
        }, 4000);
      }
    );
  }

  callMap() {
    this.showMap.emit(this.formReading);
  }
}
