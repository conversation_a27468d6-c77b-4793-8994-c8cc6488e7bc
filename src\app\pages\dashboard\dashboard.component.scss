/* Configuração inicial para 2 linhas e 2 colunas */
.container-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 2 colunas de tamanho igual */
  grid-template-rows: repeat(2, 1fr); /* 2 linhas de tamanho igual */
  height: 100vh; /* O grid vai ocupar 100% da altura da janela */
  gap: 10px; /* Espaçamento entre os itens */
  box-sizing: border-box;
}

/* Estilos dos itens do grid */
.grid-item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.29);
  height: 100%;
  box-sizing: border-box;
}

/* Faz o grid-content ocupar o restante do espaço disponível */
.grid-content {
  flex-grow: 1; /* Permite que o grid-content cresça */
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.grid-header {
  height: 30px; /* Altura fixa do header */
  padding: 0 4px;
  border-bottom: rgba(0, 0, 0, 0.29) 1px solid;
  font-size: 0.875em;
  color: #34b575;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0; /* Garante que o header não encolherá */
}

.grid-item:first-child {
  height: 100%; /* Garante que o mapa ocupe toda a altura disponível na célula do grid */
}

/* Quando a tela for menor que 1000px, ajuste para 4 linhas e 1 coluna */
@media (max-width: 1000px) {
  .container-grid {
    grid-template-columns: 1fr; /* Apenas 1 coluna */
    grid-template-rows: repeat(4, 1fr); /* 4 linhas de tamanho igual */
    height: auto;
  }
}

.list-section {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-check-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.8em;
}

.form-control {
  border-color: #d4d2d2;
  font-size: 0.875em;
}

.form-select {
  font-size: 0.875em !important;
  line-height: 1.52857143 !important;
}

.message-no-content {
  font-size: 2em;
  color: orange;
  font-weight: 700;
  text-align: center;
}

.list-instruments,
.legend {
  font-size: 0.75em;
}

.dropdown-menu {
  margin: 0 auto;
  right: 0;
}
