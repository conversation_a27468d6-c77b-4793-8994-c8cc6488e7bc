import { Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

//Services
import { UserService } from 'src/app/services/user.service';
import { UsersService as UsersServiceApi } from 'src/app/services/api/users.service';

@Component({
  selector: 'app-modal-termo',
  templateUrl: './modal-termo.component.html',
  styleUrls: ['./modal-termo.component.scss']
})
export class ModalTermoComponent implements OnInit {
  @Input() public user: any = {
    id: '',
    name: '',
    family_name: '',
    alias: '',
    email: '',
    username: '',
    role: '',
    locale: ''
  };

  public termo: string = '';

  @ViewChild('modalTermo') modalTermo: ElementRef;

  constructor(
    private modalService: NgbModal,
    private userService: UserService,
    private usersServiceApi: UsersServiceApi
  ) {}

  ngOnInit(): void {
    this.getTermo();
  }

  public getTermo() {
    this.usersServiceApi.getTerms().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.termo = dados.html_text;
    });
  }

  public openModal() {
    this.modalService.open(this.modalTermo, { size: 'xl' });
  }
}
