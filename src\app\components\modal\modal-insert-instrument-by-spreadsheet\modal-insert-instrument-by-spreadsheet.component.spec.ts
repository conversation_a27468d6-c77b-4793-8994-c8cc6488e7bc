import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalInsertInstrumentBySpreadsheetComponent } from './modal-insert-instrument-by-spreadsheet.component';

describe('ModalInsertInstrumentBySpreadsheetComponent', () => {
  let component: ModalInsertInstrumentBySpreadsheetComponent;
  let fixture: ComponentFixture<ModalInsertInstrumentBySpreadsheetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalInsertInstrumentBySpreadsheetComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalInsertInstrumentBySpreadsheetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
