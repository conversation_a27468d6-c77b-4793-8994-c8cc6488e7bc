import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-instructions',
  templateUrl: './modal-instructions.component.html',
  styleUrls: ['./modal-instructions.component.scss']
})
export class ModalInstructionsComponent implements OnInit {
  @ViewChild('modalInstructions') modalInstructions: ElementRef;

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  openModal() {
    this.modalService.open(this.modalInstructions);
  }

  downloadFile() {}
}
