import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { IDropdownSettings } from 'ng-multiselect-dropdown';

import { SortableListComponent } from '@components/sortable-list/sortable-list.component';

import { MessageCadastro } from 'src/app/constants/message.constants';
import { conditions, activities as activitiesEnum } from 'src/app/constants/structure.constants';
import { accessLevel as accessLevelPermission } from 'src/app/constants/permissions.constants';

import { StructureTypeService } from 'src/app/services/api/structureType.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-stability-tab',
  templateUrl: './stability-tab.component.html',
  styleUrls: ['./stability-tab.component.scss']
})
export class StabilityTabComponent implements OnInit {
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  @ViewChild(SortableListComponent) sortableList: SortableListComponent;

  public formStructureStability: FormGroup = new FormGroup({
    should_evaluate: new FormControl('', [Validators.required]),
    structure_type: new FormControl('', [Validators.required]),
    gravity: new FormControl('', [Validators.required]),
    seismic_coefficient_horizontal: new FormControl('', [Validators.required]),
    seismic_coefficient_vertical: new FormControl('', [Validators.required])
  });

  public formStructureType: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    active: new FormControl(true),
    name: new FormControl('', [Validators.required]),
    description: new FormControl(''),
    activities: new FormControl('', [Validators.min(1)])
  });

  public structureStability: any = {
    should_evaluate_drained_condition: null,
    should_evaluate_undrained_condition: null,
    should_evaluate_pseudo_static_condition: null,
    gravity: null,
    seismic_coefficient_horizontal: null,
    seismic_coefficient_vertical: null
  };

  public conditions = conditions;

  public activitiesList: any = [];

  public ctrlTypeStructure: boolean = false;

  public structureTypeList: any = [];

  public structureType: any = {
    id: null,
    name: null,
    description: null,
    activities: []
  };

  public structureTypeRequest: any = {};

  public editSctructureType: boolean = false;

  public message: any = [{ text: '', status: false }];
  public messagesErrorTypeStructure: any = null;

  public dropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Selecionar todas',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  public activitiesCollapse: boolean = false;

  public activitiesSelected: any = [];
  public activitiesSorted: any = [];
  public activitiesNumberSelected: number = 0;

  public conditionsDisabled = false;

  public func = fn;

  constructor(private structureTypeService: StructureTypeService) {}

  /**
   * Lifecycle hook executado na criação do componente.
   * Carrega lista de tipos de estrutura, configura atividades e valida permissões de acesso.
   */
  ngOnInit(): void {
    this.getStructureTypeList();
    this.setStructureTypeActivity();
    this.validateAccess();
  }

  /**
   * Obtém a lista de tipos de estrutura via serviço e armazena em `structureTypeList`.
   */
  getStructureTypeList() {
    this.structureTypeService.getStructureTypesList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.structureTypeList = dados;
    });
  }

  /**
   * Cadastra um novo tipo de estrutura com as atividades definidas.
   * Em caso de sucesso, limpa o formulário e atualiza a lista.
   * Exibe mensagens de erro em caso de validação falha.
   */
  registerStructureType() {
    this.structureTypeService.postStructureTypes(this.structureTypeRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.SucessoCadastro;
        this.message.status = true;
        this.formStructureType.reset();
        this.setStructureTypeActivity();
        this.structureTypeRequest.activities = [];

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.getStructureTypeList();
        this.ctrlTypeStructure = false;
        this.formStructureStability.get('structure_type').setValue(dados);
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorTypeStructure = error.error;
          setTimeout(() => {
            this.messagesErrorTypeStructure = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Busca um tipo de estrutura pelo ID e preenche o formulário com seus dados.
   * Define atividades selecionadas e ativa o modo de edição.
   *
   * @param structureTypeId - ID do tipo de estrutura.
   */
  getStructureType(structureTypeId: string) {
    this.structureTypeService.getStructureTypesById(structureTypeId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.editSctructureType = true;

      this.formStructureType.get('id').setValue(dados.id);
      this.formStructureType.get('name').setValue(dados.name);
      this.formStructureType.get('description').setValue(dados.description);
      this.formStructureType.get('active').setValue(dados.active);
      this.formStructureType.get('activities').setValue(dados.activities.length);

      this.activitiesSelected = dados.activities;
      this.activitiesNumberSelected = dados.activities.length;

      this.ctrlTypeStructure = true;
      this.activitiesCollapse = true;

      this.setStructureTypeActivity();
    });
  }

  /**
   * Atualiza um tipo de estrutura existente com os dados do formulário.
   * Em caso de sucesso, limpa o formulário e atualiza a lista.
   */
  editStructureType() {
    this.structureTypeService.putStructureTypes(this.structureTypeRequest.id, this.structureTypeRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.formStructureType.reset();
        this.setStructureTypeActivity();
        this.structureTypeRequest.activities = [];

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.getStructureTypeList();
        this.ctrlTypeStructure = false;
        this.formStructureStability.get('structure_type').setValue(dados);
      },
      (error) => {
        if (error.status === 400) {
          this.messagesErrorTypeStructure = error.error;
          setTimeout(() => {
            this.messagesErrorTypeStructure = null;
          }, 4000);
        }
      }
    );
  }

  /**
   * Executa validações e prepara os dados conforme o tipo informado.
   * No caso de tipo `structureType`, prepara os dados e envia para cadastro ou edição.
   *
   * @param type - Tipo da entidade a ser validada: 'structureType' ou 'structure'.
   */
  validate(type: string = '') {
    if (type === 'structureType') {
      this.activitiesSorted = fn.filterByKeyAndValue(this.sortableList.dataList, 'selected', true);
    }

    this.formatData(type);
  }

  /**
   * Formata os dados do formulário conforme o tipo.
   * Para `structureType`, define as atividades ordenadas e envia para cadastro/edição.
   *
   * @param type - Tipo da entidade: 'structureType' ou 'structure'.
   */
  formatData(type: string = '') {
    if (type === 'structureType') {
      this.structureTypeRequest = this.structureType;

      this.structureTypeRequest.id = this.formStructureType.get('id').value;

      this.structureTypeRequest.active = this.formStructureType.get('active').value;

      this.structureTypeRequest.name = this.formStructureType.get('name').value;

      this.structureTypeRequest.description = this.formStructureType.get('description').value;

      // Separando os itens que foram selecionados e definindo o index pela ordem
      this.activitiesSorted.forEach((activitiesSortedItem, index) => {
        this.structureTypeRequest.activities.push({
          activity: parseInt(activitiesSortedItem.activity),
          index: index
        });
      });

      if (!this.editSctructureType) {
        delete this.structureTypeRequest.id;
        delete this.structureTypeRequest.active;
        this.registerStructureType();
      } else {
        this.editStructureType();
      }
    } else if (type === 'structure') {
    }
  }

  /**
   * Monta a lista de atividades, marcando as selecionadas e definindo índices.
   * Atualiza `activitiesList` com base em `activitiesSelected`.
   */
  setStructureTypeActivity() {
    this.activitiesList = fn.enumToArrayComplete(activitiesEnum, false);
    let activietesNotSelected = fn.differenceByIndex(this.activitiesList, this.activitiesSelected, 'activity');

    let idx = this.activitiesSelected.length;
    activietesNotSelected = activietesNotSelected.map((item: any) => {
      item.index = idx;
      item.selected = false;
      idx++;
      return item;
    });

    this.activitiesSelected = this.activitiesSelected.map((item: any) => {
      item.name = this.activitiesList[item.activity - 1].name;
      item.selected = true;
      return item;
    });
    this.activitiesList = this.activitiesSelected.concat(activietesNotSelected);
  }

  /**
   * Define a quantidade de atividades selecionadas manualmente.
   *
   * @param quantidade - Valor numérico a ser setado.
   * @param element - FormControl a ser atualizado.
   */
  getNumberSelected(quantidade: number, element: any) {
    element.setValue(quantidade);
  }

  /**
   * Reseta o formulário de tipo de estrutura e reconfigura as atividades.
   *
   * @param type - Tipo de formulário. Apenas 'structureType' neste caso.
   * @param option - Se `true`, ativa controles adicionais ao resetar.
   */
  resetForm(type: string = '', option: boolean = false) {
    if (type === 'structureType') {
      this.activitiesCollapse = false;
      if (option) {
        this.ctrlTypeStructure = true;
        this.formStructureType.get('activities').setValue('');
      }

      this.editSctructureType = false;
      this.activitiesSelected = [];
      this.setStructureTypeActivity();
      this.formStructureType.reset();
      this.formStructureType.get('activities').setValue('');
    }
  }

  /**
   * Valida permissões de acesso ao formulário com base no perfil do usuário.
   * Se em modo visualização, desabilita campos. Caso contrário, libera campos específicos para edição.
   *
   * @param role - (Opcional) Nível de permissão. Padrão: 0.
   */
  validateAccess(role: number = 0): any {
    if (this.profile.description !== accessLevelPermission.SuperSuporte && this.profile.description !== accessLevelPermission.Suporte) {
      this.view = true;
    }

    if (this.view) {
      this.formStructureStability.disable();
      this.formStructureType.disable();
      this.conditionsDisabled = true;
    }

    if (this.profile.description !== accessLevelPermission.SuperSuporte && this.profile.description !== accessLevelPermission.Suporte && this.edit) {
      this.formStructureStability.get('should_evaluate').enable();
      this.formStructureStability.get('gravity').enable();
      this.formStructureStability.get('seismic_coefficient_horizontal').enable();
      this.formStructureStability.get('seismic_coefficient_vertical').enable();
      this.conditionsDisabled = false;
    }
  }

  /**
   * Popula o formulário de estabilidade estrutural com os dados recebidos.
   * Caso o campo `structure_type` esteja presente, busca os detalhes e exibe atividades.
   *
   * @param dataTab - Objeto contendo os dados a serem inseridos no formulário.
   */
  setData(dataTab: any = []) {
    for (var index in dataTab) {
      this.formStructureStability.get(index).setValue(dataTab[index]);
      switch (index) {
        case 'structure_type':
          this.ctrlTypeStructure = true;
          this.getStructureType(this.formStructureStability.get('structure_type').value);
          this.activitiesCollapse = true;
          break;
      }
    }
    this.activitiesCollapse = true;
  }

  /**
   * Extrai os dados do formulário de estabilidade estrutural.
   * Converte o campo `should_evaluate` em um objeto com valores `true`.
   *
   * @returns Objeto contendo os dados formatados.
   */
  getData() {
    let dataTab = {};
    for (let index in this.formStructureStability.controls) {
      switch (index) {
        case 'should_evaluate':
          const should_evaluate = this.formStructureStability.get(index).value;
          should_evaluate.map((item: any) => {
            dataTab[item.id] = true;
          });
          break;
        default:
          dataTab[index] = this.formStructureStability.get(index).value;
          break;
      }
    }
    return dataTab;
  }
}
