import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup } from '@angular/forms';

import { MessageCadastro, MessagePadroes, ModalConfirm } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';

import { FilterService } from 'src/app/services/filter.service';
import { UserService } from 'src/app/services/user.service';
import { NgxSpinnerService } from 'ngx-spinner';

import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { SimulatorService as SimulatorServiceApi } from 'src/app/services/api/simulator.service';

import * as moment from 'moment';

@Component({
  selector: 'app-list-simulations',
  templateUrl: './list-simulations.component.html',
  styleUrls: ['./list-simulations.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListSimulationsComponent implements OnInit {
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalConfirm') ModalConfirm: any;
  @ViewChild('modalShareSimulation') ModalShareSimulation: any;
  @ViewChild('modalFixedSimulation') ModalFixedSimulation: any;

  public formSimulations: FormGroup = new FormGroup({
    SearchIdentifier: new FormControl(''),
    SectionId: new FormControl([]),
    Name: new FormControl([])
  });

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public sections: any = [];
  public sectionSettings = MultiSelectDefault.Sections;

  public ctrlBtnFilter: boolean = false;
  public controls: any = [];

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };
  public messagesError: any = null;

  public filterParams: any = {};
  public filterBodyParams: any = {
    section_ids: []
  };

  public filter: any = {
    SearchIdentifier: '',
    ClientId: '',
    ClientUnitId: '',
    StructureId: '',
    Name: ''
  };

  public filterSearch: any = {};
  public filterSearchBody: any = {};

  public modalData: any = {};
  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  public simulationItem: any = null;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'ID',
      width: '50px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Simulação',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'Estrutura',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['structure_name']
    },
    {
      label: 'Seção',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['section_name']
    },
    {
      label: 'Criado por',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_by']
    },
    {
      label: 'Data',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Status',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['status']
    },
    {
      label: 'Simulação temporária?',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['should_keep']
    },
    {
      label: 'Ações',
      width: '50px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['miniDashboard']
    }
  ];

  constructor(
    private filterService: FilterService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private sectionsServiceApi: SectionsServiceApi,
    private simulatorServiceApi: SimulatorServiceApi,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.controls = this.formSimulations.controls;
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
    }, 1000);
  }

  /**
   * Obtém as seções com base na estrutura selecionada e na ação, atualizando as seções e os seletores de cor.
   * @param {any} structure - A estrutura selecionada.
   * @param {string} action - A ação a ser realizada (seleção ou deseleção).
   */
  getSections(structure, action: string = 'select') {
    if (action === 'select') {
      this.sectionsServiceApi.getSectionList({ structureId: structure.id }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.sections = dados;
      });
    } else {
      this.sections = [];
    }
  }

  //Lista de simulações - Recupera a lista de simulações com base nos parâmetros fornecidos
  getSimulationsList(params, bodyParams) {
    this.ngxSpinnerService.show();

    params['Page'] = this.page;
    params['PageSize'] = this.pageSize;

    this.simulatorServiceApi.postSimulationsSearch(bodyParams, params).subscribe(
      (resp) => {
        let dados: any = resp;

        if (dados != null && dados != undefined) {
          dados = dados.body === undefined ? dados : dados.body;

          if (dados) {
            this.tableData = dados ? dados.data : [];
            this.collectionSize = dados.total_items_count;
            this.formatData();
          } else {
            this.tableData = [];
            this.collectionSize = 0;
            this.message.text = MessagePadroes.NoRegister;
            this.message.status = true;
            this.message.class = 'alert-warning';
          }
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.message.text = MessagePadroes.NoRegister;
          this.message.status = true;
          this.message.class = 'alert-warning';
        }

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
      }
    );
  }

  //Pesquisa simulações com base nos filtros aplicados
  searchSimulations(filterParams?: any) {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterParams = {
      SearchIdentifier: this.controls['SearchIdentifier'].value,
      Identifier: this.filter.Identifier,
      Name: this.filter.Name != '' ? this.filter.Name : '',
      ClientId: filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '',
      ClientUnitId: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '',
      StructureId: filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].id : ''
    };

    this.filterBodyParams.section_ids = this.controls['SectionId'].value.map((item) => item.id);

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterSearchBody = {
      ...this.filterBodyParams
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.getSimulationsList(this.filterSearch, this.filterSearchBody);
  }

  /**
   * Gerencia os filtros aplicados. Se $btn for verdadeiro, executa uma nova busca; caso contrário,
   * recupera os filtros aplicados e refina a busca com base neles.
   * @param {boolean} [$btn=false] - Indica se a função foi chamada a partir de um botão de busca.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchSimulations(); // Executa a busca diretamente ao clicar no botão
    } else {
      // Recupera os filtros salvos
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchSimulations(); // Realiza uma nova busca caso não haja filtros salvos
      } else {
        // Aplica os filtros existentes
        this.filterSearch = data.filters;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;

        // Atualiza os valores do formulário com os filtros recuperados
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier;
        this.filter.ClientId = this.filterSearch.ClientId;
        this.filter.ClientUnitId = this.filterSearch.ClientUnitId;
        this.filter.StructureId = this.filterSearch.StructureId;
        this.filter.Name = this.filterSearch.Name;

        // Aplica os filtros de hierarquia, se disponíveis
        this.filterParams = {
          SearchIdentifier: this.filter.SearchIdentifier,
          Name: this.filter.Name,
          ClientId: this.filter.ClientId || '',
          ClientUnitId: this.filter.ClientUnitId || '',
          StructureId: this.filter.StructureId || ''
        };

        // Executa a busca com os filtros aplicados
        this.searchSimulations(this.filterSearch);
      }

      // Configura os filtros de hierarquia, se disponíveis
      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
        this.hierarchy.setUnits(data.filtersHierarchy.units);
        this.hierarchy.setStructures(data.filtersHierarchy.structures);
      }
    }
  }

  /**
   * Formata os dados da tabela de análises de estabilidade para exibição na interface.
   * Este método processa cada análise recebida, ajustando as informações
   * conforme necessário para exibição. Especificamente:
   * - Traduz o valor numérico do status para um valor descritivo em português.
   * - Formata a data de criação da análise para o fuso horário de São Paulo.
   * - Define o campo de detalhes como nulo inicialmente.
   *
   * @returns void
   */
  formatData() {
    const simulationStatusMap = {
      1: 'Processando',
      2: 'Falhou',
      3: 'Concluído'
    };

    this.tableData = this.tableData.map((item: any) => {
      item.configMiniDashboard = {
        share: item.should_keep ? true : false,
        fixed: item.should_keep ? false : true
      };

      item.section_name = item.sections[0].section_name;
      item.structure_name = item.sections[0].structure.name;
      item.created_by = item.created_by.first_name + ' ' + item.created_by.surname;
      item.created_date = moment(item.created_date).tz('America/Sao_Paulo').subtract(3, 'hours').format('DD/MM/YYYY HH:mm:ss');
      item['should_keep_original'] = item.should_keep;
      item.should_keep = item.should_keep ? 'Não' : 'Sim';
      item.status = simulationStatusMap[item.status] || 'Desconhecido';

      return item;
    });
  }

  //Reseta os filtros aplicados no formulário e na hierarquia.
  resetFilter() {
    this.hierarchy.resetFilters();

    this.controls['SectionId'].setValue([]);
    this.controls['SearchIdentifier'].setValue('');
    this.controls['Name'].setValue('');
    this.sections = [];

    this.searchSimulations();
  }

  /**
   * Processa eventos da hierarquia de filtros, atualizando as seções ou cores com base no evento.
   * @param {any} $event - O evento da hierarquia de filtros.
   */
  getEventHierarchy($event) {
    switch ($event.type) {
      case 'units':
        this.sections = [];
        break;
      case 'structures':
        this.sections = [];

        if ($event.element != null) {
          this.getSections($event.element, $event.action);
        }
        this.ctrlBtnFilter = $event.action === 'select' ? true : false;
        break;
    }
    if ($event.action === 'deselect') {
      this.ctrlBtnFilter = false;
    }
  }

  /**
   * Gerencia o evento de clique em uma linha da tabela, executando a ação correspondente.
   * @param {any} [$event=null] - O evento de clique.
   */
  clickRowEvent($event: any = null) {
    let params = null;
    switch ($event.action) {
      case 'edit':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/editSimulation']);
        break;
      case 'delete':
        this.modalTitle = 'Excluir simulação';
        this.modalMessage = ModalConfirm.ExcluirSimulação;
        this.modalConfig.iconHeader = null;
        this.modalConfig.action = 'confirmDelete';
        this.modalData = { id: $event.id };
        this.ModalConfirm.openModal();
        break;
      case 'confirmDelete':
        this.deleteSimulations($event.data.id);
        break;
      case 'result':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/resultSimulation']);
        break;
      case 'share':
        this.simulationItem = this.tableData.find((item) => item.id === $event.id);
        this.ModalShareSimulation.openModal();
        break;
      case 'shareUser':
        const userIds = $event.form['UserId'].value.map((item) => item.id);
        this.shareSimulation(this.simulationItem['id'], userIds);
        break;
      case 'view':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/view']);
        break;
      case 'fixed':
        this.simulationItem = this.tableData.find((item) => item.id === $event.id);
        this.simulationItem['option'] = 'fixedSimulation';
        this.modalTitle = 'Fixar simulação';
        this.ModalFixedSimulation.openModal();
        break;
      case 'fixedSimulation':
        params = {
          id: $event.item.id,
          name: $event.form['Name'].value,
          should_keep: true
        };
        this.putSimulations(params, 'fixed');
        break;
      case 'rename':
        this.simulationItem = this.tableData.find((item) => item.id === $event.id);
        this.simulationItem['option'] = 'renameSimulation';
        this.modalTitle = 'Renomear simulação';
        this.ModalFixedSimulation.openModal();
        break;
      case 'renameSimulation':
        params = {
          id: $event.item.id,
          name: $event.form['Name'].value,
          should_keep: $event.item.should_keep_original
        };
        this.putSimulations(params, 'rename');
        break;

      default:
        break;
    }
  }

  /**
   * Compartilha a simulação com os usuários especificados.
   * @param {string} simulationId - O ID da simulação a ser compartilhada.
   * @param {string[]} userIds - Os IDs dos usuários com quem a simulação será compartilhada.
   */
  shareSimulation(simulationId, userIds) {
    this.messagesError = [];

    this.simulatorServiceApi.postShareSimulations(simulationId, userIds).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.ShareSimulation;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
        }
      }
    );
  }

  //Exclui a simulação com base no ID fornecido.
  deleteSimulations(simulationId: string) {
    this.ngxSpinnerService.show();

    this.messagesError = [];

    this.simulatorServiceApi.deleteSimulationsById(simulationId).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.DeleteSimulation;
        this.message.status = true;
        this.message.class = 'alert-success';
        this.searchSimulations();

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
        }
      }
    );
  }

  putSimulations(params, option) {
    this.messagesError = [];

    this.simulatorServiceApi.putSimulationsById(params.id, params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = option === 'fixed' ? MessageCadastro.FixedSimulation : MessageCadastro.RenameSimulation;
        this.message.status = true;
        this.message.class = 'alert-success';

        this.searchSimulations();

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          if (error.error.hasOwnProperty('errors')) {
            const index = Object.keys(error.error.errors);
            this.messagesError.push({ message: error.error.errors[index[0]][0] });
          } else {
            error.error.forEach((msgError) => {
              this.messagesError.push(msgError);
            });
          }
        }
      }
    );
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.pageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.page = page;
      }
    }

    this.managerFilters();
  }
}
