import { Component, ElementRef, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';

import { ActivatedRoute } from '@angular/router';

import { UserService } from 'src/app/services/user.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { ImagesService as ImagesServiceApi } from 'src/app/services/api/image.service';

import { MessageCadastro } from 'src/app/constants/message.constants';
import { Observable, forkJoin } from 'rxjs';

@Component({
  selector: 'app-images-instrument',
  templateUrl: './images-instrument.component.html',
  styleUrls: ['./images-instrument.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ImagesInstrumentComponent implements OnInit {
  @ViewChild('appImages') appImages: ElementRef;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public instrumentName: any = {};

  public profile: any = null;
  public permissaoUsuario: any = null;

  public imagesItens: any = null;

  constructor(
    private activatedRoute: ActivatedRoute,
    private imagesServiceApi: ImagesServiceApi,
    private instrumentsServiceApi: InstrumentsServiceApi,
    public userService: UserService
  ) {}

  /**
   * Método executado na inicialização do componente.
   * Carrega o perfil do usuário, as permissões, e obtém os dados do instrumento e suas imagens associadas.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.getInstrumet(this.activatedRoute.snapshot.params.instrumentId);
    this.getImages(this.activatedRoute.snapshot.params.instrumentId);
  }

  /**
   * Obtém os detalhes de um instrumento específico pelo seu ID.
   * @param {string} instrumentId - O ID do instrumento a ser buscado.
   */
  getInstrumet(instrumentId: string) {
    this.instrumentsServiceApi.getInstrumentsById(instrumentId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.instrumentName = dados.identifier;
    });
  }

  /**
   * Obtém as imagens associadas a um instrumento específico pelo seu ID.
   * @param {string} instrumentId - O ID do instrumento para o qual as imagens serão buscadas.
   */
  getImages(instrumentId: string) {
    this.imagesItens = [];
    const params = {
      Entities: 1,
      'Filters.Instruments': instrumentId
    };

    this.imagesServiceApi.getImages(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.imagesItens = dados.images;
    });
  }

  /**
   * Registra novas imagens associadas a um instrumento e retorna um observable com o resultado da operação.
   * @returns {Observable<any>} - Um observable que emite o resultado do registro das imagens.
   */
  registerImage(): Observable<any> {
    return new Observable((observer) => {
      const formData = new FormData();
      let ctrlImage = false;

      formData.append('InstrumentId', this.activatedRoute.snapshot.params.instrumentId);

      let index = 1;
      this.appImages['images'].forEach((image) => {
        if (image.new) {
          ctrlImage = true;
          formData.append('Images', image.file);
          formData.append(`Description${index}`, image.description);
          index++;
        }
      });

      if (ctrlImage) {
        this.imagesServiceApi.postImage(formData, {}).subscribe(
          (resp) => {
            let dados: any = resp;
            dados = dados.body === undefined ? dados : dados.body;
            observer.next({ success: true, data: dados, type: 'addImageSuccess' });
            observer.complete();
          },
          (error) => {
            observer.next({ success: false, error: error, type: 'addImageError' });
            observer.complete();
          }
        );
      } else {
        observer.next({ success: true, data: 'No image', type: 'noImage' });
        observer.complete();
      }
    });
  }

  /**
   * Atualiza a descrição de uma imagem existente associada a um instrumento.
   * @param {any} image - O objeto da imagem a ser atualizada, contendo o ID e a nova descrição.
   * @returns {Observable<any>} - Um observable que emite o resultado da atualização da imagem.
   */
  updateImage(image): Observable<any> {
    return new Observable((observer) => {
      const params = {
        id: image.id,
        description: image.description
      };
      this.imagesServiceApi.patchImage(image.id, params).subscribe(
        (resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;
          observer.next({ success: true, data: dados, type: 'updImageSuccess' });
          observer.complete();
        },
        (error) => {
          observer.next({ success: false, error: error, type: 'updImageError' });
          observer.complete();
        }
      );
    });
  }

  /**
   * Manipula o resultado combinado das operações de registro e atualização de imagens.
   * @param {any[]} results - Um array contendo os resultados das operações de registro e atualização.
   */
  handleCombinedResult(results: any[]): void {
    results.forEach((result) => {
      if (result.success) {
        this.message.text = MessageCadastro.ImagemCadastrada;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
      } else {
        if (result.error.status !== 200) {
          this.messagesError = [];
          result.error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
      }
    });
    this.imagesItens = [];
    this.appImages['images'] = [];
    this.getImages(this.activatedRoute.snapshot.params.instrumentId);
  }

  //Envia as imagens para serem registradas ou atualizadas. Combina as operações em um único observable e lida com os resultados.
  sendImages(): void {
    const registerObservable: Observable<any> = this.registerImage();

    const updateObservablesArray: Observable<any>[] = this.appImages['images']
      .filter((imageData) => !imageData.new)
      .map((imageData) => this.updateImage(imageData));

    forkJoin(registerObservable, ...updateObservablesArray).subscribe(
      (results) => {
        this.handleCombinedResult(results);
      },
      (error) => {
        //console.error('Erro inesperado:', error);
      }
    );
  }
}
