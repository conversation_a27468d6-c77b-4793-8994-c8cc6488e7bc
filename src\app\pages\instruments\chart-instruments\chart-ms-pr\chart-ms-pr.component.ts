import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { Periods, YAxisDisplacement } from 'src/app/constants/chart.constants';
import { MessageInputInvalid } from 'src/app/constants/message.constants';

import { FormService } from 'src/app/services/form.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { ChartService as ChartServiceApi } from 'src/app/services/api/chart.service';

import { FileLoaderService } from 'src/app/services/file-loader.service';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';

@Component({
  selector: 'app-chart-ms-pr',
  templateUrl: './chart-ms-pr.component.html',
  styleUrls: ['./chart-ms-pr.component.scss']
})
export class ChartMsPrComponent implements OnInit {
  public formChart: FormGroup = new FormGroup({
    instrument: new FormControl([]),
    start_date: new FormControl('', [Validators.required]),
    end_date: new FormControl('', [Validators.required]),
    color_displacement: new FormControl('#17A2B8'),
    color_upper_limit: new FormControl('#7BC7DA'),
    color_lower_limit: new FormControl('#A1D7E5'),
    yaxis_displacement: new FormControl(1),
    chart_height: new FormControl(300)
  });

  public chart: any = {};
  public chartLegendsTop: number = 50;
  public chartLegendsBottom: number = 0;

  public controls: any = null;
  public periods = Periods;
  public yAxisDisplacement = YAxisDisplacement;

  public instruments: any = [];
  public instrumentsSettings = MultiSelectDefault.Instruments;

  public showColorPicker: any = {
    color_displacement: false,
    color_upper_limit: false,
    color_lower_limit: false
  };

  public selectedColor: any = {
    color_displacement: '#17A2B8',
    color_upper_limit: '#7BC7DA',
    color_lower_limit: '#A1D7E5'
  };

  public xAxis: any = [];
  public yAxis: any = [];

  public data: any = null;
  public yAxisDescription: any = '';

  public chartSeries: any = [];
  public chartLegends: any = [];

  public min: number = null;
  public max: number = null;

  public func = fn;

  public messageReturn: any = { text: '', status: false };

  constructor(
    private activatedRoute: ActivatedRoute,
    private chartServiceApi: ChartServiceApi,
    private fileLoaderService: FileLoaderService,
    private formService: FormService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private router: Router
  ) {}

  /**
   * Método de inicialização do componente.
   * Configura os controles e carrega a lista de instrumentos.
   */
  ngOnInit(): void {
    this.controls = this.formChart.controls;
    this.instrumentsSettings.singleSelection = true;

    this.getInstruments();
    this.formService.monitorAllFormControls(this.formChart, this.onFormControlChange.bind(this));
  }

  ngOnDestroy() {
    // Cancela as assinaturas ao destruir o componente
    this.formService.formDestroySubscribe();
  }

  /**
   * Método chamado quando um controle é alterado.
   * @param {string} controlName - Nome (ou caminho) do controle alterado.
   * @param {any} value - Novo valor do controle.
   */
  onFormControlChange(controlName: string, value: any): void {
    if (controlName === 'instrument' && value.length > 0 && this.formChart.get('start_date').valid && this.formChart.get('end_date').valid) {
      this.getChart();
    }

    if (
      controlName === 'start_date' &&
      this.formChart.get('start_date').valid &&
      this.formChart.get('end_date').valid &&
      this.formChart.get('instrument')?.value &&
      this.formChart.get('instrument')?.value.length > 0
    ) {
      this.getChart();
    }

    if (
      controlName === 'end_date' &&
      this.formChart.get('end_date').valid &&
      this.formChart.get('start_date').valid &&
      this.formChart.get('instrument')?.value &&
      this.formChart.get('instrument')?.value.length > 0
    ) {
      this.getChart();
    }

    if (
      controlName === 'yaxis_displacement' &&
      this.formChart.get('end_date').valid &&
      this.formChart.get('start_date').valid &&
      this.formChart.get('instrument')?.value &&
      this.formChart.get('instrument')?.value.length > 0
    ) {
      this.getChart();
    }
  }

  /**
   * Obtém a lista de instrumentos com base na estrutura e tipo de instrumento especificados.
   * Define o instrumento selecionado nos controles do formulário.
   */
  getInstruments() {
    let params = { StructureId: this.activatedRoute.snapshot.queryParams.structure, Type: this.activatedRoute.snapshot.queryParams.typeInstrument };
    this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
      let dados: any = resp;

      if (dados.status == 200) {
        dados = dados.body === undefined ? dados : dados.body;
        this.instruments = dados;
        let instrumentInfo = fn.findIndexInArrayofObject(this.instruments, 'id', this.activatedRoute.snapshot.params.instrumentId, 'identifier', true);
        this.controls['instrument'].setValue([instrumentInfo]);
      }
    });
  }

  /**
   * Obtém os dados do gráfico de percolação com base nos instrumentos selecionados.
   * @param {string|null} id - ID opcional para o gráfico.
   */
  getChart() {
    this.resetConfigurations();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    let instrumentId = this.activatedRoute.snapshot.params.instrumentId;
    let params = {};

    // Validação das datas
    const startDateControl = this.controls['start_date'];
    const endDateControl = this.controls['end_date'];

    if (!startDateControl.value || !endDateControl.value) {
      this.messageReturn.text = 'Por favor, preencha as datas inicial e final para gerar o gráfico.';
      this.messageReturn.status = true;

      // Marca os campos como tocados para ativar as validações
      startDateControl.markAsTouched();
      endDateControl.markAsTouched();

      setTimeout(() => {
        this.messageReturn.status = false;
      }, 4000);
      return; // Impede a continuação caso as datas não estejam preenchidas
    }

    // Prepara os parâmetros para a API
    if (startDateControl.value != '') {
      params['StartDate'] = moment(startDateControl.value).format('YYYY-MM-DD');
    }

    if (endDateControl.value != '') {
      params['EndDate'] = moment(endDateControl.value).format('YYYY-MM-DD');
    }

    // Chamada da API
    this.chartServiceApi.getChartSurfaceLandmarkOrPrism(instrumentId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados) {
        this.data = dados;
        this.formatData();
      } else {
        this.messageReturn.text = MessageInputInvalid.NoChart;
        this.messageReturn.status = true;

        setTimeout(() => {
          this.messageReturn.status = false;
        }, 4000);
      }
    });
  }

  //Constrói o gráfico a partir dos dados fornecidos.
  constructChart(data) {
    this.controls['chart_height'].setValue(300);
    this.constructXAxis(data);
  }

  //Constrói o eixo X do gráfico com base nas datas dos dados fornecidos.
  constructXAxis(data) {
    let dates = [];

    // Instrumento
    dates = data.readings.map((item) => moment(item.date).format('DD/MM/YYYY HH:mm:ss'));
    this.xAxis.push(...dates);

    const orderedDates = this.xAxis
      .map((dateTimeString) => {
        // Convertendo o formato brasileiro para o formato americano
        const [datePart, timePart] = dateTimeString.split(' ');
        const [dia, mes, ano] = datePart.split('/').map(Number);
        const [hora, minuto, segundo] = timePart.split(':').map(Number);
        return new Date(ano, mes - 1, dia, hora, minuto, segundo);
      })
      .sort((a, b) => a - b)
      .map((date) => date.toLocaleString('pt-BR').replace(',', '')); // Usando toLocaleString para considerar a data e a hora

    this.xAxis = orderedDates;
    this.xAxis = this.uniqueArray(this.xAxis);

    this.constructSeries(data);
  }

  //Limpa o formulário das configurações do gráfico.
  resetConfigurations(generate = false) {
    this.chart = {};
    this.xAxis = [];
    this.yAxis = [];

    this.yAxisDescription = '';

    this.chartSeries = [];
    this.chartLegends = [];

    this.min = null;
    this.max = null;

    if (this.data !== null && generate) {
      this.formatData();
    }
  }

  //Constrói as séries de dados do gráfico.
  constructSeries(data) {
    const datesObject = {};

    for (const date of this.xAxis) {
      datesObject[date] = null;
    }

    let series = {};
    this.chartSeries = [];
    this.chartLegends = [];

    this.controls['chart_height'].setValue(Math.floor(this.chartLegendsTop) + this.controls['chart_height'].value);
    this.yAxisDescription = fn.findIndexInArrayofObject(this.yAxisDisplacement, 'value', this.controls['yaxis_displacement'].value, 'label', true);

    series['Limite Superior'] = { ...datesObject };
    series['Limite Inferior'] = { ...datesObject };
    series[this.yAxisDescription.label] = { ...datesObject };

    const securityLevels = Object.keys(data['security-levels']);

    // Filtrar os níveis de segurança para incluir apenas aqueles com valores não nulos
    const filteredSecurityLevels = securityLevels.filter((level) => {
      return data['security-levels'][level].some((item) => item.value !== null);
    });

    filteredSecurityLevels.forEach((securityLevel) => {
      series[securityLevel] = { ...datesObject };
    });

    // Preencher dados das leituras
    data.readings.forEach((element) => {
      let date = moment(element.date).format('DD/MM/YYYY HH:mm:ss');

      // Correção: Garante que a chave existe antes de definir valores
      if (!series['Limite Superior']) series['Limite Superior'] = {};
      if (!series['Limite Inferior']) series['Limite Inferior'] = {};
      if (!series[this.yAxisDescription.label]) series[this.yAxisDescription.label] = {};

      const upper_limit = element.upper_limit;
      const lower_limit = element.lower_limit;
      const value = element.displacement;

      series['Limite Superior'][date] = upper_limit != null ? parseFloat(upper_limit) : null;
      series['Limite Inferior'][date] = lower_limit != null ? parseFloat(lower_limit) : null;
      series[this.yAxisDescription.label][date] = value != null ? parseFloat(value) : null;

      // series['Limite Superior'][date] =
      //   series.hasOwnProperty('Limite Superior') && series['Limite Superior'].hasOwnProperty(date)
      //     ? typeof upper_limit == 'string'
      //       ? parseFloat(upper_limit)
      //       : upper_limit
      //     : null;

      // series['Limite Inferior'][date] =
      //   series.hasOwnProperty('Limite Inferior') && series['Limite Inferior'].hasOwnProperty(date)
      //     ? typeof lower_limit == 'string'
      //       ? parseFloat(lower_limit)
      //       : lower_limit
      //     : null;

      // series[this.yAxisDescription.label][date] =
      //   series.hasOwnProperty(this.yAxisDescription.label) && series[this.yAxisDescription.label].hasOwnProperty(date)
      //     ? typeof value == 'string'
      //       ? parseFloat(value)
      //       : value
      //     : null;
    });

    // Adicionar séries ao gráfico
    const itemSeriesUL = {
      name: 'Limite Superior',
      type: 'line',
      data: Object.values(series['Limite Superior']),
      connectNulls: true,
      itemStyle: {
        color: this.controls['color_upper_limit'].value
      }
    };

    this.chartSeries.push(itemSeriesUL);
    this.chartLegends.push('Limite Superior');
    this.defineMinMax(itemSeriesUL.data);

    const itemSeriesLL = {
      name: 'Limite Inferior',
      type: 'line',
      data: Object.values(series['Limite Inferior']),
      connectNulls: true,
      itemStyle: {
        color: this.controls['color_lower_limit'].value
      }
    };
    this.chartSeries.push(itemSeriesLL);
    this.chartLegends.push('Limite Inferior');
    this.defineMinMax(itemSeriesLL.data);

    const itemSeries = {
      name: this.yAxisDescription.label,
      type: 'line',
      data: Object.values(series[this.yAxisDescription.label]),
      connectNulls: true,
      itemStyle: {
        color: this.controls['color_displacement'].value
      }
    };

    this.chartSeries.push(itemSeries);
    this.chartLegends.push(this.yAxisDescription.label);
    this.defineMinMax(itemSeries.data);

    // Adicionar níveis de segurança ao gráfico
    for (const key in data['security-levels']) {
      data['security-levels'][key].forEach((element) => {
        let date = moment(element.date).format('DD/MM/YYYY HH:mm:ss');
        const value = element.value;

        // Correção: Garante que a chave existe antes de definir valores
        if (!series[key]) series[key] = {};
        series[key][date] = value != null ? parseFloat(value) : null;

        //series[key][date] = series.hasOwnProperty(key) && series[key].hasOwnProperty(date) ? (typeof value == 'string' ? parseFloat(value) : value) : null;
      });

      if (data['security-levels'][key].length > 0) {
        const itemSeries = {
          name: key,
          type: 'line',
          data: Object.values(series[key]),
          connectNulls: true,
          itemStyle: {
            color: data['security-levels'][key][0].color
          }
        };
        this.chartLegends.push(key);
        this.chartSeries.push(itemSeries);
        this.defineMinMax(itemSeries.data);
      }
    }

    this.constructYAxis();
  }

  //Prepara os dados para os eixos Y do gráfico.
  constructYAxis() {
    this.yAxis = [];

    let itemYAxis = {
      name: this.yAxisDescription.label,
      type: 'value',
      axisLine: {
        show: true
      },
      nameRotate: 90,
      nameLocation: 'center',
      nameGap: 45,
      nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
      alignTicks: true,
      axisLabel: {
        formatter: function (value, index) {
          return value.toFixed(1);
        }
      },
      show: true,
      interval: 5,
      min: this.min,
      max: this.max
    };

    this.yAxis.push(itemYAxis);

    this.generateChart();
  }

  /**
   * Define os valores mínimos e máximos dos dados da série para o eixo Y.
   * @param {any} array - Dados da série.
   * @param {number} index - Índice do eixo Y.
   */
  defineMinMax(array: any, index = null) {
    // Remover elementos nulos do array
    const filteredArray = array.filter((item) => item !== null);

    // Mapear para valores numéricos
    const numericArray = filteredArray.map((item) => (typeof item === 'number' ? item : item.value));

    // Calcular mínimo e máximo
    const min = Math.min(...numericArray);
    const max = Math.max(...numericArray);

    // Arredondar para múltiplos de 10 com casas decimais
    const roundToMultipleOf10 = (value: number) => {
      const roundedValue = Math.round(value * 10) / 10;
      return roundedValue % 1 === 0 ? roundedValue : roundedValue.toFixed(1);
    };

    const previous: any = roundToMultipleOf10(Math.floor(min));
    const next: any = roundToMultipleOf10(Math.ceil(max));

    // Definir valores mínimo e máximo na instância atual
    this.min = this.min == null ? previous : Math.min(this.min, previous);
    this.max = this.max == null ? next : Math.max(this.max, next);
  }

  /**
   * Executa ações quando um clique é detectado fora de um elemento específico.
   * @param {string} element - Elemento que detectou o clique fora.
   * @param {number} index - Índice do elemento.
   */
  onClickedOutside(element: string, index = '') {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker[index] = false;
        break;
    }
  }

  /**
   * Atualiza a cor selecionada após a conclusão da seleção de cor.
   * @param {any} $event - Evento de seleção de cor.
   * @param {number} index - Índice do controle de cor.
   */
  changeComplete($event, index = '') {
    this.selectedColor[index] = $event.color.hex;
    this.controls[index].setValue(this.selectedColor[index]);
  }

  //Define a altura do gráfico.
  setHeight(height: string): void {
    this.controls['chart_height'].setValue(parseInt(height));
    this.generateChart();
  }

  /**
   * Remove elementos repetidos de um array.
   * @param {any[]} array - Array de elementos.
   * @returns {any[]} - Array com elementos únicos.
   */
  uniqueArray(array) {
    const uniqueArray = [];
    const seeDates = {};

    for (const date of array) {
      if (!seeDates[date]) {
        uniqueArray.push(date);
        seeDates[date] = true;
      }
    }

    return uniqueArray;
  }

  /**
   * Carrega o conteúdo de um arquivo a partir de um caminho específico.
   * @param {string} path - Caminho para o arquivo.
   * @param {string} fileName - Nome do arquivo.
   * @param {number} index - Índice do marcador.
   */
  loadFileContent(path: string, fileName: string, index = 0) {
    this.fileLoaderService.loadFile(path, fileName).subscribe(
      (data) => {
        // this.markers[index].text = data;
      },
      (error) => {
        console.error('Erro ao carregar o arquivo:', error);
      }
    );
  }

  /**
   * Substitui todas as ocorrências de uma string por outra em um texto.
   * @param {string} text - Texto original.
   * @param {string[]} oldValues - Array de valores a serem substituídos.
   * @param {string[]} newValues - Array de novos valores para substituição.
   * @returns {string} - Texto com as substituições aplicadas.
   */
  replaceMultipleOccurrences(text, oldValues, newValues) {
    if (oldValues.length !== newValues.length) {
      throw new Error('Os arrays devem ter o mesmo comprimento.');
    }

    let newText = text;
    for (let i = 0; i < oldValues.length; i++) {
      const oldValue = oldValues[i];
      const newValue = newValues[i];
      newText = newText.split(oldValue).join(newValue);
    }

    return newText;
  }

  changeYAxis() {
    if (this.data !== null) {
      this.resetConfigurations(true);
    }
  }

  //Formata os dados recebidos e os prepara para a construção do gráfico.
  formatData() {
    const displacement = [
      null,
      'east_displacement',
      'north_displacement',
      'a_displacement',
      'b_displacement',
      'z_displacement',
      'total_planimetric_displacement'
    ];

    const security = [
      null,
      'e_axis_security_levels',
      'n_axis_security_levels',
      'a_axis_security_levels',
      'b_axis_security_levels',
      'z_axis_security_levels',
      'planimetric_axis_security_levels'
    ];

    const idx = this.formChart.controls['yaxis_displacement'].value;

    let dados: any = {
      readings: [],
      'security-levels': {
        'Nível de Atenção': [],
        'Nível de Alerta': [],
        'Nível de Emergência': []
      }
    };

    this.data.readings.forEach((item, index) => {
      dados.readings[index] = {
        displacement: item[displacement[idx]],
        upper_limit: item[displacement[idx]] + item.upper_limit,
        lower_limit: item[displacement[idx]] - item.lower_limit,
        date: item.date
      };

      if (item[security[idx]] != null) {
        dados['security-levels']['Nível de Atenção'].push({
          value: item[security[idx]].attention_level,
          color: '#ffc107',
          date: item.date
        });

        dados['security-levels']['Nível de Alerta'].push({
          value: item[security[idx]].alert_level,
          color: '#fd7e14',
          date: item.date
        });

        dados['security-levels']['Nível de Emergência'].push({
          value: item[security[idx]].emergency_level,
          color: '#dc3545',
          date: item.date
        });
      }
    });

    this.constructChart(dados);
  }

  //Gera e configura o gráfico usando as opções definidas.
  generateChart() {
    this.chart['options'] = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegends,
        icon: 'rect',
        left: 'center',
        top: 'bottom',
        selected: {
          'Limite Superior': false, // Desativado por padrão
          'Limite Inferior': false // Desativado por padrão
        }
      },
      grid: {
        containLabel: true,
        top: this.chartLegendsTop,
        left: 50,
        right: 50,
        height: this.controls['chart_height'].value
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        interval: 0.1,
        boundaryGap: true,
        data: this.xAxis,
        axisLabel: {
          interval: Math.floor(this.xAxis.length / 35), // Define o intervalo para exibir todos os valores do eixo X
          rotate: 60
        }
      },
      yAxis: this.yAxis,
      series: this.chartSeries
    };
  }

  //Navega de volta para a tela de instrumentos.
  goBack() {
    this.router.navigate(['/instruments']);
  }
}
