import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormBuilder, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-modal-insert-aspect',
  templateUrl: './modal-insert-aspect.component.html',
  styleUrls: ['./modal-insert-aspect.component.scss']
})
export class ModalInsertAspectComponent implements OnInit, OnChanges {
  @ViewChild('modalInsertAspect') ModalInsertAspect: ElementRef;

  @Input() public areasAspectsList: any;
  @Input() public areas: any;

  @Output() public aspectInserted = new EventEmitter<{ area: { id: string; name: string }; aspect: { id: string; description: string } }>();

  public formModal: FormGroup; // Formulário reativo
  public filteredAreasList: any[] = [];
  public filteredAspectsList: any[] = [];
  public message: any = { text: '', status: false, class: 'alert-success' };

  constructor(private fb: FormBuilder, private modalService: NgbModal) {}

  /**
   * Inicializa o componente ao ser carregado.
   */
  ngOnInit(): void {
    this.formModal = this.fb.group({
      area: [null], // Área selecionada
      aspect: [null] // Aspecto selecionado
    });

    // Filtrar áreas no início
    this.updateFilteredAreas();
  }

  /**
   * Detecta e processa mudanças nos inputs vinculados ao componente.
   * @param {SimpleChanges} changes - Contém as mudanças detectadas nos inputs do componente.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['areasAspectsList'] || changes['areas']) {
      this.updateFilteredAreas();
    }
  }

  /**
   * Atualiza a lista de áreas filtradas.
   */
  updateFilteredAreas(): void {
    this.filteredAreasList = this.areasAspectsList.filter((areaAspect) => {
      const existingArea = this.areas.find((a) => a.id === areaAspect.id);
      if (!existingArea) return true; // Área não está em `this.areas`, incluir na lista

      // Verificar se há aspectos faltantes
      const existingAspectIds = existingArea.aspects.map((aspect) => aspect.id);
      return areaAspect.aspects.some((aspect) => !existingAspectIds.includes(aspect.id));
    });

    // Define a primeira área e seus aspectos como padrão
    const defaultAreaId = this.filteredAreasList[0]?.id || null;
    if (defaultAreaId) {
      this.formModal.get('area')?.setValue(defaultAreaId);
      this.updateFilteredAspects(defaultAreaId);

      // Define o primeiro aspecto como padrão
      const defaultAspectId = this.filteredAspectsList[0]?.id || null;
      if (defaultAspectId) {
        this.formModal.get('aspect')?.setValue(defaultAspectId);
      }
    }
  }

  /**
   * Atualiza a lista de aspectos filtrados com base na área selecionada.
   * @param {string} areaId - ID da área selecionada.
   */
  updateFilteredAspects(areaId: string): void {
    const area = this.areasAspectsList.find((a) => a.id === areaId);
    const existingArea = this.areas.find((a) => a.id === areaId);

    if (!area) {
      this.filteredAspectsList = [];
      this.message = { text: 'Nenhum aspecto disponível para esta área.', status: true, class: 'alert-warning' };
      return;
    }

    const existingAspectIds = existingArea ? existingArea.aspects.map((aspect) => aspect.id) : [];
    this.filteredAspectsList = area.aspects.filter((aspect) => !existingAspectIds.includes(aspect.id));

    if (this.filteredAspectsList.length === 0) {
      this.message = { text: 'Nenhum aspecto disponível para esta área.', status: true, class: 'alert-warning' };
    } else {
      this.message = { text: '', status: false, class: '' };
    }
  }

  /**
   * Abre o modal de inserção de aspectos.
   */
  openModal() {
    this.modalService.open(this.ModalInsertAspect, { size: 'md' });
  }

  /**
   * Fecha o modal de inserção de aspectos.
   */
  closeModal(): void {
    this.modalService.dismissAll();
  }

  /**
   * Insere um novo aspecto na área selecionada.
   */
  public insertAspect(): void {
    const selectedArea = this.formModal.get('area')?.value;
    const selectedAspect = this.formModal.get('aspect')?.value;

    // Validação dos dados selecionados
    if (!selectedArea) {
      this.message = {
        status: true,
        text: 'Por favor, selecione uma área.',
        class: 'alert-danger'
      };
      return;
    }

    if (!selectedAspect) {
      this.message = {
        status: true,
        text: 'Por favor, selecione um aspecto.',
        class: 'alert-danger'
      };
      return;
    }

    this.aspectInserted.emit({
      area: { id: selectedArea, name: this.getAreaName(selectedArea) },
      aspect: { id: selectedAspect, description: this.getAspectDescription(selectedAspect) }
    });
    // Também emite para outro fluxo, se necessário
    // this.getAspect.emit(selectedAspect);

    this.resetForm(); // Limpa o formulário após a inserção
    this.closeModal(); // Fecha o modal após a inserção
  }

  /**
   * Obtém o nome da área com base no seu ID.
   * @param {string} areaId - ID da área.
   * @returns {string} - Nome da área correspondente.
   */
  private getAreaName(areaId: string): string {
    // Procura pela área na lista areasAspectsList
    const area = this.areasAspectsList.find((a) => a.id === areaId);
    return area ? area.name : '';
  }

  /**
   * Obtém a descrição do aspecto com base no seu ID.
   * @param {string} aspectId - ID do aspecto.
   * @returns {string} - Descrição do aspecto correspondente.
   */
  private getAspectDescription(aspectId: string): string {
    // Percorre todas as áreas e seus aspectos para encontrar a descrição do aspecto
    for (const area of this.areasAspectsList) {
      const aspect = area.aspects.find((a) => a.id === aspectId);
      if (aspect) {
        return aspect.description;
      }
    }
    return '';
  }

  /**
   * Manipula a mudança da área selecionada no formulário.
   * @param {Event} event - Evento disparado ao selecionar uma área.
   */
  onAreaChange(event: Event): void {
    const areaId = (event.target as HTMLSelectElement).value; // Converte para HTMLSelectElement
    this.updateFilteredAspects(areaId);
    this.formModal.get('aspect')?.reset(); // Limpa o aspecto selecionado
  }

  /**
   * Reseta o formulário e limpa as mensagens.
   */
  resetForm(): void {
    this.formModal.reset();
    this.message = { status: false, text: '', class: '' };
  }
}
