import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { IDropdownSettings } from 'ng-multiselect-dropdown';

import { FormService } from 'src/app/services/form.service';

import { typeCohesion } from 'src/app/constants/static-materials.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-undrained',
  templateUrl: './undrained.component.html',
  styleUrls: ['./undrained.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class UndrainedComponent implements OnInit, OnChanges {
  @Input() public data: any = null;
  @Input() public view: boolean = false;

  public formUndrained: FormGroup = new FormGroup({
    cohesion_type: new FormControl([], [Validators.required]),
    //Constant
    cohesion: new FormControl('', [Validators.required]),
    tensile_strength: new FormControl({ value: null, disabled: true }),
    is_tensile_strength: new FormControl(false),
    //F(Depth from Top of Layer)
    cohesion_top: new FormControl('', [Validators.required]),
    cohesion_variation: new FormControl('', [Validators.required]),
    f_tensile_strength: new FormControl({ value: null, disabled: true }),
    is_f_tensile_strength: new FormControl(false),
    maximum: new FormControl({ value: null, disabled: true }),
    is_maximum: new FormControl(false),
    //F(Depth from Horizontal Datum)
    cohesion_datum: new FormControl('', [Validators.required]),
    cohesion_variation_datum: new FormControl('', [Validators.required]),
    datum: new FormControl('', [Validators.required]),
    datum_tensile_strength: new FormControl({ value: null, disabled: true }),
    is_datum_tensile_strength: new FormControl(false),
    datum_maximum: new FormControl({ value: null, disabled: true }),
    is_datum_maximum: new FormControl(false),
    //F(Distance to slope)
    cohesion_top_slope: new FormControl('', [Validators.required]),
    cohesion_variation_slope: new FormControl('', [Validators.required]),
    slope_tensile_strength: new FormControl({ value: null, disabled: true }),
    is_slope_tensile_strength: new FormControl(false),
    maximum_slope: new FormControl({ value: null, disabled: true }),
    is_maximum_slope: new FormControl(false)
  });

  public typeCohesion = typeCohesion;

  public selectedTypeCohesion = null;

  public fields: any = [];

  public func = fn;

  public dropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'name',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 10,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  constructor(public formService: FormService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  itemEvent(item: any, action: string = 'select') {
    if (this.formUndrained.controls['cohesion_type'].value.length > 0) {
      this.selectedTypeCohesion = this.formUndrained.controls['cohesion_type'].value[0].id;
    } else {
      this.selectedTypeCohesion = null;
    }
  }

  splitData($dados) {
    this.formUndrained.controls['cohesion_type'].setValue([
      fn.findIndexInArrayofObject(this.typeCohesion, 'id', $dados.cohesion_type.toString(), 'name', true)
    ]);

    this.selectedTypeCohesion = $dados.cohesion_type;

    switch ($dados.cohesion_type) {
      case 1:
        this.formUndrained.controls['cohesion'].setValue($dados.cohesion);
        if ($dados.tensile_strength != null) {
          this.formUndrained.controls['is_tensile_strength'].setValue(true);
          this.formUndrained.controls['tensile_strength'].setValue($dados.tensile_strength);
          this.formService.checkboxControlValidate(this.formUndrained, 'tensile_strength');
        }
        break;
      case 2:
        this.formUndrained.controls['cohesion_top'].setValue($dados.cohesion);
        this.formUndrained.controls['cohesion_variation'].setValue($dados.cohesion_variation);
        if ($dados.tensile_strength != null) {
          this.formUndrained.controls['is_f_tensile_strength'].setValue(true);
          this.formUndrained.controls['f_tensile_strength'].setValue($dados.tensile_strength);
          this.formService.checkboxControlValidate(this.formUndrained, 'f_tensile_strength');
        }
        if ($dados.maximum != null) {
          this.formUndrained.controls['is_maximum'].setValue(true);
          this.formUndrained.controls['maximum'].setValue($dados.maximum);
          this.formService.checkboxControlValidate(this.formUndrained, 'maximum');
        }
        break;
      case 3:
        this.formUndrained.controls['cohesion_datum'].setValue($dados.cohesion);
        this.formUndrained.controls['cohesion_variation_datum'].setValue($dados.cohesion_variation);
        this.formUndrained.controls['datum'].setValue($dados.datum);
        if ($dados.tensile_strength != null) {
          this.formUndrained.controls['is_datum_tensile_strength'].setValue(true);
          this.formUndrained.controls['datum_tensile_strength'].setValue($dados.tensile_strength);
          this.formService.checkboxControlValidate(this.formUndrained, 'datum_tensile_strength');
        }
        if ($dados.maximum != null) {
          this.formUndrained.controls['is_datum_maximum'].setValue(true);
          this.formUndrained.controls['datum_maximum'].setValue($dados.maximum);
          this.formService.checkboxControlValidate(this.formUndrained, 'datum_maximum');
        }
        break;
      case 4:
        this.formUndrained.controls['cohesion_top_slope'].setValue($dados.cohesion);
        this.formUndrained.controls['cohesion_variation_slope'].setValue($dados.cohesion_variation);
        if ($dados.tensile_strength != null) {
          this.formUndrained.controls['is_slope_tensile_strength'].setValue(true);
          this.formUndrained.controls['slope_tensile_strength'].setValue($dados.tensile_strength);
          this.formService.checkboxControlValidate(this.formUndrained, 'slope_tensile_strength');
        }
        if ($dados.maximum != null) {
          this.formUndrained.controls['is_maximum_slope'].setValue(true);
          this.formUndrained.controls['maximum_slope'].setValue($dados.maximum);
          this.formService.checkboxControlValidate(this.formUndrained, 'maximum_slope');
        }
        break;
    }

    if (this.view) {
      this.formUndrained.disable();
    }
  }

  validate() {
    let formFields: any = [];
    let formValid = false;

    if (this.formUndrained.controls['cohesion_type'].value.length > 0) {
      switch (this.formUndrained.controls['cohesion_type'].value[0].id) {
        case '1':
          formFields = ['cohesion', 'tensile_strength', 'is_tensile_strength'];
          break;
        case '2':
          formFields = ['cohesion_top', 'cohesion_variation', 'is_f_tensile_strength', 'f_tensile_strength', 'is_maximum', 'maximum'];
          break;
        case '3':
          formFields = [
            'cohesion_datum',
            'cohesion_variation_datum',
            'datum',
            'is_datum_tensile_strength',
            'datum_tensile_strength',
            'is_datum_maximum',
            'datum_maximum'
          ];
          break;
        case '4':
          formFields = [
            'cohesion_top_slope',
            'cohesion_variation_slope',
            'is_slope_tensile_strength',
            'slope_tensile_strength',
            'is_maximum_slope',
            'maximum_slope'
          ];
          break;
      }
      formValid = this.formService.validateFormList(this.formUndrained, formFields);
    }

    if (!formValid) {
      this.formUndrained.markAllAsTouched();
    }

    return formValid;
  }
}
