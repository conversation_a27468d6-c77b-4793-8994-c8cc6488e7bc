import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import type { ECharts, EChartsOption } from 'echarts';
import * as echarts from 'echarts';

@Component({
  selector: 'app-e-charts',
  templateUrl: './e-charts.component.html',
  styleUrls: ['./e-charts.component.scss']
})
export class EChartsComponent implements OnInit {
  @Input() public dataChart: any;
  @Input() public height: any = 0;
  @Input() public id: string = 'chart-container';
  @Output() chartInit = new EventEmitter<ECharts>(); // Emite a instância do gráfico

  public options: EChartsOption;
  public chartInstance: ECharts;

  constructor() {}

  ngOnInit(): void {}

  /**
   * Método do ciclo de vida Angular chamado após a inicialização da view.
   * Inicializa a instância do gráfico ECharts no elemento com o `id` fornecido
   * e aplica as opções definidas em `dataChart.options`.
   * Emite o evento `chartInit` com a instância do gráfico.
   */
  ngAfterViewInit(): void {
    const el = document.getElementById(this.id);

    if (!el) {
      console.warn(`[ECharts] Elemento com id '${this.id}' não encontrado no DOM.`);
      return;
    }

    if (!this.dataChart?.options) {
      console.warn(`[ECharts] dataChart.options não definido ao inicializar o gráfico '${this.id}'.`);
      return;
    }

    this.chartInstance = echarts.init(el);
    //this.chartInstance.setOption(this.dataChart.options);
    this.chartInstance.setOption(this.dataChart.options, true);
    this.chartInit.emit(this.chartInstance);
  }

  /**
   * Redimensiona dinamicamente o gráfico ECharts, caso ele já tenha sido inicializado.
   * Ideal para ser chamado após mudanças no layout ou em eventos de resize da janela.
   */
  public resize(): void {
    if (this.chartInstance) {
      this.chartInstance.resize();
    }
  }
}
