import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class ClientService {
  constructor(private api: ApiService) {}

  // Cadastro de clientes
  postClient(params: any) {
    const url = '/clients';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna os clientes para uso em filtro
  getClients(params: any) {
    const url = '/clients/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getClientsList(params: any = {}) {
    const url = '/clients';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca o cliente por ID
  getClientById(id: string) {
    const url = `/clients/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  // Atualizará todos dados do cliente
  putClients(id: string, params: any) {
    const url = `/clients/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  // Exclui o registro do cliente no banco de dados
  deleteClient(id: string) {
    const url = `/clients/${id}`;
    return this.api.delete<any>(url, 'client');
  }

  patchClients(id: string, params: any) {
    const url = `/clients/${id}`;
    return this.api.patch<any>(url, params, 'client');
  }

  // Logo do cliente, tela inicial
  getClientLogo(id: string) {
    const url = `/clients/${id}/logo`;
    return this.api.get<any>(url, null, false, 'client');
  }
}
