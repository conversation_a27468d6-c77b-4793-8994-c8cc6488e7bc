<div class="list-content mt-2">
  <form [formGroup]="formFilter">
    <div class="row mt-2">
      <div class="col-sm-12">
        <div class="card">
          <div class="card-header form-control-bg">
            <span> {{ mapName }} {{ instrumentName }}</span>
            <br />
          </div>
          <div class="card-body">
            <div
              class="row g-3 mt-1"
              *ngIf="mapType == '' || mapType.includes('general')"
            >
              <!-- Selects Cliente, Unidade e Estrutura -->
              <app-hierarchy
                #hierarchy
                [elements]="elements"
                class="col-md-12"
                (sendEventHierarchy)="getEventHierarchy($event)"
                (filtersChanged)="filterEventHierarchy($event)"
              ></app-hierarchy>
            </div>
            <div class="row mt-1">
              <!-- Subtipo -->
              <div
                class="col-md-3"
                *ngIf="
                  (mapType == '' || mapType.includes('general')) &&
                  structureSelected
                "
              >
                <label class="form-label">Subtipo</label>
                <select
                  class="form-select"
                  formControlName="Subtype"
                  (change)="filterSubtypes()"
                >
                  <option value="">Selecione...</option>
                  <ng-template ngFor let-item [ngForOf]="subTypes">
                    <option
                      [ngValue]="item.id"
                      *ngIf="['1', '2'].includes(item.id)"
                    >
                      {{ item.value }}
                    </option>
                  </ng-template>
                </select>
              </div>
              <!-- Período -->
              <div
                class="col-md-3"
                *ngIf="
                  structureSelected && formFilter.get('Subtype').value != ''
                "
              >
                <label class="form-label">Período </label>
                <select
                  class="form-select"
                  formControlName="Period"
                  (change)="calculatePeriod(formFilter.get('Period').value)"
                >
                  <ng-template ngFor let-item [ngForOf]="period">
                    <option [ngValue]="item.value">{{ item.label }}</option>
                  </ng-template>
                  <option value="0">Personalizado</option>
                </select>
              </div>
              <!-- Data inicial -->
              <div
                class="col-md-2"
                *ngIf="
                  formFilter.get('Period').value != 1 &&
                  formFilter.get('Subtype').value != ''
                "
              >
                <label class="form-label">Data inicial</label>
                <input
                  type="date"
                  class="form-control"
                  formControlName="StartDate"
                  (change)="
                    calculatePeriod(formFilter.get('Period').value, false)
                  "
                />
              </div>
              <!-- Data final -->
              <div
                class="col-md-2"
                *ngIf="
                  formFilter.get('Period').value != 1 &&
                  formFilter.get('Subtype').value != ''
                "
              >
                <label class="form-label">Data final</label>
                <input
                  type="date"
                  class="form-control"
                  formControlName="EndDate"
                  (change)="
                    calculatePeriod(formFilter.get('Period').value, false)
                  "
                />
              </div>

              <!-- Gerar Mapa -->
              <div
                class="col-md-2 mt-2 d-flex align-items-end"
                *ngIf="formFilter.get('Subtype').value != ''"
              >
                <app-button
                  [class]="'btn-logisoil-blue'"
                  [icon]="'fa fa-map'"
                  [label]="'Gerar Mapa'"
                  (click)="generateMap()"
                ></app-button>
              </div>
              <div class="row mt-2">
                <!-- Instrumento -->
                <div
                  class="col-md-3"
                  *ngIf="
                    (mapType == '' || mapType.includes('general')) &&
                    controls['Subtype'].value != '' &&
                    dataMap?.instruments
                  "
                >
                  <label class="form-label">Localizar instrumento</label>
                  <ng-multiselect-dropdown
                    [placeholder]="'Selecione...'"
                    [settings]="instrumentsSettings"
                    [data]="dataMap?.instruments"
                    formControlName="InstrumentId"
                    (onSelect)="
                      getEventHierarchy({
                        action: 'select',
                        type: 'instruments',
                        element:
                          this.formFilter.controls['InstrumentId'].value[0]
                      })
                    "
                    (onDeSelect)="
                      getEventHierarchy({
                        action: 'deselect',
                        type: 'instruments',
                        element:
                          this.formFilter.controls['InstrumentId'].value[0]
                      })
                    "
                  >
                  </ng-multiselect-dropdown>
                </div>
              </div>
            </div>
            <!-- Parâmetros de percolação -->
            <div
              class="mt-2 row"
              *ngIf="
                (mapType == 'percolation' || mapType.includes('percolation')) &&
                ctrlParamsPercolation
              "
            >
              <div class="col-md-12 mt-2 d-flex align-items-end">
                <!-- Dam break -->
                <app-button
                  [class]="
                    percolationConfig.kml
                      ? 'btn-logisoil-gray'
                      : 'btn-logisoil-brown'
                  "
                  [icon]="
                    percolationConfig.kml ? 'fa fa-eye' : 'fa fa-eye-slash'
                  "
                  [label]="'Dam Break'"
                  class="me-2"
                  (click)="managerKMLLayers()"
                ></app-button>
                <!-- Seções -->
                <app-button
                  [class]="
                    percolationConfig.section
                      ? 'btn-logisoil-gray'
                      : 'btn-logisoil-blue'
                  "
                  [icon]="
                    percolationConfig.section ? 'fa fa-eye' : 'fa fa-eye-slash'
                  "
                  [label]="'Seções'"
                  class="me-2"
                  (click)="managerSections()"
                ></app-button>
                <!-- Pulso -->
                <app-button
                  [class]="
                    percolationConfig.pulse
                      ? 'btn-logisoil-red'
                      : 'btn-logisoil-blue'
                  "
                  [icon]="'fa fa-bullseye'"
                  [label]="
                    percolationConfig.pulse ? 'Desligar pulso' : 'Ligar pulso'
                  "
                  (click)="
                    percolationConfig.pulse = !percolationConfig.pulse;
                    ctrlPulse()
                  "
                  class="me-2"
                ></app-button>
                <!-- Resetar zoom -->
                <app-button
                  [class]="'btn-logisoil-gray'"
                  [icon]="'fa fa-magnifying-glass-location'"
                  [label]="'Resetar Zoom'"
                  class="me-2"
                  (click)="resetZoom()"
                ></app-button>
                <!-- Cor variação absoluta positiva -->
                <div
                  (clickOutside)="
                    onClickedOutside('colorPicker', 'absPositive')
                  "
                >
                  <app-button
                    [class]="'btn-logisoil-white'"
                    [icon]="'fa fa-square fa-xl'"
                    [iconColor]="selectedColor.absPositive"
                    [label]="'Δabs +'"
                    class="me-2"
                    (click)="
                      showColorPicker.absPositive = !showColorPicker.absPositive
                    "
                  ></app-button>
                  <div
                    class="d-flex justify-content-center justify-content-md-start color-picker-container"
                    *ngIf="showColorPicker.absPositive"
                  >
                    <div style="width: 220px; display: inline-block">
                      <color-sketch
                        [color]="selectedColor.absPositive"
                        (onChangeComplete)="
                          changeComplete($event, 'absPositive')
                        "
                      ></color-sketch>
                    </div>
                  </div>
                </div>
                <!-- Cor variação absoluta negativa -->
                <div
                  (clickOutside)="
                    onClickedOutside('colorPicker', 'absNegative')
                  "
                >
                  <app-button
                    [class]="'btn-logisoil-white'"
                    [icon]="'fa fa-square fa-xl'"
                    [iconColor]="selectedColor.absNegative"
                    [label]="'Δabs -'"
                    class="me-2"
                    (click)="
                      showColorPicker.absNegative = !showColorPicker.absNegative
                    "
                  ></app-button>
                  <div
                    class="d-flex justify-content-center justify-content-md-start color-picker-container"
                    *ngIf="showColorPicker.absNegative"
                  >
                    <div style="width: 220px; display: inline-block">
                      <color-sketch
                        [color]="selectedColor.absNegative"
                        (onChangeComplete)="
                          changeComplete($event, 'absNegative')
                        "
                      ></color-sketch>
                    </div>
                  </div>
                </div>
                <!-- Cor variação nula -->
                <div
                  (clickOutside)="onClickedOutside('colorPicker', 'absNull')"
                >
                  <app-button
                    [class]="'btn-logisoil-white'"
                    [icon]="'fa fa-square fa-xl'"
                    [iconColor]="selectedColor.absNull"
                    [label]="'Δabs ~='"
                    class="me-2"
                    (click)="showColorPicker.absNull = !showColorPicker.absNull"
                  ></app-button>
                  <div
                    class="d-flex justify-content-center justify-content-md-start color-picker-container"
                    *ngIf="showColorPicker.absNull"
                  >
                    <div style="width: 220px; display: inline-block">
                      <color-sketch
                        [color]="selectedColor.absNull"
                        (onChangeComplete)="changeComplete($event, 'absNull')"
                      ></color-sketch>
                    </div>
                  </div>
                </div>
                <app-button
                  [class]="'btn-logisoil-green'"
                  [icon]="'fa fa-thin fa-floppy-disk'"
                  [label]="'Salvar Cor'"
                  [type]="false"
                  (click)="postPercolationAbsoluteVariationColor()"
                >
                </app-button>
              </div>
            </div>

            <!-- Parâmetros de deslocamento -->
            <div
              class="row mt-2"
              *ngIf="
                (mapType == 'displacement' ||
                  mapType.includes('displacement')) &&
                ctrlParamsDisplacement
              "
            >
              <!-- Primeira linha -->
              <div class="col-md-3">
                <label class="form-label">Exagero:</label>
                <input
                  class="form-control"
                  type="number"
                  formControlName="Overkill"
                  min="0"
                  step="1"
                  (keypress)="
                    func.controlNumber(
                      $event,
                      formFilter.get('Overkill'),
                      'positiveDecimalLimit'
                    )
                  "
                  (keyup)="
                    func.controlNumber($event, formFilter.get('Overkill'))
                  "
                />
                <!-- Mensagens de erro -->
                <div
                  *ngIf="
                    controls['Overkill'].invalid &&
                    (controls['Overkill'].dirty || controls['Overkill'].touched)
                  "
                  class="text-danger mt-1 small"
                >
                  <div *ngIf="controls['Overkill'].errors?.required">
                    O valor é obrigatório.
                  </div>
                  <div *ngIf="controls['Overkill'].errors?.min">
                    O valor deve ser maior ou igual a 0.
                  </div>
                  <div *ngIf="controls['Overkill'].errors?.pattern">
                    Somente números inteiros são permitidos.
                  </div>
                </div>
              </div>

              <div class="col-md-2">
                <label class="form-label">Casas decimais: </label>
                <input
                  class="form-control"
                  type="number"
                  formControlName="DecimalPlaces"
                  min="0"
                  max="16"
                  step="1"
                  (keypress)="
                    func.controlNumber(
                      $event,
                      formFilter.get('DecimalPlaces'),
                      'positiveDecimalLimit'
                    )
                  "
                  (keyup)="
                    func.controlNumber($event, formFilter.get('DecimalPlaces'))
                  "
                />
              </div>

              <div class="col-md-1">
                <label class="form-label me-2">Unidade: </label>
                <select class="form-select" formControlName="MetricUnit">
                  <ng-template ngFor let-item [ngForOf]="metricUnit">
                    <option [ngValue]="item.id">{{ item.name }}</option>
                  </ng-template>
                </select>
              </div>

              <div class="col-md-3">
                <label class="form-label">
                  Opacidade(%):
                  <small class="text-muted"
                    >(valor inteiro entre 5 e 100)</small
                  >
                </label>
                <input
                  class="form-control"
                  type="number"
                  formControlName="Opacity"
                  min="5"
                  max="100"
                  step="1"
                />
                <!-- Mensagens de erro -->
                <div
                  *ngIf="
                    controls['Opacity'].invalid &&
                    (controls['Opacity'].dirty || controls['Opacity'].touched)
                  "
                  class="text-danger mt-1 small"
                >
                  <div *ngIf="controls['Opacity'].errors?.required">
                    O valor é obrigatório.
                  </div>
                  <div *ngIf="controls['Opacity'].errors?.min">
                    O valor mínimo permitido é 5.
                  </div>
                  <div *ngIf="controls['Opacity'].errors?.max">
                    O valor máximo permitido é 100.
                  </div>
                  <div *ngIf="controls['Opacity'].errors?.pattern">
                    Somente números inteiros são permitidos.
                  </div>
                </div>
              </div>
            </div>

            <!-- Segunda linha -->
            <div
              class="row mt-2"
              *ngIf="
                (mapType == 'displacement' ||
                  mapType.includes('displacement')) &&
                ctrlParamsDisplacement
              "
            >
              <div class="col-md-2">
                <label class="form-label">Espessura vetores:</label>
                <input
                  class="form-control"
                  type="number"
                  formControlName="ArrowWidth"
                  min="1"
                  step="1"
                />
              </div>

              <div
                class="col-md-2 d-flex align-items-end"
                (clickOutside)="onClickedOutside('colorPicker', 'arrowColor')"
              >
                <app-button
                  [class]="'btn-logisoil-white'"
                  [icon]="'fa fa-square fa-xl'"
                  [iconColor]="selectedColor.arrowColor"
                  [label]="'Deslocamentos'"
                  (click)="
                    showColorPicker.arrowColor = !showColorPicker.arrowColor
                  "
                ></app-button>

                <div
                  class="d-flex justify-content-center justify-content-md-start color-picker-container"
                  *ngIf="showColorPicker.arrowColor"
                >
                  <div style="width: 220px; display: inline-block">
                    <color-sketch
                      [color]="selectedColor.arrowColor"
                      (onChangeComplete)="changeComplete($event, 'arrowColor')"
                    ></color-sketch>
                  </div>
                </div>
              </div>
            </div>

            <!-- Parâmetros de deslocamento -->
            <div
              class="row mt-2"
              *ngIf="
                (mapType == 'displacement' ||
                  mapType.includes('displacement')) &&
                ctrlParamsDisplacement
              "
            >
              <div class="col-md-12 mt-2 d-flex align-items-end">
                <!-- Recalque positivo -->
                <div
                  (clickOutside)="
                    onClickedOutside('colorPicker', 'positiveSettlementColor')
                  "
                  style="margin-right: 20px"
                >
                  <app-button
                    [class]="'btn-logisoil-white'"
                    [icon]="'fa fa-square fa-xl'"
                    [iconColor]="selectedColor.positiveSettlementColor"
                    [label]="'Recalque Positivo'"
                    (click)="
                      showColorPicker.positiveSettlementColor =
                        !showColorPicker.positiveSettlementColor
                    "
                  ></app-button>
                  <div
                    class="d-flex justify-content-center justify-content-md-start color-picker-container"
                    *ngIf="showColorPicker.positiveSettlementColor"
                  >
                    <div style="width: 220px; display: inline-block">
                      <color-sketch
                        [color]="selectedColor.positiveSettlementColor"
                        (onChangeComplete)="
                          changeComplete($event, 'positiveSettlementColor')
                        "
                      ></color-sketch>
                    </div>
                  </div>
                </div>
                <!-- Recalque negativo -->
                <div
                  (clickOutside)="
                    onClickedOutside('colorPicker', 'negativeSettlementColor')
                  "
                  style="margin-right: 20px"
                >
                  <div>
                    <app-button
                      [class]="'btn-logisoil-white'"
                      [icon]="'fa fa-square fa-xl'"
                      [iconColor]="selectedColor.negativeSettlementColor"
                      [label]="'Recalque Negativo'"
                      (click)="
                        showColorPicker.negativeSettlementColor =
                          !showColorPicker.negativeSettlementColor
                      "
                    ></app-button>
                    <div
                      class="d-flex justify-content-center justify-content-md-start color-picker-container"
                      *ngIf="showColorPicker.negativeSettlementColor"
                    >
                      <div style="width: 220px; display: inline-block">
                        <color-sketch
                          [color]="selectedColor.negativeSettlementColor"
                          (onChangeComplete)="
                            changeComplete($event, 'negativeSettlementColor')
                          "
                        ></color-sketch>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Deslocamento  altimétrico -->
                <app-button
                  [class]="
                    displacementConfig.pulse
                      ? 'btn-logisoil-blue'
                      : 'btn-logisoil-gray'
                  "
                  [icon]="
                    displacementConfig.pulse ? 'fa fa-eye' : 'fa fa-eye-slash'
                  "
                  [label]="'Desloc. Altimétrico'"
                  style="margin-right: 20px"
                  (click)="
                    displacementConfig.pulse = !displacementConfig.pulse;
                    ctrlPulse()
                  "
                ></app-button>
                <!-- Deslocamento  planimétrico -->
                <app-button
                  [class]="
                    displacementConfig.arrows
                      ? 'btn-logisoil-blue'
                      : 'btn-logisoil-gray'
                  "
                  [icon]="
                    displacementConfig.arrows ? 'fa fa-eye' : 'fa fa-eye-slash'
                  "
                  [label]="'Desloc. Planimétrico'"
                  style="margin-right: 20px"
                  (click)="managerArrows()"
                ></app-button>
                <!-- Salvar configurações -->
                <app-button
                  [class]="'btn-logisoil-green'"
                  [icon]="'fa fa-floppy-disk'"
                  [label]="'Salvar configurações'"
                  [type]="false"
                  [disabled]="
                    controls['Opacity'].invalid || controls['Overkill'].invalid
                  "
                  (click)="postDisplacementMapConfiguration()"
                ></app-button>
              </div>
            </div>
            <!-- Alertas -->
            <div
              class="col-md-12 mt-3 alert"
              [ngClass]="message.class"
              role="alert"
              *ngIf="message.status"
            >
              {{ message.text }}
            </div>
            <app-alert
              class="mt-2"
              [class]="'alert-danger'"
              [messages]="messagesError"
            ></app-alert>
          </div>
        </div>
        <!-- Mapa -->
        <div class="row mt-2">
          <div class="col-md-12 maps">
            <app-google-maps
              [id]="'mapInstruments'"
              #mapInstruments
            ></app-google-maps>
          </div>
        </div>
        <!-- Botão Voltar -->
        <div class="row mt-3">
          <div
            class="col-md-12 mb-3 d-flex align-items-end justify-content-end"
          >
            <app-button
              [class]="'btn-logisoil-blue'"
              [icon]="'fa fa-arrow-left'"
              [label]="'Voltar à tela principal'"
              [routerLink]="['/instruments']"
            ></app-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
