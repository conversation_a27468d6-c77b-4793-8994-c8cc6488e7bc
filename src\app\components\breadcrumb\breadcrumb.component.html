<!-- <ul class="breadcrumb-group" *ngIf="titulo.titulo !== 'Home'"> -->
<ul class="breadcrumb-group" *ngIf="titulo.titulo !== 'Home'">
  <li class="breadcrumb-li">
    <a [routerLink]="'/'">
      <img src="/assets/ico/ico-menu/home.svg" />
      <span class="links_name">Home</span>
    </a>
  </li>
  <li class="breadcrumb-li">
    <a
      [routerLink]="titulo.tituloRouterLink"
      [queryParams]="titulo.tituloQueryParams"
    >
      <i [ngClass]="titulo.icone" *ngIf="titulo.tipoIcone !== 'svg'"></i>
      <img
        src="/assets/ico/ico-menu/{{ titulo.icone }}"
        *ngIf="titulo.tipoIcone === 'svg'"
      />
      <span class="links_name">{{ titulo.titulo }}</span>
    </a>
  </li>
  <li class="breadcrumb-li" *ngIf="titulo.acao !== ''">
    <a
      [routerLink]="titulo.acaoRouterLink"
      [queryParams]="
        titulo.hasOwnProperty('acaoQueryParams') ? titulo.acaoQueryParams : ''
      "
    >
      <i [ngClass]="titulo.acaoIcone"></i>
      <span class="links_name">{{ titulo.acao }}</span>
    </a>
  </li>
  <ng-container *ngFor="let subTitulo of titulo.subTitulos; let i = index">
    <li class="breadcrumb-li">
      <a [routerLink]="titulo.subTituloRouterLink">
        <i
          [ngClass]="subTitulo.icone"
          *ngIf="subTitulo.tipoIcone !== 'svg'"
        ></i>
        <img
          src="/assets/ico/ico-menu/{{ subTitulo.icone }}"
          *ngIf="subTitulo.tipoIcone === 'svg'"
        />
        <span class="links_name">{{ subTitulo.titulo }}</span>
      </a>
    </li>
  </ng-container>
  <li class="breadcrumb-li last-item">
    <a (click)="initTour()" style="cursor: pointer">
      <i [ngClass]="'fa fa-question-circle-o'"></i>
      <span class="links_name">Iniciar Tutorial</span>
    </a>
  </li>
</ul>
