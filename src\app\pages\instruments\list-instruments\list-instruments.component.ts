import { Component, ElementRef, Inject, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { DOCUMENT } from '@angular/common';

import { coordinateFormat, Datum, MultiSelectDefault } from 'src/app/constants/app.constants';
import { MessagePadroes } from 'src/app/constants/message.constants';
import { Automated, groupInstruments, OnLine, Subtypes, typeInstruments } from 'src/app/constants/instruments.constants';

import { DataService } from 'src/app/services/data.service';
import { FilterService } from 'src/app/services/filter.service';
import { MenuService } from 'src/app/services/menu.service';
import { SharedService } from 'src/app/services/shared.service';
import { UserService } from 'src/app/services/user.service';

import { ClientUnitService as ClientUnitServiceApi } from 'src/app/services/api/clientUnit.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';
import { ImagesService as ImagesServiceApi } from 'src/app/services/api/image.service';

import { GoogleMapsComponent } from '@components/google-maps/google-maps.component';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import { format } from 'date-fns';
import { NgxSpinnerService } from 'ngx-spinner';

import { NotificationService } from 'src/app/services/notification.service';

@Component({
  selector: 'app-list-instruments',
  templateUrl: './list-instruments.component.html',
  styleUrls: ['./list-instruments.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListInstrumentsComponent implements OnInit {
  @ViewChild('mapInstruments', { static: false }) mapInstruments: GoogleMapsComponent;
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalInstrument') ModalInstrument: any;
  @ViewChild('modalDownloadListInstrument') ModalDownloadListInstrument: any;
  @ViewChild('modalInsertInstrumentBySpreadsheet') ModalInsertInstrumentBySpreadsheet: any;
  @ViewChild('modalChart') ModalChart: any;
  @ViewChild('modalComponents') ModalComponents: any;

  public dropdownInstrument = false;
  public groupInstruments: any = groupInstruments;
  public menu: any = [];

  public tableHeader: any = [
    {
      label: 'ID',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Nome',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['identifier']
    },
    {
      label: 'Ponto de medição',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['measurement']
    },
    {
      label: 'Tipo',
      width: '140px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['type_name']
    },
    {
      label: 'Δabs(m)',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['absolute_variation'],
      type: 'event',
      sort: true,
      menu: true
    },
    {
      label: 'Online?',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['online_name']
    },
    {
      label: 'Estrutura',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['structure_name']
    },
    {
      label: 'Data da instalação',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['installation_date_format']
    },
    {
      label: 'Automatizado?',
      width: '100px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['automated_name']
    },
    {
      label: 'Coordenadas',
      width: '20%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['coordinates']
    },
    {
      label: 'Ações',
      width: '60px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['miniDashboard']
    }
  ];

  public tableData: any = [];
  public selectedColumns = this.tableHeader;

  public automated: any = Automated;
  public coordinateFormat: any = coordinateFormat;
  public datum: any = Datum;
  public onLine: any = OnLine;
  public typeInstruments: any = typeInstruments;
  public subTypes: any = [];

  public client: any = {
    id: null,
    name: null,
    active: null
  };

  public message: any = [{ text: '', status: false }];
  public messageReturn: any = [{ text: '', status: false }];
  public messageSection: any = [{ text: '', status: false }];

  public filterParams: any = {};
  public filter: any = {
    SearchIdentifier: '',
    Identifier: '',
    Type: '',
    Subtype: '',
    Online: '',
    Datum: 1,
    Automated: '',
    InstallationDate: '',
    CoordinateFormat: 1,
    SubtypeMap: '',
    SectionId: '',
    VariationPeriodDays: null,
    OrderBy: 2,
    Period: ''
  };

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public multipleSettings = MultiSelectDefault.Multiple;
  public viewSettings = MultiSelectDefault.View;

  public showMaps: boolean = false;

  public dataMapsStructure = {
    showPin: true,
    showSectionInfoWindow: false,
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  public listTypeInstruments: any = [];
  public instruments: any = [];

  public document: any = null;
  public intervalId: any = null;
  public clickEventsubscription: Subscription;

  public instrumentEvidence: any = null;
  public selectedSubtype: any = [];
  public selectedInstrument: any = null;
  public selectedSection: any = null;
  public selectedStructureId: string = '';

  public showSections = false;
  public sections: any = [];

  public func = fn;

  public groupInstrumentsList: any = [];

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public filterSearch: any = {};
  public filterSearchBody: any = {};
  public instrumentInfo: any = null;
  public titleModalChart: string = '';
  public configModal: any = null;
  public titleModal: string = '';

  public bannerNotifications: any = [];
  public showNotificationBanner: boolean = true;

  public showPin: boolean = true;
  public showSectionInfoWindow: boolean = false;

  can(action: string): boolean {
    return !!this.permissaoUsuario?.[action];
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private clientUnitServiceApi: ClientUnitServiceApi,
    private router: Router,
    private dataService: DataService,
    private element: ElementRef,
    private filterService: FilterService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private menuService: MenuService,
    private sharedService: SharedService,
    private sectionsServiceApi: SectionsServiceApi,
    private structuresServiceApi: StructuresServiceApi,
    private userService: UserService,
    private ngxSpinnerService: NgxSpinnerService,
    private imagesServiceApi: ImagesServiceApi,
    private notificationService: NotificationService,

    @Inject(DOCUMENT) documentDom: Document
  ) {
    this.document = documentDom;
    this.clickEventsubscription = this.sharedService.getClickEvent().subscribe((instrument) => {
      this.eventMap(instrument);
    });
  }

  /**
   * Método de inicialização do componente.
   * Carrega o perfil do usuário, permissões e configura o serviço de compartilhamento.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;
    this.sharedService.clear();
    this.menu = this.menuService.setMenu('menuInstrumentacao');
    this.subTypes = fn.enumToArraySimple(Subtypes);

    let queryParams = this.activatedRoute.snapshot.queryParams;

    if (queryParams.structure_id && queryParams.structure_id != '') {
      this.callFromStructure(queryParams.structure_id);
    } else {
      this.ngxSpinnerService.show();
    }

    this.notificationService.notificationsBanner$.subscribe(({ instrumentationNotifications }) => {
      this.bannerNotifications = instrumentationNotifications;
    });

    this.notificationService.bannerVisibility$.subscribe(({ instrumentationBannerStatus }) => {
      this.showNotificationBanner = instrumentationBannerStatus;
    });

    if (queryParams.showPin) {
      this.showPin = queryParams.showPin == 'false' ? false : true;
    }

    if (queryParams.showSectionInfoWindow) {
      this.showSectionInfoWindow = queryParams.showSectionInfoWindow == 'false' ? false : true;
    }

    this.dataMapsStructure.showPin = this.showPin;
    this.dataMapsStructure.showSectionInfoWindow = this.showSectionInfoWindow;
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros.
   */
  ngAfterViewInit(): void {
    this.ngxSpinnerService.show();

    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
      this.ngxSpinnerService.hide(); // Oculta o spinner após os dados serem carregados
    }, 1000);
  }

  /**
   * Método chamado quando o usuário clica fora de um determinado elemento.
   * Oculta o dropdown de instrumentos se o elemento clicado for 'instrument'.
   * @param {string} element - Nome do elemento clicado.
   */
  onClickedOutside(element: string) {
    switch (element) {
      case 'instrument':
        this.dropdownInstrument = false;
        break;
    }
  }

  /**
   * Abre o modal correspondente à ação especificada.
   * @param {string} action - Ação que define qual modal será aberto.
   */
  openModal(action: string) {
    switch (action) {
      case 'selectInstrument':
        this.ModalInstrument.openModal();
        break;
      case 'downloadInformations':
        this.ModalDownloadListInstrument.openModal();
        break;
      case 'insertInstrumentBySpreadsheet':
        this.ModalInsertInstrumentBySpreadsheet.openModal();
        break;
      case 'variationAbsolute':
        this.ModalChart.openModal();
        break;
    }
  }

  /**
   * Obtém as seções do mapa com base no ID da estrutura e na ação especificada.
   * Se a ação for 'select', faz a chamada à API para buscar as seções e as plota no mapa.
   * @param {string} structureId - ID da estrutura.
   * @param {string} [action='select'] - Ação que determina o comportamento da busca.
   */
  getSectionsMap(structureId, action: string = 'select') {
    this.messageSection.status = false;
    this.sections = [];
    this.filter.SectionId = '';

    if (action === 'select') {
      this.sectionsServiceApi.postSectionMaps({ structure_ids: [structureId] }).subscribe((resp) => {
        let dados: any = resp;

        if (fn.isEmpty(dados)) {
          this.showSections = false;
          this.messageSection.text = MessagePadroes.NoSection;
          this.messageSection.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.messageSection.status = false;
          }, 4000);
        } else {
          dados = fn.isEmpty(dados) ? [] : dados.body === undefined ? dados : dados.body;
          this.sections = dados;
          this.plotSections(this.sections);
        }
      });
    }
  }

  /**
   * Obtém a lista de instrumentos com base nos parâmetros fornecidos e uma opção de controle.
   * Se bem-sucedido, formata os dados para exibição na tabela. Se não houver registros, exibe uma mensagem.
   * @param {any} params - Parâmetros de filtro para a busca.
   * @param {string} [option=''] - Opção de controle que altera o comportamento do método.
   */
  getInstrumentsList(params, option = '') {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.instrumentsServiceApi.getInstrumentsSearch(params).subscribe(
      (resp) => {
        let dados: any = resp;

        if (dados.status == 200) {
          this.tableData = dados.body.data ? dados.body.data : [];
          this.collectionSize = dados.body.total_items_count;

          this.formatData();
          if (option == 'refresh') {
            this.selectedInstrument = null;
            this.selectedSection = null;
            this.loadInstrument(this.instrumentEvidence.id);
          }
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status === 500) {
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  /**
   * Realiza a pesquisa de instrumentos com base nos filtros fornecidos e na opção selecionada.
   * Configura os parâmetros de filtro e chama a função de busca de instrumentos.
   * @param {string} [option=''] - Opção que define como a busca será realizada.
   * @param {string} [search=''] - Termo de busca para o instrumento.
   */
  searchInstrument(option = '', search: string = '') {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterParams = {
      SearchIdentifier: this.filter.SearchIdentifier === null ? '' : this.filter.SearchIdentifier,
      Identifier: this.filter.Identifier === null ? '' : this.filter.Identifier,
      Type: this.filter.Type === null ? '' : this.filter.Type,
      Subtype: this.filter.Subtype === null ? '' : this.filter.Subtype,
      Online: this.filter.Online === null ? '' : this.filter.Online,
      Datum: this.filter.Datum === null ? '' : this.filter.Datum,
      Automated: this.filter.Automated === null ? '' : this.filter.Automated,
      ClientId: filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '',
      ClientUnitId: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '',
      StructureId: filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].id : '',
      InstallationDate: this.filter.InstallationDate != '' ? moment(this.filter.InstallationDate).format('YYYY-MM-DD') : '',
      SectionId: this.filter.SectionId === null ? '' : this.filter.SectionId,
      CoordinateFormat: this.filter.CoordinateFormat === null ? '' : this.filter.CoordinateFormat,
      Option: option,
      //Variação absoluta
      VariationPeriodDays: this.filter.VariationPeriodDays,
      OrderBy: this.filter.OrderBy === null ? 2 : this.filter.OrderBy
    };

    if (fn.isEmpty(this.filterParams.VariationPeriodDays)) {
      delete this.filterParams.VariationPeriodDays;
    }

    this.selectedInstrument = null;
    this.selectedSection = null;

    if (option == 'breakapart') {
      this.filterSearch = {
        SearchIdentifier: search,
        Page: 1,
        PageSize: this.pageSize,
        OutputDatum: this.filter.Datum
      };
    } else if (option == 'breakapartSection') {
      this.filterSearch = {
        SectionId: search,
        Page: 1,
        PageSize: this.pageSize,
        OutputDatum: this.filter.Datum
      };
    } else {
      this.filterSearch = {
        ...this.filterParams,
        Page: this.page,
        PageSize: this.pageSize,
        OutputDatum: this.filter.Datum
      };
    }

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.getInstrumentsList(this.filterSearch, option);
  }

  /**
   * Formata os dados de instrumentos para exibição na tabela, ajustando os nomes de tipo, medição, variação absoluta,
   * coordenadas e outras propriedades, conforme necessário.
   */
  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      let itemInstrument = item;
      let idx = fn.findIndexInArrayofObject(this.typeInstruments, 'id', item.type);

      itemInstrument['type_name'] = this.typeInstruments[idx].name;
      itemInstrument['measurement'] = item.measurement == null ? '-' : item.measurement.identifier;
      itemInstrument['absolute_variation'] = item.absolute_variation == null ? '-' : item.absolute_variation.toFixed(2);
      itemInstrument['absolute_variation_color'] =
        item.absolute_variation == '-' ? '' : item.absolute_variation > 0 ? '#34b575' : item.absolute_variation < 0 ? 'red' : 'orange';
      itemInstrument['online_name'] = item.online ? 'Sim' : 'Não';
      itemInstrument['structure_name'] = item.structure.name;
      itemInstrument['installation_date_format'] = moment(item.installation_date).format('DD/MM/YYYY');
      itemInstrument['automated_name'] = item.automated ? 'Sim' : 'Não';
      itemInstrument['configMiniDashboard'] = { chart: [9].includes(parseInt(item.type)) ? false : true };

      if (this.filter.CoordinateFormat == 1) {
        //Geodetic
        itemInstrument['coordinates'] =
          item.coordinate_setting.coordinate_systems.decimal_geodetic.latitude + ',' + item.coordinate_setting.coordinate_systems.decimal_geodetic.longitude;
      } else {
        //UTM
        itemInstrument['coordinates'] =
          item.coordinate_setting.coordinate_systems.utm.zone_number +
          ', ' +
          item.coordinate_setting.coordinate_systems.utm.zone_letter +
          ', N ' +
          item.coordinate_setting.coordinate_systems.utm.northing.toFixed(2) +
          ', E ' +
          item.coordinate_setting.coordinate_systems.utm.easting.toFixed(2);
      }
      return itemInstrument;
    });
  }

  /**
   * Redefine os filtros aplicados ao sistema e reinicializa o estado dos filtros e seleções.
   * Remove os valores aplicados e chama o serviço para reiniciar a filtragem.
   */
  resetFilter() {
    this.hierarchy.resetFilters();

    this.filter = {
      SearchIdentifier: '',
      Identifier: '',
      Type: '',
      Subtype: '',
      Online: '',
      Datum: '',
      Automated: '',
      InstallationDate: '',
      CoordinateFormat: '',
      SectionId: '',
      //Variação Absoluta
      VariationPeriodDays: null,
      OrderBy: 2,
      Period: ''
    };

    this.filterParams = {};
    this.filterSearch = {};

    this.selectedInstrument = null;
    this.selectedSection = null;

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.managerFilters();
  }

  /**
   * Gerencia os filtros aplicados. Se $btn for verdadeiro, executa uma nova busca, caso contrário,
   * recupera os filtros aplicados e refina a busca com base neles.
   * @param {boolean} [$btn=false] - Indica se a função foi chamada a partir de um botão de busca.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchInstrument();
    } else {
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchInstrument();
      } else {
        this.filterSearch = data.filters;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;

        //Formulario do filtro
        this.filter.Identifier = this.filterSearch.Identifier !== undefined ? this.filterSearch.Identifier : '';
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier !== undefined ? this.filterSearch.SearchIdentifier : '';
        this.filter.Type = this.filterSearch.Type !== undefined ? this.filterSearch.Type : '';
        this.filter.Subtype = this.filterSearch.Subtype !== undefined ? this.filterSearch.Subtype : '';
        this.filter.Online = this.filterSearch.Online !== undefined ? this.filterSearch.Online : '';
        this.filter.Datum = this.filterSearch.Datum !== undefined ? this.filterSearch.Datum : 1;
        this.filter.Automated = this.filterSearch.Automated !== undefined ? this.filterSearch.Automated : '';
        this.filter.InstallationDate = this.filterSearch.InstallationDate !== undefined ? this.filterSearch.InstallationDate : '';
        this.filter.CoordinateFormat = this.filterSearch.CoordinateFormat !== undefined ? this.filterSearch.CoordinateFormat : 1;
        this.filter.SubtypeMap = this.filterSearch.SubtypeMap !== undefined ? this.filterSearch.SubtypeMap : '';
        this.filter.SectionId = this.filterSearch.SectionId !== undefined ? this.filterSearch.SectionId : '';
        //Variação Absoluta
        this.filter.VariationPeriodDays = this.filterSearch.VariationPeriodDays !== undefined ? this.filterSearch.VariationPeriodDays : null;
        this.filter.OrderBy = this.filterSearch.OrderBy !== undefined ? this.filterSearch.OrderBy : 2;
        this.filter.Period = this.filterSearch.Period !== undefined ? this.filterSearch.Period : '';

        this.searchInstrument(this.filterSearch);
      }

      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
        this.hierarchy.setUnits(data.filtersHierarchy.units);
        this.hierarchy.setStructures(data.filtersHierarchy.structures);
      }
    }
  }

  /**
   * Carrega os detalhes de um instrumento selecionado e define a estrutura, unidade do cliente e o cliente
   * para serem utilizados em outras operações.
   * @param {string} instrumentId - ID do instrumento selecionado.
   */
  loadInstrument(instrumentId: string) {
    this.resetMap();

    this.showMaps = true;

    let idx = fn.findIndexInArrayofObject(this.tableData, 'id', instrumentId);

    let instrumentRow = this.tableData[idx];

    this.sharedService.structure = { id: instrumentRow.structure.id, name: instrumentRow.structure.name };

    this.dataService.getClientUnit(instrumentRow.structure.client_unit_id).subscribe((clientUnit) => {
      this.sharedService.clientUnit = { id: clientUnit.id, name: clientUnit.name };
      this.sharedService.client = { id: clientUnit.client.id, name: clientUnit.client.name };
    });

    this.getListTypeInstruments(instrumentRow.structure.id, instrumentId);
  }

  /**
   * Obtém os tipos de instrumentos disponíveis para a estrutura selecionada e popula a lista com esses instrumentos.
   * @param {string} [structureId=''] - ID da estrutura selecionada.
   * @param {string} [instrumentId=''] - ID do instrumento selecionado.
   */
  getListTypeInstruments(structureId: string = '', instrumentId: string = '') {
    this.instruments = [];
    this.selectedStructureId = structureId;
    if (structureId != '') {
      this.instrumentsServiceApi.getGroupInstrumentsMaps({ StructureId: structureId }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.instruments = dados;
        let idx = fn.findIndexInArrayofObject(this.instruments, 'id', instrumentId);
        this.managerListTypeInstruments(this.instruments[idx]);
      });
    }
  }

  /**
   * Gerencia a exibição dos instrumentos no mapa com base no tipo de instrumento selecionado e a evidência do instrumento.
   * @param {any} instrumentEvidence - Informações do instrumento evidenciado.
   */
  managerListTypeInstruments(instrumentEvidence: any = null) {
    this.listTypeInstruments = this.groupInstruments.map((groupInstrumentItem: any, index: number) => {
      let item = {};
      item = groupInstrumentItem;
      item['instruments'] = [];
      item['item'] = index;
      return item;
    });

    if (!fn.isEmpty(this.instruments)) {
      this.instruments.map((instrumentItem) => {
        let idx = fn.findIndexInArrayofObject(this.listTypeInstruments, 'type', instrumentItem.type);
        this.listTypeInstruments[idx].instruments.push(instrumentItem);
      });
      this.intervalId = setInterval(() => {
        this.plotInstruments(this.listTypeInstruments, instrumentEvidence);
      }, 200);
    }
  }

  /**
   * Modifica a seleção de subtipos de instrumentos no mapa de acordo com a ação realizada.
   * @param {any} subtypes - Subtipos selecionados.
   * @param {string} action - Ação realizada, como 'select', 'selectAll', 'deselect', etc.
   */
  changeSubtypes(subtypes: any, action: string) {
    switch (action) {
      case 'select':
        subtypes = [subtypes];
        break;
      case 'selectAll':
        break;
      case 'deselect':
        subtypes = [];
        break;
      case 'deselectAll':
        subtypes = [];
        break;
    }

    this.resetMap();

    this.selectedSubtype = subtypes.map((subtype) => {
      return parseInt(subtype.id);
    });

    this.intervalId = setInterval(() => {
      this.plotInstruments(this.listTypeInstruments, this.instrumentEvidence, this.selectedSubtype);
    }, 200);
  }

  /**
   * Obtém as informações para download com base nos filtros e configurações aplicadas e aciona o serviço
   * de download do arquivo.
   */
  getDownloadInformations() {
    let formDownloadInformation = this.ModalDownloadListInstrument.formDownloadInformation;
    let params = {};

    if (this.selectedInstrument == null && this.selectedSection == null) {
      let filterHierarchy = this.hierarchy.getFilters();
      params = {
        SearchIdentifier: this.filter.SearchIdentifier,
        Identifier: this.filter.Identifier,
        Online: this.filter.Online,
        Automated: this.filter.Automated,
        ClientUnitId: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '',
        StructureId: filterHierarchy.structures != '' ? filterHierarchy.structures[0].id : '',
        InstallationDate: this.filter.InstallationDate != '' ? moment(this.filter.InstallationDate).format('YYYY-MM-DD') : '',
        FileFormat: formDownloadInformation.controls['file_format'].value[0].value,
        SectionId: this.filter.SectionId
      };
    }

    let ctrlDownload = false;

    formDownloadInformation.controls['type_instrument'].value.forEach((typeInstrument) => {
      let idx = fn.findIndexInArrayofObject(this.typeInstruments, 'id', typeInstrument.id);

      params['Type'] = typeInstrument.id;
      if (this.selectedInstrument == null && this.selectedSection == null) {
        ctrlDownload = true;
        this.getInstrumentsFileDownload(params, this.typeInstruments[idx]);
      } else if (this.selectedSection != null) {
        params['SectionId'] = this.selectedSection;
        params['Type'] = typeInstrument.id;
        params['FileFormat'] = formDownloadInformation.controls['file_format'].value[0].value;
        this.getInstrumentsFileDownload(params, this.typeInstruments[idx]);
        ctrlDownload = true;
      } else {
        if (this.selectedInstrument.type == typeInstrument.id) {
          params['SearchIdentifier'] = this.selectedInstrument.search_identifier;
          params['Type'] = typeInstrument.id;
          params['FileFormat'] = formDownloadInformation.controls['file_format'].value[0].value;
          this.getInstrumentsFileDownload(params, this.typeInstruments[idx]);
          ctrlDownload = true;
        }
      }
    });
  }

  /**
   * Realiza o download de um template de inserção de instrumentos por planilha de acordo com os parâmetros fornecidos.
   */
  getDownloadTemplate() {
    let formDownloadTemplate = this.ModalInsertInstrumentBySpreadsheet.formInsertInstrumentBySpreadsheet;
    let params = {
      Type: formDownloadTemplate.controls['type_instrument'].value[0].id,
      FileFormat: formDownloadTemplate.controls['file_format'].value[0].value,
      Datum: formDownloadTemplate.controls['datum'].value[0].id,
      StructureId: formDownloadTemplate.controls['structure'].value[0].id,
      Rows: formDownloadTemplate.controls['rows'].value ? formDownloadTemplate.controls['rows'].value : 1
    };

    let idx = fn.findIndexInArrayofObject(this.typeInstruments, 'id', params.Type);

    this.instrumentsServiceApi.getInstrumentsFileDownloadTemplate(params).subscribe(
      (resp) => {
        if (resp['status'] == 200) {
          let extension = resp['body'].type == 'text/csv' ? '.csv' : '.xlsx';
          let fileName = this.typeInstruments[idx].alias + '_template_' + format(new Date(), 'ddMMyyyyHH:mm:ss') + extension;
          this.downloadFile(resp['body'], resp['body'].type, fileName);
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * Exibe os instrumentos no mapa, definindo marcadores para cada instrumento e permitindo interações como a separação
   * e exibição de gráficos ou imagens.
   * @param {any} listTypeInstruments - Lista dos tipos de instrumentos a serem exibidos.
   * @param {any} instrumentEvidence - Instrumento evidenciado.
   * @param {Array} [subtypes=[]] - Lista de subtipos de instrumentos selecionados.
   */
  plotInstruments(listTypeInstruments, instrumentEvidence: any = null, subtypes = []) {
    clearInterval(this.intervalId);
    let idx = -1;
    listTypeInstruments.forEach((element, index) => {
      if (element.instruments.length > 0 && idx == -1) {
        idx = index;
      }
    });
    if (idx == -1) {
      clearInterval(this.intervalId);
    }

    if (instrumentEvidence != null) {
      this.instrumentEvidence = instrumentEvidence;
      this.dataMapsStructure.center.lat = instrumentEvidence.decimal_geodetic_coordinate.latitude;
      this.dataMapsStructure.center.lng = instrumentEvidence.decimal_geodetic_coordinate.longitude;
      this.dataMapsStructure.markers[0].position.lat = instrumentEvidence.decimal_geodetic_coordinate.latitude;
      this.dataMapsStructure.markers[0].position.lng = instrumentEvidence.decimal_geodetic_coordinate.longitude;
    }

    if (!this.element.nativeElement.querySelector('div[title="' + listTypeInstruments[idx].instruments[0].identifier + '"]')) {
      listTypeInstruments.forEach((listTypeInstrumentItem) => {
        listTypeInstrumentItem.instruments.forEach((instrument) => {
          let svgMarker = {
            path: 'M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0',
            fillColor: listTypeInstrumentItem.color,
            fillOpacity: 1.0,
            strokeWeight: 1.5,
            strokeColor: 'white',
            rotation: 0,
            scale: 0.3,
            anchor: new google.maps.Point(0, 0)
          };

          let marker = {
            position: {
              lat: instrument.decimal_geodetic_coordinate.latitude,
              lng: instrument.decimal_geodetic_coordinate.longitude
            },
            title: instrument.identifier,
            options: {},
            icon: svgMarker,
            id: 'mk-' + instrument.identifier,
            zIndex: -999
          };

          let idx = fn.findIndexInArrayofObject(this.tableData, 'id', instrument.id);

          let infoWindowMarker = {
            content: '',
            ariaLabel: instrument.identifier,
            id: instrument.identifier,
            data: instrument,
            classTitle: 'd-flex justify-content-center',
            contentConfig: [
              {
                component: 'app-button',
                attrs: {
                  class: 'btn-logisoil-blue',
                  icon: '',
                  label: 'Separar',
                  type: true,
                  eventClick: true,
                  event: 'breakApartInstrument',
                  id: 'iw-button-' + fn.hashCode(instrument.identifier)
                }
              },
              {
                component: 'app-button',
                attrs: {
                  class: 'btn-logisoil-blue',
                  icon: '',
                  label: 'Ver gráfico',
                  type: true,
                  eventClick: true,
                  event: 'showGraphicInstrument',
                  id: 'iw-button-' + fn.hashCode(instrument.identifier)
                }
              },
              {
                component: 'app-button',
                attrs: {
                  class: 'btn-logisoil-blue',
                  icon: '',
                  label: 'Ver imagens',
                  type: true,
                  eventClick: true,
                  event: 'showImage',
                  id: 'iw-button-' + fn.hashCode(instrument.identifier)
                }
              }
            ],
            classGroup: 'd-flex justifiy-content-around'
          };

          marker['infoWindowMarker'] = infoWindowMarker;

          if (subtypes.length > 0) {
            if (subtypes.includes(listTypeInstrumentItem.subtype)) {
              this.dataMapsStructure.markers.push(marker);
            }
          } else {
            this.dataMapsStructure.markers.push(marker);
          }
        });
      });
      this.sendDataMap('markersMultiple', false);
      if (instrumentEvidence != null) {
        this.mapInstruments.openInfoWindowFromMarkerId(['mk-' + instrumentEvidence.identifier], 'mk-', false);
      }
    } else {
      clearInterval(this.intervalId);
    }
  }

  /**
   * Desenha as seções no mapa utilizando as configurações definidas para exibição, como cor e espessura das linhas.
   * @param {any} sections - Lista de seções a serem plotadas no mapa.
   */
  plotSections(sections) {
    this.dataMapsStructure.polylines = [];
    sections.forEach((section) => {
      let polyline = {
        path: [],
        strokeColor: section.map_line_setting.color,
        strokeOpacity: section.map_line_setting.type == 1 ? 0 : 1,
        strokeWeight: section.map_line_setting.width,
        icons: [
          {
            icon: {
              path: 'M 0,-1 0,1',
              strokeOpacity: section.map_line_setting.type == 1 ? 1 : 0,
              scale: 4,
              strokeWeight: section.map_line_setting.width
            },
            offset: '0',
            repeat: '20px'
          }
        ],
        id: section.name,
        name: section.name
      };
      polyline.path.push({
        lat: section.upstream_decimal_geodetic.latitude,
        lng: section.upstream_decimal_geodetic.longitude
      });
      if (section.midpoint_decimal_geodetic) {
        polyline.path.push({
          lat: section.midpoint_decimal_geodetic.latitude,
          lng: section.midpoint_decimal_geodetic.longitude
        });
      }
      polyline.path.push({
        lat: section.downstream_decimal_geodetic.latitude,
        lng: section.downstream_decimal_geodetic.longitude
      });

      let infoWindowPolyline = {
        content: '',
        ariaLabel: section.name,
        id: section.name,
        data: section,
        contentConfig: [
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-blue',
              icon: '',
              label: 'Separar',
              type: true,
              eventClick: true,
              event: 'breakApartInstrumentsSection',
              id: 'iw-button-polyline' + fn.hashCode(section.name)
            }
          },
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-blue',
              icon: '',
              label: 'Ver seção',
              type: true,
              eventClick: true,
              event: 'viewSection',
              id: 'iw-button-section' + fn.hashCode(section.name)
            }
          }
        ],
        classGroup: 'd-flex justify-content-around'
      };

      polyline['infoWindowPolyline'] = infoWindowPolyline;
      //adiciona a secao para exibir no mapa
      this.dataMapsStructure.polylines.push(polyline);
    });
    this.sendDataMap('polylinesMultiple', false, false);
  }

  /**
   * Alterna a exibição das seções no mapa, ativando ou desativando a exibição de polilinhas.
   */
  managerSections() {
    this.showSections = !this.showSections;
    if (this.showSections) {
      this.getSectionsMap(this.selectedStructureId);
    } else {
      this.sendDataMap('clearPolylinesMultiple', false, false);
    }
  }

  /**
   * Define o nível de zoom para exibição da estrutura no mapa e atualiza o estado do mapa.
   * @param {string} type - Tipo de zoom a ser aplicado (estrutura, instrumento, etc).
   * @param {string} zoom - Nível de zoom a ser aplicado.
   */
  setZoom(type: string, zoom: string): void {
    if (type == 'structure') {
      this.dataMapsStructure.zoom = parseInt(zoom);
      this.sendDataMap('', false);
    }
  }

  /**
   * Envia os dados configurados para o mapa de instrumentos, incluindo marcadores, polilinhas, e centralização.
   * @param {string} option - Opção de configuração a ser aplicada no mapa.
   * @param {boolean} [clear=true] - Indica se o mapa deve ser limpo antes de aplicar novas configurações.
   * @param {boolean} [center=true] - Indica se o mapa deve ser centralizado.
   */
  sendDataMap(option, clear = true, center = true) {
    this.mapInstruments.setDataMap(this.dataMapsStructure, option, clear, center);
  }

  /**
   * Gerencia os eventos acionados no mapa, como exibição de gráficos, separação de instrumentos e visualização de seções.
   * @param {any} $event - Evento acionado no mapa.
   */
  eventMap($event) {
    switch ($event.type) {
      case 'breakApartInstrument':
        this.dataService.getDataById('instrument', ['search_identifier'], $event.data.data.id).subscribe((data) => {
          this.selectedInstrument = data;
          this.searchInstrument('breakapart', data.search_identifier);
        });
        break;
      case 'showGraphicInstrument':
        break;
      case 'viewSection':
        this.router.navigate(['/sections/' + $event.data.id + '/edit']);
        break;
      case 'breakApartInstrumentsSection':
        this.selectedSection = $event.data.data.id;
        this.searchInstrument('breakapartSection', $event.data.data.id);
        break;
      case 'mapZoomChanged':
        this.dataMapsStructure.zoom = parseInt($event.data);
        break;
      case 'mapInstrumentInfoWindow':
        this.managerGroup($event.data, 'add');
        break;
      case 'mapInstrumentInfoWindowClose':
        this.managerGroup($event.data, 'remove');
        break;
      case 'showImage':
        this.getImages($event.data);
        break;
    }
  }

  /**
   * Gerencia a adição ou remoção de instrumentos do grupo de instrumentos baseado na ação selecionada.
   * @param {any} $data - Dados do instrumento a ser adicionado ou removido.
   * @param {string} action - Ação a ser realizada (adicionar ou remover).
   */
  managerGroup($data, action) {
    if (action === 'add') {
      this.groupInstrumentsList.push($data);
    } else {
      let idx = fn.findIndexInArrayofObject(this.groupInstrumentsList, 'id', $data.id);
      this.groupInstrumentsList.splice(idx, 1);
    }
  }

  /**
   * Cria um grupo de instrumentos selecionados e navega para a página de visualização do grupo de instrumentos.
   */
  createGroupInstruments() {
    this.sharedService.groupInstrumentsList = this.groupInstrumentsList;
    this.router.navigate(['instruments/groupInstruments']);
  }

  /**
   * Reseta o mapa de instrumentos, removendo todos os marcadores e polilinhas.
   */
  resetMap() {
    this.mapInstruments.clearMap();

    this.dataMapsStructure.markers = [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ];

    this.selectedSubtype = [];
    this.showSections = false;
  }

  /**
   * Recarrega o mapa de instrumentos aplicando as configurações e filtrando a lista de instrumentos.
   */
  refreshMapInstruments() {
    this.dataMapsStructure.markers = [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ];

    this.getInstrumentsList(this.filterSearch, 'refresh');
  }

  /**
   * Gerencia os eventos de clique em uma linha da tabela, como editar, visualizar ou consultar informações de um instrumento.
   * @param {any} $event - Evento de clique acionado na linha da tabela.
   */
  clickRowEvent($event: any = null) {
    let routerLink = '';

    if ($event.hasOwnProperty('routerLink')) {
      routerLink = $event.routerLink.split('?')[0];
    }

    const actionLabels: any = {
      edit: 'editar',
      history: 'visualizar'
    };

    const phraseEndings: any = {
      edit: 'este Instrumento.',
      history: 'o histórico deste Instrumento.'
    };

    const isRestrictedAction = ['edit', 'history'].includes($event.action);
    const hasPermission = this.can($event.action);

    if (isRestrictedAction && !hasPermission) {
      this.showPermissionAlert(`Você não tem permissão para ${actionLabels[$event.action]} ${phraseEndings[$event.action]}`);
      return;
    }

    switch ($event.action) {
      case 'edit':
        this.router.navigate([routerLink + '/' + $event.id + '/edit']);
        break;
      case 'view':
        this.router.navigate([routerLink + '/' + $event.id + '/view']);
        break;
      case 'history':
        this.router.navigate([routerLink + '/' + $event.id + '/history']);
        break;
      case 'consult':
        this.router.navigate([routerLink + '/' + $event.id + '/consult']);
        break;
      case 'images':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/images']);
        break;
      case 'chart':
        this.instrumentInfo = fn.findIndexInArrayofObject(this.tableData, 'id', $event.id, 'type', true);
        this.router.navigate([routerLink + '/' + $event.id + '/chart'], {
          queryParams: { typeInstrument: this.instrumentInfo.type, structure: this.instrumentInfo.structure.id }
        });
        break;
      case 'map':
        this.loadInstrument($event.id);
        break;
      //Grafico de Variacao Absoluta
      case 'event':
        switch ($event.row.referent[0]) {
          case 'absolute_variation':
            this.instrumentInfo = fn.findIndexInArrayofObject(this.tableData, 'id', $event.id, 'type', true);
            if ([1, 2, 3].includes(this.instrumentInfo.type)) {
              this.titleModalChart = 'Histórico de Leituras do ' + this.instrumentInfo.identifier;
              this.openModal('variationAbsolute');
            }
            break;
        }
        break;
      case 'absolute_variation':
        this.filter.VariationPeriodDays = $event.params.variation_period_days;
        this.filter.OrderBy = $event.params.order_by;
        this.managerFilters(true);
        break;
      case 'reset':
        this.resetFilter();
        break;
      case 'viewMap':
        this.instrumentInfo = fn.findIndexInArrayofObject(this.tableData, 'id', $event.id, 'type', true);

        if ([1, 2, 3, 10].includes(parseInt(this.instrumentInfo.type))) {
          //Percolacao
          this.router.navigate([routerLink + '/' + $event.id + '/map'], {
            queryParams: { typeInstrument: this.instrumentInfo.type, structure: this.instrumentInfo.structure.id, mapType: 'percolation' }
          });
        }

        //Marco Superficial e Prisma - Sprint 65
        if ([/*4, 5,*/ 6, 7 /*, 8*/].includes(parseInt(this.instrumentInfo.type))) {
          //Deslocamento
          this.router.navigate([routerLink + '/' + $event.id + '/map'], {
            queryParams: { typeInstrument: this.instrumentInfo.type, structure: this.instrumentInfo.structure.id, mapType: 'displacement' }
          });
        }

        if ([9].includes(parseInt(this.instrumentInfo.type))) {
          //Microsismica
        }

        if ([12, 13].includes(parseInt(this.instrumentInfo.type))) {
          //Clima
        }
        break;
      default:
        break;
    }
  }

  /**
   * Exibe uma mensagem de alerta na tela por 5 segundos.
   */
  private showPermissionAlert(text: string): void {
    this.message = {
      text,
      status: true,
      class: 'alert-danger'
    };
    setTimeout(() => (this.message.status = false), 5000);
  }

  /**
   * Gerencia os eventos de clique em botões ou ícones da interface, como download de informações e inserção por planilha.
   * @param {any} $event - Evento de clique acionado no componente.
   */
  clickEvent($event: any = null) {
    switch ($event) {
      case 'downloadInformations':
      case 'insertInstrumentBySpreadsheet':
        this.openModal($event);
        break;
      case 'sendDownloadInformations':
        this.getDownloadInformations();
        break;
      case 'downloadTemplate':
        this.getDownloadTemplate();
        break;
      case 'insertBySpreadsheet':
        this.router.navigate(['instruments/insertBySpreadsheet']);
        this.ModalInsertInstrumentBySpreadsheet.closeModal();
        break;
    }
  }

  /**
   * Faz o download do arquivo de instrumentos com base nos parâmetros e tipo de instrumento fornecidos.
   * @param {any} params - Parâmetros de busca e filtros aplicados.
   * @param {any} typeInstruments - Informações do tipo de instrumento para o download.
   */
  getInstrumentsFileDownload(params: any = {}, typeInstruments: any = null) {
    this.ModalDownloadListInstrument.labelAlert = '';
    this.ModalDownloadListInstrument.messagesError = [];

    this.instrumentsServiceApi.getInstrumentsFileDownload(params).subscribe(
      (resp) => {
        if (resp['status'] == 200) {
          let extension = resp['body'].type == 'text/csv' ? '.csv' : '.xlsx';
          let fileName = typeInstruments.alias + '_' + format(new Date(), 'ddMMyyyyHH:mm:ss') + extension;
          this.downloadFile(resp['body'], resp['body'].type, fileName);
        } else if (resp['status'] == 204) {
          this.ModalDownloadListInstrument.labelAlert = 'Conforme filtro, não há registro para:';
          this.ModalDownloadListInstrument.messagesError.push({ message: '- ' + typeInstruments.name });

          setTimeout(() => {
            this.ModalDownloadListInstrument.messagesError = [];
          }, 4000);
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * Realiza o download de um arquivo, criando um link temporário e acionando o download do arquivo.
   * @param {any} data - Dados do arquivo a ser baixado.
   * @param {string} [type='text/csv'] - Tipo do arquivo a ser baixado.
   * @param {string} fileName - Nome do arquivo a ser baixado.
   */
  downloadFile(data: any, type: string = 'text/csv', fileName: string) {
    type = type == 'text/csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

    const blob = new Blob([data], { type: type });
    const url = window.URL.createObjectURL(blob);
    //window.open(url);
    let anchor = document.createElement('a');
    anchor.download = fileName;
    anchor.href = url;
    anchor.click();
  }

  /**
   * Alterna a exibição de colunas da tabela de acordo com o evento de seleção ou deseleção.
   * @param {any} $event - Evento acionado ao selecionar ou deselecionar colunas.
   * @param {string} type - Tipo de ação realizada (selecionar, deselecionar, selecionar todas, etc).
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Obtém informações da estrutura com base no ID fornecido e ajusta a hierarquia de cliente, unidade e estrutura.
   * @param {string} structureId - ID da estrutura.
   */
  callFromStructure(structureId) {
    this.structuresServiceApi.getStructureById(structureId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.clientUnitServiceApi.getClientUnitsById(dados.client_unit.id).subscribe((resp) => {
        let dadosUnit: any = resp;
        dadosUnit = dadosUnit.body === undefined ? dadosUnit : dadosUnit.body;

        this.hierarchy.setClients([{ id: dadosUnit.client.id, name: dadosUnit.client.name }]);
        this.hierarchy.setUnits([{ id: dados.client_unit.id, name: dados.client_unit.name }]);
        this.hierarchy.setStructures([{ id: dados.id, name: dados.name }]);

        this.managerFilters(true);
      });
    });
  }

  /**
   * Obtém as imagens relacionadas ao instrumento selecionado e exibe em um modal.
   * @param {any} instrument - Informações do instrumento selecionado.
   */
  getImages(instrument) {
    let params = {
      Entities: 1,
      'Filters.Instruments': instrument.data.id
    };
    this.imagesServiceApi.getImages(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.titleModal = 'Imagens do instrumento : ' + instrument.data.identifier;
      this.configModal = {
        imagesItens: dados.images,
        message: { text: '', status: false, class: 'alert-success' },
        uploadActive: false
      };
      if (dados.images.length == 0) {
        this.configModal['message'] = { text: MessagePadroes.NoImage, status: true, class: 'alert-warning' };
      }
      this.ModalComponents.openModal();
    });
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }

    this.managerFilters();
  }

  /**
   * Fecha o banner de notificação e aciona o serviço para registrar o fechamento do banner.
   */
  handleCloseNotificationBanner() {
    this.showNotificationBanner = false;
    this.notificationService.handleCloseInstrumentationBanner();
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  /**
   * Navega para a página inicial.
   */
  goBack() {
    this.router.navigate(['/']);
  }
}
