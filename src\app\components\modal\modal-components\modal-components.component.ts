import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-components',
  templateUrl: './modal-components.component.html',
  styleUrls: ['./modal-components.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ModalComponentsComponent implements OnInit {
  @ViewChild('modalComponents') ModalComponentsComponent: ElementRef;

  @Output() public sendClickEvent = new EventEmitter();

  @Input() public config: any = null;
  @Input() public component: any = null;
  @Input() public title: any = '';

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  openModal() {
    this.modalService.open(this.ModalComponentsComponent, { size: 'xl' });
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  clickEvent(action: any = null) {
    this.sendClickEvent.emit(action);
  }
}
