<div class="container" style="position: relative">
  <!-- Div que exibirá a imagem DXF -->
  <div class="position-relative dxf-container">
    <div class="dxf-image">
      <div class="canvasContainer col" [id]="idCanvas"></div>
    </div>

    <!-- Dropdown para a lista de itens -->
    <div class="dropdown position-absolute top-0 end-0 m-2">
      <app-button
        [class]="'btn-logisoil-green dropdown-toggle'"
        [customBtn]="true"
        [label]="'Camadas'"
        (click)="toggleDropdown()"
        aria-expanded="false"
      ></app-button>
      <!-- Adicione a classe dropdown-menu-end -->
      <ul
        class="dropdown-menu dropdown-menu-end mt-1 p-3"
        [class.show]="dropdownOpen"
        style="max-height: 300px; overflow-y: auto; width: 250px"
      >
        <li
          class="d-flex align-items-center mb-2"
          *ngFor="let layer of layers; let i = index"
          :key="layer.name"
          tag="label"
        >
          <input
            class="form-check-input me-2"
            type="checkbox"
            checked
            (input)="_ToggleLayer(layer, $event)"
            [id]="'item_' + i"
          />
          <i
            class="me-2"
            [ngClass]="_GetIconClass(layer.name)"
            [style.color]="_GetCssColor(layer.color)"
          ></i>
          <label class="form-check-label" [for]="'item_' + i">{{
            layer.displayName
          }}</label>
        </li>
      </ul>
    </div>
  </div>
  <ngx-spinner
    bdColor="rgba(51,51,51,0.8)"
    size="medium"
    color="#34b575"
    type="ball-clip-rotate-pulse"
    [fullScreen]="false"
  ></ngx-spinner>
</div>
