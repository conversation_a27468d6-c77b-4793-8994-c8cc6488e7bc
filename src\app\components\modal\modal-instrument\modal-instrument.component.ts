import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';

import { typeInstruments } from 'src/app/constants/instruments.constants';

@Component({
  selector: 'app-modal-instrument',
  templateUrl: './modal-instrument.component.html',
  styleUrls: ['./modal-instrument.component.scss']
})
export class ModalInstrumentComponent implements OnInit {
  @ViewChild('modalInstrument') modalInstrument: ElementRef;

  public typeInstrumentsList: any = typeInstruments;
  public selectedTypeInstrument: any = '';

  public pressureCells: boolean = false;
  public measuringPoints: boolean = false;
  public magneticRings: boolean = false;

  public pressureCellsSelected: boolean = false;
  public measuringPointsSelected: boolean = false;
  public magneticRingsSelected: boolean = false;

  public modalRef: any = NgbModalRef;

  constructor(private modalService: NgbModal, private router: Router) {}

  ngOnInit(): void {}

  openModal() {
    this.modalService.open(this.modalInstrument);
  }

  selectTypeInstrument($event: any) {
    this.selectedTypeInstrument = '';

    if ($event.target.value != '') {
      let typeInstrument = JSON.parse($event.target.value);
      this.pressureCells = typeInstrument.pressureCells;
      this.measuringPoints = typeInstrument.measuringPoints;
      this.magneticRings = typeInstrument.magneticRings;
      this.selectedTypeInstrument = typeInstrument;
    }
  }

  create() {
    if (this.selectedTypeInstrument !== '') {
      this.modalService.dismissAll();
      let params = {
        ...this.selectedTypeInstrument,
        pressureCells: this.pressureCellsSelected,
        measuringPoints: this.measuringPointsSelected,
        magneticRings: this.magneticRingsSelected
      };
      this.router.navigate(['instruments/create'], { queryParams: { ...params } });
    }
  }
}
