import { Injectable } from '@angular/core';
import { EventTypes, OidcSecurityService, PublicEventsService } from 'angular-auth-oidc-client';
import jwt_decode from 'jwt-decode';
import { Observable, map } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private attempt: number = 0;

  constructor(private oidcSecurityService: OidcSecurityService, private eventService: PublicEventsService) {}

  initAuthMonitoring() {
    this.eventService.registerForEvents().subscribe((event) => {
      console.log('[Auth Event]', event);

      if (event.type === EventTypes.TokenExpired || event.type === EventTypes.IdTokenExpired) {
        console.warn('Token expirado ou IdToken expirado. Tentando renovação via checkAuth()...');

        // Tenta renovar manualmente antes de redirecionar para login
        this.oidcSecurityService.checkAuth().subscribe((result: any) => {
          if (result.isAuthenticated) {
            console.log('Token renovado com sucesso após expiração via checkAuth()');
          } else {
            console.warn('Não foi possível renovar. Redirecionando para login...');
            this.oidcSecurityService.authorize();
          }
        });
      }
    });
  }

  checkAuth(): Observable<boolean> {
    return this.oidcSecurityService.checkAuth();
  }

  login() {
    this.oidcSecurityService.authorize();
  }

  logout() {
    sessionStorage.removeItem('videoWatched');
    this.oidcSecurityService.logoff();
  }

  getToken() {
    return this.oidcSecurityService.getToken();
  }

  getLocale() {
    const token = this.oidcSecurityService.getToken();
    const user: any = jwt_decode(token);

    return user.locale;
  }

  isAuthenticated(): Observable<boolean> {
    return this.oidcSecurityService.isAuthenticated$;
  }

  checkAuthenticated() {
    return this.oidcSecurityService.checkAuth().pipe(
      map((res: any) => {
        return res;
      })
    );
  }

  renewToken(): Observable<boolean> {
    return this.checkAuthenticated(); // Reutiliza o método existente para verificar e renovar o token
  }
}
