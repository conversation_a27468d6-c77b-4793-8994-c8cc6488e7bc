<div class="list-content">
  <!-- Cadastrar novo usuário -->
  <div class="button-client">
    <app-button
      tourAnchor="register_user_button"
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Cadastrar novo usuário'"
      [routerLink]="['create']"
    ></app-button>
  </div>
  <!-- Cadastrar novo usuário -->

  <div class="row g-3 mt-1">
    <!-- ID -->
    <div class="col-md-3" tourAnchor="id_filter">
      <label class="form-label">ID</label>
      <input
        [(ngModel)]="filter.SearchIdentifier"
        type="number"
        step="1"
        min="1"
        class="form-control"
        placeholder="ID usuário"
        autocomplete="off"
        (keypress)="
          func.controlNumber(
            $event,
            filter.SearchIdentifier,
            'positive',
            'ngModel'
          )
        "
        (keyup)="
          func.controlNumber($event, filter.SearchIdentifier, null, 'ngModel')
        "
      />
    </div>
    <!-- Usuário -->
    <div class="col-md-3" tourAnchor="user_filter">
      <label class="form-label">Usuário</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="userSettings"
        [data]="users"
        [(ngModel)]="filter.Username"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Status -->
    <div class="col-md-3" tourAnchor="status_dropdown">
      <label class="form-label">Status</label>
      <select class="form-select" [(ngModel)]="filter.Active">
        <option value="">Selecione...</option>
        <option *ngFor="let item of status" [ngValue]="item.value">
          {{ item.status }}
        </option>
      </select>
    </div>
    <!-- Nível de acesso -->
    <div class="col-md-3" tourAnchor="access_level_dropdown">
      <label class="form-label">Nível de acesso</label>
      <select class="form-select" [(ngModel)]="filter.Role">
        <option value="">Selecione</option>
        <ng-container *ngFor="let item of roleUser">
          <option
            [value]="item.id"
            *ngIf="
              accessLevelEnum[item.id].level <=
                (profile.level == 6 ? 7 : profile.level) &&
              (profile.description != 'super-support' && item.id == 'support'
                ? false
                : true)
            "
          >
            {{ item.label }}
          </option>
        </ng-container>
      </select>
    </div>
    <!-- Selects Cliente, Unidade e Estrutura -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-9"
      tourAnchor="client_dropdown"
    ></app-hierarchy>
    <!-- Visualização -->
    <div class="col-md-3" tourAnchor="visualization_dropdown">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Botões -->
    <div class="col-md-12 d-flex align-items-end justify-content-end">
      <app-button
        tourAnchor="search_button"
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        (click)="managerFilters(true)"
        class="me-1"
      ></app-button>
      <app-button
        tourAnchor="reset_button"
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>

    <!-- Alertas -->
    <div class="alert alert-warning" role="alert" *ngIf="messageReturn.status">
      {{ messageReturn.text }}
    </div>
    <div class="alert alert-success" role="alert" *ngIf="message.status">
      {{ message.text }}
    </div>
    <div class="alert alert-warning" role="alert" *ngIf="messageWarning.status">
      {{ messageWarning.text }}
    </div>

    <!-- Tabela -->
    <div class="col-12 mt-3" tourAnchor="table_users">
      <app-table
        *ngIf="tableData.length > 0"
        [messageReturn]="messageReturn"
        [tableHeader]="tableHeader"
        [tableData]="tableData"
        (sendToggleStatus)="toggleStatus($event)"
        (sendEmailPassword)="emailPassword($event)"
        [permissaoUsuario]="permissaoUsuario"
      >
      </app-table>

      <!-- Paginação -->
      <app-paginator
        tourAnchor="paginator"
        *ngIf="tableData.length > 0"
        [collectionSize]="collectionSize"
        [page]="page"
        [maxSize]="10"
        [boundaryLinks]="true"
        [pageSize]="pageSize"
        (sendPageChange)="loadPage($event)"
        [enableItemPerPage]="true"
      ></app-paginator>
    </div>
  </div>

  <!-- Botão Voltar -->
  <div class="col-md-12 mt-2 d-flex align-items-end justify-content-end mb-3">
    <app-button
      tourAnchor="back_button"
      [class]="'btn-logisoil-blue'"
      [label]="'Voltar à tela inicial'"
      [icon]="'fa fa-arrow-left'"
      [click]="goBack.bind(this)"
      class="me-1"
    ></app-button>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>

<tour-step-template></tour-step-template>
