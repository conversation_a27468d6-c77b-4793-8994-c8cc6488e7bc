<ng-container [formGroup]="formSelect">
  <div *ngIf="!ctrlItem" class="d-flex">
    <select class="form-select" formControlName="itemSelect">
      <option value="">Selecione...</option>
      <option *ngFor="let item of selectOptions" [ngValue]="item[idField]">
        {{ item[textField] }}
      </option>
    </select>
    <app-button
      *ngIf="controls['itemSelect'].value == '' && !view"
      [class]="'btn-logisoil-add-item'"
      [icon]="'fa fa-thin fa-plus'"
      class="ms-1"
      title="Adicionar"
      style="margin-top: -7px !important"
      (click)="addItem()"
      [disabled]="!controls['itemSelect'].enabled"
    >
    </app-button>
    <app-button
      *ngIf="controls['itemSelect'].value != '' && !view"
      [class]="'btn-logisoil-edit-item'"
      [icon]="'fa fa-thin fa-pencil'"
      class="ms-1"
      title="Editar"
      style="margin-top: -7px !important"
      (click)="updateItem()"
    >
    </app-button>
  </div>
  <div *ngIf="ctrlItem" class="d-flex">
    <input
      type="text"
      class="form-control"
      formControlName="itemInput"
      [maxlength]="
        options != null && options.hasOwnProperty('maxlength')
          ? options.maxlength
          : ''
      "
    />
    <app-button
      [class]="'btn-logisoil-cancel-item'"
      [icon]="'fa fa-thin fa-xmark'"
      class="ms-1"
      title="Cancelar "
      style="margin-top: -7px !important"
      (click)="cancelItem()"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-add'"
      [icon]="'fa fa-thin fa-check'"
      class="ms-1"
      title="Confirmar "
      style="margin-top: -7px !important"
      (click)="saveItem()"
    >
    </app-button>
  </div>
</ng-container>
