import { Component, Input, OnChanges, SimpleChanges, OnInit, Output, EventEmitter, ElementRef, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import * as moment from 'moment';

@Component({
  selector: 'app-modal-attachments-trail',
  templateUrl: './modal-attachments-trail.component.html',
  styleUrls: ['./modal-attachments-trail.component.scss']
})
export class ModalAttachmentsTrailComponent implements OnInit, OnChanges {
  @ViewChild('modalAttachmentsTrailComponent') modalAttachmentsTrailComponent: ElementRef;

  @Input() attachmentsTrail: any[] = [];
  @Input() occurrenceSearchIdentifier!: number;

  @Output() public sendClickEvent = new EventEmitter();
  @Output() close = new EventEmitter<void>();
  @Output() addToMap = new EventEmitter<number[]>(); // IDs selecionados

  public selected: { [id: number]: boolean } = {};

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Selecionar',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['select'],
      type: 'check',
      configCheck: {
        disabled: 'check_disabled',
        selected: 'check_selected'
      }
    },
    {
      label: 'Ficha',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['inspection_sheet_id']
    },
    {
      label: 'Data',
      width: '30%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Ocorrência',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Qtde. anexos',
      width: '20%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['attachments_count']
    }
  ];

  constructor(private modalService: NgbModal) {}

  ngOnInit(): void {}

  /**
   * Executado sempre que há mudanças nos `@Input`s do componente.
   *
   * Neste caso, verifica se houve mudança em `attachmentsTrail` e,
   * caso haja dados, reinicia a paginação e formata os dados para exibição na tabela.
   *
   * @param {SimpleChanges} changes - Objeto com as mudanças nos inputs.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['attachmentsTrail'] && this.attachmentsTrail?.length > 0) {
      this.collectionSize = this.attachmentsTrail.length;
      this.page = 1;
      this.formatData();
    }
  }

  /**
   * Realiza a paginação e formatação dos dados da trilha de anexos (`attachmentsTrail`)
   * para exibição na tabela.
   *
   * - Define o intervalo de itens com base na página atual e tamanho da página.
   * - Define `check_disabled` com base em `attachments_count`.
   * - Ordena os dados por data de criação decrescente (`_created_date_raw`).
   */
  formatData(): void {
    const start = (this.page - 1) * this.pageSize;
    const end = start + this.pageSize;

    const paginated = this.attachmentsTrail.slice(start, end);

    this.tableData = paginated.map((item) => {
      const canDisabled = item.attachments_count == 0;
      // const isSelected = this.selected[item.search_identifier] || false;

      return {
        ...item,
        check_disabled: canDisabled
      };
    });

    this.tableData = [...this.tableData].sort((a, b) => moment(b._created_date_raw).diff(moment(a._created_date_raw)));
  }

  /**
   * Abre o modal de visualização dos anexos da trilha usando o `modalService`.
   */
  openModal() {
    this.modalService.open(this.modalAttachmentsTrailComponent, { size: 'xl' });
  }

  /**
   * Trata os cliques nas linhas da tabela ou nas checkboxes de seleção.
   *
   * - Se o `action` for `'checkbox'`, alterna a seleção do item.
   * - Impede seleção se `attachments_count` for `0`.
   * - Emite `sendClickEvent` para demais ações.
   *
   * @param $event - Evento do DOM (pode ser `null`).
   * @param params - Parâmetros contendo `action` e `id`.
   */
  clickRowEvent($event: any = null): void {
    if ($event.action === 'checkbox') {
      const item = this.tableData.find((i) => i.occurrence_id === $event?.data?.occurrence_id);
      if (!item) return;

      const canSelect = item.attachments_count > 0;

      if (!canSelect) {
        // Força visualmente o uncheck
        this.selected[item.search_identifier] = false;
        item.select = false;
        setTimeout(() => {
          if ($event?.target) {
            ($event.target as HTMLInputElement).checked = false;
          }
        });
        return;
      }

      const current = this.selected[item.search_identifier] || false;
      const updated = !current;

      this.selected[item.search_identifier] = updated;
      item.select = updated;

      // Força reavaliação da expressão para habilitar botão
      this.tableData = [...this.tableData];

      return;
    }
  }

  /**
   * Retorna `true` se houver pelo menos um item com anexo selecionado.
   *
   * Essa verificação é usada para habilitar ou desabilitar ações em lote.
   */
  get hasValidSelection(): boolean {
    return this.attachmentsTrail.some((item) => item.attachments_count > 0 && this.selected[item.search_identifier]);
  }

  /**
   * Emite o evento `addToMap` com os `search_identifier` dos itens selecionados.
   *
   * Os itens são convertidos para `number` e enviados como array.
   */
  emitAddToMap(): void {
    const selectedOccurrenceIds = this.attachmentsTrail
      .filter((item) => item.attachments_count > 0 && this.selected[item.search_identifier])
      .map((item) => item.search_identifier);
    this.addToMap.emit(selectedOccurrenceIds);
  }
  /**
   * Verifica se há ao menos um item com anexos (> 0).
   * Isso é usado para decidir se o botão "Adicionar no mapa" será exibido.
   */
  get hasAttachmentsToMap(): boolean {
    return this.attachmentsTrail.some((item) => item.attachments_count > 0);
  }

  /**
   * Carrega a página selecionada e realiza a busca de análises de estabilidade.
   * @param {number} selectPage - Página selecionada.
   */
  loadPage(selectPage: number) {
    this.page = selectPage;
    this.formatData();
  }
}
