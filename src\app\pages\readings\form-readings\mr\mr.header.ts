const mrTableHeader = [
  {
    label: 'Selecionar',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['select'],
    type: 'check'
  },
  {
    label: 'ID',
    width: '60px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['reading_search_identifier']
  },
  {
    label: 'Instrumento',
    width: '50%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['identifier']
  },
  {
    label: 'Data e hora',
    width: '50%',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['date_format']
  },
  {
    label: '<PERSON><PERSON>',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['measure']
  },

  {
    label: 'Prof. absoluta (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['absolute_depth']
  },
  {
    label: 'Delta referência (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['delta_ref']
  },
  {
    label: 'Recalque absoluto (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['absolute_settlement']
  },
  {
    label: 'Recalque relativo (mm)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['relative_settlement']
  },
  {
    label: 'Cota (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['quota']
  },
  {
    label: 'Prof. relativa (m)',
    width: '120px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['relative_depth']
  },
  {
    label: 'Ações',
    width: '80px',
    rowspan: '',
    colspan: '',
    show: true,
    referent: ['actionCustom']
  }
];

export { mrTableHeader };
