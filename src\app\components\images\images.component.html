<form class="mt-1 row g-3" [formGroup]="formImages">
  <div *ngIf="!showCarousel">
    <div *ngIf="uploadActive">
      <!-- Instruções -->
      <div class="col-md-12">
        <span class="fw-bold text-instructions">
          <i class="fa fa-exclamation-circle me-2" aria-hidden="true"></i
          >Instruções:
        </span>
        <br />
        <span>
          <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
          Máximo de imagens: {{ maxFiles }}.
        </span>
        <br />
        <span>
          <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
          Tamanho máximo permitido por imagem: {{ maxFileSize / 1000000 }} MB.
        </span>
        <br />
        <span>
          <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
          Formatos de arquivo válidos: .png, .jpg, .jpeg, .bmp, .gif
        </span>
      </div>

      <!-- Input para selecionar a imagem -->
      <div class="mt-2 row">
        <div class="col-md-6">
          <input
            type="file"
            formControlName="image"
            multiple
            class="form-control"
            accept=".png,.jpg, .jpeg, .gif, .bmp"
            (change)="uploadFile($event)"
            #fileInput
            [disabled]="images.length < maxFiles"
          />

          <small
            class="form-text text-danger"
            *ngIf="
              !formImages.get('image').valid && formImages.get('image').touched
            "
            ><i class="bi bi-x-circle me-2"></i>Formato de arquivo
            inválido.</small
          >
          <small class="form-text text-danger" *ngIf="!limitFileSize"
            ><i class="bi bi-x-circle me-2"></i>Excedido o tamanho máximo
            permitido de {{ maxFileSize / 1000000 }} MB por imagem.<br
          /></small>
          <small class="form-text text-danger" *ngIf="!limitsFiles"
            ><i class="bi bi-x-circle me-2"></i>Excedido o número máximo
            permitido de uploads.
          </small>
        </div>
      </div>
    </div>

    <!-- Alerta -->
    <app-alert
      [class]="'col-md-12 mt-2 alert-danger'"
      [messages]="messagesError"
    ></app-alert>
    <div
      class="col-md-12 mt-2 alert"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
    >
      {{ message.text }}
    </div>

    <div class="mt-2 row" *ngIf="images.length > 0">
      <div formArrayName="description">
        <div class="card" *ngIf="showImages">
          <div class="card-header">Galeria</div>
          <div class="card-body">
            <!-- Tela de Estruturas e Tela de Instrumentos -->
            <div class="row" *ngIf="uploadActive">
              <div
                class="col-lg-4 col-md-8 col-xs-12"
                *ngFor="let image of images; let i = index"
              >
                <div class="img-thumbnail mb-2 mt-2">
                  <a class="thumbnail" (click)="showCarouselImage(image.index)">
                    <img
                      class="img-item"
                      [src]="image.path"
                      alt="{{ image.description }}"
                    />
                  </a>
                  <div class="image-details">
                    <div class="col-md-12">
                      <label class="form-label me-2"
                        >Descrição (opcional):</label
                      >
                      <input
                        type="text"
                        class="form-control"
                        maxlength="50"
                        [formControlName]="image.index"
                        data-ls-module="charCounter"
                        (input)="onValueChange($event, i)"
                        (blur)="onBlur($event, i)"
                      />
                      <small class="form-text text-secondary"
                        >Caracteres {{ image.counter }}
                      </small>
                    </div>
                    <div
                      class="mt-2 mb-2 col-md-12 d-flex justify-content-center"
                    >
                      <a
                        [download]="image.fileName"
                        [href]="image.fileContentDownload"
                        class="me-2"
                      >
                        <app-button
                          [class]="'btn btn-logisoil-blue'"
                          [icon]="'fa fa-download'"
                          [type]="true"
                          data-bs-toggle="tooltip"
                          data-bs-placement="bottom"
                          title="Download"
                          class="me-2"
                        >
                        </app-button>
                      </a>
                      <app-button
                        *ngIf="uploadActive"
                        [class]="'btn-logisoil-red'"
                        [icon]="'fa fa-times'"
                        [type]="true"
                        data-bs-toggle="tooltip"
                        data-bs-placement="bottom"
                        title="Excluir imagem"
                        (click)="removeImage(i)"
                      >
                      </app-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- Tela de Imagens -->
            <div class="row" *ngIf="!uploadActive">
              <div
                class="col-lg-4 col-md-8 col-xs-12"
                *ngFor="let image of images; let i = index"
              >
                <div class="img-thumbnail mb-2 mt-2">
                  <div class="image-details">
                    <p class="text-center">
                      {{ image.description || image.fileName }}
                    </p>
                  </div>
                  <a class="thumbnail" (click)="showCarouselImage(image.index)">
                    <img
                      class="img-item"
                      [src]="image.path"
                      alt="{{ image.description }}"
                    />
                  </a>
                  <div class="image-details">
                    <div class="col-md-12 d-flex justify-content-start mt-1">
                      Usuário: {{ image.user }}
                    </div>
                    <div class="col-md-12 d-flex justify-content-start mt-1">
                      Data: {{ image.date }}
                    </div>
                    <!-- Botão Download -->
                    <div class="col-md-12 d-flex justify-content-start mt-1">
                      <a
                        [download]="image.fileName"
                        [href]="image.fileContentDownload"
                        class="me-2"
                      >
                        <app-button
                          [class]="'btn btn-logisoil-blue'"
                          [icon]="'fa fa-download'"
                          [type]="true"
                          data-bs-toggle="tooltip"
                          data-bs-placement="bottom"
                          title="Download"
                          class="me-2"
                        >
                        </app-button>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>

<!--Carousel-->
<app-carousel
  [images]="images"
  [showCarousel]="showCarousel"
  [selectedImage]="selectedImage"
  (sendClose)="closeCarousel()"
></app-carousel>

<!-- Carousel-->

<!-- Confirmar exclusao -->
<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
  [data]="modalData"
  [id]="modalId"
></app-modal-confirm>
