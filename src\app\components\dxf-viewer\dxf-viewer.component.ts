import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { DxfViewer } from 'dxf-viewer';
import * as Three from 'three';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-dxf-viewer',
  templateUrl: './dxf-viewer.component.html',
  styleUrls: ['./dxf-viewer.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DxfViewerComponent implements OnInit, OnChanges {
  @Input() public fileDxf: any = null;
  @Input() public show: boolean = false;
  @Input() public idCanvas: string = 'canvasContainer';

  public layers = null;
  public dxfViewer = null;

  constructor(private sanitizer: DomSanitizer) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.fileDxf.currentValue != null) {
      this.show = true;
      setTimeout(() => {
        this.handleFileInput(this.fileDxf);
      }, 50);
    } else {
      this.show = false;
    }
  }

  _ToggleLayer(layer, $event) {
    this.dxfViewer.ShowLayer(layer.name, $event.target.checked);
  }

  _GetCssColor(value) {
    let s = value.toString(16);
    while (s.length < 6) {
      s = '0' + s;
    }
    return '#' + s;
  }

  _GetIconClass(layerName: string): string {
    if (layerName && (layerName.toLowerCase() === 'external' || layerName.toLowerCase() === 'material')) {
      return 'fa-solid fa-draw-polygon';
    }

    return 'fa-solid fa-square';
  }

  async handleFileInput(file) {
    const canvasRef = document.getElementById(this.idCanvas);

    const dxfViewer = new DxfViewer(canvasRef, {
      canvasWidth: 500,
      canvasHeight: 500,
      autoResize: true,
      clearColor: new Three.Color('#fff'),
      clearAlpha: 1,
      canvasAlpha: true,
      canvasPremultipliedAlpha: true,
      antialias: true,
      colorCorrection: true,
      blackWhiteInversion: true,
      pointSize: 2,
      sceneOptions: {
        arcTessellationAngle: (10 / 180) * Math.PI,
        minArcTessellationSubdivisions: 8,
        wireframeMesh: false,
        suppressPaperSpace: false,
        textOptions: {
          curveSubdivision: 2,
          fallbackChar: '\uFFFD?'
        }
      },
      retainParsedDxf: true,
      preserveDrawingBuffer: true,
      fileEncoding: 'utf-8'
    });

    await dxfViewer.Load({
      url: URL.createObjectURL(file),
      fonts: ['OpenSans-Regular.ttf', 'Roboto-LightItalic.ttf'],
      progressCbk: (phase, size, total) => {},
      workerFactory: null
    });

    const layersIterable = dxfViewer.GetLayers();
    const layersArray = Array.from(layersIterable);

    const filteredLayers = layersArray.filter((layer) => layer.name !== '0');
    const sortedLayers = filteredLayers.sort((a, b) => {
      const isInteger = (name) => /^\d+$/.test(name);
      const getPriority = (layer) => {
        if (layer.name.startsWith(' ') || isInteger(layer.name)) return 1;
        if (layer.name.toLowerCase() === 'external') return 2;
        if (layer.name.toLowerCase() === 'material') return 3;
        return 4;
      };

      const priorityA = getPriority(a);
      const priorityB = getPriority(b);

      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      if (priorityA === 1 && priorityB === 1) {
        const isANumber = !isNaN(Number(a.name));
        const isBNumber = !isNaN(Number(b.name));

        if (isANumber && isBNumber) {
          return Number(a.name) - Number(b.name);
        } else if (isANumber) {
          return -1;
        } else if (isBNumber) {
          return 1;
        }
      }

      return a.name.localeCompare(b.name);
    });

    this.layers = sortedLayers;
    this.dxfViewer = dxfViewer;
  }
}
