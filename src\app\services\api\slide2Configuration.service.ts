import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class Slide2ConfigurationService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  // Aba Configurações de Análise
  postSlide2Configuration(params: any) {
    const url = '/slide2-configurations';
    return this.api.post<any>(url, params, {}, 'client');
  }

  getSlide2Configuration(params: any) {
    const url = '/slide2-configurations/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getSlide2ConfigurationList(params: any = {}) {
    const url = '/slide2-configurations';
    return this.api.get<any>(url, params, false, 'client');
  }

  getSlide2ConfigurationById(id: string) {
    const url = `/slide2-configurations/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putSlide2Configuration(id: string, params: any) {
    const url = `/slide2-configurations/${id}`;
    return this.api.put<any>(url, params, 'client');
  }
}
