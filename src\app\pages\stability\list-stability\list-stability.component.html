<div class="list-content">
  <!-- Menu -->
  <div class="dashboard-menu mb-3 mt-3">
    <app-drag-and-drop
      [dataList]="menu"
      [orderField]="'Index'"
      [textField]="'Titulo'"
      [class]="'drag-list-flex'"
      (sendClickEvent)="clickEvent($event)"
    ></app-drag-and-drop>
  </div>
</div>

<!-- Notificações de Banner-->
<app-alert
  *ngIf="showNotificationBanner"
  [class]="'alert-warning'"
  class="mt-3"
  [messages]="bannerNotifications"
  [showCloseButton]="true"
  [onClose]="handleCloseNotificationBanner.bind(this)"
></app-alert>

<div class="list-content mt-2">
  <form [formGroup]="formFilter">
    <div class="row mt-1">
      <!-- Selects Cliente, Unidade e Estrutura -->
      <app-hierarchy
        #hierarchy
        [elements]="elements"
        class="col-md-12"
      ></app-hierarchy>
    </div>

    <div class="row mt-1 mb-3">
      <div class="col-md-2">
        <label class="form-label">De</label>
        <input type="date" class="form-control" formControlName="StartDate" />
      </div>
      <div class="col-md-2">
        <label class="form-label">Até</label>
        <input type="date" class="form-control" formControlName="EndDate" />
      </div>
      <div class="col-md-3 d-flex align-items-end">
        <label class="form-label"
          >Visualização máxima de pacotes por página</label
        >
      </div>
      <div class="col-md-1 d-flex align-items-end">
        <input
          type="number"
          class="form-control"
          min="1"
          max="30"
          step="1"
          placeholder="5"
          formControlName="PageSize"
        />
      </div>
      <div class="col-md-4 d-flex align-items-end justify-content-end">
        <app-button
          [class]="'btn-logisoil-blue'"
          [icon]="'fa fa-search'"
          [label]="'Buscar'"
          class="me-1"
          (click)="searchPackages()"
        ></app-button>
        <app-button
          [class]="'btn-logisoil-gray'"
          [icon]="'fa fa-eraser'"
          [label]="'Limpar'"
          (click)="resetFilter()"
        ></app-button>
      </div>
    </div>
  </form>

  <!-- Alerta -->
  <div
    class="alert alert-warning mt-3"
    role="alert"
    *ngIf="messageReturn.status"
  >
    {{ messageReturn.text }}
  </div>

  <div class="alert alert-success mt-2" role="alert" *ngIf="message.status">
    {{ message.text }}
  </div>

  <app-alert
    class="mt-2"
    [class]="'alert-danger'"
    [messages]="messagesError"
  ></app-alert>

  <!-- Lista de pacotes -->
  <div class="row mt-3" *ngIf="tableData.length > 0">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <div class="row">
            <div class="col-2 d-flex justify-content-center">
              <img
                src="assets/ico/ico-menu/box-seam.svg"
                class="img-fluid img-thumbnail me-1"
              />Nome do pacote
            </div>
            <div class="col-3 d-flex justify-content-center">Estrutura</div>
            <div class="col-3 d-flex justify-content-center">Seções</div>
            <div class="col-2 d-flex justify-content-center">
              N.A montante (m)
            </div>
            <div class="col-1 d-flex justify-content-center">Status</div>
            <div class="col-1 d-flex justify-content-center">Ações</div>
          </div>
        </div>
        <div class="card-body">
          <ng-template ngFor let-row [ngForOf]="tableData" let-r="index">
            <div class="row mt-1 mb-1 border-bottom">
              <!-- Nome do pacote -->
              <div class="col-2 d-flex justify-content-center">
                <a (click)="managerTabs(r, 0, 'row')" style="cursor: pointer"
                  >{{ row.date }}
                </a>
              </div>
              <!-- Estrutrua -->
              <div class="col-3 d-flex justify-content-center">
                {{ row.structure_name }}
              </div>
              <!-- Secoes -->
              <div
                class="col-3 d-flex flex-wrap justify-content-center align-items-center"
              >
                <ng-template
                  ngFor
                  let-section
                  [ngForOf]="row.sections"
                  let-s="index"
                >
                  <span
                    class="badge d-flex align-items-center mt-1"
                    [ngClass]="s > 0 ? 'ms-2' : ''"
                    >{{ section.name }}</span
                  >
                </ng-template>
              </div>
              <!-- Na montante -->
              <div class="col-2 d-flex justify-content-center">
                {{ row.upstream_linimetric_ruler_quota }}
              </div>
              <!-- Status -->
              <div class="col-1 d-flex justify-content-center">
                {{ row.status }}
              </div>
              <!-- Acoes -->
              <div class="col-1 d-flex justify-content-center">
                <app-button
                  [class]="'btn-logisoil-calculator me-2'"
                  [icon]="'fa fa-calculator'"
                  [ngbTooltip]="'Calcular'"
                  placement="bottom-left"
                  (click)="
                    openModal('calculate', {
                      package_id: row.id,
                      section_id: null
                    })
                  "
                ></app-button>
                <app-button
                  [class]="'btn-logisoil-forceCalculator me-2'"
                  [icon]="'fa fa-wrench'"
                  [ngbTooltip]="'Forçar cálculo'"
                  placement="bottom-left"
                  (click)="
                    openModal('forceCalculation', {
                      package_id: row.id,
                      section_id: null
                    })
                  "
                ></app-button>
                <app-button
                  [class]="'btn-logisoil-ignorePackage'"
                  [icon]="'fa fa-circle-thin'"
                  [ngbTooltip]="'Ignorar pacote de leituras'"
                  (click)="
                    openModal('delete', {
                      package_id: row.id,
                      section_id: null
                    })
                  "
                  placement="bottom-right"
                ></app-button>
              </div>
            </div>
            <!-- Seções -->
            <div class="row mt-4" *ngIf="row.tab">
              <div class="col-md-12">
                <ul class="nav nav-tabs" id="sectionTabs">
                  <ng-template
                    ngFor
                    let-section
                    [ngForOf]="row.sections"
                    let-c="index"
                  >
                    <li class="nav-item">
                      <a
                        class="nav-link"
                        data-bs-toggle="tab"
                        role="tab"
                        (click)="managerTabs(r, c, 'col')"
                        [ngClass]="section.tab ? 'active' : ''"
                        >{{ section.name }}</a
                      >
                    </li>
                  </ng-template>
                </ul>
                <div class="tab-content mt-2" id="sectionTabContent">
                  <ng-template
                    ngFor
                    let-section
                    [ngForOf]="row.sections"
                    let-c="index"
                  >
                    <div
                      class="tab-pane fade"
                      role="tabpanel"
                      [ngClass]="section.tab ? 'show active' : ''"
                    >
                      <!-- Alerta Seção -->
                      <div
                        class="alert alert-warning mt-3"
                        role="alert"
                        *ngIf="row.message.status"
                      >
                        {{ row.message.text }}
                      </div>
                      <div *ngIf="!row.message.status && section.package">
                        <div class="row mt-3">
                          <!-- Revisão -->
                          <div class="col-md-2 text-start">
                            <label class="form-label" style="font-size: 14.25px"
                              >Revisão</label
                            >
                            <select
                              class="form-select"
                              [(ngModel)]="section.selectedReviewId"
                              (change)="onReviewChange(section)"
                            >
                              <option value="">Selecione...</option>
                              <option
                                *ngFor="let review of section.reviews"
                                [value]="review.id"
                              >
                                {{ review.index }}
                              </option>
                            </select>
                          </div>

                          <!-- Etapa de Obra -->
                          <div class="col-md-3 text-start">
                            <label class="form-label" style="font-size: 14.25px"
                              >Etapa de Obra</label
                            >

                            <!-- Se houver etapas -->
                            <select
                              class="form-select"
                              [(ngModel)]="section.selectedStageId"
                              *ngIf="section.selectedStages?.length > 0"
                            >
                              <option value="">Selecione...</option>
                              <option
                                *ngFor="let stage of section.selectedStages"
                                [value]="stage.id"
                              >
                                {{ stage.stage }}
                              </option>
                            </select>

                            <!-- Se não houver etapas -->
                            <div
                              *ngIf="section.selectedStages?.length === 0"
                              class="form-control bg-light text-muted"
                            >
                              Sem etapa de obra
                            </div>
                          </div>
                          <div class="col-md-3 d-flex align-items-end">
                            <app-button
                              [class]="'btn-logisoil-blue'"
                              [icon]="'fa fa-refresh'"
                              [label]="'Atualizar'"
                              (click)="onUpdateSection(row, section)"
                            ></app-button>
                          </div>
                        </div>

                        <div class="row mt-3">
                          <div
                            class="col-lg-3 col-md-3"
                            *ngIf="
                              section.package.process_water_level_downstream
                            "
                          >
                            N.A. jusante (m)
                          </div>
                          <div class="col-lg-3 col-md-3">
                            Comp. de praia (m)
                          </div>
                          <div class="col-lg-3 col-md-3">Cota da obra</div>
                          <div class="col-lg-3 col-md-3">
                            <app-button
                              [class]="'btn-logisoil-calculator me-2'"
                              [icon]="'fa fa-calculator'"
                              [ngbTooltip]="'Calcular'"
                              placement="bottom-left"
                              (click)="
                                openModal('calculate', {
                                  package_id: row.id,
                                  section_id: section.id
                                })
                              "
                            ></app-button>
                            <app-button
                              [class]="'btn-logisoil-forceCalculator me-2'"
                              [icon]="'fa fa-wrench'"
                              [ngbTooltip]="'Forçar cálculo'"
                              placement="bottom-left"
                              (click)="
                                openModal('forceCalculation', {
                                  package_id: row.id,
                                  section_id: section.id
                                })
                              "
                            ></app-button>
                            <app-button
                              [class]="'btn-logisoil-ignorePackage'"
                              [icon]="'fa fa-times'"
                              [ngbTooltip]="'Ignorar pacote de leituras'"
                              placement="bottom-right"
                              (click)="
                                openModal('delete', {
                                  package_id: row.id,
                                  section_id: section.id
                                })
                              "
                            ></app-button>
                          </div>
                        </div>
                        <div class="row">
                          <!-- Na jusante -->
                          <div
                            class="col-lg-3 col-md-3"
                            *ngIf="
                              section.package.process_water_level_downstream
                            "
                          >
                            {{
                              section.package.data.downstream_linimetric.quota
                            }}
                          </div>
                          <!-- Praia -->
                          <div class="col-lg-3 col-md-3">
                            {{ section.package.data.beach_length.length }}
                          </div>
                          <!-- Cota da obra -->
                          <div
                            class="col-lg-3 col-md-3 d-flex justify-content-center"
                          >
                            <select
                              class="form-select"
                              style="width: 180px !important"
                              *ngIf="section.package.data.work_quota != null"
                            >
                              <option
                                *ngFor="
                                  let itemWorkQuota of section.package.data
                                    .work_quota
                                "
                                [ngValue]="itemWorkQuota"
                              >
                                {{ itemWorkQuota }}
                              </option>
                            </select>
                            <div
                              *ngIf="section.package.data.work_quota == null"
                            >
                              -
                            </div>
                          </div>
                        </div>
                        <div class="table-responsive mt-4">
                          <table
                            class="table table-hover text-center align-middle"
                          >
                            <thead>
                              <tr>
                                <th>Instrumento</th>
                                <th>Ponto de medição</th>
                                <th>Cota (m)</th>
                                <th>Variação (m)</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr
                                *ngFor="
                                  let instrument_with of section.package
                                    .instrumentWith;
                                  let i = index
                                "
                              >
                                <td>{{ instrument_with.identifier }}</td>
                                <td>
                                  {{
                                    instrument_with.measurement != null
                                      ? instrument_with.measurement.identifier
                                      : ''
                                  }}
                                </td>
                                <td>
                                  {{ instrument_with.reading_value.quota }}
                                </td>
                                <td
                                  [style.color]="
                                    instrument_with.reading_value
                                      .quota_variation_color
                                  "
                                >
                                  {{ instrument_with.quota_variation }}
                                </td>
                              </tr>
                              <tr
                                *ngFor="
                                  let instrument_without of section.package
                                    .instrumentWithout;
                                  let i = index
                                "
                                style="background-color: #ffeeba"
                              >
                                <td>{{ instrument_without.identifier }}</td>
                                <td>
                                  {{
                                    instrument_without.measurement != null
                                      ? instrument_without.measurement
                                          .identifier
                                      : ''
                                  }}
                                </td>
                                <td>
                                  {{ instrument_without.reading_value.quota }}
                                </td>
                                <td>
                                  {{ instrument_without.quota_variation }}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </ng-template>
        </div>
      </div>
    </div>

    <!-- Paginação -->
    <div class="row mt-2">
      <app-paginator
        [collectionSize]="collectionSize"
        [page]="page"
        [maxSize]="30"
        [boundaryLinks]="true"
        [pageSize]="formFilter.controls['PageSize'].value"
        (sendPageChange)="loadPage($event)"
      ></app-paginator>
    </div>

    <!-- Botão Voltar -->
    <div class="row mt-1">
      <div
        class="col-md-12 mt-3 d-flex align-items-end justify-content-end mb-2"
      >
        <app-button
          [class]="'btn-logisoil-blue'"
          [icon]="'fa fa-arrow-left'"
          [label]="'Voltar à tela inicial'"
          [click]="goBack.bind(this)"
        ></app-button>
      </div>
    </div>
  </div>
</div>

<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
  [data]="modalData"
></app-modal-confirm>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
