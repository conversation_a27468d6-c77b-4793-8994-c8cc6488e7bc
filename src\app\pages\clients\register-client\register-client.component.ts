import { AfterViewInit, Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { CustomValidators } from 'src/app/utils/custom-validators';
import { format, compareAsc, differenceInDays } from 'date-fns';

import { ClientService } from 'src/app/services/api/client.service';
import { UserService } from 'src/app/services/user.service';

import { MessageCadastro, MessageInputInvalid } from 'src/app/constants/message.constants';
import { maxTrialPeriod } from 'src/app/constants/app.constants';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';

import { NgxSpinnerService } from 'ngx-spinner';

//Tour guiado
import { CustomTourService } from 'src/app/services/custom-tour.service';
import { TourService } from 'ngx-ui-tour-ng-bootstrap';

@Component({
  selector: 'app-register-client',
  templateUrl: './register-client.component.html',
  styleUrls: ['./register-client.component.scss']
})
export class RegisterClientComponent implements OnInit {
  remainingText: number;

  public formClient: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    name: new FormControl(null, [Validators.required, Validators.maxLength(32)]),
    trial_period_start: new FormControl({ value: null, disabled: false }, [Validators.required]),
    trial_period_end: new FormControl({ value: null, disabled: false }, [Validators.required]),
    contractual_period_start: new FormControl({ value: null, disabled: false }, [Validators.required]),
    contractual_period_end: new FormControl({ value: null, disabled: false }, [Validators.required]),
    logo: new FormControl(
      null,
      Validators.compose([CustomValidators.validadorExtensaoArquivo(['png', 'jpg', 'jpeg']), CustomValidators.validadorTamanhoArquivo(1000000)])
    ),

    period_radio: new FormControl(null),
    active: new FormControl(true),
    note: new FormControl(null, [Validators.maxLength(1000)])
  });

  public client: any = {
    id: null,
    name: null,
    active: true,
    logo: {
      base64: null,
      name: null
    },
    periods: {
      trial_period: {
        start: null,
        end: null
      },
      contractual_period: {
        start: null,
        end: null
      }
    },
    note: null
  };

  public clientRequest: any = {};

  periodItems: any[] = [
    {
      name: 'trial',
      value: 'trial'
    },
    {
      name: 'contractual',
      value: 'contractual'
    }
  ];

  public periodSel: any;
  public periodSelected: string;
  public periodSelectedString: string;
  public periodList: any = this.periodItems;
  public edit: boolean = false;
  public status: any = [
    { status: 'Ativo', value: true },
    { status: 'Inativo', value: false }
  ];
  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public messageInvalid = MessageInputInvalid;

  public fileContent: string = '';
  public fileName: string = '';

  public maxlength: number = 1000;
  public charachtersCount: number = 0;
  public counter: string;

  public controlDateTrial: boolean = false;
  public controlDateContractual: boolean = false;

  public messageTrialPeriod: string = '';
  public messageContractualPeriod: string = '';

  public profile: any = null;
  public permissaoUsuario: any = null;

  public created_date: any = null;

  public formCrtl: boolean = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private clientService: ClientService,
    private customTourService: CustomTourService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    public tourService: TourService,
    public userService: UserService
  ) {}

  /**
   * Método executado ao inicializar o componente.
   * Configura o perfil do usuário, permissões e verifica se o cliente está em modo de edição.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.formCrtl = true;

    if (this.activatedRoute.snapshot.params.clientId) {
      this.edit = true;
      this.getClient(this.activatedRoute.snapshot.params.clientId);
    }
  }

  /**
   * Obtém os dados do cliente pelo ID fornecido.
   * @param {string} clientId - O ID do cliente.
   */
  getClient(clientId: string) {
    this.clientService.getClientById(clientId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados.note) {
        this.charachtersCount = dados.note.length;
        this.counter = `${this.charachtersCount} de ${this.maxlength}`;
      }

      this.formClient.get('id').setValue(dados.search_identifier);
      this.formClient.get('name').setValue(dados.name);
      this.formClient.get('active').setValue(dados.active);
      this.formClient.get('note').setValue(dados.note);

      if (dados.periods.trial_period != null) {
        this.formClient.get('trial_period_start').enable();
        this.formClient.get('trial_period_end').enable();

        this.formClient.get('trial_period_start').setValue(moment(dados.periods.trial_period.start).format('YYYY-MM-DD'));
        this.formClient.get('trial_period_end').setValue(moment(dados.periods.trial_period.end).format('YYYY-MM-DD'));
        this.formClient.get('period_radio').setValue('trial');
      }

      if (dados.periods.contractual_period != null) {
        this.formClient.get('contractual_period_start').enable();
        this.formClient.get('contractual_period_end').enable();

        this.formClient.get('contractual_period_start').setValue(moment(dados.periods.contractual_period.start).format('YYYY-MM-DD'));
        this.formClient.get('contractual_period_end').setValue(moment(dados.periods.contractual_period.end).format('YYYY-MM-DD'));
        this.formClient.get('period_radio').setValue('contractual');
      }

      if (dados.logo && dados.logo.base64) {
        this.fileName = dados.logo.name;
        this.fileContent = 'data:' + this.detectMimeType(dados.logo.base64) + ';base64,' + dados.logo.base64;
      }

      this.created_date = dados.created_date ? moment(dados.created_date).format('YYYY-MM-DD') : format(new Date(), 'yyyy-MM-dd 00:00:00');

      this.controlDateTrial = true;
      this.controlDateContractual = true;

      this.ngxSpinnerService.hide();
    });
  }

  //Registra um novo cliente usando os dados do formulário.
  registerClient() {
    this.ngxSpinnerService.show();

    this.formCrtl = false;

    this.messagesError = [];

    this.clientService.postClient(this.clientRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.SucessoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';
        this.formClient.reset();
        this.fileContent = '';
        this.formClient.get('logo').setValue(null);

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/clients']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          this.formCrtl = true;
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  //Edita o cliente existente com os dados atualizados do formulário.
  editClient() {
    this.ngxSpinnerService.show();
    this.formCrtl = false;
    this.messagesError = [];

    this.clientService.putClients(this.clientRequest.id, this.clientRequest).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/clients']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }

        this.formCrtl = true;
        this.ngxSpinnerService.hide();
      }
    );
  }

  //Atualiza o período selecionado (trial ou contractual) e habilita os campos de datas correspondentes.
  onPeriodChange(period: any) {
    this.getSelectedPeriod();
  }

  //Obtém o período selecionado e atualiza os campos correspondentes no formulário.
  getSelectedPeriod() {
    this.periodSel = this.periodItems.find((item) => item.value === this.formClient.get('period_radio').value);
    this.periodSelectedString = this.periodSel.name;

    this.formClient.get('trial_period_start').enable();
    this.formClient.get('trial_period_end').enable();

    this.formClient.get('contractual_period_start').enable();
    this.formClient.get('contractual_period_end').enable();
  }

  //Valida os dados do formulário e formata-os antes de enviar para registro ou edição.
  validate() {
    this.formatData();
  }

  //Formata os dados do cliente e prepara a requisição para registro ou edição.
  formatData() {
    this.clientRequest = this.client;

    this.clientRequest.name = this.formClient.get('name').value;
    this.clientRequest.active = this.formClient.get('active').value;
    this.clientRequest.note = this.formClient.get('note').value;

    this.clientRequest.periods.trial_period.start =
      this.formClient.get('trial_period_start').value != '' ? moment(this.formClient.get('trial_period_start').value).format('YYYY-MM-DDT00:00:00') : '';
    this.clientRequest.periods.trial_period.end =
      this.formClient.get('trial_period_end').value != '' ? moment(this.formClient.get('trial_period_end').value).format('YYYY-MM-DDT23:59:59') : '';
    this.clientRequest.periods.contractual_period.start =
      this.formClient.get('contractual_period_start').value != ''
        ? moment(this.formClient.get('contractual_period_start').value).format('YYYY-MM-DDT00:00:00')
        : '';
    this.clientRequest.periods.contractual_period.end =
      this.formClient.get('contractual_period_end').value != ''
        ? moment(this.formClient.get('contractual_period_end').value).format('YYYY-MM-DDT23:59:59')
        : '';

    if (this.formClient.get('logo').value) {
      const fileName = this.formClient.get('logo').value.split('\\');
      this.clientRequest.logo.name = fileName[fileName.length - 1];
      this.clientRequest.logo.base64 = this.fileContent;
    } else if (this.fileContent) {
      this.clientRequest.logo.name = this.fileName;
      this.clientRequest.logo.base64 = this.fileContent;
    } else {
      delete this.clientRequest.logo;
    }
    if (!this.edit) {
      delete this.clientRequest.id;
      delete this.clientRequest.active;
      this.registerClient();
    } else {
      this.clientRequest.id = this.activatedRoute.snapshot.params.clientId;
      this.editClient();
    }
  }

  //Carrega o arquivo de logo do cliente e converte-o para base64.
  uploadFile($event: any) {
    let file = $event.dataTransfer ? $event.dataTransfer.files[0] : $event.target.files[0];
    let pattern = /image-*/;
    let reader = new FileReader();
    if (!file.type.match(pattern)) {
      return;
    }
    reader.onload = this.uploadFileLoaded.bind(this);
    reader.readAsDataURL(file);
  }

  //Carrega o conteúdo do arquivo de logo em formato base64.
  uploadFileLoaded($event: any) {
    let reader = $event.target;
    this.fileContent = reader.result;
  }

  // Detecta o mime type de um arquivo em formato base64.
  detectMimeType(base64: string | string[]) {
    const signatures: any = {
      // JVBERi0: 'application/pdf',
      // R0lGODdh: 'image/gif',
      // R0lGODlh: 'image/gif',
      iVBORw0KGgo: 'image/png',
      '/9j/': 'image/jpg'
    };
    for (let s in signatures) {
      if (base64.indexOf(s) === 0) {
        return signatures[s];
      }
    }
  }

  //Atualiza o contador de caracteres conforme o usuário digita na área de nota.
  onValueChange(event: any): void {
    this.charachtersCount = event.target.value.length;
    this.counter = `${this.charachtersCount} de ${this.maxlength}`;
  }

  //Valida os períodos de teste e contratual, garantindo que as datas estejam corretas e dentro dos limites permitidos.
  periodValidate() {
    let trialPeriodStart = this.formClient.get('trial_period_start').value;
    let trialPeriodEnd = this.formClient.get('trial_period_end').value;
    let contractualPeriodStart = this.formClient.get('contractual_period_start').value;
    let contractualPeriodEnd = this.formClient.get('contractual_period_end').value;

    let dateBase = !this.edit ? new Date(format(new Date(), 'yyyy-MM-dd 00:00:00')) : new Date(this.created_date + ' 00:00:00');

    //Extraindo dia, mês e ano
    let dateMensagem = format(dateBase, 'dd') + '/' + format(dateBase, 'MM') + '/' + format(dateBase, 'yyyy');

    // Validação Período de Teste
    trialPeriodStart = !fn.isEmpty(trialPeriodStart) ? new Date(trialPeriodStart + ' 00:00:00') : null;

    trialPeriodEnd = trialPeriodEnd != null ? new Date(trialPeriodEnd + ' 00:00:00') : null;

    if (trialPeriodStart != null || trialPeriodEnd != null) {
      if (compareAsc(trialPeriodStart, dateBase) != 0) {
        this.controlDateTrial = false;
        this.messageTrialPeriod = 'A data inicial deve ser igual a ' + dateMensagem + '.';
      } else if (trialPeriodStart != null && trialPeriodEnd != null && compareAsc(trialPeriodStart, trialPeriodEnd) != -1) {
        this.controlDateTrial = false;
        this.messageTrialPeriod = 'A data final deve ser posterior à data inicial.';
      } else {
        this.controlDateTrial = true;
        this.messageTrialPeriod = null;
      }

      // Validacao total Periodo de Teste
      if (trialPeriodStart != null && trialPeriodEnd != null && differenceInDays(trialPeriodEnd, trialPeriodStart) > maxTrialPeriod) {
        this.controlDateTrial = false;
        this.messageTrialPeriod = `O período selecionado não pode ser superior a ${maxTrialPeriod} dias.`;
      }
    } else {
      this.controlDateTrial = true;
      this.messageTrialPeriod = null;
    }

    // Validação Período Contratual
    contractualPeriodStart = contractualPeriodStart != null ? new Date(contractualPeriodStart + ' 00:00:00') : null;

    contractualPeriodEnd = contractualPeriodEnd != null ? new Date(contractualPeriodEnd + ' 00:00:00') : null;

    if (contractualPeriodStart != null || contractualPeriodEnd != null) {
      if (trialPeriodStart == null && trialPeriodEnd == null && compareAsc(contractualPeriodStart, new Date(dateBase)) != 0) {
        this.controlDateContractual = false;
        this.messageContractualPeriod = 'A data inicial deve ser igual à data ' + dateMensagem;
      } else if (trialPeriodEnd != null && contractualPeriodStart != null && compareAsc(trialPeriodEnd, contractualPeriodStart) != -1) {
        this.controlDateContractual = false;
        this.messageContractualPeriod = 'A data inicial deve ser posterior à data final do Período de Teste.';
      } else if (contractualPeriodStart != null && contractualPeriodEnd != null && compareAsc(contractualPeriodStart, contractualPeriodEnd) != -1) {
        this.controlDateContractual = false;
        this.messageContractualPeriod = 'A data final deve ser posterior à data inicial.';
      } else {
        this.controlDateContractual = true;
        this.messageContractualPeriod = null;
      }
    } else {
      this.controlDateContractual = true;
      this.messageContractualPeriod = null;
    }
  }

  loadTourGuide() {
    this.customTourService.startTour(this.tourService, 'assets/tour-guide/register-client.tourguide.json');
  }
}
