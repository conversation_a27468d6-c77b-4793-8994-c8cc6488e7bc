<div
  class="d-flex align-items-center flex-wrap px-2"
  [ngClass]="
    enableItemPerPage ? 'justify-content-between' : 'justify-content-end'
  "
>
  <!-- Seletor de quantidade por página -->
  <div class="d-flex align-items-center mb-2" *ngIf="enableItemPerPage">
    <label for="pageSizeSelect" class="me-2 mb-0">Exibir</label>
    <select
      id="pageSizeSelect"
      class="form-select form-select-sm"
      style="width: auto"
      [(ngModel)]="pageSize"
      (change)="goToPage()"
    >
      <option *ngFor="let size of pageSizeOptions" [value]="size">
        {{ size }}
      </option>
    </select>
    <span class="ms-2">por página</span>
  </div>

  <div class="d-flex align-items-center mb-2" *ngIf="enableItemPerPage">
    <label for="pageSelect" class="me-2 mb-0">Página</label>
    <input
      type="number"
      step="1"
      id="pageSelect"
      class="form-control form-control-sm"
      style="width: 80px"
      [(ngModel)]="pageSelect"
      (keyup.enter)="goToPage(pageSelect)"
      (keypress)="allowOnlyInteger($event, pageSelect)"
      [min]="1"
      [max]="totalPages"
      appDisableScroll
    />
    <app-button
      [class]="'btn-logisoil-blue btn-sm btn-outline-secondary ms-2'"
      [label]="'Ir'"
      (click)="goToPage(pageSelect)"
    ></app-button>
  </div>

  <!-- Texto informativo -->
  <div class="mb-2" *ngIf="enableItemPerPage">
    {{ getDisplayText() }}
  </div>
</div>

<!-- Paginação -->
<ngb-pagination
  [collectionSize]="collectionSize"
  [(page)]="page"
  [maxSize]="maxSize"
  [boundaryLinks]="true"
  [rotate]="rotate"
  [pageSize]="pageSize"
  class="d-flex justify-content-center"
  (pageChange)="getPageChange($event)"
>
</ngb-pagination>
