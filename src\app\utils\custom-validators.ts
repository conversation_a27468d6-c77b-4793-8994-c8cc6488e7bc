import { AbstractControl, ValidatorFn } from '@angular/forms';

export class CustomValidators {
  static validadorExtensaoArquivo(extensoesValidas: string[]): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } => {
      const valor = control.value;

      if (!valor) {
        return null;
      }
      const extensao = valor.split('.');

      return extensoesValidas.includes(extensao[extensao.length - 1]) ? null : { customError: 'Formato de arquivo inválido.' };
    };
  }

  // Tamanho do arquivo em bytes
  static validadorTamanhoArquivo(tamanhoMaxArquivo: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } => {
      const valor = control.value;

      if (!valor || !valor.tamanho) {
        return null;
      }

      return valor.tamanho <= tamanhoMaxArquivo ? null : { customError: 'Tamanho do arquivo excede ' + tamanhoMaxArquivo / 1000000 + 'MB.' };
    };
  }

  // Valida se o valor já está presente em determinada listagem
  static validadorValorListado(lista: any[], nomeAtributo: string, mensagemErro?: string): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } => {
      const valor = control.value;

      if (!valor) {
        return null;
      }

      return !lista.some((item) => item[nomeAtributo] === valor) ? null : { customError: mensagemErro || 'Valor já listado.' };
    };
  }
}
