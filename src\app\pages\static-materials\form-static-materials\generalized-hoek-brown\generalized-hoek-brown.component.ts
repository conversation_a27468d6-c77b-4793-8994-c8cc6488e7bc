import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { IDropdownSettings } from 'ng-multiselect-dropdown';

import { strengthDefinition } from 'src/app/constants/static-materials.constants';

import { FormService } from 'src/app/services/form.service';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-generalized-hoek-brown',
  templateUrl: './generalized-hoek-brown.component.html',
  styleUrls: ['./generalized-hoek-brown.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class GeneralizedHoekBrownComponent implements OnInit, OnChanges {
  @Input() public data: any = null;
  @Input() public view: boolean = false;

  public formGeneralizedHoekBrown: FormGroup = new FormGroup({
    strength_definition: new FormControl('', [Validators.required]),
    ucs_intact: new FormControl('', [Validators.required]),
    gsi: new FormControl('', [Validators.required]),
    mi: new FormControl('', [Validators.required]),
    d: new FormControl('', [Validators.required]),
    mb: new FormControl('', [Validators.required]),
    s: new FormControl('', [Validators.required]),
    a: new FormControl('', [Validators.required])
  });

  public func = fn;

  public strengthDefinition = strengthDefinition;

  public seletectedStrengthDefinition = null;

  public dropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'name',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 10,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  constructor(public formService: FormService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  itemEvent(item: any, action: string = 'select') {
    if (this.formGeneralizedHoekBrown.controls['strength_definition'].value.length > 0) {
      this.seletectedStrengthDefinition = this.formGeneralizedHoekBrown.controls['strength_definition'].value[0].id;
    } else {
      this.seletectedStrengthDefinition = null;
    }
  }

  splitData($dados) {
    this.formGeneralizedHoekBrown.controls['strength_definition'].setValue([
      fn.findIndexInArrayofObject(this.strengthDefinition, 'id', $dados.strength_definition.toString(), 'name', true)
    ]);

    this.seletectedStrengthDefinition = $dados.strength_definition;

    this.formGeneralizedHoekBrown.controls['ucs_intact'].setValue($dados.ucs_intact);
    this.formGeneralizedHoekBrown.controls['gsi'].setValue($dados.gsi);
    this.formGeneralizedHoekBrown.controls['mi'].setValue($dados.mi);
    this.formGeneralizedHoekBrown.controls['d'].setValue($dados.d);
    this.formGeneralizedHoekBrown.controls['mb'].setValue($dados.mb);
    this.formGeneralizedHoekBrown.controls['s'].setValue($dados.s);
    this.formGeneralizedHoekBrown.controls['a'].setValue($dados.a);

    if (this.view) {
      this.formGeneralizedHoekBrown.disable();
    }
  }

  validate() {
    let formFields: any = [];
    let formValid = false;

    if (this.formGeneralizedHoekBrown.controls['strength_definition'].value.length > 0) {
      switch (this.formGeneralizedHoekBrown.controls['strength_definition'].value[0].id) {
        case '1':
          formFields = ['gsi', 'mi', 'd'];
          break;
        case '2':
          formFields = ['mb', 's', 'a'];
          break;
      }
      formValid = this.formService.validateFormList(this.formGeneralizedHoekBrown, formFields);
    }

    if (!formValid) {
      this.formGeneralizedHoekBrown.markAllAsTouched();
    }

    return formValid;
  }
}
