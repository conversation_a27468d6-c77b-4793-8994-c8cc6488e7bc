import { Component, Input, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { TabsConditionsComponent } from '@pages/static-materials/conditions-static-materials/tabs-conditions/tabs-conditions.component';

@Component({
  selector: 'app-general-tab',
  templateUrl: './general-tab.component.html',
  styleUrls: ['./general-tab.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class GeneralTabComponent implements OnInit {
  @ViewChild(TabsConditionsComponent) tabConditions: TabsConditionsComponent;
  @ViewChild('hierarchy') hierarchy: any;

  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;

  public formStaticMaterials: FormGroup = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.maxLength(50)]),
    active: new FormControl(true)
  });

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true,
      showCheckbox: false
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true,
      showCheckbox: false
    },
    structures: {
      single: true,
      active: true,
      showCheckbox: false
    }
  };

  public filterHierarchy: any = {};

  constructor() {}

  ngOnInit(): void {}

  getEventHierarchy($event) {
    switch ($event.type) {
      case 'units':
        break;
      case 'structures':
        if ($event.element != null) {
          if ($event.action === 'select') {
            this.tabConditions.getStaticMaterialsList($event.element);
          }
        }

        break;
    }

    if ($event.action === 'deselect') {
      this.tabConditions.materialsList = [];
    }
  }

  filterEventHierarchy($event) {
    this.filterHierarchy = $event;
  }
}
