import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MessageInputInvalid } from 'src/app/constants/message.constants';
import { TypeRegister } from 'src/app/constants/instruments.constants';

import { UserService } from 'src/app/services/user.service';

import * as moment from 'moment';

import { ReadingService } from 'src/app/services/api/reading.service';

@Component({
  selector: 'app-history-reading',
  templateUrl: './history-reading.component.html',
  styleUrls: ['./history-reading.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HistoryReadingComponent implements OnInit {
  public formHistoryReading: FormGroup = new FormGroup({
    id: new FormControl(null),
    description: new FormControl('', [Validators.required, Validators.maxLength(1000)]),
    type: new FormControl('', [Validators.required])
  });

  public typeRegister: any = TypeRegister;

  public ctrlHistory: boolean = false;

  public message: any = [{ text: '', status: false }];
  public messagesError: any = null;
  public messageReturn: any = [{ text: '', status: false }];

  public profile: any = null;
  public permissaoUsuario: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public tableHeader: any = [
    {
      label: 'Data/Hora',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Usuário',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Modificações',
      width: '65%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    }
  ];

  public tableData: any = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private readingServiceApi: ReadingService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.getHistoryReading(this.activatedRoute.snapshot.params.readingId);
  }

  getHistoryReading(readingId: string) {
    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    this.readingServiceApi.getReadingHistory(readingId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados) {
        this.tableData = dados ? dados.data : [];
        this.collectionSize = dados.total_items_count;
        this.formatDataHistory('tableData');
      } else {
        this.tableData = [];
        this.collectionSize = 0;
        this.messageReturn.text = MessageInputInvalid.NoHistory;
        this.messageReturn.status = true;
      }
    });
  }

  formatDataHistory(type) {
    this[type] = this[type].map((item: any) => {
      let itemData = {
        created_date: moment(item.created_date).format('DD/MM/YYYY HH:mm:ss'),
        user: item.modified_by.first_name + ' ' + item.modified_by.surname,
        changes: item.changes
      };
      return itemData;
    });
  }

  // Metodo que recebe a pagina selecionada
  loadPage(selectPage: number, option: string): void {
    if (option == 'history') {
      this.page = selectPage;
      this.getHistoryReading(this.activatedRoute.snapshot.params.readingId);
    }
  }
}
