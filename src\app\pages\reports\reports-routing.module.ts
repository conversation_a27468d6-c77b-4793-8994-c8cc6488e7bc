import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListReportsComponent } from './list-reports/list-reports.component';
import { SendReportComponent } from './send-report/send-report.component';
import { CreateNewScheduleComponent } from './create-new-schedule/create-new-schedule.component';

import { AppGuard } from '../../guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListReportsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EmitirRelatório,
    component: SendReportComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CriarAgendamento,
    component: CreateNewScheduleComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarAgendamentoDeRelatorio,
    component: CreateNewScheduleComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReportsRoutingModule {}
