import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { MessageInputInvalid } from 'src/app/constants/message.constants';

import { Markers } from 'src/app/constants/chart.constants';

import { ChartService as ChartServiceApi } from 'src/app/services/api/chart.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';

import { FileLoaderService } from 'src/app/services/file-loader.service';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';

@Component({
  selector: 'app-chart-rl',
  templateUrl: './chart-rl.component.html',
  styleUrls: ['./chart-rl.component.scss']
})
export class ChartRlComponent implements OnInit {
  public formChart: FormGroup = new FormGroup({
    instrument_upstream: new FormControl([]), // montante
    instrument_downstream: new FormControl([]), //jusante
    start_date: new FormControl('', [Validators.required]),
    end_date: new FormControl('', [Validators.required]),
    color: new FormControl('#000000'),
    marker: new FormControl('circle'),
    marker_length: new FormControl(1),
    chart_height: new FormControl(300)
  });

  public chart: any = {};
  public chartLegendsTop: number = 50;
  public chartLegendsBottom: number = 0;

  public controls: any = null;
  public markers = Markers;

  public instrument_upstream: any = [];
  public instrument_downstream: any = [];
  public instrumentsSettings = MultiSelectDefault.Instruments;

  public showColorPicker: boolean[] = [false];
  public selectedColor: string[] = ['#000000'];

  public xAxis: any = [];
  public yAxis: any = [];

  public chartSeries: any = [];
  public chartLegends: any = [];

  public min: number = null;
  public max: number = null;

  public func = fn;

  public messageReturn: any = { text: '', status: false };

  constructor(
    private activatedRoute: ActivatedRoute,
    private chartServiceApi: ChartServiceApi,
    private fileLoaderService: FileLoaderService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private router: Router
  ) {}

  /**
   * Método de inicialização do componente.
   * Configura os controles, carrega marcadores, e obtém os instrumentos.
   */
  ngOnInit(): void {
    this.controls = this.formChart.controls;
    this.instrumentsSettings.singleSelection = true;
    this.loadMarkers();
    this.getInstruments();
  }

  //Obtém a lista de instrumentos com base na estrutura selecionada e no tipo especificado.
  getInstruments() {
    let params = { StructureId: this.activatedRoute.snapshot.queryParams.structure, Type: this.activatedRoute.snapshot.queryParams.typeInstrument };
    this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
      let dados: any = resp;
      if (dados.status == 200) {
        dados = dados.body === undefined ? dados : dados.body;
        this.getPositionInstrument(dados);
      }
    });
  }

  getPositionInstrument(dados) {
    let selectedInstrument = this.activatedRoute.snapshot.params.instrumentId;
    dados.forEach((instrument, index) => {
      this.instrumentsServiceApi.getInstrumentsById(instrument.id).subscribe((resp) => {
        let dadosInstrument: any = resp;
        dadosInstrument = dadosInstrument.body === undefined ? dadosInstrument : dadosInstrument.body;
        let obj = {
          id: dadosInstrument.id,
          identifier: dadosInstrument.identifier,
          type: dadosInstrument.type
        };

        //Montante
        if (dadosInstrument.linimetric_ruler_position === 1) {
          if (obj.id == selectedInstrument) {
            this.controls['instrument_upstream'].setValue([obj]);
          }
          this.instrument_upstream.push(obj);
        }
        //Jusante
        if (dadosInstrument.linimetric_ruler_position === 2) {
          if (obj.id == selectedInstrument) {
            this.controls['instrument_downstream'].setValue([obj]);
          }
          this.instrument_downstream.push(obj);
        }
      });
    });
  }

  /**
   * Obtém os dados do gráfico de percolação com base nos instrumentos selecionados.
   * @param {string|null} id - ID opcional para o gráfico.
   */
  getChart(id = null) {
    this.resetConfigurations();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    let params = {};

    if (this.controls['start_date'].value != '') {
      params['StartDate'] = moment(this.controls['start_date'].value).format('YYYY-MM-DD');
    }

    if (this.controls['end_date'].value != '') {
      params['EndDate'] = moment(this.controls['end_date'].value).format('YYYY-MM-DD');
    }

    if (this.controls['instrument_upstream'].value.length > 0) {
      params['UpstreamLinimetricRulerId'] = this.controls['instrument_upstream'].value[0].id;
    }

    if (this.controls['instrument_downstream'].value.length > 0) {
      params['DownstreamLinimetricRulerId'] = this.controls['instrument_downstream'].value[0].id;
    }

    this.chartServiceApi.getChartRl(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      if (dados) {
        this.formatData(dados);
      } else {
        this.messageReturn.text = MessageInputInvalid.NoChart;
        this.messageReturn.status = true;

        setTimeout(() => {
          this.messageReturn.status = false;
        }, 4000);
      }
    });
  }

  //Constrói o gráfico a partir dos dados fornecidos.
  constructChart(data) {
    this.controls['chart_height'].setValue(300);
    this.constructXAxis(data);
  }

  //Constrói o eixo X do gráfico com base nas datas dos dados fornecidos.
  constructXAxis(data) {
    let dates = [];
    let datesSl = []; //Níveis de segurança

    for (const instrument in data.instruments) {
      if (data.instruments.hasOwnProperty(instrument)) {
        dates = [];
        for (const key2 in data.instruments[instrument]) {
          if (key2 !== 'security-levels') {
            dates.push(moment(data.instruments[instrument][key2].date).format('DD/MM/YYYY'));
          } else if (key2 === 'security-levels') {
            //Níveis de segurança
            for (const level in data.instruments[instrument][key2]) {
              if (data.instruments[instrument][key2].hasOwnProperty(level)) {
                datesSl = data.instruments[instrument][key2][level].map((item) => moment(item.date).format('DD/MM/YYYY'));
                this.xAxis.push(...datesSl);
                datesSl = [];
              }
            }
          }
        }
        this.xAxis.push(...dates);
      }
    }

    const orderedDates = this.xAxis
      .map((data) => {
        const [dia, mes, ano] = data.split('/').map(Number);
        return new Date(ano, mes - 1, dia);
      })
      .sort((a, b) => a - b)
      .map((data) => data.toLocaleDateString('pt-BR'));

    this.xAxis = orderedDates;
    this.xAxis = this.uniqueArray(this.xAxis);

    this.constructSeries(data);
  }

  //Limpa o formulário das configurações do gráfico.
  resetConfigurations() {
    this.chart = {};
    this.chartLegendsTop = 50;
    this.chartLegendsBottom = 0;
    this.xAxis = [];
    this.yAxis = [];

    this.chartSeries = [];
    this.chartLegends = [];

    this.min = null;
    this.max = null;
  }

  //Constrói as séries de dados do gráfico.
  constructSeries(data) {
    const datesObject = {};

    for (const date of this.xAxis) {
      datesObject[date] = null;
    }

    let series = {};
    this.chartSeries = [];
    this.chartLegends = [];

    this.controls['chart_height'].setValue(Math.floor(this.chartLegendsTop) + this.controls['chart_height'].value);

    for (const instrument in data.instruments) {
      if (data.instruments.hasOwnProperty(instrument)) {
        series[instrument] = { ...datesObject };

        for (const level in data.instruments[instrument]['security-levels']) {
          const securityLevels = Object.keys(data.instruments[instrument]['security-levels']);
          securityLevels.forEach((securityLevel) => {
            series[instrument + ' - ' + securityLevel] = { ...datesObject };
          });
        }
      }
    }

    for (const instrument in data.instruments) {
      if (data.instruments.hasOwnProperty(instrument)) {
        for (const key in data.instruments[instrument]) {
          if (key !== 'security-levels') {
            let date = moment(data.instruments[instrument][key].date).format('DD/MM/YYYY');
            const value = data.instruments[instrument][key].quota;
            series[instrument][date] =
              series.hasOwnProperty(instrument) && series[instrument].hasOwnProperty(date) ? (typeof value == 'string' ? parseFloat(value) : value) : null;
          } else if (key === 'security-levels') {
            //Níveis de segurança
            for (const securityLevel in data.instruments[instrument][key]) {
              if (data.instruments[instrument][key].hasOwnProperty(securityLevel)) {
                data.instruments[instrument][key][securityLevel].forEach((element) => {
                  let date = moment(element.date).format('DD/MM/YYYY');
                  const value = element.value;
                  series[instrument + ' - ' + securityLevel][date] =
                    series.hasOwnProperty(instrument + ' - ' + securityLevel) && series[instrument + ' - ' + securityLevel].hasOwnProperty(date)
                      ? typeof value == 'string'
                        ? parseFloat(value)
                        : value
                      : null;
                });

                const itemSeries = {
                  name: instrument + ' - ' + securityLevel,
                  type: 'line',
                  data: Object.values(series[instrument + ' - ' + securityLevel]),
                  connectNulls: true,
                  itemStyle: {
                    color: data.instruments[instrument][key][securityLevel][0].color
                  },
                  lineStyle: {
                    type: 'dashed' //Define a linha como tracejada
                  },
                  areaStyle: { opacity: 0.05 }
                };
                this.chartLegends.push(instrument + ' - ' + securityLevel);
                this.chartSeries.push(itemSeries);
                this.defineMinMax(itemSeries.data);
              }
            }
          }
        }
      }
    }

    for (const instrument in data.instruments) {
      if (data.instruments.hasOwnProperty(instrument)) {
        //Marcador, cor e tamanho
        let typeIcon = this.controls['marker'].value;
        let colorIcon = this.controls['color'].value;
        let lengthIcon = this.controls['marker_length'].value;
        let marker = fn.findIndexInArrayofObject(this.markers, 'marker', typeIcon, 'marker', true);

        const itemSeries = {
          name: instrument,
          type: 'line',
          data: Object.values(series[instrument]),
          connectNulls: true,
          showSymbol: true,
          symbol: `image://${this.getSvgWithReplacedValue(marker.text, colorIcon)}`,
          symbolSize: parseInt(lengthIcon) * 120,
          itemStyle: {
            color: colorIcon
          }
        };
        this.chartSeries.push(itemSeries);
        this.chartLegends.push(instrument);
        this.defineMinMax(itemSeries.data);
      }
    }

    this.constructYAxis();
  }

  //Prepara os dados para os eixos Y do gráfico.
  constructYAxis() {
    this.yAxis = [];

    let itemYAxis = {
      name: 'Cota NA (m)',
      type: 'value',
      axisLine: {
        show: true
      },
      nameRotate: 90,
      nameLocation: 'center',
      nameGap: 55,
      nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
      alignTicks: true,
      axisLabel: {
        formatter: function (value, index) {
          return value.toFixed(1);
        }
      },
      show: true,
      interval: Math.ceil((this.max - this.min) / 25),
      min: this.min,
      max: this.max
    };
    this.yAxis.push(itemYAxis);
    this.generateChart();
  }

  /**
   * Define os valores mínimos e máximos dos dados da série para o eixo Y.
   * @param {any} array - Dados da série.
   * @param {number} index - Índice do eixo Y.
   */
  defineMinMax(array: any, index = null) {
    array = array.filter((item) => item !== null);

    array = array.map((item) => {
      if (item != null) {
        return typeof item == 'number' ? item : item.value;
      }
    });

    const min = Math.min(...array);
    const max = Math.max(...array);
    let previous = min - (min % 10);
    let next = max + (10 - (max % 10));

    this.min = this.min == null ? previous : this.min;
    this.min = Math.min(this.min, previous);
    previous = this.min;

    this.max = this.max == null ? next : this.max;
    this.max = Math.max(this.max, next);
    next = this.max;
  }

  /**
   * Executa ações quando um clique é detectado fora de um elemento específico.
   * @param {string} element - Elemento que detectou o clique fora.
   * @param {number} index - Índice do elemento.
   */
  onClickedOutside(element: string, index = 0) {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker[index] = false;
        break;
    }
  }

  /**
   * Atualiza a cor selecionada após a conclusão da seleção de cor.
   * @param {any} $event - Evento de seleção de cor.
   * @param {number} index - Índice do controle de cor.
   */
  changeComplete($event, index = 0) {
    this.selectedColor[index] = $event.color.hex;
    this.controls[`color`].setValue(this.selectedColor[index]);
  }

  //Define a altura do gráfico.
  setHeight(height: string): void {
    this.controls['chart_height'].setValue(parseInt(height));
    this.generateChart();
  }

  /**
   * Remove elementos repetidos de um array.
   * @param {any[]} array - Array de elementos.
   * @returns {any[]} - Array com elementos únicos.
   */
  uniqueArray(array) {
    const uniqueArray = [];
    const seeDates = {};

    for (const date of array) {
      if (!seeDates[date]) {
        uniqueArray.push(date);
        seeDates[date] = true;
      }
    }
    return uniqueArray;
  }

  //Carrega os marcadores para o gráfico a partir dos arquivos SVG.
  loadMarkers() {
    this.markers.forEach((marker, index) => {
      this.loadFileContent('assets/markers/', marker.icon, index);
    });
  }

  /**
   * Carrega o conteúdo de um arquivo a partir de um caminho específico.
   * @param {string} path - Caminho para o arquivo.
   * @param {string} fileName - Nome do arquivo.
   * @param {number} index - Índice do marcador.
   */
  loadFileContent(path: string, fileName: string, index = 0) {
    this.fileLoaderService.loadFile(path, fileName).subscribe(
      (data) => {
        this.markers[index].text = data;
      },
      (error) => {
        console.error('Erro ao carregar o arquivo:', error);
      }
    );
  }

  /**
   * Converte o arquivo SVG para texto e substitui suas cores.
   * @param {string} svg - Conteúdo SVG.
   * @param {string} color - Cor para substituição.
   * @returns {string} - SVG convertido para base64.
   */
  getSvgWithReplacedValue(svg, color = '#000000') {
    svg = this.replaceMultipleOccurrences(svg, ['rgb(0,0,0)', 'rgb(101,101,101)'], [color, color]);
    const svgBase64 = btoa(svg);
    return `data:image/svg+xml;base64,${svgBase64}`;
  }

  /**
   * Substitui todas as ocorrências de uma string por outra em um texto.
   * @param {string} text - Texto original.
   * @param {string[]} oldValues - Array de valores a serem substituídos.
   * @param {string[]} newValues - Array de novos valores para substituição.
   * @returns {string} - Texto com as substituições aplicadas.
   */
  replaceMultipleOccurrences(text, oldValues, newValues) {
    if (oldValues.length !== newValues.length) {
      throw new Error('Os arrays devem ter o mesmo comprimento.');
    }

    let newText = text;
    for (let i = 0; i < oldValues.length; i++) {
      const oldValue = oldValues[i];
      const newValue = newValues[i];
      newText = newText.split(oldValue).join(newValue);
    }
    return newText;
  }

  //Formata os dados recebidos e os prepara para a construção do gráfico.
  formatData($dados) {
    let dados: any = {
      instruments: {}
    };

    $dados.instruments.forEach((instrument, index) => {
      dados.instruments[instrument.instrument_identifier] = {};
      dados.instruments[instrument.instrument_identifier]['security-levels'] = {};
      dados.instruments[instrument.instrument_identifier]['security-levels']['Nível de Atenção'] = [];
      dados.instruments[instrument.instrument_identifier]['security-levels']['Nível de Alerta'] = [];
      dados.instruments[instrument.instrument_identifier]['security-levels']['Nível de Emergência'] = [];

      instrument.readings.forEach((reading, index) => {
        dados.instruments[instrument.instrument_identifier][index] = {
          quota: reading.quota,
          date: reading.date
        };

        dados.instruments[instrument.instrument_identifier]['security-levels']['Nível de Atenção'].push({
          value: Math.floor(Math.random() * (25 - 5 + 1)) + 5, //reading.attention_level,
          color: '#ffc107',
          date: reading.date
        });
        dados.instruments[instrument.instrument_identifier]['security-levels']['Nível de Alerta'].push({
          value: Math.floor(Math.random() * (25 - 5 + 1)) + 5, //reading.alert_level,
          color: '#fd7e14',
          date: reading.date
        });
        dados.instruments[instrument.instrument_identifier]['security-levels']['Nível de Emergência'].push({
          value: Math.floor(Math.random() * (25 - 5 + 1)) + 5, //reading.emergency_level,
          color: '#dc3545',
          date: reading.date
        });
      });
    });
    this.constructChart(dados);
  }

  //Gera e configura o gráfico usando as opções definidas.
  generateChart() {
    this.chart['options'] = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegends,
        icon: 'rect',
        left: 'center',
        top: 'bottom'
      },
      grid: {
        containLabel: true,
        top: this.chartLegendsTop,
        left: 50,
        right: 50,
        height: this.controls['chart_height'].value
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        interval: 0.1,
        boundaryGap: false,
        data: this.xAxis,
        axisLabel: {
          interval: Math.floor(this.xAxis.length / 35), // Define o intervalo para exibir todos os valores do eixo X
          rotate: 60
        }
      },
      yAxis: this.yAxis,
      series: this.chartSeries
    };
  }

  //Navega de volta para a tela de instrumentos.
  goBack() {
    this.router.navigate(['/instruments']);
  }
}
