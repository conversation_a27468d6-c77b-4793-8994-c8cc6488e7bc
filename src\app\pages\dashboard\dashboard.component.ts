import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';

import { DashboardService } from 'src/app/services/dashboard.service';

import { DashboardService as DashboardServiceApi } from 'src/app/services/api/dashboard.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';

import { NgxSpinnerService } from 'ngx-spinner';
import { UsersService as UsersServiceApi } from 'src/app/services/api/users.service';

import { groupInstruments } from 'src/app/constants/instruments.constants';
import { Markers, XAxisRotate, YAxisLabel } from 'src/app/constants/chart.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { Period } from 'src/app/constants/simulations.constants';

import { EChartsComponent } from '@components/e-charts/e-charts.component';
import { GoogleMapsComponent } from '@components/google-maps/google-maps.component';

import { ECharts } from 'echarts';
import { Subscription } from 'rxjs';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  @ViewChild('chartSafetyFactor', { static: false }) chartSafetyFactor: EChartsComponent;
  @ViewChild(GoogleMapsComponent) googleMaps: GoogleMapsComponent | undefined;
  @ViewChild('modalChartFs') ModalChartFs: any;

  public sectionForm: FormGroup = new FormGroup({
    SectionId: new FormControl([]),
    Period: new FormControl(1),
    StartDate: new FormControl(''),
    EndDate: new FormControl('')
  });

  public sections: any[] = [];
  public sectionSettings = MultiSelectDefault.Sections;
  public period: any = Period;

  public structureName: string = '';

  public messageSection: any = { text: '', status: false, class: 'alert-success' };
  public messagesErrorDXF: any = [];

  // DXF
  public fileDxf: any = null;
  public fileContent: string = '';
  public fileName: string = '';
  public titleDxf: string = '';

  // Mapa
  public groupInstruments: any = groupInstruments;
  public dropdownOpen = false;

  public dataMapsStructure = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  // Gráficos
  public messageChartFS: any = { text: '', status: false, class: 'alert-success' };
  public messagesErrorChartFS: any = [];
  public messageChartPercolation: any = { text: '', status: false, class: 'alert-success' };
  public messagesErrorChartPercolation: any = [];

  //Gráfico FS
  public chartFS: any = {};
  public dadosFS: any = {};
  public xAxisFS: any = [];
  public yAxisFS: any = [];
  public chartSeriesFS: any = [];
  public chartLegendsFS: any = [];
  public chartLegendsTopFS: number = 50;
  public chartLegendsBottomFS: number = 0;

  //Modal gráfico FS
  public configModal: any = null;
  public titleModal: string = '';
  public isModalOpen: boolean = false;
  public selectedItem: any = null;

  //Gráfico INAs e PZs
  public chartPercolation: any = {};
  public dadosPercolation: any = null;
  public xAxisPercolation: any = [];
  public yAxisPercolation: any = [];
  public chartSeriesPercolation: any = [];
  public chartLegendsPercolation: any = [];
  public chartLegendsTopPercolation: number = 50;
  public chartLegendsBottomPercolation: number = 0;
  public markersPercolation = Markers;
  public xAxisRotate = XAxisRotate;
  public yAxisLabelPercolation = YAxisLabel;
  public chartSeriesTypePercolation: string = 'line';
  public min: number = null;
  public max: number = null;

  private subscriptions: Subscription[] = [];

  private chartsInitialized = {
    safetyFactor: false
  };

  public configPercolation = {
    line_type: 'solid',
    line_stroke: 2,
    //Leituras
    marker: 'circle',
    marker_length: 1,
    //Leituras secas
    dry_marker: 'x',
    dry_marker_length: 1,
    color0: '#000000',
    //Somente marcadores
    only_markers: false,
    // Intervalo eixo X
    xAxis_interval: 1,
    xAxis_rotate: 45,
    chart_height: 300,
    // Intervalo eixo Y
    yAxis0_interval: null,
    yAxis0_min: null,
    yAxis0_max: null,
    // Intervalo eixo Y NA Reservatorio
    yAxis1_interval: { value: null, disabled: true },
    yAxis1_min: null,
    yAxis1_max: null,
    marker1: 'rectangle',
    marker1_length: 1,
    color1: '#121FE6',
    only1_markers: false,
    instrument_upstream: [], // montante
    instrument_downstream: [], //jusante
    // Intervalo eixo Y Pluviometria
    instrument_climate: [],
    yAxis2_interval: { value: null, disabled: true },
    yAxis2_min: null,
    yAxis2_max: null,
    marker2: 'triangle',
    marker2_length: 1,
    color2: '#13EED2',
    only2_markers: false,
    graphic2_type: 'line'
  };

  private resizeObserver: ResizeObserver;

  public filterHierarchy: any;
  public structure: any;
  public client: any;

  public legendItens: any = [];
  private alreadyLoadedFromStorage = false;

  constructor(
    private elementRef: ElementRef,
    private dashboardService: DashboardService,
    private dashboardServiceApi: DashboardServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private sectionsServiceApi: SectionsServiceApi,
    public userServiceApi: UsersServiceApi
  ) {}

  /**
   * Método do ciclo de vida do Angular chamado ao inicializar o componente.
   * Define configurações iniciais, configura observadores de redimensionamento
   * e carrega dados do armazenamento local.
   */
  ngOnInit(): void {
    this.sectionSettings.singleSelection = true;
    this.calculatePeriod(this.sectionForm.get('Period').value);
    this.loadFromStorage();

    this.legendItens = groupInstruments.filter((item) => [1, 2, 3].includes(item.type));

    const container = this.elementRef.nativeElement.querySelector('.container-grid');
    if (container) {
      this.resizeObserver = new ResizeObserver(() => {
        container.style.display = 'none';
        setTimeout(() => {
          container.style.display = 'grid';
        }, 0);
      });
      this.resizeObserver.observe(container);
    }
  }

  /**
   * Método chamado ao destruir o componente, garantindo a liberação de recursos
   * ao desinscrever observadores e assinaturas de eventos.
   */
  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  /**
   * Carrega dados salvos no armazenamento local, como estrutura e cliente,
   * e inicializa as chamadas para obter informações do dashboard e do mapa.
   */
  loadFromStorage(): void {
    if (this.alreadyLoadedFromStorage) {
      return;
    }
    this.messagesErrorChartFS = [];
    this.messagesErrorDXF = [];
    this.messagesErrorChartPercolation = [];

    this.alreadyLoadedFromStorage = true;

    this.filterHierarchy = localStorage.getItem('filterHierarchy');

    if (this.filterHierarchy) {
      const filterData = JSON.parse(this.filterHierarchy);
      this.structure = filterData.StructureId;
      this.client = filterData.ClientId;

      if (this.structure && this.structure.id) {
        //Seção
        this.sectionsServiceApi.getSectionList({ structureId: this.structure.id, active: true }).subscribe((resp: any) => {
          let dados = resp?.body ?? resp;
          this.sections = Array.isArray(dados) ? dados : [];

          if (Array.isArray(this.sections) && this.sections.length > 0) {
            const section = this.sections[1] ?? this.sections[0]; // Pega a segunda ou a primeira se só houver uma

            if (section?.id) {
              this.sectionForm.get('SectionId').setValue([section]);

              this.getDashboardSectionDXF(section, 'select');
              this.getDashboardSectionMap(section, 'select');
              this.getDashboardSafetyFactor(section, 'select');
              this.getDashboardPercolation(section, 'select');
            } else {
              console.warn('Seção encontrada não possui ID válido.');
              this.messageSection = {
                text: 'Seção sem dados válidos para renderização.',
                status: true,
                class: 'alert-warning'
              };
            }
          } else {
            console.warn('Nenhuma seção disponível para esta estrutura.');

            this.messagesErrorChartFS.push({ message: 'Nenhuma seção disponível para esta estrutura.' });
            this.messagesErrorDXF.push({ message: 'Nenhuma seção disponível para esta estrutura.' });
            this.messagesErrorChartPercolation.push({ message: 'Nenhuma seção disponível para esta estrutura.' });
          }
        });
        //Mapa
        this.structureName = this.structure.name;
        this.dashboardService.mapsStructure(this.client.id, this.structure.id).subscribe((data) => {
          this.dataMapsStructure = data;
          this.googleMaps.setDataMap(this.dataMapsStructure, 'markers', true);
        });
      }
    }
  }

  /**
   * Obtém uma seção específica com base no ID informado.
   * @param {any} data - Objeto contendo as seções disponíveis.
   * @param {string} sectionId - ID da seção a ser localizada.
   * @returns {any} - Seção correspondente ao ID informado.
   */
  getSectionById(data, sectionId) {
    return data.sections.find((section) => section.id === sectionId);
  }

  /**
   * Calcula o período de acordo com o valor informado e configura as datas de início e fim,
   * além de calcular a diferença de dias entre elas.
   *
   * - Bloqueia os campos StartDate e EndDate caso o valor seja maior que 1.
   * - Habilita os campos para edição pelo usuário caso o valor seja igual a 0.
   * - Configura o campo VariationPeriodDays com a diferença em dias entre StartDate e EndDate.
   *
   * @param {number} value - O valor do período selecionado.
   * @param {boolean} [reset=true] - Indica se os campos de datas devem ser limpos antes do cálculo.
   */
  calculatePeriod(value, reset = true) {
    const startDateControl = this.sectionForm.get('StartDate');
    const endDateControl = this.sectionForm.get('EndDate');
    // Limpa os valores de VariationPeriodDays se o valor for diferente de 0
    if (reset) {
      endDateControl.setValue('');
      startDateControl.setValue('');
    }

    if (parseInt(value) > 1) {
      const today = moment().toDate(); // Define a data de hoje como um objeto Date

      if (endDateControl) {
        endDateControl.setValue(moment(today).format('YYYY-MM-DD')); // Seta a data de hoje no formato 'YYYY-MM-DD'

        const startDate = this.calculateNewDate(today, parseInt(value));
        startDateControl.setValue(moment(startDate).format('YYYY-MM-DD'));
      }

      // Bloquear os campos StartDate e EndDate
      startDateControl.disable();
      endDateControl.disable();
    } else if (parseInt(value) === 0) {
      // Se o valor for 0, habilita os campos para edição pelo usuário
      startDateControl.enable();
      endDateControl.enable();
    }
  }

  /**
   * Calcula uma nova data com base na data fornecida e no período selecionado.
   * Subtrai meses ou anos conforme o valor do período.
   *
   * @param {Date} dateParam - A data de referência para o cálculo.
   * @param {number} periodValue - O valor do período a ser subtraído da data.
   * @returns {Date} - A nova data calculada após subtração do período.
   */
  calculateNewDate(dateParam: Date, periodValue: number): Date {
    let newDate = new Date(dateParam);
    switch (periodValue) {
      case 2:
        newDate = moment(dateParam).subtract(6, 'months').toDate();
        break;
      case 3:
        newDate = moment(dateParam).subtract(1, 'years').toDate();
        break;
      case 4:
        newDate = moment(dateParam).subtract(2, 'years').toDate();
        break;
      case 5:
        newDate = moment(dateParam).subtract(3, 'years').toDate();
        break;
      case 6:
        newDate = moment(dateParam).subtract(5, 'years').toDate();
        break;
    }
    return newDate;
  }

  /**
   * Obtém o DXF da seção selecionada e processa os dados recebidos.
   * @param {any} section - Seção selecionada.
   * @param {string} [action='select'] - Define a ação de carregamento ou remoção.
   */
  getDashboardSectionDXF(section: any, action: string = 'select'): void {
    if (!section?.id) {
      console.warn('ID da seção não encontrado. Não foi possível buscar o DXF.');
      return;
    }

    this.ngxSpinnerService.show();
    this.fileDxf = null;
    this.messagesErrorDXF = [];

    if (action === 'deselect') {
      this.ngxSpinnerService.hide();
      //Limpar DXF
    } else {
      this.dashboardServiceApi
        .getSectionDXF({ SectionId: section.id })
        .subscribe(
          (resp: any) => {
            if (resp) {
              let dados: any = resp;
              dados = dados.body === undefined ? dados : dados.body;
              this.titleDxf = ` Revisão ${dados.section_review_index} - ${moment(dados.section_review_start_date).format('DD/MM/YYYY HH:mm:ss')}`;
              this.loadDrawing(dados);
            } else {
              console.error('Arquivo DXF não encontrado na resposta.');
            }
          },
          (error) => {
            console.error('Erro ao buscar o DXF:', error);
            if (error.status >= 400) {
              this.messagesErrorDXF = [];
              error.error.forEach((msgError) => {
                this.messagesErrorDXF.push(msgError);
              });
            }
          }
        )
        .add(() => {
          this.ngxSpinnerService.hide();
        });
    }
  }

  /**
   * Carrega o desenho associado a uma revisão, permitindo que ele seja exibido.
   * @param {Object} drawing Objeto contendo as informações do desenho.
   */
  loadDrawing(drawing) {
    if (drawing != null) {
      this.fileContent = drawing.base64;
      this.fileName = drawing.name;
      this.fileDxf = fn.base64ToFile(this.fileContent, this.fileName);
    }
  }

  /**
   * Obtém e exibe a seção no mapa, incluindo instrumentos e linhas de seção.
   * @param {any} section - Seção selecionada.
   * @param {string} [action='select'] - Define a ação de carregamento ou remoção.
   */
  getDashboardSectionMap(section: any, action: string = 'select'): void {
    if (action === 'select') {
      let params = {
        StructureId: this.structure.id
      };

      this.dashboardServiceApi.getSectionMap(params).subscribe((resp: any) => {
        let dataResp: any = resp;
        dataResp = dataResp.body === undefined ? dataResp : dataResp.body;

        let sectionData = this.getSectionById(dataResp, section.id);

        const instruments = sectionData.instruments.filter((item) => [1, 2, 3].includes(item.type));

        this.plotInstruments(instruments);
        this.plotSectionLine(sectionData);
      });
    } else {
    }
  }

  /**
   * Alterna a exibição do dropdown de opções.
   */
  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  /**
   * Plota os instrumentos no mapa, configurando marcadores visuais para cada um.
   * @param {any[]} $instruments - Lista de instrumentos a serem exibidos no mapa.
   */
  plotInstruments($instruments) {
    this.resetMap();
    $instruments.forEach((instrument) => {
      let strokeColor = 'white';
      let fillColor = fn.findIndexInArrayofObject(groupInstruments, 'type', instrument.type, 'color');
      let svgMarker = {
        path: 'M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0',
        fillColor: fillColor,
        fillOpacity: 1.0,
        strokeWeight: 1.5,
        strokeColor: strokeColor,
        rotation: 0,
        scale: 0.3,
        anchor: new google.maps.Point(0, 0)
      };
      let marker = {
        position: {
          lat: instrument.coordinate.latitude,
          lng: instrument.coordinate.longitude
        },
        title: instrument.identifier,
        options: {},
        icon: svgMarker,
        id: 'mk-' + instrument.identifier,
        zIndex: -999
      };
      let infoWindowMarker = {
        content: instrument.identifier,
        ariaLabel: instrument.identifier,
        id: instrument.identifier,
        data: instrument,
        contentConfig: []
      };

      marker['infoWindowMarker'] = infoWindowMarker;
      this.dataMapsStructure.markers.push(marker);
    });
    this.googleMaps.setDataMap(this.dataMapsStructure, 'markersMultiple', true);
  }

  /**
   * Reseta o mapa removendo os marcadores existentes.
   */
  resetMap(): void {
    const marker0 = this.dataMapsStructure.markers.shift();
    // Limpa os marcadores existentes na estrutura de dados e no mapa
    this.dataMapsStructure.markers = [];
    this.dataMapsStructure.markers.push(marker0);
    this.googleMaps.setDataMap(this.dataMapsStructure, 'clearMarkers', true); // 'clearMarkers' é uma chave personalizada para limpar
  }

  /**
   * Plota a linha da seção no mapa, utilizando coordenadas de montante, ponto médio e jusante.
   * @param {any} section - Objeto contendo os dados da seção.
   */
  plotSectionLine(section: any): void {
    this.resetPolylines();

    // Verifique se 'map_line_setting' está presente
    if (!section.map_line_setting) {
      console.error('map_line_setting está ausente na seção:', section);
      return;
    }

    const polyline = {
      path: [],
      strokeColor: section.map_line_setting.color,
      strokeOpacity: section.map_line_setting.type === 1 ? 0 : 1,
      strokeWeight: section.map_line_setting.width,
      icons: [
        {
          icon: {
            path: 'M 0,-1 0,1',
            strokeOpacity: section.map_line_setting.type === 1 ? 1 : 0,
            scale: 4,
            strokeWeight: section.map_line_setting.width
          },
          offset: '0',
          repeat: '20px'
        }
      ],
      id: this.sectionForm.get('SectionId').value[0].id,
      name: this.sectionForm.get('SectionId').value[0].name
    };

    // Adiciona as coordenadas ao caminho da linha
    polyline.path.push({
      lat: section.section_upstream_coordinate.latitude,
      lng: section.section_upstream_coordinate.longitude
    });

    // Coordenada opcional do ponto médio
    if (section.section_midpoint_coordinate) {
      polyline.path.push({
        lat: section.section_midpoint_coordinate.latitude,
        lng: section.section_midpoint_coordinate.longitude
      });
    }

    // Coordenada do ponto downstream
    polyline.path.push({
      lat: section.section_downstream_coordinate.latitude,
      lng: section.section_downstream_coordinate.longitude
    });

    // Configuração da janela de informações para a polilinha
    const infoWindowPolyline = {
      content: this.sectionForm.get('SectionId').value[0].name,
      ariaLabel: this.sectionForm.get('SectionId').value[0].name,
      id: this.sectionForm.get('SectionId').value[0].id,
      data: section,
      contentConfig: []
    };

    // Adiciona o infoWindow à polilinha
    polyline['infoWindowPolyline'] = infoWindowPolyline;

    // Adiciona a polilinha à estrutura de dados para exibição no mapa
    this.dataMapsStructure.polylines.push(polyline);

    // Atualiza o mapa com a nova polilinha
    this.googleMaps.setDataMap(this.dataMapsStructure, 'polylinesMultiple', false);
  }

  /**
   * Remove todas as polilinhas exibidas no mapa.
   */
  resetPolylines(): void {
    // Limpa as polilinhas existentes na estrutura de dados
    this.dataMapsStructure.polylines = [];

    // Chama setDataMap para remover as polilinhas do mapa
    this.googleMaps.setDataMap(this.dataMapsStructure, 'clearPolylines', true); // 'clearPolylines' indica a remoção de polylines
  }

  /**
   * Atualiza o mapa com base na nova seção selecionada, removendo dados anteriores.
   * @param {string} sectionId - ID da seção selecionada.
   */
  updateMapWithNewSection(sectionId: string): void {
    // Primeiro, reseta o mapa e as polilinhas
    this.resetMap(); // Limpa os instrumentos
    this.resetPolylines(); // Limpa as polilinhas

    // Busca os novos dados da seção e plota os instrumentos e polilinhas
    this.plotSectionLine(sectionId);
  }

  /**
   * Obtém e processa o gráfico de Fator de Segurança da seção selecionada.
   * @param {any} section - Seção selecionada.
   * @param {string} [action='select'] - Define a ação de carregamento ou remoção.
   */
  getDashboardSafetyFactor(section: any, action: string = 'select'): void {
    if (!section?.id || !this.structure?.id) {
      console.warn('ID da seção ou da estrutura não encontrado. Gráfico de Fator de Segurança não será carregado.');
      return;
    }

    this.ngxSpinnerService.show();
    this.messagesErrorChartFS = [];
    this.resetChartFS();

    let params = {
      StructureId: this.structure.id
    };

    params['StartDate'] = this.sectionForm.get('StartDate').value != '' ? `${this.sectionForm.get('StartDate').value} 00:00:00` : '';
    params['EndDate'] = this.sectionForm.get('EndDate').value != '' ? `${this.sectionForm.get('EndDate').value} 23:59:59` : '';

    if (action === 'select') {
      this.dashboardServiceApi
        .getStabilityChart(params)
        .subscribe(
          (resp: any) => {
            if (resp && resp.status === 200 && resp.body) {
              let dataResp = resp.body;
              this.dadosFS = this.dadosFS || {};
              this.dadosFS.safety_factor_metric_results = dataResp.safety_factor_metric_results;

              const sectionData = this.getSectionById(dataResp, section.id);

              if (sectionData?.safety_factor_results?.length) {
                this.dadosFS.section_result = { safety_factor_results: sectionData.safety_factor_results };
                this.constructChartFS(this.dadosFS);
              } else {
                this.messagesErrorChartFS.push({ message: 'A Seção selecionada não possui resultados de FS disponíveis.' });
              }
            } else {
              this.messagesErrorChartFS.push({ message: 'A Seção selecionada não possui dados para gerar o gráfico.' });
            }
          },
          (error) => {
            console.error('Erro ao buscar o Gráfico:', error);
            this.messagesErrorChartFS = [];

            if (error?.status === 204) {
              this.messagesErrorChartFS.push({ message: 'Não há dados disponíveis para esta seção.' });
            } else if (error?.error?.length) {
              error.error.forEach((msgError) => {
                this.messagesErrorChartFS.push(msgError);
              });
            } else {
              this.messagesErrorChartFS.push({ message: 'Erro inesperado ao carregar o gráfico.' });
            }
          }
        )
        .add(() => {
          this.ngxSpinnerService.hide();
        });
    }
  }

  /**
   * Constrói os dados para exibição do gráfico de Fator de Segurança.
   * @param {any} data - Dados do gráfico.
   */
  constructChartFS(data) {
    this.constructXAxisFS(data);
  }

  /**
   * Constrói os dados do eixo X do gráfico de Fator de Segurança.
   * @param {any} data - Dados do gráfico.
   */
  constructXAxisFS(data) {
    const dates = data.section_result.safety_factor_results.map((result) => moment(result.reading_created_date).format('DD/MM/YYYY'));
    this.xAxisFS = Array.from(new Set(dates)).sort((a, b) => (moment(a, 'DD/MM/YYYY').isBefore(moment(b, 'DD/MM/YYYY')) ? -1 : 1));

    this.constructSeriesFS(data);
  }

  /**
   * Constrói as séries de dados do gráfico de Fator de Segurança.
   * @param {any} data - Dados do gráfico.
   */
  constructSeriesFS(data) {
    const initializeDateObject = (dates) => {
      return dates.reduce((acc, date) => {
        acc[date] = null;
        return acc;
      }, {});
    };

    let CircularDrainedSeries = { ...initializeDateObject(this.xAxisFS) };
    let CircularUndrainedSeries = { ...initializeDateObject(this.xAxisFS) };
    let CircularPseudoStaticSeries = { ...initializeDateObject(this.xAxisFS) };
    let NonCircularDrainedSeries = { ...initializeDateObject(this.xAxisFS) };
    let NonCircularUndrainedSeries = { ...initializeDateObject(this.xAxisFS) };
    let NonCircularPseudoStaticSeries = { ...initializeDateObject(this.xAxisFS) };

    data.section_result.safety_factor_results.forEach((safetyResult) => {
      const date = moment(safetyResult.reading_created_date).format('DD/MM/YYYY');
      const value = safetyResult.value;
      switch (safetyResult.sli_file_type) {
        case 1: //CircularDrained
          CircularDrainedSeries[date] = { value, safetyResult };
          break;
        case 2: //CircularUndrained
          CircularUndrainedSeries[date] = { value, safetyResult };
          break;
        case 3: //CircularPseudoStatic
          CircularPseudoStaticSeries[date] = { value, safetyResult };
          break;
        case 4: //NonCircularDrained
          NonCircularDrainedSeries[date] = { value, safetyResult };
          break;
        case 5: //NonCircularUndrained
          NonCircularUndrainedSeries[date] = { value, safetyResult };
          break;
        case 6: //NonCircularPseudoStatic
          NonCircularPseudoStaticSeries[date] = { value, safetyResult };
          break;
      }
    });

    const addSeries = (type, seriesData, color) => {
      const itemSeries = {
        name: type,
        type: 'line',
        allData: Object.values(seriesData).map((item: any) => item),
        data: Object.values(seriesData).map((item: any) => (item ? item.value : null)),
        itemStyle: {
          color: color //this.selectedColor[section.name]
        },
        connectNulls: true,
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 7,
        hoverAnimation: false, // Desativa a animação ao passar o mouse
        sampling: 'none' // Desativa o 'sampling' para garantir que todos os pontos sejam mostrados
      };
      this.chartLegendsFS.push(type);
      this.chartSeriesFS.push(itemSeries);
    };

    const seriesData = [
      { name: 'Circular Drenada', data: CircularDrainedSeries, color: '#8b5cf6' },
      { name: 'Cricular Não Drenada', data: CircularUndrainedSeries, color: '#1e3a8a' },
      { name: 'Cricular Pseudo Estática', data: CircularPseudoStaticSeries, color: '#166534' },
      { name: 'Não Circular Drenada', data: NonCircularDrainedSeries, color: '#db2777' },
      { name: 'Não Circular Não Drenada', data: NonCircularUndrainedSeries, color: '#60a5fa' },
      { name: 'Não Circular Pseudo Estática', data: NonCircularPseudoStaticSeries, color: '#2dd4bf' }
    ];

    seriesData.forEach(({ name, data, color }) => {
      if (!Object.values(data).every((value) => value === null)) {
        addSeries(name, data, color);
      }
    });

    const referenceValues = Array.from(new Set(data.safety_factor_metric_results.map((item) => item.reference_value)));
    const dates = this.xAxisFS.map((date) => referenceValues);

    referenceValues.forEach((values) => {
      const dashedSeries = {
        name: `FS = ${values}`,
        type: 'line',
        data: dates,
        lineStyle: {
          type: 'dashed',
          color: '#000000'
        },
        showSymbol: false,
        markLine: {
          data: [
            {
              yAxis: values,
              label: {
                formatter: `FS = ${values}`,
                position: 'insideEndTop'
              }
            }
          ],
          lineStyle: {
            type: 'dashed',
            color: '#000000',
            width: 3
          },
          symbol: 'none'
        }
      };

      this.chartSeriesFS.push(dashedSeries);
    });

    this.constructYAxisFS();
  }

  /**
   * Configura os dados do eixo Y para exibição no gráfico de Fator de Segurança.
   */
  constructYAxisFS() {
    let itemYAxis = {
      name: '',
      type: 'value',
      axisLine: {
        show: true
      },
      nameRotate: 90,
      nameLocation: 'center',
      nameGap: 55,
      nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
      alignTicks: true,
      axisLabel: {
        formatter: function (value, index) {
          return value.toFixed(1);
        }
      },
      interval: 0.5,
      show: true
    };
    this.yAxisFS = itemYAxis;

    this.generateChartFS();
  }

  /**
   * Gera e configura o gráfico de Fator de Segurança com base nos dados fornecidos.
   */
  generateChartFS() {
    this.chartFS['options'] = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegendsFS,
        icon: 'rect',
        left: 'center',
        top: 'bottom'
      },
      grid: {
        containLabel: true,
        top: this.chartLegendsTopFS,
        left: 50,
        right: 50,
        height: 300
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        //interval: 0.1,
        boundaryGap: false,
        data: this.xAxisFS,
        axisLabel: {
          interval: Math.floor(this.xAxisFS.length / 35), // Define o intervalo para exibir todos os valores do eixo X
          rotate: 60
        }
      },
      yAxis: this.yAxisFS,
      series: this.chartSeriesFS
    };

    setTimeout(() => {
      this.initChartsFS('safetyFactor');
    }, 100);
  }

  /**
   * Inicializa o gráfico de Fator de Segurança, preparando os eventos e interações.
   * @param {string} typeChart - Tipo de gráfico a ser inicializado.
   */
  initChartsFS(typeChart: any): void {
    const chartMap = {
      safetyFactor: {
        chart: this.chartSafetyFactor,
        initialized: this.chartsInitialized.safetyFactor
      }
    };

    const selectedChart = chartMap[typeChart];

    if (selectedChart.chart && !selectedChart.initialized) {
      this.initChartFS(selectedChart.chart, typeChart);
      this.chartsInitialized[typeChart] = true;
    }
  }

  /**
   * Inicializa o evento de clique em marcadores do gráfico de Fator de Segurança.
   * @param {ECharts} chartInstance - Instância do gráfico.
   * @param {string} chartType - Tipo do gráfico.
   */
  initChartFS(chartComponent: EChartsComponent, chartType: string): void {
    if (chartComponent) {
      const subscription = chartComponent.chartInit.subscribe((chartInstance: ECharts) => {
        this.initMarkerClickEventFS(chartInstance, chartType);
      });
      this.subscriptions.push(subscription);
    } else {
      console.error(`Chart component not found or chartInit is undefined: ${chartType}`);
    }
  }

  /**
   * Inicializa o evento de clique em marcadores do gráfico de Fator de Segurança.
   * @param {ECharts} chartInstance - Instância do gráfico.
   * @param {string} chartType - Tipo do gráfico.
   */
  initMarkerClickEventFS(chartInstance: ECharts, chartType: string): void {
    // Remover eventos previamente registrados para evitar duplicação
    chartInstance.off('click');
    chartInstance.off('legendselectchanged');

    // Evento para clique em marcadores
    chartInstance.on('click', (params: any) => {
      if (params.componentType === 'series' && params.seriesType === 'line') {
        const series = chartInstance.getOption().series[params.seriesIndex];
        const allData = series.allData[params.dataIndex];
        const clickedData = this.extractDataFS(params.seriesIndex, params.dataIndex, allData);
        this.handleMarkerClickFS(clickedData);
      }
    });

    const updateColor = (chartInstance: ECharts, event) => {
      //chartInstance.on('legendselectchanged', (event: any) => {
      const { selected } = event;

      // Determinar quais legendas estão ativas
      const activeLegends = Object.keys(selected).filter((key) => selected[key]);

      // Detectar categorias

      const isDrenada = activeLegends.some((legend) => legend.includes('Circular Drenada'));
      const isNaoDrenada = activeLegends.some((legend) => legend.includes('Circular Não Drenada'));
      const isPseudoEstatica = activeLegends.some((legend) => legend.includes('Circular Pseudo Estática'));

      // Obter todas as séries do gráfico
      const currentSeries: any = chartInstance.getOption().series;

      // Atualizar as cores e séries com base nas regras
      const updatedSeries = currentSeries.map((series) => {
        if (series.markLine) {
          if (series.name === 'FS = 1.5') {
            series.markLine.data[0].label.formatter = 'FS = 1.5'; // coloca texto
            if (isDrenada && !isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ffff00'; // Amarelo
            } else if (isDrenada && (isNaoDrenada || isPseudoEstatica)) {
              series.markLine.lineStyle.color = '#000000'; // Preto
            } else if (!isDrenada) {
              series.markLine.lineStyle.color = 'transparent'; // Invisível
              series.markLine.data[0].label.formatter = ''; // remove texto
            }
          }

          if (series.name === 'FS = 1.3') {
            series.markLine.data[0].label.formatter = 'FS = 1.3'; // coloca texto
            if (isDrenada && !isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ff7f00'; // Laranja
            } else if (!isDrenada && isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ff0000'; // Vermelho
            } else if ((isNaoDrenada && (isDrenada || isPseudoEstatica)) || (!isNaoDrenada && isDrenada && isPseudoEstatica)) {
              series.markLine.lineStyle.color = '#000000'; // Preto
            } else if (!isDrenada && !isNaoDrenada) {
              series.markLine.lineStyle.color = 'transparent'; // Invisível
              series.markLine.data[0].label.formatter = ''; // remove texto
            }
          }

          if (series.name === 'FS = 1.1') {
            series.markLine.data[0].label.formatter = 'FS = 1.1'; // coloca texto
            if (isDrenada && !isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ff0000'; // Vermelho
            } else if (!isDrenada && isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ff7f00'; // Laranja
            } else if (!isDrenada && !isNaoDrenada && isPseudoEstatica) {
              series.markLine.lineStyle.color = '#ffff00'; // Amarelo
            } else if ((isPseudoEstatica && (isDrenada || isNaoDrenada)) || (isDrenada && isNaoDrenada && !isPseudoEstatica)) {
              series.markLine.lineStyle.color = '#000000'; // Preto
            } else if (!isDrenada && !isNaoDrenada && !isPseudoEstatica) {
              series.markLine.lineStyle.color = 'transparent'; // Invisível
              series.markLine.data[0].label.formatter = ''; // remove texto
            }
          }
        }

        return series;
      });

      // Atualizar o gráfico com as novas configurações de séries
      chartInstance.setOption({
        series: updatedSeries
      });
    };

    chartInstance.on('legendselectchanged', (event: any) => {
      updateColor(chartInstance, event);
    });

    setTimeout(() => {
      const initialLegendData = chartInstance.getOption().legend[0]?.data || []; // Captura os dados das legendas
      const initialSelected: Record<string, boolean> = initialLegendData.reduce((acc, legend) => {
        acc[legend] = true; // Define todas as legendas como selecionadas inicialmente
        return acc;
      }, {});

      updateColor(chartInstance, { selected: initialSelected });
    }, 50);
  }

  /**
   * Extrai os dados correspondentes ao marcador clicado no gráfico.
   * @param {number} seriesIndex - Índice da série no gráfico.
   * @param {number} dataIndex - Índice do dado dentro da série.
   * @param {any} allData - Todos os dados disponíveis na série.
   * @returns {any} - Dados extraídos do marcador.
   */
  extractDataFS(seriesIndex: number, dataIndex: number, allData: any): any {
    const section = this.dadosFS.section_result;
    if (section) {
      return {
        id: section.id,
        name: section.name,
        safety_factor_result: allData
      };
    }
    return null;
  }

  /**
   * Manipula o clique em um marcador do gráfico de Fator de Segurança.
   * @param {any} eventData - Dados do marcador clicado.
   */
  handleMarkerClickFS(eventData: any): void {
    if (!this.isModalOpen && eventData) {
      this.isModalOpen = true;
      this.selectedItem = eventData;
      this.titleModal = `- Estrutura: ${this.structureName}
          ${this.sectionForm.get('SectionId')?.value?.[0]?.name ? '| Seção: ' + this.sectionForm.get('SectionId')?.value?.[0]?.name : ''}`;
      setTimeout(() => {
        this.ModalChartFs.openModal();
        this.ModalChartFs.modalClosed.subscribe(() => {
          this.isModalOpen = false;
        });
      }, 50);
    }
  }

  /**
   * Reseta os dados e configurações do gráfico de Fator de Segurança.
   */
  resetChartFS() {
    this.chartFS = {};

    this.xAxisFS = [];
    this.yAxisFS = [];

    this.chartSeriesFS = [];
    this.chartLegendsFS = [];
  }

  /**
   * Obtém e processa os dados do gráfico de Percolação da seção selecionada.
   * @param {any} section - Seção selecionada.
   * @param {string} [action='select'] - Define a ação de carregamento ou remoção.
   */
  getDashboardPercolation(section: any, action: string = 'select'): void {
    if (!section?.id) {
      console.warn('ID da seção não encontrado. Gráfico de Percolação não será carregado.');
      return;
    }

    this.messagesErrorChartPercolation = [];
    this.resetChartPercolation();

    let params = {
      SectionId: section.id
    };

    params['StartDate'] = this.sectionForm.get('StartDate').value != '' ? `${this.sectionForm.get('StartDate').value} 00:00:00` : '';
    params['EndDate'] = this.sectionForm.get('EndDate').value != '' ? `${this.sectionForm.get('EndDate').value} 23:59:59` : '';

    if (action === 'select') {
      this.dashboardServiceApi.getPercolationChart(params).subscribe(
        (resp: any) => {
          if (resp && resp.status === 200 && resp.body) {
            const dados = resp.body;

            if (Array.isArray(dados.percolation_instruments) && dados.percolation_instruments.length > 0) {
              this.dadosPercolation = dados;
              this.formatDataPercolation(dados);
            } else {
              this.messagesErrorChartPercolation.push({ message: 'A Seção selecionada não possui dados de instrumentos de percolação.' });
            }
          } else {
            this.messagesErrorChartPercolation.push({ message: 'A Seção selecionada não possui dados para gerar o gráfico.' });
          }

          // if (resp && resp.status == 200) {
          //   this.dadosPercolation = resp;
          //   this.dadosPercolation = this.dadosPercolation.body === undefined ? this.dadosPercolation : this.dadosPercolation.body;
          //   this.formatDataPercolation(this.dadosPercolation);
          // } else {
          //   this.messagesErrorChartPercolation.push({ message: 'A Seção selecionada não possui dados para gerar o gráfico.' });
          // }
        },
        (error) => {
          console.error('Erro ao buscar o Gráfico de Percolação:', error);
          this.messagesErrorChartPercolation = [];

          if (error?.status === 204) {
            this.messagesErrorChartPercolation.push({ message: 'Não há dados disponíveis para esta seção.' });
          } else if (error?.error?.length) {
            error.error.forEach((msgError) => {
              this.messagesErrorChartPercolation.push(msgError);
            });
          } else {
            this.messagesErrorChartPercolation.push({ message: 'Erro inesperado ao carregar o gráfico de percolação.' });
          }

          // console.error('Erro ao buscar o Gráfico:', error);
          // if (error.status >= 400) {
          //   this.messagesErrorChartFS = [];
          //   error.error.forEach((msgError) => {
          //     this.messagesErrorChartFS.push(msgError);
          //   });
          // }
        }
      );
    }
  }

  /**
   * Formata os dados recebidos para exibição no gráfico de Percolação.
   * @param {any} $dados - Dados brutos do gráfico.
   */
  formatDataPercolation($dados) {
    let dados: any = {
      instruments: [],
      water_level_reservoir: [],
      pluviometry: []
    };

    $dados.percolation_instruments.forEach((instrument, index) => {
      instrument.readings.forEach((reading, index) => {
        dados.instruments.push({
          identifier: instrument.instrument_identifier,
          quota: reading.quota,
          top_quota: reading.top_quota,
          base_quota: reading.base_quota,
          dry: reading.dry ? 1 : 0,
          date: reading.date
        });
      });

      dados.water_level_reservoir = $dados.linimetric_rulers_readings;
      dados.pluviometry = $dados.climate_instrument_readings;
    });
    this.constructChartPercolation(dados);
  }

  /**
   * Constrói os dados do gráfico de Percolação.
   * @param {any} data - Dados do gráfico.
   */
  constructChartPercolation(data) {
    this.constructXAxisPercolation(data);
  }

  /**
   * Constrói os dados do eixo X do gráfico de Percolação.
   * @param {any} data - Dados do gráfico.
   */
  constructXAxisPercolation(data) {
    let dates = data.instruments.map((item) => moment(item.date).format('DD/MM/YYYY'));
    this.xAxisPercolation.push(...dates);

    dates = data.water_level_reservoir.map((item) => moment(item.date).format('DD/MM/YYYY'));
    this.xAxisPercolation.push(...dates);

    dates = data.pluviometry.map((item) => moment(item.date).format('DD/MM/YYYY'));
    this.xAxisPercolation.push(...dates);

    const datasOrdenadas = this.xAxisPercolation
      .map((data) => {
        const [dia, mes, ano] = data.split('/').map(Number);
        return new Date(ano, mes - 1, dia);
      })
      .sort((a, b) => a - b)
      .map((data) => data.toLocaleDateString('pt-BR'));

    this.xAxisPercolation = datasOrdenadas;
    this.xAxisPercolation = this.uniqueArray(this.xAxisPercolation);

    this.configPercolation['xAxis_interval'] = Math.floor(this.xAxisPercolation.length / 10);

    this.constructSeriesPercolation(data);
  }

  /**
   * Constrói as séries de dados do gráfico de Percolação.
   * @param {any} data - Dados do gráfico.
   */
  constructSeriesPercolation(data) {
    const datesObject = {};

    for (const date of this.xAxisPercolation) {
      datesObject[date] = null;
    }

    let series = {};

    series['NA Reservatório (m)'] = { ...datesObject };
    series['Pluviometria (mm)'] = { ...datesObject };

    const listInstruments = [...new Set(data.instruments.map((instrument) => instrument.identifier))];

    listInstruments.forEach((instrument) => {
      series[instrument + ''] = { ...datesObject };
      series[instrument + ' - CT'] = { ...datesObject };
      series[instrument + ' - CB'] = { ...datesObject };
    });

    data.instruments.forEach((element) => {
      let date = moment(element.date).format('DD/MM/YYYY');
      const quota = element.quota;
      const top_quota = element.top_quota;
      const base_quota = element.base_quota;
      if (element.dry) {
        let markerDry = fn.findIndexInArrayofObject(this.markersPercolation, 'marker', this.configPercolation['dry_marker'], 'marker', true);
        let config = {};
        config['value'] =
          series.hasOwnProperty(element.identifier) && series[element.identifier].hasOwnProperty(date)
            ? typeof quota == 'string'
              ? parseFloat(quota)
              : quota
            : null;
        config['symbol'] = `image://${this.getSvgWithReplacedValue(markerDry.text, this.configPercolation['color0'])}`;
        config['symbolSize'] = 120;
        series[element.identifier][date] = config;
      } else {
        series[element.identifier][date] =
          series.hasOwnProperty(element.identifier) && series[element.identifier].hasOwnProperty(date)
            ? typeof quota == 'string'
              ? parseFloat(quota)
              : quota
            : null;
      }

      series[element.identifier + ' - CT'][date] =
        series.hasOwnProperty(element.identifier) && series[element.identifier].hasOwnProperty(date)
          ? typeof top_quota == 'string'
            ? parseFloat(top_quota)
            : top_quota
          : null;
      series[element.identifier + ' - CB'][date] =
        series.hasOwnProperty(element.identifier) && series[element.identifier].hasOwnProperty(date)
          ? typeof base_quota == 'string'
            ? parseFloat(base_quota)
            : base_quota
          : null;
    });

    data.water_level_reservoir.forEach((element) => {
      let date = moment(element.date).format('DD/MM/YYYY');
      const quota = element.quota;

      series['NA Reservatório (m)'][date] = series['NA Reservatório (m)'].hasOwnProperty(date) ? (typeof quota == 'string' ? parseFloat(quota) : quota) : null;
    });

    data.pluviometry.forEach((element) => {
      let date = moment(element.date).format('DD/MM/YYYY');
      const pluviometry = element.pluviometry;

      series['Pluviometria (mm)'][date] = series['Pluviometria (mm)'].hasOwnProperty(date)
        ? typeof pluviometry == 'string'
          ? parseFloat(pluviometry)
          : pluviometry
        : null;
    });

    this.chartSeriesPercolation = [];
    this.chartLegendsPercolation = [];

    for (const key in series) {
      let item = fn.findIndexInArrayofObject(this.yAxisLabelPercolation, 'label', key, 'id', true);
      let idx = item === '' ? 0 : item.id;
      if ((item.show && idx > 0) || idx == 0) {
        //Marcador, cor e tamanho
        let typeIcon = idx > 0 ? this.configPercolation['marker' + idx] : this.configPercolation['marker'];
        let colorIcon = idx > 0 ? this.configPercolation['color' + idx] : this.randomHexColor();
        let lengthIcon = idx > 0 ? this.configPercolation['marker' + idx + '_length'] : this.configPercolation['marker_length'];
        let marker = fn.findIndexInArrayofObject(this.markersPercolation, 'marker', typeIcon, 'marker', true);

        const itemSeries = {
          name: key,
          type: idx === 2 ? this.configPercolation['graphic' + idx + '_type'] : this.chartSeriesTypePercolation,
          data: Object.values(series[key]),
          lineStyle: {
            width: this.configPercolation['line_stroke'],
            type: this.configPercolation['line_type'],
            opacity: idx > 0 ? (this.configPercolation['only' + idx + '_markers'].value ? 0 : 1) : this.configPercolation['only_markers'] ? 0 : 1
          },
          yAxisIndex: idx,
          barWidth: 20,
          itemStyle: {
            color: colorIcon
          },
          showSymbol: true,
          symbol: `image://${this.getSvgWithReplacedValue(marker.text, colorIcon)}`,
          symbolSize: parseInt(lengthIcon) * 120,
          connectNulls: true
        };
        this.chartSeriesPercolation.push(itemSeries);
        this.chartLegendsPercolation.push(idx === 0 ? key : item.label);
        this.defineMinMax(itemSeries.data, itemSeries.yAxisIndex);
      }
    }
    this.constructYAxisPercolation();
  }

  /**
   * Configura os eixos Y do gráfico de Percolação.
   */
  constructYAxisPercolation() {
    this.yAxisPercolation = [];
    this.yAxisLabelPercolation.forEach((item) => {
      if (item.label != 'Cota topo (m)' && item.label != 'Cota base (m)') {
        let itemYAxis = {
          name: item.label,
          type: 'value',
          position: item.position,
          offset: item.offset,
          axisLine: {
            show: true
          },
          nameRotate: item.nameRotate,
          nameLocation: item.nameLocation,
          nameGap: item.nameGap,
          nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
          alignTicks: true,
          axisLabel: {
            formatter: function (value, index) {
              return value.toFixed(1);
            }
          },
          show: item.show
        };
        if (item.id == 0) {
          itemYAxis['interval'] = this.configPercolation[`yAxis${item.id}_interval`];
        }

        itemYAxis['min'] = this.configPercolation[`yAxis${item.id}_min`];
        itemYAxis['max'] = this.configPercolation[`yAxis${item.id}_max`];

        if (fn.isEmpty(this.configPercolation[`yAxis${item.id}_interval`])) {
          itemYAxis['interval'] = Math.ceil((itemYAxis['max'] - itemYAxis['min']) / 15);
          this.configPercolation[`yAxis${item.id}_interval`] = itemYAxis['interval'];
        } else {
          itemYAxis['interval'] = this.configPercolation[`yAxis${item.id}_interval`];
        }

        this.yAxisPercolation.push(itemYAxis);
      }
    });

    this.generateChartPercolation();
  }

  /**
   * Gera e configura o gráfico de Percolação com base nos dados fornecidos.
   */
  generateChartPercolation() {
    this.chartPercolation['options'] = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegendsPercolation,
        icon: 'rect',
        left: 'center',
        top: 'bottom'
      },
      grid: {
        containLabel: true,
        top: '50', //Espaco para a legenda
        height: this.configPercolation['chart_height']
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: this.xAxisPercolation,
        axisLabel: {
          interval: this.configPercolation['xAxis_interval'] - 1, // Define o intervalo para exibir todos os valores do eixo X
          rotate: this.configPercolation['xAxis_rotate']
        }
      },
      yAxis: this.yAxisPercolation,
      series: this.chartSeriesPercolation
    };
  }

  /**
   * Reseta os dados e configurações do gráfico de Percolação.
   */
  resetChartPercolation() {
    this.chartPercolation = {};

    this.xAxisPercolation = [];
    this.yAxisPercolation = [];

    this.chartSeriesPercolation = [];
    this.chartLegendsPercolation = [];

    this.min = null;
    this.max = null;

    this.configPercolation = {
      line_type: 'solid',
      line_stroke: 2,
      //Leituras
      marker: 'circle',
      marker_length: 1,
      //Leituras secas
      dry_marker: 'x',
      dry_marker_length: 1,
      color0: '#000000',
      //Somente marcadores
      only_markers: false,
      // Intervalo eixo X
      xAxis_interval: 1,
      xAxis_rotate: 45,
      chart_height: 300,
      // Intervalo eixo Y
      yAxis0_interval: null,
      yAxis0_min: null,
      yAxis0_max: null,
      // Intervalo eixo Y NA Reservatorio
      yAxis1_interval: { value: null, disabled: true },
      yAxis1_min: null,
      yAxis1_max: null,
      marker1: 'rectangle',
      marker1_length: 1,
      color1: '#121FE6',
      only1_markers: false,
      instrument_upstream: [], // montante
      instrument_downstream: [], //jusante
      // Intervalo eixo Y Pluviometria
      instrument_climate: [],
      yAxis2_interval: { value: null, disabled: true },
      yAxis2_min: null,
      yAxis2_max: null,
      marker2: 'triangle',
      marker2_length: 1,
      color2: '#13EED2',
      only2_markers: false,
      graphic2_type: 'line'
    };
  }

  /**
   * Remove elementos repetidos de um array.
   * @param {any[]} array - Array de elementos.
   * @returns {any[]} - Array com elementos únicos.
   */
  uniqueArray(array) {
    const uniqueArray = [];
    const seeDates = {};

    for (const date of array) {
      if (!seeDates[date]) {
        uniqueArray.push(date);
        seeDates[date] = true;
      }
    }

    return uniqueArray;
  }

  /**
   * Converte o arquivo SVG para texto e substitui suas cores.
   * @param {string} svg - Conteúdo SVG.
   * @param {string} color - Cor para substituição.
   * @returns {string} - SVG convertido para base64.
   */
  getSvgWithReplacedValue(svg, color = '#000000') {
    svg = this.replaceMultipleOccurrences(svg, ['rgb(0,0,0)', 'rgb(101,101,101)'], [color, color]);
    const svgBase64 = btoa(svg);
    return `data:image/svg+xml;base64,${svgBase64}`;
  }

  /**
   * Substitui todas as ocorrências de uma string por outra em um texto.
   * @param {string} text - Texto original.
   * @param {string[]} oldValues - Array de valores a serem substituídos.
   * @param {string[]} newValues - Array de novos valores para substituição.
   * @returns {string} - Texto com as substituições aplicadas.
   */
  replaceMultipleOccurrences(text, oldValues, newValues) {
    if (oldValues.length !== newValues.length) {
      throw new Error('Os arrays devem ter o mesmo comprimento.');
    }

    let newText = text;
    for (let i = 0; i < oldValues.length; i++) {
      const oldValue = oldValues[i];
      const newValue = newValues[i];
      newText = newText.split(oldValue).join(newValue);
    }

    return newText;
  }

  /**
   * Gera uma cor hexadecimal aleatória.
   * @returns {string} - Cor hexadecimal.
   */
  randomHexColor() {
    const randomColorComponent = () => {
      const component = Math.floor(Math.random() * 256); //Valor aleatorio entre 0  e 255
      return component.toString(16).padStart(2, '0'); //Converte para hexadecimal e completa com zero se necessario
    };

    const r = randomColorComponent();
    const g = randomColorComponent();
    const b = randomColorComponent();

    return `#${r}${g}${b}`;
  }

  /**
   * Define os valores mínimos e máximos dos dados da série para o eixo Y.
   * @param {any} array - Dados da série.
   * @param {number} index - Índice do eixo Y.
   */
  defineMinMax(array: any, index) {
    array = array.filter((item) => item !== null);

    array = array.map((item) => {
      if (item != null) {
        return typeof item == 'number' ? item : item.value;
      }
    });

    const min = Math.min(...array);
    const max = Math.max(...array);
    let previous = min - (min % 10);
    let next = max + (10 - (max % 10));

    if (index == 0) {
      this.min = this.min == null ? previous : this.min;
      this.min = Math.min(this.min, previous);
      previous = this.min;

      this.max = this.max == null ? next : this.max;
      this.max = Math.max(this.max, next);
      next = this.max;
    }

    this.configPercolation[`yAxis${index}_min`] = previous;
    this.configPercolation[`yAxis${index}_max`] = next;
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }
}
