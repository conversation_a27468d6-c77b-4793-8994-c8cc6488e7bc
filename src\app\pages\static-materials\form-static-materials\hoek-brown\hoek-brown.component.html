<div class="col-md-12">
  <form [formGroup]="formHoekBrown">
    <div class="row">
      <label class="form-label" style="font-style: italic">Fórmula:</label>
      <div class="col-md-3">
        <img
          src="assets/images/static-materials/hoek-brown.png"
          class="img-fluid img-thumbnail"
          style="max-height: 80px; width: auto"
        />
      </div>
    </div>
    <div class="row mt-1">
      <!-- UCS Intacto (kPa) -->
      <div class="col-md-4">
        <label class="form-label">UCS Intacto</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="ucs_intact"
            min="0"
            step="0.0000001"
            (keypress)="
              func.controlNumber(
                $event,
                formHoekBrown.get('ucs_intact'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formHoekBrown.get('ucs_intact'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formHoekBrown.get('ucs_intact').valid &&
            formHoekBrown.get('ucs_intact').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- m -->
      <div class="col-md-4">
        <label class="form-label">M</label>
        <input
          type="text"
          class="form-control"
          formControlName="m"
          min="0"
          step="0.0000001"
          (keypress)="
            func.controlNumber(
              $event,
              formHoekBrown.get('m'),
              'positiveDecimalDot'
            )
          "
          (keyup)="func.controlNumber($event, formHoekBrown.get('m'))"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formHoekBrown.get('m').valid && formHoekBrown.get('m').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- s -->
      <div class="col-md-4">
        <label class="form-label">S</label>
        <input
          type="text"
          class="form-control"
          formControlName="s"
          min="0"
          step="0.0000001"
          (keypress)="
            func.controlNumber(
              $event,
              formHoekBrown.get('s'),
              'positiveDecimalDot'
            )
          "
          (keyup)="func.controlNumber($event, formHoekBrown.get('s'))"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formHoekBrown.get('s').valid && formHoekBrown.get('s').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
  </form>
</div>
