import { <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>, Component, HostListener, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';
import { navBar } from 'src/app/constants/readings.constants';

import { PackagesService as PackagesServiceApi } from 'src/app/services/api/packages.service';
import { ReadingService as ReadingServiceApi } from 'src/app/services/api/reading.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';

import { FilterService } from 'src/app/services/filter.service';
import { NotificationService } from 'src/app/services/notification.service';
import { ReadingService } from 'src/app/services/reading.service';
import { UserService } from 'src/app/services/user.service';
import { TableHeaderService } from 'src/app/services/table-header.service';

import { NgxSpinnerService } from 'ngx-spinner';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-list-readings',
  templateUrl: './list-readings.component.html',
  styleUrls: ['./list-readings.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListReadingsComponent implements OnInit, AfterViewInit {
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalConfirm') ModalConfirm: any;
  @ViewChild('modalHistory') ModalHistory: any;

  public formDateTime: FormGroup = new FormGroup({
    date: new FormControl('', [Validators.required])
  });

  public sections: any = [];
  public instruments: any = [];

  public selectedSection: any = [];

  public filterParams: any = {};
  public filterBody: any = {};

  public filterBodyParams: any = {
    section_ids: [],
    instrument_ids: []
  };

  public filter: any = {
    SearchIdentifier: '',
    Identifier: '',
    StartDate: '',
    EndDate: '',
    InstrumentType: ''
  };

  public section: any = {
    id: null,
    name: null,
    active: null
  };

  public sectionSettings = MultiSelectDefault.Sections;
  public instrumentsSettings = MultiSelectDefault.Instruments;

  public multipleSettings = MultiSelectDefault.Multiple;
  public viewSettings = MultiSelectDefault.View;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public func = fn;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageDateTime: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public showDateTime: boolean = false;

  public modalTitle: string = '';
  public modalMessage: string = '';
  public modalInstruction: string = '';
  public modalConfig: any = {
    iconHeader: '',
    action: ''
  };

  public selectedSections: any = {};

  public navbar: any = null;

  public typeInstrumentId: any = null;

  public historyId: string = null;
  public historyType: string = null;

  public ctrlInstrument: boolean = true;

  public selectedReadings: number = 0;
  public selectedPackages: number = 0;

  public tableHeader: any = [];

  public bannerNotifications: any = [];
  public showNotificationBanner: boolean = true;

  //Coluna Acoes
  public actionCustom: any = [
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-pencil-square-o',
      label: '',
      title: 'Editar',
      type: 'true',
      option: 'edit'
    },
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-history',
      label: '',
      title: 'Histórico',
      type: 'true',
      option: 'history'
    },
    {
      class: 'btn-logisoil-edit',
      icon: 'fa fa-eye',
      label: '',
      title: 'Visualizar',
      type: 'true',
      option: 'view'
    }
  ];

  public tableData: any = [];
  public selectedColumns = this.tableHeader;

  public shiftPress: boolean = false;
  public shiftIds: any = [];

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public filterSearch: any = {};
  public filterSearchBody: any = {};

  public ctrlBtnFilter: boolean = false;

  can(action: string): boolean {
    return !!this.permissaoUsuario?.[action];
  }

  /**
   * Método de escuta para eventos de pressionamento de teclas. Detecta quando a tecla Shift é pressionada.
   * @param {KeyboardEvent} event - O evento de teclado.
   */
  @HostListener('window:keydown', ['$event'])
  onKeyPress(event: KeyboardEvent) {
    if (event.shiftKey) {
      this.shiftPress = true;
    }
  }

  /**
   * Método de escuta para eventos de liberação de teclas. Detecta quando a tecla Shift é liberada.
   * @param {KeyboardEvent} event - O evento de teclado.
   */
  @HostListener('window:keyup', ['$event'])
  onKeyRelease(event: KeyboardEvent) {
    if (!event.shiftKey) {
      this.shiftPress = false;
    }
  }

  constructor(
    private filterService: FilterService,
    private router: Router,
    private readingsServiceApi: ReadingServiceApi,
    private sectionsServiceApi: SectionsServiceApi,
    private packagesServiceApi: PackagesServiceApi,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private readingService: ReadingService,
    private userService: UserService,
    private ngxSpinnerService: NgxSpinnerService,
    private tableHeaderService: TableHeaderService,
    private notificationService: NotificationService
  ) {}

  /**
   * Método executado ao inicializar o componente.
   * Configura o perfil do usuário, permissões e notificação de banner.
   */
  ngOnInit(): void {
    this.ngxSpinnerService.show();

    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.navbar = fn.constToObject(navBar);
    this.navbar = fn.objToArray(this.navbar);

    this.notificationService.notificationsBanner$.subscribe(({ readingNotifications }) => {
      this.bannerNotifications = readingNotifications;
    });

    this.notificationService.bannerVisibility$.subscribe(({ readingBannerStatus }) => {
      this.showNotificationBanner = readingBannerStatus;
    });

    // Seleciona por padrão as colunas com show === true no dropdown de Visualização
    if (!this.selectedColumns || this.selectedColumns.length === 0) {
      this.selectedColumns = this.tableHeader.filter((col: any) => col.show !== false);
    }
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros.
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
    }, 1000);
  }

  /**
   * Obtém a lista de seções com base na estrutura selecionada.
   * @param {any} structure - A estrutura selecionada.
   * @param {string} [action='select'] - A ação realizada (seleção ou desseleção).
   */
  getSections(structure, action: string = 'select') {
    if (action === 'select') {
      this.sectionsServiceApi.getSectionList({ structureId: structure.id }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.sections = dados;
      });
    } else {
      this.sections = [];
      this.instruments = [];

      this.filterBodyParams.section_ids = [];
      this.filterBodyParams.instrument_ids = [];
    }
  }

  /**
   * Obtém a lista de instrumentos com base nas seções selecionadas.
   * @param {any} sections - As seções selecionadas.
   * @param {string} [action=''] - A ação realizada (seleção, desseleção, etc.).
   */
  getInstruments(sections: any = [], action: string = '') {
    switch (action) {
      case 'select':
        sections = this.filterBodyParams.section_ids;
        break;
      case 'selectAll':
        break;
      case 'deselect':
        sections = this.filterBodyParams.section_ids;
        break;
      case 'deselectAll':
        break;
    }

    let uniqueArray = [];

    if (sections.length == 0) {
      this.instruments = [];
      const filterHierarchy: any = this.hierarchy.getFilters();
      if (filterHierarchy?.structures && Array.isArray(filterHierarchy.structures) && filterHierarchy.structures.length > 0) {
        let params = { StructureId: filterHierarchy.structures[0].id };
        this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
          let dados: any = resp;
          if (dados.status == 200) {
            dados = dados.body === undefined ? dados : dados.body;
            this.instruments = dados;
          }
        });
      }

      this.filterBodyParams.instrument_ids = [];
    } else {
      this.selectedSections = [];
      sections.forEach((section) => {
        this.sectionsServiceApi.getSectionByIdInstrument(section.id).subscribe((resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;
          dados.instruments.map((instrument) => {
            this.selectedSections[instrument.id] = instrument;
          });

          Object.keys(this.selectedSections).map((key) => {
            uniqueArray = uniqueArray.concat({ id: this.selectedSections[key].id, identifier: this.selectedSections[key].identifier });
          });

          this.instruments = fn.uniqueArray(uniqueArray);

          this.removeNonExistingItems();
        });
      });
    }
  }

  //Remove itens inexistentes da lista de instrumentos.
  removeNonExistingItems() {
    const filteredArray = this.filterBodyParams.instrument_ids.filter((item) => this.instruments.some((option) => option.id === item.id));
    this.filterBodyParams.instrument_ids = filteredArray;
  }

  /**
   * Obtém a lista de leituras com base nos parâmetros e corpo de requisição fornecidos.
   * @param {any} params - Parâmetros da requisição.
   * @param {any} bodyParams - Corpo da requisição.
   */
  getReadingsList(params, bodyParams) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.readingsServiceApi.postReadingsSearch(bodyParams, params).subscribe(
      (resp) => {
        let dados: any = resp;

        if (dados != null && dados != undefined) {
          dados = dados.body === undefined ? dados : dados.body;

          if (dados) {
            this.tableData = dados ? dados.data : [];
            this.collectionSize = dados.total_items_count;
            this.tableData = this.tableHeaderService.formatDataReading(this.tableData, this.typeInstrumentId);
          } else {
            this.tableData = [];
            this.collectionSize = 0;
            this.messageReturn.text = MessagePadroes.NoRegister;
            this.messageReturn.status = true;
            this.message.class = 'alert-warning';

            setTimeout(() => {
              this.messageReturn.status = false;
            }, 4000);
          }
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
          this.message.class = 'alert-warning';
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status === 500) {
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  /**
   * Obtém a lista de leituras de comprimento de praia com base nos parâmetros e corpo de requisição fornecidos.
   * @param {any} params - Parâmetros da requisição.
   * @param {any} bodyParams - Corpo da requisição.
   */
  getReadingsBeachLengthList(params, bodyParams) {
    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.readingsServiceApi.postBeachLengthsSearch(bodyParams, params).subscribe(
      (resp) => {
        let dados: any = resp;

        if (dados != null && dados != undefined) {
          dados = dados.body === undefined ? dados : dados.body;

          if (dados) {
            this.tableData = dados ? dados.data : [];
            this.collectionSize = dados.total_items_count;
            this.tableData = this.tableHeaderService.formatDataReading(this.tableData, this.typeInstrumentId);
          } else {
            this.tableData = [];
            this.collectionSize = 0;
            this.messageReturn.text = MessagePadroes.NoRegister;
            this.messageReturn.status = true;

            setTimeout(() => {
              this.messageReturn.status = false;
            }, 4000);
          }
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status === 500) {
          this.ngxSpinnerService.hide();
        }
      }
    );
  }

  //Realiza a busca de leituras com base nos filtros aplicados.
  searchReading() {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterParams = {};
    this.filterBody = {};

    this.filterParams = {
      SearchIdentifier: this.filter.SearchIdentifier,
      Identifier: this.filter.Identifier,
      InstrumentType: this.filter.InstrumentType,
      ClientId: filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '',
      ClientUnitId: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '',
      StructureId: filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].id : '',
      StartDate: this.filter.StartDate != '' ? this.filter.StartDate : '',
      EndDate: this.filter.EndDate != '' ? this.filter.EndDate : ''
    };

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterSearch['Section'] = this.filterBodyParams.section_ids;
    this.filterSearch['Instrument'] = this.filterBodyParams.instrument_ids;

    this.filterBody['section_ids'] = this.filterBodyParams.section_ids.map((section) => {
      return section.id;
    });

    this.filterBodyParams.instrument_ids = fn.isEmpty(this.filterBodyParams.instrument_ids) ? [] : this.filterBodyParams.instrument_ids;

    this.filterBody['instrument_ids'] = this.filterBodyParams.instrument_ids.map((instrument) => {
      return instrument.id;
    });

    this.filterSearchBody = {
      ...this.filterBody
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters(), this.filterSearchBody);

    this.getReadingsList(this.filterSearch, this.filterSearchBody);
  }

  /**
   * Gerencia os eventos de hierarquia e atualiza as seções e instrumentos conforme necessário.
   * @param {any} $event - O evento de hierarquia.
   */
  getEventHierarchy($event) {
    switch ($event.type) {
      case 'units':
        this.sections = [];
        this.instruments = [];
        this.filterBodyParams.section_ids = [];
        this.filterBodyParams.instrument_ids = [];
        break;
      case 'structures':
        this.sections = [];
        this.instruments = [];
        this.filterBodyParams.section_ids = [];
        this.filterBodyParams.instrument_ids = [];

        if ($event.element != null) {
          this.getInstruments();
          this.getSections($event.element, $event.action);
        }
        this.ctrlBtnFilter = $event.action === 'select' ? true : false;
        break;
    }

    if ($event.action === 'deselect') {
      this.ctrlBtnFilter = false;
      this.sections = [];
      this.instruments = [];
      this.filterBodyParams.section_ids = [];
      this.filterBodyParams.instrument_ids = [];
    }
  }

  //Realiza a busca de leituras de comprimento de praia com base nos filtros aplicados.
  searchReadingBeachLength() {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterParams = {};
    this.filterBody = {};

    this.filterParams = {
      SearchIdentifier: this.filter.SearchIdentifier,
      ClientId: filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '',
      ClientUnitId: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '',
      StructureId: filterHierarchy.structures != '' ? filterHierarchy.structures[0].id : '',
      StartDate: this.filter.StartDate != '' ? moment(this.filter.StartDate).format('YYYY-MM-DDT00:00:00') : '',
      EndDate: this.filter.EndDate != '' ? moment(this.filter.EndDate).format('YYYY-MM-DDT00:00:00') : ''
    };

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterSearch['InstrumentType'] = '11';
    this.filterSearch['Section'] = this.filterBodyParams.section_ids;
    this.filterBody['section_ids'] = this.filterBodyParams.section_ids.map((section) => {
      return section.id;
    });

    this.filterSearchBody = {
      ...this.filterBody
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters(), this.filterSearchBody);

    this.getReadingsBeachLengthList(this.filterSearch, this.filterSearchBody);
  }

  //Gerencia ações de leitura como edição, exclusão e geração de pacotes.
  actionReadings(option) {
    let filtered = this.readingService.getSelectedReadings(this.tableData, this.formDateTime.controls['date'].value, option);
    if (option == 'patch') {
      this.updateDateTime(filtered);
    } else if (option == 'delete') {
      this.deleteReadings(filtered);
    } else if (option == 'packages') {
      this.generatePackages(filtered);
    }
  }

  //Atualiza a data e hora das leituras selecionadas.
  updateDateTime(params) {
    this.messagesError = [];

    this.readingsServiceApi.patchReadings(params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;

        if (this.typeInstrumentId <= 10 || this.typeInstrumentId == 12 || this.typeInstrumentId == 13) {
          this.searchReading();
        } else {
          this.searchReadingBeachLength();
        }
        this.formDateTime.controls['date'].setValue('');
        this.selectedReadings = 0;
        this.selectedPackages = 0;

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
      }
    );
  }

  //Exclui as leituras selecionadas.
  deleteReadings(params) {
    this.messagesError = [];

    this.readingsServiceApi.deleteReadings(params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.DeleteReadings;
        this.message.status = true;

        if (this.typeInstrumentId <= 10 || this.typeInstrumentId == 12 || this.typeInstrumentId == 13) {
          this.searchReading();
        } else {
          this.searchReadingBeachLength();
        }

        this.selectedReadings = 0;

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
      }
    );
  }

  //Gera pacotes com base nas leituras selecionadas.
  generatePackages(params) {
    this.ngxSpinnerService.show();

    this.messagesError = [];

    this.packagesServiceApi.postPackages(params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.GeneratePackages;
        this.message.status = true;

        if (this.typeInstrumentId <= 10 || this.typeInstrumentId == 12 || this.typeInstrumentId == 13) {
          this.searchReading();
        } else {
          this.searchReadingBeachLength();
        }

        this.selectedReadings = 0;
        this.selectedPackages = 0;

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  //Altera o tipo de instrumento exibido na grid de instrumentos.
  changeTypeInstrument(typeInstrumentsId: any = '', called = '') {
    let instrumentId: any = null;

    typeInstrumentsId = Number.isNaN(typeInstrumentsId) ? '' : typeInstrumentsId;

    if (typeInstrumentsId == this.filter.InstrumentType) {
      this.filter.InstrumentType = '';
      this.typeInstrumentId = '';
      instrumentId = -1;
    } else {
      instrumentId = typeof typeInstrumentsId == 'string' ? parseInt(typeInstrumentsId) : typeInstrumentsId;
      this.filter.InstrumentType = typeInstrumentsId;
      this.typeInstrumentId = typeInstrumentsId;
    }

    if (instrumentId == -1) {
      this.actionCustom[0].option = 'edit';
      this.actionCustom[1].option = 'history';
      this.actionCustom[2].option = 'view';
      this.ctrlInstrument = true;
      if (called != 'managerFilters') {
        this.searchReading();
      }
      this.tableHeader = this.tableHeaderService.getTableHeaderReading(null);

      // Remove a coluna de checkbox da Visualização (label "Selecionar")
      this.tableHeader = this.tableHeader.filter((col: any) => col.label !== 'Selecionar');

      // Seleciona colunas visíveis por padrão
      if (!this.selectedColumns || this.selectedColumns.length === 0) {
        this.selectedColumns = this.tableHeader.filter((col: any) => col.show !== false);
      }
    } else if (instrumentId <= 10 || instrumentId == 12 || instrumentId == 13) {
      this.actionCustom[0].option = 'edit';
      this.actionCustom[1].option = 'history';
      this.actionCustom[2].option = 'view';
      this.ctrlInstrument = true;
      if (called != 'managerFilters') {
        this.searchReading();
      }
      this.tableHeader = this.tableHeaderService.getTableHeaderReading(typeInstrumentsId);

      // Seleciona colunas visíveis por padrão
      if (!this.selectedColumns || this.selectedColumns.length === 0) {
        this.selectedColumns = this.tableHeader.filter((col: any) => col.show !== false);
      }
    } else if (instrumentId == 11) {
      this.actionCustom[0].option = 'editBeachLength';
      this.actionCustom[1].option = 'historyBeachLength';
      this.actionCustom[2].option = 'viewBeachLength';
      this.ctrlInstrument = false;
      this.searchReadingBeachLength();
      this.tableHeader = this.tableHeaderService.getTableHeaderReading(typeInstrumentsId);

      // Seleciona colunas visíveis por padrão
      if (!this.selectedColumns || this.selectedColumns.length === 0) {
        this.selectedColumns = this.tableHeader.filter((col: any) => col.show !== false);
      }
    }
  }

  //Reseta os filtros e limpa as seções e instrumentos selecionados.
  resetFilter() {
    this.hierarchy.resetFilters();

    this.filter = {
      SearchIdentifier: '',
      Identifier: '',
      SectionId: '',
      InstrumentType: '',
      StartDate: '',
      EndDate: ''
    };

    this.sections = [];
    this.instruments = [];

    this.filterParams = {};
    this.filterBody = {};
    this.filterBodyParams.section_ids = [];
    this.filterBodyParams.instrument_ids = [];

    this.filterSearch = {};
    this.filterSearchBody = {};

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters(), this.filterSearchBody);

    this.managerFilters();
  }

  /**
   * Gerencia a aplicação dos filtros na busca de leituras.
   * @param {boolean} [$btn=false] - Indica se o filtro foi acionado por um botão.
   */
  managerFilters($btn = false) {
    if ($btn) {
      if (this.filter.InstrumentType == 11) {
        this.searchReadingBeachLength();
      } else {
        this.searchReading();
        this.changeTypeInstrument('', 'managerFilters');
      }
    } else {
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchReading();
        this.changeTypeInstrument('', 'managerFilters');
      } else {
        this.filterSearch = data.filters;
        this.filterSearchBody = data.filtersBody;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;

        //Formulario do filtro
        this.filter.Identifier = this.filterSearch.Identifier;
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier;
        this.filter.ClientId = this.filterSearch.ClientId;
        this.filter.ClientUnitId = this.filterSearch.ClientUnitId;
        this.filter.StructureId = this.filterSearch.StructureId;
        this.filter.InstrumentType = this.filterSearch.InstrumentType;
        this.filter.StartDate = this.filterSearch.StartDate;
        this.filter.EndDate = this.filterSearch.EndDate;

        this.filterBodyParams.section_ids = this.filterSearch.Section;
        this.filterBodyParams.instrument_ids = this.filterSearch.Instrument;

        let type = typeof this.filter.InstrumentType == 'string' ? parseInt(this.filter.InstrumentType) : this.filter.InstrumentType;
        this.filter.InstrumentType = '';
        this.changeTypeInstrument(type);

        // if (type <= 10 || type == 12 || type == 13 || fn.isEmpty(type)) {
        //   this.getReadingsList(this.filterSearch, this.filterSearchBody);
        // } else {
        //   this.getReadingsBeachLengthList(this.filterSearch, this.filterSearchBody);
        // }
      }

      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
        this.hierarchy.setUnits(data.filtersHierarchy.units);
        this.hierarchy.setStructures(data.filtersHierarchy.structures);
        if (data?.filtersHierarchy?.structures && Array.isArray(data.filtersHierarchy.structures) && data.filtersHierarchy.structures.length > 0) {
          this.getSections(data.filtersHierarchy.structures[0]);
        }

        if (this.filterSearch.Section && this.filterSearch.Section.length > 0) {
          this.filterSearch.Section.forEach((section) => {
            this.getInstruments(section, 'select');
          });
        }
      }
    }
  }

  //Reseta a seleção de itens na tabela.
  resetSelected() {
    this.tableData = this.tableData.map((item) => {
      item['select'] = false;
    });
  }

  /**
   * Gerencia o evento de clique em uma linha da tabela, executando a ação correspondente.
   * @param {any} [$event=null] - O evento de clique.
   */
  clickRowEvent($event: any = null) {
    let idx = -1;

    const actionLabels: any = {
      edit: 'editar'
    };

    const phraseEndings: any = {
      edit: 'esta Leitura.'
    };

    const isRestrictedAction = ['edit'].includes($event.action);
    const hasPermission = this.can($event.action);

    if (isRestrictedAction && !hasPermission) {
      this.showPermissionAlert(`Você não tem permissão para ${actionLabels[$event.action]} ${phraseEndings[$event.action]}`);
      return;
    }

    switch ($event.action) {
      case 'edit':
        this.router.navigate(['readings/' + $event.id + '/edit']);
        break;
      case 'history':
        idx = $event.index;
        this.modalTitle = 'Histórico da leitura - ' + this.tableData[idx].identifier;
        this.historyId = $event.id;
        this.historyType = 'Instrument';
        this.ModalHistory.openModal();
        break;
      case 'view':
        this.router.navigate(['readings/' + $event.id + '/view']);
        break;
      case 'editBeachLength':
        this.router.navigate(['readings/' + $event.id + '/edit'], { queryParams: { praia: true }, queryParamsHandling: 'merge' });
        break;
      case 'historyBeachLength':
        idx = $event.index;
        this.modalTitle = 'Histórico comp. de praia - ' + this.tableData[idx].structure_name + ' - ' + this.tableData[idx].section_name;
        this.historyId = $event.id;
        this.historyType = 'BeachLength';
        this.ModalHistory.openModal();
        break;
      case 'viewBeachLength':
        this.router.navigate(['readings/' + $event.id + '/view'], { queryParams: { praia: true }, queryParamsHandling: 'merge' });
        break;
      case 'checkbox':
        this.managerSelected($event);
        break;
      case 'confirm':
        this.actionReadings('delete');
        break;
      case 'generatePackages':
        this.actionReadings('packages');
        break;
    }
  }

  /**
   * Exibe uma mensagem de alerta na tela por 5 segundos.
   */
  private showPermissionAlert(text: string): void {
    this.message = {
      text,
      status: true,
      class: 'alert-danger'
    };
    setTimeout(() => (this.message.status = false), 5000);
  }

  /**
   * Abre um modal para confirmação ou geração de pacotes.
   * @param {string} action - A ação a ser confirmada.
   * @param {any} [data=null] - Dados adicionais para a ação.
   */
  openModal(action: string, data: any = null) {
    switch (action) {
      case 'confirmDelete':
        this.modalTitle = 'Excluir selecionados';
        this.modalMessage = 'Deseja confirmar a exclusão das leituras selecionadas?';
        this.modalInstruction = `Não é possível excluir apenas uma leitura do ponto de medição de um instrumento. Ao remover uma leitura do ponto de
        medição, todas as leituras do instrumento serão excluídas.`;
        this.modalConfig.iconHeader = 'fa fa-trash-o';
        this.modalConfig.action = 'confirm';
        this.ModalConfirm.openModal();
        break;
      case 'generatePackages':
        this.modalTitle = 'Gerar pacotes';
        this.modalMessage = 'Tem certeza de que deseja forçar a geração de pacotes para os dados selecionados?';
        this.modalInstruction = ``;
        this.modalConfig.iconHeader = 'fa fa-cube';
        this.modalConfig.action = 'generatePackages';
        this.ModalConfirm.openModal();
        break;
    }
  }

  /**
   * Gerencia a seleção de itens na tabela com suporte à seleção múltipla usando a tecla Shift.
   * @param {any} $event - O evento de seleção.
   */
  managerSelected($event) {
    if (!this.shiftPress) {
      this.shiftIds = [];
      this.shiftIds.push($event.index);
    } else {
      if (this.shiftIds.length < 2) {
        this.shiftIds.push($event.index);
      } else {
        this.shiftIds.pop();
        this.shiftIds.push($event.index);
      }
    }

    if (this.shiftIds.length < 2) {
      this.tableData[$event.index].select = !this.tableData[$event.index].select;
    } else {
      let ordIds = this.shiftIds.slice().sort(function (a, b) {
        return a - b;
      });

      this.tableData.forEach((item, idx) => {
        if (idx >= ordIds[0] && idx <= ordIds[1]) {
          this.tableData[idx].select = true;
        } else {
          this.tableData[idx].select = false;
        }
      });
    }

    this.selectedReadings = fn.totalItemInObject(this.tableData, 'select', true);
    this.selectedPackages = fn.totalItemInObject(this.tableData, 'select', true, 'type_instrument', [1, 2, 3]);
  }

  /**
   * Alterna a visibilidade das colunas na grid de visualização.
   * @param {any} $event - O evento de alternância.
   * @param {string} type - O tipo de alternância (selecionar, desselecionar, etc.).
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }

    this.managerFilters();
  }

  //Gerencia o fechamento do banner de notificação.
  handleCloseNotificationBanner() {
    this.showNotificationBanner = false;
    this.notificationService.handleCloseReadingBanner();
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  /**
   * Navega para a página inicial.
   */
  goBack() {
    this.router.navigate(['/']);
  }
}
