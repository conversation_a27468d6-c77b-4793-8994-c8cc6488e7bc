<form [formGroup]="formHistoryInstrument" (ngSubmit)="validate()">
  <!-- <PERSON><PERSON><PERSON> Histórico -->
  <div class="col-md-12 mt-3" *ngIf="tableData.length > 0">
    <label class="form-title mt-2"
      >Histórico de cadastros e edições de instrumentos:</label
    >
    <app-table
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeader"
      [tableData]="tableData"
      [permissaoUsuario]="permissaoUsuario"
    >
    </app-table>

    <!-- Paginação -->
    <app-paginator
      *ngIf="tableData.length > 0"
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event, 'history')"
    ></app-paginator>
  </div>

  <!-- Alertas -->
  <div
    class="alert alert-warning"
    role="alert"
    *ngIf="messageReturnNote.status"
  >
    {{ messageReturnNote.text }}
  </div>
  <!-- Alertas -->

  <!-- Tabela de registros manuais -->
  <div class="col-md-12 mt-3" *ngIf="tableDataNote.length > 0">
    <label class="form-title mt-2">Histórico de registros manuais:</label>
    <app-table
      [messageReturn]="messageReturnNote"
      [tableHeader]="tableHeaderNote"
      [tableData]="tableDataNote"
      [permissaoUsuario]="permissaoUsuario"
      [actionCustom]="actionCustom"
      (sendClickRowEvent)="clickRowEvent($event)"
    >
    </app-table>

    <!-- Paginação -->
    <app-paginator
      [collectionSize]="collectionSizeNote"
      [page]="pageNote"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSizeNote"
      (sendPageChange)="loadPage($event, 'note')"
    ></app-paginator>
  </div>

  <!-- Alertas -->
  <div class="alert alert-success mt-3" role="alert" *ngIf="message.status">
    {{ message.text }}
  </div>

  <div class="row mt-2">
    <app-alert [class]="'alert-danger'" [messages]="messagesError"></app-alert>
  </div>

  <!-- Botão novo registro -->
  <div class="row g-3 mt-1" *ngIf="!ctrlHistory">
    <div class="col-md-2 align-self-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [customBtn]="true"
        [icon]="'fa fa-file-text-o'"
        [label]="'Novo Registro'"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        (click)="ctrlHistory = !ctrlHistory"
      ></app-button>
    </div>
  </div>

  <div class="row mt-3" *ngIf="ctrlHistory">
    <ul class="nav nav-tabs px-2">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page">{{
          !editNote ? 'Novo Registro' : 'Editar Registro'
        }}</a>
      </li>
    </ul>
    <!-- Tipo de Registro -->
    <div class="col-md-2 mt-2">
      <label class="form-label">Tipo de Registro</label>
      <select class="form-select" formControlName="type">
        <option value="">Selecione...</option>
        <option *ngFor="let item of typeRegister" [ngValue]="item.value">
          {{ item.label }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formHistoryInstrument.get('type').valid &&
          formHistoryInstrument.get('type').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Descrição -->
    <div class="col-md-12 mt-2">
      <label class="form-label">Descrição:</label>
      <textarea
        pInputTextArea
        rows="11"
        class="form-control"
        formControlName="description"
        data-ls-module="charCounter"
        maxlength="1000"
        (input)="onValueChange($event)"
      ></textarea>
      <small class="form-text text-secondary"
        >Caracteres {{ counter }} (Máximo: 1000)
      </small>
      <small
        class="form-text text-danger"
        *ngIf="
          !formHistoryInstrument.get('description').valid &&
          formHistoryInstrument.get('description').touched
        "
        >Campo Obrigatório.</small
      >
    </div>
    <!-- Imagem -->
    <div class="col-md-4">
      <label class="form-label">Imagem</label>
      <input
        type="file"
        formControlName="image"
        class="form-control"
        accept=".png,.jpg, .jpeg"
        (change)="uploadFile($event)"
      />
      <i class="fa fa-exclamation-circle me-2 mt-2"></i>
      <em class="form-text mt-2"
        >Formatos de arquivo válidos: .png, .jpg, .jpeg </em
      ><br />
      <small
        class="form-text text-danger"
        *ngIf="
          !formHistoryInstrument.get('image').valid &&
          formHistoryInstrument.get('image').touched
        "
        ><i class="bi bi-x-circle me-2"></i>Formato de arquivo inválido.</small
      >
      <!-- Visualização da imagem -->
      <img
        [src]="fileContentImage"
        class="img-fluid img-thumbnail me-2 mt-2"
        *ngIf="fileContentImage"
        style="max-width: 100%"
      />
      <br />
      <button
        class="btn btn-secondary btn-sm mt-2"
        type="button"
        (click)="
          fileContentImage = '';
          formHistoryInstrument.get('image').setValue(null)
        "
        *ngIf="fileContentImage"
      >
        <i class="fa fa-times"></i>
        Remover Imagem
      </button>
    </div>
    <!-- Upload arquivo -->
    <div class="col-md-4">
      <label class="form-label">Arquivo</label>
      <input
        type="file"
        formControlName="file"
        class="form-control"
        accept=".doc,.docx,.dxf,.dwg,.xls,.xlsx,.csv,.pdf "
        (change)="uploadFile($event)"
      />
      <i class="fa fa-exclamation-circle me-2 mt-2"></i>
      <em class="form-text mt-2"
        >Formatos de arquivo válidos: .csv,.doc, .docx, .dxf, .dwg, .pdf, .xls,
        .xlsx </em
      ><br />
      <div class="col-md-12 d-flex justify-content-center mt-2">
        <a
          [download]="fileNameFile"
          [href]="fileContentDownloadFile"
          class="me-2"
          *ngIf="fileContentFile && editNote"
        >
          <app-button
            [class]="'btn btn-logisoil-blue'"
            [icon]="'fa fa-download'"
            [label]="'Download arquivo: ' + fileNameFile"
            [type]="true"
          ></app-button>
        </a>
        <button
          class="btn btn-secondary btn-sm"
          type="button"
          (click)="
            fileContentFile = '';
            formHistoryInstrument.get('file').setValue(null)
          "
          *ngIf="fileContentFile"
        >
          <i class="fa fa-times"></i>
          Remover Arquivo
        </button>
      </div>
    </div>
  </div>

  <!-- Botões -->
  <div
    class="col-md-12 mt-2 d-flex justify-content-end mb-3"
    *ngIf="ctrlHistory"
  >
    <app-button
      [class]="'btn-logisoil-green'"
      [icon]="'fa fa-thin fa-floppy-disk'"
      [label]="!editNote ? 'Salvar' : 'Editar'"
      [type]="false"
      class="me-1"
      [disabled]="!formHistoryInstrument.valid"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-red'"
      [icon]="'fa fa-thin fa-xmark'"
      [label]="'Cancelar'"
      [type]="true"
      (click)="ctrlHistory = false; resetForm('historyInstrument')"
    >
    </app-button>
  </div>

  <!-- Tabela de Histórico -->
  <div class="col-md-12 mt-3" *ngIf="tableDataNoteHistory.length > 0">
    <label class="form-title mt-2"
      >Históricos dos cadastros e das edições dos registros manuais:</label
    >
    <app-table
      [messageReturn]="messageReturn"
      [tableHeader]="tableHeaderNoteHistory"
      [tableData]="tableDataNoteHistory"
      [permissaoUsuario]="permissaoUsuario"
    >
    </app-table>

    <!-- Paginação -->
    <app-paginator
      [collectionSize]="collectionSizeNoteHistory"
      [page]="pageNoteHistory"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSizeNoteHistory"
      (sendPageChange)="loadPage($event, 'noteHistory')"
    ></app-paginator>
  </div>

  <!-- Alertas -->
  <div
    class="alert alert-warning mt-2"
    role="alert"
    *ngIf="messageReturnNoteHistory.status"
  >
    {{ messageReturnNoteHistory.text }}
  </div>
  <!-- Alertas -->

  <div class="col-md-12 mt-2 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/instruments']"
    ></app-button>
  </div>
</form>
