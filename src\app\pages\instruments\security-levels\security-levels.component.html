<div class="row g-3" *ngIf="!ctrlSecurityLevels">
  <div class="col-md-3 mt-3">
    <div class="d-inline-block">
      <app-button
        [class]="'btn-logisoil-blue'"
        [customBtn]="true"
        [icon]="'fa fa-exclamation-triangle'"
        [label]="'Níveis de Segurança'"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        (click)="ctrlSecurityLevels = !ctrlSecurityLevels"
      ></app-button>
    </div>
  </div>
</div>
<form>
  <div [formGroup]="formSecurityLevels">
    <div class="row g-3 mt-2" *ngIf="ctrlSecurityLevels">
      <!-- Aba Níveis de Segurança  -->
      <ul class="nav nav-tabs px-2">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page">Níveis de Segurança</a>
        </li>
      </ul>
      <div
        class="row"
        *ngIf="
          typeInstrument != 6 &&
          typeInstrument != 7 &&
          typeInstrument != 12 &&
          typeInstrument != 13
        "
      >
        <!-- Nível de Atenção -->
        <div class="col-md-3 warning-level mt-2 pb-3 pt-2">
          <label class="form-text">Nível de Atenção (m):</label>
          <input
            type="text"
            class="form-control"
            step="0.01"
            formControlName="attention"
            min="-9999999999999"
            max="9999999999999"
            maxlength="9999999999999"
            (keypress)="func.controlNumber($event, null, 'notE')"
            (keyup)="
              func.controlNumber($event, formSecurityLevels.get('attention'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <!-- Valor anterior -->
          <small
            class="form-text text-secondary previous"
            *ngIf="_current['attention'] != null"
            >{{ _current['attention'] }}<br
          /></small>
        </div>
        <!-- Nível de Alerta -->
        <div class="col-md-3 alert-level mt-2 pb-3 pt-2">
          <label class="form-text">Nível de Alerta (m):</label>
          <input
            type="text"
            class="form-control"
            step="0.01"
            formControlName="alert"
            autocomplete="off"
            min="-9999999999999"
            max="9999999999999"
            maxlength="9999999999999"
            (keypress)="func.controlNumber($event, null, 'notE')"
            (keyup)="
              func.controlNumber($event, formSecurityLevels.get('alert'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <!-- Valor anterior -->
          <small
            class="form-text text-secondary previous"
            *ngIf="_current['alert'] != null"
            >{{ _current['alert'] }}<br
          /></small>
        </div>
        <!-- Nível de Emergência -->
        <div class="col-md-3 emergency-level mt-2 pb-3 pt-2">
          <label class="form-text">Nível de Emergência (m):</label>
          <input
            type="text"
            class="form-control"
            step="0.01"
            formControlName="emergency"
            autocomplete="off"
            min="-9999999999999"
            max="9999999999999"
            maxlength="9999999999999"
            (keypress)="func.controlNumber($event, null, 'notE')"
            (keyup)="
              func.controlNumber($event, formSecurityLevels.get('emergency'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <!-- Valor anterior -->
          <small
            class="form-text text-secondary previous"
            *ngIf="_current['emergency'] != null"
            >{{ _current['emergency'] }}<br
          /></small>
        </div>
        <!-- Variação Brusca (m) entre leituras -->
        <div class="col-md-3 abrupt-variation mt-2 pb-3 pt-2">
          <label class="form-text">Variação Brusca (m) entre leituras:</label>
          <input
            type="text"
            class="form-control"
            step="0.01"
            formControlName="abrupt_variation_between_readings"
            autocomplete="off"
            min="-9999999999999"
            max="9999999999999"
            maxlength="9999999999999"
            (keypress)="func.controlNumber($event, null, 'notE')"
            (keyup)="
              func.controlNumber(
                $event,
                formSecurityLevels.get('abrupt_variation_between_readings')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <!-- Valor anterior -->
          <small
            class="form-text text-secondary previous"
            *ngIf="_current['abrupt_variation_between_readings'] != null"
            >{{ _current['abrupt_variation_between_readings'] }}<br
          /></small>
        </div>
      </div>
      <!-- Pluviômetro e Pluviógrafo -->
      <div class="row" *ngIf="typeInstrument == 12 || typeInstrument == 13">
        <div class="col-md-3 warning-level mt-2 pb-3 pt-2">
          <label class="form-text"
            >Nível de segurança da pluviometria diária (mm):</label
          >
          <input
            type="text"
            class="form-control"
            formControlName="maximum_daily_rainfall"
            min="0"
            max="9999999999999"
            maxlength="9999999999999"
            (keypress)="
              func.controlNumber(
                $event,
                formSecurityLevels.get('maximum_daily_rainfall'),
                'positiveDecimal'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formSecurityLevels.get('maximum_daily_rainfall')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <!-- Valor anterior -->
          <small
            class="form-text text-secondary previous"
            *ngIf="_current['maximum_daily_rainfall'] != null"
            >{{ _current['maximum_daily_rainfall'] }}<br
          /></small>
        </div>
        <div
          class="col-md-3 abrupt-variation mt-2 pb-3 pt-2"
          *ngIf="typeInstrument == 13"
        >
          <label class="form-text"
            >Nível de segurança da intensidade (mm/h):</label
          >
          <input
            type="text"
            class="form-control"
            formControlName="rain_intensity"
            min="0"
            max="9999999999999"
            maxlength="9999999999999"
            (keypress)="
              func.controlNumber(
                $event,
                formSecurityLevels.get('rain_intensity'),
                'positiveDecimal'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formSecurityLevels.get('rain_intensity')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <!-- Valor anterior -->
          <small
            class="form-text text-secondary previous"
            *ngIf="_current['rain_intensity'] != null"
            >{{ _current['rain_intensity'] }}<br
          /></small>
        </div>
      </div>
      <!-- Marco superficial e Prisma -->
      <div class="row" *ngIf="typeInstrument == 6 || typeInstrument == 7">
        <ng-template ngFor let-axisItem [ngForOf]="axis" let-i="index">
          <!-- Nível de atenção -->
          <div class="col-md-3 warning-level mt-2 pb-3 pt-2">
            <label class="form-text"
              >Nível de Atenção do {{ axisItem.label }} (mm):</label
            >
            <input
              type="text"
              class="form-control"
              step="0.01"
              formControlName="attention_{{ axisItem.value }}"
              min="-9999999999999"
              max="9999999999999"
              maxlength="9999999999999"
              (keypress)="func.controlNumber($event, null, 'notE')"
              (keyup)="
                func.controlNumber(
                  $event,
                  formSecurityLevels.get('attention_' + axisItem.value)
                )
              "
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['attention_' + axisItem.value] != null"
              >{{ _current['attention_' + axisItem.value] }}<br
            /></small>
          </div>
          <!-- Nível de Alerta -->
          <div class="col-md-3 alert-level mt-2 pb-3 pt-2">
            <label class="form-text"
              >Nível de Alerta do {{ axisItem.label }} (mm):</label
            >
            <input
              type="text"
              class="form-control"
              step="0.01"
              formControlName="alert_{{ axisItem.value }}"
              autocomplete="off"
              min="-9999999999999"
              max="9999999999999"
              maxlength="9999999999999"
              (keypress)="func.controlNumber($event, null, 'notE')"
              (keyup)="
                func.controlNumber(
                  $event,
                  formSecurityLevels.get('alert_' + axisItem.value)
                )
              "
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['alert_' + axisItem.value] != null"
              >{{ _current['alert_' + axisItem.value] }}<br
            /></small>
          </div>
          <!-- Nível de Emergência -->
          <div class="col-md-3 emergency-level mt-2 pb-3 pt-2">
            <label class="form-text"
              >Nível de Emergência do {{ axisItem.label }} (mm):</label
            >
            <input
              type="text"
              class="form-control"
              step="0.01"
              formControlName="emergency_{{ axisItem.value }}"
              autocomplete="off"
              min="-9999999999999"
              max="9999999999999"
              maxlength="9999999999999"
              (keypress)="func.controlNumber($event, null, 'notE')"
              (keyup)="
                func.controlNumber(
                  $event,
                  formSecurityLevels.get('emergency_' + axisItem.value)
                )
              "
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="_current['emergency_' + axisItem.value] != null"
              >{{ _current['emergency_' + axisItem.value] }}<br
            /></small>
          </div>
          <!-- Variação Brusca (m) -->
          <div class="col-md-3 abrupt-variation mt-2 pb-3 pt-2">
            <label class="form-text"
              >Variação Brusca (mm) entre leituras do
              {{ axisItem.label }}:</label
            >
            <input
              type="text"
              class="form-control"
              step="0.01"
              formControlName="abrupt_variation_between_readings_{{
                axisItem.value
              }}"
              autocomplete="off"
              min="-9999999999999"
              max="9999999999999"
              maxlength="9999999999999"
              (keypress)="func.controlNumber($event, null, 'notE')"
              (keyup)="
                func.controlNumber(
                  $event,
                  formSecurityLevels.get(
                    'abrupt_variation_between_readings_' + axisItem.value
                  )
                )
              "
              (blur)="func.formatType($event)"
              (focus)="func.formatType($event)"
              appDisableScroll
            />
            <!-- Valor anterior -->
            <small
              class="form-text text-secondary previous"
              *ngIf="
                _current[
                  'abrupt_variation_between_readings_' + axisItem.value
                ] != null
              "
              >{{
                _current['abrupt_variation_between_readings_' + axisItem.value]
              }}<br
            /></small>
          </div>
        </ng-template>
      </div>

      <div class="col-md-12 mt-2 d-flex align-items-end justify-content-end">
        <app-button
          [class]="'btn-logisoil-red'"
          [icon]="'fa fa-thin fa-xmark'"
          [label]="'Cancelar'"
          [type]="true"
          (click)="ctrlSecurityLevels = !ctrlSecurityLevels"
          *ngIf="!view"
        >
        </app-button>
      </div>
    </div>
  </div>
</form>
