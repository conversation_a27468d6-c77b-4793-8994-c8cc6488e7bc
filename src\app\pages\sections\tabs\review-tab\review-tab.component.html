<form class="row g-3">
  <div class="col-md-3">
    <app-button
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Nova Revisão Seção'"
      data-bs-toggle="tooltip"
      data-bs-placement="bottom"
      (click)="resetForm('review', !view)"
      [disabled]="view"
      *ngIf="!editReview"
    >
    </app-button>
  </div>
</form>

<!-- Banner de comprimento da revisão -->
<div class="row mt-2" *ngIf="messageReviewsConsistent.status && edit">
  <div class="col-md-12">
    <div
      class="alert"
      [ngClass]="messageReviewsConsistent.class"
      [innerHTML]="messageReviewsConsistent.text"
    ></div>
  </div>
</div>

<form class="mt-2" [formGroup]="formReview">
  <div *ngIf="ctrlReview">
    <div class="row" *ngIf="message.status">
      <div class="col-md-12">
        <div
          class="alert"
          [ngClass]="message.class"
          [innerHTML]="message.text"
        ></div>
      </div>
    </div>

    <ul class="nav nav-tabs px-2">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page"
          >{{ editReview ? 'Editar' : 'Nova' }} Revisão</a
        >
      </li>
    </ul>

    <!-- Alerta Revisão "em obra" -->
    <div
      class="row mt-2"
      *ngIf="editReview && formReview.get('is_under_construction')?.value"
    >
      <div class="col-md-12">
        <div class="alert alert-info" role="alert">
          Esta revisão foi designada como "Seção de obra".
        </div>
      </div>
    </div>

    <div class="row mt-2">
      <!-- Data e hora -->
      <div class="col-md-3">
        <label class="form-label">Data e hora de início:</label>
        <input
          type="datetime-local"
          class="form-control"
          formControlName="start_date"
        />
      </div>

      <!-- Tipo de seção -->
      <div class="col-md-9">
        <label class="form-label">Tipo de seção:</label>
        <select class="form-select" formControlName="structure_type">
          <option value="">
            É necessário selecionar uma Estrutura na Aba "Geral"
          </option>
          <option *ngFor="let item of structureTypeList" [ngValue]="item.id">
            {{ item.name }}
          </option>
        </select>
        <small
          class="form-text text-danger"
          *ngIf="
            !formReview.get('structure_type').valid &&
            formReview.get('structure_type').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>

    <!-- Seção de Obra -->
    <div class="row mt-2">
      <div class="col-md-2" *ngIf="!editReview">
        <input
          class="form-check-input me-1"
          formControlName="is_under_construction"
          type="checkbox"
          value=""
        />
        <label class="form-label">Seção de Obra</label>
      </div>
    </div>

    <!-- DXF -->
    <div *ngIf="!formReview.get('is_under_construction')?.value">
      <div class="row mt-2">
        <!-- Upload DXF -->
        <div class="col-md-12">
          <label class="form-label">Upload documento .dxf (Opcional):</label>
          <input
            type="file"
            class="form-control"
            formControlName="drawing"
            accept=".dxf"
            (change)="uploadFile($event)"
            #fileInput
          />
          <small
            class="form-text text-danger"
            *ngIf="
              !formReview.get('drawing').valid &&
              formReview.get('drawing').touched
            "
            ><i class="bi bi-x-circle me-2"></i>Formato de arquivo inválido.
            <br
          /></small>
          <small
            class="form-text text-danger"
            *ngIf="!formReview.get('drawing_size').valid"
            ><i class="bi bi-x-circle me-2"></i>Excedido o tamanho máximo
            permitido.<br
          /></small>
          <em class="form-text">Tamanho máximo permitido: 10MB. </em>
        </div>
      </div>

      <!-- Download DXF -->
      <div class="row mt-2" *ngIf="fileContentView">
        <div class="col-md-12 d-flex justify-content-center">
          <a
            [download]="fileNameView"
            [href]="fileContentDownloadView"
            class="me-2"
          >
            <app-button
              [class]="'btn btn-logisoil-blue'"
              [icon]="'fa fa-download'"
              [label]="'Download arquivo: ' + fileNameView"
              [type]="true"
            >
            </app-button>
          </a>
          <button
            class="btn btn-gray btn-sm"
            type="button"
            (click)="clearDrawing()"
          >
            <i class="fa fa-times me-2"></i>
            Remover arquivo
          </button>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-12">
          <app-dxf-viewer
            [fileDxf]="fileDXFView"
            [idCanvas]="'canvasDXFReview'"
          ></app-dxf-viewer>
        </div>
      </div>
    </div>

    <div class="row mt-2">
      <!-- Descrição -->
      <div class="col-md-12">
        <label class="form-label">Descrição (Opcional):</label>
        <textarea
          pInputTextArea
          rows="2"
          class="form-control"
          formControlName="description"
          maxlength="100"
          (input)="onValueChange('description', $event)"
        ></textarea>
      </div>
      <!-- Contador de caracteres -->
      <small class="form-text text-muted d-block">
        Caracteres {{ charCounts['description'] || 0 }} de 100
      </small>
    </div>
    <small
      class="form-text text-danger"
      *ngIf="
        !formReview.get('description').valid &&
        formReview.get('description').touched
      "
      ><i class="fa fa-exclamation-circle me-2"></i>Limite de caracteres:
      100.</small
    >

    <!-- Banner de comprimento da etapa de obra -->
    <div class="row mt-3" *ngIf="messageConstructionStagesConsistent.status">
      <div class="col-md-12">
        <div
          class="alert"
          [ngClass]="messageConstructionStagesConsistent.class"
          [innerHTML]="messageConstructionStagesConsistent.text"
        ></div>
      </div>
    </div>

    <!-- Etapas de obra -->
    <div
      class="mt-2"
      *ngIf="
        formReview.get('is_under_construction')?.value &&
        tableDataStage.length > 0
      "
    >
      <app-table
        [tableHeader]="tableHeaderStage"
        [tableData]="tableDataStage"
        [actionCustom]="actionCustomStage"
        (sendClickRowEvent)="clickRowEvent($event)"
      >
      </app-table>
    </div>

    <!-- Nova Etapa de Obra -->
    <div
      class="row mt-3"
      *ngIf="formReview.get('is_under_construction')?.value"
    >
      <div class="col-md-12">
        <ul class="nav nav-tabs px-2">
          <li class="nav-item">
            <a class="nav-link active" aria-current="page"
              >Nova Etapa de Obra</a
            >
          </li>
        </ul>
      </div>

      <form [formGroup]="formConstructionStage">
        <!-- Etapa -->
        <div class="row mt-2">
          <div class="col-md-3">
            <label class="form-label">Etapa:</label>
            <input type="text" class="form-control" formControlName="stage" />
          </div>

          <!-- Descrição -->
          <div class="col-md-9">
            <label class="form-label">Descrição (opcional):</label>
            <input
              type="text"
              class="form-control"
              formControlName="description"
              data-ls-module="charCounter"
              maxlength="100"
              (input)="onValueChangeStage('description', $event)"
            />
            <small class="form-text text-muted d-block">
              Caracteres {{ charCountsStage['description'] || 0 }} de
              {{ stageMaxlength }}
            </small>
          </div>

          <small
            class="form-text text-danger"
            *ngIf="
              !formReview.get('description').valid &&
              formReview.get('description').touched
            "
            ><i class="fa fa-exclamation-circle me-2"></i>Limite de caracteres:
            100.</small
          >
        </div>

        <!-- Upload DXF da Etapa (Opcional) -->
        <div class="row mt-2">
          <div class="col-md-12 mt-2">
            <label class="form-label"
              >Upload DXF da Etapa de Obra (Opcional):</label
            >
            <input
              type="file"
              class="form-control"
              accept=".dxf"
              (change)="onUploadStageFile($event)"
              #fileInputStage
            />
            <em class="form-text">Tamanho máximo permitido: 10MB. </em>
          </div>
        </div>

        <!-- Feedback visual do DXF -->
        <div class="row mt-2" *ngIf="stageFileContentView">
          <div class="col-md-12 d-flex justify-content-center">
            <a
              [download]="stageFileNameView"
              [href]="stageFileContentDownloadView"
              class="me-2"
            >
              <app-button
                [class]="'btn btn-logisoil-blue'"
                [icon]="'fa fa-download'"
                [label]="'Download arquivo: ' + stageFileNameView"
                [type]="true"
              >
              </app-button>
            </a>
            <button
              class="btn btn-gray btn-sm"
              type="button"
              (click)="clearStageDrawing()"
            >
              <i class="fa fa-times me-2"></i>
              Remover arquivo
            </button>
          </div>
        </div>

        <!-- Visualizador DXF -->
        <div class="row mt-2 mb-3">
          <div class="col-md-12">
            <app-dxf-viewer
              [fileDxf]="stageFileDXFView"
              [idCanvas]="'canvasDXFStage'"
            ></app-dxf-viewer>
          </div>
        </div>

        <!-- Alerta -->
        <div class="row" *ngIf="messageStage.status">
          <div class="col-md-12">
            <div
              class="alert"
              [ngClass]="messageStage.class"
              [innerHTML]="messageStage.text"
            ></div>
          </div>
        </div>

        <!-- Adicionar etapa de obra -->
        <div class="row">
          <div class="col-md-12 d-flex justify-content-end">
            <app-button
              [class]="'btn-logisoil-blue'"
              [icon]="'fa fa-plus-circle'"
              [label]="'Adicionar Etapa de Obra'"
              [type]="true"
              (click)="addNewConstructionStage()"
              *ngIf="editingStageIndex == null"
            ></app-button>
            <app-button
              [class]="'btn-logisoil-blue'"
              [icon]="'fa fa-pencil'"
              [label]="'Editar Etapa de Obra'"
              [type]="true"
              *ngIf="editingStageIndex != null"
              (click)="confirmEditConstructionStage()"
            ></app-button>
            <app-button
              [class]="'btn-logisoil-red'"
              [icon]="'fa fa-thin fa-xmark'"
              [label]="'Cancelar'"
              [type]="true"
              class="ms-1"
              *ngIf="editingStageIndex != null"
              (click)="cancelEditConstructionStage()"
            >
            </app-button>
          </div>
        </div>
      </form>
    </div>

    <hr />

    <!-- Botões -->
    <div class="row mt-3">
      <div class="col-md-12 d-flex justify-content-end">
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fas fa-plus-circle'"
          [label]="'Adicionar Revisão'"
          [type]="true"
          class="me-1"
          [disabled]="!formReview.valid"
          (click)="addReviewSection()"
          *ngIf="!editReview"
          [disabled]="view"
        >
        </app-button>
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-floppy-disk'"
          [label]="'Salvar modificações'"
          [type]="true"
          class="me-1"
          [disabled]="!formReview.valid"
          (click)="editReviewSection()"
          *ngIf="editReview"
          [disabled]="view"
        >
        </app-button>
        <app-button
          [class]="'btn-logisoil-red'"
          [icon]="'fa fa-thin fa-xmark'"
          [label]="'Cancelar'"
          [type]="true"
          class="me-1"
          (click)="ctrlReview = false; resetForm('review')"
        >
        </app-button>
      </div>
    </div>
  </div>
  <small class="form-text text-danger" *ngIf="reviewsValidate"
    >É necessário adicionar ao menos uma revisão.</small
  >
</form>

<ul class="nav nav-tabs px-2 mt-4">
  <li class="nav-item">
    <a class="nav-link active" aria-current="page">Histórico de Revisões</a>
  </li>
</ul>

<div class="alert alert-warning mt-2" role="alert" *ngIf="messageReturn.status">
  {{ messageReturn.text }}
</div>

<!-- Histórico de Revisões -->
<div class="mt-2">
  <app-table
    [messageReturn]="messageReturn"
    [tableHeader]="tableHeader"
    [tableData]="tableData"
    [permissaoUsuario]="permissaoUsuario"
    [actionCustom]="actionCustom"
    [detail]="this.view"
    [eventRow]="this.view"
    (sendClickRowEvent)="clickRowEvent($event)"
  >
  </app-table>
</div>

<!-- Visualizar DXF -->
<app-modal-view-dxf
  [title]="'DXF'"
  #modalViewDxf
  [dxfInfo]="dxfInfo"
></app-modal-view-dxf>

<!-- Confirmar exclusao da etapa de obra -->
<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
  [data]="modalData"
></app-modal-confirm>

<app-modal-standard
  #modalStandard
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitleStandard"
  [message]="modalMessageStandard"
  [instruction]="modalInstructionStandard"
  [modalConfig]="modalConfigStandard"
  [data]="modalDataStandard"
></app-modal-standard>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
