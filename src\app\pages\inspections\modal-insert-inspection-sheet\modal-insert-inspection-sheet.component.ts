import { Component, ElementRef, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';

import { InsertInspectionSheet } from 'src/app/constants/inspections.constants';

@Component({
  selector: 'app-modal-insert-inspection-sheet',
  templateUrl: './modal-insert-inspection-sheet.component.html',
  styleUrls: ['./modal-insert-inspection-sheet.component.scss']
})
export class ModalInsertInspectionSheetComponent implements OnInit {
  @Output() public sendClickEvent = new EventEmitter();
  @ViewChild('modalInsertInspectionSheet') modalInsertInspectionSheet: ElementRef;

  public modalRef: any = NgbModalRef;

  public insertInspectionSheet = InsertInspectionSheet;
  public selectedOption: any;

  constructor(private modalService: NgbModal, private router: Router) {}

  ngOnInit(): void {}

  openModal() {
    this.modalService.open(this.modalInsertInspectionSheet);
  }

  clickRowEvent(action: any = null) {
    this.sendClickEvent.emit(action);
  }

  create() {}
}
