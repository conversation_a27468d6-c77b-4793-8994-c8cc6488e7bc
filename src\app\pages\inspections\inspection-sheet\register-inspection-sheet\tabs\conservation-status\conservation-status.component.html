<div class="row mt-2" [formGroup]="conservationStatusForm">
  <div class="col-md-12">
    <p *ngIf="!view">
      Assinalar peso correspondente. No caso de peso 10 em qualquer coluna,
      proceder inspeção especial.
    </p>
    <table class="table table-bordered table-hover align-middle">
      <thead class="table-light">
        <tr>
          <!-- Cabeçalhos das colunas -->
          <th *ngFor="let key of getColumnKeys()" style="width: 20%">
            {{ environmentalConservationStatus[key].name }}
          </th>
        </tr>
      </thead>
      <tbody>
        <!-- <PERSON><PERSON> da tabela -->
        <tr *ngFor="let weightIndex of getMaxWeights()">
          <!-- Células com inputs de radio -->
          <td
            *ngFor="let key of getColumnKeys()"
            [style.background-color]="
              environmentalConservationStatus[key].weight[weightIndex]
                ?.value === conservationStatusForm.get(key).value
                ? environmentalConservationStatus[key].weight[weightIndex]
                    ?.color
                : ''
            "
            (click)="
              selectRadio(
                key,
                environmentalConservationStatus[key].weight[weightIndex]?.value
              )
            "
          >
            <span
              *ngIf="environmentalConservationStatus[key]?.weight[weightIndex]"
              ><div>
                <label>{{
                  environmentalConservationStatus[key].weight[weightIndex]
                    ?.description
                }}</label>
              </div>
              <div>
                <input
                  type="radio"
                  [value]="
                    environmentalConservationStatus[key].weight[weightIndex]
                      ?.value
                  "
                  [formControlName]="key"
                  (change)="onFormChange(key)"
                  (click)="$event.stopPropagation()"
                  [disabled]="view"
                />
                <label class="ms-2"
                  >({{
                    environmentalConservationStatus[key].weight[weightIndex]
                      ?.value
                  }})</label
                >
              </div></span
            >
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
