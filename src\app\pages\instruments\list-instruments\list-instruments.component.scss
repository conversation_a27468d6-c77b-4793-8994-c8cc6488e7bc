.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.button-instrument {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-control {
  border-color: #d4d2d2;
  font-size: 0.875em;
}

.form-select {
  font-size: 0.875em !important;
  line-height: 1.52857143 !important;
}

.list-instruments,
.list-group,
.legend {
  font-size: 0.875em;
}

.list-group {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  li {
    white-space: nowrap;
  }
  li + li {
    margin-left: 8px;
  }
}

.range {
  width: 100%;
  margin-top: 4px;
  margin-bottom: 20px;
  cursor: pointer;
}

.group-iw {
  display: flex;
  div + div {
    margin-left: 5px;
  }
  justify-content: center;
}

.maps.show {
  animation: showMap 1s ease 0s 1 normal forwards;
}

@keyframes showMap {
  0% {
    opacity: 0;
    transform: rotateX(-100deg);
    transform-origin: top;
    height: 0px;
  }
  100% {
    opacity: 1;
    transform: rotateX(0deg);
    transform-origin: top;
    height: 580px;
  }
}
