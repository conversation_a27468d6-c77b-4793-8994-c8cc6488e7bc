import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  Renderer2,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import { Datum, coordinateFormat, zoneLetterUTM, zoneNumberUTM, MultiSelectDefault } from 'src/app/constants/app.constants';
import { accessLevel as accessLevelPermission } from 'src/app/constants/permissions.constants';
import { groupInstruments } from 'src/app/constants/instruments.constants';
import { MessagePadroes } from 'src/app/constants/message.constants';

import { ClientService } from 'src/app/services/api/client.service';
import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { CoordinateConversionsService } from 'src/app/services/api/coordinateConversions';
import { CoordinateService } from 'src/app/services/coordinate.service';

import { ImagesService as ImagesServiceApi } from 'src/app/services/api/image.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { StructuresService } from 'src/app/services/api/structure.service';
import { DxfService } from 'src/app/services/dxf.service';

import { MapLine } from 'src/app/constants/app.constants';
import { GoogleMapsComponent } from 'src/app/components/google-maps/google-maps.component';

import fn from 'src/app/utils/function.utils';

import { DOCUMENT } from '@angular/common';
import { Subscription } from 'rxjs';
import { SharedService } from 'src/app/services/shared.service';

@Component({
  selector: 'app-general-tab',
  templateUrl: './general-tab.component.html',
  styleUrls: ['./general-tab.component.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class GeneralTabComponent implements OnInit, AfterViewInit {
  @ViewChild('mapSections', { static: false }) mapSections: GoogleMapsComponent;
  @ViewChild('modalComponents') ModalComponents: any;

  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  @Output() public sendStructure = new EventEmitter();

  public formSection: FormGroup = new FormGroup({
    id: new FormControl({ value: null, disabled: true }),
    name: new FormControl(null, [Validators.required]),
    client: new FormControl('', [Validators.required]),
    client_unit: new FormControl('', [Validators.required]),
    structure: new FormControl('', [Validators.required]),
    is_skew: new FormControl(false),
    normal_line_azimuth: new FormControl(null, [Validators.required]),
    skew_line_azimuth: new FormControl(''),
    datum: new FormControl({ value: '', disabled: true }), //Campo bloqueado
    instruments: new FormControl([]),
    map_line_setting_type: new FormControl(2, [Validators.required]),
    map_line_setting_color: new FormControl('#ffffff', [Validators.required]),
    map_line_setting_width: new FormControl(3, [Validators.required]),

    //Montante
    upstream_coordinate_format: new FormControl(null, [Validators.required]),
    upstream_zone_number: new FormControl({ value: '', disabled: true }, [Validators.required]),
    upstream_zone_letter: new FormControl({ value: '', disabled: true }, [Validators.required]),
    upstream_northing: new FormControl({ value: null, disabled: true }, [Validators.required]),
    upstream_easting: new FormControl({ value: null, disabled: true }, [Validators.required]),
    upstream_latitude: new FormControl({ value: null, disabled: true }, [Validators.required]),
    upstream_longitude: new FormControl({ value: null, disabled: true }, [Validators.required]),

    //Jusante
    downstream_coordinate_format: new FormControl(null, [Validators.required]),
    downstream_zone_number: new FormControl({ value: '', disabled: true }, [Validators.required]),
    downstream_zone_letter: new FormControl({ value: '', disabled: true }, [Validators.required]),
    downstream_northing: new FormControl({ value: null, disabled: true }, [Validators.required]),
    downstream_easting: new FormControl({ value: null, disabled: true }, [Validators.required]),
    downstream_latitude: new FormControl({ value: null, disabled: true }, [Validators.required]),
    downstream_longitude: new FormControl({ value: null, disabled: true }, [Validators.required]),

    //Intermediário
    midpoint_coordinate_format: new FormControl(null),
    midpoint_zone_number: new FormControl({ value: '', disabled: true }),
    midpoint_zone_letter: new FormControl({ value: '', disabled: true }),
    midpoint_northing: new FormControl({ value: null, disabled: true }),
    midpoint_easting: new FormControl({ value: null, disabled: true }),
    midpoint_latitude: new FormControl({ value: null, disabled: true }),
    midpoint_longitude: new FormControl({ value: null, disabled: true }),

    //Profundidade
    minimum_drained_depth: new FormControl('', [Validators.required]),
    minimum_undrained_depth: new FormControl('', [Validators.required]),
    minimum_pseudo_static_depth: new FormControl('', [Validators.required]),

    coordinate_valid: new FormControl(true, [Validators.required])
  });

  public clients: any = [];
  public units: any = [];
  public structures: any = [];
  public instruments: any = [];
  public instrumentsOnline: any = [];

  public datum: any = Datum;
  public zoneLetterUTM: any = zoneLetterUTM;
  public zoneNumberUTM: any = zoneNumberUTM;

  public coordinateFormatSel: any;
  public coordinateFormatSelected: string;
  public coordinateFormatString: string;
  public coordinateFormatList: any = {
    upstream: coordinateFormat,
    downstream: coordinateFormat,
    midpoint: coordinateFormat
  };

  public instrumentsDisabled = false;

  public filter: any = { ClientId: '', ClientUnitId: '' };
  public charCounts: { [key: string]: number } = {};
  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public func = fn;

  public dataMapsSection = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: [
      {
        path: [],
        strokeColor: '#ffffff',
        strokeOpacity: 0,
        strokeWeight: 3,
        icons: [
          {
            icon: {
              path: 'M 0,-1 0,1',
              strokeOpacity: 1,
              scale: 4,
              strokeWeight: 3
            },
            offset: '0',
            repeat: '20px'
          }
        ]
      }
    ]
  };

  public showInfoWindow: boolean = false;

  public instrumentSettings = MultiSelectDefault.Instruments;

  public mapLineSetting = MapLine;

  public showColorPicker: boolean = false;
  public selectedColor: string = '#ffffff';

  //Exibir instrumentos no mapa
  public groupInstruments: any = groupInstruments;
  public intervalId: any = null;
  public document: any = null;
  public clickEventsubscription: Subscription;

  public conditions: any = {
    drained: false,
    pseudo_static: false,
    undrained: false
  };

  public configModal: any = null;
  public titleModal: string = '';

  public fileContent: string = '';
  public fileName: string = '';
  public fileDXF: any = null;

  public mapTabConfig: any = { styleColor: false, active: true };
  public dxfTabConfig: any = { styleColor: false, active: false };

  constructor(
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private clientService: ClientService,
    private clientUnitService: ClientUnitService,
    private coordinateConversionsService: CoordinateConversionsService,
    private coordinateService: CoordinateService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private sectionsServiceApi: SectionsServiceApi,
    private structuresService: StructuresService,
    @Inject(DOCUMENT) documentDom: Document,
    private renderer: Renderer2,
    private element: ElementRef,
    private sharedService: SharedService,
    private imagesServiceApi: ImagesServiceApi,
    private dxfService: DxfService
  ) {
    this.document = documentDom;
    this.clickEventsubscription = this.sharedService.getClickEvent().subscribe((instrument) => {
      this.eventMap(instrument);
    });
  }

  /**
   * Inicializa o componente, carregando os clientes, validando o acesso e verificando a validação de inclinação.
   */
  ngOnInit(): void {
    this.getClients();
    this.validateAccess();
    this.isSkewValidate();

    this.loadFilter(this.formSection, 'client', 'client_unit', 'structure', true);
  }

  ngAfterViewInit() {
    // Força o alinhamento e estilo dos elementos
    const headers = document.querySelectorAll('.card-header .form-check-label');
    headers.forEach((header) => {
      (header as HTMLElement).style.textAlign = 'left';
      (header as HTMLElement).style.fontSize = '0.875rem';
    });

    // Garante que as mudanças no DOM sejam refletidas
    this.cdr.detectChanges();
  }

  //Carrega a lista de clientes a partir da API e atualiza a lista no componente.
  getClients() {
    this.clientService.getClientsList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      // Filtrar clientes ativos
      this.clients = dados;
    });
  }

  /**
   * Carrega as unidades com base no cliente selecionado ou redefine os valores caso não haja cliente.
   * @param {string} clientId - O ID do cliente selecionado.
   */
  getUnits(clientId: string) {
    this.conditions = {
      drained: false,
      pseudo_static: false,
      undrained: false
    };

    if (clientId) {
      this.clientUnitService.getClientUnitsId({ clientId: clientId, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;

        // Filtrar unidades ativas
        this.units = dados;
      });
    } else {
      this.units = [];
      this.structures = [];
      this.instruments = [];
      this.instrumentsOnline = [];
      this.resetCoordinates();

      this.formSection.get('client_unit').setValue('');
      this.formSection.get('structure').setValue('');
      this.formSection.get('instruments').setValue([]);

      this.mapSections.clearMap();
    }
  }

  /**
   * Obtém as unidades de um cliente específico pelo seu ID.
   * @param {string} clientId - O ID do cliente.
   */
  getUnitsId(clientId: string) {
    this.clientUnitService.getClientUnitsById(clientId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.formSection.get('client').setValue(dados.client.id);
      this.getUnits(dados.client.id);
    });
  }

  /**
   * Carrega a lista de estruturas de uma unidade cliente específica ou redefine os valores caso não haja unidade.
   * @param {string} clientUnitId - O ID da unidade cliente.
   */
  getStructureList(clientUnitId: string) {
    this.conditions = {
      drained: false,
      pseudo_static: false,
      undrained: false
    };

    if (clientUnitId) {
      this.structuresService.getStructureList({ clientUnitId: clientUnitId, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.structures = dados;
      });
    } else {
      this.structures = [];
      this.instruments = [];
      this.instrumentsOnline = [];

      this.formSection.get('structure').setValue('');
      this.formSection.get('instruments').setValue([]);

      this.resetCoordinates();
      this.mapSections.clearMap();
    }
  }

  /**
   * Obtém a lista de instrumentos de um tipo específico com base na estrutura selecionada.
   * @param {string} structureId - O ID da estrutura selecionada.
   */
  getListTypeInstruments(structureId: string) {
    this.instruments = [];
    this.instrumentsOnline = [];

    this.instrumentsServiceApi.getGroupInstrumentsMaps({ StructureId: structureId }).subscribe((resp) => {
      let dados: any = resp;

      if (resp['status'] == 200) {
        dados = dados.body === undefined ? dados : dados.body;

        this.instruments = dados;
        this.instrumentsOnline = fn.filterByKeyAndValue(this.instruments, 'online', true);

        setTimeout(() => {
          this.plotInstruments();
        }, 500);

        setTimeout(() => {
          this.selectedGroupInstruments();
        }, 1000);
      }
    });
  }

  //Reseta as coordenadas dos campos no formulário.
  resetCoordinates() {
    this.formSection.get('datum').setValue('');
  }

  /**
   * Converte as coordenadas entre diferentes sistemas de referência.
   * @param {string} [type=''] - O tipo de conversão a ser aplicada.
   */
  /**
   * Converte as coordenadas entre diferentes sistemas de referência.
   * @param {string} [type=''] - O tipo de conversão a ser aplicada.
   */
  coordinatesConversion(type: string = '') {
    this.coordinateService.coordinatesConversion(this.formSection, type).subscribe((coordinates) => {
      if (coordinates !== null && coordinates.hasOwnProperty('hasError') && coordinates.hasError) {
        const error = coordinates.error;
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        setTimeout(() => {
          this.messagesError = [];
        }, 4000);

        this.formSection.controls['coordinate_valid'].setValue('');
      } else {
        if (coordinates !== null) {
          const total = this.formSection.get('is_skew').value ? 3 : 2;

          if (coordinates.type === 'UTM') {
            this.formSection.get(`${type}_zone_letter`).setValue(coordinates.zone_letter);
            this.formSection.get(`${type}_zone_number`).setValue(coordinates.zone_number);
            this.formSection.get(`${type}_northing`).setValue(coordinates.northing);
            this.formSection.get(`${type}_easting`).setValue(coordinates.easting);
          } else if (coordinates.type === 'Geodetic') {
            this.formSection.get(`${type}_latitude`).setValue(coordinates.latitude);
            this.formSection.get(`${type}_longitude`).setValue(coordinates.longitude);
          }

          const idx = type === 'upstream' ? 0 : type === 'downstream' ? 1 : 2;

          // Conversão para SIRGAS
          if (this.formSection.get('datum').value !== 2) {
            const params = {
              decimal_geodetic: {
                latitude: this.formSection.get(`${type}_latitude`).value,
                longitude: this.formSection.get(`${type}_longitude`).value
              },
              input_datum: this.formSection.get('datum').value,
              output_datum: 2
            };

            this.coordinateConversionsService.postCoordinateDatum(params).subscribe((resp) => {
              let dados: any = resp;
              dados = dados.body === undefined ? dados : dados.body;

              this.ensurePolyline(idx);

              this.dataMapsSection.polylines[0].path[idx] = {
                lat: dados.latitude,
                lng: dados.longitude
              };

              if (this.dataMapsSection.polylines[0].path.length >= total) {
                this.sendDataMap('polylines');
              }
            });
          } else {
            this.ensurePolyline(idx);

            this.dataMapsSection.polylines[0].path[idx] = {
              lat: this.formSection.get(`${type}_latitude`)?.value ?? 0,
              lng: this.formSection.get(`${type}_longitude`)?.value ?? 0
            };

            if (this.dataMapsSection.polylines[0].path.length >= total) {
              this.sendDataMap('polylines');
            }
          }

          this.formSection.controls['coordinate_valid'].setValue(true);
        }
      }
    });
  }

  /**
   * Altera o formato de coordenadas com base no formato selecionado.
   * @param {any} coordinateFormat - O formato de coordenada selecionado.
   * @param {string} [type=''] - O tipo de coordenada (upstream, downstream, etc.).
   */
  onCoordinateFormatChange(coordinateFormat: any, type: string = '') {
    this.getSelectedCoordinateFormat(type);
  }

  /**
   * Obtém e aplica o formato de coordenada selecionado.
   * @param {string} [type=''] - O tipo de coordenada (upstream, downstream, etc.).
   */
  getSelectedCoordinateFormat(type: string = '') {
    if (!this.view) {
      this.coordinateFormatSel = coordinateFormat.find(
        (item) =>
          item.id ===
          (typeof this.formSection.get(type + '_' + 'coordinate_format').value == 'string'
            ? parseInt(this.formSection.get(type + '_' + 'coordinate_format').value)
            : '')
      );

      this.coordinateFormatString = this.coordinateFormatSel !== undefined ? this.coordinateFormatSel.value : '';

      if (this.coordinateFormatString === 'UTM') {
        this.formSection.get(type + '_' + 'northing').enable();
        this.formSection.get(type + '_' + 'easting').enable();

        this.formSection.get(type + '_' + 'latitude').disable();
        this.formSection.get(type + '_' + 'longitude').disable();
      } else if (this.coordinateFormatString === 'Decimal Geodetic') {
        this.formSection.get(type + '_' + 'latitude').enable();
        this.formSection.get(type + '_' + 'longitude').enable();

        this.formSection.get(type + '_' + 'zone_number').disable();
        this.formSection.get(type + '_' + 'zone_letter').disable();
        this.formSection.get(type + '_' + 'northing').disable();
        this.formSection.get(type + '_' + 'easting').disable();
      }
    }
  }

  /**
   * Atualiza a posição do mapa com base nas configurações de coordenadas fornecidas.
   * @param {any} coordinateSetting - As configurações de coordenadas.
   * @param {string} [name=''] - O nome associado às coordenadas.
   */
  updatePositionMaps(coordinateSetting: any, name: string = ''): void {
    this.dataMapsSection.zoom = 16;
    this.dataMapsSection.markers[0].title = name;
    if (coordinateSetting.datum !== 2) {
      let params = {
        decimal_geodetic: {
          latitude: coordinateSetting.coordinate_systems.decimal_geodetic.latitude,
          longitude: coordinateSetting.coordinate_systems.decimal_geodetic.longitude
        },
        input_datum: coordinateSetting.datum,
        output_datum: 2 //Sirgas
      };
      this.coordinateConversionsService.postCoordinateDatum(params).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.dataMapsSection.center.lat = dados.latitude;
        this.dataMapsSection.center.lng = dados.longitude;
        this.dataMapsSection.markers[0].position.lat = dados.latitude;
        this.dataMapsSection.markers[0].position.lng = dados.longitude;
        this.sendDataMap('markers');
      });
    } else {
      this.dataMapsSection.center.lat = coordinateSetting.coordinate_systems.decimal_geodetic.latitude;
      this.dataMapsSection.center.lng = coordinateSetting.coordinate_systems.decimal_geodetic.longitude;
      this.dataMapsSection.markers[0].position.lat = coordinateSetting.coordinate_systems.decimal_geodetic.latitude;
      this.dataMapsSection.markers[0].position.lng = coordinateSetting.coordinate_systems.decimal_geodetic.longitude;
      this.sendDataMap('markers');
    }
  }

  setCoordinatesByStructure(coordinateSetting: any) {
    const utm = coordinateSetting.coordinate_systems.utm;

    this.formSection.get('upstream_zone_number').setValue(utm.zone_number);
    this.formSection.get('upstream_zone_letter').setValue(utm.zone_letter);
    this.formSection.get('upstream_coordinate_format').setValue('2');

    this.getSelectedCoordinateFormat('upstream');

    this.formSection.get('downstream_zone_number').setValue(utm.zone_number);
    this.formSection.get('downstream_zone_letter').setValue(utm.zone_letter);
    this.formSection.get('downstream_coordinate_format').setValue('2');

    this.getSelectedCoordinateFormat('downstream');

    this.formSection.get('midpoint_zone_number').setValue(utm.zone_number);
    this.formSection.get('midpoint_zone_letter').setValue(utm.zone_letter);
    this.formSection.get('midpoint_coordinate_format').setValue('2');

    this.getSelectedCoordinateFormat('midpoint');
  }

  /**
   * Obtém a estrutura com base no evento selecionado e atualiza os instrumentos e coordenadas.
   * @param {any} $event - O evento contendo a estrutura selecionada.
   */
  getStructure($event: any) {
    this.conditions = {
      drained: false,
      pseudo_static: false,
      undrained: false
    };

    this.sendStructure.emit($event);

    if ($event) {
      this.getListTypeInstruments($event);
    } else {
      this.instruments = [];
      this.instrumentsOnline = [];
      this.formSection.get('instruments').setValue([]);
      this.resetCoordinates();
      this.mapSections.clearMap();
    }
  }

  /**
   * Altera o tipo de linha exibido no mapa.
   * @param {number} lineType - O tipo de linha a ser exibido (1 para linha contínua, 2 para tracejada).
   */
  changeLineType(lineType) {
    if (lineType === 1) {
      this.dataMapsSection.polylines[0].strokeOpacity = 1;
      this.dataMapsSection.polylines[0].icons[0].icon.strokeOpacity = 0;
    } else {
      this.dataMapsSection.polylines[0].strokeOpacity = 0;
      this.dataMapsSection.polylines[0].icons[0].icon.strokeOpacity = 1;
    }
    if (this.dataMapsSection.polylines[0].path.length >= 2) {
      this.sendDataMap('polylines');
    }
  }

  /**
   * Altera a espessura da linha exibida no mapa.
   * @param {number} strokeWeight - A nova espessura da linha.
   */
  changeLineWeight(strokeWeight) {
    this.dataMapsSection.polylines[0].strokeWeight = strokeWeight;
    this.dataMapsSection.polylines[0].icons[0].icon.strokeWeight = strokeWeight;
    if (this.dataMapsSection.polylines[0].path.length >= 2) {
      this.sendDataMap('polylines');
    }
  }

  /**
   * Altera a cor da linha do mapa com base na seleção de cor.
   * @param {any} $event - O evento contendo a cor selecionada.
   */
  changeComplete($event) {
    this.selectedColor = $event.color.hex;
    this.formSection.get('map_line_setting_color').setValue(this.selectedColor);
    this.dataMapsSection.polylines[0].strokeColor = this.selectedColor;
    if (this.dataMapsSection.polylines[0].path.length >= 2) {
      this.sendDataMap('polylines');
    }
  }

  /**
   * Oculta o seletor de cor quando o clique ocorre fora do elemento.
   * @param {string} element - O elemento que foi clicado.
   */
  onClickedOutside(element: string) {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker = false;
        break;
    }
  }

  /**
   * Valida o acesso com base no perfil do usuário e ajusta o estado de visualização do formulário.
   * @param {number} [role=0] - O nível de acesso do usuário.
   * @returns {any} - Retorna o nível de acesso validado.
   */
  validateAccess(role: number = 0): any {
    if (this.profile.description !== accessLevelPermission.SuperSuporte && this.profile.description !== accessLevelPermission.Suporte) {
      this.view = true;
    }

    if (this.view) {
      this.formSection.get('instruments')?.disable({ emitEvent: false });
    }
  }

  /**
   * Obtém os dados atuais do formulário e retorna como um objeto.
   * @returns {any} - Os dados do formulário.
   */
  getData() {
    let dataTab = {};
    for (let index in this.formSection.controls) {
      dataTab[index] = this.formSection.get(index).value;
    }
    return dataTab;
  }

  /**
   * Define os dados do formulário com base nos valores fornecidos.
   * @param {any} [dataTab=[]] - Os dados a serem aplicados ao formulário.
   */
  setData(dataTab: any = []) {
    for (var index in dataTab) {
      this.formSection.get(index).setValue(dataTab[index]);

      switch (index) {
        case 'client_unit':
          this.getUnitsId(this.formSection.get('client_unit').value);
          break;
        case 'structure':
          this.getStructureList(this.formSection.get('client_unit').value);
          this.getListTypeInstruments(this.formSection.get(index).value);
          break;
        case 'upstream_coordinate_format':
        case 'downstream_coordinate_format':
        case 'midpoint_coordinate_format':
          let coordinate_format = index.split('_');
          this.onCoordinateFormatChange(null, coordinate_format[0]);
          break;
      }
    }

    this.charCounts['name'] = this.formSection.get('name').value.length;
    this.plotSection();
  }

  //Desenha as seções no mapa com base nas coordenadas convertidas.
  plotSection() {
    this.coordinatesConversion('upstream');

    if (this.formSection.get('is_skew').value) {
      this.coordinatesConversion('midpoint');
    }

    this.coordinatesConversion('downstream');

    // Atualiza cor da linha
    this.selectedColor = this.formSection.get('map_line_setting_color').value;
    this.dataMapsSection.polylines[0].strokeColor = this.selectedColor;

    // Garante que os paths existem antes de configurar largura e estilo
    this.ensurePolyline(0);
    this.ensurePolyline(1);

    if (this.formSection.get('is_skew').value) {
      this.ensurePolyline(2);
    }

    const lineWeight = this.formSection.get('map_line_setting_width').value;
    this.dataMapsSection.polylines[0].strokeWeight = lineWeight;
    this.dataMapsSection.polylines[0].icons[0].icon.strokeWeight = lineWeight;

    // Define o tipo da linha
    const lineType = this.formSection.get('map_line_setting_type').value;
    if (lineType === 2) {
      this.dataMapsSection.polylines[0].strokeOpacity = 0;
      this.dataMapsSection.polylines[0].icons[0].icon.strokeOpacity = 1;
    } else {
      this.dataMapsSection.polylines[0].strokeOpacity = 1;
      this.dataMapsSection.polylines[0].icons[0].icon.strokeOpacity = 0;
    }
  }

  /**
   * Garante que a estrutura `dataMapsSection.polylines[0].path` esteja inicializada corretamente
   * para o índice fornecido. Caso `polylines` ou `path` estejam ausentes ou incompletos,
   * esta função os inicializa com os valores padrão esperados.
   *
   * Este método previne erros como `Cannot read properties of undefined (reading 'lat')`
   * ao manipular coordenadas no mapa antes da estrutura estar totalmente pronta.
   *
   * @param {number} idx - Índice da posição na linha (0 = montante, 1 = jusante, 2 = intermediário)
   */
  private ensurePolyline(idx: number): void {
    if (!this.dataMapsSection.polylines || this.dataMapsSection.polylines.length === 0) {
      this.dataMapsSection.polylines = [
        {
          path: [],
          strokeColor: '#ffffff',
          strokeOpacity: 0,
          strokeWeight: 3,
          icons: [
            {
              icon: {
                path: 'M 0,-1 0,1',
                strokeOpacity: 1,
                scale: 4,
                strokeWeight: 3
              },
              offset: '0',
              repeat: '20px'
            }
          ]
        }
      ];
    }

    if (!this.dataMapsSection.polylines[0].path) {
      this.dataMapsSection.polylines[0].path = [];
    }

    while (this.dataMapsSection.polylines[0].path.length <= idx) {
      this.dataMapsSection.polylines[0].path.push({ lat: 0, lng: 0 });
    }
  }

  //Plota os instrumentos no mapa com base nos dados obtidos.
  plotInstruments() {
    this.instruments.forEach((instrument) => {
      let color = instrument.online ? fn.findIndexInArrayofObject(this.groupInstruments, 'type', instrument.type, 'color') : 'gray';
      let strokeColor = instrument.online ? 'white' : fn.findIndexInArrayofObject(this.groupInstruments, 'type', instrument.type, 'color');

      let svgMarker = {
        path: 'M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0',
        fillColor: color,
        fillOpacity: 1.0,
        strokeWeight: 1.5,
        strokeColor: strokeColor,
        rotation: 0,
        scale: 0.3,
        anchor: new google.maps.Point(0, 0)
      };

      let marker = {
        position: {
          lat: instrument.decimal_geodetic_coordinate.latitude,
          lng: instrument.decimal_geodetic_coordinate.longitude
        },
        title: instrument.identifier,
        options: {},
        icon: svgMarker,
        id: 'mk-' + instrument.identifier,
        zIndex: -999
      };

      let infoWindowMarker = {
        content: '',
        ariaLabel: instrument.identifier,
        id: instrument.identifier,
        data: instrument,
        contentConfig: []
      };

      if (this.view) {
        infoWindowMarker['contentConfig'].push({
          component: 'app-button',
          attrs: {
            class: 'btn-logisoil-blue',
            icon: '',
            label: 'Ver imagens',
            type: true,
            eventClick: true,
            event: 'showImage',
            id: 'iw-button-vi' + fn.hashCode(instrument.identifier)
          },
          classItem: 'd-flex justify-content-center'
        });
      } else {
        infoWindowMarker['contentConfig'].push({
          component: 'app-button',
          attrs: {
            class: 'btn-logisoil-blue',
            icon: '',
            label: 'Selecionar',
            type: true,
            eventClick: true,
            event: 'mapInstrument',
            id: 'iw-button-' + fn.hashCode(instrument.identifier)
          },
          classItem: 'd-flex justify-content-center',
          show: instrument.online
        });
      }

      if (!instrument.online) {
        delete infoWindowMarker['contentConfig']['component'];
      }

      marker['infoWindowMarker'] = infoWindowMarker;

      this.dataMapsSection.markers.push(marker);
    });
    this.sendDataMap('markersMultiple', false);
  }

  /**
   * Gerencia a seleção ou remoção de instrumentos no formulário e no mapa.
   * @param {any} instrumentSelect - O instrumento selecionado.
   * @param {string|null} [option=null] - A opção para adicionar ou editar o instrumento.
   */
  managerInstruments(instrumentSelect, option = null) {
    let findIdentifier = instrumentSelect.data ? instrumentSelect.data.id : instrumentSelect.identifier;
    let idx = fn.findIndexInArrayofObject(this.instruments, 'identifier', findIdentifier);
    let selectedInstrument = this.instruments[idx];

    let dados = this.formSection.controls['instruments'].value;
    dados = dados.length == 0 ? [] : dados;

    dados = dados.map((instrument) => {
      return { id: instrument.id, identifier: instrument.identifier };
    });

    let instrument = { id: selectedInstrument.id, identifier: selectedInstrument.identifier };

    let action =
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).innerHTML.search('Selecionar') !== -1 ? 'add' : 'remove';

    if (option == 'edit') {
      action = 'add';
    }

    if (action === 'add') {
      dados = fn.managerArrayObject(dados, instrument, 'addIfNotExist');
      if (dados) {
        this.formSection.controls['instruments'].setValue(dados);
        this.managerButtonInfoWindow(instrument, 'toRemove');
      }
    } else if (action === 'remove') {
      dados = fn.managerArrayObject(dados, instrument, 'removeIfExist');
      if (dados) {
        this.formSection.controls['instruments'].setValue(dados);
        this.managerButtonInfoWindow(instrument, 'toAdd');
      }
    }
  }

  /**
   * Atualiza o botão de seleção ou remoção no infoWindow com base na ação fornecida.
   * @param {any} instrument - O instrumento associado ao botão.
   * @param {string} action - A ação a ser realizada ('toRemove' ou 'toAdd').
   */
  managerButtonInfoWindow(instrument, action) {
    if (action == 'toRemove' && this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier))) {
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).classList.remove('btn-logisoil-green');
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).classList.add('btn-logisoil-red');
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).innerHTML = 'Remover';
    } else if (action == 'toAdd' && this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier))) {
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).classList.remove('btn-logisoil-red');
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).classList.add('btn-logisoil-green');
      this.element.nativeElement.querySelector('#iw-button-' + fn.hashCode(instrument.identifier)).innerHTML = 'Selecionar';
    }
  }

  /**
   * Seleciona os instrumentos do grupo para exibição no mapa e atualiza os botões do infoWindow.
   */
  selectedGroupInstruments() {
    let markerId = [];

    this.formSection.controls['instruments'].value.forEach((instrument) => {
      if (this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]')) {
        markerId.push('mk-' + instrument.identifier);
      }
    });

    //Para vir com a infoWindow fechada
    if (this.showInfoWindow) {
      this.mapSections.openInfoWindowFromMarkerId(markerId, 'mk-');
    }

    setTimeout(() => {
      this.formSection.controls['instruments'].value.forEach((instrument) => {
        if (this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]')) {
          this.managerButtonInfoWindow(instrument, 'toRemove');
        }
      });
    }, 500);
  }

  /**
   * Atualiza os instrumentos selecionados com base na ação realizada (selecionar ou desmarcar).
   * @param {any[]} instruments - Os instrumentos selecionados ou desmarcados.
   * @param {string} action - A ação realizada ('select', 'selectAll', 'deselect', 'deselectAll').
   */
  getInstrumentsInList(instruments, action) {
    switch (action) {
      case 'select':
        instruments = [instruments];
        break;
      case 'selectAll':
        break;
      case 'deselect':
        instruments = [instruments];
        break;
      case 'deselectAll':
        break;
    }

    instruments.forEach((instrument) => {
      if (action.substring(0, 3) === 'des') {
        this.managerButtonInfoWindow(instrument, 'toAdd');
      } else if (action.substring(0, 3) === 'sel') {
        this.mapSections.openInfoWindowFromMarkerId(['mk-' + instrument.identifier], 'mk-', false);
        setTimeout(() => {
          this.managerButtonInfoWindow(instrument, 'toRemove');
        }, 5);
      }
    });
  }

  /**
   * Valida o campo de inclinação do formulário e atualiza os campos correspondentes com base no valor.
   */
  isSkewValidate() {
    if (this.formSection.get('is_skew').value) {
      this.formSection.get('skew_line_azimuth').setValidators([Validators.required]);
      this.formSection.get('skew_line_azimuth').updateValueAndValidity();

      this.formSection.get('skew_line_azimuth').enable();

      this.formSection.get('midpoint_coordinate_format').setValidators([Validators.required]);
      this.formSection.get('midpoint_coordinate_format').updateValueAndValidity();

      this.formSection.get('midpoint_zone_number').setValidators([Validators.required]);
      this.formSection.get('midpoint_zone_number').updateValueAndValidity();

      this.formSection.get('midpoint_zone_letter').setValidators([Validators.required]);
      this.formSection.get('midpoint_zone_letter').updateValueAndValidity();

      this.formSection.get('midpoint_northing').setValidators([Validators.required]);
      this.formSection.get('midpoint_northing').updateValueAndValidity();

      this.formSection.get('midpoint_easting').setValidators([Validators.required]);
      this.formSection.get('midpoint_easting').updateValueAndValidity();

      this.formSection.get('midpoint_latitude').setValidators([Validators.required]);
      this.formSection.get('midpoint_latitude').updateValueAndValidity();

      this.formSection.get('midpoint_longitude').setValidators([Validators.required]);
      this.formSection.get('midpoint_longitude').updateValueAndValidity();
    } else {
      this.formSection.controls['skew_line_azimuth'].setErrors(null);
      this.formSection.controls['skew_line_azimuth'].clearValidators();

      this.formSection.get('skew_line_azimuth').disable();

      this.formSection.controls['midpoint_coordinate_format'].setErrors(null);
      this.formSection.controls['midpoint_coordinate_format'].clearValidators();

      this.formSection.controls['midpoint_zone_number'].setErrors(null);
      this.formSection.controls['midpoint_zone_number'].clearValidators();

      this.formSection.controls['midpoint_zone_letter'].setErrors(null);
      this.formSection.controls['midpoint_zone_letter'].clearValidators();

      this.formSection.controls['midpoint_northing'].setErrors(null);
      this.formSection.controls['midpoint_northing'].clearValidators();

      this.formSection.controls['midpoint_easting'].setErrors(null);
      this.formSection.controls['midpoint_easting'].clearValidators();

      this.formSection.controls['midpoint_latitude'].setErrors(null);
      this.formSection.controls['midpoint_latitude'].clearValidators();

      this.formSection.controls['midpoint_longitude'].setErrors(null);
      this.formSection.controls['midpoint_longitude'].clearValidators();
    }
  }

  /**
   * Gerencia os eventos relacionados ao mapa, como selecionar um instrumento ou exibir imagens.
   * @param {any} $event - O evento disparado no mapa.
   */
  eventMap($event) {
    switch ($event.type) {
      case 'mapInstrument':
        this.managerInstruments($event);
        break;
      case 'showImage':
        this.getImages($event.data);
        break;
    }
  }

  /**
   * Envia os dados do mapa para atualização.
   * @param {string} option - A opção de dados a ser enviada.
   * @param {boolean} [clear=true] - Indica se os dados existentes devem ser limpos antes da atualização.
   */
  sendDataMap(option, clear = true) {
    this.mapSections.setDataMap(this.dataMapsSection, option, clear);
  }

  /**
   * Gerencia as condições no formulário com base nos valores selecionados.
   * @param {any} conditions - As condições selecionadas.
   */
  managerConditions(conditions) {
    this.conditions = conditions;

    Object.keys(this.conditions).forEach((condition) => {
      if (this.conditions[condition]) {
        this.formSection.get('minimum_' + condition + '_depth').setValidators([Validators.required]);
        this.formSection.get('minimum_' + condition + '_depth').updateValueAndValidity();
        this.formSection.get('minimum_' + condition + '_depth').enable();
      } else {
        this.formSection.get('minimum_' + condition + '_depth').setErrors(null);
        this.formSection.get('minimum_' + condition + '_depth').clearValidators();
        this.formSection.get('minimum_' + condition + '_depth').disable();
        this.formSection.get('minimum_' + condition + '_depth').setValue(null);
      }

      if (this.view) {
        this.formSection.disable();
      }
    });
  }

  /**
   * Obtém as imagens associadas a um instrumento selecionado e abre a modal de imagens.
   * @param {any} instrument - O instrumento cujas imagens devem ser exibidas.
   */
  getImages(instrument) {
    let params = {
      Entities: 1,
      'Filters.Instruments': instrument.data.id
    };
    this.imagesServiceApi.getImages(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.titleModal = 'Imagens do instrumento : ' + instrument.data.identifier;
      this.configModal = {
        imagesItens: dados.images,
        message: { text: '', status: false, class: 'alert-success' },
        uploadActive: false
      };
      if (dados.images.length == 0) {
        this.configModal['message'] = { text: MessagePadroes.NoImage, status: true, class: 'alert-warning' };
      }
      this.ModalComponents.openModal();
    });
  }

  /**
   * Exibe o conteúdo de um arquivo DXF a partir da revisão fornecida.
   * - Verifica se a revisão contém desenhos.
   * - Se o primeiro item da revisão possuir um desenho (`drawing`), o método converte o arquivo base64 para um arquivo DXF e o prepara para exibição.
   * - Caso contrário, exibe uma mensagem informando que não há registros disponíveis.
   *
   * @param {any} review - Objeto contendo as revisões a serem exibidas.
   * @returns {void}
   */
  displayDXF(review) {
    if (review.length > 0) {
      if (review[0].drawing != null) {
        this.sectionsServiceApi.postSectionsDXFTransformMaterial({ base64: review[0].drawing.base64 }).subscribe(
          (resp: any) => {
            this.fileContent = resp.base64; // Usa o base64 tratado retornado pelo backend
            this.fileName = review[0].drawing.name;
            this.fileDXF = fn.base64ToFile(this.fileContent, this.fileName);

            this.dxfService.processDxf({ fileContent: resp.base64, fileNmae: review[0].drawing.name }).subscribe(
              (result) => {
                this.fileDXF = result.materialTransform.fileDXF;
              },
              (error) => {
                console.error('Erro no processamento do arquivo:', error);
              }
            );
          },
          (error) => {
            if (error.status >= 400) {
              console.error('Erro ao tratar o arquivo:', error);
            }
          }
        );
      } else {
        this.message.text = MessagePadroes.NoRegister;
        this.message.status = true;
        this.message.class = 'alert-warning';
      }
    }
  }

  /**
   * Seleciona a aba a ser exibida na interface, alternando entre o mapa e o arquivo DXF.
   * - Ativa a aba de mapa quando a opção selecionada for 'map' e desativa a aba de DXF.
   * - Ativa a aba de DXF quando a opção selecionada for 'dxf' e desativa a aba de mapa.
   *
   * @param {string} option - Opção para selecionar a aba desejada ('map' ou 'dxf').
   * @returns {void}
   */
  selectTab(option: any = '') {
    switch (option) {
      case 'map':
        this.mapTabConfig.active = true;
        this.dxfTabConfig.active = false;
        break;
      case 'dxf':
        this.mapTabConfig.active = false;
        this.dxfTabConfig.active = true;
        break;
    }
  }

  /**
   * Carrega os filtros salvos no `localStorage` e preenche os campos do formulário com base nos dados disponíveis.
   * Verifica se há valores salvos para ClientId, ClientUnitId, e StructureId, e preenche os campos correspondentes no formulário.
   * Se os valores existirem, também busca as unidades e estruturas associadas.
   *
   * @param {FormGroup} $form - O formulário que será preenchido com os filtros.
   * @param {string} client - O nome do campo de cliente no formulário.
   * @param {string} unit - O nome do campo de unidade no formulário.
   * @param {string} structure - O nome do campo de estrutura no formulário.
   */
  loadFilter($form, client, unit, structure, onlyId = false) {
    const savedFilters = localStorage.getItem('filterHierarchy');
    if (savedFilters && !this.edit && !this.view) {
      const filterHierarchy = JSON.parse(savedFilters);
      const configClient = {
        value: onlyId ? filterHierarchy.ClientId.id : [{ id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }],
        param: onlyId ? filterHierarchy.ClientId.id : { id: filterHierarchy.ClientId.id, name: filterHierarchy.ClientId.name }
      };

      const configClientUnit = {
        value: onlyId ? filterHierarchy.ClientUnitId.id : [{ id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }],
        param: onlyId ? filterHierarchy.ClientUnitId.id : { id: filterHierarchy.ClientUnitId.id, name: filterHierarchy.ClientUnitId.name }
      };

      const configStructure = {
        value: onlyId ? filterHierarchy.StructureId.id : [{ id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }],
        param: onlyId ? filterHierarchy.StructureId.id : { id: filterHierarchy.StructureId.id, name: filterHierarchy.StructureId.name }
      };

      // Verificar se existe ClientId, ClientUnitId, e StructureId e preenchê-los
      if (filterHierarchy.ClientId) {
        $form.get(client)?.setValue(configClient.value);
        this.getUnits(configClient.param);
      }

      if (filterHierarchy.ClientUnitId) {
        $form.get(unit)?.setValue(configClientUnit.value);
        this.getStructureList(configClientUnit.param);
      }

      if (filterHierarchy.StructureId) {
        $form.get(structure)?.setValue(configStructure.value);
        this.getStructure(configStructure.param);
      }
    }
  }

  // Atualiza o contador do campo específico
  onValueChange(event: any, field: string): void {
    this.charCounts[field] = event.target.value.length;
  }
}
