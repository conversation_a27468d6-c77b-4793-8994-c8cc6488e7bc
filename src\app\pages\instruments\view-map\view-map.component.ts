import { AfterViewInit, Component, ElementRef, Inject, OnInit, Renderer2, ViewChild, ViewEncapsulation } from '@angular/core';
import { DOCUMENT } from '@angular/common';

import { FormControl, FormGroup, Validators } from '@angular/forms';

import { ActivatedRoute, Router } from '@angular/router';

import { MultiSelectDefault, MetricUnit } from 'src/app/constants/app.constants';
import { Subtypes, PeriodsAbs, groupInstruments } from 'src/app/constants/instruments.constants';
import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';
import { Period } from 'src/app/constants/simulations.constants';

import { DataService } from 'src/app/services/data.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { SharedService } from 'src/app/services/shared.service';
import { UserService } from 'src/app/services/user.service';

import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { MapsService as MapsServiceApi } from 'src/app/services/api/maps.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';

import { GoogleMapsComponent } from 'src/app/components/google-maps/google-maps.component';

import { Subscription } from 'rxjs';
import * as TWEEN from '@tweenjs/tween.js';
import fn from 'src/app/utils/function.utils';

import * as moment from 'moment';

@Component({
  selector: 'app-view-map',
  templateUrl: './view-map.component.html',
  styleUrls: ['./view-map.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ViewMapComponent implements OnInit, AfterViewInit {
  @ViewChild('mapInstruments', { static: false }) mapInstruments: GoogleMapsComponent;
  @ViewChild('hierarchy') hierarchy: any;

  public formFilter: FormGroup = new FormGroup({
    ClientId: new FormControl([]),
    ClientUnitId: new FormControl([]),
    StructureId: new FormControl([]),
    InstrumentId: new FormControl([]),
    Subtype: new FormControl(''),
    Period: new FormControl(1),
    VariationPeriodDays: new FormControl(''),
    StartDate: new FormControl(''),
    EndDate: new FormControl(''),
    Opacity: new FormControl(5, [
      Validators.required,
      Validators.min(5),
      Validators.max(100),
      Validators.pattern(/^\d+$/) // garante que é número inteiro
    ]),
    Overkill: new FormControl(1000, [Validators.required, Validators.min(0), Validators.pattern(/^\d+$/)]),
    ArrowWidth: new FormControl(2),
    DecimalPlaces: new FormControl(2),
    MetricUnit: new FormControl('m')
  });

  public dataMapsInstruments = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: [],
    kmlLayers: [],
    geoDatas: [],
    polygons: []
  };

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public periodsAbs: any = PeriodsAbs; //Mapa de Percolação
  public metricUnit: any = MetricUnit; //Mapa de Deslocamento
  public period: any = Period;

  public instruments: any = []; //Mapa Geral
  public instrumentsBySubtype: any = []; //Mapa Geral
  public subTypes: any = []; //Mapa Geral

  public selectedSections: any = {}; //Mapa Geral

  public instrumentsSettings = MultiSelectDefault.Instruments; //Mapa Geral
  public sectionSettings = MultiSelectDefault.Sections; //Mapa Geral

  public ctrlBtnFilter: boolean = false;
  public ctrlParamsPercolation: boolean = false;
  public ctrlParamsDisplacement: boolean = false;

  public clickEventsubscription: Subscription;

  public mapType: any = '';
  public prefix: string = '';

  public controls: any = [];

  public message: any = [{ text: '', status: false, class: 'alert-success' }];
  public messagesError: any = null;

  public dataMap: any = null;

  public showColorPicker = {
    absPositive: false, //Mapa de percolação
    absNegative: false, //Mapa de percolação
    absNull: false, //Mapa de percolação
    positiveSettlementColor: false, //Mapa de deslocamento
    negativeSettlementColor: false, //Mapa de deslocamento
    arrowColor: false //Mapa de deslocamento
  };

  public selectedColor = {
    absPositive: '#28A745', //Mapa de percolação
    absNegative: '#DC3545', //Mapa de percolação
    absNull: '#FFC107', //Mapa de percolação
    positiveSettlementColor: '#FF00AE', //Mapa de deslocamento
    negativeSettlementColor: '#11FF00', //Mapa de deslocamento
    arrowColor: '#dc3545' //Mapa de deslocamento
  };

  public percolationConfig: any = {
    pulse: true,
    section: false,
    kml: false
  };

  public displacementConfig: any = {
    pulse: true,
    arrows: true
  };

  public instrumentName: string = '';
  public instrument: any = null;
  public mapName: string = '';

  public absolute_variation_colors_id: string = null;
  public displacement_map_configuration_id: string = null;
  public settlement_colors_id: string = null;
  public structure_id: string = null;

  public func = fn;

  public document: any = null;
  public styleElement: HTMLStyleElement;

  public instrumentSelected = null;
  public structureSelected = null;

  public animatedItems: any = {};

  public filterHierarchy: any = {};

  constructor(
    private activatedRoute: ActivatedRoute,
    private dataService: DataService,
    private element: ElementRef,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private mapsServiceApi: MapsServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private renderer: Renderer2,
    private router: Router,
    private sharedService: SharedService,
    private sectionsServiceApi: SectionsServiceApi,
    private userService: UserService,
    @Inject(DOCUMENT) documentDom: Document
  ) {
    this.document = documentDom;
    this.clickEventsubscription = this.sharedService.getClickEvent().subscribe((event) => {
      this.eventMap(event);
    });

    // Crie o elemento <style> apenas uma vez
    this.styleElement = this.renderer.createElement('style');
    this.styleElement.type = 'text/css';
    this.renderer.appendChild(this.element.nativeElement.ownerDocument.head, this.styleElement);
  }

  //Inicializa o componente, configurando o perfil do usuário, permissões, tipo de mapa e outros parâmetros necessários.
  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.instrumentsSettings.singleSelection = true;
    this.subTypes = fn.enumToArraySimple(Subtypes);
    this.controls = this.formFilter.controls;

    if (this.activatedRoute.snapshot.queryParams && this.activatedRoute.snapshot.queryParams.mapType) {
      //this.mapType = this.activatedRoute.snapshot.queryParams.mapType;
      this.ctrlParamsDisplacement = this.mapType === 'displacement' ? true : false;
      this.ctrlParamsPercolation = this.mapType === 'percolation' ? true : false;

      this.filterSubtypes(this.activatedRoute.snapshot.queryParams.mapType, this.activatedRoute?.snapshot?.params?.instrumentId ?? null);
    } else {
      this.prefix = 'general_';
    }

    this.mapsTitle();

    if (this.activatedRoute.snapshot.queryParams && this.activatedRoute.snapshot.queryParams.structure) {
      this.structureSelected = this.activatedRoute.snapshot.queryParams.structure;
      this.mapsStructure(this.activatedRoute.snapshot.queryParams.structure);
    }

    this.updateCSS();
    this.calculatePeriod(this.formFilter.get('Period').value);
  }

  ngAfterViewInit(): void {
    this.updateCSS();
  }

  /**
   * Calcula o período de acordo com o valor informado e configura as datas de início e fim,
   * além de calcular a diferença de dias entre elas.
   *
   * - Bloqueia os campos StartDate e EndDate caso o valor seja maior que 1.
   * - Habilita os campos para edição pelo usuário caso o valor seja igual a 0.
   * - Configura o campo VariationPeriodDays com a diferença em dias entre StartDate e EndDate.
   *
   * @param {number} value - O valor do período selecionado.
   * @param {boolean} [reset=true] - Indica se os campos de datas devem ser limpos antes do cálculo.
   */
  calculatePeriod(value, reset = true) {
    const startDateControl = this.formFilter.get('StartDate');
    const endDateControl = this.formFilter.get('EndDate');
    const variationPeriodDaysControl = this.formFilter.get('VariationPeriodDays');

    // Limpa os valores de VariationPeriodDays se o valor for diferente de 0
    if (reset) {
      endDateControl.setValue(null);
      startDateControl.setValue(null);
      variationPeriodDaysControl.setValue(null); // Limpa o valor de VariationPeriodDays
    }

    if (parseInt(value) > 1) {
      const today = moment().toDate(); // Define a data de hoje como um objeto Date

      if (endDateControl) {
        endDateControl.setValue(moment(today).format('YYYY-MM-DD')); // Seta a data de hoje no formato 'YYYY-MM-DD'

        const startDate = this.calculateNewDate(today, parseInt(value));
        startDateControl.setValue(moment(startDate).format('YYYY-MM-DD'));

        // Calcula a diferença absoluta de dias entre StartDate e EndDate
        const differenceInDays = Math.abs(moment(startDate).diff(moment(today), 'days'));
        variationPeriodDaysControl.setValue(differenceInDays); // Atribui o valor ao FormControl VariationPeriodDays
      }

      // Bloquear os campos StartDate e EndDate
      startDateControl.disable();
      endDateControl.disable();
    } else if (parseInt(value) === 0) {
      // Se o valor for 0, habilita os campos para edição pelo usuário
      startDateControl.enable();
      endDateControl.enable();

      // Se StartDate e EndDate tiverem valores, calcula o período personalizado
      const startDateValue = startDateControl.value;
      const endDateValue = endDateControl.value;

      if (startDateValue && endDateValue) {
        const differenceInDays = Math.abs(moment(startDateValue).diff(moment(endDateValue), 'days'));
        variationPeriodDaysControl.setValue(differenceInDays); // Atribui o valor de dias ao FormControl VariationPeriodDays
      }
    }
  }

  /**
   * Calcula uma nova data com base na data fornecida e no período selecionado.
   * Subtrai meses ou anos conforme o valor do período.
   *
   * @param {Date} dateParam - A data de referência para o cálculo.
   * @param {number} periodValue - O valor do período a ser subtraído da data.
   * @returns {Date} - A nova data calculada após subtração do período.
   */
  calculateNewDate(dateParam: Date, periodValue: number): Date {
    let newDate = new Date(dateParam);
    switch (periodValue) {
      case 2:
        newDate = moment(dateParam).subtract(6, 'months').toDate();
        break;
      case 3:
        newDate = moment(dateParam).subtract(1, 'years').toDate();
        break;
      case 4:
        newDate = moment(dateParam).subtract(2, 'years').toDate();
        break;
      case 5:
        newDate = moment(dateParam).subtract(3, 'years').toDate();
        break;
      case 6:
        newDate = moment(dateParam).subtract(5, 'years').toDate();
        break;
    }
    return newDate;
  }

  //Define o título do mapa baseado no tipo de mapa selecionado.
  mapsTitle() {
    this.mapName = `Mapa ${this.mapType.includes('percolation') ? 'de Percolação' : this.mapType.includes('displacement') ? 'de Deslocamento' : 'Geral'}`;
  }

  //Filtra os subtipos de instrumentos com base no valor selecionado no formulário e redefine o mapa conforme necessário.
  filterSubtypes(mapType = null, instrumentId = null) {
    let types = [];
    this.dataMap = null;
    this.controls['InstrumentId'].setValue([]);

    if (mapType === 'percolation' || parseInt(this.formFilter.controls['Subtype'].value) === 1) {
      // this.resetMap('percolation');
      types = [1, 2, 3 /*, 10*/];
      this.instrumentSelected = instrumentId;
      this.mapType = `${this.prefix}percolation`;
      this.ctrlBtnFilter = false;
      this.ctrlParamsDisplacement = false;
    }
    if (mapType === 'displacement' || parseInt(this.formFilter.controls['Subtype'].value) === 2) {
      // this.resetMap('displacement');
      types = [/*4, 5,*/ 6, 7 /*, 8*/];
      this.instrumentSelected = instrumentId;
      this.mapType = `${this.prefix}displacement`;
      this.ctrlBtnFilter = false;
      this.ctrlParamsPercolation = false;
    }

    if (!['percolation', 'displacement'].includes(mapType) && ![1, 2].includes(parseInt(this.formFilter.controls['Subtype'].value))) {
      this.instrumentSelected = null;
      this.mapType = '';
      this.ctrlBtnFilter = false;
      this.ctrlParamsDisplacement = false;
      this.ctrlParamsPercolation = false;
      this.resetMap(false);
    }

    if (mapType) {
      let subtype = mapType === 'percolation' ? Subtypes.Percolação : mapType === 'displacement' ? Subtypes.Deslocamentos : '';
      this.formFilter.controls['Subtype'].setValue(subtype);
    }
  }

  //Remove os itens que não existem mais na lista de instrumentos selecionados
  removeNonExistingItems() {
    const filteredArray = this.formFilter.controls['InstrumentId'].value.filter((item) => this.instrumentsBySubtype.some((option) => option.id === item.id));
    this.formFilter.controls['InstrumentId'].setValue(filteredArray);
  }

  /**
   * Centraliza o mapa na estrutura selecionada usando suas coordenadas.
   * @param {any} structureId - O ID da estrutura cujas coordenadas serão usadas para centralizar o mapa.
   */
  mapsStructure(structureId = null) {
    this.ngxSpinnerService.show();
    this.dataService.getStructureCoordinate(null, [structureId]).subscribe((coordinate) => {
      if (coordinate.length > 0) {
        this.dataMapsInstruments.center.lat = coordinate[0].decimal_geodetic.latitude;
        this.dataMapsInstruments.center.lng = coordinate[0].decimal_geodetic.longitude;
        this.dataMapsInstruments.markers[0].position.lat = coordinate[0].decimal_geodetic.latitude;
        this.dataMapsInstruments.markers[0].position.lng = coordinate[0].decimal_geodetic.longitude;
        this.sendDataMap('markers');
      }
      this.ngxSpinnerService.hide();
    });
  }

  //Gera o mapa baseado nos parâmetros selecionados, seja para deslocamento ou percolação.
  generateMap(resetMap: boolean = false) {
    if (resetMap) {
      this.resetMap(false);
    }
    let params = {};
    let InstrumentId = this.instrumentSelected?.id ?? this.activatedRoute.snapshot.params.instrumentId ?? null;
    let StructureId = this.filterHierarchy?.structures?.[0].id;

    if (this.mapType.includes('percolation')) {
      params = {
        ...(InstrumentId != null ? { InstrumentId: InstrumentId } : { StructureId: StructureId }),
        ...(this.controls['VariationPeriodDays'].value ? { VariationPeriodDays: this.controls['VariationPeriodDays'].value } : {})
      };
      this.getMapsPercolation(params);
    }

    if (this.mapType.includes('displacement')) {
      params = {
        ...(InstrumentId != null ? { InstrumentId: InstrumentId } : { StructureId: StructureId }),
        ...(this.controls['StartDate'].value ? { StartDate: this.controls['StartDate'].value } : {}),
        ...(this.controls['EndDate'].value ? { EndDate: this.controls['EndDate'].value } : {}),
        Overkill: this.controls['Overkill'].value
      };
      this.getMapsDisplacement(params);
    }
  }

  /**
   * Obtém e configura o mapa de percolação com base nos parâmetros fornecidos.
   * @param {any} params - Os parâmetros necessários para obter o mapa de percolação.
   */
  getMapsPercolation(params) {
    this.ngxSpinnerService.show();

    this.absolute_variation_colors_id = null;
    this.structure_id = null;

    this.mapsServiceApi.getMapsPercolation(params).subscribe(
      (resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        if (dados) {
          this.absolute_variation_colors_id =
            dados.structure_map_data.absolute_variation_colors == null ? null : dados.structure_map_data.absolute_variation_colors.id;
          this.structure_id = dados.structure_map_data.structure_id;
          this.dataMap = dados;

          if (!this.dataMap || !this.dataMap.instruments || !Array.isArray(this.dataMap.instruments) || this.dataMap.instruments.length === 0) {
            this.message.text = MessagePadroes.NoPercolationData;
            this.message.status = true;
            this.message.class = 'alert-warning';

            setTimeout(() => {
              this.message = { text: '', status: false, class: 'alert-success' };
            }, 4000);
          } else {
            this.managerMap();
          }
          this.ngxSpinnerService.hide();
        } else {
          this.message.text = MessagePadroes.NoPercolationData;
          this.message.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.message = { text: '', status: false, class: 'alert-success' };
          }, 4000);
        }
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });

          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
      }
    );
  }

  /**
   * Obtém e configura o mapa de deslocamento com base nos parâmetros fornecidos.
   * @param {any} params - Os parâmetros necessários para obter o mapa de deslocamento.
   */
  getMapsDisplacement(params) {
    this.ngxSpinnerService.show();

    this.displacement_map_configuration_id = null;
    this.structure_id = null;

    this.mapsServiceApi.getMapsDisplacement(params).subscribe(
      (resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        if (dados) {
          this.displacement_map_configuration_id =
            dados.structure_map_data.displacement_map_configuration == null ? null : dados.structure_map_data.displacement_map_configuration.id;
          this.structure_id = dados.structure_map_data.structure_id;
          this.dataMap = dados;
          if (!this.dataMap || !this.dataMap.instruments || !Array.isArray(this.dataMap.instruments) || this.dataMap.instruments.length === 0) {
            this.message.text = MessagePadroes.NoDisplacementData;
            this.message.status = true;
            this.message.class = 'alert-warning';

            setTimeout(() => {
              this.message = { text: '', status: false, class: 'alert-success' };
            }, 4000);
          } else {
            this.managerMap();
          }
          this.ngxSpinnerService.hide();
        } else {
          this.message.text = MessagePadroes.NoDisplacementData;
          this.message.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.message = { text: '', status: false, class: 'alert-success' };
          }, 4000);
        }
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
      }
    );
  }

  //Atualiza as cores de variação absoluta no mapa de percolação.
  postPercolationAbsoluteVariationColor() {
    const params = {
      id: this.absolute_variation_colors_id,
      structure_id: this.structure_id,
      positive_absolute_variation_color: this.selectedColor.absPositive,
      negative_absolute_variation_color: this.selectedColor.absNegative,
      constant_absolute_variation_color: this.selectedColor.absNull
    };

    this.dataMap.structure_map_data.absolute_variation_colors['positive_absolute_variation_color'] = this.selectedColor.absPositive;
    this.dataMap.structure_map_data.absolute_variation_colors['negative_absolute_variation_color'] = this.selectedColor.absNegative;
    this.dataMap.structure_map_data.absolute_variation_colors['constant_absolute_variation_color'] = this.selectedColor.absNull;

    this.mapsServiceApi.postPercolationAbsoluteVariationColor(params).subscribe(
      (resp) => {
        this.absolute_variation_colors_id = resp.toString();

        this.message.text = MessageCadastro.AlteracaoCor;
        this.message.status = true;
        this.message.class = 'alert-success';

        this.managerMap();

        setTimeout(() => {
          this.message = { text: '', status: false, class: 'alert-success' };
        }, 4000);
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
      }
    );
  }

  //Atualiza as configurações do mapa de deslocamento, como cores e opacidade.
  postDisplacementMapConfiguration() {
    const params = {
      id: this.displacement_map_configuration_id,
      structure_id: this.structure_id,
      positive_settlement_color: this.selectedColor.positiveSettlementColor,
      negative_settlement_color: this.selectedColor.negativeSettlementColor,
      settlement_color_opacity: parseFloat(this.controls['Opacity'].value),
      arrow_color: this.selectedColor.arrowColor,
      arrow_width: parseFloat(this.controls['ArrowWidth'].value)
    };

    this.dataMap.structure_map_data.displacement_map_configuration.positive_settlement_color = this.selectedColor.positiveSettlementColor;
    this.dataMap.structure_map_data.displacement_map_configuration.negative_settlement_color = this.selectedColor.negativeSettlementColor;
    this.dataMap.structure_map_data.displacement_map_configuration.settlement_color_opacity = parseFloat(this.controls['Opacity'].value);
    this.dataMap.structure_map_data.displacement_map_configuration.arrow_color = this.selectedColor.arrowColor;
    this.dataMap.structure_map_data.displacement_map_configuration.arrow_width = parseFloat(this.controls['ArrowWidth'].value);

    this.mapsServiceApi.postDisplacementMapConfiguration(params).subscribe(
      (resp) => {
        this.displacement_map_configuration_id = resp.toString();

        this.message.text = MessageCadastro.AlteracaoConfiguracao;
        this.message.status = true;
        this.message.class = 'alert-success';

        this.plotInstrumentsDisplacement();

        setTimeout(() => {
          this.message = { text: '', status: false, class: 'alert-success' };
        }, 4000);
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
      }
    );
  }

  //Obtém arquivos KML para serem usados como camadas no mapa.
  getKmlFiles() {
    if (this.dataMap.structure_map_data.layers.length > 0) {
      this.dataMap.structure_map_data.layers.forEach((layer, index) => {
        const url = layer.file_direct_download_url.replace('api/v1', '');
        const partUrl = url.split('/');
        const fileName = partUrl.pop();
        this.mapsServiceApi.getURLKml({}, url).subscribe((resp: any) => {
          if (resp['status'] == 200) {
            this.dataMap.structure_map_data.layers[index]['fileInfo'] = this.downloadFile(resp['body'], resp['body'].type, fileName);
          }
        });
      });
    }
  }

  //Gerencia o mapa, ajustando as configurações com base no tipo de mapa (deslocamento ou percolação).
  managerMap() {
    if (this.mapType.includes('displacement')) {
      if (this.dataMap.structure_map_data.displacement_map_configuration != null) {
        this.selectedColor.positiveSettlementColor = this.dataMap.structure_map_data.displacement_map_configuration.positive_settlement_color; //Recalque positivo
        this.selectedColor.negativeSettlementColor = this.dataMap.structure_map_data.displacement_map_configuration.negative_settlement_color; // Recalque negativo
        this.selectedColor.arrowColor = this.dataMap.structure_map_data.displacement_map_configuration.arrow_color; //Setas
        this.controls['Opacity'].setValue(this.dataMap.structure_map_data.displacement_map_configuration.settlement_color_opacity);
        this.controls['ArrowWidth'].setValue(this.dataMap.structure_map_data.displacement_map_configuration.arrow_width);
      } else {
        this.dataMap.structure_map_data.displacement_map_configuration = {};
      }
      this.dataMapsInstruments.zoom = this.dataMap.structure_map_data.map_configuration.general_zoom;
      this.ctrlParamsDisplacement = true;
      this.displacementConfig.pulse = true;
      this.plotInstrumentsDisplacement();
    }
    if (this.mapType.includes('percolation')) {
      if (this.dataMap.structure_map_data.absolute_variation_colors != null) {
        this.selectedColor.absPositive = this.dataMap.structure_map_data.absolute_variation_colors.positive_absolute_variation_color;
        this.selectedColor.absNegative = this.dataMap.structure_map_data.absolute_variation_colors.negative_absolute_variation_color;
        this.selectedColor.absNull = this.dataMap.structure_map_data.absolute_variation_colors.constant_absolute_variation_color;
        this.updateCSS();
      } else {
        this.dataMap.structure_map_data.absolute_variation_colors = {};
      }
      this.dataMapsInstruments.zoom = this.dataMap.structure_map_data.map_configuration.general_zoom;

      this.ctrlParamsPercolation = true;
      this.percolationConfig.pulse = true;
      this.getKmlFiles();
      this.plotInstrumentsPercolation();
    }
  }

  //Alterna a exibição das seções no mapa, adicionando ou removendo polilinhas
  managerSections() {
    this.percolationConfig.section = !this.percolationConfig.section;
    if (this.percolationConfig.section) {
      this.plotSections(this.dataMap.structure_map_data.sections);
    } else {
      this.sendDataMap('clearPolylinesMultiple', false, false);
    }
  }

  //Alterna a exibição das setas de direção no mapa de deslocamento.
  managerArrows() {
    this.displacementConfig.arrows = !this.displacementConfig.arrows;
    if (this.displacementConfig.arrows) {
      this.mapInstruments.polylines.forEach((polyline, index) => {
        polyline.setMap(this.mapInstruments.newMap);
      });
    } else {
      this.mapInstruments.polylines.forEach((polyline, index) => {
        polyline.setMap(null);
      });
    }
  }

  //Alterna a exibição das camadas KML no mapa de percolação.
  managerKMLLayers() {
    this.percolationConfig.kml = !this.percolationConfig.kml;
    if (this.percolationConfig.kml) {
      this.plotGeoDatas();
    } else {
      this.sendDataMap('clearGeoDataMultiple', false, false);
    }
  }

  //Plota os instrumentos no mapa de percolação, configurando marcadores e infowindows
  plotInstrumentsPercolation() {
    this.resetMap('percolation');
    let openInstrument = null;

    this.dataMap.instruments.forEach((instrument, index) => {
      let strokeColor = 'white';
      const absoluteVariation = parseFloat(instrument.absolute_variation ? instrument.absolute_variation : 0);
      let fillColor = fn.findIndexInArrayofObject(groupInstruments, 'type', instrument.type, 'color');
      let size = Math.abs(absoluteVariation) * 14.42 + 26;

      // let fillColor =
      //   absoluteVariation > 0 ? this.selectedColor.absPositive : absoluteVariation < 0 ? this.selectedColor.absNegative : this.selectedColor.absNull;

      this.dataMap.instruments[index]['markerConfig'] = {
        size: size,
        fillColor: fillColor,
        className: absoluteVariation > 0 ? 'absPositive' : absoluteVariation < 0 ? 'absNegative' : 'absNull'
      };
      let svgMarker = {
        path: `M-${size},0a${size},${size} 0 1,0 ${size * 2},0a${size},${size} 0 1,0 -${size * 2},0`,
        fillColor: fillColor,
        fillOpacity: 0.5,
        strokeWeight: 1.5,
        strokeColor: strokeColor,
        rotation: 0,
        scale: 0.3,
        anchor: new google.maps.Point(0, 0)
      };
      let marker = {
        ariaLabel: instrument.identifier,
        position: {
          lat: instrument.decimal_geodetic_coordinate.latitude,
          lng: instrument.decimal_geodetic_coordinate.longitude
        },
        title: instrument.identifier,
        options: {},
        icon: svgMarker,
        id: 'mk-' + instrument.identifier,
        zIndex: -999
      };
      let infoWindowMarker = {
        content: instrument.identifier,
        ariaLabel: instrument.identifier,
        id: instrument.identifier,
        data: instrument,
        classTitle: 'd-flex justify-content-center',
        contentConfig: [
          {
            component: 'app-map-info',
            attrs: {
              content: 'Variação: ',
              value: absoluteVariation.toFixed(2),
              color: fillColor,
              id: 'iw-map-info-mk' + fn.hashCode(instrument.identifier)
            },
            classItem: 'd-flex justify-content-center'
          },
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-blue',
              icon: '',
              label: 'Gráfico',
              type: true,
              eventClick: true,
              event: 'viewChart',
              id: 'iw-button-mk' + fn.hashCode(instrument.identifier)
            },
            classItem: 'd-flex justify-content-center'
          }
        ],
        classGroup: 'd-flex flex-column'
      };
      marker['infoWindowMarker'] = infoWindowMarker;
      this.dataMapsInstruments.markers.push(marker);
      if (instrument.id === this.instrumentSelected) {
        openInstrument = instrument.identifier;
      }
    });
    this.sendDataMap('markersMultiple', true);
    if (openInstrument) {
      this.mapInstruments.openInfoWindowFromMarkerId(['mk-' + openInstrument], 'mk-', false);
    }
  }

  //Plota os instrumentos no mapa de deslocamento, configurando marcadores, polilinhas e polígonos.
  plotInstrumentsDisplacement() {
    this.resetMap('displacement');
    let openInstrument = null;

    this.dataMap.instruments.forEach((instrument, index) => {
      let instrumentColor = fn.findIndexInArrayofObject(groupInstruments, 'type', instrument.type, 'color');

      let multipler = this.controls['MetricUnit'].value == 'm' ? 0.001 : this.controls['MetricUnit'].value == 'cm' ? 0.1 : 1;

      let z_displacement = `${(instrument.z_displacement_variation * multipler).toFixed(this.controls['DecimalPlaces'].value)} ${
        this.controls['MetricUnit'].value
      }`;
      let n_displacement = `${(instrument.n_displacement_variation * multipler).toFixed(this.controls['DecimalPlaces'].value)} ${
        this.controls['MetricUnit'].value
      }`;
      let e_displacement = `${(instrument.e_displacement_variation * multipler).toFixed(this.controls['DecimalPlaces'].value)} ${
        this.controls['MetricUnit'].value
      }`;

      const recalqueN = parseFloat(instrument.n_displacement_variation ? instrument.n_displacement_variation : 0);
      let fillColorN = recalqueN >= 0 ? this.selectedColor.positiveSettlementColor : this.selectedColor.negativeSettlementColor;

      const recalqueE = parseFloat(instrument.e_displacement_variation ? instrument.e_displacement_variation : 0);
      let fillColorE = recalqueE >= 0 ? this.selectedColor.positiveSettlementColor : this.selectedColor.negativeSettlementColor;

      const recalqueZ = parseFloat(instrument.z_displacement_variation ? instrument.z_displacement_variation : 0);
      let fillColorZ = recalqueZ >= 0 ? this.selectedColor.positiveSettlementColor : this.selectedColor.negativeSettlementColor;

      let strokeColor = fillColorZ;

      let size = 26;

      this.dataMap.instruments[index]['markerConfig'] = {
        size: size,
        fillColor: fillColorZ,
        className: recalqueZ >= 0 ? 'positiveSettlementColor' : 'negativeSettlementColor'
      };

      let svgMarker = {
        path: `M-${size},0a${size},${size} 0 1,0 ${size * 2},0a${size},${size} 0 1,0 -${size * 2},0`,
        fillColor: instrumentColor,
        fillOpacity: 0.5,
        strokeWeight: 1.5,
        strokeColor: strokeColor,
        rotation: 0,
        scale: 0.3,
        anchor: new google.maps.Point(0, 0)
      };
      let marker = {
        ariaLabel: instrument.identifier,
        position: {
          lat: instrument.coordinate_settings.coordinate_systems.decimal_geodetic.latitude,
          lng: instrument.coordinate_settings.coordinate_systems.decimal_geodetic.longitude
        },
        title: instrument.identifier,
        options: {},
        icon: svgMarker,
        id: 'mk-' + instrument.identifier,
        zIndex: -999
      };
      let infoWindowMarker = {
        content: instrument.identifier,
        ariaLabel: instrument.identifier,
        id: instrument.identifier,
        data: instrument,
        classTitle: 'd-flex justify-content-center',
        contentConfig: [
          {
            component: 'app-map-info',
            attrs: {
              content: 'Desloc. E: ',
              value: e_displacement,
              color: fillColorE,
              id: 'iw-map-info-e-mk' + fn.hashCode(instrument.identifier)
            },
            classItem: 'w-100 text-start'
          },
          {
            component: 'app-map-info',
            attrs: {
              content: 'Desloc. N: ',
              value: n_displacement,
              color: fillColorN,
              id: 'iw-map-info-n-mk' + fn.hashCode(instrument.identifier)
            },
            classItem: 'w-100 text-start'
          },
          {
            component: 'app-map-info',
            attrs: {
              content: 'Desloc. Z: ',
              value: z_displacement,
              color: fillColorZ,
              id: 'iw-map-info-z-mk' + fn.hashCode(instrument.identifier)
            },
            classItem: 'w-100 text-start'
          },
          {
            component: 'app-button',
            attrs: {
              class: 'btn-logisoil-blue',
              icon: '',
              label: 'Gráfico',
              type: true,
              eventClick: true,
              event: 'viewChart',
              id: 'iw-button-mk' + fn.hashCode(instrument.identifier)
            },
            classItem: 'd-flex justify-content-center'
          }
        ],
        classGroup: 'd-flex flex-column'
      };
      marker['infoWindowMarker'] = infoWindowMarker;
      this.dataMapsInstruments.markers.push(marker);

      // Direcao (Seta)
      let polyline = {
        path: [],
        strokeColor: this.selectedColor.arrowColor,
        strokeOpacity: 1,
        strokeWeight: parseFloat(this.controls['ArrowWidth'].value),
        icons: [
          {
            icon: {
              path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW
            },
            offset: '100%'
          }
        ]
      };

      polyline.path.push({
        lat: instrument.coordinate_settings.coordinate_systems.decimal_geodetic.latitude,
        lng: instrument.coordinate_settings.coordinate_systems.decimal_geodetic.longitude
      });
      polyline.path.push({
        lat: instrument.calculated_decimal_geodetic_coordinate.latitude,
        lng: instrument.calculated_decimal_geodetic_coordinate.longitude
      });
      polyline['infoWindowPolyline'] = [];
      this.dataMapsInstruments.polylines.push(polyline);

      // Poligonos
      let polygon = {
        type: 'CIRCLE',
        strokeColor: '#FFFFFF',
        strokeOpacity: 0.5,
        strokeWeight: 1.5,
        fillColor: fillColorZ,
        fillOpacity: parseFloat(this.controls['Opacity'].value) / 100,
        id: 'polycircle-' + instrument.identifier,
        radius: Math.abs(recalqueZ),
        center: {
          lat: instrument.coordinate_settings.coordinate_systems.decimal_geodetic.latitude,
          lng: instrument.coordinate_settings.coordinate_systems.decimal_geodetic.longitude
        }
      };

      polygon['infoWindowPolygon'] = [];

      this.animatedItems['polycircle-' + instrument.identifier] = {
        objectTween: null,
        originalValue: Math.abs(recalqueZ)
      };

      this.dataMapsInstruments.polygons.push(polygon);
      if (instrument.id === this.instrumentSelected) {
        openInstrument = instrument.identifier;
      }
    });
    this.sendDataMap('markersMultiple', true);
    this.sendDataMap('polylinesMultiple', true);
    this.sendDataMap('polygonsMultiple', true);
    if (openInstrument) {
      this.mapInstruments.openInfoWindowFromMarkerId(['mk-' + openInstrument], 'mk-', false);
    }
  }

  /**
   * Plota as seções no mapa, adicionando polilinhas com as configurações adequadas.
   * @param {any} $sections - As seções a serem plotadas no mapa.
   */
  plotSections($sections) {
    $sections.forEach((section) => {
      let polyline = {
        path: [],
        strokeColor: section.map_line_setting.color,
        strokeOpacity: section.map_line_setting.type == 1 ? 0 : 1,
        strokeWeight: section.map_line_setting.width,
        icons: [
          {
            icon: {
              path: 'M 0,-1 0,1',
              strokeOpacity: section.map_line_setting.type == 1 ? 1 : 0,
              scale: 4,
              strokeWeight: section.map_line_setting.width
            },
            offset: '0',
            repeat: '20px'
          }
        ],
        id: section.id,
        name: section.name
      };

      polyline.path.push({
        lat: section.coordinates.upstream_coordinate_setting.coordinate_systems.decimal_geodetic.latitude,
        lng: section.coordinates.upstream_coordinate_setting.coordinate_systems.decimal_geodetic.longitude
      });
      if (section.coordinates.midpoint_coordinate_setting) {
        polyline.path.push({
          lat: section.coordinates.midpoint_coordinate_setting.coordinate_systems.decimal_geodetic.latitude,
          lng: section.coordinates.midpoint_coordinate_setting.coordinate_systems.decimal_geodetic.longitude
        });
      }
      polyline.path.push({
        lat: section.coordinates.downstream_coordinate_setting.coordinate_systems.decimal_geodetic.latitude,
        lng: section.coordinates.downstream_coordinate_setting.coordinate_systems.decimal_geodetic.longitude
      });

      let infoWindowPolyline = {
        content: section.name,
        ariaLabel: section.name,
        id: section.name,
        data: section,
        contentConfig: []
      };

      polyline['infoWindowPolyline'] = infoWindowPolyline;

      //adiciona a secao para exibir no mapa
      this.dataMapsInstruments.polylines.push(polyline);
    });
    this.sendDataMap('polylinesMultiple', false);
  }

  //Converte e plota dados KML no mapa como GeoJSON.
  plotGeoDatas() {
    if (this.dataMap.structure_map_data.layers.length > 0) {
      let completedConversions = 0; // Variável para contar as conversões concluídas
      const totalLayers = this.dataMap.structure_map_data.layers.length; // Número total de camadas

      this.dataMap.structure_map_data.layers.forEach((layer, index) => {
        this.mapInstruments
          .convertKmlToGeoJson(layer.fileInfo.blob)
          .then((geoJson) => {
            let geoData = {
              geoJson: geoJson
            };
            let infoWindowGeoData = {
              content: 'Dam Break',
              ariaLabel: 'Dam Break',
              id: 'Dam Break',
              data: {},
              contentConfig: []
            };
            geoData['infoWindowGeoData'] = infoWindowGeoData;
            this.dataMapsInstruments.geoDatas.push(geoData);
            completedConversions++;

            if (completedConversions === totalLayers) {
              this.sendDataMap('geoData', false, false);
            }
          })
          .catch((error) => {});
      });
    } else {
      this.percolationConfig.kml = false;

      this.message.text = MessagePadroes.NoMapLayerDB;
      this.message.status = true;
      this.message.class = 'alert-warning';

      setTimeout(() => {
        this.message = { text: '', status: false, class: 'alert-success' };
      }, 4000);
    }
  }

  /**
   * Envia os dados do mapa para serem renderizados com as configurações atuais.
   * @param {string} option - A opção que define o tipo de dados a serem enviados.
   * @param {boolean} [clear=true] - Define se o mapa deve ser limpo antes de adicionar novos dados.
   * @param {boolean} [center=true] - Define se o mapa deve ser centralizado.
   */
  sendDataMap(option, clear = true, center = true) {
    this.mapInstruments.setDataMap(this.dataMapsInstruments, option, clear, center);
  }

  /**
   * Controla a animação de pulsação para os instrumentos no mapa de percolação e deslocamento.
   */
  ctrlPulse() {
    let $dados = this.dataMap;
    if (this.percolationConfig.pulse && this.mapType.includes('percolation')) {
      $dados.instruments.forEach((instrument) => {
        if (this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]')) {
          this.renderer.addClass(
            this.element.nativeElement.querySelector('div[title="' + instrument.identifier + '"]'),
            `pulse-pin-${instrument.markerConfig.className}`
          );
        }
      });
    } else if (!this.percolationConfig.pulse && this.mapType.includes('percolation')) {
      let elements = this.element.nativeElement.querySelectorAll('[class*="pulse-pin"]');

      // Itere sobre todos os elementos encontrados
      elements.forEach((element) => {
        // Obtenha todas as classes do elemento
        let classes = element.className.split(' ');

        // Filtrar e remover as classes que começam com 'pulse-pin'
        let newClasses = classes.filter((className) => !/^pulse-pin/.test(className));

        // Atualize as classes do elemento
        element.className = newClasses.join(' ');
      });
    }

    if (this.displacementConfig.pulse && this.mapType.includes('displacement')) {
      this.mapInstruments.polygons.forEach((polygon, index) => {
        let props = { radius: 1 };

        let tween = new TWEEN.Tween(props)
          .to({ radius: this.animatedItems[polygon.id].originalValue }, 750) // Dobrar o raio em 1 segundo
          .repeat(Infinity)
          .easing(TWEEN.Easing.Quadratic.InOut) // Especificar a função de easing (suavização)
          .yoyo(true) // Repetir a animação
          .onUpdate(() => {
            // Atualizar o raio do círculo com o valor interpolado
            polygon.setRadius(props.radius);
          })
          .start(); // Iniciar a animação

        this.animatedItems[polygon.id].objectTween = tween;

        // Função de atualização do Tween.js
        function animate() {
          requestAnimationFrame(animate);
          TWEEN.update();
        }

        // Iniciar a função de atualização
        animate();
      });
    } else if (!this.displacementConfig.pulse && this.mapType.includes('displacement')) {
      this.mapInstruments.polygons.forEach((polygon, index) => {
        this.animatedItems[polygon.id].objectTween.stop();
        polygon.setRadius(0);
      });
    }
  }

  /**
   * Gerencia eventos do mapa, como carregar marcadores ou exibir gráficos.
   * @param {any} $event - O evento disparado pelo mapa.
   */
  eventMap($event) {
    switch ($event.type) {
      case 'loadMarkers':
        if (this.mapType.includes('percolation') || this.mapType.includes('displacement')) {
          setTimeout(() => {
            this.ctrlPulse();
          }, 1500);
        }
        break;
      case 'viewChart':
        let info: any = $event.data.data;
        this.router.navigate(['instruments/' + info.id + '/chart'], {
          queryParams: { typeInstrument: info.type, structure: info.structure_id, period: this.controls['VariationPeriodDays'].value }
        });
        break;

      default:
        break;
    }
  }

  //Redefine o zoom do mapa para o nível geral configurado.
  resetZoom() {
    this.dataMapsInstruments.zoom = this.dataMap.structure_map_data.map_configuration.general_zoom;
    this.sendDataMap('zoom', false, true);
  }

  /**
   * Redefine o mapa para seu estado inicial, removendo marcadores, polígonos e outras configurações.
   * @param {string|null} [options=null] - As opções que definem quais partes do mapa devem ser redefinidas.
   */
  resetMap(options = null) {
    let resetCenter = options == null ? true : false;

    if (options == null) {
      this.ctrlParamsPercolation = false;
      this.percolationConfig = {
        pulse: true,
        section: false,
        kml: false
      };
      this.ctrlParamsDisplacement = false;
      this.displacementConfig = {
        pulse: true,
        arrows: true
      };
      this.animatedItems = {};
    }

    const marker0 = this.dataMapsInstruments.markers.shift();

    this.dataMapsInstruments.markers = [];
    this.dataMapsInstruments.polylines = [];
    this.dataMapsInstruments.kmlLayers = [];
    this.dataMapsInstruments.geoDatas = [];
    this.dataMapsInstruments.polygons = [];
    this.dataMapsInstruments.markers.push(marker0);

    this.mapInstruments.clearMapSrv(resetCenter);
  }

  /**
   * Gerencia eventos relacionados à hierarquia de unidades, estruturas e instrumentos.
   * @param {any} $event - O evento disparado pela hierarquia.
   */
  getEventHierarchy($event) {
    if ($event.type != 'instruments') {
      this.resetVariables();
    }

    switch ($event.type) {
      case 'units':
        this.ctrlParamsDisplacement = false;
        this.ctrlParamsPercolation = false;
        break;
      case 'structures':
        this.ctrlParamsDisplacement = false;
        this.ctrlParamsPercolation = false;
        if ($event.element != null && $event.action == 'select') {
          this.structureSelected = $event.element.id;
          this.mapsStructure($event.element.id);
        }
        break;
      case 'instruments':
        if ($event.action == 'select') {
          this.mapInstruments.openInfoWindowFromMarkerId(['mk-' + $event.element.identifier], 'mk-', false);
        }
        break;
    }
  }

  //Atualiza a hierarquia de filtros com base no evento de filtro.
  filterEventHierarchy($event) {
    this.filterHierarchy = $event;
  }

  //Atualiza o CSS das classes de animação baseadas nas cores selecionadas.
  updateCSS() {
    if (this.mapType.includes('percolation')) {
      let classPulsePositive = fn.classPulse('absPositive', this.selectedColor.absPositive);
      let classPulseNegative = fn.classPulse('absNegative', this.selectedColor.absNegative);
      let classPulseNull = fn.classPulse('absNull', this.selectedColor.absNull);

      let allClasses = classPulsePositive + classPulseNegative + classPulseNull;

      this.styleElement.innerHTML = allClasses;
    }
  }

  /**
   * Lida com cliques fora de um elemento específico, como um color picker.
   * @param {string} element - O elemento a ser monitorado.
   * @param {string} [property=''] - A propriedade relacionada ao elemento.
   */
  onClickedOutside(element: string, property: string = '') {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker[property] = false;
        break;
    }
  }

  /**
   * Lida com a mudança completa de cor no color picker e atualiza o mapa com a nova cor.
   * @param {any} $event - O evento de mudança de cor.
   * @param {string} [property=''] - A propriedade de cor a ser atualizada.
   */
  changeComplete($event, property: string = '') {
    this.selectedColor[property] = $event.color.hex;

    if (this.mapType.includes('percolation')) {
      this.plotInstrumentsPercolation();
    }

    if (this.mapType.includes('displacement')) {
      this.plotInstrumentsDisplacement();
    }
  }

  /**
   * Faz o download de um arquivo a partir de um blob de dados e retorna a URL para acesso.
   * @param {any} data - Os dados do arquivo.
   * @param {string} [type=''] - O tipo MIME do arquivo.
   * @param {string} fileName - O nome do arquivo a ser baixado.
   * @returns {Object} - Um objeto contendo a URL e o blob do arquivo.
   */
  downloadFile(data: any, type: string = '', fileName: string) {
    const blob = new Blob([data], { type: type });
    const url = window.URL.createObjectURL(blob);
    return {
      url: url,
      blob: blob
    };
  }

  resetVariables() {
    this.dataMap = null;
    this.controls['InstrumentId'].setValue([]);
    this.controls['Subtype'].setValue('');
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  //Navega de volta para a página inicial.
  goBack() {
    this.router.navigate(['/']);
  }
}
