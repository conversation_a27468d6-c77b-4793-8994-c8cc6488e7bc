import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';

@Component({
  selector: 'app-infinite-strength',
  templateUrl: './infinite-strength.component.html',
  styleUrls: ['./infinite-strength.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class InfiniteStrengthComponent implements OnInit, OnChanges {
  @Input() public data: any = null;
  @Input() public view: boolean = false;

  public formInfiniteStrength: FormGroup = new FormGroup({
    is_allow_sliding_along_boundary: new FormControl(false)
  });
  constructor(public formService: FormService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  splitData($dados) {
    if ($dados.allow_sliding_along_boundary != null) {
      this.formInfiniteStrength.controls['is_allow_sliding_along_boundary'].setValue($dados.allow_sliding_along_boundary);
    }
    if (this.view) {
      this.formInfiniteStrength.disable();
    }
  }

  validate() {
    let formValid = this.formService.validateForm(this.formInfiniteStrength);

    if (!formValid) {
      this.formInfiniteStrength.markAllAsTouched();
    }

    return formValid;
  }
}
