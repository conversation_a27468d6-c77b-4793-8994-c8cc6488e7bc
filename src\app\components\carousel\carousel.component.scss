#carouselImages {
  display: flex;
  flex-direction: column;
  height: auto;
  background-color: rgb(244 244 245);
  border: 1px solid #34b575;
  min-height: auto !important;
}

.carousel-control-prev,
.carousel-control-next {
  opacity: 1;
  margin-top: 50px;
  margin-bottom: 50px;
}

.icon-navigation {
  color: #34b575;
}

.carousel-item,
.carousel-item .active {
  height: auto !important;
  position: relative;
  width: 100%;
  background-color: #f0f0f0;
  height: 100px; /* Apenas para fins de demonstração */
  box-sizing: border-box; /* Garante que padding e borda sejam incluídos na largura total */
  padding: 2px; /* Adiciona um espaçamento interno à div */
  margin: 0; /* Remove as margens padrão */
}

.full-width-div {
  width: 100%;
  background-color: #f0f0f0;
  height: 100px; /* Apenas para fins de demonstração */
  box-sizing: border-box; /* Garante que padding e borda sejam incluídos na largura total */
  padding: 2px; /* Adiciona um espaçamento interno à div */
  margin: 0; /* Remove as margens padr<PERSON> */
}

#image-album .modal-footer {
  display: block;
}

.image-overlay {
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  height: 45px; /* Altura da barra cinza */
  background-color: rgba(0, 0, 0, 0.5); /* Cor cinza com transparência */
  text-align: center; /* Centraliza o texto horizontalmente */
  color: white; /* Cor do texto */
  font-size: 16px; /* Tamanho da fonte */
  font-weight: bold;
  display: flex;
  align-items: center; /* Centraliza verticalmente */
  justify-content: center; /* Centraliza horizontalmente */
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: -25px;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
  list-style: none;
}

.carousel-item {
  transition: opacity 0.5s ease; /* Adiciona uma transição suave de opacidade */
}

.carousel-item img {
  max-width: 100%;
  height: auto; /* Garante que a imagem mantenha a proporção */
}

#carouselImages {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 310px);
  background-color: rgb(244 244 245);
  border: 1px solid #34b575;
}

.carousel-inner {
  position: relative; /* Adiciona posicionamento relativo para os slides */
  width: 100%;
  overflow: hidden; /* Esconde conteúdo extra além do carrossel */
}

.carousel-item {
  display: none; /* Inicialmente, oculta todos os slides */
  position: relative;
  width: 100%;
  transition: opacity 0.5s ease; /* Adiciona uma transição suave de opacidade */
}

.carousel-item.active {
  display: block; /* Exibe o slide ativo */
  opacity: 1; /* Garante que o slide ativo seja completamente visível */
}

.carousel-item img {
  max-width: 100%;
  height: auto !important; /* Garante que a imagem mantenha a proporção */
}
