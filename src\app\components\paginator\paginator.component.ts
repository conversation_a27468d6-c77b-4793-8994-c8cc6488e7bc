import { Component, Input, Output, EventEmitter, SimpleChanges, OnInit, OnChanges } from '@angular/core';

@Component({
  selector: 'app-paginator',
  templateUrl: './paginator.component.html',
  styleUrls: ['./paginator.component.scss']
})
export class PaginatorComponent implements OnInit, OnChanges {
  @Input() public collectionSize: number;
  @Input() public page: number;
  @Input() public maxSize: number = 10;
  @Input() public boundaryLinks: boolean = true;
  @Input() public pageSize: number;
  @Input() public rotate: boolean = false;
  @Input() public enableItemPerPage: boolean = false;
  @Output() public sendPageChange = new EventEmitter();
  @Output() public sendPageSizeChange = new EventEmitter<number>();

  private defaultPageSizeOptions = [10, 20, 30];
  public pageSizeOptions: number[] = [];

  desiredPage: number;
  totalPages = 0;
  pageSelect = 1;

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges) {
    this.updatePageSizeOptions();
  }

  private updatePageSizeOptions() {
    this.pageSizeOptions = this.defaultPageSizeOptions.filter((opt) => opt <= this.collectionSize);
    if (this.pageSizeOptions.length === 0) {
      this.pageSizeOptions = [10];
    }

    if (this.pageSize > this.collectionSize) {
      this.pageSize = this.pageSizeOptions[0];
    }

    this.totalPages = Math.ceil(this.collectionSize / this.pageSize);
  }

  goToPage(page = null) {
    if (this.desiredPage >= 1 && this.desiredPage <= this.totalPages) {
      this.page = this.desiredPage;
    } else if (page != null) {
      this.page = page;
    } else {
      this.page = 1;
    }

    this.getPageChange({ page: this.page, pageSize: this.pageSize });
  }

  getDisplayText(): string {
    const collectionSize = parseInt(this.collectionSize.toString());
    const pageSize = parseInt(this.pageSize.toString());
    const start = (this.page - 1) * this.pageSize + 1;
    let end = start + pageSize - 1;
    end = collectionSize > end ? end : collectionSize;

    return `Exibindo ${start} – ${end} de ${this.collectionSize} resultados`;
  }

  getPageChange($event: any) {
    this.sendPageChange.emit($event);
  }

  allowOnlyInteger(event: KeyboardEvent, currentValue: number | null): void {
    const key = event.key;

    // Só permite números de 0 a 9
    if (!/^\d$/.test(key)) {
      event.preventDefault();
      return;
    }

    // Simula o valor resultante após a digitação
    const nextValue = (currentValue ?? '').toString() + key;
    const numericValue = parseInt(nextValue, 10);

    const min = 1;
    const max = this.totalPages;

    if (numericValue > max || numericValue < min) {
      event.preventDefault();
    }
  }
}
