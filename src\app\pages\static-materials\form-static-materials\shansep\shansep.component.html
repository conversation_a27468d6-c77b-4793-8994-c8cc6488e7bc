<div class="col-md-12">
  <form [formGroup]="formShansep">
    <div class="row">
      <label class="form-label" style="font-style: italic">Fórmula:</label>
      <div class="col-md-3">
        <img
          src="assets/images/static-materials/shansep.png"
          class="img-fluid img-thumbnail"
          style="max-height: 80px; width: auto"
        />
      </div>
    </div>
    <div class="row mt-1">
      <!-- a -->
      <div class="col-md-4">
        <label class="form-label">A</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="a"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formShansep.get('a'),
                'positiveDecimalDot'
              )
            "
            (keyup)="func.controlNumber($event, formShansep.get('a'))"
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa/m</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="!formShansep.get('a').valid && formShansep.get('a').touched"
          >Campo Obrigatório.</small
        >
      </div>
      <!-- S -->
      <div class="col-md-4">
        <label class="form-label">S</label>
        <input
          type="text"
          class="form-control"
          formControlName="s"
          min="0"
          step="0.01"
          (keypress)="
            func.controlNumber(
              $event,
              formShansep.get('s'),
              'positiveDecimalDot'
            )
          "
          (keyup)="func.controlNumber($event, formShansep.get('s'))"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="!formShansep.get('s').valid && formShansep.get('s').touched"
          >Campo Obrigatório.</small
        >
      </div>
      <!-- m -->
      <div class="col-md-4">
        <label class="form-label">m</label>
        <input
          type="text"
          class="form-control"
          formControlName="m"
          min="0"
          step="0.01"
          (keypress)="
            func.controlNumber(
              $event,
              formShansep.get('m'),
              'positiveDecimalDot'
            )
          "
          (keyup)="func.controlNumber($event, formShansep.get('m'))"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          appDisableScroll
        />
        <small
          class="form-text text-danger"
          *ngIf="!formShansep.get('m').valid && formShansep.get('m').touched"
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <div class="row mt-1">
      <!-- Resistência máxima ao cisalhamento (kPa) -->
      <div class="col-md-4">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_maximum_shear_strength"
          (change)="
            formService.checkboxControlValidate(
              formShansep,
              'maximum_shear_strength'
            )
          "
        />
        <label class="form-label">Resistência máxima ao cisalhamento</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="maximum_shear_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formShansep.get('maximum_shear_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formShansep.get('maximum_shear_strength')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formShansep.get('maximum_shear_strength').valid &&
            formShansep.get('maximum_shear_strength').touched &&
            formShansep.get('is_maximum_shear_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Resistência a tração (kPa) -->
      <div class="col-md-4">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_tensile_strength"
          (change)="
            formService.checkboxControlValidate(formShansep, 'tensile_strength')
          "
        />
        <label class="form-label">Resistência a tração</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="tensile_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formShansep.get('tensile_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formShansep.get('tensile_strength'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formShansep.get('tensile_strength').valid &&
            formShansep.get('tensile_strength').touched &&
            formShansep.get('is_tensile_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <div class="row mt-1">
      <!-- Stress history type -->
      <div class="col-md-4">
        <label class="form-label">Tipo de histórico de tensões:</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="dropdownSettings"
          [data]="stressHistoryType"
          formControlName="stress_history_type"
          (onSelect)="itemEvent($event, 'select', 'stressHistoryType')"
          (onDeSelect)="itemEvent($event, 'deselect', 'stressHistoryType')"
          [disabled]="formShansep.controls['stress_history_type'].disabled"
        ></ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formShansep.get('stress_history_type').valid &&
            formShansep.get('stress_history_type').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <div
      class="row mt-1"
      *ngIf="formShansep.controls['stress_history_type'].value.length > 0"
    >
      <div class="col-md-4">
        <label class="form-label">Método de histórico de tensões:</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="dropdownSettings"
          [data]="stressHistoryMethodData"
          formControlName="stress_history_method"
          (onSelect)="itemEvent($event, 'select', 'stressHistoryMethod')"
          (onDeSelect)="itemEvent($event, 'deselect', 'stressHistoryMethod')"
          [disabled]="formShansep.controls['stress_history_method'].disabled"
        ></ng-multiselect-dropdown>
      </div>
    </div>
    <!-- Constant -->
    <div
      class="row mt-1"
      *ngIf="
        formShansep.controls['stress_history_method'].value.length > 0 &&
        formShansep.controls['stress_history_method'].value[0].id == '1'
      "
    >
      <div class="col-md-3">
        <label class="form-label">Constante</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="constant"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formShansep.get('constant'),
                'positiveDecimalDot'
              )
            "
            (keyup)="func.controlNumber($event, formShansep.get('constant'))"
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formShansep.get('constant').valid &&
            formShansep.get('constant').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <!-- Tabela -->
    <div
      class="row mt-1"
      *ngIf="
        formShansep.controls['stress_history_method'].value.length > 0 &&
        formShansep.controls['stress_history_method'].value[0].id != '1'
      "
    >
      <div class="col-md-6">
        <app-material-points
          #materialPointsRef
          [pointsInput]="{
            x: label_x,
            y: label_y
          }"
          [buttonChart]="false"
          [data]="dataPoints"
          [view]="view"
        ></app-material-points>
      </div>
    </div>
  </form>
</div>
