<div class="tab-content">
  <div class="mt-2 row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header form-control-bg">
          <label>{{ title }} </label>
        </div>
      </div>
    </div>
  </div>
  <app-reload-timer
    #reloadTimer
    [progressColor]="'#032561'"
    [showButton]="false"
    [intervalDuration]="timer * 1000"
    [reverse]="true"
    [startTrigger]="showTimer"
    [message]="
      'Usuário inativo, a ficha será salva e redirecionado para a tela de listagem em:'
    "
    (timerComplete)="onTimerComplete()"
  ></app-reload-timer>

  <div class="mt-1 row" *ngIf="dados.locked">
    <div class="col-md-12">
      <div class="alert alert-info my-1" role="alert">
        {{ lockedMessage }}
      </div>
    </div>
  </div>

  <!-- <PERSON><PERSON> <PERSON> -->
  <ul class="nav nav-tabs mt-3">
    <ng-container *ngFor="let key of tabKeys">
      <li class="nav-item" *ngIf="tabsConfig[key].show">
        <button
          class="nav-link"
          [ngClass]="tabsConfig[key].active ? 'active' : ''"
          (click)="selectTab(key)"
          [style.background-color]="tabsConfig[key].styleColor ? '#dc3545' : ''"
          [style.color]="tabsConfig[key].styleColor ? '#ffffff' : ''"
          [disabled]="tabsConfig[key].disabled"
        >
          {{ tabsConfig[key].name }}
        </button>
      </li>
    </ng-container>
  </ul>

  <!-- Conteúdo das Abas -->
  <div
    class="tab-pane fade"
    [ngClass]="tabsConfig.general.active ? 'show active' : ''"
    id="general"
    role="tabpanel"
    aria-labelledby="general-tab"
  >
    <app-general-data
      [inspectionSheetType]="inspectionSheetType"
      (formChanged)="onFormChange($event)"
      [status]="dados.status"
      [locked]="dados.locked"
      (validationChanged)="onValidationChanged($event)"
    ></app-general-data>
  </div>

  <div
    class="tab-pane fade"
    [ngClass]="tabsConfig.previousSituation.active ? 'show active' : ''"
    id="previous-situation"
    role="tabpanel"
    aria-labelledby="previous-situation-tab"
  >
    <app-previous-situation
      [inspectionSheetType]="inspectionSheetType"
    ></app-previous-situation>
  </div>

  <div
    class="tab-pane fade"
    [ngClass]="tabsConfig.aspectsObserved.active ? 'show active' : ''"
    id="aspects-observed"
    role="tabpanel"
    aria-labelledby="aspects-observed-tab"
  >
    <app-aspects-observed
      [inspectionSheetType]="inspectionSheetType"
      (saveTriggered)="onSave($event)"
      (formChanged)="onFormChange($event)"
      [status]="dados.status"
      [locked]="dados.locked"
    ></app-aspects-observed>
  </div>

  <div
    class="tab-pane fade"
    [ngClass]="tabsConfig.conservationStatus.active ? 'show active' : ''"
    id="conservation-status"
    role="tabpanel"
    aria-labelledby="conservation-status-tab"
  >
    <app-conservation-status
      [inspectionSheetType]="inspectionSheetType"
      (formChanged)="onFormChange($event)"
      [status]="dados.status"
      [locked]="dados.locked"
    ></app-conservation-status>
  </div>

  <div
    class="tab-pane fade"
    [ngClass]="tabsConfig.generalObservations.active ? 'show active' : ''"
    id="general-observations"
    role="tabpanel"
    aria-labelledby="general-observations-tab"
  >
    <app-general-observations
      [inspectionSheetType]="inspectionSheetType"
      (formChanged)="onFormChange($event)"
      [status]="dados.status"
      [locked]="dados.locked"
    ></app-general-observations>
  </div>

  <div
    class="tab-pane fade"
    [ngClass]="tabsConfig.actionsExecuted.active ? 'show active' : ''"
    id="actions-executed"
    role="tabpanel"
    aria-labelledby="actions-executed-tab"
  >
    <app-actions-executed
      [inspectionSheetType]="inspectionSheetType"
      (formChanged)="onFormChange($event)"
      [status]="dados.status"
      [locked]="dados.locked"
    ></app-actions-executed>
  </div>

  <div
    class="tab-pane fade"
    [ngClass]="tabsConfig.currentSituation.active ? 'show active' : ''"
    id="current-situation"
    role="tabpanel"
    aria-labelledby="current-situation-tab"
  >
    <app-current-situation
      [inspectionSheetType]="inspectionSheetType"
      (formChanged)="onFormChange($event)"
      [status]="dados.status"
      [locked]="dados.locked"
    ></app-current-situation>
  </div>
  <div class="row mt-2">
    <div class="col-md-12">
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesError"
      ></app-alert>
    </div>
  </div>

  <div class="row mt-3">
    <div class="col-md-12 d-flex justify-content-end mb-3">
      <app-button
        *ngIf="hasPreviousTab()"
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Retornar'"
        class="me-1"
        (click)="onPrevious()"
        [disabled]="!allowTabNavigation"
      ></app-button>

      <app-button
        *ngIf="hasNextTab()"
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-right'"
        [label]="'Avançar'"
        (click)="onNext()"
        [disabled]="!allowTabNavigation"
      ></app-button>

      <app-button
        *ngIf="
          hasPreviousTab() &&
          !hasNextTab() &&
          dados.status === 1 &&
          allowTabNavigation
        "
        [class]="'btn-logisoil-green'"
        [icon]="'fa fa-arrow-circle-o-down'"
        [label]="'Finalizar Preenchimento'"
        (click)="onSave({ userCompletedTheInspection: true })"
        [disabled]="!allowTabNavigation"
      ></app-button>
    </div>
  </div>

  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/inspections']"
    ></app-button>
  </div>
</div>

<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
></app-modal-confirm>

<ngx-spinner
  bdColor="rgba(51,51,51,0.1)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
