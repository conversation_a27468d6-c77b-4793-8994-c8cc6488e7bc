<form [formGroup]="formReading" class="mb-3">
  <!-- Instrumento -->
  <div class="col-md-2">
    <label class="form-label">Instrumento</label>
    <select
      class="form-select"
      formControlName="instrument"
      (change)="changeInstrument(formReading.controls['instrument'].value)"
    >
      <option value="" *ngIf="formReading.controls['instrument'].value == ''">
        Selecione...
      </option>
      <option
        *ngFor="let instrumentItem of instrumentsList"
        [ngValue]="instrumentItem.id"
      >
        {{ instrumentItem.identifier }}
      </option>
    </select>
    <small
      class="form-text text-danger"
      *ngIf="
        !formReading.get('instrument').valid &&
        formReading.get('instrument').touched &&
        !formReading.get('instrument').disabled
      "
      >Campo Obrigatório.</small
    >
  </div>
  <!-- Apenas PZE -->
  <div class="col-md-2 ms-3">
    <label class="form-label">Célula de pressão</label>
    <input type="text" class="form-control" formControlName="measure" />
  </div>

  <!-- Data e hora -->
  <div class="col-md-2 ms-3">
    <label class="form-label">Data e hora</label>
    <input type="datetime-local" class="form-control" formControlName="date" />
    <small
      class="form-text text-danger"
      *ngIf="!formReading.get('date').valid && formReading.get('date').touched"
      >Campo Obrigatório.</small
    >
  </div>

  <!-- Cota Piezométrica -->
  <div class="col-md-2 ms-3">
    <label class="form-label">Cota Piezométrica</label>
    <div class="input-group">
      <input
        type="text"
        class="form-control"
        formControlName="quota"
        (blur)="func.formatType($event)"
        (focus)="func.formatType($event)"
        (keypress)="func.controlNumber($event, null, 'notE')"
        (keyup)="func.controlNumber($event, formReading.get('quota'))"
        appDisableScroll
      /><span class="input-group-text">{{ units[0] }}</span>
    </div>
  </div>

  <!-- Seco -->
  <div class="col-md-2 ms-3 align-self-end">
    <input
      class="form-check-input me-1"
      formControlName="dry"
      type="checkbox"
      value=""
      (change)="toogleDry()"
    />
    <label class="form-label">Seco</label>
  </div>
</form>
