<div class="row" [formGroup]="formHierarchy">
  <!-- Cliente -->
  <div [ngClass]="col" *ngIf="elements.clients" tourAnchor="hierarchy_client">
    <label class="form-label">Cliente</label>
    <ng-multiselect-dropdown
      [placeholder]="'Selecione...'"
      [settings]="config.clients.settings"
      [data]="config.clients.data"
      formControlName="clients"
      (onSelect)="onEvent($event, 'select', 'clients')"
      (onDeSelect)="onEvent($event, 'deselect', 'clients')"
      [ngClass]="{
        'disabled-dropdown': disabled
      }"
    >
    </ng-multiselect-dropdown>
    <small
      class="form-text text-danger"
      *ngIf="
        validate &&
        formHierarchy.get('clients').invalid &&
        formHierarchy.get('clients').touched
      "
      >Campo Obrigatório.</small
    >
    <div
      tourAnchor="hierarchy_client_checkbox"
      class="form-check"
      *ngIf="
        this.elements.clients.hasOwnProperty('active') &&
        !disabled &&
        (user.role === 'Super Suporte' || user.role === 'Suporte')
      "
    >
      <input
        class="form-check-input"
        type="checkbox"
        value=""
        [checked]="!this.elements.clients.active"
        (change)="controlActive('clients', $event)"
      />

      <label class="form-label"> Incluir inativos </label>
    </div>
  </div>

  <!-- Unidade -->
  <div [ngClass]="col" *ngIf="elements.units" tourAnchor="hierarchy_unit">
    <label class="form-label">Unidade</label>
    <ng-multiselect-dropdown
      [placeholder]="'Selecione...'"
      [settings]="config.units.settings"
      [data]="config.units.data"
      formControlName="units"
      (onSelect)="onEvent($event, 'select', 'units')"
      (onDeSelect)="onEvent($event, 'deselect', 'units')"
      [ngClass]="{
        'disabled-dropdown': disabled
      }"
    >
    </ng-multiselect-dropdown>
    <small
      class="form-text text-danger"
      *ngIf="
        validate &&
        formHierarchy.get('units').invalid &&
        formHierarchy.get('units').touched
      "
      >Campo Obrigatório.</small
    >
    <div
      tourAnchor="hierarchy_unit_checkbox"
      class="form-check"
      *ngIf="
        this.elements.units.hasOwnProperty('active') &&
        !disabled &&
        (user.role === 'Super Suporte' || user.role === 'Suporte')
      "
    >
      <input
        class="form-check-input"
        type="checkbox"
        value=""
        [checked]="!this.elements.units.active"
        (change)="controlActive('units', $event)"
      />
      <label class="form-label"> Incluir inativos </label>
    </div>
  </div>

  <!-- Estrutura -->
  <div
    [ngClass]="col"
    *ngIf="elements.structures"
    tourAnchor="hierarchy_sctructure"
  >
    <label class="form-label">Estrutura</label>
    <ng-multiselect-dropdown
      [placeholder]="'Selecione...'"
      [settings]="config.structures.settings"
      [data]="config.structures.data"
      formControlName="structures"
      (onSelect)="onEvent($event, 'select', 'structures')"
      (onDeSelect)="onEvent($event, 'deselect', 'structures')"
      [ngClass]="{
        'disabled-dropdown': disabled
      }"
    >
    </ng-multiselect-dropdown>
    <small
      class="form-text text-danger"
      *ngIf="
        validate &&
        formHierarchy.get('structures').invalid &&
        formHierarchy.get('structures').touched
      "
      >Campo Obrigatório.</small
    >
    <div
      tourAnchor="hierarchy_structure_checkbox"
      class="form-check"
      *ngIf="
        this.elements.structures.hasOwnProperty('active') &&
        !disabled &&
        (user.role === 'Super Suporte' || user.role === 'Suporte')
      "
    >
      <input
        class="form-check-input"
        type="checkbox"
        value=""
        [checked]="!this.elements.structures.active"
        (change)="controlActive('structures', $event)"
      />
      <label class="form-label"> Incluir inativos </label>
    </div>
  </div>
</div>
