.breadcrumb-group {
  box-sizing: border-box;
  height: 35px;
  background-color: #ffffff;
  border-radius: 8px;
  border: rgba(0, 0, 0, 0.29) 1px solid;
  display: flex;
  flex-direction: row;
  margin: 0;
  padding: 0;
}

.breadcrumb-li {
  list-style: none;
  margin: 0 8px;
}

.breadcrumb-li a {
  margin-top: 9px;
  line-height: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.breadcrumb-li a i {
  color: #34b575;
  font-size: 0.875em;
}

.breadcrumb-li a img {
  filter: invert(53%) sepia(92%) saturate(368%) hue-rotate(99deg)
    brightness(94%) contrast(50%);
  height: 16px;
}

.breadcrumb-li a span {
  color: #34b575;
  margin-left: 5px;
  font-size: 0.875em;
  font-weight: 100;
}

li.breadcrumb-li + li.breadcrumb-li a {
  border-left: 1px solid #34b575;
  padding-left: 16px;
}

.last-item {
  margin-left: auto; /* Empurra o último item para a direita */
}
