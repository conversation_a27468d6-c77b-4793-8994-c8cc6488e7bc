import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';

import { GoogleMapsComponent } from '@components/google-maps/google-maps.component';

import { CoordinateService } from 'src/app/services/coordinate.service';

import { SharedService } from 'src/app/services/shared.service';

@Component({
  selector: 'app-modal-map',
  templateUrl: './modal-map.component.html',
  styleUrls: ['./modal-map.component.scss']
})
export class ModalMapComponent implements OnInit, OnChanges, AfterViewInit {
  @ViewChild('modalMap') modalMap: ElementRef;
  @ViewChild(GoogleMapsComponent) googleMaps: GoogleMapsComponent;
  @Output() public sendClickEvent = new EventEmitter();
  @Input() public title: any = null;
  @Input() public coordinates: any = null;
  @Input() public data: any = {};

  public formCoordinates: FormGroup = new FormGroup({
    datum: new FormControl('', [Validators.required]),
    coordinate_format: new FormControl(null, [Validators.required]),
    zone_number: new FormControl({ value: '', disabled: true }),
    zone_letter: new FormControl({ value: '', disabled: true }),
    northing: new FormControl({ value: null, disabled: true }, [Validators.required]),
    easting: new FormControl({ value: null, disabled: true }, [Validators.required]),
    latitude: new FormControl({ value: null, disabled: true }),
    longitude: new FormControl({ value: null, disabled: true })
  });

  public dataMaps = {
    height: '500px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  public clickEventsubscription: Subscription;

  constructor(public activeModal: NgbActiveModal, private sharedService: SharedService, private coordinateService: CoordinateService) {
    this.clickEventsubscription = this.sharedService.getClickEvent().subscribe((instrument) => {
      this.eventMap(instrument);
    });
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {}

  ngAfterViewInit() {
    this.managerMap();
  }

  managerMap() {
    this.dataMaps.center.lat = this.coordinates.coordinate_systems.decimal_geodetic.latitude;
    this.dataMaps.center.lng = this.coordinates.coordinate_systems.decimal_geodetic.longitude;
    this.dataMaps.markers[0].position.lat = this.coordinates.coordinate_systems.decimal_geodetic.latitude;
    this.dataMaps.markers[0].position.lng = this.coordinates.coordinate_systems.decimal_geodetic.longitude;
    setTimeout(() => {
      this.sendDataMap('markersMultiple');
    }, 100);
  }

  sendDataMap(option, clear = true) {
    this.googleMaps.setDataMap(this.dataMaps, option, clear);
  }

  eventMap($event) {
    switch ($event.type) {
      case 'click':
        this.formCoordinates.controls['coordinate_format'].setValue(1);
        this.formCoordinates.controls['datum'].setValue(this.coordinates.datum);
        this.formCoordinates.controls['latitude'].setValue($event.data.lat());
        this.formCoordinates.controls['longitude'].setValue($event.data.lng());
        this.coordinatesConversion();
        break;
    }
  }

  coordinatesConversion(type: string = '') {
    this.coordinateService.coordinatesConversion(this.formCoordinates, type).subscribe((coordinates) => {
      if (coordinates !== null) {
        if (coordinates.type == 'UTM') {
          this.formCoordinates.controls['zone_letter'].setValue(coordinates.zone_letter);
          this.formCoordinates.controls['zone_number'].setValue(coordinates.zone_number);
          this.formCoordinates.controls['northing'].setValue(coordinates.northing);
          this.formCoordinates.controls['easting'].setValue(coordinates.easting);
        } else if (coordinates.type == 'Geodetic') {
          this.formCoordinates.controls['latitude'].setValue(coordinates.latitude);
          this.formCoordinates.controls['longitude'].setValue(coordinates.longitude);
        }
      }
    });
  }

  confirm() {
    let coordinates = {
      datum: this.formCoordinates.controls['datum'].value,
      coordinate_format: this.formCoordinates.controls['coordinate_format'].value,
      zone_number: this.formCoordinates.controls['zone_number'].value,
      zone_letter: this.formCoordinates.controls['zone_letter'].value,
      northing: this.formCoordinates.controls['northing'].value,
      easting: this.formCoordinates.controls['easting'].value,
      latitude: this.formCoordinates.controls['latitude'].value,
      longitude: this.formCoordinates.controls['longitude'].value
    };
    this.sendClickEvent.emit({ type: 'coordinates', coordinates: coordinates, data: this.data });
    this.activeModal.close('Close click');
  }
}
