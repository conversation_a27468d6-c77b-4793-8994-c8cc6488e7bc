<form class="g-3" [formGroup]="formLayers">
  <div class="row">
    <div class="col-md-12">
      <span class="fw-bold text-instructions">
        Cadastro de layer do tipo "Dam Break" (Opcional)
      </span>
      <br />
      <span class="fw-bold text-instructions">
        <i class="fa fa-exclamation-circle me-2" aria-hidden="true"></i
        >Instruções:
      </span>
      <br />
      <span>
        <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
        Garanta que haja no máximo uma layer do tipo "Dam Break" por estrutura.
      </span>
      <br />
      <span>
        <i class="fa fa-arrow-right me-1" aria-hidden="true"></i>
        Tamanho máximo permitido : {{ maxFileSize / 1000000 }} MB.
      </span>
    </div>
  </div>

  <!-- Upload de arquivo (somente se não estiver no modo visualização) -->
  <div class="mt-2 row" *ngIf="filesArray.length < maxFiles && !view">
    <div class="col-md-6">
      <label class="form-label">Upload documento .kml ou .kmz:</label>
      <input
        type="file"
        class="form-control"
        formControlName="layer"
        accept=".kml, .kmz"
        (change)="uploadFile($event)"
        [disabled]="view"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formLayers.get('layer').valid && formLayers.get('layer').touched
        "
        ><i class="bi bi-x-circle me-2"></i>Formato de arquivo inválido. <br
      /></small>

      <small class="form-text text-danger" *ngIf="!limitFileSize"
        ><i class="bi bi-x-circle me-2"></i>Excedido o tamanho máximo permitido
        de {{ maxFileSize / 1000000 }} MB <br
      /></small>

      <small class="form-text text-danger" *ngIf="!limitsFiles"
        ><i class="bi bi-x-circle me-2"></i>Excedido o número máximo permitido
        de uploads.
      </small>
    </div>
  </div>

  <!-- Visualização simples do nome do arquivo quando estiver em modo de visualização -->
  <div class="mt-2 row" *ngIf="view">
    <div class="col-md-6">
      <label class="form-label">Documento anexado:</label>
      <div
        class="form-control bg-light d-flex align-items-center"
        style="min-height: 38px"
      >
        <i class="fa fa-file me-2 text-muted"></i>
        <ng-container *ngIf="filesArray.length > 0; else noFile">
          <a
            class="text-decoration-none"
            [href]="filesArray[0].fileContentDownload"
            [download]="filesArray[0].fileName"
            target="_blank"
          >
            <i class="fa fa-download me-2 text-success"></i>
            {{ filesArray[0].fileName }}
          </a>
        </ng-container>
        <ng-template #noFile>
          <span class="text-muted">
            <i class="fa fa-file me-2 text-muted"></i> Nenhum arquivo anexado
          </span>
        </ng-template>
      </div>
    </div>
  </div>

  <!-- Tabela de arquivos anexados -->
  <div class="row mt-2" *ngIf="filesArray.length > 0">
    <div class="col-md-6">
      <table class="table align-middle table-bordered table-hover">
        <thead class="table-light">
          <tr>
            <th style="width: 75%">Arquivo</th>
            <th style="width: 25%" *ngIf="!view">Ação</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let fileItem of filesArray; let i = index">
            <tr>
              <td>{{ fileItem.fileName }}</td>
              <td *ngIf="!view">
                <app-button
                  [class]="'btn-logisoil-red'"
                  [icon]="'fa fa-thin fa-xmark'"
                  [label]="'Remover'"
                  [type]="true"
                  class="me-1"
                  (click)="removeFile(i)"
                >
                </app-button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</form>
