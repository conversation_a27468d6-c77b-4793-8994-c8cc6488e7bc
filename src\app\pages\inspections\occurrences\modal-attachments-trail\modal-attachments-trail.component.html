<div
  class="modal d-block show fade"
  tabindex="-1"
  role="dialog"
  style="background: rgba(0, 0, 0, 0.4)"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Exibir anexos</h5>
        <button
          type="button"
          class="btn-close"
          aria-label="Fechar"
          (click)="close.emit()"
        ></button>
      </div>

      <div class="modal-body">
        <!-- Alerta -->
        <div
          *ngIf="attachmentsTrail.length === 0"
          class="alert alert-warning d-flex align-items-center"
          role="alert"
        >
          <i class="fa fa-info-circle me-2"></i>
          Nenhuma ocorrência anterior foi registrada para esta ficha.
        </div>

        <ng-container *ngIf="attachmentsTrail.length > 0">
          <p>
            A ocorrência <strong>{{ occurrenceSearchIdentifier }}</strong> é
            parte de uma trilha: este mesmo item foi observado (e anexos foram
            inseridos) em várias fichas de inspeção ao longo do tempo.
          </p>
          <p>
            Para visualizar os anexos no mapa, selecione quais fichas desta
            trilha deseja buscar:
          </p>

          <!-- Tabela -->
          <app-table
            [tableHeader]="tableHeader"
            [tableData]="tableData"
            (sendClickRowEvent)="clickRowEvent($event)"
          ></app-table>

          <!-- Paginação -->
          <div class="row mt-3">
            <app-paginator
              [collectionSize]="collectionSize"
              [page]="page"
              [maxSize]="10"
              [boundaryLinks]="true"
              [pageSize]="pageSize"
              (sendPageChange)="loadPage($event)"
            ></app-paginator>
          </div>
        </ng-container>
      </div>

      <div class="modal-footer">
        <!-- Alerta caso não haja nenhum anexo disponível -->
        <div
          *ngIf="attachmentsTrail.length > 0 && !hasAttachmentsToMap"
          class="alert alert-warning d-flex align-items-center w-100 m-0"
          role="alert"
        >
          <i class="fa fa-info-circle me-2"></i>
          Nenhum anexo disponível para adicionar no mapa.
        </div>

        <!-- Botão Adicionar no Mapa (aparece só se houver anexo possível) -->
        <app-button
          *ngIf="attachmentsTrail.length > 0 && hasAttachmentsToMap"
          [class]="'btn-logisoil-green'"
          [label]="'Adicionar anexos no mapa'"
          [disabled]="!hasValidSelection"
          (click)="emitAddToMap()"
        ></app-button>

        <!-- Sempre exibe o botão Fechar -->
        <app-button
          [class]="'btn-logisoil-gray'"
          [label]="'Fechar'"
          (click)="close.emit()"
        ></app-button>
      </div>
    </div>
  </div>
</div>
