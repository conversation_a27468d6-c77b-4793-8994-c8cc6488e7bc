import { Injectable } from '@angular/core';
import { Idle, DEFAULT_INTERRUPTSOURCES } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class IdleService {
  private idleState: string = 'Not started';
  private timedOut: boolean = false;
  public idleDetected$ = new Subject<void>();
  public activityDetected$ = new Subject<void>();

  private activityEvents = ['mousemove', 'keydown', 'click', 'touchstart']; // Eventos monitorados

  constructor(private idle: Idle, private keepalive: Keepalive) {
    this.registerActivityDetection();
  }

  setupIdleDetection(idleTime: number = 300, timeout: number = 30): void {
    this.idle.setIdle(idleTime);
    this.idle.setTimeout(timeout);
    this.idle.setInterrupts(DEFAULT_INTERRUPTSOURCES);

    this.idle.onIdleStart.subscribe(() => {
      this.idleDetected$.next(); // Notifica inatividade
    });

    this.idle.onTimeout.subscribe(() => {
      this.logoutUser();
    });

    this.idle.watch();
    this.idleState = 'Started';
    this.timedOut = false;
  }

  private registerActivityDetection(): void {
    this.activityEvents.forEach((event) => document.addEventListener(event, this.handleActivity));
  }

  private handleActivity = (): void => {
    this.activityDetected$.next();
    this.resetIdleState();
  };

  private resetIdleState(): void {
    this.idle.watch(); // Reinicia o monitoramento de inatividade
  }

  stopIdleDetection(): void {
    this.idle.stop();
    this.idleState = 'Stopped';
    this.unregisterActivityDetection();
  }

  private unregisterActivityDetection(): void {
    this.activityEvents.forEach((event) => document.removeEventListener(event, this.handleActivity));
  }

  private logoutUser(): void {}
}
