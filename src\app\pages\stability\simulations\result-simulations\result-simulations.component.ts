import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { NgxSpinnerService } from 'ngx-spinner';
import { UserService } from 'src/app/services/user.service';

import { SimulatorService as SimulatorServiceApi } from 'src/app/services/api/simulator.service';

import * as moment from 'moment';

import {
  PhreaticPiezometricFields,
  ConditionFields,
  CalculationMethods,
  Conditions,
  Period,
  PhreaticPiezometric,
  UpstreamWaterLevelReference,
  DownstreamWaterLevelReference,
  BeachLengthReference,
  ReferenceReadings,
  SurfaceType,
  SearchMethod,
  SimulatorFields,
  SectionFields,
  SearchMethodsFieldsLabels
} from 'src/app/constants/simulations.constants';

import { surfacesType as SearchMethodsFields } from 'src/app/constants/structure.constants';
import { typeInstruments as TypeInstruments } from 'src/app/constants/instruments.constants';
import { SliFileType } from 'src/app/constants/stability.constants';

import fn from 'src/app/utils/function.utils';

import { DomSanitizer } from '@angular/platform-browser';
import { format } from 'date-fns';

@Component({
  selector: 'app-result-simulations',
  templateUrl: './result-simulations.component.html',
  styleUrls: ['./result-simulations.component.scss']
})
export class ResultSimulationsComponent implements OnInit {
  public resultSimulation: any = null;
  public resumeSimulationParams: any = null;
  public resumeSimulationPairs: any = null;
  public resumeSimulationSection: any = null;
  public resumeSimulationSectionInstruments: any = null;
  public resumeSimulationCircular: any = null;
  public resumeSimulationNonCircular: any = null;
  public resultSimulationSection: any = null;

  public mapSliFileType = SliFileType.reduce((acc, item) => {
    acc[item.value] = item;
    return acc;
  }, {});

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Instrumento',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['instrument_identifier']
    },
    {
      label: 'Cota de leitura (m)',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['quota']
    },
    {
      label: 'Data da leitura',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['']
    }
  ];

  public mmt = moment;

  constructor(
    private activatedRoute: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private ngxSpinnerService: NgxSpinnerService,
    private simulatorServiceApi: SimulatorServiceApi,
    private userService: UserService
  ) {}

  /**
   * Método executado ao inicializar o componente.
   * Obtém o perfil do usuário e verifica se há um ID de simulação na rota ativa para buscar os dados da simulação.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    if (this.activatedRoute.snapshot.params.simulationId) {
      this.getSimulations(this.activatedRoute.snapshot.params.simulationId);
    }
  }

  /**
   * Busca as informações de uma simulação com base no ID fornecido.
   * Exibe um spinner enquanto os dados são carregados.
   * @param {string} simulationId - O ID da simulação a ser buscada.
   */
  getSimulations(simulationId: string) {
    this.ngxSpinnerService.show();

    this.simulatorServiceApi.getSimulationsById(simulationId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.formatData(dados);
      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Formata os dados recebidos de uma simulação.
   * Processa e organiza os dados para exibição, incluindo parâmetros, seções e tipos de superfície.
   * @param {any} $dados - Dados da simulação recebidos da API.
   */
  formatData($dados) {
    this.resultSimulation = $dados;

    this.resultSimulation['file_zip_all'] = this.resultSimulation.sections[0].section_name + ' - ' + format(new Date(), 'yyyy.MM.dd') + '.zip';

    const reviewId = this.resultSimulation.sections[0].section_review_index ? ` - Revisão ${this.resultSimulation.sections[0].section_review_index}, ` : '';
    const date = this.resultSimulation.sections[0].section_review_start_date
      ? moment(this.resultSimulation.sections[0].section_review_start_date).format('DD/MM/YYYY HH:mm:ss')
      : '';
    this.resultSimulation['section_review'] = `${reviewId} ${date}`;

    this.formatResumeSimulationParams($dados);
    this.formatResumeSimulation($dados);
    this.formatResumeSimulationSection($dados);
    this.formatResumeSimulationSectionInstruments($dados);
    this.formatResumeSimulationSurfaceType($dados);
    this.formatResultSimulationSection($dados);
  }

  /**
   * Formata os parâmetros da simulação, incluindo as condições avaliadas, o método freático/piezométrico e o fator de segurança alvo.
   * @param {any} $dados - Dados da simulação.
   */
  formatResumeSimulationParams($dados) {
    // Montar a string de condições
    const conditionsArray = [];
    if ($dados.should_evaluate_drained_condition) {
      conditionsArray.push('Drenada');
    }
    if ($dados.should_evaluate_undrained_condition) {
      conditionsArray.push('Não drenada');
    }
    if ($dados.should_evaluate_pseudo_static_condition) {
      conditionsArray.push('Pseudo estática');
    }
    const conditions = conditionsArray.join(', ') || '-';

    const phreaticPiezometric = PhreaticPiezometric.find((item) => item.value === $dados.water_table_configuration)?.label || '-';

    // Montar o JSON final
    this.resumeSimulationParams = {
      conditions: conditions,
      phreaticPiezometric: phreaticPiezometric,
      safetyFactorTarget: $dados.safety_factor_target || '-'
    };
  }

  /**
   * Formata o resumo geral da simulação, mapeando os campos necessários para exibição.
   * Agrupa os campos em pares de dois.
   * @param {any} $dados - Dados da simulação.
   */
  formatResumeSimulation($dados) {
    let allFields = [];

    // Mapeamento de constantes
    const constantsMap = {
      ReferenceReadings,
      CalculationMethods,
      Conditions,
      Period,
      PhreaticPiezometric,
      UpstreamWaterLevelReference,
      DownstreamWaterLevelReference,
      BeachLengthReference,
      SurfaceType,
      SearchMethod,
      SimulatorFields
    };

    // Extrair campos de ConditionFields
    ConditionFields.forEach((condition) => {
      if ($dados[condition.reference]) {
        allFields = allFields.concat(condition.fields);
      }
    });

    // Extrair campos de PhreaticPiezometricFields com base em water_table_configuration
    const phreaticFields = PhreaticPiezometricFields.find((item) => item.phreatic_piezometric === $dados.water_table_configuration);
    if (phreaticFields) {
      allFields = allFields.concat(phreaticFields.fields);
    }

    // Remover duplicações com base no label dos campos
    const uniqueFields = allFields.filter((field, index, self) => index === self.findIndex((f) => f.label === field.label));

    // Adicionar a propriedade value a cada item de uniqueFields
    uniqueFields.forEach((field) => {
      if (field.constant && constantsMap[field.constant]) {
        // Se a constante estiver definida e existir no mapeamento, procurar o valor correspondente
        const constantArray = constantsMap[field.constant];
        const matchingItem = constantArray.find((item) => item.value === $dados[field.reference]);
        field.value = matchingItem ? matchingItem.label : '-';
      } else if (field.reference === 'seismic_coefficient.horizontal') {
        field.value = $dados.seismic_coefficient ? $dados.seismic_coefficient.horizontal : '-';
      } else if (field.reference === 'seismic_coefficient.vertical') {
        field.value = $dados.seismic_coefficient ? $dados.seismic_coefficient.vertical : '-';
      } else if (field.reference === 'start_date') {
        field.value = $dados.start_date ? moment($dados.start_date).tz('America/Sao_Paulo').format('DD/MM/YYYY') : '-';
      } else if (field.reference === 'end_date') {
        field.value = $dados.end_date ? moment($dados.end_date).tz('America/Sao_Paulo').format('DD/MM/YYYY') : '-';
      } else {
        field.value = $dados[field.reference] || '-';
      }
    });

    this.resumeSimulationPairs = [];
    for (let i = 0; i < uniqueFields.length; i += 2) {
      this.resumeSimulationPairs.push(uniqueFields.slice(i, i + 2));
    }
  }

  /**
   * Formata as seções da simulação, mapeando os campos das seções e agrupando-os em pares.
   * @param {any} $dados - Dados da simulação.
   */
  formatResumeSimulationSection($dados) {
    const sectionResumes = [];

    // Mapeamento de constantes
    const constantsMap = {
      BeachLengthReference
      // Adicionar outras constantes ao mapeamento conforme necessário
    };

    $dados.sections.forEach((section) => {
      let allFields = [];

      // Processar diretamente os campos de SectionFields
      SectionFields.forEach((sectionField) => {
        if (section[sectionField.reference] !== undefined) {
          allFields.push(sectionField);
        }
      });

      // Remover duplicações com base no label dos campos
      const uniqueFields = allFields.filter((field, index, self) => index === self.findIndex((f) => f.label === field.label));

      // Adicionar a propriedade value a cada item de uniqueFields
      uniqueFields.forEach((field) => {
        if (field.constant && constantsMap[field.constant]) {
          // Se a constante estiver definida e existir no mapeamento, procurar o valor correspondente
          const constantArray = constantsMap[field.constant];
          const matchingItem = constantArray.find((item) => item.value === section[field.reference]);
          field.value = matchingItem ? matchingItem.label : '-';
        } else {
          field.value = section[field.reference] || '-';
        }
      });

      // Agrupar os campos em pares de dois
      const fields = [];
      for (let i = 0; i < uniqueFields.length; i += 2) {
        fields.push(uniqueFields.slice(i, i + 2));
      }

      // Adicionar o resumo da seção ao array sectionResumes
      sectionResumes.push({
        sectionId: section.section_id,
        sectionName: section.section_name || '-',
        fields: fields,
        warnings: (section.warnings ?? []).map((warning) => ({
          ...warning,
          created_date: moment(warning.created_date).format('DD/MM/YYYY HH:mm:ss')
        })),
        section_review_index: section.section_review_index || null,
        section_review_start_date: section.section_review_start_date || null,
        constructionStage: section.construction_stage || null,
        constructionStageId: section.construction_stage_id || null
      });
    });

    this.resumeSimulationSection = sectionResumes;
  }

  /**
   * Formata os instrumentos presentes em cada seção da simulação.
   * Mapeia os instrumentos e exibe seus identificadores e cotas.
   * @param {any} $dados - Dados da simulação.
   */
  formatResumeSimulationSectionInstruments($dados) {
    this.resumeSimulationSectionInstruments = {};

    $dados.sections.forEach((section) => {
      if (section.instruments && section.instruments.length > 0) {
        const instruments = section.instruments.map((instrument) => {
          // Encontrar o nome do tipo de instrumento com base no ID
          const instrumentType = TypeInstruments.find((type) => type.id === instrument.instrument_type)?.name || '-';

          return {
            instrumentIdentifier: instrument.instrument_identifier || '-',
            instrumentType: instrumentType,
            measureIdentifier: instrument.measurement_identifier || '-',
            quota: instrument.quota || '-',
            dry: instrument.dry ? 'Seco' : '-'
          };
        });

        // Atribuir a lista de instrumentos ao section_id como chave
        this.resumeSimulationSectionInstruments[section.section_id] = {
          instruments: instruments
        };
      } else {
        // Se a seção não tiver instrumentos, definir instruments como null
        this.resumeSimulationSectionInstruments[section.section_id] = {
          instruments: []
        };
      }
    });
  }

  /**
   * Mapeia os métodos de cálculo com base nos IDs e no array de métodos fornecidos.
   * @param {number[]} calculationMethodIds - IDs dos métodos de cálculo.
   * @param {any[]} methods - Lista de métodos de cálculo disponíveis.
   * @returns {string[]} - Lista de nomes dos métodos de cálculo correspondentes.
   */
  mapCalculationMethods(calculationMethodIds, methods) {
    return calculationMethodIds
      .map((id) => {
        const method = methods.find((m) => m.id === String(id)); // Comparar usando String
        return method ? method.name : null;
      })
      .filter((name) => name !== null);
  }

  /**
   * Gera o resumo da simulação para um tipo de superfície (circular ou não circular).
   * Inclui os métodos de cálculo e de busca utilizados.
   * @param {any} slide2Config - Configurações da simulação.
   * @param {any} surfaceType - Tipo de superfície.
   * @returns {any} - Objeto contendo o resumo da simulação.
   */
  generateResumeSimulation(slide2Config, surfaceType) {
    const { search, id } = surfaceType;
    const params = id === '1' ? slide2Config.circular_parameters : slide2Config.non_circular_parameters;

    // Mapeando os métodos de cálculo
    const calculationMethod = this.mapCalculationMethods(params.calculation_methods, CalculationMethods);

    // Localizando o método de busca
    const searchMethodConfig = search.find((s) => s.value === String(params.circular_search_method || params.non_circular_search_method));

    // Construindo os campos de busca
    const searchMethodFields = searchMethodConfig.fields.map((fieldKey) => {
      const fieldLabel = SearchMethodsFieldsLabels[fieldKey].name;
      const fieldValue = params[fieldKey];
      return {
        label: fieldLabel,
        value: fieldValue
      };
    });

    return {
      calculationMethod,
      searchMethod: searchMethodConfig.method,
      searchMethodFields
    };
  }

  /**
   * Formata o resumo dos tipos de superfície (circular e não circular) utilizados na simulação.
   * @param {any} $dados - Dados da simulação.
   */
  formatResumeSimulationSurfaceType($dados) {
    // Verificar se circular_parameters existe
    if ($dados.slide2_configuration && $dados.slide2_configuration.circular_parameters) {
      this.resumeSimulationCircular = this.generateResumeSimulation($dados.slide2_configuration, SearchMethodsFields.surface_type_circular);
    } else {
      this.resumeSimulationCircular = null;
    }

    // Verificar se non_circular_parameters existe
    if ($dados.slide2_configuration && $dados.slide2_configuration.non_circular_parameters) {
      this.resumeSimulationNonCircular = this.generateResumeSimulation($dados.slide2_configuration, SearchMethodsFields.surface_type_non_circular);
    } else {
      this.resumeSimulationNonCircular = null;
    }
  }

  /**
   * Formata os resultados das seções da simulação, incluindo arquivos DXF, PNG e ZIP associados.
   * @param {any} $dados - Dados da simulação.
   */
  formatResultSimulationSection($dados) {
    this.resultSimulationSection = {};

    $dados.sections.forEach((section) => {
      if (section.results && section.results.length > 0) {
        let results = section.results.map((result) => {
          // Encontrar o nome do método de cálculo com base no ID
          const calculationMethod = CalculationMethods.find((method) => method.id === String(result.calculation_method))?.name || '-';

          // Encontrar o surfaceType e condition com base no sli_file_type
          const sliFileType = this.mapSliFileType[result.sli_file_type];
          const surfaceType = SurfaceType.find((type) => type.value === sliFileType?.surfaceTypeId)?.label || '-';
          const condition = Conditions.find((cond) => cond.value === sliFileType?.conditionId)?.label || '-';

          let fileNameBase =
            section.section_name +
            ' ' +
            fn.convertToValidFilename(surfaceType + ' ' + condition + ' ' + calculationMethod) +
            ' - ' +
            format(new Date(), 'yyyy.MM.dd');

          // Construindo a propriedade dxfFile
          const dxfFile = {
            fileContent: result.dxf_file.base64,
            fileContentDownload: this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + result.dxf_file.base64),
            fileName: fileNameBase + '.dxf',
            fileView: fn.base64ToFile(result.dxf_file.base64, fileNameBase + '.dxf'),
            fileUrl: '' // Adicionar fileUrl vazio
          };

          // Construindo a propriedade pngFile
          const pngFile = {
            fileContent: result.png_file.base64,
            fileContentDownload: this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + result.png_file.base64),
            fileName: fileNameBase + '.png',
            fileView: fn.base64ToFile(result.png_file.base64, fileNameBase + '.png'),
            fileUrl: '' // Adicionar fileUrl vazio
          };

          // Construindo a propriedade zipFile
          const zipFile = {
            fileContent: '',
            fileContentDownload: '',
            fileName: fileNameBase + '.zip',
            fileView: '',
            fileUrl: result.zip_file_download_url // Adicionar fileUrl com o valor de result.zip_file
          };

          return {
            resultsId: result.id,
            fs: result.value,
            calculationMethod: calculationMethod,
            surfaceType: surfaceType,
            condition: condition,
            dxfFile: dxfFile,
            pngFile: pngFile,
            zipFile: zipFile
          };
        });

        // Ordenar os resultados
        results = this.orderResults(results);

        // Atribuir a lista de results ao section_id como chave
        this.resultSimulationSection[section.section_id] = {
          results: results
        };
      } else {
        this.resultSimulationSection[section.section_id] = {
          results: null
        };
      }
    });
  }

  /**
   * Ordena os resultados da simulação com base no tipo de superfície, condição e método de cálculo.
   * @param {any[]} results - Lista de resultados a serem ordenados.
   * @returns {any[]} - Lista ordenada de resultados.
   */
  orderResults(results) {
    return results.sort((a, b) => {
      if (a.surfaceType < b.surfaceType) return -1;
      if (a.surfaceType > b.surfaceType) return 1;
      if (a.condition < b.condition) return -1;
      if (a.condition > b.condition) return 1;
      if (a.calculationMethod < b.calculationMethod) return -1;
      if (a.calculationMethod > b.calculationMethod) return 1;
      return 0;
    });
  }

  /**
   * Baixa um arquivo (DXF, PNG ou ZIP) associado à simulação.
   * @param {string} type - O tipo de arquivo a ser baixado (DXF, PNG ou ZIP).
   * @param {any} file - Informações do arquivo a ser baixado.
   */
  downloadFile(type, file, fileName = '') {
    switch (type) {
      case 'DXF':
      case 'PNG':
        const blob = fn.base64toBlob(file.fileContent);
        const url = window.URL.createObjectURL(blob);
        this.forceDownload(url, file.fileName);
        break;
      case 'ZIP':
        this.getFileZip(file.fileUrl, 'result', file.fileName);
        break;
      case 'ZIPALL':
        this.getFileZip(file, 'simulation', fileName);
        break;
    }
  }

  /**
   * Faz o download de um arquivo ZIP com base na URL fornecida.
   * @param {string} url - A URL do arquivo ZIP.
   */
  getFileZip(url, type, fileName = '') {
    fileName != '' ? fileName : 'filename.zip';

    url = url.replace('api/v1', '');

    let method = type === 'result' ? 'getSimulationsResultZipFile' : 'getSimulationsZipFile';

    this.simulatorServiceApi[method](url).subscribe((resp: any) => {
      if (resp['status'] == 200) {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        const blob = new Blob([resp['body']], { type: resp['body'].type });
        const url = window.URL.createObjectURL(blob);
        this.forceDownload(url, fileName);
      }
    });
  }

  /**
   * Força o download de um arquivo através de um link temporário.
   * @param {string} file - A URL ou base64 do arquivo.
   * @param {string} name - O nome do arquivo para download.
   */
  forceDownload(file, name) {
    const link: any = document.createElement('a');
    link.href = file;
    link.download = name;

    document.body.appendChild(link);

    link.click();

    document.body.removeChild(link);
  }
}
