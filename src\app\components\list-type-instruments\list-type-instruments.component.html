<form [formGroup]="formTypeInstruments">
  <label class="form-label">{{ data.name }}</label>
  <ng-multiselect-dropdown
    [placeholder]="'Selecione...'"
    [settings]="dropdownSettings"
    [data]="data.instruments"
    (onSelect)="itemEvent($event, 'select')"
    (onSelectAll)="itemEvent($event, 'selectAll')"
    (onDeSelect)="itemEvent($event, 'deselect')"
    (onDeSelectAll)="itemEvent($event, 'deselectAll')"
    formControlName="typeInstrument"
  ></ng-multiselect-dropdown>
</form>
