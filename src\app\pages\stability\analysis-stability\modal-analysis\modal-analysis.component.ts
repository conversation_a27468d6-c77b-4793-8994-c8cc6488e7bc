import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { FormControl, FormGroup } from '@angular/forms';

import { MessagePadroes } from 'src/app/constants/message.constants';
import { CalculationMethods, SliFileType, StructureStatus } from 'src/app/constants/stability.constants';

import { StabilityService as StabilityServiceApi } from 'src/app/services/api/stability.service';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';

//Para colocar a URL do arquivo .dxf como seguro
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-modal-analysis',
  templateUrl: './modal-analysis.component.html',
  styleUrls: ['./modal-analysis.component.scss']
})
export class ModalAnalysisComponent implements OnInit, OnChanges {
  @ViewChild('modalAnalysis') ModalAnalysis: ElementRef;

  @Output() public sendClickEvent = new EventEmitter();

  @Input() public title: string = '';
  @Input() public sectionInfo: any = null;

  public formStabilityAnalysis: FormGroup = new FormGroup({
    CalculationMethods: new FormControl(''),
    SliFileType: new FormControl('')
  });

  public sliFileType = SliFileType;
  public calculationMethods = CalculationMethods;
  public structureStatus = StructureStatus;

  public ctrlBtnFilter: boolean = false;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public fileDxf: any = null;
  public fileContent: string = '';
  public fileName: string = '';
  public fileContentDownload: any = '';

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'Situação Atual da Estrutura',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['structure_status']
    },
    {
      label: 'Seção',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['section_name']
    },
    {
      label: 'Revisão Seção',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['section_review_index']
    },
    {
      label: 'Data/Horário',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['reading_created_date']
    }
  ];

  constructor(private modalService: NgbModal, private sanitizer: DomSanitizer, private stabilityServiceApi: StabilityServiceApi) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.sectionInfo && changes.sectionInfo.currentValue != null) {
      this.getStabilityAnalysisSection(changes.sectionInfo.currentValue.id);
    }
  }

  openModal() {
    this.modalService.open(this.ModalAnalysis, { fullscreen: true });
    if (this.sectionInfo && this.sectionInfo.id) {
      this.getStabilityAnalysisSection(this.sectionInfo.id);
    }
  }

  getSafetyFactorResult(): any | null {
    this.fileDxf = null;
    this.fileContent = '';
    this.fileName = '';
    this.fileContentDownload = '';

    const safetyFactorResults = this.tableData[0].safety_factor_results;
    const calculationMethod = this.formStabilityAnalysis.controls['CalculationMethods'].value;
    const sliFileType = this.formStabilityAnalysis.controls['SliFileType'].value;

    const result = safetyFactorResults.find((result) => result.calculation_method === calculationMethod && result.sli_file_type === sliFileType);

    if (fn.isEmpty(result)) {
      this.message.text = MessagePadroes.NoFileDXF;
      this.message.status = true;
      this.message.class = 'alert-warning';

      setTimeout(() => {
        this.message.status = false;
      }, 5000);
    } else {
      const params = {
        safetyFactorId: result.id,
        fileType: 1
      };

      this.getSafetyFactorId(params);
    }
  }

  //Tabela
  getStabilityAnalysisSection(id) {
    this.message.text = '';
    this.message.status = false;
    this.messagesError = [];

    this.stabilityServiceApi.getStabilityAnalysisSectionId(id).subscribe(
      (resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;

        if (dados) {
          this.tableData = dados ? [dados] : [];
          this.formatData();
        } else {
          this.tableData = [];
          this.message.text = MessagePadroes.NoRegister;
          this.message.status = true;
          this.message.class = 'alert-warning';
        }
      },
      (error) => {
        console.error('Erro ao carregar os dados:', error);
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
      }
    );
  }

  //Gerar Arquivo DXF
  getSafetyFactorId(params) {
    this.message.text = '';
    this.message.status = false;

    this.stabilityServiceApi.getSafetyFactorId(params.safetyFactorId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.loadDrawing(dados);
    });
  }

  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      item.work_quota = item.work_quota ? item.work_quota : '-';
      item.structure_status = fn.findIndexInArrayofObject(StructureStatus, 'value', item.structure_status, 'name');
      item.section_review_index = item.section_review_result.index;
      item.reading_created_date = moment(item.reading_created_date).format('DD/MM/YYYY HH:mm:ss');
      return item;
    });
  }

  loadDrawing(drawing) {
    if (drawing != null) {
      this.fileContent = drawing.base64;
      this.fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + this.fileContent);
      this.fileName = drawing.name;

      this.fileDxf = fn.base64ToFile(this.fileContent, this.fileName);
    }
  }

  resetVariables() {
    this.fileDxf = null;
    this.fileContent = '';
    this.fileName = '';
    this.fileContentDownload = '';

    this.tableData = [];
  }
}
