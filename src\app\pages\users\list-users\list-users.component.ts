import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

import { UsersService as UsersServiceApi } from 'src/app/services/api/users.service';
import { UserService } from 'src/app/services/user.service';
import { FilterService } from 'src/app/services/filter.service';

import { Status, MultiSelectDefault, accessLevel, statusLevel } from 'src/app/constants/app.constants';
import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';

import * as moment from 'moment';

import fn from 'src/app/utils/function.utils';
import { NgxSpinnerService } from 'ngx-spinner';

//Tour guiado
import { CustomTourService } from 'src/app/services/custom-tour.service';
import { TourService } from 'ngx-ui-tour-ng-bootstrap';

@Component({
  selector: 'app-list-users',
  templateUrl: './list-users.component.html',
  styleUrls: ['./list-users.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListUsersComponent implements OnInit {
  @ViewChild('hierarchy') hierarchy: any;

  public tableHeader: any = [
    {
      label: 'ID',
      width: '50px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Usuário',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['username']
    },
    {
      label: 'Data de Cadastro',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Termo de Serviço',
      width: '200px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['acceptance_date_of_terms']
    },
    {
      label: 'Última Visualização',
      width: '200px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['last_login_date']
    },
    {
      label: 'Acessos',
      width: '100px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['login_counter']
    },
    {
      label: 'Status',
      width: '100px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['active']
    },
    {
      label: 'Ações',
      width: '100px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['action'],
      extra: ['recovery']
    }
  ];

  public tableData: any = [];

  public func = fn;

  public accessLevelEnum = accessLevel;
  public roleUser: any = [];
  public status: any = Status;
  public units: any = [];

  public userSettings = MultiSelectDefault.Users;
  public viewSettings = MultiSelectDefault.View;

  public selectedColumns = this.tableHeader;

  public users: any = [];

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };
  public messageWarning: any = { text: '', status: false, class: 'alert-warning' };

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public filterBodyParams: any = {};
  public filterParams: any = {};

  public filter: any = {
    Username: [],
    FirstName: [],
    SearchIdentifier: '',
    Active: '',
    Role: ''
  };

  public filterBody: any = {
    client_ids: [],
    client_unit_ids: [],
    structure_ids: []
  };

  public userRestriction: any = [];

  public buttonAdd: boolean = true;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public user: any = {
    id: null,
    active: null
  };

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    }
  };

  public filterSearch: any = {};
  public filterSearchBody: any = {};

  constructor(
    private customTourService: CustomTourService,
    private filterService: FilterService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private userService: UserService,
    private usersServiceApi: UsersServiceApi,
    public tourService: TourService
  ) {}

  //Inicializa o componente, carrega os dados do usuário e configura permissões.
  ngOnInit(): void {
    this.ngxSpinnerService.show();

    this.roleUser = fn.enumToArray(accessLevel, {
      id: 'level',
      value: 'value'
    });

    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.getUser();

    if (!this.permissoes['/clients'].list) {
      delete this.elements.clients;
    }
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros após um pequeno delay.
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
    }, 1000);
  }

  /**
   * Obtém a lista de usuários com base nos parâmetros fornecidos e formata os dados para exibição.
   * @param {any} params - Parâmetros de consulta.
   * @param {any} bodyParams - Parâmetros do corpo da requisição.
   */
  getUsersList(params, bodyParams) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.usersServiceApi.getUsers(bodyParams, params).subscribe(
      (resp) => {
        let dados: any = resp;

        if (dados != null && dados != undefined) {
          dados = dados.body === undefined ? dados : dados.body;

          if (dados) {
            this.tableData = dados ? dados.data : [];
            this.collectionSize = dados.total_items_count;
            this.formatData();
          } else {
            this.tableData = [];
            this.collectionSize = 0;
            this.messageReturn.text = MessagePadroes.NoRegister;
            this.messageReturn.status = true;
            this.messageReturn.class = 'alert-warning';

            setTimeout(() => {
              this.messageReturn.status = false;
            }, 4000);
          }

          this.ngxSpinnerService.hide();
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
          this.messageReturn.class = 'alert-warning';

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
      }
    );
  }

  //Formata as datas exibidas na tabela de usuários.
  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      if (item.created_date) {
        item.created_date = moment(item.created_date).format('DD/MM/YYYY');
      }
      if (item.acceptance_date_of_terms) {
        item.acceptance_date_of_terms = moment(item.acceptance_date_of_terms).format('DD/MM/YYYY');
      }
      if (item.last_login_date) {
        item.last_login_date = moment(item.last_login_date).format('DD/MM/YYYY HH:mm:ss');
      }
      item.sendEmail = true;
      return item;
    });
  }

  /**
   * Alterna o status ativo/inativo de um usuário.
   * @param {any} user - O usuário que terá seu status alterado.
   */
  toggleStatus(user: any) {
    this.user.id = user.id;
    this.user.active = user.active;
    this.editUser();
  }

  /**
   * Reenvia um e-mail para o usuário com instruções para alteração de senha.
   * @param {any} user - O usuário que receberá o e-mail.
   */
  emailPassword(user: any) {
    this.usersServiceApi
      .putExecuteActionsEmail(user.id, {
        id: user.id,
        actions: [1]
      })
      .subscribe(
        (resp) => {
          const dados: any = resp;
          this.message.text = MessageCadastro.EnviarEmailNovaSenha;
          this.message.status = true;

          setTimeout(() => {
            this.message.status = false;
          }, 8000);

          let idx = fn.findIndexInArrayofObject(this.tableData, 'id', user.id);
          this.tableData[idx].sendEmail = false;

          setTimeout(() => {
            this.tableData[idx].sendEmail = true;
          }, 60000);
        },
        (error) => {
          if (error.status === 400) {
            this.messageWarning.text = error.error[0].message;
            this.messageWarning.status = true;

            setTimeout(() => {
              this.messageWarning.status = false;
            }, 4000);
          }
        }
      );
  }

  //Edita as informações de um usuário, incluindo seu status.
  editUser() {
    this.usersServiceApi.patchUsers(this.user.id, this.user).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.AlteracaoStatus;
      this.message.status = true;
      this.message.class = 'alert-success';

      setTimeout(() => {
        this.message.status = false;
      }, 4000);
    });
  }

  //Realiza a busca de usuários com base nos filtros definidos.
  searchUser() {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterParams = {};
    this.filterBodyParams = {};

    //Formatacao de dados
    this.filterParams = { ...this.filter };
    this.filterParams.Username = this.filter.Username[0] && this.filter.Username[0].name ? this.filter.Username[0].name : '';
    this.filterParams.SearchIdentifier = this.filterParams.SearchIdentifier == null ? '' : this.filterParams.SearchIdentifier;

    this.filterBody.client_ids = filterHierarchy.clients ? filterHierarchy.clients : [];
    this.filterBodyParams.client_ids = fn.extractIndex(this.filterBody.client_ids, 'id');

    this.filterBody.client_unit_ids = filterHierarchy.units ? filterHierarchy.units : [];
    this.filterBodyParams.client_unit_ids = fn.extractIndex(this.filterBody.client_unit_ids, 'id');

    this.filterBody.structure_ids = filterHierarchy.structures ? filterHierarchy.structures : [];
    this.filterBodyParams.structure_ids = fn.extractIndex(this.filterBody.structure_ids, 'id');

    this.filterBodyParams.client_ids = this.filterBodyParams.client_ids.length === 0 ? this.userRestriction.clients : this.filterBodyParams.client_ids;

    this.filterBodyParams.client_unit_ids =
      this.filterBodyParams.client_unit_ids.length === 0 ? this.userRestriction.client_units : this.filterBodyParams.client_unit_ids;

    this.filterBodyParams.structure_ids =
      this.filterBodyParams.structure_ids.length === 0 ? this.userRestriction.structures : this.filterBodyParams.structure_ids;

    const role = fn.objectToArray(statusLevel, {
      id: 'description',
      value: 'level'
    });

    this.filterParams.Role = this.filter.Role ? role[this.filter.Role].level : '';

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterSearch['User'] = this.filter.Username;

    this.filterSearchBody = {
      ...this.filterBodyParams
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters(), this.filterSearchBody);

    this.getUsersList(this.filterSearch, this.filterSearchBody);
  }

  //Obtém a lista de usuários para exibição em filtros.
  getUser() {
    this.usersServiceApi.getUsersList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      this.users = dados;
      this.users = this.users.map((item: any) => {
        return { id: item.id, name: item.username };
      });
    });
  }

  //Reseta os filtros aplicados e restaura os valores padrões.
  resetFilter() {
    this.hierarchy.resetFilters();

    this.filter = {
      Username: [],
      SearchIdentifier: '',
      Role: '',
      Active: ''
    };

    this.filterParams = {};
    this.filterBodyParams = {};

    this.filterSearch = {};
    this.filterSearchBody = {};

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters(), this.filterSearchBody);
    this.managerFilters();
  }

  /**
   * Gerencia os filtros aplicados na busca de usuários.
   * @param {boolean} [$btn=false] - Indica se a busca deve ser executada ao acionar o botão.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchUser();
    } else {
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchUser();
      } else {
        this.filterSearch = data.filters;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;
        this.filterSearchBody = data.filtersBody;

        //Formulario do filtro
        this.filter.Username = this.filterSearch.User;
        this.filter.FirstName = this.filterSearch.FirstName;
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier;
        this.filter.Active = this.filterSearch.Active;
        this.filter.Role = this.filterSearch.Role;

        this.getUsersList(this.filterSearch, this.filterSearchBody);
      }
      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
        this.hierarchy.setUnits(data.filtersHierarchy.units);
        this.hierarchy.setStructures(data.filtersHierarchy.structures);
      }
    }
  }

  /**
   * Alterna a exibição de colunas na tabela de usuários.
   * @param {any} $event - O evento disparado ao alternar colunas.
   * @param {string} type - O tipo de ação, pode ser 'select', 'deselect', 'selectAll' ou 'deselectAll'.
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }

    this.managerFilters();
  }

  loadTourGuide() {
    this.customTourService.startTour(this.tourService, 'assets/tour-guide/list-users.tourguide.json');
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  //Retorna à página inicial.
  goBack() {
    this.router.navigate(['/']);
  }
}
