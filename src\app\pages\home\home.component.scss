/* Configuração inicial para 2 linhas e 2 colunas */
.container-grid {
  display: grid;
  grid-template-columns: repeat(
    2,
    minmax(0, 1fr)
  ); /* Mantém 2 colunas flexíveis */
  grid-template-rows: auto; /* Altura dinâmica */
  height: auto; /* Removendo o 100vh para evitar overflow */
  gap: 10px; /* Espaçamento entre os itens */
  box-sizing: border-box;
}

/* Estilos dos itens do grid */
.grid-item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.29);
  min-height: 350px; /* Define uma altura mínima para manter alinhado */
  height: 100%; /* Garante que todos cresçam uniformemente */
  box-sizing: border-box;
}

.grid-header {
  height: 30px; /* Altura fixa do header */
  padding: 0 4px;
  border-bottom: rgba(0, 0, 0, 0.29) 1px solid;
  font-size: 0.875em;
  color: #34b575;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0; /* Garante que o header não encolherá */
}

/* Faz o grid-content ocupar o restante do espaço disponível */
.grid-content {
  flex-grow: 1; /* Permite que o grid-content cresça */
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.grid-item:first-child {
  height: 100%; /* Garante que o mapa ocupe toda a altura disponível na célula do grid */
}

.list-section {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.section-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  gap: 15px; /* Espaçamento entre os itens */
}

.carousel-control-prev-custom,
.carousel-control-next-custom {
  font-size: 1.5rem; /* Tamanho das setas */
  color: #34b575; /* Cor das setas */
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s ease-in-out;
}

.carousel-control-prev-custom:hover,
.carousel-control-next-custom:hover {
  color: #28a745; /* Cor mais forte ao passar o mouse */
}

.carousel-control-prev-custom i,
.carousel-control-next-custom i {
  font-size: 1.5rem;
}

.instruments-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* Mantém 4 colunas no desktop */
  gap: 10px;
  padding: 15px;
}

.d-grid {
  display: grid;
  justify-content: start; // <- Alinha à esquerda sempre
  align-content: start;
  width: 100%;
  box-sizing: border-box;
}

.d-grid > div {
  display: flex;
  justify-content: center;
  align-items: center;
}

.d-grid > div > div {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.icon-svg {
  width: 1.25em;
  height: 1.25em;
  vertical-align: middle;
  filter: invert(53%) sepia(33%) saturate(569%) hue-rotate(105deg)
    brightness(95%) contrast(95%);
}

.updates-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 15px;
}

.update-card {
  flex: 1 1 calc(50% - 10px); /* Faz os cards ocuparem metade da largura */
  min-height: 100px;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 2px solid #dee2e6;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.update-card .value {
  font-size: 20px;
  font-weight: bold;
  color: #212529;
  margin-top: 5px;
}

.update-card .date {
  font-size: 12px;
  color: #6c757d;
  margin-top: 8px;
}

.message-no-content {
  font-size: 2em;
  color: orange;
  font-weight: 700;
  text-align: center;
}

.square-card {
  width: 100%;
  height: 100%; // ← ESSENCIAL: ocupa 100% da célula
  min-height: 100px;
}

.content {
  width: 100%;
  height: 100%; // ← Ocupa o card inteiro também
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 8px;
  flex-direction: column;
}

.instrument-count {
  font-size: 1.5rem;
  font-weight: bold;
}

.instrument-name {
  font-size: 1rem;
  margin-top: 5px;
}

/* Quando a tela for menor que 1000px, ajuste para 4 linhas e 1 coluna */
@media (max-width: 1000px) {
  .container-grid {
    grid-template-columns: 1fr; /* Apenas 1 coluna */
    grid-template-rows: repeat(4, auto); /* 4 linhas ajustáveis */
  }
}

.list-instruments,
.legend {
  font-size: 0.75em;
}

.dropdown-menu {
  margin: 0 auto;
  right: 0;
}
