import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { UserService } from 'src/app/services/user.service';

import { ClientService as ClientServiceApi } from 'src/app/services/api/client.service';
import { ClientUnitService as ClientUnitServiceApi } from 'src/app/services/api/clientUnit.service';
import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';

@Component({
  selector: 'app-hierarchy',
  templateUrl: './hierarchy.component.html',
  styleUrls: ['./hierarchy.component.scss']
})
export class HierarchyComponent implements OnInit {
  @Input() public elements: any = {};
  @Input() public validate: boolean = false;
  @Input() public disabled: boolean = false;

  @Output() public sendEventHierarchy = new EventEmitter();
  @Output() public filtersChanged = new EventEmitter();

  @Input() config: any = {
    clients: {
      data: [],
      settings: MultiSelectDefault.Default,
      onSelect: null,
      onSelectAll: null,
      onDeSelect: null,
      onDeSelectAll: null,
      filter: '',
      single: true
    },
    units: {
      data: [],
      settings: MultiSelectDefault.Default,
      onSelect: null,
      onSelectAll: null,
      onDeSelect: null,
      onDeSelectAll: null,
      filter: '',
      single: true
    },
    structures: {
      data: [],
      settings: MultiSelectDefault.Default,
      onSelect: null,
      onSelectAll: null,
      onDeSelect: null,
      onDeSelectAll: null,
      filter: '',
      single: true
    }
  };

  public col = 'col-md-4';
  public currentFilters: any = {};

  public formHierarchy: FormGroup;

  public filterHierarchy: any = {};
  public user: any = {};

  public firstLoad: any = {
    clients: true,
    units: true,
    structures: true
  };

  constructor(
    private clientServiceApi: ClientServiceApi,
    private clientUnitServiceApi: ClientUnitServiceApi,
    private structuresServiceApi: StructuresServiceApi,
    private userService: UserService
  ) {}

  /**
   * Método de inicialização do componente.
   *
   * Executa as ações iniciais ao carregar o componente:
   * - Recupera os filtros salvos localmente no `localStorage` com a chave `'filterHierarchy'`
   *   e atualiza a variável `filterHierarchy` se existir.
   * - Obtém o perfil do usuário atual por meio do `UserService` e o armazena em `this.user`.
   * - Inicializa o formulário reativo `formHierarchy` com base no estado da propriedade `disabled`
   *   chamando o método `buildForm()`.
   *
   * Este método é chamado automaticamente pelo Angular após a criação do componente.
   */
  ngOnInit(): void {
    this.buildForm();

    const savedFilters = localStorage.getItem('filterHierarchy');
    if (savedFilters) {
      this.filterHierarchy = JSON.parse(savedFilters);
    }

    this.user = this.userService.getUserProfile(true);
  }

  /**
   * Inicializa o formulário reativo `formHierarchy` com os campos:
   * clients, units e structures. Define cada controle como habilitado
   * ou desabilitado com base no valor atual de `this.disabled`.
   *
   * Cada controle é inicializado com validação obrigatória (required).
   * Esse método deve ser chamado durante a inicialização do componente (`ngOnInit`).
   *
   * Exemplo de uso:
   *  this.buildForm();
   */
  private buildForm(): void {
    this.formHierarchy = new FormGroup({
      clients: new FormControl({ value: [], disabled: this.disabled }, [Validators.required]),
      units: new FormControl({ value: [], disabled: this.disabled }, [Validators.required]),
      structures: new FormControl({ value: [], disabled: this.disabled }, [Validators.required])
    });
  }

  /**
   * Habilita ou desabilita dinamicamente os controles do formulário
   * `formHierarchy` (clients, units e structures) com base no valor
   * de `this.disabled`.
   *
   * Deve ser chamado quando o valor de `this.disabled` mudar em tempo de execução.
   *
   * Exemplo de uso:
   *  this.disabled = true;
   *  this.toggleDisabledFields();
   */
  public toggleDisabledFields(): void {
    const controls = ['clients', 'units', 'structures'];

    controls.forEach((ctrl) => {
      const control = this.formHierarchy.get(ctrl);
      if (control) {
        this.disabled ? control.disable({ emitEvent: false }) : control.enable({ emitEvent: false });
      }
    });
  }

  /**
   * Método executado ao detectar mudanças nos inputs do componente.
   * - Quando `elements` muda, atualiza os elementos exibidos com `managerElements`.
   * - Quando `disabled` muda, atualiza dinamicamente o estado dos campos do formulário.
   *
   * @param {SimpleChanges} changes - Objeto contendo as mudanças detectadas.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.elements && changes.elements.currentValue) {
      this.managerElements(changes.elements.currentValue);
    }

    if (changes.disabled && !changes.disabled.firstChange) {
      if (this.formHierarchy) {
        this.toggleDisabledFields();
      }
    }
  }

  /**
   * Gerencia os elementos configurados no componente.
   * @param {any} $elements - Os elementos configurados.
   */
  managerElements($elements) {
    let ctrlWidth = 0;

    if ($elements.clients) {
      this.getClients();
      this.config.clients.settings.singleSelection = $elements.clients.single;
      ctrlWidth++;
    }

    if ($elements.units) {
      this.config.units.settings.singleSelection = $elements.units.single;
      if (!$elements.clients) {
        this.getUnits(null);
      }
      ctrlWidth++;
    }

    if ($elements.structures) {
      this.config.structures.settings.singleSelection = $elements.structures.single;
      ctrlWidth++;
    }
    this.col = `col-md-${12 / ctrlWidth}`;
  }

  /**
   * Manipula eventos com base nos itens e ações selecionados.
   * @param {any} item - O item selecionado.
   * @param {string} [action='select'] - A ação realizada (seleção ou desseleção).
   * @param {string} type - O tipo de elemento (clients, units, structures).
   */

  onEvent(item, action: string = 'select', type) {
    if (this.disabled) return;

    if (type == 'clients') {
      this.formHierarchy.get('units').setValue([]);
      this.formHierarchy.get('structures').setValue([]);
      this.getUnits(item, action);
    }

    if (type == 'units') {
      this.formHierarchy.get('structures').setValue([]);
      this.getStructures(item, action);
    }

    this.sendEventHierarchy.emit({ action: action, type: type, element: item });
    this.updateCurrentFilters();
  }

  // Obtém a lista de clientes
  getClients(check = true) {
    let params = {};
    if (this.elements.clients.hasOwnProperty('active') && this.elements.clients.active != null) {
      params['active'] = this.elements.clients.active;
    }
    this.clientServiceApi.getClientsList(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.config.clients.data = dados;

      let clientSelect = null;

      if (this.config.clients.data && this.config.clients.data.length == 1 && check) {
        clientSelect = this.config.clients.data[0];
      } else {
        if (this.formHierarchy.get('clients')?.value.length == 0 && this.filterHierarchy.ClientId && this.firstLoad.clients) {
          clientSelect = { id: this.filterHierarchy.ClientId.id, name: this.filterHierarchy.ClientId.name };
        }
      }
      if (clientSelect != null) {
        this.setClients([clientSelect]);
        this.sendEventHierarchy.emit({ action: 'select', type: 'clients', element: clientSelect });
      }
      this.firstLoad.clients = false;
    });
  }

  /**
   * Obtém a lista de unidades com base no cliente selecionado.
   * @param {any} client - O cliente selecionado.
   * @param {string} [action='select'] - A ação realizada (seleção ou desseleção).
   */
  getUnits(client, action: string = 'select', check = true) {
    this.config.units.data = [];
    this.config.structures.data = [];

    this.formHierarchy.get('units').setValue([]);
    this.formHierarchy.get('structures').setValue([]);

    if (action === 'select') {
      let params = client == null ? {} : { clientId: client.id };

      if (this.elements.units && this.elements.units.hasOwnProperty('active') && this.elements.units.active != null) {
        params['active'] = this.elements.units.active;
      }

      this.clientUnitServiceApi.getClientUnitsId(params).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.config.units.data = dados;

        let unitSelect = null;

        if (this.config.units.data && this.config.units.data.length == 1 && check) {
          unitSelect = this.config.units.data[0];
        } else {
          if (this.formHierarchy.get('units')?.value.length == 0 && this.filterHierarchy.ClientUnitId && this.firstLoad.units) {
            unitSelect = { id: this.filterHierarchy.ClientUnitId.id, name: this.filterHierarchy.ClientUnitId.name };
          }
        }

        if (unitSelect != null) {
          this.setUnits([unitSelect]);
          this.sendEventHierarchy.emit({ action: 'select', type: 'units', element: unitSelect });
        }
        this.firstLoad.units = false;
      });
    }
  }

  /**
   * Obtém a lista de estruturas com base na unidade selecionada.
   * @param {any} clientUnit - A unidade selecionada.
   * @param {string} [action='select'] - A ação realizada (seleção ou desseleção).
   */
  getStructures(clientUnit, action: string = 'select', check = true) {
    this.config.structures.data = [];

    this.formHierarchy.get('structures').setValue([]);

    if (action == 'select') {
      let params = clientUnit == null ? {} : { clientUnitId: clientUnit.id };

      if (this.elements.structures && this.elements.structures.hasOwnProperty('active') && this.elements.structures.active != null) {
        params['active'] = this.elements.structures.active;
      }

      this.structuresServiceApi.getStructureList(params).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.config.structures.data = dados;

        let structureSelect = null;

        if (this.config.structures.data && this.config.structures.data.length == 1 && check) {
          structureSelect = this.config.structures.data[0];
        } else {
          if (this.formHierarchy.get('structures')?.value.length == 0 && this.filterHierarchy.StructureId && this.firstLoad.structures) {
            structureSelect = { id: this.filterHierarchy.StructureId.id, name: this.filterHierarchy.StructureId.name };
          }
        }

        if (structureSelect != null) {
          this.setStructures([structureSelect]);
          this.sendEventHierarchy.emit({ action: 'select', type: 'structures', element: structureSelect });
        }
        this.firstLoad.structures = false;
      });
    }
  }

  /**
   * Define os clientes selecionados.
   * @param {any} client - Os clientes selecionados.
   */
  setClients(client, check = true) {
    if (client && client[0]) {
      this.getUnits(client[0], 'select', check);
    }

    this.formHierarchy.get('clients').setValue(client);
    this.updateCurrentFilters();
  }

  /**
   * Define as unidades selecionadas.
   * @param {any} units - As unidades selecionadas.
   */
  setUnits(units, check = true) {
    if (units && units[0]) {
      this.getStructures(units[0], 'select', check);
    }

    this.formHierarchy.get('units').setValue(units);
    this.updateCurrentFilters();
  }

  /**
   * Define as estruturas selecionadas.
   * @param {any} structures - As estruturas selecionadas.
   */
  setStructures(structures, check = true) {
    this.formHierarchy.get('structures').setValue(structures);
    this.updateCurrentFilters();
  }

  /**
   * Obtém os filtros aplicados atualmente.
   * @returns {any} - Os filtros aplicados.
   */
  getFilters() {
    return this.formHierarchy.value;
  }

  //Reseta os filtros aplicados
  resetFilters() {
    this.formHierarchy.reset();

    this.config.units.data = [];
    this.config.structures.data = [];

    if (this.elements.clients && this.elements.clients.hasOwnProperty('active')) {
      this.elements.clients.active = true;
    }

    if (this.elements.units && this.elements.units.hasOwnProperty('active')) {
      this.elements.units.active = true;
    }

    if (this.elements.structures && this.elements.structures.hasOwnProperty('active')) {
      this.elements.structures.active = true;
    }
  }

  //Atualiza os filtros atuais e emite um evento de mudança de filtros.
  updateCurrentFilters() {
    this.currentFilters = this.getFilters();
    this.filtersChanged.emit(this.currentFilters);
  }

  /**
   * Controla a exibição de entidades (clientes, unidades, estruturas) ativas ou inativas com base no estado do checkbox.
   *
   * - Atualiza o status `active` da entidade correspondente no objeto `elements`.
   * - Dispara a função de carregamento apropriada conforme a entidade:
   *   - `clients`: chama `getClients()`.
   *   - `units`: chama `getUnits()` com base no primeiro cliente selecionado.
   *   - `structures`: chama `getStructures()` com base na primeira unidade selecionada.
   *
   * @param entity Nome da entidade (`clients`, `units`, `structures`)
   * @param event Evento do checkbox (usado para determinar se está marcado ou não)
   */
  controlActive(entity, event) {
    this.elements[entity].active = event.target.checked ? null : true;
    switch (entity) {
      case 'clients':
        this.getClients();
        break;
      case 'units':
        if (this.formHierarchy.get('clients').value && this.formHierarchy.get('clients').value[0]) {
          this.getUnits(this.formHierarchy.get('clients').value[0]);
        }
        break;
      case 'structures':
        if (this.formHierarchy.get('units').value && this.formHierarchy.get('units').value[0]) {
          this.getStructures(this.formHierarchy.get('units').value[0]);
        }
        break;
    }
  }
}
