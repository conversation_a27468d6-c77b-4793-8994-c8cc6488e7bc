<form [formGroup]="formDatasheet">
  <div class="row">
    <!-- Country -->
    <div class="col-md-4">
      <label class="form-label">País</label>
      <select
        class="form-select"
        formControlName="countries"
        (change)="getStatesList()"
      >
        <option value="">Selecione...</option>
        <option *ngFor="let country of countries" [ngValue]="country.id">
          {{ country.name }}
        </option>
      </select>
    </div>
    <!-- State -->
    <div class="col-md-4">
      <label class="form-label">Estado</label>
      <select
        class="form-select"
        formControlName="states"
        (change)="getCitiesList()"
      >
        <option value="">Selecione...</option>
        <option *ngFor="let state of states" [ngValue]="state.id">
          {{ state.name }}
        </option>
      </select>
    </div>
    <!-- City -->
    <div class="col-md-4">
      <label class="form-label">Cidade</label>
      <select class="form-select" formControlName="cities">
        <option value="">Selecione...</option>
        <option *ngFor="let city of cities" [ngValue]="city.id">
          {{ city.name }}
        </option>
      </select>
    </div>
  </div>

  <div class="row mt-2">
    <!-- Purpose -->
    <div class="col-md-12">
      <label class="form-label">Finalidade</label>
      <input
        type="text"
        class="form-control"
        formControlName="purpose"
        autocomplete="off"
        maxlength="255"
      />
    </div>
  </div>

  <div class="row mt-2">
    <!-- Construction stages -->
    <div class="col-md-1">
      <label class="form-label">Etapas</label>
      <input
        type="number"
        min="0"
        step="1"
        class="form-control"
        formControlName="construction_stages"
        (keypress)="func.controlNumber($event, null, 'positive')"
        (keyup)="
          func.controlNumber($event, formDatasheet.get('construction_stages'))
        "
        appDisableScroll
      />
    </div>
    <div class="col-md-2">
      <label class="form-label">Ano de construção</label>
      <input
        type="number"
        min="0"
        class="form-control"
        formControlName="construction_year"
        autocomplete="off"
        (keypress)="func.controlNumber($event, null, 'positive')"
        (keyup)="
          func.controlNumber($event, formDatasheet.get('construction_year'))
        "
        appDisableScroll
      />
    </div>
    <div class="col-md-2">
      <label class="form-label">Início da operação</label>
      <input
        type="number"
        min="0"
        class="form-control"
        formControlName="start_of_operation_date"
        autocomplete="off"
        (keypress)="func.controlNumber($event, null, 'positive')"
        (keyup)="
          func.controlNumber(
            $event,
            formDatasheet.get('start_of_operation_date')
          )
        "
        appDisableScroll
      />
    </div>
    <div class="col-md-2">
      <label class="form-label">Término da operação</label>
      <input
        type="number"
        min="0"
        class="form-control"
        formControlName="end_of_operation_date"
        autocomplete="off"
        (keypress)="func.controlNumber($event, null, 'positive')"
        (keyup)="
          func.controlNumber($event, formDatasheet.get('end_of_operation_date'))
        "
        appDisableScroll
      />
    </div>
    <div class="col-md-5">
      <label class="form-label">Empresa projetista</label>
      <input
        type="text"
        class="form-control"
        formControlName="designing_company"
        autocomplete="off"
        maxlength="500"
      />
    </div>
  </div>

  <div class="row mt-2">
    <div class="col-md-12">
      <label class="form-label">Situação atual da barragem</label>
      <input
        type="text"
        class="form-control"
        formControlName="current_status_of_dam"
        autocomplete="off"
        maxlength="500"
      />
    </div>
  </div>

  <div class="row mt-2">
    <!-- Cota da crista -->
    <div class="col-md-3">
      <label class="form-label">Cota crista (m)</label>
      <input
        type="number"
        class="form-control"
        formControlName="crest_quota"
        min="-9999999999999"
        max="9999999999999"
        (keypress)="func.controlNumber($event, null, 'notE')"
        (keyup)="func.controlNumber($event, formDatasheet.get('crest_quota'))"
        appDisableScroll
      />
    </div>
    <!-- Largura da crista -->
    <div class="col-md-3">
      <label class="form-label">Largura crista (m)</label>
      <input
        type="number"
        min="0"
        class="form-control"
        formControlName="crest_dimension_width"
        (keypress)="
          func.controlNumber(
            $event,
            formDatasheet.get('crest_dimension_width'),
            'positiveDecimal'
          )
        "
        (keyup)="
          func.controlNumber($event, formDatasheet.get('crest_dimension_width'))
        "
        appDisableScroll
      />
    </div>
    <!-- Crest dimension length -->
    <div class="col-md-3">
      <label class="form-label">Comprimento crista (m)</label>
      <input
        type="number"
        min="0"
        class="form-control"
        formControlName="crest_dimension_length"
        (keypress)="
          func.controlNumber(
            $event,
            formDatasheet.get('crest_dimension_length'),
            'positiveDecimal'
          )
        "
        (keyup)="
          func.controlNumber(
            $event,
            formDatasheet.get('crest_dimension_length')
          )
        "
        appDisableScroll
      />
    </div>
    <!-- Total height -->
    <div class="col-md-3">
      <label class="form-label">Altura total (m)</label>
      <input
        type="number"
        min="0"
        class="form-control"
        formControlName="total_height"
        (keypress)="
          func.controlNumber(
            $event,
            formDatasheet.get('total_height'),
            'positiveDecimal'
          )
        "
        (keyup)="func.controlNumber($event, formDatasheet.get('total_height'))"
        appDisableScroll
      />
    </div>
  </div>

  <div class="row mt-2">
    <!-- Upstream slope -->
    <div class="col-md-4">
      <label class="form-label">Inclinação montante</label>
      <input
        type="text"
        class="form-control"
        maxlength="255"
        formControlName="upstream_slope"
      />
    </div>
    <!-- Downstream slope -->
    <div class="col-md-4">
      <label class="form-label">Inclinação jusante</label>
      <input
        type="text"
        class="form-control"
        maxlength="255"
        formControlName="downstream_slope"
      />
    </div>
    <!-- Classification -->
    <div class="col-md-4">
      <label class="form-label">Classificação</label>
      <select class="form-select" formControlName="classification">
        <option value="">Selecione...</option>
        <option *ngFor="let item of classifications" [ngValue]="item.value">
          {{ item.classification }}
        </option>
      </select>
    </div>
  </div>

  <div class="row mt-2">
    <!-- Section type -->
    <div class="col-md-6">
      <label class="form-label">Tipo de seção</label>
      <input
        type="text"
        class="form-control"
        formControlName="section_type"
        maxlength="255"
      />
    </div>
    <!-- Foundation type -->
    <div class="col-md-6">
      <label class="form-label">Tipo de fundação</label>
      <input
        type="text"
        class="form-control"
        maxlength="255"
        formControlName="foundation_type"
      />
    </div>
  </div>

  <div class="row mt-2">
    <!-- Raising method -->
    <div class="col-md-1">
      <label class="form-label">Método de Alteamento</label>
    </div>
    <div class="col-md-3 mt-2">
      <input
        type="text"
        autocomplete="off"
        class="form-control"
        maxlength="255"
        formControlName="raising_method"
      />
    </div>
    <!-- Expected elevations -->
    <div class="col-md-2">
      <label class="form-label">Número de Alteamentos Previstos</label>
    </div>
    <div class="col-md-2 mt-2">
      <input
        type="number"
        min="0"
        step="1"
        class="form-control"
        formControlName="expected_elevations"
        (keypress)="func.controlNumber($event, null, 'positive')"
        (keyup)="
          func.controlNumber($event, formDatasheet.get('expected_elevations'))
        "
        appDisableScroll
      />
    </div>
    <!-- Elevations made -->
    <div class="col-md-2">
      <label class="form-label">Número de Alteamentos Realizados</label>
    </div>
    <div class="col-md-2 mt-2">
      <input
        type="number"
        min="0"
        step="1"
        class="form-control"
        formControlName="elevations_made"
        (keypress)="func.controlNumber($event, null, 'positive')"
        (keyup)="
          func.controlNumber($event, formDatasheet.get('elevations_made'))
        "
        appDisableScroll
      />
    </div>
  </div>

  <div class="row mt-2">
    <!-- Reservatório -->
    <div class="col-sm-6">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <!-- Reservoir design volume -->
            <div class="col-md-6">
              <label class="form-label"
                >Vol. de Projeto do Reservatório (m³)</label
              >
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="reservoir_design_volume"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('reservoir_design_volume'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('reservoir_design_volume')
                  )
                "
                appDisableScroll
              />
            </div>
            <!-- Current reservoir volume -->
            <div class="col-md-6">
              <label class="form-label">Vol. Atual do Reservatório (m³)</label>
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="current_reservoir_volume"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('current_reservoir_volume'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('current_reservoir_volume')
                  )
                "
                appDisableScroll
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Drenagem -->
    <div class="col-sm-6">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <!-- Internal drainage -->
            <div class="col-md-6">
              <label class="form-label">Drenagem Interna</label>
              <input
                type="text"
                autocomplete="off"
                class="form-control"
                maxlength="255"
                formControlName="internal_drainage"
              />
            </div>
            <!-- Superficial drainage -->
            <div class="col-md-6">
              <label class="form-label">Drenagem Superficial</label>
              <input
                type="text"
                autocomplete="off"
                class="form-control"
                maxlength="255"
                formControlName="superficial_drainage"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-2">
    <div class="col-sm-12">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <!-- Basin area in square kilometers -->
            <div class="col-md-2 d-flex align-items-center">
              <label class="form-label">Área da Bacia (km²)</label>
            </div>
            <div class="col-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="basin_area_in_square_kilometers"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('basin_area_in_square_kilometers'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('basin_area_in_square_kilometers')
                  )
                "
                appDisableScroll
              />
            </div>
            <!-- Project precipitation -->
            <div class="col-md-2">
              <label class="form-label">Precipitação do Projeto (mm)</label>
            </div>
            <div class="col-md-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="project_precipitation"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('project_precipitation'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('project_precipitation')
                  )
                "
                appDisableScroll
              />
            </div>
            <!-- Full of project -->
            <div class="col-md-2 d-flex align-items-center">
              <label class="form-label">Cheia de Projeto (anos)</label>
            </div>
            <div class="col-md-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="full_of_project"
                (keypress)="func.controlNumber($event, null, 'positive')"
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('full_of_project')
                  )
                "
                appDisableScroll
              />
            </div>
          </div>
          <div class="row mt-2">
            <!-- Maximum influent flow -->
            <div class="col-md-2 d-flex align-items-center">
              <label class="form-label">Vazão Máx. Afluente (m³/s)</label>
            </div>
            <div class="col-md-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="maximum_influent_flow"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('maximum_influent_flow'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('maximum_influent_flow')
                  )
                "
                appDisableScroll
              />
            </div>
            <!-- Project flow -->
            <div class="col-md-2 d-flex align-items-center">
              <label class="form-label">Vazão de Projeto (m³/s)</label>
            </div>
            <div class="col-md-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="project_flow"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('project_flow'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber($event, formDatasheet.get('project_flow'))
                "
                appDisableScroll
              />
            </div>
            <!-- Normal maximum water level -->
            <div class="col-md-2 d-flex align-items-center">
              <label class="form-label">NA máximo normal (m)</label>
            </div>
            <div class="col-md-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="normal_maximum_water_level"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('normal_maximum_water_level'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('normal_maximum_water_level')
                  )
                "
                appDisableScroll
              />
            </div>
          </div>
          <div class="row mt-2">
            <!-- Maximum water level maximorum -->
            <div class="col-md-2 d-flex align-items-center">
              <label class="form-label">NA máximo Maximorum (m)</label>
            </div>
            <div class="col-md-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="maximum_water_level_maximorum"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('maximum_water_level_maximorum'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('maximum_water_level_maximorum')
                  )
                "
                appDisableScroll
              />
            </div>
            <!-- Freeboard normal maximum water level -->
            <div class="col-md-2">
              <label class="form-label"
                >Borda livre (NA máximo normal) (m)</label
              >
            </div>
            <div class="col-md-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="freeboard_normal_maximum_water_level"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('freeboard_normal_maximum_water_level'),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('freeboard_normal_maximum_water_level')
                  )
                "
                appDisableScroll
              />
            </div>
            <!-- Freeboard maximum water level maximorum -->
            <div class="col-md-2">
              <label class="form-label">Borda livre (NA máximo Max) (m)</label>
            </div>
            <div class="col-md-2 mt-2">
              <input
                type="number"
                min="0"
                class="form-control"
                formControlName="freeboard_maximum_water_level_maximorum"
                (keypress)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get(
                      'freeboard_maximum_water_level_maximorum'
                    ),
                    'positiveDecimal'
                  )
                "
                (keyup)="
                  func.controlNumber(
                    $event,
                    formDatasheet.get('freeboard_maximum_water_level_maximorum')
                  )
                "
                appDisableScroll
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-2">
    <div class="col-md-3">
      <label class="form-label">Cota da soleira do vertedouro (m)</label>
      <input
        type="number"
        min="-9999999999999"
        max="9999999999999"
        class="form-control"
        formControlName="spillway_sill_quota"
        (keypress)="func.controlNumber($event, null, 'notE')"
        (keyup)="
          func.controlNumber($event, formDatasheet.get('spillway_sill_quota'))
        "
        appDisableScroll
      />
    </div>
    <!-- Spillway -->
    <div class="col-md-9">
      <label class="form-label">Vertedouro</label>
      <input
        type="text"
        autocomplete="off"
        class="form-control"
        maxlength="255"
        formControlName="spillway"
      />
    </div>
  </div>

  <div class="row mt-2">
    <div class="col-md-12">
      <label class="form-label">Curso d’água interceptado</label>
      <input
        type="text"
        autocomplete="off"
        class="form-control"
        maxlength="255"
        formControlName="intercepted_watercourse"
      />
    </div>
  </div>

  <!-- Tabela Potencial de dano ambiental -->
  <div class="row mt-2">
    <div class="col-md-12">
      <label class="form-label">Potencial de dano ambiental</label>
      <table class="table table-bordered table-hover align-middle">
        <thead class="table-light">
          <tr>
            <!-- Cabeçalhos das colunas -->
            <th *ngFor="let key of getColumnKeys()" style="width: 25%">
              {{ environmentalDamagePotential[key].name }}
            </th>
          </tr>
        </thead>
        <tbody>
          <!-- Linhas da tabela -->
          <tr *ngFor="let weightIndex of getMaxWeights()">
            <!-- Células com inputs de radio -->
            <td *ngFor="let key of getColumnKeys()">
              <span
                *ngIf="environmentalDamagePotential[key]?.weight[weightIndex]"
                ><div>
                  <label>{{
                    environmentalDamagePotential[key].weight[weightIndex]
                      ?.description
                  }}</label>
                </div>
                <div>
                  <input
                    type="radio"
                    [value]="
                      environmentalDamagePotential[key].weight[weightIndex]
                        ?.value
                    "
                    [formControlName]="key"
                  />
                  <label class="ms-2"
                    >({{
                      environmentalDamagePotential[key].weight[weightIndex]
                        ?.value
                    }})</label
                  >
                </div></span
              >
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</form>
