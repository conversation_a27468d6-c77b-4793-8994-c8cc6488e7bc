export enum EInstrumentTypes {
  WATER_LEVEL_INDICATOR = 'WATER_LEVEL_INDICATOR',
  STANDPIPE_PIEZOMETER = 'STANDPIPE_PIEZOMETER',
  ELECTRIC_PIEZOMETER = 'ELECTRI<PERSON>_PIEZOMETER',
  CONVENTIONAL_INCLINOMETER = 'CONVENTIONAL_INCLINOMETER',
  IN_PLACE_INCLINOMETER = 'IN_PLACE_INCLINOMETER',
  SURFACE_MARK = 'SURFACE_MARK',
  FLOW_METER = 'FLOW_METER',
  WEATHER_STATION = 'WEATHER_STATION',
  LIMNIMETRIC_RULER = 'LIMNIMETRIC_RULER'
}

export enum TituloInstrumento {
  WATER_LEVEL_INDICATOR = "Indicador de Nível d'água",
  STANDPIPE_PIEZOMETER = 'Piezômetro de Tubo Aberto',
  ELECTRIC_PIEZOMETER = 'Piezômetro <PERSON>é<PERSON>o',
  CONVENTIONAL_INCLINOMETER = 'Inclinômetro Convencional',
  IN_PLACE_INCLINOMETER = 'Inclinômetro IPI',
  SURFACE_MARK = 'Marco Superficial (Prisma)',
  FLOW_METER = 'Medidor de Vazão',
  WEATHER_STATION = 'Estação Meteorológica',
  LIMNIMETRIC_RULER = 'Régua Linimétrica'
}
