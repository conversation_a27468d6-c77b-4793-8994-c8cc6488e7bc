const InspectionSheetStatus = [
  { value: 1, label: 'Em preenchimento' },
  { value: 2, label: 'Não concluído' },
  { value: 3, label: '<PERSON>clu<PERSON><PERSON>' }
];

const InspectionSheetType = [
  { value: 1, label: 'RISR' },
  { value: 2, label: 'EoR' },
  { value: 3, label: 'FIE' },
  { value: 4, label: 'FIR' },
  { value: 5, label: 'Em Branco' } //FromScratch
];

// Aba Ocorrências:
const OccurrenceDateFilter = [
  { value: 1, label: 'Por data de inspeção' }, //InspectionSheetDate
  { value: 2, label: 'Por data de prazo' }, //DeadlineDate
  { value: 3, label: 'Por dias restantes de prazo' } //DaysRemainingInDeadline
];

const OccurrenceStatus = [
  { value: 1, label: 'Concluído' },
  { value: 2, label: 'Pendente' }
];

const ActionPlan = [
  { value: 1, label: 'Gera<PERSON>' },
  { value: 0, label: 'A gerar' }
];

// Aba Planos de ação:
const ActionPlanDateFilter = [
  { value: 1, label: 'Por data de criação do plano' }, //CreatedDate
  { value: 2, label: 'Por data de conclusão' } //CompletionDate
];

//Aba Planos de ação
const ActionPlanArea = [
  { value: 1, label: 'Geologia' },
  { value: 2, label: 'Geotecnia' },
  { value: 3, label: 'Hidrologia' },
  { value: 4, label: 'Instrumentação' },
  { value: 5, label: 'Inspeções' },
  { value: 6, label: 'Documentação' }
];

//Aba Planos de ação
const ActionPlanStatus = [
  { value: 1, label: 'Concluída' },
  { value: 2, label: 'Em andamento' },
  { value: 3, label: 'Pendente' },
  { value: 4, label: 'Reprogramada' },
  { value: 5, label: 'Em prazo' },
  { value: 6, label: 'Cancelada' }
];

//Aba Planos de ação
const ActionPlanSeverity = [
  { value: 1, label: '1 - Sem gravidade' }, //NotSevere
  { value: 2, label: '2 - Pouco grave' }, //SlightlySevere
  { value: 3, label: '3 - Grave' }, //Severe
  { value: 4, label: '4 - Muito grave' }, //VerySevere
  { value: 5, label: '5 - Extremamente grave' } //ExtremelySevere
];

//Aba Planos de ação
const ActionPlanUrgency = [
  { value: 1, label: '1 - Não é urgente' }, //NotUrgent
  { value: 2, label: '2 - Pouco urgente' }, //SlightlyUrgent
  { value: 3, label: '3 - Urgente' }, //Urgent
  { value: 4, label: '4 - Muito urgente' }, //VeryUrgent
  { value: 5, label: '5 - Extremamente urgente' } //ExtremelyUrgent
];

//Aba Planos de ação
const ActionPlanTendency = [
  { value: 1, label: '1 - Estável' }, //Stable
  { value: 2, label: '2 - Piora a longo prazo' }, //DeterioratingInLongTerm
  { value: 3, label: '3 - Piora a médio prazo' }, //DeterioratingInMediumTerm
  { value: 4, label: '4 - Piora a curto prazo' }, //DeterioratingInShortTerm
  { value: 5, label: '5 - Piora rapidamente' } //RapidlyDeteriorating
];

const InsertInspectionSheet = [
  { value: 1, label: 'Ficha de inspeção de Segurança Regular - RISR' },
  { value: 2, label: 'Ficha de inspeção - EoR' },
  { value: 3, label: 'Ficha de inspeção - FIE' },
  { value: 4, label: 'Ficha de inspeção - FIR' }
];

const AnomalyScore = [
  { value: 1, points: 0 },
  { value: 2, points: 2 },
  { value: 3, points: 3 },
  { value: 4, points: 4 },
  { value: 5, points: 5 },
  { value: 6, points: 6 },
  { value: 7, points: 10 }
];

//Tabela da aba Situação pretérita
const Anomaly = {
  spillway_structures_reliability: { id: 1, name: 'Confiabilidade das estruturas extravasoras', score: [0, 3, 6, 10] },
  percolation: { id: 2, name: 'Percolação', score: [0, 3, 6, 10] },
  deformations_and_settlements: { id: 3, name: 'Deformações e recalques', score: [0, 2, 6, 10] },
  slope_deterioration: { id: 4, name: 'Deterioração dos taludes/paramentos', score: [0, 2, 6, 10] },
  surface_drainage: { id: 5, name: 'Drenagem superficial', score: [0, 2, 4, 5] }
};

const ActionResultClassification = [
  { value: 1, label: 'Extinto' },
  { value: 2, label: 'Não controlado' },
  { value: 3, label: 'Controlado' }
];

const OccurrenceResponse = [
  { value: 1, label: 'Sim' },
  { value: 2, label: 'Não' },
  { value: 3, label: 'Não se aplica' }
];

const OccurrenceActionPlanDeadlineStatus = [
  { value: 1, label: 'Pendente' },
  { value: 2, label: 'Atrasado' },
  { value: 3, label: 'Concluído' }
];

const OccurrenceActionPlanStatusList = [
  { value: 1, label: 'Sem plano de ação', icon: 'fa fa-circle', color: 'black' }, // HasNoActionPlan
  { value: 2, label: 'Concluído', icon: 'fa fa-circle', color: 'gray' }, // Completed
  { value: 3, label: 'Pendente (> 3 dias)', icon: 'fa fa-circle', color: 'green' }, // PendingMoreThanThreeDays
  { value: 4, label: 'Pendente (≤ 3 dias)', icon: 'fa fa-circle', color: 'yellow' }, // PendingThreeDaysOrLess
  { value: 5, label: 'Atrasado', icon: 'fa fa-circle', color: 'red' }, // Overdue
  { value: 6, label: 'Atrasado recorrente', icon: 'fa fa-circle', color: 'red' } // OverdueAndRecurring
];

const OccurrenceDatePeriod = [
  { value: 1, label: 'Último mês' },
  { value: 2, label: 'Últimos 3 meses' },
  { value: 3, label: 'Últimos 6 meses' },
  { value: 4, label: 'Último ano' },
  { value: 5, label: 'Últimos 2 anos' },
  { value: 6, label: 'Últimos 5 anos' },
  { value: 8, label: 'Personalizado' }
];

const EnvironmentalConservationStatus = {
  spillway_structures_reliability: {
    name: 'Confiabilidade das estruturas extravasoras',
    weight: [
      {
        value: 0,
        description: 'Estruturas civis bem mantidas e em operação normal/barragem sem necessidade de estruturas extravasoras',
        color: '#e2efda'
      },
      {
        value: 3,
        description: 'Estruturas com problemas identificados e medidas corretivas em implantação',
        color: '#fff2cc'
      },
      {
        value: 6,
        description:
          'Estruturas com problemas identificados e sem implantação das medidas corretivas necessárias, sem restrição operacional e extravasor com capacidade plena',
        color: '#f4b084'
      },
      {
        value: 10,
        description: 'Estruturas com problemas identificados, com redução de capacidade vertente e sem medidas corretivas',
        color: '#ff8585'
      }
    ]
  },
  percolation: {
    name: 'Percolação',
    weight: [
      {
        value: 0,
        description: 'Percolação totalmente controlada pelo sistema de drenagem',
        color: '#e2efda'
      },
      {
        value: 3,
        description: 'Umidade ou surgência nas áreas de jusante, parâmetros, taludes e ombreiras estáveis e monitorados',
        color: '#fff2cc'
      },
      {
        value: 6,
        description: 'Umidade ou surgência nas áreas de jusante, parâmetros, taludes ou ombreiras sem implantação das medidas corretivas necessárias',
        color: '#f4b084'
      },
      {
        value: 10,
        description:
          'Surgência nas áreas de jusante com carreamento de material ou com vazão crescente ou infiltração do material contido, com potencial de compromentimento da segurança da estrutura',
        color: '#ff8585'
      }
    ]
  },
  deformations_and_settlements: {
    name: 'Deformações e recalques',
    weight: [
      {
        value: 0,
        description: 'Não existem deformações e recalques com potencial de comprometimento da segurança da estrutura',
        color: '#e2efda'
      },
      {
        value: 2,
        description: 'Existência de trincas e abatimentos com medidas corretivas em implantação',
        color: '#fff2cc'
      },
      {
        value: 6,
        description: 'Existência de trincas e abatimentos sem implantação das medidas corretivas necessárias',
        color: '#f4b084'
      },
      {
        value: 10,
        description: 'Existência de trincas, abatimentos ou escorregamentos, com potencial de comprometimento da segurança da estrutura',
        color: '#ff8585'
      }
    ]
  },
  slope_deterioration: {
    name: 'Deterioração dos taludes/paramentos',
    weight: [
      {
        value: 0,
        description: 'Não existe deterioração de taludes e parâmetros',
        color: '#e2efda'
      },
      {
        value: 2,
        description: 'Falhas na proteção dos taludes e parâmetros, presença de vegetação arbustiva',
        color: '#fff2cc'
      },
      {
        value: 6,
        description: 'Erosões superficiais, ferragem exposta, presença de vegetação arbórea, sem implantação das medidas corretivas necessárias',
        color: '#f4b084'
      },
      {
        value: 10,
        description:
          'Depressões acentuadas nos taludes, escorregamentos, sulcos profundos de erosão, com potencial de comprometimento da segurança da estrutura',
        color: '#ff8585'
      }
    ]
  },
  surface_drainage: {
    name: 'Drenagem superficial',
    weight: [
      {
        value: 0,
        description: 'Drenagem superficial existente e operante',
        color: '#e2efda'
      },
      {
        value: 2,
        description: 'Existência de trincas e/ou assoreamento e/ou abatimentos com medidas corretivas em implantação',
        color: '#fff2cc'
      },
      {
        value: 4,
        description: 'Existência de trincas e/ou assoreamento e/ou abatimentos sem medidas corretivas em implantação',
        color: '#f4b084'
      },
      {
        value: 5,
        description: 'Drenagem superficial inexistente',
        color: '#ff8585'
      }
    ]
  }
};

export {
  InspectionSheetStatus,
  InspectionSheetType,
  OccurrenceDateFilter,
  OccurrenceStatus,
  ActionPlan,
  ActionPlanArea,
  ActionPlanDateFilter,
  ActionPlanStatus,
  ActionPlanSeverity,
  ActionPlanUrgency,
  ActionPlanTendency,
  InsertInspectionSheet,
  Anomaly,
  ActionResultClassification,
  OccurrenceResponse,
  AnomalyScore,
  OccurrenceActionPlanDeadlineStatus,
  OccurrenceActionPlanStatusList,
  OccurrenceDatePeriod,
  EnvironmentalConservationStatus
};
