import { Component, Input, OnInit } from '@angular/core';
import { CustomValidators } from 'src/app/utils/custom-validators';
import { FormControl, FormGroup, Validators } from '@angular/forms';

//Para colocar a URL do arquivo como seguro
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-layers-tab',
  templateUrl: './layers-tab.component.html',
  styleUrls: ['./layers-tab.component.scss']
})
export class LayersTabComponent implements OnInit {
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public profile: any = null;
  @Input() public permissaoUsuario: any = null;

  public formLayers: FormGroup = new FormGroup({
    layer: new FormControl(null, Validators.compose([CustomValidators.validadorExtensaoArquivo(['kml', 'kmz', 'KML', 'KMZ'])]))
  });

  public limitsFiles = true;
  public limitFileSize = true;

  public maxFiles: number = 1;
  public maxFileSize: number = 10000000;

  public fileContent: string = '';
  public fileName: string = '';
  public fileContentDownload: any = '';
  public fileLayer: any = null;

  public filesArray: any = [];

  public layers: any = [];

  constructor(private sanitizer: DomSanitizer) {}

  /**
   * Lifecycle hook executado após a criação do componente.
   * Desativa o campo `layer` do formulário caso o componente esteja em modo de visualização.
   */
  ngOnInit(): void {
    if (this.view) {
      this.formLayers.get('layer')?.disable();
    }
  }

  /**
   * Manipula o upload de arquivos via input ou arraste.
   * Valida quantidade máxima de arquivos e tamanho máximo por arquivo.
   * Aceita apenas extensões `.kml` e `.kmz`. Se válido, chama `uploadFileLoaded`.
   *
   * @param $event - Evento de upload do input ou arraste.
   */
  uploadFile($event: any) {
    let files = $event.dataTransfer ? $event.dataTransfer.files : $event.target.files;
    this.limitsFiles = true;

    if (files.length + this.filesArray.length > this.maxFiles) {
      this.limitsFiles = false;
    } else {
      this.limitFileSize = true;
      Array.from(files).forEach((file: any, index) => {
        if (file.size > this.maxFileSize) {
          this.limitFileSize = false;
        }
      });

      if (this.limitFileSize) {
        Array.from(files).forEach((file: any, index) => {
          let reader = new FileReader();
          let pattern = /\.(kml|kmz|KML|KMZ)$/i;
          if (!pattern.test(file.name)) {
            return;
          }
          reader.onload = ($event: any) => this.uploadFileLoaded($event, index, file);
          reader.readAsDataURL(file);
        });
      }
    }
  }

  /**
   * Manipula o carregamento de um arquivo via `FileReader`.
   * Converte o conteúdo para base64, cria um objeto seguro para visualização
   * e adiciona o arquivo ao array `filesArray`.
   *
   * @param $event - Evento do `FileReader`.
   * @param index - Índice do arquivo dentro do array.
   * @param file - Objeto do arquivo original.
   */
  uploadFileLoaded($event: any, index: number, file: any) {
    let reader = $event.target;
    const fileContent = reader.result;
    const fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl(fileContent);
    const fileName = this.formLayers.get('layer').value.split('\\');
    const fileSize = file.size / (1024 * 1024); // Convertendo bytes para megabytes

    this.filesArray.push({
      id: null,
      path: fileContent,
      fileName: fileName[fileName.length - 1],
      fileContentDownload: fileContentDownload,
      fileSize: fileSize.toFixed(2)
    });
  }

  /**
   * Remove um arquivo da lista `filesArray` com base no índice.
   * Também limpa o valor do campo `layer` do formulário.
   *
   * @param index - Índice do arquivo a ser removido.
   */
  removeFile(index) {
    this.formLayers.get('layer').setValue(null);
    this.filesArray.splice(index, 1);
  }

  /**
   * Recupera os arquivos carregados formatados para envio.
   * Inclui base64, nome do arquivo e tipo fixo `1`.
   *
   * @returns Array de objetos com dados dos arquivos formatados.
   */
  getData() {
    let dataTab = [];

    this.filesArray.forEach((fileItem, index) => {
      let item = {};

      if (fileItem.id != null) {
        item['id'] = fileItem.id;
      }
      item['file'] = { base64: fileItem.path, name: fileItem.fileName };
      item['type'] = 1;

      dataTab.push({
        ...item
      });
    });

    return dataTab;
  }

  /**
   * Popula o array `filesArray` com base nos dados informados,
   * criando a URL segura de visualização a partir do base64.
   *
   * @param dataTab - Array com os dados dos arquivos a serem inseridos.
   */
  setData(dataTab: any = []) {
    dataTab.forEach((fileItem, index) => {
      this.filesArray.push({
        id: fileItem.item,
        path: fileItem.file.base64,
        fileName: fileItem.file.name,
        fileContentDownload: this.sanitizer.bypassSecurityTrustResourceUrl(fileItem.file.base64),
        fileSize: 0
      });
    });
  }
}
