<div
  class="dropdown-minidashboard-table"
  (clickOutside)="onClickedOutside('miniDashboard')"
>
  <app-button
    [class]="'btn-minidashboard'"
    [icon]="'fas fa-list'"
    (click)="dropdownMiniDashboard = !dropdownMiniDashboard"
  ></app-button>

  <ul [ngClass]="dropdownMiniDashboard ? 'active' : ''">
    <!-- Versão dinâmica baseada em [actions] -->
    <ng-container
      *ngIf="actions && actions.length > 0; else legacyMiniDashboard"
    >
      <li *ngFor="let actionItem of actions">
        <a (click)="clickEvent(actionItem.action)">
          <i [ngClass]="actionItem.icon"></i>
          <span>{{ actionItem.label }}</span>
        </a>
      </li>
    </ng-container>

    <!-- Versão estática baseada em miniDashboard -->
    <ng-template #legacyMiniDashboard>
      <ng-template
        ngFor
        let-miniDashboard
        [ngForOf]="miniDashboard"
        let-i="index"
      >
        <li>
          <a (click)="clickEvent(miniDashboard.Click, {})">
            <hr *ngIf="miniDashboard.Separador" />
            <img
              *ngIf="miniDashboard.IconeType === 'svg'"
              src="/assets/ico/ico-menu/{{ miniDashboard.Icone }}"
            />
            <i
              *ngIf="miniDashboard.IconeType !== 'svg'"
              [ngClass]="miniDashboard.Icone"
            ></i>
            <span>{{ miniDashboard.Titulo }}</span>
          </a>
        </li>
      </ng-template>
    </ng-template>
  </ul>
</div>
