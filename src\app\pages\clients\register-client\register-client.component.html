<div class="list-content">
  <!-- Alerta -->
  <div
    class="alert mt-3"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status"
  >
    {{ message.text }}
  </div>

  <form class="row g-3 mt-1" [formGroup]="formClient" (ngSubmit)="validate()">
    <div class="col-md-6" tourAnchor="name">
      <div>
        <label class="form-label">Nome</label>
        <input
          type="text"
          formControlName="name"
          class="form-control"
          maxlength="32"
          autocomplete="off"
          (input)="onValueChange($event)"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formClient.get('name').valid && formClient.get('name').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>

    <div class="col-md-6"></div>

    <!-- Agendamento - Período de Teste -->
    <div class="col-md-6" tourAnchor="trial_period">
      <div class="card">
        <div class="card-header text-center">
          Agendamento - Período de Teste
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12">
              <label class="form-label">Selecione o período:</label>
            </div>
          </div>
          <div class="row">
            <div class="col-6 text-center">
              <label class="form-label">De</label>
              <input
                type="date"
                class="form-control"
                formControlName="trial_period_start"
                (blur)="periodValidate()"
              />
            </div>
            <div class="col-6 text-center">
              <label class="form-label">Até</label>
              <input
                type="date"
                class="form-control"
                formControlName="trial_period_end"
                (blur)="periodValidate()"
              />
            </div>
          </div>
          <br />
          <small
            class="form-text text-danger"
            *ngIf="messageTrialPeriod != null"
            >{{ messageTrialPeriod }}</small
          >
        </div>
      </div>
    </div>
    <!-- Agendamento - Período Contratual -->
    <div class="col-md-6" tourAnchor="contractual_period">
      <div class="card">
        <div class="card-header text-center">
          Agendamento - Período Contratual
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12">
              <label class="form-label">Selecione o período:</label>
            </div>
          </div>
          <div class="row">
            <div class="col-6 text-center">
              <label class="form-label">De</label>
              <input
                type="date"
                class="form-control"
                formControlName="contractual_period_start"
                (blur)="periodValidate()"
              />
            </div>
            <div class="col-6 text-center">
              <label class="form-label">Até</label>
              <input
                type="date"
                class="form-control"
                formControlName="contractual_period_end"
                (blur)="periodValidate()"
              />
            </div>
          </div>
          <br />
          <small
            class="form-text text-danger"
            *ngIf="messageContractualPeriod != null"
            >{{ messageContractualPeriod }}</small
          >
        </div>
      </div>
    </div>
    <!-- Logomarca -->
    <div class="col-md-6" tourAnchor="logo">
      <div class="card card-size">
        <div class="card-header">
          <i class="fa fa-upload me-1"></i>
          Logomarca (Opcional)
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-9">
              <input
                type="file"
                formControlName="logo"
                class="form-control"
                accept=".png,.jpg, .jpeg"
                (change)="uploadFile($event)"
              />
              <em class="form-text">Tamanho máximo permitido: 1 MB. </em>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formClient.get('logo').valid &&
                  formClient.get('logo').touched
                "
                ><i class="bi bi-x-circle me-2"></i>Formato de arquivo
                inválido.</small
              >
              <br />
              <i class="fa fa-exclamation-circle me-1"></i>
              <em class="form-text"
                >Formatos de arquivo válidos: .png, .jpg, .jpeg
              </em>
            </div>
            <div class="col-md-3">
              <app-button
                *ngIf="fileContent"
                [class]="'btn-logisoil-red'"
                [icon]="'fa fa-times'"
                [label]="'Remover Imagem'"
                [type]="true"
                class="me-1"
                (click)="
                  fileContent = ''; formClient.get('logo').setValue(null)
                "
              >
              </app-button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-12 d-flex justify-content-center">
              <img
                [src]="fileContent"
                class="img-fluid img-thumbnail"
                *ngIf="fileContent"
                style="max-height: 200px; width: auto"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Observações -->
    <div class="col-md-6 col-size align-self-stretch" tourAnchor="note">
      <div class="card card-size">
        <div class="card-header form-control-bg">
          <i class="fa fa-align-justify me-2"></i>
          Observações (Opcional)
        </div>
        <div class="card-body">
          <div class="row">
            <div class="input-group"></div>
            <textarea
              pInputTextArea
              rows="11"
              class="form-control"
              formControlName="note"
              data-ls-module="charCounter"
              maxlength="1000"
              (input)="onValueChange($event)"
            ></textarea>
          </div>
          <small class="form-text"
            >Caracteres {{ counter }} (Máximo: 1000)
          </small>
        </div>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="!formClient.get('note').valid && formClient.get('note').touched"
        ><i class="fa fa-exclamation-circle me-2"></i>Limite de caracteres:
        1000.</small
      >
    </div>

    <div class="row mt-2">
      <!-- Mensagens de erro -->
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesError"
      ></app-alert>
    </div>

    <!-- Botões -->
    <div class="col-md-12 d-flex justify-content-end mb-3">
      <app-button
        tourAnchor="save_button"
        [class]="'btn-logisoil-green'"
        [icon]="'fa fa-floppy-o'"
        [label]="'Salvar'"
        [type]="false"
        [disabled]="
          !formClient.valid || !controlDateTrial || !controlDateContractual
        "
        *ngIf="(permissaoUsuario.edit || permissaoUsuario.create) && formCrtl"
        class="me-1"
      >
      </app-button>

      <app-button
        tourAnchor="back_button"
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [routerLink]="['/clients']"
        class="me-1"
      ></app-button>
    </div>
    <!-- Botões -->
  </form>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>

<tour-step-template></tour-step-template>
