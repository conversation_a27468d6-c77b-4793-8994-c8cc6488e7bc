.dropdown-notifications {
  position: relative;
}

.dropdown-notifications > ul {
  background-color: #ffffff;
  position: absolute;
  border-radius: 5px;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.29);
  top: 40px;
  left: -388px;
  padding: 0;
  margin: 0;
  pointer-events: none;
  opacity: 0;
  transform: rotateX(-90deg);
  transform-origin: top center;
  transition: all 0.25s ease;
  transition-timing-function: ease-out;
  box-sizing: border-box;
  width: 500px;
  min-width: 500px;
  z-index: 99;

  display: flex;
  flex-direction: column;
  height: 700px; // Ajuste conforme necessário
}

.dropdown-notifications > ul.active {
  opacity: 1;
  pointer-events: all;
  transform: rotateX(0deg);
}

.dropdown-notifications > ul > li.li-header,
.dropdown-notifications > ul > li.li-buttons,
.dropdown-notifications > ul > li.li-pagination {
  flex: 0 0 auto;
}

.dropdown-notifications > ul > li.li-content {
  flex: 1 1 auto;
  overflow-y: auto;
  padding-top: 10px;

  div {
    display: grid;
    text-decoration: none;
    padding: 0 8px;
    color: #212529;
    grid-template-columns: 32px auto;
    grid-template-rows: auto auto auto;
    grid-template-areas:
      'checkbox title'
      'content content'
      'date date';

    input {
      grid-area: checkbox;
    }

    .title {
      grid-area: title;
      font-size: 0.875em;
      font-weight: 500;
    }

    .content {
      grid-area: content;
      font-size: 0.875em;
      text-align: justify;
    }

    .date {
      grid-area: date;
      font-size: 0.75em;
      color: rgba(33, 37, 41, 0.5);
    }
  }

  hr {
    margin: 8px 0;
    padding: 0;
  }
}

.dropdown-notifications > ul > li.li-header {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  padding-top: 5px;
  background-color: #f4f7f7;
}

.dropdown-notifications > ul > li.li-buttons {
  display: flex;
  justify-content: space-around;
  background-color: #f4f7f7;
  border-bottom: rgba(0, 0, 0, 0.3) 1px solid;
  height: 40px;
}

.dropdown-notifications > ul > li.li-pagination {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f4f7f7;
  padding: 5px 10px;
  gap: 5px;
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -5px;
  font-size: 9px;
  font-weight: 600;
  color: #fff;
  background-color: #dc3545;
  border-radius: 999px;
  padding: 2px 5px;
  line-height: 1;
  min-width: 20px;
  text-align: center;
  z-index: 10;
}

.btn-notification {
  position: relative;
}

::ng-deep app-paginator ngb-pagination {
  flex-wrap: wrap;
  justify-content: center;
  min-width: 200px;

  .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

.jump-to-page {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;

  label {
    margin: 0;
  }

  input {
    width: 60px;
    text-align: center;
    font-size: 0.8rem;
    padding: 2px 4px;
    border: 1px solid #ced4da;
    border-radius: 4px;
  }

  button {
    font-size: 0.75rem;
    padding: 2px 10px;
    border: 1px solid #34b575;
    background-color: #34b575;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: darken(#34b575, 10%);
    }
  }
}
