import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalHistoryomponent } from './modal-history.component';

describe('ModalHistoryomponent', () => {
  let component: ModalHistoryomponent;
  let fixture: ComponentFixture<ModalHistoryomponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ModalHistoryomponent]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalHistoryomponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
