<div class="inspection-sheet-container">
  <!-- Filtro -->
  <div class="row filters">
    <!-- Selects Cliente, Unidade e Estrutura -->
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-12"
    ></app-hierarchy>
    <div class="col-md-2">
      <label class="form-label">ID</label>
      <input
        type="number"
        step="1"
        min="1"
        class="form-control"
        autocomplete="off"
        [(ngModel)]="filter.SearchIdentifier"
        name="SearchIdentifier"
      />
    </div>
    <div class="col-md-2">
      <label class="form-label">Data inicial</label>
      <input
        type="date"
        class="form-control"
        [(ngModel)]="filter.StartDate"
        name="StartDate"
      />
      <!-- Checkbox -->
      <div class="form-check">
        <input type="checkbox" class="form-check-input" id="allDatesCheckbox" />
        <label class="form-check-label" for="allDatesCheckbox"
          >Todas as datas</label
        >
      </div>
    </div>
    <div class="col-md-2">
      <label class="form-label">Data final</label>
      <input
        type="date"
        class="form-control"
        [(ngModel)]="filter.EndDate"
        name="EndDate"
      />
    </div>
    <div class="col-md-3">
      <label class="form-label">Status</label>
      <select class="form-select" [(ngModel)]="selectedStatus">
        <option value="all">Todos</option>
        <option
          *ngFor="let status of inspectionSheetStatus"
          [value]="status.value"
        >
          {{ status.label }}
        </option>
      </select>
    </div>
    <div class="col-md-3">
      <label class="form-label">Origem</label>
      <select class="form-select" [(ngModel)]="selectedOrigin">
        <option value="all">Todas</option>
        <option
          *ngFor="let origin of inspectionSheetType"
          [value]="origin.value"
        >
          {{ origin.label }}
        </option>
      </select>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue me-2'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        (click)="managerFilters(true)"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>
  </div>

  <div
    class="alert"
    [ngClass]="message.class"
    role="alert"
    *ngIf="message.status"
  >
    {{ message.text }}
  </div>

  <!-- Ficha de Inspeção -->
  <div class="add-inspection d-flex align-items-end justify-content-end mb-3">
    <app-button
      *ngIf="permissaoUsuario?.create"
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Inserir Ficha de Inspeção'"
      (click)="getSelectedStructure()"
      [disabled]="!isStructureSelected"
    ></app-button>
  </div>

  <!-- Tabela -->
  <app-table
    *ngIf="tableData.length > 0"
    [tableHeader]="tableHeader"
    [tableData]="tableData"
    [permissaoUsuario]="permissaoUsuario"
    [menuMiniDashboard]="'miniDashboardInspectionSheet'"
    (sendClickRowEvent)="clickRowEvent($event)"
  ></app-table>

  <!-- Paginação -->
  <div class="row mt-3" *ngIf="tableData.length > 0">
    <app-paginator
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event)"
      [enableItemPerPage]="true"
    ></app-paginator>
  </div>
</div>

<app-modal-insert-inspection-sheet
  #modalInsertInspectionSheet
  [structureId]="structureId"
></app-modal-insert-inspection-sheet>

<!-- Confirmar exclusao do agendamento -->
<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
  [data]="modalData"
></app-modal-confirm>

<app-modal-history-inspection-sheet
  #modalHistoryInspectionSheet
  [data]="modalData"
></app-modal-history-inspection-sheet>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
