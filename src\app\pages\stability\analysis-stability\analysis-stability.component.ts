import { AfterViewInit, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup } from '@angular/forms';

import { CalculationMethods, Conditions, FS, SurfaceType, ZipFile } from 'src/app/constants/stability.constants';
import { typeInstruments } from 'src/app/constants/instruments.constants';
import { MessagePadroes } from 'src/app/constants/message.constants';
import { MultiSelectDefault } from 'src/app/constants/app.constants';

import { DataService } from 'src/app/services/data.service';
import { FilterService } from 'src/app/services/filter.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { UserService } from 'src/app/services/user.service';

import { NotificationService as NotificationServiceApi } from 'src/app/services/notification.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { StabilityService as StabilityServiceApi } from 'src/app/services/api/stability.service';

import { DomSanitizer } from '@angular/platform-browser';

import * as moment from 'moment-timezone';
import { format } from 'date-fns';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-analysis-stability',
  templateUrl: './analysis-stability.component.html',
  styleUrls: ['./analysis-stability.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AnalysisStabilityComponent implements OnInit, AfterViewInit {
  @ViewChild('hierarchy') hierarchy: any;
  @ViewChild('modalViewDxf') ModalViewDxf: any;
  @ViewChild('modalReadingHistory') ModalReadingHistory: any;

  public formStabilityAnalysis: FormGroup = new FormGroup({
    SearchIdentifier: new FormControl(''),
    ClientId: new FormControl([]),
    ClientUnitId: new FormControl([]),
    StructureId: new FormControl([]),
    SectionId: new FormControl([]),
    StartDate: new FormControl(''),
    EndDate: new FormControl('')
  });

  public controls: any = [];

  public sectionSettings = MultiSelectDefault.Sections;
  public sections: any = [];

  public calculationMethods = CalculationMethods;
  public typeInstrument = typeInstruments;
  public soilConditionType = Conditions;
  public surfaceType = SurfaceType;
  public fs = FS;
  public zipFile = ZipFile;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public ctrlBtnFilter: boolean = false;
  public ctrlConfiguration: boolean = false;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public viewSettings = MultiSelectDefault.View;

  public bannerNotifications: any = [];
  public showNotificationBanner: boolean = true;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public func = fn;

  public readingHistoryData: any[] = [];
  public readingHistoryTitle: string = '';

  public showColorPicker = {};
  public selectedColor = {};

  public dxfInfo = null;

  public tableData: any = [];
  public tableHeader: any = [
    {
      label: 'ID',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Estrutura',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['structure_name']
    },
    {
      label: 'Seção',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['section_name']
    },
    {
      label: 'Revisão Seção',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['section_review_index']
    },
    {
      label: 'Data/Horário',
      width: '120px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    }
  ];

  public detailHeader: any = [
    {
      label: 'Método de cálculo',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['calculation_method']
    },
    {
      label: 'Fator de Segurança',
      width: '160px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['value']
    },
    {
      label: 'Tipo de superfície',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['surface_type']
    },
    {
      label: 'Condição',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['soil_condition_type']
    },
    {
      label: 'Ações',
      width: '160px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['view_dxf'],
      type: ['button'],
      config: {
        class: 'btn-logisoil-blue',
        icon: 'fa fa-file',
        label: 'Visualizar DXF',
        type: true,
        option: 'view_dxf',
        title: 'Visualizar DXF'
      }
    },
    {
      label: 'Downloads',
      width: '240px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom']
    }
  ];

  //Coluna Ações
  public detailActionCustom: any = [
    {
      class: 'btn-logisoil-blue',
      icon: 'fa fa-file',
      label: ' .dxf',
      title: 'Download DXF',
      type: 'true',
      option: 'download_dxf'
    },
    {
      class: 'btn-logisoil-blue',
      icon: 'fa fa-file-image-o',
      label: ' .png',
      title: 'Download PNG',
      type: 'true',
      option: 'download_png'
    },
    {
      class: 'btn-logisoil-blue',
      icon: 'fa fa-file-archive-o',
      label: ' .zip',
      title: 'Download ZIP',
      type: 'true',
      option: 'download_zip'
    }
  ];

  public detailHistoryHeader: any = [
    {
      label: 'Data',
      width: '180px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Alterações',
      width: '260px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    },
    {
      label: 'Usuário',
      width: '40%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Instrumento',
      width: '30%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['instrument']
    },
    {
      label: 'Tipo',
      width: '30%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['type_instrument']
    }
  ];

  public detailWarningHeader: any = [
    {
      label: 'Data',
      width: '180px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Mensagem',
      width: '100%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['message']
    }
  ];

  public selectedColumns = this.tableHeader;

  constructor(
    private dataService: DataService,
    private filterService: FilterService,
    private ngxSpinnerService: NgxSpinnerService,
    private notificationServiceApi: NotificationServiceApi,
    private sectionsServiceApi: SectionsServiceApi,
    private stabilityServiceApi: StabilityServiceApi,
    private router: Router,
    private userService: UserService,
    private sanitizer: DomSanitizer
  ) {}

  /**
   * Método de inicialização que é chamado após a construção do componente.
   * Inicializa as configurações e dados necessários para o funcionamento do componente.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.sectionSettings.singleSelection = true;
    this.controls = this.formStabilityAnalysis.controls;

    this.notificationServiceApi.notificationsBanner$.subscribe(({ stabilityNotifications }) => {
      this.bannerNotifications = stabilityNotifications;
    });

    this.notificationServiceApi.bannerVisibility$.subscribe(({ stabilityBannerStatus }) => {
      this.showNotificationBanner = stabilityBannerStatus;
    });
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros.
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
    }, 1000);
  }

  /**
   * Obtém a lista de seções com base na estrutura selecionada e realiza ações de seleção/deseleção.
   *
   * @param {any} structure - Estrutura selecionada.
   * @param {string} [action='select'] - Ação a ser realizada (select ou deselect).
   */
  getSections(structure, action: string = 'select') {
    if (action === 'select') {
      this.sectionsServiceApi.getSectionList({ structureId: structure.id }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.sections = dados;
        this.getSectionsColors(this.sections);
      });
    } else {
      this.sections = [];
      this.selectedColor = {};
      this.showColorPicker = {};
    }
  }

  /**
   * Obtém a lista de análises de estabilidade com base nos parâmetros fornecidos.
   *
   * @param {any} params - Parâmetros de busca.
   */
  getStabilityAnalysisList(params) {
    this.ngxSpinnerService.show();
    this.message.status = false;
    this.messagesError = [];

    this.stabilityServiceApi.getStabilityAnalysisSearch(params).subscribe(
      (resp) => {
        let dados: any = resp;

        if (dados.status == 200) {
          this.tableData = dados.body.data ? dados.body.data : [];
          this.collectionSize = dados.body.total_items_count;
          this.formatData();
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.message.text = MessagePadroes.NoRegister;
          this.message.status = true;
          this.message.class = 'alert-warning';

          setTimeout(() => {
            this.message.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Formata os dados da tabela de análises de estabilidade.
   */
  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      item.created_date = moment(item.created_date).tz('America/Sao_Paulo').subtract(3, 'hours').format('DD/MM/YYYY HH:mm:ss');

      const review = item.section_review_index;
      const stage = item.stage;
      item.section_review_index = stage ? `${review} / ${stage}` : `${review}`;

      item.detail = null;
      return item;
    });
  }

  /**
   * Realiza a busca das análises de estabilidade com base nos filtros aplicados.
   */
  searchStabilityAnalysis() {
    let filterHierarchy = this.hierarchy.getFilters();

    const params = {
      SearchIdentifier: this.controls['SearchIdentifier'].value,
      StartDate: this.controls['StartDate'].value,
      EndDate: this.controls['EndDate'].value,
      ClientId: filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '',
      ClientUnitId: filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '',
      StructureId: filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].id : '',
      SectionId: this.controls['SectionId'].value.length > 0 ? this.controls['SectionId'].value[0].id : '',
      Page: this.page,
      PageSize: this.pageSize,
      //Para persistência do filtro
      SectionFilter: this.controls['SectionId'].value
    };

    this.filterService.setFilters(params, this.hierarchy.getFilters());
    this.getStabilityAnalysisList(params);
  }

  /**
   * Obtém a análise de estabilidade pelo ID e atualiza a tabela com os detalhes da análise.
   *
   * @param {number} stabilityAnalysisId - ID da análise de estabilidade.
   * @param {number} index - Índice da análise na tabela.
   */
  getStabilityAnalysisById(stabilityAnalysisId, index) {
    if (this.tableData[index].detail == null) {
      this.stabilityServiceApi.getStabilityAnalysisById(stabilityAnalysisId).subscribe((resp) => {
        let dados: any = resp;
        if (resp['status'] == 200) {
          dados = dados.body === undefined ? dados : dados.body;
          this.managerDetail(dados, index);
        } else {
          //Colocar mensagem "Nenhum registro encontrado"
        }
      });
    } else {
      const detail = this.tableData[index].detail;

      if (Array.isArray(detail)) {
        const allHidden = detail.every((d) => !d.show); // se todos estiverem ocultos
        detail.forEach((d) => (d.show = allHidden)); // ativa todos se estavam ocultos, ou oculta todos se pelo menos um estava visível
      } else if (detail && typeof detail === 'object') {
        detail.show = !detail.show;
      }
    }
  }

  /**
   * Gerencia os detalhes da análise de estabilidade e formata os dados detalhados.
   *
   * @param {any} dados - Dados da análise de estabilidade.
   * @param {number} index - Índice da análise na tabela.
   */
  managerDetail(dados, index) {
    const detailData = this.formatDetail(dados.safety_factor_results || [], index);
    const detailHistoryData = this.formatDetailHistory(dados.reading_history || []);
    const detailWarningData = this.formatDetailWarning(dados.warnings || []);

    this.tableData[index].detail = [
      {
        // TABELA 1: Fatores de Segurança
        detailHeader: this.detailHeader,
        detailData: detailData,
        detailActionCustom: this.detailActionCustom,
        show: true
      },
      {
        // TABELA 2: Histórico de Leitura
        detailHeader: this.detailHistoryHeader,
        detailData: detailHistoryData,
        detailActionCustom: [], // sem ações
        show: true
      },
      {
        // TABELA 3: Warnings
        detailHeader: this.detailWarningHeader,
        detailData: detailWarningData,
        detailActionCustom: [],
        show: true
      }
    ];
  }

  /**
   * Formata os detalhes dos fatores de segurança.
   *
   * @param {any[]} $safetyFactors - Lista de fatores de segurança.
   * @returns {any[]} Lista formatada de fatores de segurança.
   */
  formatDetail($safetyFactors, index) {
    return $safetyFactors.map((safetyFactor) => {
      let item = {};
      item['calculation_method'] = fn.findIndexInArrayofObject(this.calculationMethods, 'value', safetyFactor.calculation_method, 'label', false);
      item['value'] = safetyFactor.value.toFixed(2);
      item['surface_type'] = SurfaceType.find((item) => item.value === safetyFactor.surface_type)?.label || null;
      item['soil_condition_type'] = Conditions.find((item) => item.value === safetyFactor.soil_condition_type)?.label || null;
      item['view_dxf'] = '';
      item['safetyFactor'] = safetyFactor;

      let fileNameBase =
        this.tableData[index].section_name +
        ' ' +
        fn.convertToValidFilename(item['surface_type'] + ' ' + item['soil_condition_type'] + ' ' + item['calculation_method']) +
        ' - ' +
        format(new Date(), 'yyyy.MM.dd');
      item['fileNameBase'] = fileNameBase;

      return item;
    });
  }

  /**
   * Formata o histórico de leituras de instrumentos para exibição.
   *
   * - Converte a data para o fuso horário de São Paulo e formata como `DD/MM/YYYY HH:mm:ss`.
   * - Concatena o nome do usuário que realizou a modificação.
   * - Retorna o identificador e o nome do tipo do instrumento, ou `'-'` se ausente.
   *
   * @param $readingHistory Array de históricos de leituras.
   * @returns Array formatado para exibição.
   */
  formatDetailHistory($readingHistory: any[]): any[] {
    return $readingHistory.map((readingHistory) => ({
      created_date: moment(readingHistory.created_date).tz('America/Sao_Paulo').format('DD/MM/YYYY HH:mm:ss'),
      changes: readingHistory.changes,
      user: `${readingHistory.modified_by.first_name} ${readingHistory.modified_by.surname}`,
      instrument: readingHistory.instrument?.identifier || '-',
      type_instrument: typeInstruments.find((t) => t.id === readingHistory.instrument?.type)?.name || '-'
    }));
  }

  /**
   * Formata os avisos gerados durante o processo de leitura de instrumentos.
   *
   * - Converte a data para o fuso horário de São Paulo e formata como `DD/MM/YYYY HH:mm:ss`.
   * - Retorna a mensagem de cada aviso.
   *
   * @param $warnings Array de avisos de leitura.
   * @returns Array de avisos formatados.
   */
  formatDetailWarning($warnings: any[]): any[] {
    return $warnings.map((w) => ({
      created_date: moment(w.created_date).tz('America/Sao_Paulo').format('DD/MM/YYYY HH:mm:ss'),
      message: w.message
    }));
  }

  /**
   * Gerencia os filtros aplicados e realiza a busca de análises de estabilidade.
   *
   * @param {boolean} [$btn=false] - Indica se a busca deve ser acionada por um botão.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchStabilityAnalysis();
    } else {
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchStabilityAnalysis();
      } else {
        this.controls['SearchIdentifier'].setValue(data.filters.SearchIdentifier);
        this.controls['StartDate'].setValue(data.filters.StartDate);
        this.controls['EndDate'].setValue(data.filters.EndDate);
        this.controls['SectionId'].setValue(data.filters.SectionFilter);

        if (Object.keys(data.filtersHierarchy).length !== 0) {
          this.hierarchy.setClients(data.filtersHierarchy.clients);
          this.hierarchy.setUnits(data.filtersHierarchy.units);
          this.hierarchy.setStructures(data.filtersHierarchy.structures);

          if (data.filtersHierarchy.structures[0] !== undefined) {
            this.getSections(data.filtersHierarchy.structures[0]);
          }
        }
      }
    }
  }

  /**
   * Reseta os filtros aplicados no formulário e na hierarquia.
   */
  resetFilter() {
    this.hierarchy.resetFilters();

    this.controls['SectionId'].setValue([]);
    this.controls['SearchIdentifier'].setValue('');
    this.controls['StartDate'].setValue('');
    this.controls['EndDate'].setValue('');

    this.sections = [];
    this.managerFilters(true);
  }

  /**
   * Gerencia os eventos de hierarquia e atualiza as seções e botões de filtro.
   *
   * @param {any} $event - Evento de hierarquia.
   */
  getEventHierarchy($event) {
    switch ($event.type) {
      case 'units':
        this.sections = [];
        this.selectedColor = {};
        this.showColorPicker = {};
        break;
      case 'structures':
        this.sections = [];
        this.selectedColor = {};
        this.showColorPicker = {};

        if ($event.element != null) {
          this.getSections($event.element, $event.action);
        }
        this.ctrlBtnFilter = $event.action === 'select' ? true : false;
        break;
    }

    if ($event.action === 'deselect') {
      this.ctrlBtnFilter = false;
    }
  }

  /**
   * Obtém as cores das seções de forma aleatória e atualiza a exibição do seletor de cores.
   * @param {any[]} $sections - Lista de seções.
   */
  getSectionsColors($sections) {
    const colors = fn.generateRandomColors($sections.length);

    $sections.forEach((section, index) => {
      this.selectedColor[section.name] = colors[index];
      this.showColorPicker[section.name] = false;
    });
  }

  /**
   * Gerencia eventos de clique fora do elemento, como o seletor de cores.
   * @param {string} element - Nome do elemento.
   * @param {string} [property=''] - Propriedade do elemento.
   */
  onClickedOutside(element: string, property: string = '') {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker[property] = false;
        break;
    }
  }

  /**
   * Atualiza a cor selecionada no seletor de cores.
   * @param {any} $event - Evento de mudança de cor.
   * @param {string} [property=''] - Propriedade do elemento.
   */
  changeComplete($event, property: string = '') {
    this.selectedColor[property] = $event.color.hex;
  }

  /**
   * Gerencia eventos de clique em linhas da tabela, incluindo downloads de arquivos.
   * @param {any} [$event=null] - Evento de clique na linha.
   */
  clickRowEvent($event: any = null) {
    const detailIndex = 0;
    let item = null;
    let fileName = '';

    switch ($event.action) {
      case 'clickedRow':
        let row = this.tableData[$event.index];
        this.getStabilityAnalysisById(row.id, $event.index);
        break;
      case 'download_dxf':
        item = this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].safetyFactor.dxf_file;
        fileName = this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].fileNameBase + '.dxf';
        this.getFile(item, fileName);
        break;
      case 'download_png':
        item = this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].safetyFactor.png_file;
        fileName = this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].fileNameBase + '.png';
        this.getFile(item, fileName);
        break;
      case 'download_zip':
        item = this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].safetyFactor.zip_file_download_url;
        fileName = this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].fileNameBase + '.zip';
        this.getFileZip(item, fileName);
        break;
      case 'view_dxf':
        this.dxfInfo = {
          dxf: this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].safetyFactor.dxf_file,
          structure_name: this.tableData[$event.rowIdx].structure_name,
          section_name: this.tableData[$event.rowIdx].section_name,
          soil_condition_type: this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].soil_condition_type,
          surface_type: this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].surface_type,
          calculation_method: this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].calculation_method,
          value: this.tableData[$event.rowIdx].detail[detailIndex].detailData[$event.index].value
        };
        this.ModalViewDxf.openModal();
        break;
    }
  }

  /**
   * Obtém um arquivo a partir das informações fornecidas e força o download.
   * @param {any} $info - Informações do arquivo.
   */
  getFile($info, fileName) {
    const blob = fn.base64toBlob($info.base64);
    const url = window.URL.createObjectURL(blob);

    fileName != '' ? fileName : $info.name;

    this.forceDownload(url, fileName);
  }

  /**
   * Obtém um arquivo ZIP a partir de uma URL e força o download.
   * @param {string} url - A URL do arquivo ZIP.
   */
  getFileZip(url, fileName) {
    fileName != '' ? fileName : 'filename.zip';

    url = url.replace('api/v1', '');
    this.stabilityServiceApi.getStabilityAnalysisZipFile(url).subscribe((resp: any) => {
      if (resp['status'] == 200) {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        const blob = new Blob([resp['body']], { type: resp['body'].type });
        const url = window.URL.createObjectURL(blob);
        this.forceDownload(url, fileName);
      }
    });
  }

  /**
   * Força o download de um arquivo com um nome especificado.
   * @param {any} file - O arquivo a ser baixado.
   * @param {string} name - O nome do arquivo.
   */
  forceDownload(file, name) {
    const link: any = document.createElement('a');
    link.href = file;
    link.download = name;

    document.body.appendChild(link);

    link.click();

    document.body.removeChild(link);
  }

  /**
   * Alterna a exibição de colunas na tabela de visualização.
   *
   * @param {any} $event - Evento de alternância de colunas.
   * @param {string} type - Tipo de alternância (select, deselect, selectAll, deselectAll).
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Fecha o banner de notificações
   */
  handleCloseNotificationBanner() {
    this.showNotificationBanner = false;
    this.notificationServiceApi.handleCloseReadingBanner();
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.pageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.page = page;
      }
    }

    this.managerFilters();
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  /**
   * Navega para a página inicial.
   */
  goBack() {
    this.router.navigate(['/']);
  }
}
