.progress-container {
  width: 50%;
  max-width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin: 10px auto;
}

.progress-bar {
  height: 100%;
  transition: width 0.1s linear;
}

.reload-button {
  display: block;
  margin: 10px auto;
  padding: 8px 16px;
  font-size: 14px;
  color: white;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    background-color: #0056b3;
  }
}

.progress-info {
  text-align: center;
  margin-top: 10px;
}

.progress-message {
  font-size: 0.875em;
  color: #333;
}

.progress-counter {
  font-size: 0.875em;
  font-weight: 700;
  color: #333;
}
