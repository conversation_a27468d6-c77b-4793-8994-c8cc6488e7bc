import { Component, EventEmitter, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms'; // Adicione essas importações

//Services
import { AuthService } from 'src/app/services/auth.service';
import { DataService } from 'src/app/services/data.service';
import { MenuService } from 'src/app/services/menu.service';
import { UserService } from 'src/app/services/user.service';

//Constants
import { MenuProfile } from 'src/app/constants/rotas.constants';

//API
import { ClientService } from 'src/app/services/api/client.service';
import { ClientUnitService } from 'src/app/services/api/clientUnit.service';
import { StructuresService } from 'src/app/services/api/structure.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HeaderComponent implements OnInit {
  @ViewChild('modalProfile') ModalProfile: any;
  @ViewChild('modalTermo') ModalTermo: any;

  @Output() public sendStructure = new EventEmitter();

  public auth: any = null;
  public menuMiniDashboard: any = null;
  public menuProfile: any = MenuProfile;

  public dropdownMiniDashboard = false;
  public dropdownStructure = false;
  public dropdownProfile = false;

  //public filter: any = { ClientId: '', ClientUnitId: '', StructureId: '' };
  public clients: any = [];
  public units: any = [];
  public structures: any = [];

  public user: any = {
    id: '',
    name: ''
  };

  public logoContent: string = '';
  public logoName: string = '';

  public filterForm: FormGroup = new FormGroup({
    ClientId: new FormControl(''),
    ClientUnitId: new FormControl(''),
    StructureId: new FormControl('')
  });

  public filterHierarchy: any = {};

  public firstLoad: any = {
    clients: true,
    units: true,
    structures: true
  };

  constructor(
    private authService: AuthService,
    private userService: UserService,
    private menuService: MenuService,
    private structuresService: StructuresService,
    private clientService: ClientService,
    private clientUnitService: ClientUnitService,
    private dataService: DataService
  ) {}

  /**
   * Método executado ao inicializar o componente.
   * Configura o serviço de autenticação, carrega o perfil do usuário, os menus e inicializa o formulário.
   */
  ngOnInit(): void {
    this.auth = this.authService;
    this.menuMiniDashboard = this.menuService.setMenu('miniDashboard');
    this.user = this.userService.getUserProfile(true);
    this.menuProfile = this.menuService.setMenu('menuProfile');

    const savedFilters = localStorage.getItem('filterHierarchy');
    if (savedFilters) {
      this.filterHierarchy = JSON.parse(savedFilters);
      if (this.user.id !== this.filterHierarchy.UserId) {
        localStorage.removeItem('filterHierarchy');
      }
    }

    this.getClients();
  }

  /**
   * Carrega o logotipo do cliente com base no ID fornecido e define os dados do logotipo no formato base64.
   * @param {string} clientId - O ID do cliente para buscar o logotipo.
   */
  loadLogo(clientId) {
    this.logoName = '';
    this.logoContent = '';

    if (clientId !== '') {
      this.dataService.getClientLogo(clientId).subscribe(
        (logoData) => {
          if (logoData && logoData.logo && logoData.logo.base64) {
            const mimeType = this.detectMimeType(logoData.logo.base64) || 'image/png';
            this.logoName = logoData.logo.name;
            this.logoContent = `data:${mimeType};base64,${logoData.logo.base64}`;
          } else {
            this.setLogoPadraoWalm();
          }
        },
        (error) => {
          this.setLogoPadraoWalm();
        }
      );
    } else {
      this.setLogoPadraoWalm();
    }
  }

  isValidLogo(logo: string): boolean {
    if (!logo) {
      return false;
    }

    const isValid = logo.startsWith('data:image/');
    if (!isValid) {
    }
    return isValid;
  }

  /**
   * Define a logo padrão da Walm no topo da aplicação.
   * Este método é usado como fallback quando o cliente não possui uma logo configurada.
   */
  private setLogoPadraoWalm(): void {
    this.logoName = 'Logo padrão Walm';
    this.logoContent = 'assets/ico/WL_logo_walm.svg';
  }

  /**
   * Lida com eventos de clique fora de elementos específicos para fechar dropdowns de menu.
   * @param {string} element - O nome do elemento que deve ser fechado quando clicado fora.
   */
  onClickedOutside(element: string) {
    switch (element) {
      case 'miniDashboard':
        this.dropdownMiniDashboard = false;
        break;
      case 'structure':
        this.dropdownStructure = false;
        break;
      case 'profile':
        this.dropdownProfile = false;
        break;
    }
  }

  /**
   * Abre um modal específico com base na ação fornecida.
   * @param {string} action - A ação que determina qual modal será aberto ('meuCadastro' ou 'termoServico').
   */
  openModal(action: string): void {
    switch (action) {
      case 'meuCadastro':
        this.ModalProfile.openModal();
        break;
      case 'termoServico':
        this.ModalTermo.openModal();
        break;
    }
  }

  /**
   * Busca e carrega a lista de clientes, unidades e estruturas, resetando os filtros atuais.
   */
  getClients(check = true) {
    this.clientService.getClientsList({ active: true }).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.clients = dados;

      let clientSelect = null;
      if (this.clients && this.clients.length === 1) {
        clientSelect = this.clients[0].id;
      } else {
        if (this.filterForm.get('ClientId')?.value.length == 0 && this.filterHierarchy.ClientId && this.firstLoad.clients) {
          clientSelect = this.filterHierarchy.ClientId.id;
        }
      }

      if (clientSelect != null) {
        this.setClients(clientSelect);
      }
      this.firstLoad.clients = false;
    });
  }

  /**
   * Define o cliente selecionado no formulário e inicia o carregamento das unidades correspondentes.
   *
   * @param client - ID do cliente selecionado.
   * @param check - Flag opcional para controle de carregamento condicional.
   */
  setClients(client, check = true) {
    if (client) {
      this.getUnits(client, check);
    }
    this.filterForm.get('ClientId').setValue(client);
  }

  /**
   * Carrega a lista de unidades de um cliente com base no ID do cliente e atualiza o logotipo.
   * @param {string} clientId - O ID do cliente para buscar as unidades associadas.
   */
  getUnits(clientId, check = true) {
    this.units = [];
    this.structures = [];
    this.filterForm.get('ClientUnitId')?.setValue('');
    this.filterForm.get('StructureId')?.setValue('');
    this.loadLogo(clientId);

    if (clientId) {
      this.clientUnitService.getClientUnitsId({ clientId: clientId, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.units = dados;

        let unitSelect = null;
        if (this.units && this.units.length === 1) {
          unitSelect = this.units[0].id;
        } else {
          if (this.filterForm.get('ClientUnitId')?.value.length == 0 && this.filterHierarchy.ClientUnitId && this.firstLoad.units) {
            unitSelect = this.filterHierarchy.ClientUnitId.id;
          }
        }

        if (unitSelect != null) {
          this.setUnits(unitSelect);
        }
        this.firstLoad.units = false;
      });
    }
  }

  /**
   * Define a unidade do cliente selecionada no formulário e inicia o carregamento das estruturas correspondentes.
   *
   * @param units - ID da unidade de cliente selecionada.
   * @param check - Flag opcional para controle de carregamento condicional.
   */
  setUnits(units, check = true) {
    if (units) {
      this.getStructures(units, check);
    }

    this.filterForm.get('ClientUnitId').setValue(units);
  }

  /**
   * Carrega a lista de estruturas de uma unidade de cliente com base no ID da unidade.
   * @param {string} clientUnitId - O ID da unidade de cliente para buscar as estruturas associadas.
   */
  getStructures(clientUnitId, check = true) {
    this.structures = [];
    this.filterForm.get('StructureId')?.setValue('');

    if (clientUnitId) {
      this.structuresService.getStructureList({ clientUnitId: clientUnitId, active: true }).subscribe((resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.structures = dados;

        let structureSelect = null;
        if (this.structures && this.structures.length === 1) {
          structureSelect = this.structures[0].id;
        } else {
          if (this.filterForm.get('StructureId')?.value.length == 0 && this.filterHierarchy.StructureId && this.firstLoad.structures) {
            structureSelect = this.filterHierarchy.StructureId.id;
          }
        }

        if (structureSelect != null) {
          this.setStructures(structureSelect);
        }
        this.firstLoad.structures = false;
      });
    }
  }

  /**
   * Define a estrutura selecionada no formulário e salva a hierarquia atual no armazenamento local.
   *
   * @param structure - ID da estrutura selecionada.
   * @param check - Flag opcional para controle de carregamento condicional.
   */
  setStructures(structure, check = true) {
    if (structure) {
      this.filterForm.get('StructureId').setValue(structure);
      this.saveHierarchy();
    }
  }

  /**
   * Salva a hierarquia de filtros selecionados (cliente, unidade e estrutura) no localStorage
   * e emite os dados através do `sendStructure` para os componentes pai.
   */
  saveHierarchy() {
    const client = this.clients.find((c) => c.id === this.filterForm.get('ClientId')?.value);
    const clientUnit = this.units.find((u) => u.id === this.filterForm.get('ClientUnitId')?.value);
    const structure = this.structures.find((s) => s.id === this.filterForm.get('StructureId')?.value);

    const filterHierarchy = {
      ClientId: client ? { id: client.id, name: client.name } : null,
      ClientUnitId: clientUnit ? { id: clientUnit.id, name: clientUnit.name } : null,
      StructureId: structure ? { id: structure.id, name: structure.name } : null,
      UserId: this.user?.id
    };

    localStorage.setItem('filterHierarchy', JSON.stringify(filterHierarchy));
    this.sendStructure.emit(filterHierarchy);
  }

  /**
   * Detecta o tipo MIME de um arquivo em formato base64.
   * @param {string} base64 - O arquivo em formato base64.
   * @returns {string} - O tipo MIME detectado (ex: 'image/png', 'image/jpg').
   */
  detectMimeType(base64: string | string[]) {
    const signatures: any = {
      iVBORw0KGgo: 'image/png',
      '/9j/': 'image/jpg'
    };
    for (let s in signatures) {
      if (base64.indexOf(s) === 0) {
        return signatures[s];
      }
    }
  }
}
