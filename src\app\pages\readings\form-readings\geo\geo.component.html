<form [formGroup]="formReading" class="mb-3">
  <div class="row">
    <div class="col-md-3">
      <label class="form-label">Instrumento</label>
      <select
        class="form-select"
        formControlName="instrument"
        (change)="changeInstrument(formReading.controls['instrument'].value)"
      >
        <option value="" *ngIf="formReading.controls['instrument'].value == ''">
          Selecione...
        </option>
        <option
          *ngFor="let instrumentItem of instrumentsList"
          [ngValue]="instrumentItem.id"
        >
          {{ instrumentItem.identifier }}
        </option>
      </select>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('instrument').valid &&
          formReading.get('instrument').touched &&
          !formReading.get('instrument').disabled
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Data e hora -->
    <div class="col-md-2">
      <label class="form-label">Data e hora</label>
      <input
        type="datetime-local"
        class="form-control"
        formControlName="date"
      />
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('date').valid && formReading.get('date').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Natureza -->
    <div class="col-md-7">
      <label class="form-label">Natureza</label>
      <app-select-input
        [formControlItem]="controls['nature']"
        [selectOptions]="natures"
        [idField]="'id'"
        [textField]="'description'"
        [options]="{ maxlength: 100 }"
        (onAdd)="addNature($event)"
        (onEdit)="editNature($event)"
        [view]="view"
      ></app-select-input>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('nature').valid && formReading.get('nature').touched
        "
        >Campo Obrigatório.</small
      >
      <small class="form-text text-danger" *ngIf="messageNature != ''">{{
        messageNature
      }}</small>
    </div>
  </div>

  <div class="row mt-2">
    <!-- a_axis_pga -->
    <div class="col-md-2">
      <label class="form-label">PGA eixo A</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="a_axis_pga"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="func.controlNumber($event, formReading.get('a_axis_pga'))"
          appDisableScroll
        />
        <span class="input-group-text">%g</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('a_axis_pga').valid &&
          formReading.get('a_axis_pga').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- b_axis_pga -->
    <div class="col-md-2">
      <label class="form-label">PGA eixo B</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="b_axis_pga"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="func.controlNumber($event, formReading.get('b_axis_pga'))"
          appDisableScroll
        />
        <span class="input-group-text">%g</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('b_axis_pga').valid &&
          formReading.get('b_axis_pga').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- z_axis_pga -->
    <div class="col-md-2">
      <label class="form-label">PGA eixo Z</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          formControlName="z_axis_pga"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="func.controlNumber($event, formReading.get('z_axis_pga'))"
          appDisableScroll
        />
        <span class="input-group-text">%g</span>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          !formReading.get('z_axis_pga').valid &&
          formReading.get('z_axis_pga').touched
        "
        >Campo Obrigatório.</small
      >
    </div>

    <!-- Coordenadas Epicentro -->
    <div class="col-md-6">
      <label class="form-label"
        >Coordenadas Epicentro ({{ datum != null ? datum.value : '' }})</label
      >
      <div class="input-group">
        <span class="input-group-text">E (m)</span>
        <input
          type="text"
          class="form-control"
          formControlName="east_coordinate"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber($event, formReading.get('east_coordinate'))
          "
          appDisableScroll
        />
        <span class="input-group-text">N (m)</span>
        <input
          type="text"
          class="form-control"
          formControlName="north_coordinate"
          (blur)="func.formatType($event)"
          (focus)="func.formatType($event)"
          (keypress)="func.controlNumber($event, null, 'notE')"
          (keyup)="
            func.controlNumber($event, formReading.get('north_coordinate'))
          "
          appDisableScroll
        />
        <div class="input-group-append">
          <button
            class="btn btn-logisoil-map"
            type="button"
            ngbTooltip="Mapa"
            (click)="callMap()"
            [disabled]="!controls['east_coordinate'].enabled"
          >
            <i class="fa fa-map-location-dot"></i>
          </button>
        </div>
      </div>
      <small
        class="form-text text-danger"
        *ngIf="
          (!formReading.get('east_coordinate').valid &&
            formReading.get('east_coordinate').touched) ||
          (!formReading.get('north_coordinate').valid &&
            formReading.get('north_coordinate').touched)
        "
        >Campo Obrigatório.</small
      >
    </div>
  </div>
</form>
