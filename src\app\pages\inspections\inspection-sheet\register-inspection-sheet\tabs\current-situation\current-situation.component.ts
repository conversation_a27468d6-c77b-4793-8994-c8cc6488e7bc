import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { ActionResultClassification, Anomaly, AnomalyScore } from 'src/app/constants/inspections.constants';

@Component({
  selector: 'app-current-situation',
  templateUrl: './current-situation.component.html',
  styleUrls: ['./current-situation.component.scss']
})
export class CurrentSituationComponent implements OnInit, OnChanges {
  @Input() public inspectionSheetType: number = null;
  @Input() public status: number = null;
  @Input() public locked: boolean = false;
  @Input() public view: boolean = false;
  @Output() public formChanged = new EventEmitter<any>();

  public currentSituationForm: FormGroup = this.fb.group({
    situations: this.fb.array([]) // FormArray para a situação atual
  });

  public anomaly = Anomaly; // Constante de anomalias
  public actionResultClassification = ActionResultClassification; // Classificação dos resultados
  public anomalyScoreOptions = AnomalyScore; // Pontuações de anomalias

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {}

  /**
   * Detecta e processa mudanças nos inputs vinculados ao componente.
   * @param {SimpleChanges} changes - Contém as mudanças detectadas nos inputs do componente.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.status || changes.locked) {
      this.toggleForm();
    }
  }

  /**
   * Obtém o FormArray das situações no formulário.
   * @returns {FormArray} - Lista de situações cadastradas.
   */
  get situations(): FormArray {
    return this.currentSituationForm.get('situations') as FormArray;
  }

  /**
   * Configura os dados recebidos do componente pai no formulário de situação atual.
   * @param {any} dados - Dados das anomalias identificadas.
   */
  setData(dados: any): void {
    const anomaliesData = dados?.identified_anomalies || [];
    this.situations.clear(); // Limpa o FormArray antes de popular

    anomaliesData.forEach((anomaly) => {
      const anomalyKey = Object.keys(this.anomaly).find((key) => this.anomaly[key].id === anomaly.anomaly);

      this.situations.push(
        this.fb.group({
          Id: [anomaly.id || null], // ID da anomalia
          Identifier: [this.anomaly[anomalyKey]?.name || 'Anomalia desconhecida'], // Nome da anomalia
          ActionResultClassification: [anomaly.action_result_classification],
          AnomalyScore: [anomaly.anomaly_score || 0], // Pontuação
          Note: [anomaly.note || ''], // Observação
          Scores: [this.anomaly[anomalyKey]?.score || []] // Pontuações possíveis
        })
      );
    });
    this.toggleForm();
  }

  /**
   * Obtém o rótulo correspondente à classificação baseada na pontuação da anomalia.
   * @param {number} score - Pontuação da anomalia.
   * @returns {string} - Rótulo da classificação correspondente.
   */
  getSituationLabel(score: number): string {
    return this.actionResultClassification.find((item) => item.value === score)?.label || '-';
  }

  /**
   * Dispara o evento de salvamento ao remover o foco de um campo do formulário.
   * @param {string} controlName - Nome do campo modificado.
   * @param {number} [index] - Índice do item no FormArray, se aplicável.
   */
  onBlur(controlName: string, index?: number): void {
    if (this.currentSituationForm.disabled) return;
    const control = this.situations.at(index).get(controlName);
    if (control?.dirty) {
      this.triggerSave();
    }
  }

  /**
   * Dispara o evento de salvamento ao detectar mudanças em um campo do formulário.
   * @param {string} controlName - Nome do campo modificado.
   * @param {number} [index] - Índice do item no FormArray, se aplicável.
   */
  onChange(controlName: string, index?: number): void {
    if (this.currentSituationForm.disabled) return;
    const control = this.situations.at(index).get(controlName);
    if (control?.dirty) {
      this.triggerSave();
    }
  }

  /**
   * Emite o evento de salvamento quando alterações são detectadas no formulário.
   */
  triggerSave(): void {
    this.formChanged.emit();
  }

  /**
   * Alterna entre habilitar ou desabilitar o formulário de situação atual.
   */
  toggleForm() {
    const isLocked = this.locked || [2, 3].includes(this.status);
    if (isLocked) {
      this.currentSituationForm.disable();
    } else {
      this.currentSituationForm.enable();
    }
  }

  /**
   * Obtém as opções de pontuação disponíveis para uma determinada anomalia.
   * @param {number} index - Índice da anomalia dentro do FormArray.
   * @returns {any[]} - Lista de pontuações possíveis para a anomalia.
   */
  getAnomalyScoreOptions(index: number): any[] {
    const situation = this.situations.at(index);
    return situation ? situation.get('Scores')?.value || [] : [];
  }
}
