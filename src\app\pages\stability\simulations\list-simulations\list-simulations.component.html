<div class="list-content">
  <!-- Cadastrar nova simulação -->
  <div class="button-simulation">
    <app-button
      [class]="'btn-logisoil-green'"
      [customBtn]="true"
      [icon]="'fas fa-plus-circle'"
      [label]="'Criar simulação'"
      [routerLink]="['createSimulation']"
      *ngIf="permissaoUsuario.createSimulation"
    ></app-button>
  </div>

  <form [formGroup]="formSimulations">
    <div class="row mt-2">
      <!-- Filtro Cliente, Unidade, Estrutura -->
      <app-hierarchy
        #hierarchy
        [elements]="elements"
        class="col-md-9"
        (sendEventHierarchy)="getEventHierarchy($event)"
      ></app-hierarchy>
      <!-- Seção -->
      <div class="col-md-3">
        <label class="form-label">Seção</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="sectionSettings"
          [data]="sections"
          formControlName="SectionId"
        >
        </ng-multiselect-dropdown>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-md-2">
        <label class="form-label">ID</label>
        <input
          formControlName="SearchIdentifier"
          type="number"
          step="1"
          min="1"
          class="form-control"
          autocomplete="off"
          placeholder="ID"
        />
      </div>
      <div class="col-md-3">
        <label class="form-label">Nome da simulação</label>
        <input
          formControlName="Name"
          type="text"
          class="form-control"
          autocomplete="off"
          placeholder="Nome"
        />
      </div>
      <!-- Botões -->
      <div class="col-md-4 d-flex align-items-end">
        <app-button
          [class]="'btn-logisoil-blue'"
          [icon]="'fa fa-search'"
          [label]="'Buscar'"
          class="me-1"
          (click)="managerFilters(true)"
        ></app-button>
        <app-button
          [class]="'btn-logisoil-gray'"
          [icon]="'fa fa-eraser'"
          [label]="'Limpar'"
          (click)="resetFilter()"
        ></app-button>
      </div>
    </div>

    <!-- Alerta -->
    <div
      class="col-md-12 mt-2 alert"
      [ngClass]="message.class"
      role="alert"
      *ngIf="message.status"
    >
      {{ message.text }}
    </div>
    <!-- Mensagens de erro -->
    <div class="row mt-2">
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesError"
      ></app-alert>
    </div>

    <!-- Tabela -->
    <div class="row mt-3" *ngIf="tableData.length > 0">
      <label class="form-label d-flex justify-content-end"
        >*Simulações temporárias não podem ser compartilhadas.</label
      >
      <app-table
        [messageReturn]="messageReturn"
        [tableHeader]="tableHeader"
        [tableData]="tableData"
        (sendClickRowEvent)="clickRowEvent($event)"
        [permissaoUsuario]="permissaoUsuario"
        [menuMiniDashboard]="'miniDashboardSimulations'"
      >
      </app-table>
    </div>

    <!-- Paginação -->
    <div class="row mt-3" *ngIf="tableData.length > 0">
      <app-paginator
        [collectionSize]="collectionSize"
        [page]="page"
        [maxSize]="10"
        [boundaryLinks]="true"
        [pageSize]="pageSize"
        (sendPageChange)="loadPage($event)"
        [enableItemPerPage]="true"
      ></app-paginator>
    </div>

    <!-- Voltar -->
    <div class="col-md-12 d-flex justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [routerLink]="['/stability']"
      ></app-button>
    </div>
  </form>
</div>

<!-- Confirmar exclusão da simulação -->
<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [modalConfig]="modalConfig"
  [data]="modalData"
></app-modal-confirm>

<!-- Modal compartilhar simulação -->
<app-modal-share-simulation
  #modalShareSimulation
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitle"
  [simulationItem]="simulationItem"
></app-modal-share-simulation>

<!-- Modal fixar simulação -->
<app-modal-fixed-simulation
  #modalFixedSimulation
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitle"
  [simulationItem]="simulationItem"
></app-modal-fixed-simulation>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
