import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { LogisoilDirectivesModule } from 'src/app/shared/logisoil-directives.module';
import { UnitsRoutingModule } from './units-routing.module';
import { SharedModule } from 'src/app/components/shared.module';

import { ListUnitsComponent } from './list-units/list-units.component';
import { RegisterUnitComponent } from './register-unit/register-unit.component';

@NgModule({
  declarations: [ListUnitsComponent, RegisterUnitComponent],
  imports: [
    CommonModule,
    NgbModule,
    FormsModule,
    LogisoilDirectivesModule,
    ReactiveFormsModule,
    SharedModule,
    NgMultiSelectDropDownModule.forRoot(),
    UnitsRoutingModule
  ]
})
export class UnitsModule {}
