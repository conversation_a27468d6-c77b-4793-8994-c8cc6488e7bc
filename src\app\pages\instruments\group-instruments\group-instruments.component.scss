.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.button-group-instrument {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-title {
  color: #032561;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-control {
  border-color: #d4d2d2;
  font-size: 0.875em;
}

.form-select {
  font-size: 0.875em !important;
  line-height: 1.52857143 !important;
}

.list-instruments,
.list-group {
  font-size: 0.875em;
}

.list-group {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  li {
    white-space: nowrap;
  }
  li + li {
    margin-left: 8px;
  }
}

.nav,
.nav-tabs,
.nav-link active {
  text-align: center;
  font-size: 0.875em;
}

.info-tooltip .tooltip-inner {
  text-align: center;
  font-size: 0.875em;
}

.group-iw {
  display: flex;
  div + div {
    margin-left: 5px;
  }
  justify-content: center;
}

.maps.show {
  animation: showMap 1s ease 0s 1 normal forwards;
}

.pulse-pin-green {
  background: transparent;
  border-radius: 50%;
  // margin: 10px;
  // height: 20px;
  // width: 20px;

  box-shadow: 0 0 0 0 rgba(0, 86, 43, 1);
  transform: scale(1);
  animation: pulse 2s infinite;
}

#div-iw-root {
  hr {
    margin: 8px 0 8px 0;
  }
  .iw-title {
    color: #032561;
    font-weight: bold;
  }
}

.legend {
  font-size: 0.875em;
}

@keyframes showMap {
  0% {
    opacity: 0;
    transform: rotateX(-100deg);
    transform-origin: top;
    height: 0px;
  }
  100% {
    opacity: 1;
    transform: rotateX(0deg);
    transform-origin: top;
    height: 580px;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 86, 43, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 20px rgba(0, 86, 43, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 86, 43, 0);
  }
}
