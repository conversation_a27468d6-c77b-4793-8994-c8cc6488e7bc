<ng-template #modalInstrument let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">Cadastro de Instrumento</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <div class="modal-body">
    <label>Selecione o instrumento que deseja cadastrar:</label>
    <select
      class="form-control form-select"
      (change)="selectTypeInstrument($event)"
    >
      <option value="">Selecione...</option>
      <option
        *ngFor="let item of typeInstrumentsList"
        value="{{ item | json }}"
      >
        {{ item.name }}
      </option>
    </select>
    <!-- Células de pressão -->
    <div class="form-check form-switch mt-2" *ngIf="pressureCells">
      <label class="form-check-label"
        >Os instrumentos terão múltiplas
        <strong>células de pressão</strong>?</label
      >
      <br />
      <input
        class="form-check-input me-2"
        type="checkbox"
        role="switch"
        (change)="pressureCellsSelected = !pressureCellsSelected"
      />
      <span>{{ pressureCellsSelected ? 'Sim' : 'Não' }}</span>
    </div>
    <!-- Pontos de medição -->
    <div class="form-check form-switch mt-2" *ngIf="measuringPoints">
      <label class="form-check-label"
        >Os instrumentos terão múltiplos
        <strong>pontos de medição</strong>?</label
      >
      <br />
      <input
        class="form-check-input me-2"
        type="checkbox"
        role="switch"
        (change)="measuringPointsSelected = !measuringPointsSelected"
      />
      <span>{{ measuringPointsSelected ? 'Sim' : 'Não' }}</span>
    </div>
    <!-- Anéis magnéticos -->
    <div class="form-check form-switch mt-2" *ngIf="magneticRings">
      <label class="form-check-label"
        >Os instrumentos terão múltiplos
        <strong>anéis magnéticos</strong>?</label
      >
      <br />
      <input
        class="form-check-input me-2"
        type="checkbox"
        role="switch"
        (change)="magneticRingsSelected = !magneticRingsSelected"
      />
      <span>{{ magneticRingsSelected ? 'Sim' : 'Não' }}</span>
    </div>
  </div>

  <!-- Botões -->
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-green'"
      [label]="'Iniciar Cadastro'"
      [type]="false"
      (click)="create()"
      [disabled]="selectedTypeInstrument == ''"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-red'"
      [label]="'Cancelar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>
