<ng-template #modalFixedSimulation let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title" id="modal-fixed-simulation">
      <i class="fa fa-pencil-square-o"></i>
      {{ title }}
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <form [formGroup]="formFixedSimulation">
    <div class="modal-body">
      <div class="row">
        <div class="col-md-12">
          <label class="form-label">Nome da simulação:</label>
          <input
            type="text"
            formControlName="Name"
            class="form-control"
            maxlength="80"
          />
          <small
            class="form-text text-danger"
            *ngIf="
              !formFixedSimulation.get('Name').valid &&
              formFixedSimulation.get('Name').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
      </div>
    </div>
  </form>
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-red'"
      [label]="'Fechar'"
      (click)="c('Close click')"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-green'"
      [label]="
        this.simulationItem.option === 'fixedSimulation'
          ? 'Fixar'
          : this.simulationItem.option === 'renameSimulation'
          ? 'Renomear'
          : 'Salvar'
      "
      [type]="false"
      (click)="getForm(); c('Close click')"
      *ngIf="controls['Name'].value"
    >
    </app-button>
  </div>
</ng-template>
