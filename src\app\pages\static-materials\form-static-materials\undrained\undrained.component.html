<div class="col-md-12">
  <form [formGroup]="formUndrained">
    <div class="row">
      <label class="form-label" style="font-style: italic">Fórmula:</label>
      <div class="col-md-3">
        <img
          src="assets/images/static-materials/undrained.png"
          class="img-fluid img-thumbnail"
          style="max-height: 80px; width: auto"
        />
      </div>
    </div>
    <div class="row mt-1">
      <!-- Cohesion type -->
      <div class="col-md-4">
        <label class="form-label">Tipo de Coesão:</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="dropdownSettings"
          [data]="typeCohesion"
          formControlName="cohesion_type"
          (onSelect)="itemEvent($event, 'select')"
          (onDeSelect)="itemEvent($event, 'deselect')"
          [disabled]="formUndrained.controls['cohesion_type'].disabled"
        ></ng-multiselect-dropdown>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('cohesion_type').valid &&
            formUndrained.get('cohesion_type').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>

    <!-- Undrained - Constant -->
    <div class="row mt-1" *ngIf="selectedTypeCohesion == 1">
      <!-- Cohesion -->
      <div class="col-md-3">
        <label class="form-label">Coesão</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="cohesion"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion'),
                'positiveDecimalDot'
              )
            "
            (keyup)="func.controlNumber($event, formUndrained.get('cohesion'))"
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('cohesion').valid &&
            formUndrained.get('cohesion').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Tensile strength -->
      <div class="col-md-3">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_tensile_strength"
          (change)="
            formService.checkboxControlValidate(
              formUndrained,
              'tensile_strength'
            )
          "
        />
        <label class="form-label">Resistência a tração</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="tensile_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('tensile_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formUndrained.get('tensile_strength'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('tensile_strength').valid &&
            formUndrained.get('tensile_strength').touched &&
            formUndrained.get('is_tensile_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>

    <!-- Undrained - F(Depth from Top of Layer) -->
    <div class="row mt-1" *ngIf="selectedTypeCohesion == 2">
      <!-- F(Depth from Top of Layer) Cohesion top -->
      <div class="col-md-3">
        <label class="form-label">Coesão Topo</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="cohesion_top"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_top'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formUndrained.get('cohesion_top'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('cohesion_top').valid &&
            formUndrained.get('cohesion_top').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- F(Depth from Top of Layer) Cohesion variation -->
      <div class="col-md-3">
        <label class="form-label">Variação da Coesão</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="cohesion_variation"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_variation'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_variation')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa/m</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('cohesion_variation').valid &&
            formUndrained.get('cohesion_variation').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!--F(Depth from Top of Layer) Tensile strength  -->
      <div class="col-md-3">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_f_tensile_strength"
          (change)="
            formService.checkboxControlValidate(
              formUndrained,
              'f_tensile_strength'
            )
          "
        />
        <label class="form-label">Resistência a tração</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="f_tensile_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('f_tensile_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formUndrained.get('f_tensile_strength')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('f_tensile_strength').valid &&
            formUndrained.get('f_tensile_strength').touched &&
            formUndrained.get('is_f_tensile_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- F(Depth from Top of Layer) maximum -->
      <div class="col-md-3">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_maximum"
          (change)="
            formService.checkboxControlValidate(formUndrained, 'maximum')
          "
        />
        <label class="form-label">Máximo</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="maximum"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('maximum'),
                'positiveDecimalDot'
              )
            "
            (keyup)="func.controlNumber($event, formUndrained.get('maximum'))"
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          />
          <span class="input-group-text" id="basic-addon2">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('maximum').valid &&
            formUndrained.get('maximum').touched &&
            formUndrained.get('is_maximum').value
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <!-- Undrained - F(Depth from Horizontal Datum) -->
    <div class="row mt-1" *ngIf="selectedTypeCohesion == 3">
      <!-- Cohesion Datum -->
      <div class="col-md-3">
        <label class="form-label">Coesão Datum</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="cohesion_datum"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_datum'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formUndrained.get('cohesion_datum'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('cohesion_datum').valid &&
            formUndrained.get('cohesion_datum').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Cohesion variation datum -->
      <div class="col-md-3">
        <label class="form-label">Variação da Coesão</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="cohesion_variation_datum"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_variation_datum'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_variation_datum')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa/m</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('cohesion_variation_datum').valid &&
            formUndrained.get('cohesion_variation_datum').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Tensile strength -->
      <div class="col-md-3">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_datum_tensile_strength"
          (change)="
            formService.checkboxControlValidate(
              formUndrained,
              'datum_tensile_strength'
            )
          "
        />
        <label class="form-label">Resistência a tração</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="datum_tensile_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('datum_tensile_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formUndrained.get('datum_tensile_strength')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>

        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('datum_tensile_strength').valid &&
            formUndrained.get('datum_tensile_strength').touched &&
            formUndrained.get('is_datum_tensile_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Maximum -->
      <div class="col-md-3">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_datum_maximum"
          (change)="
            formService.checkboxControlValidate(formUndrained, 'datum_maximum')
          "
        />
        <label class="form-label">Máximo</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="datum_maximum"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('datum_maximum'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formUndrained.get('datum_maximum'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('datum_maximum').valid &&
            formUndrained.get('datum_maximum').touched &&
            formUndrained.get('is_datum_maximum').value
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Datum -->
      <div class="col-md-3">
        <label class="form-label">Datum</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="datum"
            step="0.01"
            (keypress)="
              func.controlNumber($event, formUndrained.get('datum'), 'notE')
            "
            (keyup)="func.controlNumber($event, formUndrained.get('datum'))"
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">m</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('datum').valid &&
            formUndrained.get('datum').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
    <!-- Undrained - F(Distance to Slope) -->
    <div class="row mt-1" *ngIf="selectedTypeCohesion == 4">
      <!-- Cohesion -->
      <div class="col-md-3">
        <label class="form-label">Coesão Topo</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="cohesion_top_slope"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_top_slope'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_top_slope')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('cohesion_top_slope').valid &&
            formUndrained.get('cohesion_top_slope').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Cohesion variation -->
      <div class="col-md-3">
        <label class="form-label">Variação da Coesão</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="cohesion_variation_slope"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_variation_slope'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formUndrained.get('cohesion_variation_slope')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa/m</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('cohesion_variation_slope').valid &&
            formUndrained.get('cohesion_variation_slope').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Tensile strength -->
      <div class="col-md-3">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_slope_tensile_strength"
          (change)="
            formService.checkboxControlValidate(
              formUndrained,
              'slope_tensile_strength'
            )
          "
        />
        <label class="form-label">Resistência a tração</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="slope_tensile_strength"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('slope_tensile_strength'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber(
                $event,
                formUndrained.get('slope_tensile_strength')
              )
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('slope_tensile_strength').valid &&
            formUndrained.get('slope_tensile_strength').touched &&
            formUndrained.get('is_slope_tensile_strength').value
          "
          >Campo Obrigatório.</small
        >
      </div>
      <!-- Maximum -->
      <div class="col-md-3">
        <input
          class="form-check-input me-1"
          type="checkbox"
          value=""
          checked
          formControlName="is_maximum_slope"
          (change)="
            formService.checkboxControlValidate(formUndrained, 'maximum_slope')
          "
        />
        <label class="form-label">Máximo</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            formControlName="maximum_slope"
            min="0"
            step="0.01"
            (keypress)="
              func.controlNumber(
                $event,
                formUndrained.get('maximum_slope'),
                'positiveDecimalDot'
              )
            "
            (keyup)="
              func.controlNumber($event, formUndrained.get('maximum_slope'))
            "
            (blur)="func.formatType($event)"
            (focus)="func.formatType($event)"
            appDisableScroll
          /><span class="input-group-text">kPa</span>
        </div>
        <small
          class="form-text text-danger"
          *ngIf="
            !formUndrained.get('maximum_slope').valid &&
            formUndrained.get('maximum_slope').touched &&
            formUndrained.get('is_maximum_slope').value
          "
          >Campo Obrigatório.</small
        >
      </div>
    </div>
  </form>
</div>
