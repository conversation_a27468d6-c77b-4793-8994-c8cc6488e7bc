import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { ReadingService as ReadingServiceApi } from 'src/app/services/api/reading.service';

import { FormService } from 'src/app/services/form.service';

import { fieldsReading } from 'src/app/constants/readings.constants';

import fn from 'src/app/utils/function.utils';
import Decimal from 'decimal.js';

@Component({
  selector: 'app-ina-pz',
  templateUrl: './ina-pz.component.html',
  styleUrls: ['./ina-pz.component.scss']
})
export class InaPzComponent implements OnInit, OnChanges {
  @Input() public instrumentsList: any = [];
  @Input() public index: number = null;
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public units: any = null;
  @Input() public typeInstrument: any = null;
  @Input() public datetime: any = null;

  @Output() public setInstrument = new EventEmitter();

  public formReading: FormGroup = new FormGroup({
    instrument: new FormControl('', [Validators.required]),
    date: new FormControl({ value: '', disabled: true }, [Validators.required]),
    quota: new FormControl({ value: '', disabled: true }, [Validators.required]),
    depth: new FormControl({ value: '', disabled: true }, [Validators.required]),
    pressure: new FormControl({ value: '', disabled: true }),
    dry: new FormControl({ value: false, disabled: true }),
    measure: new FormControl({ value: '', disabled: true }),
    //Para calcular
    top_quota: new FormControl(''),
    base_quota: new FormControl(''),
    //Para edicao
    id: new FormControl({ value: '', disabled: true })
  });

  public controls: any = null;
  public fieldsReading = fieldsReading;
  public func = fn;

  constructor(private formService: FormService, private readingService: ReadingServiceApi) {}

  ngOnInit(): void {
    this.controls = this.formReading.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReading.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }

    if (changes.units && changes.units.previousValue != undefined && !(changes.units.previousValue === changes.units.currentValue)) {
      this.recalculate(changes.units.previousValue, changes.units.currentValue);
    }

    if (changes.datetime && changes.datetime.currentValue != null) {
      this.controls['date'].setValue(this.datetime);
    }
  }

  changeInstrument(instrument) {
    this.setInstrument.emit(instrument);
  }

  splitData($dados) {
    if (!this.edit && !this.view && !this.spreadsheet) {
      this.controls['instrument'].enable();
    } else {
      this.controls['instrument'].disable();
    }

    this.formService.toggleFormList(this.formReading, this.fieldsReading[this.typeInstrument.id]);

    this.controls['instrument'].setValue($dados.instrument.id);
    this.controls['top_quota'].setValue($dados.top_quota);
    this.controls['base_quota'].setValue($dados.base_quota);

    if ($dados.edit) {
      this.controls['id'].setValue($dados.edit.id);

      let date = $dados.edit.date.split('.');
      this.controls['date'].setValue(date[0]);

      this.controls['quota'].setValue($dados.edit.quota);
      this.controls['depth'].setValue($dados.edit.depth);
      this.controls['pressure'].setValue($dados.edit.pressure);
      this.controls['dry'].setValue($dados.edit.dry);
      this.controls['measure'].setValue($dados.edit.measure);
      this.toogleDry();
    }

    if (this.view) {
      this.formReading.disable();
    }

    //Após importar a planilha, não é mais necessário fazer o recálculo
    // if (this.spreadsheet) {
    //   setTimeout(() => {
    //     this.calcAfterLoadSpreadsheet();
    //   }, 200);
    // }
  }

  toogleDry() {
    if (this.controls['dry'].value) {
      this.controls['quota'].setValue('');
      this.controls['depth'].setValue('');
      this.controls['pressure'].setValue('');

      this.controls['quota'].disable('');
      this.controls['depth'].disable('');
      this.controls['pressure'].disable('');

      this.formReading.markAsUntouched();
    } else {
      this.controls['quota'].enable('');
      this.controls['depth'].enable('');
      this.controls['pressure'].enable('');
    }
  }

  //Calculos
  calcQuota(option: string = '') {
    //Cota
    if (option == 'quota' && !fn.isEmpty(this.controls['quota'].value)) {
      let quota = this.controls['quota'].value;
      let quotaDecimal: any = fn.convertLengthDecimal(quota, this.units[0], 'm');

      let top_quota = this.controls['top_quota'].value;
      let top_quotaDecimal: any = new Decimal(top_quota);

      let depthDecimal: any = top_quotaDecimal.sub(quotaDecimal);

      depthDecimal = fn.convertLengthDecimal(depthDecimal, 'm', this.units[0]);
      this.controls['depth'].setValue(depthDecimal);
      this.calcQuota('depth');
    }

    //Profundidade
    if (option == 'depth' && !fn.isEmpty(this.controls['depth'].value)) {
      let depth = this.controls['depth'].value;
      let depthDecimal: any = fn.convertLengthDecimal(depth, this.units[0], 'm');

      let top_quota = this.controls['top_quota'].value;
      let top_quotaDecimal: any = new Decimal(top_quota);

      let quotaDecimal: any = top_quotaDecimal.sub(depthDecimal);
      quotaDecimal = fn.convertLengthDecimal(quotaDecimal, 'm', this.units[0]);
      this.controls['quota'].setValue(quotaDecimal);

      let quota = this.controls['quota'].value;
      quotaDecimal = fn.convertLengthDecimal(quota, this.units[0], 'm');

      let base_quota = this.controls['base_quota'].value;
      let base_quotaDecimal: any = new Decimal(base_quota);

      let pressureDecimal: any = quotaDecimal.sub(base_quotaDecimal);
      pressureDecimal = pressureDecimal.mul(9.81);

      pressureDecimal = fn.convertToPressureDecimal(pressureDecimal, 'kPa', this.units[1]);
      this.controls['pressure'].setValue(pressureDecimal);
    }

    //Pressao
    if (option == 'pressure' && !fn.isEmpty(this.controls['pressure'].value)) {
      let base_quota = this.controls['base_quota'].value;
      let base_quotaDecimal: any = new Decimal(base_quota);

      let pressure = this.controls['pressure'].value;
      let pressureDecimal: any = fn.convertToPressureDecimal(pressure, this.units[1], 'kPa');
      pressureDecimal = pressureDecimal.div(9.81);

      let quotaDecimal: any = base_quotaDecimal.add(pressureDecimal);
      quotaDecimal = fn.convertLengthDecimal(quotaDecimal, 'm', this.units[0]);
      this.controls['quota'].setValue(quotaDecimal);

      //Calculo da profundidade com base na pressao
      let top_quota = this.controls['top_quota'].value;
      let top_quotaDecimal: any = new Decimal(top_quota);

      let quota = this.controls['quota'].value;
      quotaDecimal = fn.convertLengthDecimal(quota, this.units[0], 'm');

      let depthDecimal: any = top_quotaDecimal.sub(quotaDecimal);

      depthDecimal = fn.convertLengthDecimal(depthDecimal, 'm', this.units[0]);
      this.controls['depth'].setValue(depthDecimal);
    }
  }

  //Recalculo
  recalculate(previousUnit, currentUnit) {
    if (!fn.isEmpty(this.controls['quota'].value) && previousUnit[0] != currentUnit[0]) {
      let quota = this.controls['quota'].value;
      quota = fn.convertLengthDecimal(quota, previousUnit[0], currentUnit[0]);
      this.controls['quota'].setValue(quota);
    }

    if (!fn.isEmpty(this.controls['depth'].value) && previousUnit[0] != currentUnit[0]) {
      let depth = this.controls['depth'].value;
      depth = fn.convertLengthDecimal(depth, previousUnit[0], currentUnit[0]);
      this.controls['depth'].setValue(depth);
    }

    if (!fn.isEmpty(this.controls['pressure'].value) && previousUnit[1] != currentUnit[1]) {
      let pressure = this.controls['pressure'].value;
      pressure = fn.convertToPressureDecimal(pressure, previousUnit[1], currentUnit[1]);
      this.controls['pressure'].setValue(pressure);
    }
  }

  calcAfterLoadSpreadsheet() {
    this.calcQuota('quota');
    this.calcQuota('depth');
    this.calcQuota('pressure');
  }
}
