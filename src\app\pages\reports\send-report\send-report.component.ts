import { AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { MessagePadroes } from 'src/app/constants/message.constants';
import { TypeReports as SubjectType } from 'src/app/constants/reports.constants';

import { ReportsService as ReportsServiceApi } from 'src/app/services/api/reports.service';
import { UserService } from 'src/app/services/user.service';

import { NgxSpinnerService } from 'ngx-spinner';
import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-send-report',
  templateUrl: './send-report.component.html',
  styleUrls: ['./send-report.component.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class SendReportComponent implements OnInit, AfterViewInit {
  @ViewChild('hierarchy') hierarchy: any;

  public formSendReport: FormGroup = new FormGroup({
    SubjectType: new FormControl(0),
    InstantReportParameters: new FormControl(0),
    DaysToAnalyse: new FormControl(1),
    Title: new FormControl('', [Validators.required]),
    ResponsibleName: new FormControl('', [Validators.required]),
    StartDate: new FormControl('', [Validators.required]),
    EndDate: new FormControl('', [Validators.required]),
    DestinationEmails: new FormControl('', [Validators.required]),
    PeriodicityType: new FormControl(0)
  });

  public subjectType: any = SubjectType;

  public isHierarchyValid: boolean = false;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public controls: any = null;

  public func = fn;

  constructor(
    private cdr: ChangeDetectorRef,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private reportServiceApi: ReportsServiceApi,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.controls = this.formSendReport.controls;
  }

  ngAfterViewInit() {
    this.hierarchy.formHierarchy.statusChanges.subscribe((status) => {
      this.isHierarchyValid = status === 'VALID' && this.hierarchy.validate;
    });

    // Força o alinhamento e estilo dos elementos
    const elements = document.querySelectorAll('.card-header, .form-control, .form-select');
    elements.forEach((element) => {
      const el = element as HTMLElement;
      el.style.textAlign = 'left';
      el.style.fontSize = '0.875em';
    });

    // Garante que as mudanças no DOM sejam refletidas
    this.cdr.detectChanges();
  }

  //Emitir Relatório
  sendReport() {
    this.ngxSpinnerService.show();
    this.messagesError = [];

    let filterHierarchy = this.hierarchy.getFilters();

    const params = {
      structure_id: filterHierarchy.structures[0].id,
      title: this.controls['Title'].value,
      responsible_name: this.controls['ResponsibleName'].value,
      destination_emails: this.controls['DestinationEmails'].value.split(','),
      days_to_analyze: 1,
      subject_type: this.controls['SubjectType'].value,
      periodicity_type: this.controls['PeriodicityType'].value,

      instant_report_parameters: {
        start_date: this.controls['StartDate'].value,
        end_date: this.controls['EndDate'].value
      }
    };

    this.reportServiceApi.postReports(params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessagePadroes.SendReport;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/reports']);
        }, 4000);

        this.ngxSpinnerService.hide();
      },
      (error) => {
        this.messagesError = [];
        error.error.forEach((msgError) => {
          this.messagesError.push(msgError);
        });
        setTimeout(() => {
          this.messagesError = [];
        }, 4000);

        this.ngxSpinnerService.hide();
      }
    );
  }

  validate() {
    if (this.formSendReport.valid && this.isHierarchyValid) {
      this.sendReport();
    }
  }
}
