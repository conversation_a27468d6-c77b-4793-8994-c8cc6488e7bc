.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.form-control {
  border-color: #d4d2d2;
  font-size: 0.875em;
}

.form-select {
  font-size: 0.875em !important;
  line-height: 1.52857143 !important;
}

.img-thumbnail {
  border: none !important;
  background: none !important;
}

.card-header {
  background: rgba(3, 37, 97, 0.1);
  font-size: 0.875em;
  text-align: center;
  color: #032561;
  border: 1px solid #ffffff;
}

.card-body {
  font-size: 0.875em;
  color: #032561;
  text-align: center;
}

.badge {
  background-color: #032561 !important;
  color: #ffffff;
  font-size: 0.875em;
  text-align: center;
}

.nav-link :hover {
  text-decoration: none;
  background-color: #34b575;
}

.nav,
.nav-tabs,
.nav-link active {
  font-size: 1em;
}

.table thead {
  background: rgba(3, 37, 97, 0.1);
  color: #032561;
}
