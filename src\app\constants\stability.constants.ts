const CalculationMethods = [
  { label: 'Bishop simplified', value: 1 },
  { label: 'Corps of engineers 1', value: 2 },
  { label: 'Corps of engineers 2', value: 3 },
  { label: 'GLE/Morgenstern-Price', value: 4 },
  { label: 'Janbu simplified', value: 5 },
  { label: 'Janbu corrected', value: 6 },
  { label: 'Lowe karafiath', value: 7 },
  { label: 'Ordinary or fellenius', value: 8 },
  { label: 'Spencer', value: 9 },
  { label: 'Sarma', value: 10 },
  { label: 'Menor FS', value: 11 }
];

const Conditions = [
  { value: 1, label: 'Drenada', sliFileType: [1, 4] },
  { value: 2, label: 'Não drenada', sliFileType: [2, 5] },
  { value: 3, label: 'Pseudo estática', sliFileType: [3, 6] }
];

const SurfaceType = [
  { value: 1, label: 'Circular' },
  { value: 2, label: 'Não circular' }
];

const FS = [
  { value: 1, label: 'Gráfico FS ND' },
  { value: 2, label: 'Gráfico FS D' },
  { value: 3, label: 'Gráfico FS SIS' }
];

const SliFileType = [
  { value: 1, label: 'CircularDrained', name: 'Drenada circular', surfaceTypeId: 1, conditionId: 1, surfaceType: 'Circular', condition: 'drained' },
  { value: 2, label: 'CircularUndrained', name: 'Não drenada circular', surfaceTypeId: 1, conditionId: 2, surfaceType: 'Circular', condition: 'undrained' },
  {
    value: 3,
    label: 'CircularPseudoStatic',
    name: 'Pseudo estático circular',
    surfaceTypeId: 1,
    conditionId: 3,
    surfaceType: 'Circular',
    condition: 'pseudoStatic'
  },
  { value: 4, label: 'NonCircularDrained', name: 'Drenada não circular', surfaceTypeId: 2, conditionId: 1, surfaceType: 'NonCircular', condition: 'drained' },
  {
    value: 5,
    label: 'NonCircularUndrained',
    name: 'Não drenada não circular',
    surfaceTypeId: 2,
    conditionId: 2,
    surfaceType: 'NonCircular',
    condition: 'undrained'
  },
  {
    value: 6,
    label: 'NonCircularPseudoStatic',
    name: 'Pseudo estático não circular',
    surfaceTypeId: 2,
    conditionId: 3,
    surfaceType: 'NonCircular',
    condition: 'pseudoStatic'
  }
];

const ZipFile = [
  { value: 1, label: '.png' },
  { value: 2, label: '.sli' },
  { value: 3, label: '.zip(.sltm+.sli)' }
];

const FileType = [
  { value: 1, label: 'DXF' },
  { value: 2, label: 'PNG' },
  { value: 3, label: 'SLI' },
  { value: 4, label: 'SLTM' }
];

const StructureStatus = [
  { value: 1, label: 'Operating', name: 'Operando' },
  { value: 2, label: 'HeighteningWork', name: 'Em obra de alteamento' },
  { value: 3, label: 'ReinforcementWork', name: 'Em obra de reforço' },
  { value: 4, label: 'Disabled', name: 'Desativada' }
];

//Gráfico Estabilidade
const SafetyFactorAlertLevel = [
  { value: 1, label: 'Atenção', color: '#ffc107' },
  { value: 2, label: 'Alerta', color: '#fd7e14' },
  { value: 3, label: 'Emergência', color: '#dc3545' }
];

export { CalculationMethods, Conditions, FileType, FS, SafetyFactorAlertLevel, SliFileType, StructureStatus, SurfaceType, ZipFile };
