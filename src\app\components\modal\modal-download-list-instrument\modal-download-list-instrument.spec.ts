import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalDownloadListInstrumentComponent } from './modal-download-list-instrument.component';

describe('ModalImportComponent', () => {
  let component: ModalDownloadListInstrumentComponent;
  let fixture: ComponentFixture<ModalDownloadListInstrumentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ModalDownloadListInstrumentComponent]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalDownloadListInstrumentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
