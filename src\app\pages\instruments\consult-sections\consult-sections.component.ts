import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { MessageInputInvalid, MessagePadroes } from 'src/app/constants/message.constants';

import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { UserService } from 'src/app/services/user.service';

import fn from 'src/app/utils/function.utils';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-consult-sections',
  templateUrl: './consult-sections.component.html',
  styleUrls: ['./consult-sections.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ConsultSectionsComponent implements OnInit {
  public tableHeader: any = [
    {
      label: 'Seção Vinculada ao Instrumento',
      width: '100%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'Ações',
      width: '100px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom']
    }
  ];

  //Coluna Acoes
  public actionCustom: any = [
    {
      class: 'btn-logisoil-dxf',
      icon: 'fa fa-eye',
      label: '',
      title: 'Exibir DXF',
      type: 'true',
      option: 'dxf'
    },
    {
      class: 'btn-logisoil-goToSection',
      icon: 'fa fa-share-square',
      label: '',
      title: 'Ir para a seção',
      type: 'true',
      option: 'section'
    }
  ];

  public tableData: any = [];

  public message: any = { text: '', status: false };
  public messageReturn: any = { text: '', status: false };
  public messagesError: any = null;

  public profile: any = null;
  public permissaoUsuario: any = null;

  public func = fn;

  public fileDxf: any = null;

  public instrumentIdentifier: string = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private sectionsServiceApi: SectionsServiceApi,
    private userService: UserService
  ) {}

  /**
   * Método de inicialização do componente.
   * Carrega o perfil do usuário, permissões e, se houver um instrumento especificado na rota, carrega as seções e detalhes do instrumento.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;

    if (this.activatedRoute.snapshot.params.instrumentId) {
      this.getSectionsFromInstument(this.activatedRoute.snapshot.params.instrumentId);
      this.getInstrumentsById(this.activatedRoute.snapshot.params.instrumentId);
    }
  }

  /**
   * Obtém as seções relacionadas a um instrumento específico.
   * Exibe uma mensagem de erro caso não haja registros.
   * @param {string} instrumentId - O ID do instrumento para buscar as seções relacionadas.
   */
  getSectionsFromInstument(instrumentId: string) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.instrumentsServiceApi.getInstrumentsSections(instrumentId).subscribe(
      (resp) => {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.tableData = dados;

        if (this.tableData == null) {
          this.tableData = [];
          this.messageReturn.text = MessagePadroes.NoRegister;
          this.messageReturn.status = true;
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * Obtém o arquivo DXF mais recente da revisão de uma seção específica associada a um instrumento.
   * @param {string} sectionId - O ID da seção para buscar o arquivo DXF.
   * @param {string} instrumentId - O ID do instrumento associado à seção.
   */
  getSectionsReviewsLatestFile(sectionId: string, instrumentId: string) {
    this.ngxSpinnerService.show();

    this.fileDxf = null;

    this.sectionsServiceApi;
    this.sectionsServiceApi.getSectionsReviewsLatestFile(sectionId, { InstrumentId: instrumentId }).subscribe(
      (resp) => {
        if (resp['status'] == 200) {
          this.fileDxf = new Blob([resp['body']], { type: resp['body'].type });
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
        const blb = new Blob([error.error], { type: 'application/json' });
        const reader = new FileReader();

        //Acionado apos o blob ter sido lido/carregado
        reader.addEventListener('loadend', (e) => {
          let erroMsg: any = e.target.result;
          erroMsg = JSON.parse(erroMsg);
          this.messagesError = [];
          erroMsg.forEach((msgError) => {
            console.log(msgError);
            this.messagesError.push(msgError);
          });

          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        });
        //Comece a ler o blob como texto
        reader.readAsText(blb);

        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Obtém os detalhes de um instrumento específico pelo seu ID.
   * Define o identificador do instrumento.
   * @param {string} instrumentId - O ID do instrumento para buscar os detalhes.
   */
  getInstrumentsById(instrumentId: string) {
    this.instrumentsServiceApi.getInstrumentsById(instrumentId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.instrumentIdentifier = dados.identifier;
    });
  }

  /**
   * Gerencia eventos de clique em linhas da tabela.
   * Executa ações com base no tipo de ação disparada, como exibir o arquivo DXF ou navegar para a visualização de uma seção.
   * @param {any} $event - O evento de clique que contém a ação e os dados relevantes.
   */
  clickRowEvent($event: any = null) {
    switch ($event.action) {
      case 'dxf':
        this.getSectionsReviewsLatestFile($event.id, this.activatedRoute.snapshot.params.instrumentId);
        break;
      case 'section':
        this.router.navigate(['/sections/' + $event.id + '/view']);
        break;
      default:
        break;
    }
  }

  //Navega de volta para a tela de instrumentos.
  goBack() {
    this.router.navigate(['/instruments']);
  }
}
