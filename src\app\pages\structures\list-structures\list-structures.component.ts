import { AfterViewInit, Component, OnInit, ViewEncapsulation, ViewChild } from '@angular/core';
import { Router } from '@angular/router';

import { MultiSelectDefault, Status } from 'src/app/constants/app.constants';
import { MessageCadastro, MessagePadroes } from 'src/app/constants/message.constants';

import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';
import { UserService } from 'src/app/services/user.service';
import { FilterService } from 'src/app/services/filter.service';

import fn from 'src/app/utils/function.utils';
import { NgxSpinnerService } from 'ngx-spinner';

import { SharedService } from 'src/app/services/shared.service';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-list-structures',
  templateUrl: './list-structures.component.html',
  styleUrls: ['./list-structures.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListStructuresComponent implements OnInit, AfterViewInit {
  @ViewChild('hierarchy') hierarchy: any;

  public tableHeader: any = [
    {
      label: 'ID',
      width: '50px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Unidade',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['client_unit_name']
    },
    {
      label: 'Estrutura',
      width: '50%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['name']
    },
    {
      label: 'Status',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['active']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['miniDashboard']
    }
  ];

  public tableData: any = [];
  public selectedColumns = this.tableHeader;

  public viewSettings = MultiSelectDefault.View;

  public status: any = Status;

  public structure: any = {
    id: null,
    name: null,
    active: null
  };

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messageReturn: any = { text: '', status: false };

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public profile: any = null;
  public permissaoUsuario: any = null;
  public permissoes: any = null;

  public filterParams: any = {};

  public filter: any = {
    SearchIdentifier: '',
    Name: '',
    Active: ''
  };

  public func = fn;

  public elements = {
    clients: {
      single: true,
      onSelect: 'units',
      onDeSelect: 'units',
      active: true
    },
    units: {
      single: true,
      onSelect: 'structures',
      onDeSelect: 'structures',
      active: true
    },
    structures: {
      single: true,
      active: true
    }
  };

  public filterSearch: any = {};

  can(action: string): boolean {
    return !!this.permissaoUsuario?.[action];
  }

  constructor(
    private filterService: FilterService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private structuresServiceApi: StructuresServiceApi,
    private userService: UserService,
    private sharedService: SharedService
  ) {}

  /**
   * Método de inicialização do componente.
   * Carrega o perfil do usuário, permissões e configura o serviço de compartilhamento.
   */
  ngOnInit(): void {
    this.ngxSpinnerService.show();

    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.permissoes = this.userService.permissoes;

    this.managerPermission();

    this.sharedService.subject = new Subject<any>();
  }

  /**
   * Método chamado após a inicialização da visualização do componente.
   * Inicia o gerenciamento dos filtros.
   */
  ngAfterViewInit(): void {
    setTimeout(() => {
      // Verificar se o filtro de Cliente está preenchido no componente 'hierarchy'
      if (this.hierarchy && this.hierarchy.elements && this.hierarchy.elements.length > 0) {
        this.managerFilters(true); // Dispara a busca automaticamente
      } else {
        this.managerFilters(); // Caso contrário, apenas gerencia os filtros normalmente
      }
    }, 1000);
  }

  /**
   * Método para obter a lista de estruturas a partir dos parâmetros de filtro.
   * @param {any} params - Parâmetros para a busca das estruturas.
   */
  getStructuresList(params) {
    this.ngxSpinnerService.show();

    this.messageReturn.text = '';
    this.messageReturn.status = false;

    this.structuresServiceApi.getStructures(params).subscribe(
      (resp) => {
        const dados: any = resp;
        if (dados.status == 200) {
          this.tableData = dados.body.data ? dados.body.data : [];

          this.collectionSize = dados.body.total_items_count;
          this.formatData();
        } else {
          this.tableData = [];
          this.collectionSize = 0;
          this.messageReturn.text = MessagePadroes.NoRegisterClient;
          this.messageReturn.status = true;
          this.message.class = 'alert-success';

          setTimeout(() => {
            this.messageReturn.status = false;
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(error);
      }
    );
  }

  //Método para realizar a busca de estruturas com base nos filtros selecionados.
  searchStructure() {
    let filterHierarchy = this.hierarchy.getFilters();

    this.filterParams = {};

    this.filterParams.SearchIdentifier = this.filterParams.SearchIdentifier == null ? '' : this.filterParams.SearchIdentifier;
    this.filterParams.ClientId = filterHierarchy.clients && filterHierarchy.clients[0] ? filterHierarchy.clients[0].id : '';
    this.filterParams.ClientUnitId = filterHierarchy.units && filterHierarchy.units[0] ? filterHierarchy.units[0].id : '';
    this.filterParams.Name = filterHierarchy.structures && filterHierarchy.structures[0] ? filterHierarchy.structures[0].name : '';
    this.filterParams.Active = this.filter.Active;

    this.filterSearch = {
      ...this.filterParams,
      Page: this.page,
      PageSize: this.pageSize
    };

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.getStructuresList(this.filterSearch);
  }

  //Método para formatar os dados da tabela, adicionando propriedades relevantes.
  formatData() {
    this.tableData = this.tableData.map((item: any) => {
      item.client_unit_id = item.client_unit.id;
      item.client_unit_name = item.client_unit.name;
      item.client_unit_active = item.client_unit.active;
      return item;
    });
  }

  /**
   * Método para alternar o status ativo/inativo de uma estrutura.
   * @param {any} structure - A estrutura cujo status deve ser alterado.
   */
  toggleStatus(structure: any) {
    this.structure.id = structure.id;
    this.structure.active = structure.active;
    this.editStructure();
  }

  //Método para editar uma estrutura, salvando as alterações de status.
  editStructure() {
    this.structuresServiceApi.patchStructures(this.structure.id, this.structure).subscribe((resp) => {
      const dados: any = resp;
      this.message.text = MessageCadastro.AlteracaoStatus;
      this.message.status = true;
      this.message.class = 'alert-success';

      setTimeout(() => {
        this.message.status = false;
      }, 4000);
    });
  }

  //Método para redefinir os filtros de busca para o estado inicial.
  resetFilter() {
    this.hierarchy.resetFilters();

    this.filter = {
      SearchIdentifier: '',
      Name: '',
      Active: ''
    };

    this.filterParams = {};
    this.filterSearch = {};

    this.filterService.setFilters(this.filterSearch, this.hierarchy.getFilters());
    this.managerFilters();
  }

  /**
   * Método para gerenciar a aplicação dos filtros de busca.
   * @param {boolean} $btn - Flag indicando se a ação foi acionada por um botão.
   */
  managerFilters($btn = false) {
    if ($btn) {
      this.searchStructure();
    } else {
      let data = this.filterService.getFilters();

      if (Object.keys(data.filters).length === 0) {
        this.searchStructure();
      } else {
        this.filterSearch = data.filters;
        this.page = this.filterSearch.Page;
        this.pageSize = this.filterSearch.PageSize;

        //Formulario de filtro
        this.filter.Name = this.filterSearch.Name;
        this.filter.Active = this.filterSearch.Active;
        this.filter.SearchIdentifier = this.filterSearch.SearchIdentifier;
        this.filter.ClientId = this.filterSearch.ClientId;
        this.filter.ClientUnitId = this.filterSearch.ClientUnitId;

        this.getStructuresList(this.filterSearch);
      }

      if (Object.keys(data.filtersHierarchy).length !== 0) {
        this.hierarchy.setClients(data.filtersHierarchy.clients);
        this.hierarchy.setUnits(data.filtersHierarchy.units);
        this.hierarchy.setStructures(data.filtersHierarchy.structures);
      }
    }
  }

  /**
   * Método para gerenciar a exibição das colunas da tabela.
   * @param {any} $event - O evento de alteração de exibição de colunas.
   * @param {string} type - O tipo de ação ('select', 'deselect', 'selectAll', 'deselectAll').
   */
  toggleColumns($event: any, type: string) {
    if (type === 'select' || type === 'deselect') {
      let i = this.tableHeader.findIndex((item: { label: any }) => item.label === $event.label);
      this.tableHeader[i].show = !this.tableHeader[i].show;
    } else if (type === 'selectAll') {
      $event.forEach((element: any) => {
        let i = this.tableHeader.findIndex((item: { label: any }) => item.label === element.label);
        this.tableHeader[i].show = true;
      });
    } else if (type === 'deselectAll') {
      this.tableHeader.forEach((element: any, i: number) => {
        this.tableHeader[i].show = false;
      });
    }
  }

  /**
   * Método para tratar eventos de clique em linhas da tabela.
   * Redireciona o usuário para a ação apropriada com base no evento.
   * @param {any} $event - O evento de clique na linha.
   */
  clickRowEvent($event: any = null) {
    const actionLabels: any = {
      edit: 'editar'
    };

    const phraseEndings: any = {
      edit: 'esta Estrutura.'
    };

    const isRestrictedAction = ['edit'].includes($event.action);
    const hasPermission = this.can($event.action);

    if (isRestrictedAction && !hasPermission) {
      this.showPermissionAlert(`Você não tem permissão para ${actionLabels[$event.action]} ${phraseEndings[$event.action]}`);
      return;
    }

    switch ($event.action) {
      case 'edit':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/edit']);
        break;
      case 'view':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/view']);
        break;
      case 'history':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/history']);
        break;
      case 'section':
        this.router.navigate(['/sections'], { queryParams: { structure_id: $event.id } });
        break;
      case 'instrument':
        this.router.navigate(['/instruments'], { queryParams: { structure_id: $event.id } });
        break;
      case 'images':
        this.router.navigate([$event.routerLink + '/' + $event.id + '/images']);
        break;
      default:
        break;
    }
  }

  /**
   * Exibe uma mensagem de alerta na tela por 5 segundos.
   */
  private showPermissionAlert(text: string): void {
    this.message = {
      text,
      status: true,
      class: 'alert-danger'
    };
    setTimeout(() => (this.message.status = false), 5000);
  }

  /**
   * Carrega uma nova página da tabela de seções.
   * Pode receber um número diretamente ou um objeto com propriedades `page` e opcionalmente `pageSize`.
   *
   * @param {number | { page: number | string, pageSize?: number | string }} selectPage
   * - Número da página ou objeto contendo a página e opcionalmente o tamanho da página.
   */
  loadPage(selectPage: any): void {
    let page: number;
    let pageSize: number | undefined;

    if (typeof selectPage === 'object') {
      page = parseInt(selectPage.page, 10);

      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }

      if (selectPage.pageSize !== undefined) {
        pageSize = parseInt(selectPage.pageSize, 10);
        if (!isNaN(pageSize)) {
          this.filterSearch.PageSize = pageSize;
        }
      }
    } else {
      page = Number(selectPage);
      if (!isNaN(page)) {
        this.filterSearch.Page = page;
      }
    }

    this.managerFilters();
  }

  /**
   * Método para gerenciar permissões de usuário, ajustando a interface conforme necessário.
   */
  managerPermission() {
    if (!this.permissaoUsuario.edit) {
    }
  }

  /**
   * Recarrega a página uma única vez para aplicar filtros salvos a partir do cabeçalho.
   *
   * Este método verifica se o filtro já foi recarregado utilizando a `sessionStorage`.
   * - Se ainda não tiver sido recarregado, marca como "recarregado" e força o `reload` da página.
   * - Se já tiver sido recarregado, remove a marcação da `sessionStorage`.
   *
   * Útil para cenários onde filtros devem ser reaplicados automaticamente após navegação
   * ou ações no cabeçalho, evitando recarregamentos infinitos.
   */
  loadFilterByHeader() {
    if (!sessionStorage.getItem('filterReloaded')) {
      sessionStorage.setItem('filterReloaded', 'true');
      window.location.reload();
    } else {
      sessionStorage.removeItem('filterReloaded');
    }
  }

  //Método para navegar de volta para a página principal.
  goBack() {
    this.router.navigate(['/']);
  }
}
