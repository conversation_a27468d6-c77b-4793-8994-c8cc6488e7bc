<div class="send-report">
  <p class="mt-2">Emitir novo relatório</p>

  <form [formGroup]="formSendReport">
    <div class="row">
      <app-hierarchy
        #hierarchy
        [elements]="elements"
        [validate]="true"
      ></app-hierarchy>

      <div class="col-md-3">
        <label class="form-label">Tipo</label>
        <select class="form-select" formControlName="SubjectType">
          <ng-template ngFor let-item [ngForOf]="subjectType">
            <option [ngValue]="item.value">{{ item.label }}</option>
          </ng-template>
        </select>
      </div>

      <div class="col-md-9">
        <label class="form-label">Título</label>
        <input
          type="text"
          class="form-control"
          formControlName="Title"
          autocomplete="off"
          maxlength="255"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSendReport.get('Title').valid &&
            formSendReport.get('Title').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div class="col-md-6">
        <label class="form-label">Responsável</label>
        <input
          type="text"
          class="form-control"
          formControlName="ResponsibleName"
          autocomplete="off"
          maxlength="255"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSendReport.get('ResponsibleName').valid &&
            formSendReport.get('ResponsibleName').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div class="col-md-3">
        <label class="form-label">Data Inicial</label>
        <input type="date" class="form-control" formControlName="StartDate" />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSendReport.get('StartDate').valid &&
            formSendReport.get('StartDate').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div class="col-md-3">
        <label class="form-label">Data Final</label>
        <input type="date" class="form-control" formControlName="EndDate" />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSendReport.get('EndDate').valid &&
            formSendReport.get('EndDate').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <div class="col-md-12">
        <label class="form-label">E-mails (separar por vírgula)</label>
        <input
          type="text"
          class="form-control"
          formControlName="DestinationEmails"
          autocomplete="off"
          maxlength="255"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formSendReport.get('DestinationEmails').valid &&
            formSendReport.get('DestinationEmails').touched
          "
          >Campo Obrigatório.</small
        >
      </div>

      <app-alert
        class="mt-3 alert-danger"
        [messages]="messagesError"
      ></app-alert>

      <div
        class="mt-3 alert"
        [ngClass]="message.class"
        role="alert"
        *ngIf="message.status"
      >
        {{ message.text }}
      </div>

      <div class="d-flex justify-content-end mt-3">
        <app-button
          class="btn-logisoil-green"
          [icon]="'fa fa-file-text-o'"
          [label]="'Emitir Relatório'"
          [type]="false"
          [disabled]="!formSendReport.valid || !this.isHierarchyValid"
          (click)="validate()"
        ></app-button>
      </div>
    </div>

    <div class="col-md-12 mt-3 d-flex justify-content-end mb-3">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela principal'"
        [routerLink]="['/reports']"
      ></app-button>
    </div>
  </form>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
