import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, FormArray, FormBuilder, Validators } from '@angular/forms';
import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-material-points',
  templateUrl: './material-points.component.html',
  styleUrls: ['./material-points.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class MaterialPointsComponent implements OnInit, OnChanges {
  @Input() public pointsInput: any = { x: 'Label X', y: 'Label Y' };
  @Input() public buttonChart: boolean = true;
  @Input() public data: any = null;
  @Input() public view: boolean = false;
  @Output() public sendDataChart = new EventEmitter();

  public formPoints: FormGroup;

  public func = fn;

  constructor(private formBuilder: FormBuilder) {
    this.formPoints = this.formBuilder.group({
      points: this.formBuilder.array([])
    });
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    } else {
      this.addPoint();
    }
  }

  points(): FormArray {
    return this.formPoints.get('points') as FormArray;
  }

  newPoint($point = null): FormGroup {
    return this.formBuilder.group({
      id: new FormControl($point != null ? $point.id : null),
      point_x: new FormControl($point != null ? $point.point_x : null, [Validators.required]),
      point_y: new FormControl($point != null ? $point.point_y : null, [Validators.required])
    });
  }

  addPoint($point = null) {
    this.points().push(this.newPoint($point));
  }

  removePoint(pointIndex: number) {
    this.points().removeAt(pointIndex);
  }

  generateChart() {
    let chart = {
      label_x: this.pointsInput.x,
      label_y: this.pointsInput.y,
      data: this.formPoints.value.points.map((points) => {
        const point_x = parseFloat(points.point_x);
        const point_y = parseFloat(points.point_y);
        return {
          id: points.id,
          point_x: point_x.toFixed(4),
          point_y: point_y.toFixed(4)
        };
      })
    };

    this.sendDataChart.emit(chart);
  }

  splitData($dados) {
    $dados = $dados.map((point) => {
      let item = {};
      item['id'] = point.id;
      item['point_x'] = this.view ? point.value_1.toFixed(4) : point.value_1;
      item['point_y'] = this.view ? point.value_2.toFixed(4) : point.value_2;
      this.addPoint(item);
      return item;
    });

    if (this.buttonChart) {
      setTimeout(() => {
        this.generateChart();
      }, 200);
    }
    if (this.view) {
      this.formPoints.disable();
    }
  }

  validate() {
    if (!this.points().valid) {
      this.points().markAllAsTouched();
    }
    return this.points().valid;
  }
}
