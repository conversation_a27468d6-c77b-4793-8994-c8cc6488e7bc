const OnLine = [
  { value: true, label: 'Sim' },
  { value: false, label: 'N<PERSON>' }
];

const Automated = [
  { value: true, label: 'Sim' },
  { value: false, label: 'Não' }
];

enum Subtypes {
  Percolação = 1, // INA’s, PZ’s, Medidor de Vazao e Regua Linimetrica
  Deslocamentos = 2, // Marco superficial, Medidor de Recalque, Inclinometros
  Microssísmica = 3, // Geofone
  Clima = 4 // Estacao Meteorologica, Pluviometro, Pluviógrafo
}

const GeophoneType = [
  { value: 1, label: 'Uniaxial' },
  { value: 2, label: 'Triaxial' }
];

const TypeRegister = [
  { value: 1, label: 'Atenção' },
  { value: 2, label: 'Comentário geral' },
  { value: 3, label: 'Histórico' }
];

//Histórico da instrumentação
const SecurityLevel = [
  { value: 1, label: 'Nível de atenção', color: '#f8fa9f' },
  { value: 2, label: 'Nível de alerta', color: '#f8c179' },
  { value: 3, label: 'Nível de emergência', color: '#f597a1' },
  { value: 4, label: 'Variação brusca (m) entre leituras', color: '#eeeeee' },
  //Pluviômetro e Pluviógrafo
  { value: 5, label: 'Nível de segurança da pluviometria diária (mm)', color: '#f8fa9f' },
  { value: 6, label: 'Nível de segurança da intensidade (mm/h)', color: '#eeeeee' }
];

const Axis = [
  { value: 1, label: 'Deslocamento A', color: '' },
  { value: 2, label: 'Deslocamento B', color: '' },
  { value: 3, label: 'Deslocamento Z', color: '' },
  { value: 4, label: 'Deslocamento N', color: '' },
  { value: 5, label: 'Deslocamento E', color: '' },
  { value: 6, label: 'Deslocamento Planimétrico', color: '' }
];

const Position = [
  { value: 1, label: 'Montante' },
  { value: 2, label: 'Jusante' }
];

const FileFormat = [
  { value: 1, label: '.xlsx' },
  { value: 2, label: '.csv' }
];

//Coluna Variação Absoluta
const PeriodsAbs = [
  { value: 30, label: '1 mês', show: true },
  { value: 90, label: '3 meses', show: true },
  { value: 180, label: '6 meses', show: true },
  { value: 365, label: '1 ano', show: true },
  { value: 730, label: '2 anos', show: true },
  { value: 1825, label: '5 anos', show: true }
];

//Coluna Variação Absoluta
const Ordenation = [
  // { value: 1, label: 'Data de Criação Crescente' },
  // { value: 2, label: 'Data de Criação Decrescente' },
  { value: 3, label: 'Crescente' },
  { value: 4, label: 'Decrescente' }
];

const DryType = [
  { value: 1, label: 'Interpolado' },
  { value: 2, label: 'Seco' }
];

// Cadastro de instrumento
const typeInstruments = [
  {
    name: "Indicador de nível d'água", //WaterLevelIndicator
    id: 1,
    subType: Subtypes.Percolação,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: false,
    fields: ['top_quota', 'base_quota', 'dry_type'],
    typeMeasure: '',
    nameMeasure: '',
    alias: 'INA'
  },
  {
    name: 'Piezômetro de tubo aberto', //OpenStandpipePiezometer
    id: 2,
    subType: Subtypes.Percolação,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: false,
    fields: ['top_quota', 'base_quota', 'dry_type'],
    typeMeasure: '',
    nameMeasure: '',
    alias: 'PZ'
  },
  {
    name: 'Piezômetro elétrico', //EletricPiezometer
    id: 3,
    subType: Subtypes.Percolação,
    pressureCells: true,
    measuringPoints: false,
    magneticRings: false,
    fields: ['top_quota', 'dry_type'],
    typeMeasure: 'pressure_cell',
    nameMeasure: 'Células de Pressão',
    alias: 'PZ'
  },
  {
    name: 'Inclinômetro convencional', //ConventionalInclinometer
    id: 4,
    subType: Subtypes.Deslocamentos,
    pressureCells: false,
    measuringPoints: true,
    magneticRings: false,
    fields: ['top_quota', 'azimuth'],
    typeMeasure: 'measure_point',
    nameMeasure: 'Pontos de Medição',
    alias: 'INC'
  },
  {
    name: 'Inclinômetro IPI', //IPIInclinometer
    id: 5,
    subType: Subtypes.Deslocamentos,
    pressureCells: false,
    measuringPoints: true,
    magneticRings: false,
    fields: ['top_quota', 'azimuth'],
    typeMeasure: 'measure_point',
    nameMeasure: 'Pontos de Medição',
    alias: 'INC'
  },
  {
    name: 'Marco superficial', //SurfaceLandmark
    id: 6,
    subType: Subtypes.Deslocamentos,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: false,
    fields: ['top_quota', 'azimuth', 'upper_limit', 'lower_limit'],
    typeMeasure: '',
    alias: 'MS'
  },
  {
    name: 'Prisma', //Prism
    id: 7,
    subType: Subtypes.Deslocamentos,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: false,
    fields: ['top_quota', 'azimuth', 'upper_limit', 'lower_limit'],
    typeMeasure: '',
    nameMeasure: '',
    alias: 'PRI'
  },
  {
    name: 'Medidor de recalque', //SettlementGauge
    id: 8,
    subType: Subtypes.Deslocamentos,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: true,
    fields: ['top_quota', 'depth'],
    typeMeasure: 'magnetic_ring',
    nameMeasure: 'Anéis Magnéticos',
    alias: 'MR'
  },
  {
    name: 'Geofone', //Geophone
    id: 9,
    subType: Subtypes.Microssísmica,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: false,
    fields: ['elevation', 'geophone_type'],
    typeMeasure: '',
    nameMeasure: '',
    alias: 'GEO'
  },
  {
    name: 'Régua linimétrica',
    id: 10,
    subType: Subtypes.Percolação,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: false,
    fields: ['top_quota', 'linimetric_ruler_position'],
    typeMeasure: '',
    nameMeasure: '',
    alias: 'RL'
  },
  {
    name: 'Pluviômetro',
    id: 12,
    subType: Subtypes.Clima,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: false,
    fields: [],
    typeMeasure: '',
    nameMeasure: '',
    alias: 'PLUV'
  },
  {
    name: 'Pluviógrafo',
    id: 13,
    subType: Subtypes.Clima,
    pressureCells: false,
    measuringPoints: false,
    magneticRings: false,
    fields: ['measurement_frequency'],
    typeMeasure: '',
    nameMeasure: '',
    alias: 'PLG'
  }
];

// Legenda Grupo de Instrumentos
const groupInstruments = [
  { name: "Indicador de nível d'água (INA)", icon: 'fa fa-circle', color: 'blue', type: 1, subtype: 1 },
  { name: 'Piezômetro de tubo aberto', icon: 'fa fa-circle', color: 'red', type: 2, subtype: 1 },
  { name: 'Piezômetro elétrico', icon: 'fa fa-circle', color: 'tomato', type: 3, subtype: 1 },
  { name: 'Inclinômetro convencional', icon: 'fa fa-circle', color: 'yellow', type: 4, subtype: 2 },
  { name: 'Inclinômetro IPI', icon: 'fa fa-circle', color: 'yellow', type: 5, subtype: 2 },
  { name: 'Marco superficial (MS)', icon: 'fa fa-circle', color: 'purple', type: 6, subtype: 2 },
  { name: 'Prisma', icon: 'fa fa-circle', color: 'mediumpurple', type: 7, subtype: 2 },
  { name: 'Medidor de recalque', icon: 'fa fa-circle', color: 'brown', type: 8, subtype: 2 },
  { name: 'Geofone', icon: 'fa fa-circle', color: 'green', type: 9, subtype: 3 },
  { name: 'Régua linimétrica', icon: 'fa fa-circle', color: 'cyan', type: 10, subtype: 1 },
  { name: 'Pluviômetro', icon: 'fa fa-circle', color: 'teal', type: 12, subtype: 4 },
  { name: 'Pluviógrafo', icon: 'fa fa-circle', color: 'teal', type: 13, subtype: 4 }
  // { name: 'Medidor de vazão', icon: 'fa fa-circle', color: 'pink' }
  // { name: 'Estação meteorológica', icon: 'fa fa-circle', color: 'orange' },
];

const setFieldsInstruments = [
  'azimuth',
  'geophone_type',
  'elevation',
  'top_quota',
  'base_quota',
  'depth',
  'upper_limit',
  'lower_limit',
  'measurement_frequency'
];

export {
  OnLine,
  Automated,
  Subtypes,
  GeophoneType,
  typeInstruments,
  groupInstruments,
  setFieldsInstruments,
  TypeRegister,
  SecurityLevel,
  Axis,
  Position,
  FileFormat,
  PeriodsAbs,
  Ordenation,
  DryType
};
