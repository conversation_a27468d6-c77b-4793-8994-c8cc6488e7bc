import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class PositionService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  // Cadastro de Cargos
  postPositions(params: any) {
    const url = '/positions';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna os cargos para uso em filtro
  getPositions(params: any) {
    const url = '/positions/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getPositionsList(params: any = {}) {
    const url = '/positions';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Busca o cargo por ID
  getPositionsById(id: string) {
    const url = `/positions/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  putPositions(id: string, params: any) {
    const url = `/positions/${id}`;
    return this.api.put<any>(url, params, 'client');
  }
}
