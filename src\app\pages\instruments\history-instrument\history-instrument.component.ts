import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { CustomValidators } from 'src/app/utils/custom-validators';

import { MessageCadastro, MessageInputInvalid } from 'src/app/constants/message.constants';
import { TypeRegister } from 'src/app/constants/instruments.constants';

import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';
import { UserService } from 'src/app/services/user.service';

//Para colocar a URL do arquivo .dxf como seguro
import { DomSanitizer } from '@angular/platform-browser';

import * as moment from 'moment';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-history-instrument',
  templateUrl: './history-instrument.component.html',
  styleUrls: ['./history-instrument.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HistoryInstrumentComponent implements OnInit {
  public formHistoryInstrument: FormGroup = new FormGroup({
    id: new FormControl(null),
    description: new FormControl('', [Validators.required, Validators.maxLength(1000)]),
    type: new FormControl('', [Validators.required]),
    image: new FormControl(
      null,
      Validators.compose([CustomValidators.validadorExtensaoArquivo(['png', 'jpg', 'jpeg']), CustomValidators.validadorTamanhoArquivo(1000000)])
    ),
    file: new FormControl(''),
    display_in_charts: new FormControl(true)
  });

  public typeRegister: any = TypeRegister;

  public edit: boolean = false;
  public editNote: boolean = false;

  public maxlength: number = 1000;
  public charachtersCount: number = 0;
  public counter: string;

  public fileContentImage: string = '';
  public fileContentFile: string = '';

  public fileNameImage: string = '';
  public fileNameFile: string = '';
  public fileContentDownloadFile: any = '';

  public ctrlHistory: boolean = false;

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;
  public messageReturn: any = { text: '', status: false };
  public messageReturnNote: any = { text: '', status: false };
  public messageReturnNoteHistory: any = { text: '', status: false };

  public profile: any = null;
  public permissaoUsuario: any = null;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public pageNote: number = 1;
  public pageSizeNote: number = 10;
  public collectionSizeNote: number = 0;

  public pageNoteHistory: number = 1;
  public pageSizeNoteHistory: number = 10;
  public collectionSizeNoteHistory: number = 0;

  public instrumentNoteRequest: any = {};

  public tableHeader: any = [
    {
      label: 'Data/Hora',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Usuário',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Modificações',
      width: '65%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    }
  ];

  public tableData: any = [];

  public tableHeaderNote: any = [
    {
      label: 'ID',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['search_identifier']
    },
    {
      label: 'Data/Hora',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date_format']
    },
    {
      label: 'Usuário',
      width: '15%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Tipo',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['type_name']
    },
    {
      label: 'Descrição',
      width: '45%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['description']
    },
    {
      label: 'Imagem',
      width: '15%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['image_name']
    },
    {
      label: 'Arquivo',
      width: '15%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['file_name']
    },
    {
      label: 'Ações',
      width: '80px',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['actionCustom'],
      class: ''
    }
  ];

  public tableDataNote: any = [];

  public tableHeaderNoteHistory: any = [
    {
      label: 'Data/Hora',
      width: '10%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['created_date']
    },
    {
      label: 'Usuário',
      width: '25%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['user']
    },
    {
      label: 'Modificações',
      width: '65%',
      rowspan: '',
      colspan: '',
      show: true,
      referent: ['changes']
    }
  ];

  public tableDataNoteHistory: any = [];

  public actionCustom: any = [
    {
      class: 'btn-logisoil-edit-group',
      icon: 'fa fa-thin fa-pen-to-square',
      label: '',
      title: 'Editar',
      type: 'true',
      option: 'edit'
    },
    {
      class: 'btn-logisoil-history',
      icon: 'fa fa-history',
      label: '',
      title: 'Histórico',
      type: 'true',
      option: 'history'
    }
  ];

  public instrumentNote: any = {
    id: null,
    description: null,
    type: null,
    created_date: null,
    file: {
      base64: null,
      name: null
    },
    image: {
      base64: null,
      name: null
    },
    display_in_charts: false
  };

  public selectedNoteId: string = '';

  constructor(
    private activatedRoute: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private userService: UserService
  ) {}

  /**
   * Método que é executado ao iniciar o componente.
   * Inicializa o perfil do usuário, verifica permissões, e carrega o histórico e as notas do instrumento.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;

    if (this.activatedRoute.snapshot.params.noteId) {
      this.edit = true;
    }

    this.getHistoryInstruments(this.activatedRoute.snapshot.params.instrumentId);
    this.getNoteInstrument(this.activatedRoute.snapshot.params.instrumentId);
  }

  /**
   * Obtém o histórico do instrumento e formata os dados para exibição na tabela.
   * @param {string} instrumentId - O ID do instrumento para o qual o histórico será obtido.
   */
  getHistoryInstruments(instrumentId: string) {
    const params = {
      Page: this.page,
      PageSize: this.pageSize
    };

    this.instrumentsServiceApi.getInstrumentsHistory(instrumentId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados) {
        this.tableData = dados ? dados.data : [];
        this.collectionSize = dados.total_items_count;
        this.formatDataHistory('tableData');
      } else {
        this.tableData = [];
        this.collectionSize = 0;
        this.messageReturn.text = MessageInputInvalid.NoHistory;
        this.messageReturn.status = true;
      }
    });
  }

  /**
   * Obtém as notas associadas ao instrumento e formata os dados para exibição na tabela.
   * @param {string} instrumentId - O ID do instrumento para o qual as notas serão obtidas.
   */
  getNoteInstrument(instrumentId: string) {
    const params = {
      Page: this.pageNote,
      PageSize: this.pageSizeNote
    };

    this.instrumentsServiceApi.getInstrumentsNotes(instrumentId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;

      if (dados) {
        this.tableDataNote = dados ? dados.data : [];
        this.collectionSizeNote = dados.total_items_count;
        this.formatDataNote();
        this.messageReturnNote = [{ text: '', status: false }];
      } else {
        this.tableDataNote = [];
        this.collectionSizeNote = 0;
        this.messageReturnNote.text = MessageInputInvalid.NoNotes;
        this.messageReturnNote.status = true;
      }
    });
  }

  /**
   * Obtém o histórico de uma nota específica de um instrumento e formata os dados para exibição na tabela.
   * @param {string} instrumentId - O ID do instrumento.
   * @param {string} noteId - O ID da nota cujo histórico será obtido.
   */
  getNoteInstrumentHistory(instrumentId: string, noteId: string) {
    this.selectedNoteId = noteId;

    const params = {
      Page: this.pageNoteHistory,
      PageSize: this.pageSizeNoteHistory
    };

    this.instrumentsServiceApi.getInstrumentsNoteHistory(instrumentId, noteId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      if (dados) {
        this.tableDataNoteHistory = dados ? dados.data : [];
        this.collectionSizeNoteHistory = dados.total_items_count;
        this.formatDataHistory('tableDataNoteHistory');
      } else {
        this.tableDataNoteHistory = [];
        this.collectionSizeNoteHistory = 0;
        this.messageReturnNoteHistory.text = MessageInputInvalid.NoHistory;
        this.messageReturnNoteHistory.status = true;
      }
    });
  }

  //Cadastra uma nova nota para o instrumento atual.
  registerNoteInstrument() {
    let instrumentId = this.activatedRoute.snapshot.params.instrumentId;
    this.instrumentsServiceApi.postInstrumentsNotes(instrumentId, this.instrumentNoteRequest).subscribe(
      (resp) => {
        this.message.text = MessageCadastro.NovoRegistro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.getNoteInstrument(this.activatedRoute.snapshot.params.instrumentId);
        this.resetForm('historyInstrument');
      },
      (error) => {
        console.log(error);
        if (error.status === 400) {
          const index = Object.keys(error.error.errors);
          this.messagesError = error.error.errors[index[0]][0];
        }
      }
    );
  }

  /**
   * Carrega os dados de uma nota para edição, incluindo arquivos anexos e imagens.
   * @param {string} instrumentNoteId - O ID da nota a ser carregada.
   */
  loadNoteInstrument(instrumentNoteId: string) {
    let idx = fn.findIndexInArrayofObject(this.tableDataNote, 'id', instrumentNoteId);
    let instrumentNote = this.tableDataNote[idx];

    if (this.profile.id == instrumentNote.created_by.id) {
      this.formHistoryInstrument.controls['id'].setValue(instrumentNote.id);
      this.formHistoryInstrument.controls['type'].setValue(instrumentNote.type);
      this.formHistoryInstrument.controls['description'].setValue(instrumentNote.description);
      this.ctrlHistory = true;
      this.editNote = true;
      this.messagesError = null;

      if (instrumentNote.file) {
        this.fileContentFile = instrumentNote.file.base64;
        this.fileNameFile = instrumentNote.file.name;
        this.fileContentDownloadFile = this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + this.fileContentFile);
      }

      if (instrumentNote.image) {
        this.fileContentImage = 'data:' + this.detectMimeType(instrumentNote.image.base64) + ';base64,' + instrumentNote.image.base64;
        this.fileNameImage = instrumentNote.image.name;
      }
    } else {
      this.messagesError = [{ message: 'Não é possível editar histórico criado por outro usuário.' }];
      this.ctrlHistory = false;
      this.editNote = false;

      setTimeout(() => {
        this.messagesError = null;
      }, 4000);
    }
  }

  //Edita uma nota existente do instrumento atual.
  editNoteInstrument() {
    let instrumentId = this.activatedRoute.snapshot.params.instrumentId;
    this.instrumentsServiceApi.putInstrumentsNotes(instrumentId, this.instrumentNoteRequest.id, this.instrumentNoteRequest).subscribe(
      (resp) => {
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);

        this.getNoteInstrument(this.activatedRoute.snapshot.params.instrumentId);
        this.resetForm('historyInstrument');
      },
      (error) => {
        console.log(error);
        if (error.status === 400) {
          const index = Object.keys(error.error.errors);
          this.messagesError = error.error.errors[index[0]][0];
        }
      }
    );
  }

  /**
   * Formata os dados do histórico para exibição na tabela.
   * @param {string} type - O tipo de dados a serem formatados ('tableData' ou 'tableDataNoteHistory').
   */
  formatDataHistory(type) {
    this[type] = this[type].map((item: any) => {
      let itemData = {
        created_date: moment(item.created_date).format('DD/MM/YYYY HH:mm:ss'),
        user: item.modified_by.first_name + ' ' + item.modified_by.surname,
        changes: item.changes
      };
      return itemData;
    });
  }

  //Formata os dados das notas para exibição na tabela, incluindo a identificação, o usuário e os arquivos anexos.
  formatDataNote() {
    this.tableDataNote = this.tableDataNote.map((item: any, index: number) => {
      let idx = fn.findIndexInArrayofObject(this.typeRegister, 'value', item.type);

      let itemData = item;
      itemData['search_identifier'] = index + 1;
      itemData['user'] = item.created_by.first_name + ' ' + item.created_by.surname;
      itemData['type_name'] = this.typeRegister[idx].label;
      itemData['created_date_format'] = moment(item.created_date).format('DD/MM/YYYY HH:mm:ss');
      itemData['image_name'] = item.image != null ? item.image.name : '';
      itemData['file_name'] = item.file != null ? item.file.name : '';

      return itemData;
    });
  }

  //Formata os dados do formulário de nota antes de enviá-los para cadastro ou edição.
  formatData() {
    this.instrumentNoteRequest = this.instrumentNote;

    this.instrumentNoteRequest.description = this.formHistoryInstrument.controls['description'].value;
    this.instrumentNoteRequest.type = this.formHistoryInstrument.controls['type'].value;

    if (this.formHistoryInstrument.controls['file'].value) {
      const fileNameFile = this.formHistoryInstrument.controls['file'].value.split('\\');
      this.instrumentNoteRequest.file.name = fileNameFile[fileNameFile.length - 1];
      this.instrumentNoteRequest.file.base64 = this.fileContentFile;
    } else if (this.fileContentFile) {
      this.instrumentNoteRequest.file.name = this.fileNameFile;
      this.instrumentNoteRequest.file.base64 = this.fileContentFile;
    } else {
      delete this.instrumentNoteRequest.file;
    }

    if (this.formHistoryInstrument.controls['image'].value) {
      const fileNameImage = this.formHistoryInstrument.controls['image'].value.split('\\');
      this.instrumentNoteRequest.image.name = fileNameImage[fileNameImage.length - 1];
      this.instrumentNoteRequest.image.base64 = this.fileContentImage;
    } else if (this.fileContentImage) {
      this.instrumentNoteRequest.image.name = this.fileNameImage;
      this.instrumentNoteRequest.image.base64 = this.fileContentImage;
    } else {
      delete this.instrumentNoteRequest.image;
    }
    if (!this.editNote) {
      delete this.instrumentNoteRequest.id;
      this.registerNoteInstrument();
    } else {
      this.instrumentNoteRequest.id = this.formHistoryInstrument.controls['id'].value;
      this.editNoteInstrument();
    }
  }

  /**
   * Redefine o formulário de nota, limpando todos os campos e variáveis de controle.
   * @param {string} [type=''] - O tipo de formulário a ser redefinido ('historyInstrument').
   * @param {boolean} [option=false] - Define se o controle de histórico deve ser mantido ativado.
   */
  resetForm(type: string = '', option: boolean = false) {
    if (type === 'historyInstrument') {
      this.ctrlHistory = false;
      this.editNote = false;

      this.fileContentImage = '';
      this.fileContentFile = '';
      this.fileContentDownloadFile = '';

      this.fileNameFile = '';
      this.fileNameImage = '';

      this.formHistoryInstrument.reset();
      this.formHistoryInstrument.controls['type'].setValue('');
      if (option) {
        this.ctrlHistory = true;
      }
    }
  }

  /**
   * Atualiza o contador de caracteres com base no valor digitado no campo de texto.
   * @param {any} event - O evento de mudança de valor no campo de texto.
   */
  onValueChange(event: any): void {
    this.charachtersCount = event.target.value.length;
    this.counter = `${this.charachtersCount} de ${this.maxlength}`;
  }

  /**
   * Carrega um arquivo de imagem ou documento para o formulário de nota.
   * @param {any} $event - O evento de upload de arquivo.
   */
  uploadFile($event: any) {
    let file = $event.dataTransfer ? $event.dataTransfer.files[0] : $event.target.files[0];
    let reader = new FileReader();

    reader.onload = this.uploadFileLoaded.bind(this);
    reader.readAsDataURL(file);
  }

  /**
   * Lê o conteúdo do arquivo carregado e o converte para base64 para armazenamento.
   * @param {any} $event - O evento de leitura do arquivo.
   */
  uploadFileLoaded($event: any) {
    let reader = $event.target;
    if (reader.result.substring(5, 10) == 'image') {
      this.fileContentImage = reader.result;
    } else {
      this.fileContentFile = reader.result.split(';base64,')[1];
      // this.fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl('data:aplication/octet-stream;base64,' + this.fileContent);
      // const fileName = this.formReview.get('drawing').value.split('\\');
      // this.fileName = fileName[fileName.length - 1];
    }
  }

  /**
   * Gerencia os eventos de clique nas linhas da tabela de notas, executando as ações correspondentes.
   * @param {any} $event - O evento que contém a ação e o ID da nota.
   */
  clickRowEvent($event: any = null) {
    switch ($event.action) {
      case 'edit':
        this.loadNoteInstrument($event.id);
        break;
      case 'history':
        this.selectedNoteId = '';
        this.tableDataNoteHistory = [];
        this.getNoteInstrumentHistory(this.activatedRoute.snapshot.params.instrumentId, $event.id);
        break;
      default:
        break;
    }
  }

  /**
   * Detecta o tipo MIME de um arquivo a partir de sua string base64.
   * @param {string} base64 - A string base64 do arquivo.
   * @returns {string} - O tipo MIME detectado.
   */
  detectMimeType(base64: string | string[]) {
    const signatures: any = {
      // JVBERi0: 'application/pdf',
      // R0lGODdh: 'image/gif',
      // R0lGODlh: 'image/gif',
      iVBORw0KGgo: 'image/png',
      '/9j/': 'image/jpg'
    };
    for (let s in signatures) {
      if (base64.indexOf(s) === 0) {
        return signatures[s];
      }
    }
  }

  //Valida os dados do formulário de nota antes de enviar para cadastro ou edição.
  validate() {
    this.formatData();
  }

  /**
   * Método que recebe a página selecionada e carrega os dados correspondentes para a tabela de histórico ou de notas.
   * @param {number} selectPage - O número da página a ser carregada.
   * @param {string} option - A opção para determinar se os dados carregados serão do histórico ('history') ou das notas ('note').
   */
  loadPage(selectPage: number, option: string): void {
    if (option == 'history') {
      this.page = selectPage;
      this.getHistoryInstruments(this.activatedRoute.snapshot.params.instrumentId);
    } else if (option == 'note') {
      this.pageNote = selectPage;
      this.getNoteInstrument(this.activatedRoute.snapshot.params.instrumentId);
    }
  }
}
