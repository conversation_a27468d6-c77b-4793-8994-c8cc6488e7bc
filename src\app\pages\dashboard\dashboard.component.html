<div class="list-section" [formGroup]="sectionForm">
  <!-- Seção -->
  <div class="row mt-2 mb-3">
    <div class="col-md-3">
      <label class="form-label">Seção</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="sectionSettings"
        [data]="sections"
        formControlName="SectionId"
        (onSelect)="
          getDashboardSectionDXF($event, 'select');
          getDashboardSectionMap($event, 'select');
          getDashboardSafetyFactor($event, 'select');
          getDashboardPercolation($event, 'select')
        "
        (onDeSelect)="
          getDashboardSectionDXF($event, 'deselect');
          getDashboardSectionMap($event, 'deselect');
          getDashboardSafetyFactor($event, 'deselect');
          getDashboardPercolation($event, 'deselect')
        "
      >
      </ng-multiselect-dropdown>
    </div>
    <!-- Data -->
    <div class="col-md-3">
      <label class="form-label">Gráficos - Selecione um período</label>
      <select
        class="form-select"
        formControlName="Period"
        (change)="calculatePeriod(sectionForm.get('Period').value)"
      >
        <ng-template ngFor let-item [ngForOf]="period">
          <option [ngValue]="item.value">{{ item.label }}</option>
        </ng-template>
        <option value="0">Personalizado</option>
      </select>
    </div>
    <!-- Data inicial -->
    <div class="col-md-2" *ngIf="sectionForm.get('Period').value != 1">
      <label class="form-label">Data inicial</label>
      <input
        type="date"
        class="form-control"
        formControlName="StartDate"
        (change)="calculatePeriod(sectionForm.get('Period').value, false)"
      />
    </div>
    <!-- Data final -->
    <div class="col-md-2" *ngIf="sectionForm.get('Period').value != 1">
      <label class="form-label">Data final</label>
      <input
        type="date"
        class="form-control"
        formControlName="EndDate"
        (change)="calculatePeriod(sectionForm.get('Period').value, false)"
      />
    </div>
    <!-- Buscar -->
    <div class="col-md-2 d-flex align-items-end" style="padding: 5px">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        (click)="
          getDashboardSafetyFactor(
            sectionForm.get('SectionId').value[0],
            'select'
          );
          getDashboardPercolation(
            sectionForm.get('SectionId').value[0],
            'select'
          )
        "
      ></app-button>
    </div>
  </div>
</div>

<div class="container-grid mt-2">
  <!-- Mapa da seção -->
  <div class="grid-item map-container" style="position: relative">
    <div class="grid-header">
      <div>
        <i class="fas fa-map-marked me-2"></i>
        <span *ngIf="structureName != ''">
          Estrutura: {{ structureName }}
          {{ sectionForm.get('SectionId')?.value?.[0]?.name ? '| Seção: ' + sectionForm.get('SectionId')?.value?.[0]?.name : ''}}
        </span>
      </div>
    </div>
    <app-google-maps [discount]="30"></app-google-maps>

    <!-- Dropdown para Legenda -->
    <div class="dropdown" style="position: absolute; top: 42px; right: 64px">
      <app-button
        [class]="'btn-logisoil-green'"
        [customBtn]="true"
        [label]="'Legenda'"
        (click)="toggleDropdown()"
        aria-expanded="false"
      ></app-button>
      <ul
        class="dropdown-menu dropdown-menu-end mt-1 p-3"
        [class.show]="dropdownOpen"
        style="max-height: 300px; overflow-y: auto; width: 250px"
      >
        <li
          class="dropdown-item d-flex align-items-center mb-2"
          *ngFor="let item of legendItens"
        >
          <i class="{{ item.icon }} me-2" [style.color]="item.color"></i>
          <span class="legend">{{ item.name }}</span>
        </li>
      </ul>
    </div>
  </div>

  <!-- DXF Viewer -->
  <div class="grid-item">
    <div class="grid-header">
      <div>
        <i class="fa fa-file me-2"></i>
        <span
          >DXF - Seção: {{ sectionForm.get('SectionId')?.value?.[0]?.name }} |
          {{ titleDxf }}
        </span>
      </div>
    </div>

    <!-- Mensagem caso não haja dados -->
    <div
      *ngIf="fileDxf == null"
      class="d-flex justify-content-center align-items-center"
      style="height: 340px"
    >
      <div class="text-center">
        <i class="fa fa-exclamation-circle text-warning fs-3"></i>
        <p
          class="mt-2 mb-0 text-muted"
          *ngFor="let message of messagesErrorDXF"
        >
          {{ message.message }}
        </p>
      </div>
    </div>
    <app-dxf-viewer-v2
      *ngIf="fileDxf != null"
      [fileDxf]="fileDxf"
      style="height: 100%; width: 100%"
    ></app-dxf-viewer-v2>
  </div>

  <!-- Gráfico FS -->
  <div class="grid-item" style="position: relative">
    <div class="grid-header">
      <div>
        <i class="fa fa-area-chart me-2"></i
        ><span *ngIf="structureName != ''"
          >Gráfico FS - Seção:
          {{ sectionForm.get('SectionId')?.value?.[0]?.name}}</span
        >
      </div>
    </div>

    <!-- Mensagem caso não haja dados -->
    <div
      *ngIf="messagesErrorChartFS.length > 0"
      class="d-flex justify-content-center align-items-center"
      style="height: 340px"
    >
      <div class="text-center">
        <i class="fa fa-exclamation-circle text-warning fs-3"></i>
        <p
          class="mt-2 mb-0 text-muted"
          *ngFor="let message of messagesErrorChartFS"
        >
          {{ message.message }}
        </p>
      </div>
    </div>
    <div *ngIf="chartFS.options">
      <app-e-charts
        #chartSafetyFactor
        [height]="340"
        [dataChart]="chartFS"
        [id]="'safetyFactor'"
        (chartInit)="initMarkerClickEventFS($event, 'safetyFactor')"
      ></app-e-charts>
    </div>
  </div>

  <!-- Gráfico INA's e PZ's -->
  <div class="grid-item">
    <div class="grid-header">
      <div>
        <i class="fa fa-area-chart me-2"></i
        ><span *ngIf="structureName != ''"
          >Gráfico INA's/PZ's - Seção:
          {{ sectionForm.get('SectionId')?.value?.[0]?.name }}</span
        >
      </div>
    </div>

    <!-- Mensagem caso não haja dados -->
    <div
      *ngIf="messagesErrorChartPercolation.length > 0"
      class="d-flex justify-content-center align-items-center"
      style="height: 340px"
    >
      <div class="text-center">
        <i class="fa fa-exclamation-circle text-warning fs-3"></i>
        <p
          class="mt-2 mb-0 text-muted"
          *ngFor="let message of messagesErrorChartPercolation"
        >
          {{ message.message }}
        </p>
      </div>
    </div>

    <div style="position: absolute; top: 804px; right: 140px">
      <!-- Para implementar no futuro -->
      <!-- <app-button
        [class]="'btn-logisoil-green'"
        [customBtn]="true"
        [label]="'Configuração dos eixos'"
        aria-expanded="false"
      ></app-button> -->
    </div>
    <div *ngIf="chartPercolation.options">
      <app-e-charts
        #chartPercolations
        [height]="340"
        [dataChart]="chartPercolation"
        [id]="'percolation'"
      ></app-e-charts>
    </div>
  </div>
</div>

<app-modal-chart-fs
  #modalChartFs
  [title]="titleModal"
  [sectionResult]="dadosFS?.section_result"
  [selectedItem]="selectedItem"
></app-modal-chart-fs>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
