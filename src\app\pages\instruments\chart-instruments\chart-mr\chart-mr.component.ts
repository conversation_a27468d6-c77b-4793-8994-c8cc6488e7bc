import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { Periods, InfoYAxis, YAxisLabel } from 'src/app/constants/chart.constants';
import { MessageInputInvalid } from 'src/app/constants/message.constants';

import { ChartService as ChartServiceApi } from 'src/app/services/api/chart.service';
import { InstrumentsService as InstrumentsServiceApi } from 'src/app/services/api/instrument.service';

import { FileLoaderService } from 'src/app/services/file-loader.service';

import * as moment from 'moment';
import fn from 'src/app/utils/function.utils';
import * as d3 from 'd3';

@Component({
  selector: 'app-chart-mr',
  templateUrl: './chart-mr.component.html',
  styleUrls: ['./chart-mr.component.scss']
})
export class ChartMrComponent implements OnInit {
  public formChart: FormGroup = new FormGroup({
    instrument: new FormControl([]),
    start_date: new FormControl('', [Validators.required]),
    end_date: new FormControl('', [Validators.required]),
    chart_height: new FormControl(300),
    info_yAxis: new FormControl('', [Validators.required])
  });

  public controls: any = null;
  public periods = Periods;
  public infoYAxis = InfoYAxis;
  public yAxisLabel = YAxisLabel;

  public xAxis: any = [];
  public yAxis: any = [];
  public chart: any = {};

  public instrumentId: any = null;
  public instruments: any = [];
  public instrumentsSettings = MultiSelectDefault.Instruments;
  public yAxisSettings: any = MultiSelectDefault.Single;

  public data: any = null;
  public yAxisDescription: any = '';

  public chartSeries: any = [];
  public chartLegends: any = [];

  public chartLegendsTop: number = 50;
  public chartLegendsBottom: number = 0;

  public min: number = null;
  public max: number = null;

  public messageReturn: any = [{ text: '', status: false }];

  public func = fn;

  public countSecurityLevel: number = null;

  constructor(
    private activatedRoute: ActivatedRoute,
    private chartServiceApi: ChartServiceApi,
    private fileLoaderService: FileLoaderService,
    private instrumentsServiceApi: InstrumentsServiceApi,
    private router: Router
  ) {}

  /**
   * Método de inicialização do componente.
   * Configura os controles e carrega a lista de instrumentos.
   */
  ngOnInit(): void {
    this.controls = this.formChart.controls;
    this.instrumentsSettings.singleSelection = true;
    this.getInstruments();
  }

  /**
   * Obtém a lista de instrumentos com base na estrutura e tipo de instrumento especificados.
   * Define o instrumento selecionado nos controles do formulário.
   */
  getInstruments() {
    this.instrumentId = this.activatedRoute.snapshot.params.instrumentId;

    let params = { StructureId: this.activatedRoute.snapshot.queryParams.structure, Type: this.activatedRoute.snapshot.queryParams.typeInstrument };
    this.instrumentsServiceApi.getInstrumentsList(params).subscribe((resp) => {
      let dados: any = resp;

      if (dados.status == 200) {
        dados = dados.body === undefined ? dados : dados.body;
        this.instruments = dados;
        let instrumentInfo = fn.findIndexInArrayofObject(this.instruments, 'id', this.activatedRoute.snapshot.params.instrumentId, 'identifier', true);
        this.controls['instrument'].setValue([instrumentInfo]);
      }
    });
  }

  //Obtém os dados do gráfico de percolação com base nos instrumentos selecionados.
  getChart() {
    this.resetConfigurations();
    this.messageReturn.text = '';
    this.messageReturn.status = false;

    let instrumentId = this.activatedRoute.snapshot.params.instrumentId;
    let params = {};

    params = this.controls['instrument'].value.map((object) => object.id);

    if (this.controls['start_date'].value != '') {
      params['StartDate'] = moment(this.controls['start_date'].value).format('YYYY-MM-DD');
    }

    if (this.controls['end_date'].value != '') {
      params['EndDate'] = moment(this.controls['end_date'].value).format('YYYY-MM-DD');
    }

    this.chartServiceApi.getChartSettlementGauge(instrumentId, params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      if (dados) {
        this.formatData(dados);
      } else {
        this.messageReturn.text = MessageInputInvalid.NoChart;
        this.messageReturn.status = true;

        setTimeout(() => {
          this.messageReturn.status = false;
        }, 4000);
      }
    });
  }

  //Limpa o formulário das configurações do gráfico.
  resetConfigurations() {
    this.xAxis = [];
    this.yAxis = [];
    this.chart = {};

    this.yAxisDescription = '';

    this.chartSeries = [];
    this.chartLegends = [];

    this.min = null;
    this.max = null;

    if (this.data !== null) {
      this.constructChart(this.data);
    }
  }

  //Constrói o gráfico a partir dos dados fornecidos.
  constructChart(data) {
    this.controls['chart_height'].setValue(300);
    this.constructXAxis(data);
  }

  //Constrói o eixo X do gráfico com base nas datas dos dados fornecidos.
  constructXAxis(data) {
    let dates = [];

    // // Instrumento
    // dates = data.instrument.map((item) => moment(item.date).format('DD/MM/YYYY'));
    // this.xAxis.push(...dates);

    //Aneis (medição)
    for (const key in data.readings) {
      if (data.readings.hasOwnProperty(key)) {
        dates = data.readings[key].map((item) => moment(item.date).format('DD/MM/YYYY'));
        this.xAxis.push(...dates);
      }
    }
    //Materiais estáticos
    //Nao tem valor de data

    //Níveis de segurança
    dates = Object.values(data['security-levels']).flatMap((nivelAlerta) =>
      Object.values(nivelAlerta).flatMap((item) => item.map((obj) => moment(obj.date).format('DD/MM/YYYY')))
    );

    this.xAxis.push(...dates);

    const orderedDates = this.xAxis
      .map((data) => {
        const [dia, mes, ano] = data.split('/').map(Number);
        return new Date(ano, mes - 1, dia);
      })
      .sort((a, b) => a - b)
      .map((data) => data.toLocaleDateString('pt-BR'));

    this.xAxis = orderedDates;
    this.xAxis = this.uniqueArray(this.xAxis);

    this.constructSeries(data);
  }

  //Constrói as séries de dados do gráfico.
  constructSeries(data) {
    const datesObject = {};

    for (const date of this.xAxis) {
      datesObject[date] = null;
    }

    let series = {};
    this.chartSeries = [];
    this.chartLegends = [];

    this.controls['chart_height'].setValue(Math.floor(this.chartLegendsTop) + this.controls['chart_height'].value);
    this.yAxisDescription = fn.findIndexInArrayofObject(this.infoYAxis, 'id', this.controls['info_yAxis'].value, 'name', true);

    const readings = Object.keys(data.readings);
    readings.forEach((reading) => {
      series[reading] = { ...datesObject };
    });

    data['lithotypes'].forEach((lithotype) => {
      series[lithotype.lithotype] = { ...datesObject };
    });

    const itemSecurityLevels = Object.keys(data['security-levels']);
    itemSecurityLevels.forEach((itemSecurityLevel) => {
      const securityLevels = Object.keys(data['security-levels'][itemSecurityLevel]);
      securityLevels.forEach((securityLevel) => {
        series[itemSecurityLevel + ' ' + securityLevel] = { ...datesObject };
      });
    });

    let property = ['', 'absolute_settlement', 'relative_settlement', 'quota'];

    for (const key in data.readings) {
      data.readings[key].forEach((element) => {
        let date = moment(element.date).format('DD/MM/YYYY');

        const id = this.controls['info_yAxis'].value;
        const value = element[property[id]];

        series[key][date] = series.hasOwnProperty(key) && series[key].hasOwnProperty(date) ? (typeof value == 'string' ? parseFloat(value) : value) : null;
      });

      const itemSeries = {
        name: key,
        type: 'line',
        data: Object.values(series[key]),
        connectNulls: true
      };
      this.chartSeries.push(itemSeries);
      this.chartLegends.push(key);
      this.defineMinMax(itemSeries.data);
    }

    data['lithotypes'].forEach((lithotype) => {
      for (const key in series[lithotype.lithotype]) {
        series[lithotype.lithotype][key] = lithotype.quota;
      }
      const itemSeries = {
        name: lithotype.lithotype,
        type: 'line',
        data: Object.values(series[lithotype.lithotype]),
        connectNulls: true,
        lineStyle: {
          type: 'dashed' // Define a linha como tracejada
        }
      };
      this.chartLegends.push(lithotype.lithotype);
      this.chartSeries.push(itemSeries);
      this.defineMinMax(itemSeries.data);
    });

    const colors = {
      'Nível de Atenção': { color: this.generateColors(this.countSecurityLevel, ['#ffd3af', '#fd7e14']), count: 0 },
      'Nível de Alerta': { color: this.generateColors(this.countSecurityLevel, ['#ffeaaa', '#ffc107']), count: 0 },
      'Nível de Emergência': { color: this.generateColors(this.countSecurityLevel, ['#d59399', '#dc3545']), count: 0 }
    };

    for (const itemSecurityLevel in data['security-levels']) {
      for (const securityLevels in data['security-levels'][itemSecurityLevel]) {
        for (const key in data['security-levels'][itemSecurityLevel][securityLevels]) {
          let date = moment(data['security-levels'][itemSecurityLevel][securityLevels][key].date).format('DD/MM/YYYY');
          const value = data['security-levels'][itemSecurityLevel][securityLevels][key].value;
          series[itemSecurityLevel + ' ' + securityLevels][date] =
            series.hasOwnProperty(itemSecurityLevel + ' ' + securityLevels) && series[itemSecurityLevel + ' ' + securityLevels].hasOwnProperty(date)
              ? typeof value == 'string'
                ? parseFloat(value)
                : value
              : null;
        }

        let idx = colors[securityLevels].count;

        const itemSeries = {
          name: itemSecurityLevel + ' ' + securityLevels,
          type: 'line',
          data: Object.values(series[itemSecurityLevel + ' ' + securityLevels]),
          connectNulls: true,
          itemStyle: {
            color: colors[securityLevels].color[idx]
          },
          areaStyle: { opacity: 0.05 }
        };
        this.chartLegends.push(itemSecurityLevel + ' ' + securityLevels);
        this.chartSeries.push(itemSeries);
        this.defineMinMax(itemSeries.data);

        colors[securityLevels].count = colors[securityLevels].count + 1;
      }
    }
    this.constructYAxis();
  }

  //Prepara os dados para os Eixos Y
  constructYAxis() {
    this.yAxis = [];

    let itemYAxis = {
      name: this.yAxisDescription.name,
      type: 'value',
      axisLine: {
        show: true
      },
      nameRotate: 90,
      nameLocation: 'center',
      nameGap: 75,
      nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
      alignTicks: true,
      axisLabel: {
        formatter: function (value, index) {
          return value.toFixed(2);
        }
      },
      show: true,
      interval: Math.ceil((this.max - this.min) / 25),
      min: this.min,
      max: this.max,
      inverse: true // do maior para menor.
    };
    this.yAxis.push(itemYAxis);

    this.generateChart();
  }

  /**
   * Define os valores mínimos e máximos dos dados da série para o eixo Y.
   * @param {any} array - Dados da série.
   * @param {number} index - Índice do eixo Y.
   */
  defineMinMax(array: any, index = null) {
    array = array.filter((item) => item !== null);

    array = array.map((item) => {
      if (item != null) {
        return typeof item == 'number' ? item : item.value;
      }
    });

    const min = Math.min(...array);
    const max = Math.max(...array);
    let previous = min - (min % 10);
    let next = max + (10 - (max % 10));

    this.min = this.min == null ? previous : this.min;
    this.min = Math.min(this.min, previous);
    previous = this.min;

    this.max = this.max == null ? next : this.max;
    this.max = Math.max(this.max, next);
    next = this.max;
  }

  //Define a altura do gráfico.
  setHeight(height: string): void {
    this.controls['chart_height'].setValue(parseInt(height));
    this.generateChart();
  }

  /**
   * Remove elementos repetidos de um array.
   * @param {any[]} array - Array de elementos.
   * @returns {any[]} - Array com elementos únicos.
   */
  uniqueArray(array) {
    const uniqueArray = [];
    const seeDates = {};

    for (const date of array) {
      if (!seeDates[date]) {
        uniqueArray.push(date);
        seeDates[date] = true;
      }
    }

    return uniqueArray;
  }

  /**
   * Carrega o conteúdo de um arquivo a partir de um caminho específico.
   * @param {string} path - Caminho para o arquivo.
   * @param {string} fileName - Nome do arquivo.
   * @param {number} index - Índice do marcador.
   */
  loadFileContent(path: string, fileName: string, index = 0) {
    this.fileLoaderService.loadFile(path, fileName).subscribe(
      (data) => {
        // this.markers[index].text = data;
      },
      (error) => {
        console.error('Erro ao carregar o arquivo:', error);
      }
    );
  }

  /**
   * Converte o arquivo SVG para texto e substitui suas cores.
   * @param {string} svg - Conteúdo SVG.
   * @param {string} color - Cor para substituição.
   * @returns {string} - SVG convertido para base64.
   */
  getSvgWithReplacedValue(svg, color = '#000000') {
    svg = this.replaceMultipleOccurrences(svg, ['rgb(0,0,0)', 'rgb(101,101,101)'], [color, color]);
    const svgBase64 = btoa(svg);
    return `data:image/svg+xml;base64,${svgBase64}`;
  }

  /**
   * Substitui todas as ocorrências de uma string por outra em um texto.
   * @param {string} text - Texto original.
   * @param {string[]} oldValues - Array de valores a serem substituídos.
   * @param {string[]} newValues - Array de novos valores para substituição.
   * @returns {string} - Texto com as substituições aplicadas.
   */
  replaceMultipleOccurrences(text, oldValues, newValues) {
    if (oldValues.length !== newValues.length) {
      throw new Error('Os arrays devem ter o mesmo comprimento.');
    }

    let newText = text;
    for (let i = 0; i < oldValues.length; i++) {
      const oldValue = oldValues[i];
      const newValue = newValues[i];
      newText = newText.split(oldValue).join(newValue);
    }

    return newText;
  }

  /**
   * Gera uma cor hexadecimal aleatória.
   * @returns {string} - Cor hexadecimal.
   */
  randomHexColor() {
    const randomColorComponent = () => {
      const component = Math.floor(Math.random() * 256); //Valor aleatorio entre 0  e 255
      return component.toString(16).padStart(2, '0'); //Converte para hexadecimal e completa com zero se necessario
    };

    const r = randomColorComponent();
    const g = randomColorComponent();
    const b = randomColorComponent();

    return `#${r}${g}${b}`;
  }

  changeYAxis() {
    if (this.data !== null) {
      this.resetConfigurations();
    }
  }

  //Formata os dados recebidos e os prepara para a construção do gráfico.
  formatData($dados) {
    let dados: any = {
      readings: [],
      lithotypes: [],
      'security-levels': []
    };

    this.countSecurityLevel = $dados.readings.length;

    $dados.readings.forEach((measure, index) => {
      dados['readings'][measure.measurement_identifier] = [];

      dados['security-levels'][measure.measurement_identifier] = {
        'Nível de Atenção': [],
        'Nível de Alerta': [],
        'Nível de Emergência': []
      };

      measure.data.forEach((reading, index) => {
        dados['readings'][measure.measurement_identifier].push({
          identifier: measure.measurement_identifier,
          absolute_settlement: reading.absolute_settlement,
          relative_settlement: reading.relative_settlement,
          quota: reading.quota,
          date: reading.date
        });

        dados['security-levels'][measure.measurement_identifier]['Nível de Atenção'].push({
          value: reading.attention_level,
          color: '#ffc107',
          date: reading.date
        });

        dados['security-levels'][measure.measurement_identifier]['Nível de Alerta'].push({
          value: reading.alert_level,
          color: '#fd7e14',
          date: reading.date
        });

        dados['security-levels'][measure.measurement_identifier]['Nível de Emergência'].push({
          value: reading.emergency_level,
          color: '#dc3545',
          date: reading.date
        });
      });
    });

    dados.lithotypes = $dados.lithotypes;
    this.data = dados;
    this.constructChart(dados);
  }

  /**
   * Gera uma paleta de cores interpolada com base em um comprimento específico.
   * @param {number} lenght - O número de cores a serem geradas.
   * @param {string[]} customPalette - (Opcional) Paleta de cores personalizada a ser usada em vez da paleta padrão.
   * @returns {string[]} - Array de cores geradas.
   */
  generateColors(lenght, customPalette) {
    // Use a paleta de cores personalizada se fornecida, caso contrário, use a paleta de cores padrão
    const paletaArcoIris = customPalette || [
      '#e83e8c', // Rosa
      '#fd7e14', // Laranja
      '#ffc107', // Amarelo
      '#28a745', // Verde
      '#007bff', // Azul
      '#4B0082', // Índigo
      '#8B00FF' // Violeta
    ];

    const coresIntermediarias = [];

    if (paletaArcoIris.length > 1) {
      const intervalo = 1 / (paletaArcoIris.length - 1);
      for (let i = 0; i < lenght; i++) {
        const index1 = Math.floor((i / lenght) * (paletaArcoIris.length - 1));
        const index2 = Math.min(index1 + 1, paletaArcoIris.length - 1);
        const percentual = ((i / lenght) * (paletaArcoIris.length - 1) - index1) / intervalo;
        const corIntermediaria = d3.interpolate(paletaArcoIris[index1], paletaArcoIris[index2])(percentual);
        coresIntermediarias.push(corIntermediaria);
      }
    }

    return coresIntermediarias;
  }

  //Gera e configura o gráfico usando as opções definidas.
  generateChart() {
    this.chart['options'] = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            formatter: function (params) {
              if (typeof params.value === 'number') {
                return params.value.toFixed(2); //Formata o valor para duas casas decimais
              } else {
                return params.value;
              }
            }
          }
        }
      },
      legend: {
        data: this.chartLegends,
        icon: 'rect',
        left: 'center',
        top: 'bottom'
      },
      grid: {
        containLabel: true,
        top: this.chartLegendsTop,
        left: 50,
        right: 50,
        height: this.controls['chart_height'].value
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: { title: 'Download' }
        }
      },
      xAxis: {
        type: 'category',
        interval: 0.1,
        boundaryGap: false,
        data: this.xAxis,
        axisLabel: {
          interval: Math.floor(this.xAxis.length / 35), // Define o intervalo para exibir todos os valores do eixo X
          rotate: 60
        }
      },
      yAxis: this.yAxis,
      series: this.chartSeries
    };
  }

  /**
   * Altera o instrumento selecionado e redefine as configurações do gráfico.
   * @param {any} $event - Evento contendo o instrumento selecionado.
   * @param {string} action - Ação a ser tomada, padrão é 'select'.
   */
  changeInstrument($event, action: string = 'select') {
    if (action === 'select') {
      this.instrumentId = $event.id;
    }
  }

  //Navega de volta para a tela de instrumentos.
  goBack() {
    this.router.navigate(['/instruments']);
  }
}
