<div class="list-content" *ngIf="resultSimulation">
  <!-- Resultado da simulação -->
  <div class="row mt-2">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between">
          <span>Resultados da simulação: {{ resultSimulation.name }}</span>
          <span
            >Criado por: {{ resultSimulation.created_by.first_name }}
            {{ resultSimulation.created_by.surname }}</span
          >
        </div>
      </div>
    </div>
  </div>

  <!-- Tabela -->
  <div class="row mt-2">
    <div class="col-md-12">
      <table
        class="table table-bordered table-striped table-hover align-middle"
      >
        <thead>
          <tr>
            <th scope="col" colspan="4" class="fw-semibold">
              Resumo da simulação - Seção:
              {{ resultSimulation.sections[0].section_name }}
              {{ resultSimulation.setion_review }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="w-25 text-start fw-semibold">Condição/Condições</td>
            <td class="w-25 text-start" colspan="3">
              {{ this.resumeSimulationParams?.conditions }}
            </td>
          </tr>
          <tr>
            <td class="w-25 text-start fw-semibold">Freática/Piezometrica</td>
            <td class="w-25 text-start">
              {{ this.resumeSimulationParams?.phreaticPiezometric }}
            </td>
            <td class="w-25 text-start fw-semibold">FS Alvo</td>
            <td class="w-25 text-start">
              {{ this.resumeSimulationParams?.safetyFactorTarget }}
            </td>
          </tr>
          <ng-container
            *ngFor="let resumePair of resumeSimulationPairs; let i = index"
          >
            <tr>
              <td class="text-start fw-semibold w-25">
                {{ resumePair[0]?.label }}
              </td>
              <td class="text-start w-25">{{ resumePair[0]?.value }}</td>
              <td class="text-start fw-semibold">{{ resumePair[1]?.label }}</td>
              <td class="text-start w-25">
                {{ resumePair[1]?.value }}
              </td>
            </tr>
          </ng-container>
          <tr>
            <td colspan="2" class="header-title">
              Tipo de superfície: Circular
            </td>
            <td colspan="2" class="header-title">
              Tipo de superfície: Não Circular
            </td>
          </tr>
          <tr>
            <td class="w-25 text-start fw-semibold">Método de cálculo:</td>
            <td class="w-25 text-start">
              {{
                resumeSimulationCircular?.calculationMethod.join(', ') ?? '-'
              }}
            </td>
            <td class="w-25 text-start fw-semibold">Método de cálculo:</td>
            <td class="w-25 text-start">
              {{
                resumeSimulationNonCircular?.calculationMethod.join(', ') ?? '-'
              }}
            </td>
          </tr>
          <tr>
            <td class="w-25 text-start fw-semibold">Método de busca:</td>
            <td class="w-25 text-start">
              {{ resumeSimulationCircular?.searchMethod ?? '-' }}
            </td>
            <td class="w-25 text-start fw-semibold">Método de busca:</td>
            <td class="w-25 text-start">
              {{ resumeSimulationNonCircular?.searchMethod ?? '-' }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Dados da Seção -->
  <div class="row mt-2 pa-2">
    <div class="col-md-12">
      <ng-container
        *ngFor="let resumeSection of resumeSimulationSection; let sec = index"
      >
        <hr class="mt-4" *ngIf="sec > 0" />

        <table
          class="table table-bordered table-striped table-hover align-middle"
        >
          <thead>
            <tr>
              <th scope="col" colspan="4">
                <div>
                  <strong>Dados da Seção: </strong>
                  {{ resumeSection.sectionName }}
                </div>

                <!-- Revisão da Seção -->
                <div class="mt-1" *ngIf="resumeSection.section_review_index">
                  <strong>
                    Revisão Seção: {{ resumeSection.section_review_index }}
                    <span *ngIf="resumeSection.section_review_start_date">
                      ({{
                        mmt(resumeSection.section_review_start_date).format(
                          'DD/MM/YYYY HH:mm:ss'
                        )
                      }})
                    </span>
                  </strong>
                </div>

                <!-- Etapa de Obra -->
                <div class="mt-1">
                  <ng-container
                    *ngIf="resumeSection.constructionStage; else noStage"
                  >
                    <strong class="text-primary fw-semibold">
                      Etapa de Obra: {{ resumeSection.constructionStage }}
                      <span *ngIf="resumeSection.constructionStageId">
                        (ID: {{ resumeSection.constructionStageId }})
                      </span>
                    </strong>
                  </ng-container>
                  <ng-template #noStage>
                    <small class="text-muted fst-italic">
                      Nenhuma etapa de obra cadastrada para esta seção.
                    </small>
                  </ng-template>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <ng-container
              *ngFor="
                let resumeSectionPair of resumeSection.fields;
                let i = index
              "
            >
              <tr>
                <td class="w-25 text-start fw-semibold">
                  {{ resumeSectionPair[0]?.label }}
                </td>
                <td class="w-25 text-start">
                  {{ resumeSectionPair[0]?.value }}
                </td>
                <td class="w-25 text-start fw-semibold">
                  {{ resumeSectionPair[1]?.label }}
                </td>
                <td class="w-25 text-start">
                  {{ resumeSectionPair[1]?.value }}
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>

        <!-- Intrumentos -->
        <table
          class="table table-bordered table-striped table-hover align-middle"
          *ngIf="
            resumeSimulationSectionInstruments[resumeSection.sectionId]
              ?.instruments?.length
          "
        >
          <thead>
            <tr>
              <th scope="col" colspan="5">
                Leituras consideradas na simulação
              </th>
            </tr>
            <tr>
              <th scope="col" style="width: 20%">Instrumento</th>
              <th scope="col" style="width: 30%">Tipo</th>
              <th scope="col" style="width: 20%">Ponto de medição</th>
              <th scope="col" style="width: 20%">Cota</th>
              <th scope="col" style="width: 10%">Seco?</th>
            </tr>
          </thead>
          <tbody>
            <ng-container
              *ngFor="
                let resumeInstrument of resumeSimulationSectionInstruments[
                  resumeSection.sectionId
                ].instruments;
                let ins = index
              "
            >
              <tr>
                <td class="text-center">
                  {{ resumeInstrument.instrumentIdentifier }}
                </td>
                <td class="text-center">
                  {{ resumeInstrument.instrumentType }}
                </td>
                <td class="text-center">
                  {{ resumeInstrument.measureIdentifier }}
                </td>
                <td class="text-center">
                  {{ resumeInstrument.quota }}
                </td>
                <td class="text-center">
                  {{ resumeInstrument.dry }}
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>

        <!-- Mensagem de alerta -->
        <div
          class="alert alert-warning"
          role="alert"
          *ngIf="
            !resumeSimulationSectionInstruments[resumeSection.sectionId]
              .instruments
          "
        >
          Não há instrumentos do tipo INA, PZ e PZE associados à essa seção.
        </div>

        <!-- Warnings -->
        <ng-container
          *ngFor="let resumeWarning of resumeSection.warnings; let war = index"
        >
          <div class="alert alert-warning" role="alert">
            {{ resumeWarning.created_date }} - {{ resumeWarning.message }}
            <br />
          </div>
        </ng-container>

        <!-- Botão para baixar todas as análises -->
        <div
          class="row mb-2"
          *ngIf="resultSimulationSection[resumeSection.sectionId].results"
        >
          <div class="col-md-12 d-flex justify-content-center">
            <app-button
              [class]="'btn-logisoil-blue me-3'"
              [label]="'Baixar todas as análises'"
              [icon]="'fa fa-file-archive-o'"
              (click)="
                downloadFile(
                  'ZIPALL',
                  resultSimulation.zip_file_download_url,
                  resultSimulation.file_zip_all
                )
              "
            ></app-button>
          </div>
        </div>

        <!-- Resultados -->
        <table
          class="table table-bordered table-striped table-hover align-middle"
          *ngIf="resultSimulationSection[resumeSection.sectionId].results"
        >
          <ng-container
            *ngFor="
              let resultSection of resultSimulationSection[
                resumeSection.sectionId
              ].results;
              let res = index
            "
          >
            <thead>
              <tr>
                <th scope="col" class="text-start d-flex flex-column">
                  <div>
                    FS encontrado:
                    <span style="font-size: 1.25em">{{
                      resultSection.fs
                    }}</span>
                  </div>
                  <div>
                    Tipo de Superfície:
                    {{ resultSection.surfaceType }} - Tipo de Condição:
                    {{ resultSection.condition }} - Método de cálculo:
                    {{ resultSection.calculationMethod }}
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <app-button
                    [class]="'btn-logisoil-blue me-3'"
                    [label]="'Download DXF'"
                    [icon]="'fa fa-file'"
                    (click)="downloadFile('DXF', resultSection.dxfFile)"
                  ></app-button>
                  <app-button
                    [class]="'btn-logisoil-blue me-3'"
                    [label]="'Download PNG'"
                    [icon]="'fa fa-file-image-o'"
                    (click)="downloadFile('PNG', resultSection.pngFile)"
                  ></app-button>
                  <app-button
                    [class]="'btn-logisoil-blue me-3'"
                    [label]="'Download ZIP'"
                    [icon]="'fa fa-file-archive-o'"
                    (click)="downloadFile('ZIP', resultSection.zipFile)"
                  ></app-button>
                </td>
              </tr>
              <tr>
                <td class="text-center">
                  <app-dxf-viewer
                    [fileDxf]="resultSection.dxfFile.fileView"
                    [idCanvas]="'canvas' + resultSection.resultsId"
                  ></app-dxf-viewer>
                </td>
              </tr>
            </tbody>
          </ng-container>
        </table>
        <div
          class="alert alert-warning"
          role="alert"
          *ngIf="!resultSimulationSection[resumeSection.sectionId].results"
        >
          <div *ngIf="resultSimulation.events?.length; else noResultsMessage">
            <p><strong>Razões para ausência de resultados:</strong></p>
            <ul>
              <li *ngFor="let eventItem of resultSimulation.events">
                {{ mmt(eventItem.created_date).format('DD/MM/YYYY HH:mm:ss') }}
                - {{ eventItem.event }}
              </li>
            </ul>
          </div>
          <ng-template #noResultsMessage>
            Não há resultados para esta seção.
          </ng-template>
        </div>
      </ng-container>
    </div>
  </div>

  <!-- Voltar -->
  <div class="col-md-12 d-flex justify-content-end mb-3">
    <app-button
      [class]="'btn-logisoil-blue'"
      [icon]="'fa fa-arrow-left'"
      [label]="'Voltar à tela principal'"
      [routerLink]="['/stability/simulations']"
    ></app-button>
  </div>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
