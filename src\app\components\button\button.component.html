<button
  [ngClass]="[class]"
  [type]="type ? 'button' : 'submit'"
  (click)="onClick()"
  [disabled]="disabled"
  class="btn"
  [id]="id"
  [title]="title"
>
  <i
    [ngClass]="icon"
    *ngIf="icon && !customBtn && !svg"
    [style.color]="iconColor"
  ></i>
  <span *ngIf="customBtn && !svg" class="btn-label">
    <i [ngClass]="icon" *ngIf="icon" [style.color]="iconColor"></i>
  </span>
  <img src="/assets/btn/{{ icon }}" *ngIf="svg" />
  {{ label }}
</button>
