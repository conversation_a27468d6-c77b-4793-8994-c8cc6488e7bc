////////// Paleta de cores principal Walm //////////
$logisoil-green: #6cc24a;
$logisoil-blue: #1d428a;
$logisoil-green-1: #006d68;

////////// Paleta de cores secundária Walm //////////
$logisoilsec-1: #949a90; // PANTONE: 7538 C
$logisoilsec-2: #addc91; // PANTONE: 358 C
$logisoilsec-3: #7fa9ae; // PANTONE: 5493 C
$logisoilsec-4: #89abe3; // PANTONE: 7451
$logisoilsec-5: #c6cbc4; // PANTONE: 5666 C
$logisoilsec-6: #002f6c; // PANTONE: 294 C
$logisoilsec-7: #666666;
$logisoilsec-8: #b3b3b3;

////////// COLOR SYSTEM //////////

$blue: #5e50f9;
$indigo: #6610f2;
$purple: #6a008a;
$pink: #e91e63;
$red: #f96868;
$orange: #f2a654;
$yellow: #f6e84e;
$green: #46c35f;
$teal: #58d8a3;
$cyan: #57c7d4;
$black: #000;
$white: #ffffff;
$white-smoke: #f2f7f8;
$violet: #41478a;
$darkslategray: #2e383e;
$dodger-blue: #3498db;

$colors: (
  blue: $blue,
  indigo: $indigo,
  purple: $purple,
  pink: $pink,
  red: $red,
  orange: $orange,
  yellow: $yellow,
  green: $green,
  teal: $teal,
  cyan: $cyan,
  white: $white,
  gray: #434a54,
  gray-light: #aab2bd,
  gray-lighter: #e8eff4,
  gray-lightest: #e6e9ed,
  gray-dark: #0f1531,
  black: #000000
);

$theme-colors: (
  primary: #1f3bb3,
  secondary: #f1f1f1,
  success: #34b1aa,
  info: #52cdff,
  warning: #ffaf00,
  danger: #f95f53,
  light: #fbfbfb,
  dark: #1e283d
);

$primary: #1f3bb3;
$secondary: #f1f1f1;
$success: #34b1aa;
$info: #52cdff;
$warning: #ffaf00;
$danger: #f95f53;
$light: #fbfbfb;
$dark: #1e283d;
$gray: #434a54;
$gray-light: #aab2bd;
$gray-lighter: #e8eff4;
$gray-lightest: #e6e9ed;
$gray-dark: #0f1531;
$black: #000000;
$twitter: #2caae1;
$facebook: #3b579d;
$google: #dc4a38;
$linkedin: #0177b5;
$pinterest: #cc2127;
$youtube: #e52d27;
$github: #333333;
$behance: #1769ff;
$dribbble: #ea4c89;
$reddit: #ff4500;

$theme-gradient-colors: (
  primary: linear-gradient(230deg, #759bff, #843cf6),
  secondary: linear-gradient(to right, #e7ebf0, #868e96),
  success: linear-gradient(45deg, #7bffce, #30c93e),
  info: linear-gradient(to bottom, #0e4cfd, #6a8eff),
  warning: linear-gradient(135deg, #ffc480, #ff763b),
  danger: linear-gradient(316deg, #fc5286, #fbaaa2),
  light: linear-gradient(to right, #cfd9df 0%, #e2ebf0 100%),
  dark: linear-gradient(to right, #7d7979 0%, #000000 100%)
);

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
);

////////// COLOR SYSTEM //////////

:root {
  --logisoil-color-primary: #2bb673;
}

.bg {
  .gradient-radial {
    background: rgb(106, 183, 116);
    background: radial-gradient(
      circle,
      rgba(106, 183, 116, 1) 0%,
      rgba(152, 194, 86, 1) 50%,
      rgba(106, 183, 116, 1) 100%
    );
  }
}

.opacity {
  opacity: 0.5;
  -moz-opacity: 0.5;
}

.cursor-pointer {
  cursor: pointer;
}
