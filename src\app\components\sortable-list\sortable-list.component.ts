import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { moveItemInArray, CdkDragDrop } from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-sortable-list',
  templateUrl: './sortable-list.component.html',
  styleUrls: ['./sortable-list.component.scss']
})
export class SortableListComponent implements OnInit {
  @Input() public dataList: any = [];
  @Input() public checkbox: boolean = false;
  @Input() public orderField: string = '';
  @Input() public textField: string = '';
  @Input() public filter: boolean = false;
  @Input() public selected: number = 0;
  @Input() public edit: any = null;
  @Input() public dragDrop: boolean = false;

  @Output() public sendNumberSelected = new EventEmitter();
  @Output() public sendEditItem = new EventEmitter();

  public searchItem: string = '';

  constructor() {}

  ngOnInit(): void {}

  onDrop(event: CdkDragDrop<string[]>, key: string = '', increment = 1) {
    moveItemInArray(this.dataList, event.previousIndex, event.currentIndex);
    this.dataList.forEach((item, index) => {
      item[key] = index + increment;
    });
  }

  checkedItem(itemCheckbox: any, i: number): void {
    itemCheckbox.checked ? this.selected++ : this.selected--;
    this.dataList[i].selected = itemCheckbox.checked;
    this.sendNumberSelected.emit(this.selected);
  }

  editItem(item: any) {
    if (!this.dragDrop) {
      this.sendEditItem.emit(item);
    }
  }
}
