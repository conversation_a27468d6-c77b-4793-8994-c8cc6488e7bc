import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { GalleryImagesComponent } from './gallery-images.component';

import { AppGuard } from '../../guards/app.guard';

const routes: Routes = [{ path: '', component: GalleryImagesComponent, canActivate: [AppGuard] }];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class GalleryImagesRoutingModule {}
