import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';

import { Router, ActivatedRoute } from '@angular/router';

import { MessageCadastro } from 'src/app/constants/message.constants';

import { StaticMaterialsService as StaticMaterialsServiceApi } from 'src/app/services/api/staticMaterials.service';

import { GeneralTabComponent } from '../tabs/general-tab/general-tab.component';
import { ReviewTabComponent } from '../tabs/review-tab/review-tab.component';

import { FormService } from 'src/app/services/form.service';
import { UserService } from 'src/app/services/user.service';
import { StaticMaterialsService } from 'src/app/services/static-materials.service';

import * as _ from 'lodash';

import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-register-static-materials',
  templateUrl: './register-static-materials.component.html',
  styleUrls: ['./register-static-materials.component.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: [StaticMaterialsService]
})
export class RegisterStaticMaterialsComponent implements OnInit {
  @ViewChild(GeneralTabComponent) generalTab: GeneralTabComponent;
  @ViewChild(ReviewTabComponent) reviewTab: ReviewTabComponent;

  public staticMaterialId: any = null;

  public generalTabData: any = {};
  public reviewTabData: any = {};

  public generalTabConfig: any = { styleColor: false, active: true };
  public reviewTabConfig: any = { styleColor: false, active: false };

  public edit: boolean = false;
  public view: boolean = false;

  public profile: any = null;
  public permissaoUsuario: any = null;

  public crtlSaveStaticMaterials: string = '';

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public componentDrained: any = null;
  public componentUndrained: any = null;
  public componentPseudo: any = null;

  public formDrainedlCoditionValid: any = null;
  public formUndrainedConditionValid: any = null;
  public formPseudoConditionValid: any = null;

  public constitutiveModelElement: any = {
    drained: null,
    undrained: null,
    pseudo: null
  };

  public constitutiveModelDrained: any = null;
  public constitutiveModelUndrained: any = null;
  public constitutiveModelPseudo: any = null;

  public formDrainedlValid: any = null;
  public formUndrainedValid: any = null;
  public formPseudoValid: any = null;

  public staticMaterialsRequest: any = {
    structure: {
      id: null
    },
    name: null,
    drained_static_material_value: null,
    undrained_static_material_value: null,
    pseudo_static_material_value: null
  };

  public constitutiveModelRequest: any = {
    color: null,
    natural_specific_weight: null,
    saturated_specific_weight: null,
    constitutive_model: null,
    cohesion: null,
    friction_angle: null,
    tensile_strength: null,
    cohesion_type: null,
    cohesion_variation: null,
    maximum: null,
    datum: null,
    allow_sliding_along_boundary: null,
    ucs_intact: null,
    m: null,
    s: null,
    strength_definition: null,
    gsi: null,
    mi: null,
    d: null,
    mb: null,
    a: null,
    resistance_ratio: null,
    maximum_shear_strength: null,
    minimum_shear_strength: null,
    stress_history_type: null,
    stress_history_method: null,
    constant: null,
    hu: null,
    custom_hu_value: null,
    use_drained_resistance_over_water_surface: false,
    water_surface: null,
    point_values: []
  };

  constructor(
    private activatedRoute: ActivatedRoute,
    public formService: FormService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private staticMaterialsServiceApi: StaticMaterialsServiceApi,
    private userService: UserService,
    private staticMaterialsService: StaticMaterialsService
  ) {}

  /**
   * Método chamado na inicialização do componente. Configura o formulário,
   * carrega o perfil do usuário, permissões e, se necessário, os dados de um material estático existente.
   */
  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);
    this.permissaoUsuario = this.userService.permissaoUsuario;

    if (this.activatedRoute.snapshot.params.staticMaterialId) {
      this.edit = true;
      this.staticMaterialId = this.activatedRoute.snapshot.params.staticMaterialId;
      this.getStaticMaterial(this.activatedRoute.snapshot.params.staticMaterialId);
      if (this.activatedRoute.snapshot.url && this.activatedRoute.snapshot.url[1] && this.activatedRoute.snapshot.url[1].path == 'view') {
        this.edit = false;
        this.view = true;
      }
    }
  }

  /**
   * Método que seleciona a aba do formulário com base na opção fornecida.
   * @param {any} option - A aba a ser selecionada (ex: 'drained', 'undrained', 'pseudo', 'general', 'review').
   */
  selectTab(option: any = '') {
    switch (option) {
      case 'general':
        this.generalTabConfig.active = true;
        this.reviewTabConfig.active = false;
        break;
      case 'review':
        this.generalTabConfig.active = false;
        this.reviewTabConfig.active = true;
        break;
      default:
        break;
    }
  }

  /**
   * Valida os dados do formulário de materiais estáticos utilizando o serviço `staticMaterialsService`.
   *
   * Se os dados forem válidos:
   * - Em modo de criação (`!edit`), chama `registerMaterials` para cadastrar o novo material.
   * - Em modo de edição (`edit`), anexa o `id` e o status de `active` e chama `editMaterial`.
   *
   * @returns void
   */

  validate() {
    const data = this.staticMaterialsService.validate(this.generalTab, 'tabConditions', 'formStaticMaterials', this.edit, this.view);

    if (data !== false) {
      if (!this.edit) {
        this.registerMaterials(data);
      } else {
        data['id'] = this.staticMaterialId;
        data['active'] = this.generalTab.formStaticMaterials.controls['active'].value;
        this.editMaterial(data);
      }
    }
  }

  /**
   * Registra um novo material estático usando os parâmetros fornecidos.
   * Exibe mensagens de sucesso ou erro conforme a resposta do serviço.
   * @param {any} params - Os parâmetros necessários para registrar o material estático.
   */
  registerMaterials(params: any) {
    this.ngxSpinnerService.show();
    this.messagesError = [];
    this.staticMaterialsServiceApi.postStaticMaterials(params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.SucessoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';
        setTimeout(() => {
          this.message.status = false;
          this.message.class = 'alert-success';
          this.router.navigate(['/materials']);
        }, 4000);
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  /**
   * Obtém os dados de um material estático específico pelo seu ID e preenche o formulário com esses dados.
   * @param {string} materialId - O ID do material estático a ser obtido.
   */
  getStaticMaterial(materialId: string = '') {
    this.ngxSpinnerService.show();

    this.staticMaterialsServiceApi.getStaticMaterialsById(materialId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.splitData(dados);

      this.ngxSpinnerService.hide();
    });
  }

  /**
   * Preenche o formulário com os dados fornecidos de um material estático existente.
   * Também carrega a lista de materiais e configura o formulário conforme o modo de visualização ou edição.
   * @param {any} $dados - Os dados do material estático a serem preenchidos no formulário.
   */
  splitData($dados) {
    this.generalTab.hierarchy.setClients([{ id: $dados.client.id, name: $dados.client.name }]);
    this.generalTab.hierarchy.setUnits([{ id: $dados.client_unit.id, name: $dados.client_unit.name }]);
    this.generalTab.hierarchy.setStructures([{ id: $dados.structure.id, name: $dados.structure.name }]);
    this.generalTab.formStaticMaterials.controls['name'].setValue($dados.name);
    this.generalTab.formStaticMaterials.controls['active'].setValue($dados.active);
    this.generalTab.tabConditions.getStaticMaterialsList($dados.structure);
    this.generalTab.tabConditions.dataDrained = $dados.drained_static_material_value;
    this.generalTab.tabConditions.dataUndrained = $dados.undrained_static_material_value;
    this.generalTab.tabConditions.dataPseudo = $dados.pseudo_static_static_material_value;

    this.reviewTab.formStaticMaterialsReview.controls['structures'].setValue([$dados.structure]);
    this.reviewTab.formStaticMaterialsReview.controls['name'].setValue($dados.name);
    this.reviewTab.formatData($dados);

    if (this.view) {
      this.generalTab.formStaticMaterials.disable();
    }
  }

  /**
   * Edita um material estático existente usando os parâmetros fornecidos.
   * Exibe mensagens de sucesso ou erro conforme a resposta do serviço.
   * @param {any} params - Os parâmetros necessários para editar o material estático.
   */
  editMaterial(params: any) {
    this.ngxSpinnerService.show();
    this.messagesError = [];

    this.staticMaterialsServiceApi.putStaticMaterials(this.staticMaterialId, params).subscribe(
      (resp) => {
        const dados: any = resp;
        this.message.text = MessageCadastro.EdicaoCadastro;
        this.message.status = true;
        this.message.class = 'alert-success';
        setTimeout(() => {
          this.message.status = false;
          this.router.navigate(['/materials']);
        }, 4000);
        this.ngxSpinnerService.hide();
      },
      (error) => {
        if (error.status >= 400) {
          this.messagesError = [];
          error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });
          setTimeout(() => {
            this.messagesError = [];
          }, 4000);
        }
        this.ngxSpinnerService.hide();
      }
    );
  }

  //Método que navega para a próxima aba do formulário, indo da aba 'general' para a aba 'review'.
  onNext(): void {
    if (this.generalTabConfig.active) {
      this.selectTab('review');
      this.crtlSaveStaticMaterials = 'review';
    }
  }

  //Método que navega para a próxima aba do formulário, indo da aba 'review' para a aba 'general'.
  onBack(): void {
    if (this.reviewTabConfig.active) {
      this.selectTab('general');
      this.crtlSaveStaticMaterials = '';
    }
  }
}
