import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { IDropdownSettings } from 'ng-multiselect-dropdown';

import { groupInstruments } from 'src/app/constants/instruments.constants';

@Component({
  selector: 'app-list-type-instruments',
  templateUrl: './list-type-instruments.component.html',
  styleUrls: ['./list-type-instruments.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ListTypeInstrumentsComponent implements OnInit {
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public data: any = [];
  @Input() public type: any;
  @Input() public item: number;
  @Input() public index: number;

  @Output() public sendInstruments = new EventEmitter();

  public formTypeInstruments: FormGroup = new FormGroup({
    typeInstrument: new FormControl('')
  });

  public groupInstruments: any = groupInstruments;

  public dropdownList = [];
  public selectedItems = [];

  public dropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'identifier',
    selectAllText: 'Selecionar todos',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  constructor() {}

  ngOnInit(): void {}

  itemEvent(item: any, action: string = 'select') {
    if (action === 'deselectAll') {
      item = this.data.instruments.map((instrument) => {
        let itemReturn = {};
        itemReturn['id'] = instrument.id;
        itemReturn['identifier'] = instrument.identifier;
        return itemReturn;
      });
    }

    this.sendInstruments.emit({
      instruments: item,
      action: action,
      type: this.type,
      item: this.item,
      index: this.index
    });
  }
}
