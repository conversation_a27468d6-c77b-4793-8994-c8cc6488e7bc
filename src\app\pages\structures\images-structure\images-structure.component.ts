import { Component, ElementRef, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { UserService } from 'src/app/services/user.service';
import { StructuresService as StructuresServiceApi } from 'src/app/services/api/structure.service';
import { ImagesService as ImagesServiceApi } from 'src/app/services/api/image.service';

import { MessageCadastro } from 'src/app/constants/message.constants';
import { Observable, forkJoin } from 'rxjs';

@Component({
  selector: 'app-images-structure',
  templateUrl: './images-structure.component.html',
  styleUrls: ['./images-structure.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ImagesStructureComponent implements OnInit {
  @ViewChild('appImages') appImages: ElementRef;

  public message: any = [{ text: '', status: false, class: 'alert-success' }];
  public messagesError: any = null;

  public structureName: any = {};

  public profile: any = null;
  public permissaoUsuario: any = null;

  public imagesItens: any = null;

  constructor(
    private activatedRoute: ActivatedRoute,
    private imagesServiceApi: ImagesServiceApi,
    private structuresServiceApi: StructuresServiceApi,
    public userService: UserService
  ) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile();
    this.permissaoUsuario = this.userService.permissaoUsuario;
    this.getStructure(this.activatedRoute.snapshot.params.structureId);
    this.getImages(this.activatedRoute.snapshot.params.structureId);
  }

  getStructure(structureId: string) {
    this.structuresServiceApi.getStructureByIdBasicInfo(structureId).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.structureName = dados.name;
    });
  }

  getImages(structureId: string) {
    this.imagesItens = [];
    const params = {
      Entities: 0,
      'Filters.Structures': structureId
    };

    this.imagesServiceApi.getImages(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.imagesItens = dados.images;
    });
  }

  registerImage(): Observable<any> {
    return new Observable((observer) => {
      const formData = new FormData();
      let ctrlImage = false;

      formData.append('StructureId', this.activatedRoute.snapshot.params.structureId);

      let index = 1;
      this.appImages['images'].forEach((image) => {
        if (image.new) {
          ctrlImage = true;
          formData.append('Images', image.file);
          formData.append(`Description${index}`, image.description);
          index++;
        }
      });

      if (ctrlImage) {
        this.imagesServiceApi.postImage(formData, {}).subscribe(
          (resp) => {
            let dados: any = resp;
            dados = dados.body === undefined ? dados : dados.body;
            observer.next({ success: true, data: dados, type: 'addImageSuccess' });
            observer.complete();
          },
          (error) => {
            observer.next({ success: false, error: error, type: 'addImageError' });
            observer.complete();
          }
        );
      } else {
        observer.next({ success: true, data: 'No image', type: 'noImage' });
        observer.complete();
      }
    });
  }

  updateImage(image): Observable<any> {
    return new Observable((observer) => {
      const params = {
        id: image.id,
        description: image.description
      };
      this.imagesServiceApi.patchImage(image.id, params).subscribe(
        (resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;
          observer.next({ success: true, data: dados, type: 'updImageSuccess' });
          observer.complete();
        },
        (error) => {
          observer.next({ success: false, error: error, type: 'updImageError' });
          observer.complete();
        }
      );
    });
  }

  handleCombinedResult(results: any[]): void {
    results.forEach((result) => {
      if (result.success) {
        this.message.text = MessageCadastro.ImagemCadastrada;
        this.message.status = true;
        this.message.class = 'alert-success';

        setTimeout(() => {
          this.message.status = false;
        }, 4000);
      } else {
        if (result.error.status !== 200) {
          this.messagesError = [];
          result.error.error.forEach((msgError) => {
            this.messagesError.push(msgError);
          });

          setTimeout(() => {
            this.messagesError = [];
          }, 6000);
        }
      }
    });
    this.imagesItens = [];
    this.appImages['images'] = [];
    this.getImages(this.activatedRoute.snapshot.params.structureId);
  }

  sendImages(): void {
    const registerObservable: Observable<any> = this.registerImage();

    const updateObservablesArray: Observable<any>[] = this.appImages['images']
      .filter((imageData) => !imageData.new)
      .map((imageData) => this.updateImage(imageData));

    forkJoin(registerObservable, ...updateObservablesArray).subscribe(
      (results) => {
        this.handleCombinedResult(results);
      },
      (error) => {
        //console.error('Erro inesperado:', error);
      }
    );
  }
}
