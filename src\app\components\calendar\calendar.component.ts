import { Component, Input, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { CalendarOptions, DateSelectArg, EventClickArg, EventApi } from '@fullcalendar/core';

import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';

import ptbrLocale from '@fullcalendar/core/locales/pt-br';
import esLocale from '@fullcalendar/core/locales/es';

import { UserService } from 'src/app/services/user.service';

import { INITIAL_EVENTS, createEventId } from 'src/app/utils/event-utils';

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class CalendarComponent implements OnInit {
  @ViewChild('modalCalendarActivities') ModalCalendarActivities: any;

  @Input() height: string = '';
  public calendarVisible = true;

  public calendarOptions: CalendarOptions = {
    initialView: 'dayGridMonth',
    timeZone: 'UTC',
    nowIndicator: true,
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin],
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
    },
    initialEvents: INITIAL_EVENTS,
    weekends: true,
    editable: true,
    selectable: true,
    selectMirror: true,
    dayMaxEvents: true,
    businessHours: true,
    select: this.handleDateSelect.bind(this),
    eventClick: this.handleEventClick.bind(this),
    eventsSet: this.handleEvents.bind(this),
    contentHeight: '346px'

    // events: [
    // {
    //   title: ' [Logisoil] Daily',
    //   daysOfWeek: [1, 2, 3, 4, 5],
    //   // start: '2023-01-09T09:45:00',
    //   // end: '2023-01-13T10:00:00',
    //   // duration: '00:15',
    //   constraint: 'businessHours',
    //   color: '#3788d8'
    // }
    // {
    //   title: ' [Logisoil] Daily',
    //   // start: '2023-01-16T09:45:00',
    //   // end: '2023-01-20T10:00:00',
    //   // duration: '00:15',
    //   constraint: 'businessHours',
    //   //constraint: 'availableForMeeting', // defined below
    //   color: '#3788d8'
    // },
    // {
    //   title: ' [Logisoil] Daily',
    //   // start: '2023-01-23T09:45:00',
    //   // end: '2023-01-27T10:00:00',
    //   duration: '00:15',
    //   constraint: 'businessHours',
    //   color: '#3788d8'
    // }
    // ]

    /* you can update a remote database when these fire:
    eventAdd:
    eventChange:
    eventRemove:
    */
  };

  public currentEvents: EventApi[] = [];
  public profile: any = null;
  public selectInfo: DateSelectArg;

  constructor(public userService: UserService /*private changeDetector: ChangeDetectorRef*/) {}

  ngOnInit(): void {
    this.profile = this.userService.getProfile(true);

    if (this.profile.locale.sigla == 'pt-BR') {
      this.calendarOptions['locale'] = ptbrLocale;
    } else if (this.profile.locale.sigla == 'es') {
      this.calendarOptions['locale'] = esLocale;
    }
  }

  handleCalendarToggle() {
    this.calendarVisible = !this.calendarVisible;
  }

  handleWeekendsToggle() {
    const { calendarOptions } = this;
    calendarOptions.weekends = !calendarOptions.weekends;
  }

  handleDateSelect(selectInfo: DateSelectArg) {
    this.selectInfo = selectInfo;
    this.ModalCalendarActivities.openModal();
    // const title = prompt('Por favor, insira um novo título para a sua atividade:');
    // const calendarApi = selectInfo.view.calendar;

    // calendarApi.unselect(); // clear date selection

    // if (title) {
    //   calendarApi.addEvent({
    //     id: createEventId(),
    //     title,
    //     start: selectInfo.startStr,
    //     end: selectInfo.endStr,
    //     allDay: selectInfo.allDay
    //   });
    // }
  }

  handleEventClick(clickInfo: EventClickArg) {
    if (confirm(`Tem certeza de que deseja excluir a atividade '${clickInfo.event.title}'?`)) {
      clickInfo.event.remove();
    }
  }

  handleEvents(events: EventApi[]) {
    this.currentEvents = events;
    // this.changeDetector.detectChanges();
  }

  getEvent($event) {
    switch ($event.option) {
      case 'addActivity':
        this.addActivity($event.form);
        break;
    }
  }

  addActivity(form) {
    const calendarApi = this.selectInfo.view.calendar;

    if (form.title.value) {
      calendarApi.addEvent({
        id: createEventId(),
        title: form.title.value,
        start: this.selectInfo.startStr,
        end: this.selectInfo.endStr,
        allDay: this.selectInfo.allDay
      });
    }
  }
}
