import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ClickOutsideModule } from 'ng4-click-outside';
import { ColorSketchModule } from 'ngx-color/sketch';

import { SharedModule } from '@components/shared.module';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { StaticMaterialsRoutingModule } from './static-materials-routing.module';
import { LogisoilDirectivesModule } from 'src/app/shared/logisoil-directives.module';

import { ListStaticMaterialsComponent } from './list-static-materials/list-static-materials.component';
import { RegisterStaticMaterialsComponent } from './register-static-materials/register-static-materials.component';
import { HistoryMaterialsComponent } from './history-materials/history-materials.component';

import { GeneralTabComponent } from './tabs/general-tab/general-tab.component';
import { ReviewTabComponent } from './tabs/review-tab/review-tab.component';

//Modelos constitutivos
import { MohrColoumbComponent } from './form-static-materials/mohr-coloumb/mohr-coloumb.component';
import { UndrainedComponent } from './form-static-materials/undrained/undrained.component';
import { InfiniteStrengthComponent } from './form-static-materials/infinite-strength/infinite-strength.component';
import { ShearNormalFunctionComponent } from './form-static-materials/shear-normal-function/shear-normal-function.component';
import { HoekBrownComponent } from './form-static-materials/hoek-brown/hoek-brown.component';
import { GeneralizedHoekBrownComponent } from './form-static-materials/generalized-hoek-brown/generalized-hoek-brown.component';
import { VerticalStressRatioComponent } from './form-static-materials/vertical-stress-ratio/vertical-stress-ratio.component';
import { ShansepComponent } from './form-static-materials/shansep/shansep.component';
import { MaterialPointsComponent } from './form-static-materials/material-points/material-points.component';

//Abas das condições drenada, não drenada e pseudoestática
import { ConditionsStaticMaterialsComponent } from './conditions-static-materials/conditions-static-materials.component';
import { TabsConditionsComponent } from './conditions-static-materials/tabs-conditions/tabs-conditions.component';

@NgModule({
  declarations: [
    ListStaticMaterialsComponent,
    RegisterStaticMaterialsComponent,
    GeneralTabComponent,
    ReviewTabComponent,
    HistoryMaterialsComponent,
    //Modelos constitutivos
    MohrColoumbComponent,
    UndrainedComponent,
    InfiniteStrengthComponent,
    ShearNormalFunctionComponent,
    GeneralizedHoekBrownComponent,
    HoekBrownComponent,
    VerticalStressRatioComponent,
    ShansepComponent,
    MaterialPointsComponent,
    //Abas das condições drenada, não drenada e pseudoestática
    ConditionsStaticMaterialsComponent,
    TabsConditionsComponent
  ],
  imports: [
    CommonModule,
    ClickOutsideModule,
    ColorSketchModule,
    FormsModule,
    NgbModule,
    NgMultiSelectDropDownModule.forRoot(),
    StaticMaterialsRoutingModule,
    ReactiveFormsModule,
    SharedModule,
    LogisoilDirectivesModule
  ],
  exports: [
    MohrColoumbComponent,
    UndrainedComponent,
    InfiniteStrengthComponent,
    ShearNormalFunctionComponent,
    GeneralizedHoekBrownComponent,
    HoekBrownComponent,
    VerticalStressRatioComponent,
    ShansepComponent,
    MaterialPointsComponent
  ]
})
export class StaticMaterialsModule {}
