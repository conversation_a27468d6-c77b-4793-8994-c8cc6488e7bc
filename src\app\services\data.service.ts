import { Injectable } from '@angular/core';

import { ClientService } from './api/client.service';
import { ClientUnitService } from './api/clientUnit.service';
import { InstrumentsService } from './api/instrument.service';
import { SectionsService } from './api/section.service';
import { StructuresService } from './api/structure.service';

import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DataService {
  constructor(
    private clientService: ClientService,
    private clientUnitService: ClientUnitService,
    private instrumentsService: InstrumentsService,
    private sectionsService: SectionsService,
    private structuresService: StructuresService
  ) {}

  getStructure(structureId): Observable<any> {
    if (structureId != null) {
      return this.structuresService.getStructureById(structureId).pipe(
        map((res: any) => {
          return res;
        })
      );
    }
    return of(null);
  }

  getClientUnit(clientUnitId): Observable<any> {
    if (clientUnitId != null) {
      return this.clientUnitService.getClientUnitsById(clientUnitId).pipe(
        map((res: any) => {
          return res;
        })
      );
    }
    return of(null);
  }

  getStructureCoordinate(clientId: string = '', structureIdArray: Array<string> = []) {
    if (structureIdArray.length > 0) {
      if (clientId != '' && clientId != null) {
        return this.structuresService.postStructureMaps({ structure_ids: structureIdArray }, { clientId: clientId }).pipe(
          map((res: any) => {
            return res;
          })
        );
      } else {
        return this.structuresService.postStructureMaps({ structure_ids: structureIdArray }).pipe(
          map((res: any) => {
            return res;
          })
        );
      }
    }
  }

  getSectionCoordinate(structureIdArray: Array<string> = []) {
    return this.sectionsService.postSectionMaps({ structure_ids: structureIdArray }).pipe(
      map((res: any) => {
        return res;
      })
    );
  }

  getClientLogo(clientId: string) {
    return this.clientService.getClientLogo(clientId).pipe(
      map((res: any) => {
        return res;
      })
    );
  }

  getDataById(option, nodes, id) {
    switch (option) {
      case 'instrument':
        return this.instrumentsService.getInstrumentsById(id).pipe(
          map((res: any) => {
            return this.filterNodes(res, nodes);
          })
        );
        break;
      default:
        break;
    }
  }

  filterNodes(res, nodes) {
    if (nodes.length > 0) {
      let data = {};
      Object.keys(res).forEach((key) => {
        if (nodes.includes(key)) {
          data[key] = res[key];
        }
      });
      return data;
    } else {
      return res;
    }
  }
}
