import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from './constants/rotas.constants';

const routes: Routes = [
  {
    path: Rotas.Home.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/home/<USER>').then((m) => m.HomeModule)
  },
  {
    path: Rotas.Dashboard.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/dashboard/dashboard.module').then((m) => m.DashboardModule)
  },
  {
    path: Rotas.Notificações.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/alert-center/alert-center.module').then((m) => m.AlertCenterModule)
  },
  {
    path: Rotas.Clientes.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/clients/clients.module').then((m) => m.ClientsModule)
  },
  {
    path: Rotas.Estabilidade.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/stability/stability.module').then((m) => m.StabilityModule)
  },
  {
    path: Rotas.Estruturas.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/structures/structures.module').then((m) => m.StructuresModule)
  },
  {
    path: Rotas.Instrumentacao.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/instruments/instruments.module').then((m) => m.InstrumentsModule)
  },
  {
    path: Rotas.Imagens.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/gallery-images/gallery-images.module').then((m) => m.GalleryImagesModule)
  },
  {
    path: Rotas.Inspecoes.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/inspections/inspections.module').then((m) => m.InspectionsModule)
  },
  {
    path: Rotas.Leituras.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/readings/readings.module').then((m) => m.ReadingsModule)
  },
  {
    path: Rotas.Materiais.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/static-materials/static-materials.module').then((m) => m.StaticMaterialsModule)
  },
  {
    path: Rotas.Relatorios.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/reports/reports.module').then((m) => m.ReportsModule)
  },
  {
    path: Rotas.Secoes.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/sections/sections.module').then((m) => m.SectionsModule)
  },
  {
    path: Rotas.Unidades.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/units/units.module').then((m) => m.UnitsModule)
  },
  {
    path: Rotas.Usuarios.substring(1),
    canActivate: [],
    loadChildren: () => import('./pages/users/users.module').then((m) => m.UsersModule)
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {}
