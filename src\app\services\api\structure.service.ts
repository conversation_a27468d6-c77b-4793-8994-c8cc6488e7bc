import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class StructuresService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  /**
   * Cadastra uma nova estrutura.
   *
   * @param params - Dados da estrutura a ser cadastrada.
   * @returns Observable com a resposta do backend.
   */
  postStructure(params: any) {
    const url = '/structures';
    return this.api.post<any>(url, params, {}, 'client');
  }

  /**
   * Busca estruturas para uso em filtros.
   *
   * @param params - Parâmetros de busca (ex: nome, cliente, unidade).
   * @returns Observable com lista de estruturas.
   */
  getStructures(params: any) {
    const url = '/structures/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  /**
   * Lista todas as estruturas com filtros opcionais.
   *
   * @param params - (Opcional) Filtros adicionais para listagem.
   * @returns Observable com a lista de estruturas.
   */
  getStructureList(params: any = {}) {
    const url = '/structures';
    return this.api.get<any>(url, params, false, 'client');
  }

  /**
   * Obtém os dados completos de uma estrutura pelo ID.
   *
   * @param id - ID da estrutura.
   * @returns Observable com os dados da estrutura.
   */
  getStructureById(id: string) {
    const url = `/structures/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Obtém as informações básicas de uma estrutura pelo ID.
   * Novo endpoint adicionado em 22/08/2024.
   *
   * @param id - ID da estrutura.
   * @returns Observable com dados básicos da estrutura.
   */
  getStructureByIdBasicInfo(id: string) {
    const url = `/structures/${id}/basic-info`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Obtém o histórico de alterações de uma estrutura.
   *
   * @param id - ID da estrutura.
   * @param params - (Opcional) Parâmetros de filtro para o histórico.
   * @returns Observable com a lista de eventos históricos.
   */
  getStructuresHistory(id: string, params: any = {}) {
    const url = `/structures/${id}/history`;
    return this.api.get<any[]>(url, params, false, 'client');
  }

  /**
   * Atualiza completamente os dados de uma estrutura (PUT).
   *
   * @param id - ID da estrutura.
   * @param params - Dados atualizados da estrutura.
   * @returns Observable com a resposta do backend.
   */
  putStructures(id: string, params: any) {
    const url = `/structures/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  /**
   * Atualiza parcialmente os dados de uma estrutura (PATCH).
   *
   * @param id - ID da estrutura.
   * @param params - Campos parciais a serem atualizados.
   * @returns Observable com a resposta do backend.
   */
  patchStructures(id: string, params: any) {
    const url = `/structures/${id}`;
    return this.api.patch<any>(url, params, 'client');
  }

  /**
   * Obtém a lista de países disponíveis.
   *
   * @returns Observable com a lista de países.
   */
  getCountries() {
    const url = `/countries`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Obtém a lista de estados com base nos filtros informados.
   *
   * @param params - (Opcional) Filtros como countryId.
   * @returns Observable com a lista de estados.
   */
  getStates(params: any = {}) {
    const url = `/states`;
    return this.api.get<any>(url, params, false, 'client');
  }

  /**
   * Obtém a lista de cidades com base nos filtros informados.
   *
   * @param params - (Opcional) Filtros como stateId.
   * @returns Observable com a lista de cidades.
   */
  getCities(params: any = {}) {
    const url = `/cities`;
    return this.api.get<any>(url, params, false, 'client');
  }

  /**
   * Busca dados geoespaciais para o mapa de estruturas.
   *
   * @param params - Parâmetros do corpo da requisição.
   * @param qparams - Parâmetros de query string.
   * @returns Observable com os dados do mapa.
   */
  postStructureMaps(params: any = {}, qparams: any = {}) {
    const url = '/structures/maps/search';
    return this.api.post<any>(url, params, qparams, 'client');
  }

  /**
   * Obtém a lista de naturezas de uma estrutura (usado na tela de leituras - Geofone).
   *
   * @param id - ID da estrutura.
   * @returns Observable com as naturezas associadas.
   */
  getStructureNatures(id: string) {
    const url = `/structures/${id}/natures`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Cadastra novas naturezas para uma estrutura (usado na tela de leituras - Geofone).
   *
   * @param id - ID da estrutura.
   * @param params - Dados das naturezas a serem associadas.
   * @returns Observable com a resposta do backend.
   */
  postStructureNatures(id: string, params: any = {}) {
    const url = `/structures/${id}/natures`;
    return this.api.post<any>(url, params, {}, 'client');
  }

  /**
   * Atualiza uma natureza associada a uma estrutura (usado na tela de leituras - Geofone).
   *
   * @param idStructure - ID da estrutura.
   * @param idNature - ID da natureza.
   * @param params - Dados atualizados da natureza.
   * @returns Observable com a resposta do backend.
   */
  putStructureNatures(idStructure: string, idNature: string, params: any) {
    const url = `/structures/${idStructure}/natures/${idNature}`;
    return this.api.put<any>(url, params, 'client');
  }

  /**
   * Obtém os métodos de cálculo vinculados a uma estrutura (usado em gráficos de estabilidade).
   *
   * @param id - ID da estrutura.
   * @returns Observable com os métodos de cálculo.
   */
  getStructureCalculationMethods(id: string) {
    const url = `/structures/${id}/calculation-methods`;
    return this.api.get<any>(url, null, false, 'client');
  }

  /**
   * Obtém os tipos de superfície vinculados a uma estrutura (usado em gráficos de estabilidade).
   *
   * @param id - ID da estrutura.
   * @returns Observable com os tipos de superfície.
   */
  getStructureSurfaceTypes(id: string) {
    const url = `/structures/${id}/surface-types`;
    return this.api.get<any>(url, null, false, 'client');
  }
}
