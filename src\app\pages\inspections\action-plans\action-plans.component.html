<div class="action-plans-container">
  <!-- Selects Cliente, Unidade e Estrutura -->
  <div class="row filters">
    <app-hierarchy
      #hierarchy
      [elements]="elements"
      class="col-md-12"
      (sendEventHierarchy)="getEventHierarchy($event)"
    ></app-hierarchy>
  </div>

  <div class="row filters mt-3">
    <!-- OccurrenceDateFilter -->
    <div class="col-md-3">
      <label class="form-label">Filtro de data</label>
      <select class="form-select" [(ngModel)]="selectedDateFilter">
        <option value="">Selecione</option>
        <option *ngFor="let status of dateFilter" [value]="status.value">
          {{ status.label }}
        </option>
      </select>
    </div>

    <!-- Periodo -->
    <div class="col-md-3" *ngIf="selectedDateFilter !== '3'">
      <label class="form-label">Período</label>
      <select
        class="form-select"
        [disabled]="selectedDateFilter === 'noFilter'"
        [(ngModel)]="filter.Period"
        (change)="calculatePeriod(filter.Period)"
      >
        <option value="">Selecione</option>
        <option *ngFor="let period of datePeriod" [value]="period.value">
          {{ period.label }}
        </option>
      </select>
    </div>

    <!-- Data inicial -->
    <div class="col-md-2">
      <label class="form-label">Data inicial</label>
      <input
        type="date"
        class="form-control"
        [(ngModel)]="filter.StartDate"
        [ngClass]="{ readonly: disableDateFields }"
        [disabled]="
          disableDateFields ||
          selectedDateFilter === '3' ||
          selectedDateFilter === 'noFilter'
        "
      />
    </div>

    <!-- Data final -->
    <div class="col-md-2">
      <label class="form-label">Data final</label>
      <input
        type="date"
        class="form-control"
        [(ngModel)]="filter.EndDate"
        [ngClass]="{ readonly: disableDateFields }"
        [disabled]="
          disableDateFields ||
          selectedDateFilter === '3' ||
          selectedDateFilter === 'noFilter'
        "
      />
    </div>

    <!-- Origem -->
    <div class="col-md-2">
      <label class="form-label">Origem</label>
      <select class="form-select" [(ngModel)]="selectedOrigin">
        <option value="all">Todas</option>
        <option
          *ngFor="let origin of inspectionSheetType"
          [value]="origin.value"
        >
          {{ origin.label }}
        </option>
      </select>
    </div>
  </div>

  <div class="row filters mt-3 mb-3">
    <!-- Área -->
    <div class="col-md-3">
      <label class="form-label">Área</label>
      <select class="form-select" [(ngModel)]="selectedArea">
        <option value="all">Todas</option>
        <option
          *ngFor="let areaActionPlan of area"
          [value]="areaActionPlan.value"
        >
          {{ areaActionPlan.label }}
        </option>
      </select>
    </div>

    <!-- Status -->
    <div class="col-md-3">
      <label class="form-label">Status</label>
      <select class="form-select" [(ngModel)]="selectedActionPlanStatus">
        <option value="all">Todos</option>
        <option *ngFor="let status of actionPlanStatus" [value]="status.value">
          {{ status.label }}
        </option>
      </select>
    </div>

    <!-- Visualização -->
    <div class="col-md-3">
      <label class="form-label">Visualização</label>
      <ng-multiselect-dropdown
        [placeholder]="'Selecione...'"
        [settings]="viewSettings"
        [data]="tableHeader"
        (onSelect)="toggleColumns($event, 'select')"
        (onSelectAll)="toggleColumns($event, 'selectAll')"
        (onDeSelect)="toggleColumns($event, 'deselect')"
        (onDeSelectAll)="toggleColumns($event, 'deselectAll')"
        [(ngModel)]="selectedColumns"
      >
      </ng-multiselect-dropdown>
    </div>

    <!-- Botões -->
    <div class="col-md-3 d-flex align-items-end">
      <app-button
        [class]="'btn-logisoil-blue me-2'"
        [icon]="'fa fa-search'"
        [label]="'Buscar'"
        (click)="searchActionPlans()"
      ></app-button>
      <app-button
        [class]="'btn-logisoil-gray'"
        [icon]="'fa fa-eraser'"
        [label]="'Limpar'"
        (click)="resetFilter()"
      ></app-button>
    </div>
  </div>

  <div class="row">
    <!-- Farol -->
    <app-farol-legend *ngIf="tableData.length > 0"></app-farol-legend>

    <!-- GUT -->
    <div class="gut-legend mt-2" *ngIf="tableData.length > 0">
      <strong>GUT:</strong>
      <span class="descricao"
        >Gravidade + Urgência + Tendência (máx: 125). Quanto maior, mais crítica
        a ação.</span
      >
    </div>

    <!-- Novo plano de ação -->
    <div
      class="new-action-plan d-flex align-items-end justify-content-end mb-3"
    >
      <app-button
        [class]="'btn-logisoil-green'"
        [customBtn]="true"
        [icon]="'fas fa-plus-circle'"
        [label]="'Novo Plano de Ação'"
        (click)="getSelectedStructure()"
        [disabled]="!permissaoUsuario.newActionPlan || !isStructureSelected"
      ></app-button>
    </div>

    <!-- Tabela -->
    <div class="col-md-12">
      <div
        class="alert"
        [ngClass]="message.class"
        role="alert"
        *ngIf="message.status"
      >
        {{ message.text }}
      </div>

      <app-table
        *ngIf="tableData.length > 0"
        [tableHeader]="tableHeader"
        [tableData]="tableData"
        [permissaoUsuario]="permissaoUsuario"
        [menuMiniDashboard]="'miniDashboardActionPlans'"
        [getActionsFn]="getActionButtons.bind(this)"
        (sendClickRowEvent)="clickRowEvent($event)"
      ></app-table>
    </div>
  </div>

  <!-- Paginação -->
  <div class="row mt-3" *ngIf="tableData.length > 0">
    <app-paginator
      [collectionSize]="collectionSize"
      [page]="page"
      [maxSize]="10"
      [boundaryLinks]="true"
      [pageSize]="pageSize"
      (sendPageChange)="loadPage($event)"
      [enableItemPerPage]="true"
    ></app-paginator>
  </div>
</div>

<!-- Confirmar exclusao do plano de ação -->
<app-modal-confirm
  #modalConfirm
  (sendClickEvent)="clickRowEvent($event)"
  [title]="modalTitle"
  [message]="modalMessage"
  [instruction]="modalInstruction"
  [modalConfig]="modalConfig"
  [data]="modalData"
></app-modal-confirm>

<ngx-spinner
  bdColor="rgba(51,51,51,0.1)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
