import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';

import { Observable, Subject, filter } from 'rxjs';
import { TituloInstrumento } from '../constants/enums/instrumentTypes';
import { MenuPrincipal, MenuAdmin, Acoes } from '../constants/rotas.constants';

@Injectable({
  providedIn: 'root'
})
export class GlobalService {
  tituloSubject: Subject<any> = new Subject();

  public titulo: any = {
    titulo: 'Homepage',
    acao: '',
    acaoIcone: '',
    complemento: '',
    icone: 'fa fa-icon fas fa-home',
    tipoIcone: '',
    routerLink: '/'
  };

  constructor(private router: Router) {}

  //Observa as mudanças de navegação no roteador Angular e atualiza o título da página conforme a URL.
  //Dependendo da profundidade da URL, chama o método mountTitle para montar o título apropriado.
  setTitle() {
    this.router.events.pipe(filter((event: any) => event instanceof NavigationEnd)).subscribe((event) => {
      const ocorrencia = (event.url.match(/\//g) || []).length;
      const path = event.url.split('/');
      if (ocorrencia === 1) {
        this.titulo = this.mountTitle(event.url);
        this.titulo = this.createRouterLinks(this.titulo, event.url);
        this.tituloSubject.next(this.titulo);
      } else if (ocorrencia === 2) {
        //Verificar se tem instrumento
        const ocorrenciaParam = (event.url.match(/;/g) || []).length;
        if (ocorrenciaParam === 0) {
          this.titulo = this.mountTitle(`/${path[1]}`, `${path[2]}`);
          this.titulo = this.createRouterLinks(this.titulo, event.url);
          this.tituloSubject.next(this.titulo);
        } else {
          //Tela instrumentacao
          const pathParam = path[2].split(';');
          this.titulo = this.mountTitle(`/${path[1]}`, `${pathParam[0]}`, TituloInstrumento[pathParam[1].split('type=')[1]]);
          this.titulo.fullScreen = true;
          this.titulo = this.createRouterLinks(this.titulo, event.url);
          this.tituloSubject.next(this.titulo);
        }
      } else if (ocorrencia === 3 || ocorrencia === 4) {
        this.titulo = this.mountTitle(`/${path[1]}`, `${path[ocorrencia]}`);
        this.titulo = this.createRouterLinks(this.titulo, event.url);
        if (this.titulo.acaoRouterLink == '/inspections/action-plan') {
          this.titulo.acaoRouterLink = '/inspections';
          this.titulo['acaoQueryParams'] = {
            tab: 'planosDeAcao'
          };
        }
        this.tituloSubject.next(this.titulo);
      }
    });
  }

  /**
   * Retorna um Observable que emite o título atual da página quando ele é atualizado.
   * @returns {Observable<any>} Um Observable que emite o título atual da página.
   */
  getTitle(): Observable<any> {
    return this.tituloSubject.asObservable();
  }

  /**
   * @description Monta um objeto de título com base na URL fornecida, ação e complemento opcionais.
   *               Utiliza as constantes MenuPrincipal, MenuAdmin e Acoes para determinar o título, ícone e outras propriedades.
   * @param {string} pathUrl - A URL do caminho para determinar o título.
   * @param {string} [action=''] - A ação a ser exibida no título, se aplicável.
   * @param {string} [complete=''] - Um complemento adicional para o título, se aplicável.
   * @returns {any} Um objeto contendo o título, ícone e outras propriedades configuradas para a página.
   */
  mountTitle(pathUrl: string, action: string = '', complete = ''): any {
    const pathQuery = pathUrl.split('?');
    const Menus = MenuPrincipal.concat(MenuAdmin);
    const index = Menus.findIndex((object) => {
      return object.Rota === pathQuery[0];
    });

    action = action.split('?')[0];

    if (index !== -1) {
      let item = {
        titulo: Menus[index].Titulo,
        icone: Menus[index].Icone,
        // acao: action !== '' ? Acoes[action].titulo : '',
        // acaoIcone: action !== '' ? Acoes[action].icone : '',
        acao: Acoes[action]?.titulo || '',
        acaoIcone: Acoes[action]?.icone || '',
        complemento: complete !== '' ? ' - ' + complete : '',
        tipoIcone: Menus[index].Icone.substring(Menus[index].Icone.length - 4) === '.svg' ? 'svg' : '',
        routerLink: pathQuery[0],
        subTitulos: []
      };

      // if (Acoes[action] && Acoes[action].hasOwnProperty('subTitulos')) {
      //   item.subTitulos = Acoes[action].subTitulos.map((subTitulo) => {
      //     let subTituloitem = {};
      //     subTituloitem['titulo'] = subTitulo.titulo;
      //     subTituloitem['icone'] = subTitulo.icone;
      //     subTituloitem['routerLink'] = subTitulo.routerLink;
      //     subTituloitem['tipoIcone'] = subTitulo.icone.substring(subTitulo.icone.length - 4) === '.svg' ? 'svg' : '';

      //     return subTituloitem;
      //   });
      // }
      if (Acoes[action]?.subTitulos?.length) {
        item.subTitulos = Acoes[action].subTitulos.map((subTitulo) => {
          return {
            titulo: subTitulo.titulo,
            icone: subTitulo.icone,
            routerLink: subTitulo.routerLink,
            tipoIcone: subTitulo.icone.endsWith('.svg') ? 'svg' : ''
          };
        });
      }
      return item;
    } else {
    }
  }

  /**
   * Gera links de roteamento dinâmicos para o item fornecido, identificando e organizando
   * os segmentos de caminho, parâmetros de consulta e UUID (se presente) em diferentes níveis.
   *
   * @param {Object} item - O objeto ao qual os links de roteamento gerados serão atribuídos.
   * @param {string} url - A URL que será dividida em segmentos de caminho e parâmetros de consulta.
   * @returns {Object} O item atualizado com as propriedades 'tituloRouterLink', 'acaoRouterLink',
   *                   'subTituloRouterLink', e seus respectivos parâmetros de consulta.
   */
  createRouterLinks(item, url) {
    const [pathString, queryString] = url.split('?');
    const pathSegments = pathString.split('/').filter((segment) => segment); // Remove empty segments
    const queryParams = queryString ? this.parseQueryParams(queryString) : {};

    let tituloRouterLink = '';
    let acaoRouterLink = '';
    let subTituloRouterLink = '';
    let uuid = '';

    // Identificar e remover o UUID do caminho
    const cleanedSegments = pathSegments
      .map((segment) => {
        if (this.isUUID(segment)) {
          uuid = segment;
          return null;
        }
        return segment;
      })
      .filter((segment) => segment); // Remove segmentos nulos

    // Atribuir os níveis
    if (cleanedSegments.length >= 1) {
      tituloRouterLink = `/${cleanedSegments[0]}`;
      if (cleanedSegments.length == 1) {
        tituloRouterLink = `/${uuid}/${cleanedSegments[0]}`;
      }
    }

    if (cleanedSegments.length >= 2) {
      acaoRouterLink = `/${cleanedSegments[0]}/${cleanedSegments[1]}`;
      if (cleanedSegments.length == 2) {
        acaoRouterLink = `/${cleanedSegments[0]}/${uuid}/${cleanedSegments[1]}`;
      }
    }

    if (cleanedSegments.length >= 3) {
      subTituloRouterLink = `/${cleanedSegments[0]}/${cleanedSegments[1]}/${cleanedSegments[2]}`;
      if (cleanedSegments.length == 3) {
        subTituloRouterLink = `/${cleanedSegments[0]}/${cleanedSegments[1]}/${uuid}/${cleanedSegments[2]}`;
      }
    }

    // Atribuir ao item as propriedades corretas
    item['tituloRouterLink'] = tituloRouterLink || '/';
    item['tituloQueryParams'] = cleanedSegments.length === 1 ? queryParams : {};

    item['acaoRouterLink'] = acaoRouterLink || tituloRouterLink || '/';
    item['acaoQueryParams'] = cleanedSegments.length === 2 ? queryParams : {};

    item['subTituloRouterLink'] = cleanedSegments.length >= 3 ? subTituloRouterLink : '';
    item['subTituloQueryParams'] = cleanedSegments.length >= 3 ? queryParams : {};

    return item;
  }

  /**
   * Analisa a string de consulta e converte-a em um objeto de parâmetros de consulta.
   *
   * @param {string} queryString - A string de consulta que será dividida em pares chave-valor.
   * @returns {Object} Um objeto que representa os parâmetros de consulta na forma de chave-valor.
   */
  parseQueryParams(queryString) {
    return queryString.split('&').reduce((acc, param) => {
      const [key, value] = param.split('=');
      acc[key] = value;
      return acc;
    }, {});
  }

  /**
   * Verifica se o segmento fornecido corresponde ao padrão de um UUID.
   *
   * @param {string} segment - O segmento do caminho que será verificado.
   * @returns {boolean} Retorna true se o segmento for um UUID válido, caso contrário, retorna false.
   */
  isUUID(segment) {
    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    return uuidRegex.test(segment);
  }
}
