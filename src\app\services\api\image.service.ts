import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class ImagesService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  //Cadastro imagem
  postImage(params: any, queryParams: any = {}) {
    const url = `/images`;
    return this.api.post<any>(url, params, queryParams, 'client', true);
  }

  // Retorna as imagens com base em filtro
  getImages(params: any) {
    const url = '/images/search';
    return this.api.get<any>(url, params, false, 'client');
  }

  getImageById(id: string, params: any) {
    const url = `/images/${id}/base64`;
    return this.api.get<any>(url, params, false, 'client');
  }

  deleteImage(id: string) {
    const url = `/images/${id}`;
    return this.api.delete<any>(url, 'client');
  }

  patchImage(id: string, params: any) {
    const url = `/images/${id}`;
    return this.api.patch<any>(url, params, 'client');
  }
}
