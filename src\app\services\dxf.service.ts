import { Injectable } from '@angular/core';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Observable, map, switchMap } from 'rxjs';
import fn from 'src/app/utils/function.utils';

@Injectable({
  providedIn: 'root'
})
export class DxfService {
  constructor(private sanitizer: DomSanitizer, private sectionsServiceApi: SectionsServiceApi) {}

  transformDxf(fileInfo: any): Observable<any> {
    return this.sectionsServiceApi.postSectionsDXFTransform({ base64: fileInfo.fileContent }).pipe(
      map((respTransform: any) => {
        const fileContent = respTransform.base64;
        const fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + fileContent);
        const fileDXF = fn.base64ToFile(fileContent, fileInfo.fileName);
        return {
          fileContent: fileContent,
          fileContentDownload: fileContentDownload,
          fileName: fileInfo.fileName,
          fileDXF: fileDXF
        };
      })
    );
  }

  transformDxfMaterial(fileInfo: any): Observable<any> {
    return this.sectionsServiceApi.postSectionsDXFTransformMaterial({ base64: fileInfo.fileContent }).pipe(
      map((respTransform: any) => {
        const fileContent = respTransform.base64;
        const fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl('data:application/octet-stream;base64,' + fileContent);
        const fileDXF = fn.base64ToFile(fileContent, fileInfo.fileName);
        return {
          fileContent: fileContent,
          fileContentDownload: fileContentDownload,
          fileName: fileInfo.fileName,
          fileDXF: fileDXF
        };
      })
    );
  }

  processDxf(fileInfo: any): Observable<any> {
    return this.transformDxf(fileInfo).pipe(
      switchMap((initialTransform) => {
        const updatedFileInfo = {
          fileContent: initialTransform.fileContent,
          fileName: fileInfo.fileName
        };
        return this.transformDxfMaterial(updatedFileInfo).pipe(
          map((materialTransform) => ({
            initialTransform,
            materialTransform
          }))
        );
      })
    );
  }
}
