import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListStaticMaterialsComponent } from './list-static-materials/list-static-materials.component';
import { RegisterStaticMaterialsComponent } from './register-static-materials/register-static-materials.component';
import { HistoryMaterialsComponent } from './history-materials/history-materials.component';

import { AppGuard } from '../../guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListStaticMaterialsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CadastrarMaterial,
    component: RegisterStaticMaterialsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarMaterial,
    component: RegisterStaticMaterialsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.VisualizarMaterial,
    component: RegisterStaticMaterialsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.HistoricoMaterial,
    component: HistoryMaterialsComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StaticMaterialsRoutingModule {}
