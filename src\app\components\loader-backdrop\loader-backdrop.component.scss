.fullscreen-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw; /* Largura total da tela */
  height: 100vh; /* Altura total da tela */
  background-color: #000; /* Cor de fundo */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;

  .video-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;

    video {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      object-fit: contain; /* Garante que o vídeo não será cortado */
      transform: translate(-50%, -50%);
      background-color: black; /* Fundo preto para barras */
    }
  }
}
