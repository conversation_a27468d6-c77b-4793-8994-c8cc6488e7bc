import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { animate, state, style, transition, trigger } from '@angular/animations';

import { InspectionService as InspectionServiceApi } from 'src/app/services/api/inspection.service';

import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';

import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ModalMapComponent } from '@components/modal/modal-map/modal-map.component';

import { Datum } from 'src/app/constants/app.constants';

@Component({
  selector: 'app-aspects-observed',
  templateUrl: './aspects-observed.component.html',
  styleUrls: ['./aspects-observed.component.scss'],
  animations: [
    trigger('accordionAnimation', [
      state(
        'closed',
        style({
          height: '0',
          overflow: 'hidden',
          opacity: 0
        })
      ),
      state(
        'open',
        style({
          height: '*',
          overflow: 'hidden',
          opacity: 1
        })
      ),
      transition('closed <=> open', [animate('300ms ease-in-out')])
    ])
  ]
})
export class AspectsObservedComponent implements OnInit, OnChanges {
  @ViewChild('modalInsertAspect') ModalInsertAspect: any;
  @Input() public inspectionSheetType: number = null;
  @Input() public status: number = null;
  @Input() public locked: boolean = false;
  @Input() public view: boolean = false;
  @Output() public saveTriggered = new EventEmitter();
  @Output() public formChanged = new EventEmitter<any>();

  public areas: any = [];
  public areasList: any = [];
  public aspectsList: any = [];
  public areasAspectsList: any = [];

  public activeItemId: string | null = null;

  public hierarchy: any = {};

  public areaForm: FormGroup;

  public occurrencesLinkable: any = [];

  constructor(private fb: FormBuilder, private inspectionServiceApi: InspectionServiceApi, private modalService: NgbModal) {}

  ngOnInit(): void {
    this.getAreasList();
    this.getAspectsList();

    this.initializeForm();
  }

  /**
   * Detecta e processa mudanças nos inputs vinculados ao componente.
   * @param {SimpleChanges} changes - Contém as mudanças detectadas nos inputs do componente.
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (this.areaForm && (changes.status || changes.locked)) {
      this.toggleForm();
    }
  }

  /**
   * Inicializa o formulário de áreas e aspectos.
   */
  initializeForm(): void {
    this.areaForm = this.fb.group({
      areas: this.fb.array([]) // FormArray para as áreas
    });
  }

  /**
   * Cria um FormGroup para uma área.
   * @param {any} area - Dados da área.
   * @returns {FormGroup} - Formulário da área.
   */
  createArea(area: any): FormGroup {
    return this.fb.group({
      id: [area.id, Validators.required],
      name: [area.name, Validators.required],
      aspects: this.fb.array(area.aspects.map((aspect: any) => this.createAspect(aspect))) // Mapeia e cria os aspectos
    });
  }

  /**
   * Cria um FormGroup para um aspecto.
   * @param {any} aspect - Dados do aspecto.
   * @returns {FormGroup} - Formulário do aspecto.
   */
  createAspect(aspect: any): FormGroup {
    return this.fb.group({
      id: [aspect.id, Validators.required],
      description: [aspect.description, Validators.required],
      allow_option_not_applicable: [aspect.allow_option_not_applicable],
      response_for_occurrence: [aspect.response_for_occurrence],
      occurrences: this.fb.array(aspect.occurrences.map((occurrence: any) => this.createOccurrence(occurrence))) // Mapear ocorrências
    });
  }

  /**
   * Cria um FormGroup para uma ocorrência.
   * @param {any} occurrence - Dados da ocorrência.
   * @returns {FormGroup} - Formulário da ocorrência.
   */
  createOccurrence(occurrence: any): FormGroup {
    return this.fb.group({
      id: [occurrence.id],
      search_identifier: [occurrence.search_identifier],
      northing: [occurrence.northing],
      easting: [occurrence.easting],
      note: [occurrence.note],
      linked_to: [occurrence.linked_to],
      response: [occurrence.response],
      //occurrence_attachments: this.fb.array(occurrence.occurrence_attachments.map((attachment: any) => this.createAttachment(attachment))) // Mapear anexos
      occurrence_attachments: this.fb.array((occurrence.occurrence_attachments || []).map((attachment: any) => this.createAttachment(attachment))) // Garante que é um array válido
    });
  }

  /**
   * Cria um FormGroup para um anexo de ocorrência.
   * @param {any} attachment - Dados do anexo.
   * @returns {FormGroup} - Formulário do anexo.
   */
  createAttachment(attachment: any): FormGroup {
    return this.fb.group({
      id: [attachment?.id || null],
      file: [attachment?.file || { base64: '', name: '' }], // Define valores padrão
      northing: [attachment?.northing || null],
      easting: [attachment?.easting || null]
    });
  }

  /**
   * Obtém a lista de áreas.
   */
  getAreasList() {
    this.inspectionServiceApi.getAreasList().subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.areasList = dados;
    });
  }

  /**
   * Obtém a lista de aspectos.
   * @param {string | null} areaId - ID da área (opcional).
   */
  getAspectsList(areaId = null) {
    const params = areaId ? { AreaId: areaId } : {};
    this.inspectionServiceApi.getAspectsList(params).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      if (areaId) {
        if (dados) {
          //this.filterAspectsList(areaId);
          this.aspectsList = dados;
        } else {
          this.aspectsList = [];
        }
      } else {
        this.areasAspectsList = this.formatGroupedAreas(dados);
      }
    });
  }

  /**
   * Formata a lista de áreas agrupadas com seus respectivos aspectos.
   * @param {any[]} dados - Dados das áreas e aspectos.
   * @returns {any[]} - Lista formatada de áreas e aspectos.
   */
  formatGroupedAreas(dados) {
    const groupedData = dados.reduce((acc, item) => {
      const areaId = item.area.id;

      const existingArea = acc.find((area) => area.id === areaId);

      if (existingArea) {
        existingArea.aspects.push({
          id: item.id,
          description: item.description
        });
      } else {
        acc.push({
          id: item.area.id,
          name: item.area.name,
          aspects: [
            {
              id: item.id,
              description: item.description
            }
          ]
        });
      }

      return acc;
    }, [] as { id: string; name: string; aspects: { id: string; description: string }[] }[]);

    return groupedData;
  }

  /**
   * Configura os dados recebidos do componente pai.
   * @param {any} dados - Dados recebidos.
   * @param {any[]} occurrencesLinkable - Lista de ocorrências vinculáveis.
   */
  setData(dados, occurrencesLinkable) {
    this.occurrencesLinkable = occurrencesLinkable;

    let itemDatum: any = '';

    this.hierarchy = {
      client: dados.client,
      client_unit: dados.client_unit,
      structure: dados.structure
    };

    itemDatum = Datum.find((datum) => datum.id === this.hierarchy.structure.coordinate_setting.datum);
    this.hierarchy.structure.coordinate_setting['datum_description'] = itemDatum ? `(${itemDatum.value})` : '';

    if (dados?.areas) {
      this.areas = dados.areas;
      const areasFormArray = this.areaForm.get('areas') as FormArray;
      areasFormArray.clear(); // Limpar qualquer dado existente no formulário
      this.areas.forEach((area) => {
        areasFormArray.push(this.createArea(area));
      });
    }
    this.toggleForm();
  }

  /**
   * Alterna o estado de um item no accordion.
   * @param {string} areaId - ID da área.
   */
  toggleItem(areaId: string): void {
    this.activeItemId = this.activeItemId === areaId ? null : areaId;
  }

  /**
   * Adiciona um novo aspecto a uma área.
   * @param {object} event - Dados da área e do aspecto a serem adicionados.
   */
  onAspectInserted(event: { area: { id: string; name: string }; aspect: { id: string; description: string } }): void {
    const areasFormArray = this.areaForm.get('areas') as FormArray;

    // Procurar se a área já existe no FormArray
    const existingAreaIndex = areasFormArray.controls.findIndex((areaGroup) => areaGroup.get('id')?.value === event.area.id);

    if (existingAreaIndex !== -1) {
      // Área já existe, procurar pelo aspecto
      const areaGroup = areasFormArray.at(existingAreaIndex) as FormGroup;
      const aspectsFormArray = areaGroup.get('aspects') as FormArray;

      const existingAspectIndex = aspectsFormArray.controls.findIndex((aspectGroup) => aspectGroup.get('id')?.value === event.aspect.id);

      if (existingAspectIndex === -1) {
        // Adicionar novo aspecto com ocorrência vazia
        aspectsFormArray.push(this.createAspectFormGroup(event.aspect));
      }
    } else {
      // Criar nova área com o aspecto e uma ocorrência vazia
      areasFormArray.push(this.createAreaFormGroup(event.area, event.aspect));
    }

    // Atualizar o activeItemId para abrir o accordion correspondente
    this.activeItemId = event.area.id;

    this.saveTriggered.emit({ userCompletedTheInspection: false, action: 'addAspect' });
  }

  /**
   * Cria um FormGroup para um novo aspecto.
   * @param {object} aspect - Dados do aspecto.
   * @returns {FormGroup} - Formulário do aspecto.
   */
  private createAspectFormGroup(aspect: { id: string; description: string }): FormGroup {
    return this.fb.group({
      id: [aspect.id],
      description: [aspect.description],
      occurrences: this.fb.array([this.createOccurrenceFormGroup()])
    });
  }

  /**
   * Cria um FormGroup para uma nova ocorrência.
   * @returns {FormGroup} - Formulário da ocorrência.
   */
  private createOccurrenceFormGroup(): FormGroup {
    return this.fb.group({
      id: [null],
      search_identifier: [null],
      northing: [null],
      easting: [null],
      note: [null],
      linked_to: [null],
      response: [null],
      occurrence_attachments: this.fb.array([])
    });
  }

  /**
   * Cria um FormGroup para uma nova área com um aspecto.
   * @param {object} area - Dados da área.
   * @param {object} aspect - Dados do aspecto.
   * @returns {FormGroup} - Formulário da área.
   */
  private createAreaFormGroup(area: { id: string; name: string }, aspect: { id: string; description: string }): FormGroup {
    return this.fb.group({
      id: [area.id],
      name: [area.name],
      aspects: this.fb.array([this.createAspectFormGroup(aspect)])
    });
  }

  /**
   * Adiciona uma nova ocorrência a um aspecto.
   * @param {AbstractControl} area - Controle da área.
   * @param {number} aspectIndex - Índice do aspecto dentro da área.
   */
  onAddOccurrence(area: AbstractControl, aspectIndex: number): void {
    const occurrences = area.get(['aspects', aspectIndex, 'occurrences']) as FormArray;
    occurrences.push(
      this.fb.group({
        id: [null],
        search_identifier: [null],
        northing: [null],
        easting: [null],
        note: [null],
        linked_to: [null],
        response: [null],
        occurrence_attachments: this.fb.array([])
      })
    );
    this.saveTriggered.emit({ userCompletedTheInspection: false, action: 'addOccurrence' });
  }

  /**
   * Remove uma ocorrência de um aspecto.
   * @param {AbstractControl} area - Controle da área.
   * @param {number} aspectIndex - Índice do aspecto dentro da área.
   * @param {number} occurrenceIndex - Índice da ocorrência dentro do aspecto.
   */
  onRemoveOccurrence(area: AbstractControl, aspectIndex: number, occurrenceIndex: number): void {
    const aspectsFormArray = area.get('aspects') as FormArray;
    const occurrencesFormArray = aspectsFormArray.at(aspectIndex).get('occurrences') as FormArray;

    // Remover a ocorrência especificada
    occurrencesFormArray.removeAt(occurrenceIndex);

    // Verificar se o aspecto ficou vazio (sem ocorrências)
    if (occurrencesFormArray.length === 0) {
      aspectsFormArray.removeAt(aspectIndex); // Remove o aspecto vazio

      // Verificar se a área ficou vazia (sem aspectos)
      if (aspectsFormArray.length === 0) {
        const areasFormArray = this.areaForm.get('areas') as FormArray;
        const areaIndex = areasFormArray.controls.indexOf(area);
        if (areaIndex !== -1) {
          areasFormArray.removeAt(areaIndex); // Remove a área vazia
        }
      }
    }

    // Emite o evento de atualização após a remoção
    this.saveTriggered.emit({ userCompletedTheInspection: false, action: 'removeOccurrence' });
  }

  /**
   * Dispara evento de adição de anexo de ocorrência.
   */
  onAddOccurrenceAttachment() {
    this.saveTriggered.emit({ userCompletedTheInspection: false, action: 'addOccurrenceAttachment' });
  }

  /**
   * Dispara evento de vínculo de ocorrência.
   */
  onAddOccurrenceLinked() {
    this.saveTriggered.emit({ userCompletedTheInspection: false, action: 'addOccurrenceLinked' });
  }

  /**
   * Abre o modal para inserção de aspecto, caso permitido pelo status.
   */
  openInsertAspectModal(): void {
    if ([2, 3].includes(this.status)) {
      return; // Não permite abrir o modal
    }
    this.ModalInsertAspect.openModal();
  }

  /**
   * Abre o modal do mapa e passa os dados necessários.
   * @param {any} $event - Evento contendo os dados do mapa.
   */
  showMap($event) {
    const modalRef = this.modalService.open(ModalMapComponent, { size: 'xl' });
    modalRef.componentInstance.title = 'da Estrutura ' + this.hierarchy.structure.name;
    modalRef.componentInstance.coordinates = this.hierarchy.structure.coordinate_setting;
    modalRef.componentInstance.data = $event;
    modalRef.componentInstance.sendClickEvent.subscribe(($event) => this.clickEvent($event));
  }

  /**
   * Manipula o evento de clique no mapa para atualizar as coordenadas no formulário.
   * @param {any} $event - Evento contendo as coordenadas selecionadas no mapa.
   */
  clickEvent($event) {
    if (($event.type = 'coordinates')) {
      this.onUpdateOccurrenceCoordinates($event);
    }
  }

  /**
   * Atualiza as coordenadas de uma ocorrência.
   * @param {any} $event - Evento contendo as novas coordenadas e índices.
   */
  onUpdateOccurrenceCoordinates($event: any): void {
    const { areaIndex, aspectIndex, occurrenceIndex, coordinates } = $event.data;

    // Acessar o FormArray de áreas
    const areasFormArray = this.areaForm.get('areas') as FormArray;

    // Acessar a área específica
    const areaFormGroup = areasFormArray.at(areaIndex) as FormGroup;

    // Acessar o FormArray de aspectos dentro da área
    const aspectsFormArray = areaFormGroup.get('aspects') as FormArray;

    // Acessar o aspecto específico
    const aspectFormGroup = aspectsFormArray.at(aspectIndex) as FormGroup;

    // Acessar o FormArray de ocorrências dentro do aspecto
    const occurrencesFormArray = aspectFormGroup.get('occurrences') as FormArray;

    // Acessar a ocorrência específica
    const occurrenceFormGroup = occurrencesFormArray.at(occurrenceIndex) as FormGroup;

    // Atualizar coordenadas
    occurrenceFormGroup.patchValue({
      northing: $event.coordinates.northing,
      easting: $event.coordinates.easting
    });
    this.formChanged.emit();
  }

  /**
   * Dispara o evento de salvamento de dados.
   * @param {object} event - Dados do evento de salvamento.
   */
  onSave(event: { action: string; userCompletedTheInspection: boolean }): void {
    this.saveTriggered.emit(event);
  }

  /**
   * Obtém as ocorrências vinculáveis a um determinado aspecto dentro de uma área.
   * @param {string} areaId - ID da área.
   * @param {string} aspectId - ID do aspecto.
   * @returns {any[]} - Lista de ocorrências vinculáveis.
   */
  getOccurrencesForAspect(areaId: string, aspectId: string): any[] {
    if (!this.occurrencesLinkable) {
      return [];
    }

    // Filtra as ocorrências que correspondem à área e ao aspecto
    return this.occurrencesLinkable.filter((occurrence) => occurrence.aspect.area.id === areaId && occurrence.aspect.id === aspectId);
  }

  /**
   * Atualiza uma ocorrência com base em eventos disparados.
   * @param {any} $event - Evento com a ação desejada.
   * @param {AbstractControl} area - Controle da área.
   * @param {number} aspectIndex - Índice do aspecto dentro da área.
   */
  onUpdateOccurrence($event, area: AbstractControl, aspectIndex: number): void {
    switch ($event.action) {
      case 'addOccurrence':
        this.onAddOccurrence(area, aspectIndex);
        break;
      case 'removeOccurrence':
        this.onRemoveOccurrence(area, aspectIndex, $event.index);
        break;
      case 'addOccurrenceAttachment':
        this.onAddOccurrenceAttachment();
        break;
      case 'addOccurrenceLinked':
        this.onAddOccurrenceLinked();
        break;
    }
  }

  /**
   * Dispara o evento de mudança no formulário.
   * @param {any} $event - Evento de mudança no formulário.
   */
  onFormChange($event) {
    this.formChanged.emit();
  }

  /**
   * Alterna entre habilitar ou desabilitar o formulário de áreas e aspectos.
   */
  toggleForm() {
    if (this.areaForm) {
      const isLocked = this.locked || [2, 3].includes(this.status);
      if (isLocked) {
        this.areaForm.disable();
      } else {
        this.areaForm.enable();
      }
    }
  }
}
