import { Injectable } from '@angular/core';
import { map, Observable, of } from 'rxjs';

import { DataService } from 'src/app/services/data.service';
import { SectionsService as SectionsServiceApi } from 'src/app/services/api/section.service';

import { DashboardService as DashboardServiceApi } from 'src/app/services/api/dashboard.service';

import fn from 'src/app/utils/function.utils';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  public fileDxf: any = null;
  public fileContent: string = '';
  public fileName: string = '';

  public messageSection: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = [];

  // Estado inicial do dataMapsStructure
  public dataMapsStructure = {
    height: '316px',
    width: '100%',
    zoom: 16,
    center: { lat: -17.930178, lng: -43.7908453 },
    options: {
      mapTypeId: 'satellite',
      zoomControl: true,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      maxZoom: 22,
      minZoom: 1
    },
    markers: [
      {
        position: {
          lat: -17.930178,
          lng: -43.7908453
        },
        title: '',
        options: {}
      }
    ],
    polylines: []
  };

  constructor(private sectionsServiceApi: SectionsServiceApi, private dashboardServiceApi: DashboardServiceApi, private dataService: DataService) {}

  /**
   * Obtém a lista de seções com base na estrutura selecionada e realiza ações de seleção/deseleção.
   *
   * @param {any} structure - Estrutura selecionada.
   * @param {string} [action='select'] - Ação a ser realizada (select ou deselect).
   */
  getSections(structure, action: string = 'select'): Observable<any> {
    if (action === 'select') {
      return this.sectionsServiceApi.getSectionList({ structureId: structure.id }).pipe(
        map((resp) => {
          let dados: any = resp;
          dados = dados.body === undefined ? dados : dados.body;
          return dados; // Retorna os dados da resposta
        })
      );
    } else {
      return of([]); // Retorna um Observable com uma lista vazia se a ação for "deselect"
    }
  }

  /**
   * Atualiza o centro e marcadores do mapa com base na estrutura selecionada e retorna o objeto dataMapsStructure atualizado.
   *
   * @param {string} clientId - ID do cliente
   * @param {string} structureId - ID da estrutura
   * @returns {Observable<any>} - Retorna um Observable com dataMapsStructure atualizado.
   */
  mapsStructure(clientId: string, structureId: string): Observable<any> {
    return this.dataService.getStructureCoordinate(clientId, [structureId]).pipe(
      map((coordinate) => {
        if (coordinate.length > 0) {
          // Atualiza as coordenadas no objeto dataMapsStructure
          this.dataMapsStructure.center.lat = coordinate[0].decimal_geodetic.latitude;
          this.dataMapsStructure.center.lng = coordinate[0].decimal_geodetic.longitude;
          this.dataMapsStructure.markers[0].position.lat = coordinate[0].decimal_geodetic.latitude;
          this.dataMapsStructure.markers[0].position.lng = coordinate[0].decimal_geodetic.longitude;
        }
        // Retorna o objeto dataMapsStructure atualizado
        return this.dataMapsStructure;
      })
    );
  }
}
