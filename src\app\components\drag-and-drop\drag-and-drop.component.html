<div class="drag-drop-container" cdkDropListGroup>
  <div
    [ngClass]="class"
    cdkDropList
    (cdkDropListDropped)="onDrop($event, orderField, 0)"
  >
    <ng-template ngFor let-item [ngForOf]="dataList" let-i="index">
      <a
        class="drag-item"
        cdkDrag
        [routerLink]="item.Rota ? [item.Rota] : []"
        *ngIf="!item.hasOwnProperty('Event') && item.Show"
      >
        <div class="drag-icon">
          <img
            *ngIf="item.IconeType === 'svg'"
            src="/assets/ico/ico-menu/{{ item.Icone }}"
          />
          <i *ngIf="item.IconeType !== 'svg'" [ngClass]="item.Icone"></i>
        </div>
        <span class="drag-text">{{ item[textField] }}</span>
      </a>

      <a
        class="drag-item"
        cdkDrag
        (click)="clickRowEvent(item.Event)"
        *ngIf="item.hasOwnProperty('Event') && item.Show"
      >
        <div class="drag-icon">
          <img
            *ngIf="item.IconeType === 'svg'"
            src="/assets/ico/ico-menu/{{ item.Icone }}"
          />
          <i *ngIf="item.IconeType !== 'svg'" [ngClass]="item.Icone"></i>
        </div>
        <span class="drag-text">{{ item[textField] }}</span>
      </a>
    </ng-template>
  </div>
</div>
