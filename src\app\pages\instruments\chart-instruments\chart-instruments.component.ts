import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-chart-instruments',
  templateUrl: './chart-instruments.component.html',
  styleUrls: ['./chart-instruments.component.scss']
})
export class ChartInstrumentsComponent implements OnInit {
  constructor(private activatedRoute: ActivatedRoute) {}

  public typeInstrument = null;

  ngOnInit(): void {
    this.typeInstrument = parseInt(this.activatedRoute.snapshot.queryParams.typeInstrument);
  }
}
