// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  googleMapsApi: 'AIzaSyBHwqGpAWW5fvjFe9o89EmKefvJqK4TJh8',

  apiUrl: [
    {
      id: 'client',
      url: 'https://localhost:5100/api/v1'
    },
    {
      id: 'user',
      url: 'https://localhost:5100/api/v1'
    }
  ],
  kcServer: 'https://logisoil.eastus2.cloudapp.azure.com/auth/realms/logisoil',
  kcAuth: 'https://logisoil.eastus2.cloudapp.azure.com/auth/realms/logisoil/.well-known/openid-configuration',
  kcId: 'logisoil-webapp',

  MenuAtivo: {
    Home: true,

    Notificações: true,
    Atividades: true,
    Dashboard: true,
    Documentacao: true,
    Estabilidade: true,
    GestaoClientes: true,
    GestaoEstruturas: true,
    GestaoUnidades: true,
    GestaoUsuarios: true,
    HistoricoNotificacoes: true,
    Imagens: true,
    Inspecoes: true,
    Instrumentacao: true,
    Investigacoes: true,
    Leituras: true,
    Materiais: true,
    Parametros: true,
    Perfil: true,
    Relatorios: true,
    Secoes: true,
    SolicitarRevisao: true,

    //Admin
    Clientes: true,
    Estruturas: true,
    HistoricoAtividades: true,
    Unidades: true,
    Usuarios: true,

    //Minidashboard
    Grafico: true,
    Imagem: true,
    Secao: true,
    Tabela: true,

    //Instrumentacao
    AdicionarInstrumento: true,
    BaixarInformacoes: true,
    EdicaoInstrumentoMassa: true,
    GruposInstrumentos: true,
    InserirLeituras: true,
    MapaInstrumentacao: true,
    CadastroInstrumentoPlanilha: true,
    GraficarDados: true,

    //Estabilidade
    Pacotes: true,
    Simulador: true,
    ImagensEstabilidade: true,
    MapaEstabilidade: true,
    FatoresDeSeguranca: true,
    GraficosEstabilidade: true
  }
};
