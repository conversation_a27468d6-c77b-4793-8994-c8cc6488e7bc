import { Injectable } from '@angular/core';

import { readingTableHeader } from '@pages/readings/form-readings/reading-header';
import { geoTableHeader } from '@pages/readings/form-readings/geo/geo.header';
import { inaPzTableHeader } from '@pages/readings/form-readings/ina-pz/ina-pz.header';
import { incConvTableHeader } from '@pages/readings/form-readings/inc-conv/inc-conv.header';
import { incIpiTableHeader } from '@pages/readings/form-readings/inc-ipi/inc-ipi.header';
import { mrTableHeader } from '@pages/readings/form-readings/mr/mr.header';
import { msPrTableHeader } from '@pages/readings/form-readings/ms-pr/ms-pr.header';
import { pzeTableHeader } from '@pages/readings/form-readings/pze/pze.header';
import { rlTableHeader } from '@pages/readings/form-readings/rl/rl.header';
import { beachTableHeader } from '@pages/readings/form-readings/praia/praia.header';
import { pluviometroTableHeader } from '@pages/readings/form-readings/pluviometro/pluviometro.header';
import { pluviografoTableHeader } from '@pages/readings/form-readings/pluviografo/pluviografo.header';

import * as moment from 'moment';

import { Datum } from 'src/app/constants/app.constants';
import fn from 'src/app/utils/function.utils';
import * as _ from 'lodash';

@Injectable({
  providedIn: 'root'
})
export class TableHeaderService {
  public datum: any = Datum;

  constructor() {}

  getTableHeaderReading(typeInstrumentId) {
    typeInstrumentId = typeof typeInstrumentId == 'string' ? parseInt(typeInstrumentId) : typeInstrumentId;
    let header = [];
    let itemToChange = null;
    switch (typeInstrumentId) {
      case 1: //INA
        header = inaPzTableHeader;

        // Usar find para localizar o objeto e modificar o label diretamente
        itemToChange = header.find((item) => item.label === 'Cota Piezométrica (m)');
        if (itemToChange) {
          itemToChange.label = 'Cota NA (m)';
        }
        break;
      case 2: //PZ
        header = inaPzTableHeader;

        // Usar find para localizar o objeto e modificar o label diretamente
        itemToChange = header.find((item) => item.label === 'Cota NA (m)');
        if (itemToChange) {
          itemToChange.label = 'Cota Piezométrica (m)';
        }

        break;
      case 3: //PZE
        header = pzeTableHeader;
        break;
      case 4: //Inclinometro convencional
        header = incConvTableHeader;
        break;
      case 5: //IPI
        header = incIpiTableHeader;
        break;
      case 6: //MS
      case 7: //PR
        header = msPrTableHeader;
        break;
      case 8: //Medidor de recalque
        header = mrTableHeader;
        break;
      case 9: //Geofone
        header = geoTableHeader;
        break;
      case 10: //Regua linimetrica
        header = rlTableHeader;
        break;
      case 11: //Comprimento de praia
        header = beachTableHeader;
        break;
      case 12: //Pluviometro
        header = pluviometroTableHeader;
        break;
      case 13: //Pluviografo
        header = pluviografoTableHeader;
        break;
      default:
        header = readingTableHeader;
        break;
    }
    return header;
  }

  formatDataReading(data: any, typeInstrumentId: any) {
    return data.map((item: any, index: number) => {
      return this.formatByTypeInstrument(typeInstrumentId, item);
    });
  }

  formatByTypeInstrument(typeInstrumentId: any, item: any) {
    typeInstrumentId = typeof typeInstrumentId == 'string' ? parseInt(typeInstrumentId) : typeInstrumentId;
    let itemReading = _.cloneDeep(item);
    switch (typeInstrumentId) {
      case 1: //INA
      case 2: //PZ
      case 3: //PZE
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['dry'] = item.dry ? 'Sim' : 'Não';
        itemReading['quota'] = item.quota ? parseFloat(item.quota).toFixed(3) : '-';
        itemReading['depth'] = item.depth ? parseFloat(item.depth).toFixed(3) : '-';
        itemReading['pressure'] = item.pressure ? parseFloat(item.pressure) : '-';
        itemReading['extra'] = item.instrument_linked_to_any_section
          ? { identifier: '<i class="fa-solid fa-diamond color-green" title="Instrumento associado a uma seção"></i>' }
          : {};
        itemReading['measure'] = item.measurement && item.measurement.identifier ? item.measurement.identifier : '-';
        break;
      case 4: //INC-CONV
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['depth'] = item.depth ? parseFloat(item.depth).toFixed(3) : '-';
        itemReading['positive_a'] = item.positive_a ? parseFloat(item.positive_a).toFixed(3) : '-';
        itemReading['negative_a'] = item.negative_a ? parseFloat(item.negative_a).toFixed(3) : '-';
        itemReading['positive_b'] = item.positive_b ? parseFloat(item.positive_b).toFixed(3) : '-';
        itemReading['negative_b'] = item.negative_b ? parseFloat(item.negative_b).toFixed(3) : '-';
        itemReading['average_displacement_a'] = item.average_displacement_a ? parseFloat(item.average_displacement_a).toFixed(3) : '-';
        itemReading['average_displacement_b'] = item.average_displacement_b ? parseFloat(item.average_displacement_b).toFixed(3) : '-';
        itemReading['accumulated_displacement_a'] = item.accumulated_displacement_a ? parseFloat(item.accumulated_displacement_a).toFixed(3) : '-';
        itemReading['accumulated_displacement_b'] = item.accumulated_displacement_b ? parseFloat(item.accumulated_displacement_b).toFixed(3) : '-';
        itemReading['deviation_a'] = item.deviation_a ? parseFloat(item.deviation_a).toFixed(3) : '-';
        itemReading['deviation_b'] = item.deviation_b ? parseFloat(item.deviation_b).toFixed(3) : '-';
        itemReading['measure'] = item.measurement && item.measurement.identifier ? item.measurement.identifier : '-';
        itemReading['is_referential_reading'] = item.is_referential_reading == null ? '-' : item.is_referential_reading == true ? 'Sim' : 'Não';
        break;
      case 5: //IPI
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['depth'] = item.depth ? parseFloat(item.depth).toFixed(3) : '-';
        itemReading['a_axis_reading'] = item.a_axis_reading ? parseFloat(item.a_axis_reading).toFixed(3) : '-';
        itemReading['b_axis_reading'] = item.b_axis_reading ? parseFloat(item.b_axis_reading).toFixed(3) : '-';
        itemReading['average_displacement_a'] = item.average_displacement_a ? parseFloat(item.average_displacement_a).toFixed(3) : '-';
        itemReading['average_displacement_b'] = item.average_displacement_b ? parseFloat(item.average_displacement_b).toFixed(3) : '-';
        itemReading['accumulated_displacement_a'] = item.accumulated_displacement_a ? parseFloat(item.accumulated_displacement_a).toFixed(3) : '-';
        itemReading['accumulated_displacement_b'] = item.accumulated_displacement_b ? parseFloat(item.accumulated_displacement_b).toFixed(3) : '-';
        itemReading['deviation_a'] = item.deviation_a ? parseFloat(item.deviation_a).toFixed(3) : '-';
        itemReading['deviation_b'] = item.deviation_b ? parseFloat(item.deviation_b).toFixed(3) : '-';
        itemReading['measure'] = item.measurement && item.measurement.identifier ? item.measurement.identifier : '-';
        itemReading['is_referential_reading'] = item.is_referential_reading == null ? '-' : item.is_referential_reading == true ? 'Sim' : 'Não';
        break;
      case 6: //MS
      case 7: //PR
        this.datum = fn.findIndexInArrayofObject(Datum, 'id', item.datum, 'id', true);

        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['quota'] = item.quota ? parseFloat(item.quota).toFixed(3) : '-';
        itemReading['datum'] = this.datum.value;
        itemReading['east_coordinate'] = item.east_coordinate ? parseFloat(item.east_coordinate).toFixed(6) : '-';
        itemReading['north_coordinate'] = item.north_coordinate ? parseFloat(item.north_coordinate).toFixed(6) : '-';
        itemReading['east_displacement'] = item.east_displacement ? parseFloat(item.east_displacement).toFixed(3) : '-';
        itemReading['north_displacement'] = item.north_displacement ? parseFloat(item.north_displacement).toFixed(3) : '-';
        itemReading['z_displacement'] = item.z_displacement ? parseFloat(item.z_displacement).toFixed(3) : '-';
        itemReading['total_planimetric_displacement'] = item.total_planimetric_displacement ? parseFloat(item.total_planimetric_displacement).toFixed(3) : '-';
        itemReading['a_displacement'] = item.a_displacement ? parseFloat(item.a_displacement).toFixed(3) : '-';
        itemReading['b_displacement'] = item.b_displacement ? parseFloat(item.b_displacement).toFixed(3) : '-';
        itemReading['is_referential_reading'] = item.is_referential_reading == null ? '-' : item.is_referential_reading == true ? 'Sim' : 'Não';
        break;
      case 8: //MR
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['quota'] = item.quota ? parseFloat(item.quota).toFixed(3) : '-';
        itemReading['absolute_settlement'] = item.absolute_settlement ? parseFloat(item.absolute_settlement).toFixed(3) : '-';
        itemReading['relative_settlement'] = item.relative_settlement ? parseFloat(item.relative_settlement).toFixed(3) : '-';
        itemReading['absolute_depth'] = item.depth ? parseFloat(item.depth).toFixed(3) : '-';
        itemReading['relative_depth'] = item.relative_depth ? parseFloat(item.relative_depth).toFixed(3) : '-';
        itemReading['measure'] = item.measurement && item.measurement.identifier ? item.measurement.identifier : '-';
        itemReading['delta_ref'] = item.delta_ref ? parseFloat(item.delta_ref).toFixed(2) : '-';
        break;
      case 9: //GEO
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['nature'] = item.nature && item.nature.description ? item.nature.description : '-';
        itemReading['a_axis_pga'] = item.a_axis_pga ? parseFloat(item.a_axis_pga).toFixed(3) : '-';
        itemReading['b_axis_pga'] = item.b_axis_pga ? parseFloat(item.b_axis_pga).toFixed(3) : '-';
        itemReading['z_axis_pga'] = item.z_axis_pga ? parseFloat(item.z_axis_pga).toFixed(3) : '-';
        itemReading['east_coordinate'] = item.east_coordinate ? parseFloat(item.east_coordinate).toFixed(6) : '-';
        itemReading['north_coordinate'] = item.north_coordinate ? parseFloat(item.north_coordinate).toFixed(6) : '-';
        break;
      case 10: //RL
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['quota'] = item.quota ? parseFloat(item.quota).toFixed(2) : '-';
        break;
      case 11: //Comprimento de praia
        itemReading['id'] = item.id;
        itemReading['structure_name'] = item.structure.name;
        itemReading['section_name'] = item.section.name;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['length'] = item.length ? parseFloat(item.length).toFixed(2) : '-';
        break;
      case 12: //Pluviômetro
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['pluviometry'] = item.pluviometry ? parseFloat(item.pluviometry) : '-';
        break;
      case 13: //Pluviógrafo
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['pluviometry'] = item.pluviometry ? parseFloat(item.pluviometry) : '-';
        itemReading['intensity'] = item.intensity ? parseFloat(item.intensity) : '-';
        break;
      default:
        itemReading['id'] = item.reading_id;
        itemReading['identifier'] = item.instrument.identifier;
        itemReading['date_format'] = moment(item.date).format('DD/MM/YYYY HH:mm:ss');
        itemReading['dry'] = item.dry == null ? '-' : item.dry == true ? 'Sim' : 'Não';
        itemReading['is_referential_reading'] = item.is_referential_reading == null ? '-' : item.is_referential_reading == true ? 'Sim' : 'Não';
        itemReading['quota'] = item.quota ? parseFloat(item.quota).toFixed(3) : '-';
        itemReading['depth'] = item.depth ? parseFloat(item.depth).toFixed(3) : '-';
        itemReading['pressure'] = item.pressure ? parseFloat(item.pressure) : '-';
        itemReading['extra'] =
          item.instrument_linked_to_any_section && item.instrument.type <= 3
            ? { identifier: '<i class="fa-solid fa-diamond color-green" title="Instrumento associado a uma seção"></i>' }
            : {};
        break;
    }

    if (item.removed_from_last_package) {
      if (itemReading['extra'] && itemReading['extra'].identifier) {
        itemReading['extra'].identifier = '<i class="fa fa-circle-thin color-black" title="Instrumento removido do último pacote"></i>';
      } else if ((itemReading['extra'] && !itemReading['extra'].identifier) || !itemReading['extra']) {
        itemReading['extra'] = { identifier: '<i class="fa fa-circle-thin color-black" title="Instrumento removido do último pacote"></i>' };
      }
    }

    itemReading['select_id'] = item.id;
    itemReading['select'] = false;
    itemReading['type_instrument'] = item.instrument && item.instrument.type ? item.instrument.type : '';

    return itemReading;
  }
}
