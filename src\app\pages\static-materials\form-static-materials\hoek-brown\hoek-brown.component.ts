import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import fn from 'src/app/utils/function.utils';

import { FormService } from 'src/app/services/form.service';

@Component({
  selector: 'app-hoek-brown',
  templateUrl: './hoek-brown.component.html',
  styleUrls: ['./hoek-brown.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HoekBrownComponent implements OnInit, OnChanges {
  @Input() public data: any = null;
  @Input() public view: boolean = false;

  public formHoekBrown: FormGroup = new FormGroup({
    ucs_intact: new FormControl('', [Validators.required]),
    m: new FormControl('', [Validators.required]),
    s: new FormControl('', [Validators.required])
  });

  public func = fn;

  constructor(public formService: FormService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }
  }

  splitData($dados) {
    this.formHoekBrown.controls['ucs_intact'].setValue($dados.ucs_intact.toFixed(2));
    this.formHoekBrown.controls['m'].setValue($dados.m.toFixed(2));
    this.formHoekBrown.controls['s'].setValue($dados.s.toFixed(2));

    if (this.view) {
      this.formHoekBrown.disable();
    }
  }

  validate() {
    let formValid = this.formService.validateForm(this.formHoekBrown);

    if (!formValid) {
      this.formHoekBrown.markAllAsTouched();
    }

    return formValid;
  }
}
