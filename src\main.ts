import { enableProdMode, <PERSON>rror<PERSON>and<PERSON> } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

if (environment.production) {
  enableProdMode();
}

// Handler customizado para silenciar ExpressionChangedAfterItHasBeenCheckedError
class SuppressExpressionChangedErrorHandler implements ErrorHandler {
  handleError(error: any): void {
    const errorMessage = error?.toString?.();
    console.log(errorMessage);

    if (errorMessage?.includes('ExpressionChangedAfterItHasBeenCheckedError')) {
      // Silencia esse warning específico
      return;
    }

    // Loga os demais normalmente
    console.error(error);
  }
}

platformBrowserDynamic()
  .bootstrapModule(AppModule, {
    providers: [{ provide: ErrorHandler, useClass: SuppressExpressionChangedErrorHandler }]
  })
  .catch((err) => console.error(err));

// Silencia warnings sobre '[disabled]' com 'formControlName' do Angular
const originalWarn = console.warn;
console.warn = (...args: any[]) => {
  const msg = args[0];
  if (typeof msg === 'string' && msg.includes("you're using the disabled attribute with a reactive form directive")) {
    return; // Ignora esse warning
  }
  originalWarn(...args);
};
