import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { MultiSelectDefault } from 'src/app/constants/app.constants';

import { StabilityService as StabilityServiceApi } from 'src/app/services/api/stability.service';

import fn from 'src/app/utils/function.utils';
import * as moment from 'moment';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-modal-chart-fs',
  templateUrl: './modal-chart-fs.component.html',
  styleUrls: ['./modal-chart-fs.component.scss']
})
export class ModalChartFsComponent implements OnInit, OnChanges {
  @ViewChild('modalChartFS') ModalChartFS: ElementRef;
  @Input() public title: string = '';
  @Input() public sectionResult: any = {};
  @Input() public selectedItem: any = null;

  @Output() modalClosed = new EventEmitter<void>();

  readonly constSurfacesAndCondition = [
    { id: 1, name: 'Circular Drenada' },
    { id: 2, name: 'Circular Não Drenada' },
    { id: 3, name: 'Circular Pseudo Estática' },
    { id: 4, name: 'Nâo Circular Drenada' },
    { id: 5, name: 'Nâo Circular Não Drenada' },
    { id: 6, name: 'Nâo Circular Pseudo Estática' }
  ];

  public formSafetyFactor: FormGroup = new FormGroup({
    SurfaceAndConditions: new FormControl(''),
    CreatedDate: new FormControl({ value: '', disabled: true })
  });

  public message: any = { text: '', status: false, class: 'alert-success' };
  public messagesError: any = null;

  public singleSettings = MultiSelectDefault.Single;

  public surfaceAndCondition = [];
  public createdDate = [];

  public imagesUrl: any = null;
  public ctrlShowImage: boolean = false;
  public imagesFiles: any = {
    DXF: {
      file: null,
      fileContent: '',
      fileName: '',
      fileContentDownload: '',
      fileURL: ''
    },
    PNG: {
      file: null,
      fileContent: '',
      fileName: '',
      fileContentDownload: '',
      fileURL: ''
    },
    ZIP: {
      file: null,
      fileContent: '',
      fileName: '',
      fileContentDownload: '',
      fileURL: ''
    }
  };

  constructor(private modalService: NgbModal, private sanitizer: DomSanitizer, private stabilityServiceApi: StabilityServiceApi) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.selectedItem && changes.selectedItem.currentValue != null) {
      const sliIFileType = changes.selectedItem.currentValue.safety_factor_result.safetyResult.sli_file_type;
      this.setFormValuesFromSelectedItem(sliIFileType, true);
    }
  }

  // Define os valores do formulário a partir do item selecionado.
  setFormValuesFromSelectedItem(sliIFileType: any = null, load: boolean = false): void {
    const uniqueSliFileTypes = [...new Set(this.sectionResult.safety_factor_results.map((item: any) => item.sli_file_type))];
    this.surfaceAndCondition = this.constSurfacesAndCondition.filter((item: any) => uniqueSliFileTypes.includes(item.id));

    this.formSafetyFactor.get('SurfaceAndConditions').setValue(sliIFileType);

    if (this.surfaceAndCondition.length > 0) {
      this.createdDate = this.extractDate(sliIFileType);
      if (this.createdDate.length > 0) {
        this.formSafetyFactor.get('CreatedDate').enable();

        const index = this.sectionResult.safety_factor_results.findIndex(
          (item: any) => item.safety_factor_id === this.selectedItem.safety_factor_result.safetyResult.safety_factor_id
        );

        if (this.selectedItem && load) {
          this.formSafetyFactor.get('CreatedDate').setValue(index);
          this.showImage(index);
        }
      }
    }
  }

  // Método extractData que filtra os reading_created_date com base no sli_file_type
  extractDate(id: number) {
    // Filtrando os reading_created_date onde sli_file_type é igual ao fornecido
    const filteredDates = this.sectionResult.safety_factor_results
      .map((item: any, index: number) => {
        // Apenas filtra os itens que possuem o sli_file_type igual ao id
        if (item.sli_file_type === id) {
          const createdDate = moment(item.reading_created_date).format('DD/MM/YYYY HH:mm:ss');
          return { id: index, date_format: createdDate }; // Retorna o índice original e a data formatada
        }
        return null; // Retorna null se não corresponder
      })
      .filter((item) => item !== null); // Filtra os nulls gerados para os itens que não corresponderam
    return filteredDates;
  }

  showImage($index) {
    this.ctrlShowImage = false;
    this.imagesUrl = this.sectionResult.safety_factor_results[$index];
    if (this.imagesUrl !== null) {
      this.ctrlShowImage = true;
      this.getFilePNGorDXF(this.imagesUrl.dxf_file_url, 'DXF');
    }
  }

  /**
   * Carrega o desenho a partir de um base64 e define os atributos do arquivo.
   *
   * @param {any} drawing - O desenho em base64.
   * @param {string} [type=''] - O tipo de arquivo.
   */
  loadDrawing(drawing, type = '') {
    if (drawing != null) {
      const blob = fn.base64toBlob(drawing.base64);
      const url = window.URL.createObjectURL(blob);
      this.imagesFiles[type].fileURL = url;
      this.imagesFiles[type].fileContent = drawing.base64;
      this.imagesFiles[type].fileContentDownload = this.sanitizer.bypassSecurityTrustResourceUrl(
        'data:application/octet-stream;base64,' + this.imagesFiles[type].fileContent
      );
      this.imagesFiles[type].fileName = drawing.name;

      this.imagesFiles[type].file = fn.base64ToFile(this.imagesFiles[type].fileContent, this.imagesFiles[type].fileName);
    }
  }

  /**
   * Baixa o arquivo do tipo especificado.
   *
   * @param {string} type - O tipo de arquivo a ser baixado.
   */
  downloadFile(type) {
    if (this.imagesFiles[type].fileName != '') {
      this.forceDownload(this.imagesFiles[type].fileURL, this.imagesFiles[type].fileName);
    } else {
      switch (type) {
        case 'DXF':
        case 'PNG':
          this.getFilePNGorDXF(this.imagesUrl[type.toLowerCase() + '_file_url'], type, true);
          break;
        case 'ZIP':
          this.getFileZip(this.imagesUrl['zip_file_download_url']);
          break;
      }
    }
  }

  /**
   * Força o download de um arquivo com um nome especificado.
   *
   * @param {any} file - O arquivo a ser baixado.
   * @param {string} name - O nome do arquivo.
   * @returns {void}
   */
  forceDownload(file, name) {
    const link: any = document.createElement('a');
    link.href = file;
    link.download = name;

    document.body.appendChild(link);

    link.click();

    document.body.removeChild(link);
  }

  /**
   * Obtém um arquivo PNG ou DXF a partir de uma URL.
   *
   * @param {string} url - A URL do arquivo.
   * @param {string} [type='DXF'] - O tipo de arquivo (DXF ou PNG).
   * @param {boolean} [download=false] - Indica se o arquivo deve ser baixado.
   */
  getFilePNGorDXF(url, type = 'DXF', download = false) {
    this.message.text = '';
    this.message.status = false;

    url = url.replace('api/v1', '');

    this.stabilityServiceApi.getSafetyFactorId('', {}, url).subscribe((resp) => {
      let dados: any = resp;
      dados = dados.body === undefined ? dados : dados.body;
      this.loadDrawing(dados, type);
      if (download === true) {
        this.downloadFile(type);
      }
    });
  }

  /**
   * Obtém um arquivo ZIP a partir de uma URL e força o download.
   *
   * @param {string} url - A URL do arquivo ZIP.
   */
  getFileZip(url) {
    url = url.replace('api/v1', '');
    this.stabilityServiceApi.getStabilityAnalysisZipFile(url).subscribe((resp: any) => {
      if (resp['status'] == 200) {
        let dados: any = resp;
        dados = dados.body === undefined ? dados : dados.body;
        this.imagesFiles['ZIP'].file = new Blob([resp['body']], { type: resp['body'].type });
        const blob = new Blob([resp['body']], { type: resp['body'].type });
        const url = window.URL.createObjectURL(blob);
        this.imagesFiles['ZIP'].fileURL = url;
        this.imagesFiles['ZIP'].fileName = 'filename.zip';
        this.forceDownload(url, 'filename.zip');
      }
    });
  }

  reset($resetForm = false) {
    this.imagesUrl = null;
    this.ctrlShowImage = false;
    this.imagesFiles = {
      DXF: {
        file: null,
        fileContent: '',
        fileName: '',
        fileContentDownload: '',
        fileURL: ''
      },
      PNG: {
        file: null,
        fileContent: '',
        fileName: '',
        fileContentDownload: '',
        fileURL: ''
      },
      ZIP: {
        file: null,
        fileContent: '',
        fileName: '',
        fileContentDownload: '',
        fileURL: ''
      }
    };

    if ($resetForm) {
      this.createdDate = [];
      this.surfaceAndCondition = [];
      this.formSafetyFactor.get('CreatedDate').disable();
      this.formSafetyFactor.get('CreatedDate').setValue('');
      this.formSafetyFactor.get('SurfaceAndConditions').setValue('');
    }
  }

  /**
   * Abre o modal do fator de segurança.
   */
  openModal() {
    this.modalService.open(this.ModalChartFS, { size: 'xl' });
  }

  /**
   * Fecha o modal do fator de segurança e reseta os valores.
   */
  closeModal(): void {
    this.reset(true);
    this.modalClosed.emit();
  }
}
