<div class="inspections-container">
  <div class="row mt-2 mb-3">
    <div class="col-12">
      <div class="btn-group" role="group">
        <button
          type="button"
          class="btn btn-group-blue"
          (click)="changeTab('fichasDeInspecao')"
          [ngClass]="{ selected: activeTab === 'fichasDeInspecao' }"
        >
          Fichas de inspeção
        </button>
        <button
          type="button"
          class="btn btn-group-blue"
          (click)="changeTab('ocorrencias')"
          [ngClass]="{ selected: activeTab === 'ocorrencias' }"
        >
          Ocorrências
        </button>
        <button
          type="button"
          class="btn btn-group-blue"
          (click)="changeTab('planosDeAcao')"
          [ngClass]="{ selected: activeTab === 'planosDeAcao' }"
        >
          Planos de ação
        </button>
      </div>
    </div>
  </div>
  <div class="row" *ngIf="activeTab === 'fichasDeInspecao'">
    <app-inspection-sheet></app-inspection-sheet>
  </div>
  <div class="row" *ngIf="activeTab === 'ocorrencias'">
    <app-occurrences>Ocorrências</app-occurrences>
  </div>
  <div class="row" *ngIf="activeTab === 'planosDeAcao'">
    <app-action-plans>Planos de ação</app-action-plans>
  </div>

  <div class="row mt-3 mb-3">
    <!-- Botão Voltar -->
    <div class="col-md-12 d-flex align-items-end justify-content-end">
      <app-button
        [class]="'btn-logisoil-blue'"
        [icon]="'fa fa-arrow-left'"
        [label]="'Voltar à tela inicial'"
        [click]="goBack.bind(this)"
      ></app-button>
    </div>
  </div>
</div>
