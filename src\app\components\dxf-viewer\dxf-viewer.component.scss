.list-content {
  box-sizing: border-box;
  border: rgba(0, 0, 0, 0.3) 1px solid;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 0 10px;
}

.root-layers {
  height: 550px;
  width: 300px;
  overflow-y: auto;
  overflow-x: auto;
  white-space: nowrap;
}

.layer-item {
  cursor: pointer;
}

.layer-item:hover {
  background-color: rgba($color: #000000, $alpha: 0.15);
}

.canvasContainer {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 550px;
  min-height: 550px;
}

.button-client {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}

.form-label {
  color: #34b575;
  font-family: averta-bold;
  font-size: 0.875em;
}

.periods {
  background-color: #34b575;
  color: #ffffff;
  font-size: 0.875em;
}

.form-control {
  border-color: #d4d2d2;
  font-size: 0.875em;
}

.form-select {
  font-size: 0.875em !important;
  line-height: 1.52857143 !important;
}
