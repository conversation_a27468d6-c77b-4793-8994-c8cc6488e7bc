<div *ngIf="isProgressVisible" class="progress-container">
  <div
    class="progress-bar"
    [style.width.%]="progress"
    [style.background-color]="progressColor"
  ></div>
</div>
<div
  *ngIf="isProgressVisible"
  class="progress-info d-flex justify-content-center"
>
  <p *ngIf="message && message.trim() !== ''" class="progress-message">
    {{ message }}&nbsp;
  </p>
  <p class="progress-counter" style="width: 40px">{{ displayTime }} s</p>
</div>

<button
  *ngIf="showButton"
  class="reload-button"
  [disabled]="(!reverse && progress < 100) || (reverse && progress > 0)"
  (click)="reload()"
>
  {{
    (!reverse && progress < 100) || (reverse && progress > 0)
      ? 'Waiting...'
      : 'Reload'
  }}
</button>
