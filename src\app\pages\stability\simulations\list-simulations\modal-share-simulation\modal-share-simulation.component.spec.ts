import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalShareSimulationComponent } from './modal-share-simulation.component';

describe('ModalShareSimulationComponent', () => {
  let component: ModalShareSimulationComponent;
  let fixture: ComponentFixture<ModalShareSimulationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalShareSimulationComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalShareSimulationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
