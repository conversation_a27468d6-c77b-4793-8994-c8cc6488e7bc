<ng-template #modalInsertAspect let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title">
      Utilize esta ferramenta para adicionar um aspecto não-padrão desta
      estrutura.
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <div class="modal-body">
    <div class="form-group" [formGroup]="formModal">
      <label for="areaSelect" class="form-label">Áreas:</label>
      <select
        id="areaSelect"
        formControlName="area"
        class="form-select custom-select"
        (change)="onAreaChange($event)"
      >
        <option *ngFor="let area of filteredAreasList" [value]="area.id">
          {{ area.name }}
        </option>
      </select>
      <label for="aspectSelect" class="form-label mt-2"
        >Aspectos disponíveis para inserção:</label
      >
      <select
        id="aspectSelect"
        formControlName="aspect"
        class="form-select custom-select"
        *ngIf="!message.status"
      >
        <option *ngFor="let aspect of filteredAspectsList" [value]="aspect.id">
          {{ aspect.description }}
        </option>
      </select>
      <div
        class="col-md-12 mt-2 alert"
        [ngClass]="message.class"
        role="alert"
        *ngIf="message.status"
      >
        {{ message.text }}
      </div>
    </div>
    <p class="info-text mt-2">
      Necessita inserir um mesmo aspecto em todas as fichas? Informe ao suporte
      Walm, para que ele seja inserido de forma permanente em todas as fichas
      futuras desta estrutura.
    </p>
  </div>

  <!-- Botões -->
  <div class="modal-footer">
    <app-button
      [class]="'btn-logisoil-green'"
      [label]="'Inserir novo aspecto'"
      [icon]="'fas fa-plus-circle'"
      [type]="false"
      *ngIf="!message.status"
      (click)="insertAspect()"
    >
    </app-button>
    <app-button
      [class]="'btn-logisoil-red'"
      [label]="'Cancelar'"
      (click)="c('Close click')"
    >
    </app-button>
  </div>
</ng-template>
