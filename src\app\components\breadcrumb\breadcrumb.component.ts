import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { GlobalService } from 'src/app/services/global.service';
import { UserService } from 'src/app/services/user.service';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss']
})
export class BreadcrumbComponent implements OnInit {
  @Output() startTour = new EventEmitter<void>();

  public titulo: any = {
    titulo: 'Home',
    acao: '',
    acaoIcone: '',
    complemento: '',
    icone: 'fa fa-icon fas fa-home',
    tipoIcone: '',
    routerLink: '/',
    subTitulos: []
  };

  constructor(private globalService: GlobalService, private userService: UserService) {
    this.globalService.getTitle().subscribe((titulo) => {
      if (titulo !== undefined) {
        this.titulo = titulo;
      }
    });

    this.userService.getPermission().subscribe(() => {});
  }

  ngOnInit(): void {
    this.globalService.setTitle();
    this.userService.setPermission();
  }

  initTour(): void {
    this.startTour.emit(); // Emite o evento para o componente pai
  }
}
