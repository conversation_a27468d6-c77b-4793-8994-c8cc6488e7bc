<div class="list-content pb-4">
  <form [formGroup]="formFilter">
    <div class="row g-3 mt-1">
      <app-hierarchy
        #hierarchy
        [elements]="elements"
        class="col-md-9"
        (sendEventHierarchy)="getEventHierarchy($event)"
      ></app-hierarchy>
      <!-- Seção -->
      <div class="col-md-3">
        <label class="form-label">Seção</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="sectionSettings"
          [data]="sections"
          formControlName="SectionId"
          (onSelect)="getInstruments($event, 'select')"
          (onDeSelect)="getInstruments($event, 'deselect')"
        >
        </ng-multiselect-dropdown>
      </div>
    </div>
    <div class="row mt-1">
      <!-- Subtipo -->
      <div class="col-md-3">
        <label class="form-label">Subtipo</label>
        <select
          class="form-select"
          formControlName="Subtype"
          (change)="filterSubtypes()"
        >
          <option value="">Selecione...</option>
          <option *ngFor="let item of subTypes" [ngValue]="item.id">
            {{ item.value }}
          </option>
        </select>
      </div>
      <!-- Instrumento -->
      <div class="col-md-3">
        <label class="form-label">Instrumento</label>
        <ng-multiselect-dropdown
          [placeholder]="'Selecione...'"
          [settings]="instrumentsSettings"
          [data]="instrumentsBySubtype"
          formControlName="InstrumentId"
        >
        </ng-multiselect-dropdown>
      </div>
      <div class="col-md-3 d-flex align-items-end" *ngIf="ctrlSearch">
        <app-button
          [class]="'btn-logisoil-blue'"
          [icon]="'fa fa-search'"
          [label]="'Buscar'"
          class="me-1"
          (click)="searchImages()"
        ></app-button>
        <app-button
          [class]="'btn-logisoil-gray'"
          [icon]="'fa fa-eraser'"
          [label]="'Limpar'"
          (click)="resetFilter()"
        ></app-button>
      </div>
    </div>

    <!-- Galeria de imagens -->
    <div class="row mt-3">
      <div class="col-md-12">
        <app-images
          [imagesItens]="imagesItens"
          [message]="message"
          [uploadActive]="false"
          #appImages
        ></app-images>
      </div>
    </div>

    <div class="row mt-3">
      <!-- Botão Voltar -->
      <div class="col-md-12 d-flex align-items-end justify-content-end mb-3">
        <app-button
          [class]="'btn-logisoil-blue'"
          [icon]="'fa fa-arrow-left'"
          [label]="'Voltar à tela inicial'"
          [click]="goBack.bind(this)"
        ></app-button>
      </div>
    </div>
  </form>
</div>

<ngx-spinner
  bdColor="rgba(51,51,51,0.8)"
  size="medium"
  color="#34b575"
  type="ball-clip-rotate-pulse"
  [fullScreen]="true"
  ><p class="mt-1" style="font-size: 18px; color: white">
    Carregando...
  </p></ngx-spinner
>
