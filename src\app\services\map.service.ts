import { Injectable } from '@angular/core';
import { ApiService } from './api/api.service';

@Injectable({
  providedIn: 'root'
})
export class MapsService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  // Busca dados para Mapa de estabilidade por ID
  getMapsStabilityById(id: string) {
    const url = `/maps/stability/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  postMapsStabilityConfiguration(params: any) {
    const url = '/maps/stability/stability-map-configuration';
    return this.api.post<any>(url, params, {}, 'client');
  }

  getMapsDisplacement(params: any) {
    const url = `/maps/displacement`;
    return this.api.get<any>(url, params, false, 'client');
  }

  getMapsPercolation(params: any) {
    const url = `/maps/percolation`;
    return this.api.get<any>(url, params, false, 'client');
  }

  postPercolationAbsoluteVariationColor(params: any) {
    const url = '/maps/percolation/absolute-variation-color';
    return this.api.post<any>(url, params, {}, 'client');
  }

  postDisplacementMapConfiguration(params: any) {
    const url = '/maps/displacement/displacement-map-configuration';
    return this.api.post<any>(url, params, {}, 'client');
  }

  getURLKml(params: any = {}, url: string = '') {
    return this.api.get<any>(url, params, true, 'client');
  }
}
