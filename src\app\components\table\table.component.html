<div class="table-responsive-md">
  <table
    class="table table-bordered table-hover align-middle"
    *ngIf="!messageReturn.status"
  >
    <thead class="table-light">
      <tr>
        <ng-container *ngFor="let column of tableHeader; let i = index">
          <th
            scope="col"
            [style.max-width]="column.width"
            [style.min-width]="column.width"
            [style.width]="column.width"
            [attr.rowspan]="column.rowspan"
            [attr.colspan]="column.colspan"
            style="vertical-align: middle"
            *ngIf="column.show"
          >
            <div *ngIf="!column.menu">
              {{ column.label }}
            </div>
            <div *ngIf="column.menu">
              <div
                [ngClass]="column.sort ? 'pointer' : ''"
                (click)="tableHeader[i].sort = !tableHeader[i].sort"
                style="position: relative"
              >
                {{ column.label }}
                <i
                  [ngClass]="
                    column.sort ? 'fa fa-caret-down' : 'fa fa-caret-up'
                  "
                  *ngIf="column.hasOwnProperty('sort')"
                ></i>
                <div
                  *ngIf="column.hasOwnProperty('sort') && !column.sort"
                  class="th-component"
                  (click)="$event.stopPropagation()"
                >
                  <app-th-absolute-variation
                    *ngIf="column.referent[0] === 'absolute_variation'"
                    [filter]="filterHeader"
                    (sendClickEvent)="clickHeaderEvent($event)"
                  ></app-th-absolute-variation>
                </div>
              </div>
            </div>
          </th>
        </ng-container>
      </tr>
      <tr>
        <ng-container *ngFor="let column of tableSubheader">
          <th scope="col" *ngIf="column.show" class="text-center align-middle">
            {{ column.label }}
          </th>
        </ng-container>
      </tr>
    </thead>
    <tbody>
      <ng-template ngFor let-data [ngForOf]="tableData" let-tdidx="index">
        <tr
          [style.cursor]="eventRow ? 'pointer' : ''"
          (click)="
            eventRow &&
              clickRowEvent($event, {
                action: 'clickedRow',
                index: tdidx,
                routerLink: routerLink
              })
          "
        >
          <ng-template ngFor let-row [ngForOf]="tableHeader" let-i="index">
            <ng-template
              ngFor
              let-column
              [ngForOf]="row.referent"
              let-j="index"
            >
              <!-- Nova abordagem -->
              <ng-container *ngIf="row.show">
                <ng-container
                  *ngTemplateOutlet="
                    cellTemplate;
                    context: {
                      $implicit: getCellType(row, column, data),
                      row: row,
                      column: column,
                      data: data
                    }
                  "
                ></ng-container>
              </ng-container>
            </ng-template>
            <ng-template
              #cellTemplate
              let-cellType
              let-row="row"
              let-column="column"
            >
              <td>
                <div [ngClass]="cellType.class">
                  <ng-container [ngSwitch]="cellType.type">
                    <!-- Status -->
                    <ng-container *ngSwitchCase="'active'">
                      <ng-template #tipContentStatus>
                        <span *ngIf="data[column]" class="form-switch-label"
                          >Ativo</span
                        >
                        <span *ngIf="!data[column]" class="form-switch-label"
                          >Inativo</span
                        >
                      </ng-template>
                      <div class="form-check form-switch">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          role="switch"
                          [checked]="data[column]"
                          (change)="
                            data[column] = !data[column]; toggleStatus(data)
                          "
                          [disabled]="!permissaoUsuario.edit"
                          [ngbTooltip]="tipContentStatus"
                          (click)="$event.stopPropagation()"
                        />
                      </div>
                    </ng-container>

                    <!-- Ações -->
                    <ng-container *ngSwitchCase="'action'">
                      <!-- Tela de usuários -->
                      <app-button
                        *ngIf="
                          row.extra &&
                          row.extra == 'recovery' &&
                          permissaoUsuario.edit
                        "
                        [class]="'btn-logisoil-edit'"
                        [icon]="
                          data.sendEmail
                            ? 'fa fa-unlock-alt'
                            : 'fas fa-circle-notch fa-spin'
                        "
                        [title]="'Alterar Senha'"
                        (click)="emailPassword(data)"
                        style="margin-right: 2px"
                      ></app-button>

                      <!-- Botão habilitado para usuários com permissão -->
                      <app-button
                        *ngIf="
                          (column == 'action' && !data.role) ||
                          (column == 'action' && data.role && levelUser == 6) ||
                          (column == 'action' &&
                            data.role &&
                            data.role < levelUser)
                        "
                        [class]="'btn-logisoil-edit'"
                        [icon]="'fa fa-pencil-square-o'"
                        [title]="'Editar'"
                        [routerLink]="[routerLink + '/' + data.id + '/edit']"
                        [disabled]="!permissaoUsuario.edit"
                        style="margin-right: 2px"
                      ></app-button>

                      <!-- Visualização habilitada para todos os níveis de acesso -->
                      <app-button
                        *ngIf="
                          (column == 'action' && data.role && levelUser == 6) ||
                          (column == 'action' &&
                            data.role &&
                            data.role < levelUser)
                        "
                        [class]="'btn-logisoil-edit'"
                        [icon]="'fa fa-eye'"
                        [title]="'Visualizar'"
                        [routerLink]="[routerLink + '/' + data.id + '/view']"
                        [disabled]="false"
                      ></app-button>

                      <!-- Botão editar desabilitado de acordo com os níveis de acesso -->
                      <app-button
                        *ngIf="
                          column == 'action' &&
                          data.role &&
                          levelUser != 6 &&
                          data.role == levelUser
                        "
                        [class]="'btn-logisoil-edit'"
                        [icon]="'fa fa-pencil-square-o'"
                        [title]="'Editar'"
                        [routerLink]=""
                        [disabled]="true"
                        style="margin-right: 2px"
                      ></app-button>

                      <app-button
                        *ngIf="
                          column == 'action' &&
                          data.role &&
                          levelUser != 6 &&
                          data.role == levelUser
                        "
                        [class]="'btn-logisoil-edit'"
                        [icon]="'fa fa-eye'"
                        [title]="'Visualizar'"
                        [routerLink]="[routerLink + '/' + data.id + '/view']"
                        [disabled]="!permissaoUsuario.view"
                      ></app-button>
                    </ng-container>

                    <!-- MiniDashboard -->
                    <ng-container *ngSwitchCase="'miniDashboard'">
                      <app-mini-dashboard
                        [menu]="menuMiniDashboard"
                        [actions]="getActionsFn ? getActionsFn(data) : null"
                        [config]="
                          data.hasOwnProperty('configMiniDashboard')
                            ? data.configMiniDashboard
                            : null
                        "
                        (sendClickRowEvent)="
                          clickRowEvent($event, {
                            routerLink: routerLink,
                            id: data.id
                          })
                        "
                      >
                      </app-mini-dashboard>
                    </ng-container>

                    <!-- TYPE: color -->
                    <ng-container *ngSwitchCase="'type-color'">
                      <div
                        class="color"
                        [style.background]="data[column]"
                      ></div>
                    </ng-container>

                    <!-- TYPE: event -->
                    <ng-container *ngSwitchCase="'type-event'">
                      <span *ngIf="!row.color">{{ data[column] }}</span>
                      <span
                        *ngIf="row.color"
                        [style.color]="data[column + '_color']"
                      >
                        {{ data[column] }}
                      </span>
                    </ng-container>

                    <!-- TYPE: checkbox -->
                    <ng-container *ngSwitchCase="'type-check'">
                      <input
                        type="checkbox"
                        class="form-check-input"
                        style="width: 20px; height: 20px"
                        [checked]="
                          data[row.referent] ||
                          (row?.configCheck?.selected
                            ? data[row.configCheck.selected]
                            : false)
                        "
                        [disabled]="
                          row?.configCheck?.disabled
                            ? data[row.configCheck.disabled]
                            : false
                        "
                        (change)="
                          clickRowEvent($event, {
                            action: 'checkbox',
                            id: data?.id,
                            index: tdidx,
                            data: data,
                            selected: $any($event.target).checked
                          })
                        "
                        (click)="$event.stopPropagation()"
                      />
                    </ng-container>

                    <!-- TYPE: button -->
                    <ng-container *ngSwitchCase="'type-button'">
                      <app-button
                        *ngIf="!row.hasOwnProperty('condition_config')"
                        [class]="row.config.class"
                        [icon]="row.config.icon"
                        [title]="row.config.title"
                        [label]="
                          row.config.hasOwnProperty('label')
                            ? row.config.label
                            : ''
                        "
                        (click)="
                          clickRowEvent($event, {
                            action: row.config.option,
                            id: data.id,
                            index: tdidx,
                            routerLink: routerLink
                          })
                        "
                      ></app-button>

                      <!-- Ocorrências - Planos de ação -->
                      <app-button
                        *ngIf="row.hasOwnProperty('condition_config')"
                        [class]="row.condition_config[data[column]].class"
                        [icon]="row.condition_config[data[column]].icon"
                        [title]="row.condition_config[data[column]].title"
                        [label]="
                          row.condition_config[data[column]].hasOwnProperty(
                            'label'
                          )
                            ? row.condition_config[data[column]].label
                            : ''
                        "
                        (click)="
                          clickRowEvent($event, {
                            action: row.condition_config[data[column]].option,
                            id: data.id,
                            index: tdidx,
                            routerLink: routerLink
                          })
                        "
                        [disabled]="
                          row.condition_config[data[column]].hasOwnProperty(
                            'disabled'
                          )
                            ? true
                            : false
                        "
                      ></app-button>
                    </ng-container>

                    <!-- TYPE: button_item -->
                    <ng-container *ngSwitchCase="'type-button_item'">
                      <app-button
                        class="d-flex justify-content-center"
                        *ngIf="data[column] != '-'"
                        [class]="row.config.class"
                        [icon]="row.config.icon"
                        [title]="row.config.title"
                        [label]="
                          row.config.hasOwnProperty('label')
                            ? row.config.label
                            : row.config.hasOwnProperty('label_column')
                            ? data[column]
                            : ''
                        "
                        (click)="
                          $event.stopPropagation();
                          clickRowEvent($event, {
                            action: row.config.option,
                            id: data.id,
                            index: tdidx,
                            routerLink: routerLink
                          })
                        "
                      ></app-button>
                      <div *ngIf="data[column] == '-'">{{ data[column] }}</div>
                    </ng-container>

                    <!-- TYPE: link -->
                    <ng-container *ngSwitchCase="'type-link'">
                      <a
                        href=""
                        (click)="
                          clickRowEvent($event, {
                            action: 'link',
                            id: data.id,
                            index: tdidx,
                            column: column
                          })
                        "
                      >
                        {{ data[column] }}
                      </a>
                    </ng-container>

                    <!-- TYPE: details -->
                    <ng-container *ngSwitchCase="'type-details'">
                      <app-button
                        [class]="'btn-logisoil-green'"
                        [icon]="'fa fa-info'"
                        [title]="'Consultar detalhes'"
                        (click)="
                          clickRowEvent($event, {
                            action: 'openDetails',
                            id: data.id,
                            index: tdidx
                          })
                        "
                      ></app-button>
                    </ng-container>

                    <!-- TYPE: gut -->
                    <ng-container *ngSwitchCase="'type-gut'">
                      <div class="text-center">
                        <app-gut-badge [gut]="data[column]"></app-gut-badge>
                      </div>
                    </ng-container>

                    <!-- Action Custom -->
                    <ng-container *ngSwitchCase="'actionCustom'">
                      <div [class]="cellType.class">
                        <!-- Mostra o traço "-" caso allActionsFalse retorne true -->
                        <span *ngIf="allActionsFalse(row, data, actionCustom)"
                          >-</span
                        >

                        <ng-container
                          *ngFor="let itemAction of actionCustom; let a = index"
                        >
                          <app-button
                            [class]="itemAction.class"
                            [icon]="
                              itemAction.hasOwnProperty('condition')
                                ? data[itemAction.condition.item]
                                  ? itemAction.condition.iconTrue
                                  : itemAction.condition.iconFalse
                                : itemAction.icon
                            "
                            [title]="itemAction.title"
                            [label]="itemAction.label || ''"
                            [style.marginLeft.px]="a > 0 ? 3 : 0"
                            *ngIf="
                              itemAction.option != 'check' &&
                              (!itemAction.hasOwnProperty('condition') ||
                                (itemAction.condition.iconFalse != null
                                  ? true
                                  : !!data[itemAction.condition.item]))
                            "
                            (click)="
                              clickRowEvent($event, {
                                action: itemAction.option,
                                id: data.id,
                                index: tdidx,
                                routerLink: routerLink
                              })
                            "
                          ></app-button>

                          <input
                            type="checkbox"
                            class="form-check-input"
                            *ngIf="itemAction.option == 'check'"
                            style="width: 20px; height: 20px"
                            [title]="itemAction.title"
                            [checked]="data['active']"
                            (change)="
                              clickRowEvent($event, {
                                action: itemAction.option,
                                id: data.id,
                                index: tdidx
                              })
                            "
                            (click)="$event.stopPropagation()"
                          />
                        </ng-container>
                      </div>
                    </ng-container>

                    <!--Extra -->
                    <ng-container *ngSwitchCase="'extra'">
                      <ng-container *ngIf="row['extra']">
                        <!-- Ícone à ESQUERDA -->
                        <ng-container *ngIf="data?.extra?.position !== 'right'">
                          <div
                            *ngIf="data?.extra?.[column]"
                            class="me-1"
                            [innerHtml]="data.extra[column]"
                          ></div>

                          <!-- Ícones com ação (clicáveis) -->
                          <ng-container *ngIf="data?.extra?.custom_icons">
                            <div
                              *ngFor="let icon of data.extra.custom_icons"
                              class="me-1"
                              [innerHtml]="icon.html"
                              (click)="
                                icon.column === column && icon.action
                                  ? clickRowEvent($event, {
                                      action: icon.action,
                                      id: data.id,
                                      index: tdidx,
                                      rowIdx: i
                                    })
                                  : null
                              "
                            ></div>
                          </ng-container>

                          <span *ngIf="data?.extra?.show_label !== false">
                            {{ data[column] }}
                          </span>
                        </ng-container>

                        <!-- Ícone à DIREITA -->
                        <ng-container *ngIf="data?.extra?.position === 'right'">
                          <span
                            *ngIf="data?.extra?.show_label !== false"
                            class="me-1"
                          >
                            {{ data[column] }}
                          </span>

                          <div
                            *ngIf="data?.extra?.[column]"
                            [innerHtml]="data.extra[column]"
                          ></div>

                          <!-- Ícones com ação (clicáveis) -->
                          <ng-container *ngIf="data?.extra?.custom_icons">
                            <div
                              *ngFor="let icon of data.extra.custom_icons"
                              class="ms-1"
                              [innerHtml]="icon.html"
                              (click)="
                                icon.column === column && icon.action
                                  ? clickRowEvent($event, {
                                      action: icon.action,
                                      id: data.id,
                                      index: tdidx,
                                      rowIdx: i
                                    })
                                  : null
                              "
                            ></div>
                          </ng-container>
                        </ng-container>
                      </ng-container>
                    </ng-container>

                    <ng-container *ngSwitchDefault>
                      {{ data[column] }}
                    </ng-container>
                  </ng-container>
                </div>
              </td>
            </ng-template>
          </ng-template>
        </tr>
        <!-- Específico para exibir detalhes - Tabela Fatores de Segurança -->
        <ng-container *ngIf="data?.detail">
          <tr
            *ngFor="let detailItem of getDetailList(data.detail)"
            [hidden]="!detailItem?.show"
          >
            <td [attr.colspan]="tableHeader.length">
              <!-- Se tiver dados, exibe a tabela -->
              <ng-container
                *ngIf="detailItem.detailData?.length > 0; else noData"
              >
                <app-table
                  [tableHeader]="detailItem.detailHeader"
                  [tableData]="detailItem.detailData"
                  [actionCustom]="
                    detailItem.hasOwnProperty('detailActionCustom')
                      ? detailItem.detailActionCustom
                      : []
                  "
                  [detail]="true"
                  (sendClickRowEvent)="clickRowEvent($event, { rowIdx: tdidx })"
                ></app-table>
              </ng-container>

              <!-- Exibe mensagem apenas se msg existir -->
              <ng-template #noData>
                <div *ngIf="detailItem.msg" class="alert alert-warning mb-0">
                  {{ detailItem.msg }}
                </div>
              </ng-template>
            </td>
          </tr>
        </ng-container>
      </ng-template>
    </tbody>
  </table>
</div>
