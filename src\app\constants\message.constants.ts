enum MessagePadroes {
  Error = 'Você não tem permissão para este recurso.',
  NoRegister = 'Nenhum registro encontrado.',
  NoRegisterClient = 'Nenhum registro encontrado para o cliente selecionado.',
  NoSection = 'Não há registro de seções para esta estrutura.',
  NoAlert = 'Nenhum registro de alerta encontrado.',
  NoImage = 'Nenhuma imagem cadastrada.',
  NoMapData = 'Não há dados para gerar este mapa.',
  NoMapLayerDB = 'Não há layer do tipo Dam Break cadastrada para a estrutura.',
  SendReport = 'Solicitação registrada com sucesso! Em alguns instantes, você receberá o e-mail com o relatório em anexo.',
  NoFileDXF = 'Não existe arquivo .dxf para as opções selecionadas.',
  NoNotifications = 'Não há notificações no momento.',
  NoSafetyFactor = 'Não há fatores de segurança calculados para a combinação de data, condição e seção escolhida. Selecione uma combinação de data, condição e seção que seja válida.',
  NoWaterLevel = 'É necessário que seja inserido um N.A. de jusante para barragens que trabalham com pé afogado. Tente novamente.',
  NoInstrumentationValid = 'A estrutura selecionada não possui seção com Régua Linimétrica ou Comprimento de praia.',
  NoDxfReviewSection = 'A estrutura selecionada não possui seção com arquivo DXF cadastrado em sua revisão.',
  NoSurfaceType = 'Não é possível gerar o gráfico, pois a estrutura selecionada não possui informações sobre o tipo de superfície.',
  NoCalculationMethods = 'Não é possível gerar o gráfico, pois a estrutura selecionada não possui informações sobre o método de cálculo.',
  NeedToHaveDXF = 'A estrutura selecionada não possui nenhuma seção com instrumentação válida (comprimento de praia ou régua linimétrica) nem revisões disponíveis com DXF.',
  NoPercolationData = 'A estrutura selecionada não possui nenhum instrumento do subtipo "Percolação".',
  NoDisplacementData = 'A estrutura selecionada não possui nenhum instrumento do subtipo "Deslocamento".',
  InspectionSheetInProgress = 'Já existe uma ficha de inspeção em andamento para esta estrutura.',
  NoOccurrences = 'Não existe nenhum vínculo possível para esta ocorrência.',
  DeleteOccurrence = 'Ocorrência excluída com sucesso!',
  NoInstrumentMetrics = 'Nenhum instrumento cadastrado para esta estrutura.',
  NoBeachOrRuler = 'A estrutura selecionada não possui seção com Régua Linimétrica ou Comprimento de praia.'
}

enum MessageInputInvalid {
  Required = 'Campo obrigatório.',
  Email = 'Por favor digite um e-mail válido.',
  RegisterInvalid = 'Preencha os campos obrigatórios.',
  NoHistory = 'Não há registro de histórico.',
  NoNotes = 'Não há histórico de registros manuais.',
  NoGroups = 'Não existem grupos de instrumentos criados para esta estrutura.',
  NoFile = 'Formato de arquivo inválido.',
  NoDXF = 'Não existe imagem .dxf cadastrada para a seção associada a este instrumento.',
  NoDateTime = 'Preencha todos os dados e tente novamente.',
  NoChart = 'Não há dados para gerar gráfico para este instrumento.',
  isValidCPF = 'Campo CPF inválido.'
}

enum MessageCadastro {
  SucessoCadastro = 'Cadastro realizado com sucesso!',
  EdicaoCadastro = 'Dados alterados com sucesso!',
  AlteracaoStatus = 'Status alterado com sucesso!',
  AlteracaoProfile = 'Dados alterados com sucesso!',
  EnviarEmailNovaSenha = 'E-mail reenviado para criação de nova senha.',
  AdicionarRevisaoSecao = 'Revisão adicionada com sucesso!',
  InstrumentoCadastrado = 'Instrumento(s) cadastrado(s) com sucesso!',
  NovoRegistro = 'Registro cadastrado com sucesso!',
  GrupoInstrumentoCadastrado = 'Grupo de instrumentos cadastrado com sucesso!',
  DeleteGrupoInstrumento = 'Grupo de instrumentos excluído com sucesso!',
  DeleteMaterial = 'Material excluído com sucesso!',
  DeleteReadings = 'Leitura(s) excluída(s) com sucesso!',
  GeneratePackages = 'Pacote gerado com sucesso! Para visualizá-lo, acesse o menu “Estabilidade”.',
  IgnorePackages = 'Pacote de leituras ignorado com sucesso!',
  CalculatePackages = 'Solicitação de cálculo realizada com sucesso! Aguarde alguns instantes.',
  ImagemCadastrada = 'Galeria de imagens salva com sucesso!',
  DeleteImages = 'Imagem(ns) excluída(s) com sucesso!',
  AlteracaoCor = 'Cor(es) alterada(s) com sucesso!',
  AlteracaoConfiguracao = 'Configuração do mapa alterada com sucesso!',
  DeleteReport = 'Agendamento de relatório excluído com sucesso!',
  SuccessSimulation = 'Simulação registrada com sucesso. Em instantes, os resultados estarão disponíveis em "Resultados da simulação".',
  DeleteSimulation = 'Simulação removida com sucesso dos resultados.',
  ShareSimulation = 'Simulação compartilhada com sucesso!',
  FixedSimulation = 'Simulação fixada com sucesso!',
  RenameSimulation = 'Simulação renomeada com sucesso!',
  ReviewMaterial = 'Revisão adicionada com sucesso!',
  EditReviewMaterial = 'Revisão editada com sucesso!',
  DeleteInspectionSheet = 'Ficha excluída com sucesso!',
  DeleteActionPlan = 'Plano de Ação excluído com sucesso!'
}

enum MessageUpload {
  ErroPlanilhaLeitura = `Detectamos erros de preenchimento na planilha enviada.<a href="#" (click)="clickEvent(event)"> Clique aqui</a> para baixar a planilha com os detalhes dos erros.</br>Abra a planilha baixada e verifique as células destacadas com problemas.</br>Cada erro estará anotado para facilitar a correção.</br>Após corrigir todos os erros, salve a planilha e faça o upload novamente.`
}

enum ModalConfirm {
  ForcarCalculo = 'Tem certeza de que deseja preencher os instrumentos sem leitura com a última leitura registrada no banco de dados antes da data do pacote para prosseguir com o cálculo das análises de estabilidade? Esta ação poderá ser rastreada pelos demais usuários.',
  ConfirmarOperacao = 'Deseja confirmar essa operação?',
  ExcluirSimulação = 'Deseja confirmar a exclusão da simulação selecionada?',
  FixarSimulacao = 'Deseja confirmar a fixação da simulação selecionada?',
  ExcluirFichadeInspecao = 'Deseja confirmar a exclusão da ficha de inspeção selecionada?',
  ExcluirPlanoDeAcao = 'Deseja confirmar a exclusão do Plano de Ação selecionado?',
  ExcluirEtapaDeObra = 'Deseja confirmar a exclusão da Etapa de Obra selecionada?'
}

enum AlterarSenha {
  EnviarEmail = 'E-mail enviado com sucesso!'
}

enum ErrosInput {
  required = 'Campo obrigatório',
  email = 'Por favor digite um email válido.',
  isReferencial = 'Nenhuma medição foi selecionada como referencial.'
}

export { MessageCadastro, MessageInputInvalid, MessagePadroes, MessageUpload, AlterarSenha, ErrosInput, ModalConfirm };
