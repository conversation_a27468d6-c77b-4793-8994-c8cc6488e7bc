import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';

import { FormService } from 'src/app/services/form.service';

import { fieldsReading } from 'src/app/constants/readings.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-inc-conv',
  templateUrl: './inc-conv.component.html',
  styleUrls: ['./inc-conv.component.scss']
})
export class IncConvComponent implements OnInit, OnChanges {
  @Input() public instrumentsList: any = [];
  @Input() public index: number = null;
  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;
  @Input() public spreadsheet: boolean = false;
  @Input() public units: any = null;
  @Input() public typeInstrument: any = null;
  @Input() public datetime: any = null;
  @Input() public references: any = null;

  @Output() public setInstrument = new EventEmitter();
  @Output() public executeCalc = new EventEmitter();

  public formReading: FormGroup = new FormGroup({
    instrument: new FormControl('', [Validators.required]),
    depth: new FormControl({ value: '', disabled: true }),
    date: new FormControl({ value: '', disabled: true }, [Validators.required]),
    positive_a: new FormControl({ value: '', disabled: true }),
    negative_a: new FormControl({ value: '', disabled: true }),
    positive_b: new FormControl({ value: '', disabled: true }),
    negative_b: new FormControl({ value: '', disabled: true }),
    average_displacement_a: new FormControl({ value: '', disabled: true }, [Validators.required]),
    average_displacement_b: new FormControl({ value: '', disabled: true }, [Validators.required]),
    accumulated_displacement_a: new FormControl({ value: '', disabled: true }),
    accumulated_displacement_b: new FormControl({ value: '', disabled: true }),
    deviation_a: new FormControl({ value: '', disabled: true }),
    deviation_b: new FormControl({ value: '', disabled: true }),
    measure: new FormControl({ value: '', disabled: true }), //Ponto de medicao
    //Para calcular
    measure_id: new FormControl({ value: '', disabled: true }),
    elevation: new FormControl({ value: '', disabled: true }),
    //Para edicao
    id: new FormControl({ value: '', disabled: true })
  });

  public controls: any = null;
  public fieldsReading = fieldsReading;
  public func = fn;

  constructor(private formService: FormService) {}

  ngOnInit(): void {
    this.controls = this.formReading.controls;
  }

  ngOnChanges(changes: SimpleChanges) {
    this.controls = this.formReading.controls;

    if (changes.data && changes.data.currentValue != null) {
      this.splitData(changes.data.currentValue);
    }

    if (changes.units && changes.units.previousValue != undefined && !(changes.units.previousValue === changes.units.currentValue)) {
      this.executeCalc.emit({ typeInstrument: this.typeInstrument.id, calc: 'accumulated_displacement', index: this.index });
    }

    if (changes.datetime && changes.datetime.currentValue != null) {
      this.controls['date'].setValue(this.datetime);
    }
  }

  changeInstrument(instrument) {
    this.setInstrument.emit(instrument);
  }

  splitData($dados) {
    if (this.index > 0 || this.edit || this.view || this.spreadsheet) {
      this.controls['instrument'].disable();
      this.instrumentsList = $dados.instrumentsList;
    } else {
      this.controls['instrument'].enable();
    }

    this.formService.toggleFormList(this.formReading, this.fieldsReading[this.typeInstrument.id]);

    this.controls['instrument'].setValue($dados.instrument.id);
    this.controls['measure'].setValue($dados.measure.identifier);
    this.controls['measure_id'].setValue($dados.measure.id);
    this.controls['depth'].setValue($dados.depth);
    this.controls['elevation'].setValue($dados.elevation);

    if ($dados.edit) {
      this.controls['id'].setValue($dados.edit.id);

      let date = $dados.edit.date.split('.');
      this.controls['date'].setValue(date[0]);

      this.controls['positive_a'].setValue($dados.edit.positive_a);
      this.controls['negative_a'].setValue($dados.edit.negative_a);
      this.controls['positive_b'].setValue($dados.edit.positive_b);
      this.controls['negative_b'].setValue($dados.edit.negative_b);
      this.controls['average_displacement_a'].setValue($dados.edit.average_displacement_a);
      this.controls['average_displacement_b'].setValue($dados.edit.average_displacement_b);
      this.controls['accumulated_displacement_a'].setValue($dados.edit.accumulated_displacement_a);
      this.controls['accumulated_displacement_b'].setValue($dados.edit.accumulated_displacement_b);
      this.controls['deviation_a'].setValue($dados.edit.deviation_a);
      this.controls['deviation_b'].setValue($dados.edit.deviation_b);
    }

    if (this.view) {
      this.formReading.disable();
    }

    //Após importar a planilha, não é mais necessário fazer o recálculo
    // if (this.spreadsheet) {
    //   setTimeout(() => {
    //     this.calcAfterLoadSpreadsheet();
    //   }, 200);
    // }
  }

  //Calculo deslocamento medio
  calcDisplacement(option: string = '') {
    let positive = this.controls['positive_' + option].value;
    let negative = this.controls['negative_' + option].value;

    if (positive && negative) {
      let positiveDecimal: any = fn.convertLengthDecimal(positive, this.units[0], 'mm');
      let negativeDecimal: any = fn.convertLengthDecimal(negative, this.units[0], 'mm');

      let average_displacementDecimal: any = positiveDecimal.sub(negativeDecimal);
      average_displacementDecimal = average_displacementDecimal.div(2);

      average_displacementDecimal = fn.convertLengthDecimal(average_displacementDecimal, 'mm', this.units[0]);
      this.controls['average_displacement_' + option].setValue(average_displacementDecimal);

      this.calculate('accumulated_displacement', option);
    }
  }

  calculate(calc: string = '', suffix: string = '') {
    switch (calc) {
      case 'accumulated_displacement':
        this.executeCalc.emit({ typeInstrument: this.typeInstrument.id, calc: calc, index: this.index, references: this.references });
        break;
      default:
        break;
    }
  }

  calcAfterLoadSpreadsheet() {
    this.calcDisplacement('a');
    this.calcDisplacement('b');

    this.calculate('accumulated_displacement', 'a');
    this.calculate('accumulated_displacement', 'b');
  }
}
