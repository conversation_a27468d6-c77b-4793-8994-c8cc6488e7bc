<form [formGroup]="formStructureResponsible">
  <div class="row">
    <div class="col-sm-6">
      <label class="form-label">Responsáveis</label>
      <select
        class="form-select"
        formControlName="name"
        (change)="resetForm('responsible')"
      >
        <option value="">Selecione...</option>
        <option *ngFor="let item of responsibleList" [ngValue]="item.id">
          {{ item.name }}
        </option>
      </select>
    </div>

    <div class="col-md-2 mt-4 d-flex align-items-start">
      <!-- Vincular responsável -->
      <app-button
        [class]="'btn-logisoil-addLink'"
        [icon]="'fa fa-thin fa-user-check'"
        class="me-1 mt-1"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        title="Vincular responsável"
        (click)="selectResponsible(formStructureResponsible.get('name').value)"
        *ngIf="
          can('editResponsibles') &&
          formStructureResponsible.get('name').value != ''
        "
        [disabled]="view"
      >
      </app-button>

      <!-- Adicionar responsável -->
      <app-button
        [class]="'btn-logisoil-add'"
        [icon]="'fa fa-thin fa-plus'"
        class="me-1"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        title="Adicionar responsável"
        (click)="resetForm('responsible', true)"
        *ngIf="
          can('editResponsibles') &&
          formStructureResponsible.get('name').value == ''
        "
        [disabled]="view"
      >
      </app-button>

      <!-- Editar responsável -->
      <app-button
        [class]="'btn-logisoil-editItem'"
        [icon]="'fa fa-thin fa-pencil'"
        class="me-1 mt-1"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        title="Editar responsável"
        (click)="
          ctrlResponsible = true;
          getResponsibleById(formStructureResponsible.get('name').value, 'edit')
        "
        *ngIf="
          can('editResponsibles') &&
          formStructureResponsible.get('name').value != ''
        "
      >
      </app-button>
    </div>
  </div>
</form>

<!-- Tabela de Responsáveis Cadastrados -->
<div class="row mt-2">
  <app-table
    [tableHeader]="tableHeader"
    [tableData]="tableData"
    (sendClickRowEvent)="clickRowEvent($event)"
    [actionCustom]="actionCustom"
  ></app-table>
</div>

<!-- Alerta -->
<div class="row mt-2">
  <div class="col-md-12">
    <div
      class="alert alert-success"
      role="alert"
      *ngIf="messageResponsible.status"
    >
      {{ messageResponsible.text }}
    </div>
  </div>
</div>
<!-- Alerta -->

<form
  [formGroup]="formResponsible"
  *ngIf="ctrlResponsible && !view"
  (ngSubmit)="validate('responsible')"
>
  <!-- Novo responsável -->
  <div class="row mt-2">
    <!-- Aba Novo Responsável  -->
    <ul class="nav nav-tabs px-2">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page">Responsável</a>
      </li>
    </ul>

    <div class="row mt-4">
      <!-- Mensagem de erro -->
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesErrorResponsible"
      ></app-alert>
    </div>

    <div class="col-sm-12">
      <div class="row">
        <!-- Name -->
        <div class="col-sm-6">
          <label class="form-label">Nome</label>
          <input
            type="text"
            class="form-control"
            formControlName="name"
            autocomplete="off"
            maxlength="255"
          />
          <small
            class="form-text text-danger"
            *ngIf="
              !formResponsible.get('name').valid &&
              formResponsible.get('name').touched
            "
            >Campo Obrigatório.</small
          >
        </div>
        <!-- Email address -->
        <div class="col-sm-6">
          <label class="form-label">E-mail</label>
          <input
            type="email"
            class="form-control"
            formControlName="email_address"
            autocomplete="off"
            maxlength="255"
          />
          <small
            class="form-text text-danger"
            *ngIf="
              !formResponsible.get('email_address').valid &&
              formResponsible.get('email_address').touched
            "
            >Digite um e-mail válido.</small
          >
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-sm-6">
          <div class="row">
            <!-- Função -->
            <div class="col-11">
              <label class="form-label">Função</label>
              <select
                class="form-select"
                formControlName="role"
                (change)="getRoles()"
              >
                <option value="">Selecione...</option>
                <option *ngFor="let role of roleList" [ngValue]="role.id">
                  {{ role.name }}
                </option>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formResponsible.get('role').valid &&
                  formResponsible.get('role').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
            <!-- Adicionar Função -->
            <div class="col-1 mt-4" style="padding-left: 0">
              <app-button
                [class]="'btn-logisoil-add'"
                [icon]="'fa fa-thin fa-plus'"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="Adicionar função"
                (click)="resetForm('role', true)"
                *ngIf="formResponsible.get('role').value == ''"
              >
              </app-button>
            </div>
          </div>
        </div>
        <div class="col-sm-6">
          <div class="row">
            <!-- Cargo -->
            <div class="col-11">
              <label class="form-label">Cargo</label>
              <select
                class="form-select"
                formControlName="position"
                (change)="getPositions()"
              >
                <option value="">Selecione...</option>
                <option
                  *ngFor="let position of positionList"
                  [ngValue]="position.id"
                >
                  {{ position.name }}
                </option>
              </select>
              <small
                class="form-text text-danger"
                *ngIf="
                  !formResponsible.get('position').valid &&
                  formResponsible.get('position').touched
                "
                >Campo Obrigatório.</small
              >
            </div>
            <!-- Adicionar Cargo -->
            <div class="col-1 mt-4" style="padding-left: 0">
              <app-button
                [class]="'btn-logisoil-add'"
                [icon]="'fa fa-thin fa-plus'"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="Adicionar cargo"
                (click)="resetForm('position', true)"
                *ngIf="formResponsible.get('position').value == ''"
              >
              </app-button>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-3">
          <label class="form-label">CPF</label>
          <input
            type="text"
            class="form-control"
            formControlName="cpf"
            autocomplete="off"
            placeholder="Opcional. Ex.: 000.000.000-00"
            [brmasker]="{ mask: '000.000.000-00', len: 14, type: 'num' }"
          />
        </div>
        <div class="col-md-3">
          <label class="form-label">Registro Profissional</label>
          <input
            type="text"
            class="form-control"
            formControlName="professional_record"
            autocomplete="off"
            maxlength="255"
            placeholder="Opcional"
          />
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-6 mt-2 d-flex align-items-start">
          <app-button
            [class]="'btn-logisoil-red'"
            [icon]="'fa fa-thin fa-xmark'"
            [label]="'Cancelar'"
            [type]="false"
            class="me-1"
            (click)="ctrlResponsible = false; resetForm('responsible')"
          >
          </app-button>

          <app-button
            [class]="'btn-logisoil-green'"
            [icon]="'fa fa-thin fa-floppy-disk'"
            [label]="'Salvar'"
            [type]="false"
            class="me-1"
            (click)="validate()"
            [disabled]="!formResponsible.valid"
          >
          </app-button>

          <app-button
            [class]="'btn-logisoil-blue'"
            [icon]="'fa fa-thin fa-user-check'"
            [label]="'Salvar e Vincular'"
            [type]="false"
            class="me-1"
            (click)="ctrlVinculation = true"
            [disabled]="!formResponsible.valid"
          >
          </app-button>
        </div>
      </div>
    </div>
  </div>
</form>

<!-- Alerta -->
<div class="row mt-2">
  <div class="col-md-12">
    <div class="alert alert-success" role="alert" *ngIf="messageRole.status">
      {{ messageRole.text }}
    </div>
  </div>
</div>
<!-- Alerta -->

<!-- Adicionar Função -->
<form [formGroup]="formRole" *ngIf="ctrlRole" (ngSubmit)="validate('role')">
  <div class="row mt-4">
    <ul class="nav nav-tabs px-2">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page">Cadastro de Função</a>
      </li>
    </ul>
    <div class="row mt-4">
      <!-- Mensagem de erro -->
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesErrorRole"
      ></app-alert>
    </div>

    <div class="row">
      <!-- Descrição -->
      <div class="col-sm-6">
        <label class="form-label">Descrição</label>
        <input
          type="text"
          class="form-control"
          formControlName="name"
          autocomplete="off"
          maxlength="255"
        />
        <small
          class="form-text text-danger"
          *ngIf="!formRole.get('name').valid && formRole.get('name').touched"
          >Campo Obrigatório.</small
        >
      </div>
      <div class="buttons col-sm-4">
        <app-button
          [class]="'btn-logisoil-red'"
          [icon]="'fa fa-thin fa-xmark'"
          [label]="'Cancelar'"
          [type]="false"
          class="me-1"
          (click)="ctrlRole = false"
        >
        </app-button>
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-floppy-disk'"
          [label]="'Salvar'"
          [type]="false"
          class="me-1"
          [disabled]="!formRole.valid"
        >
        </app-button>
      </div>
    </div>
  </div>
</form>

<!-- Alerta -->
<div class="row mt-2">
  <div class="col-md-12">
    <div
      class="alert alert-success"
      role="alert"
      *ngIf="messagePosition.status"
    >
      {{ messagePosition.text }}
    </div>
  </div>
</div>
<!-- Alerta -->

<!-- Adicionar Cargo -->
<form
  [formGroup]="formPosition"
  *ngIf="ctrlPosition"
  (ngSubmit)="validate('position')"
>
  <div class="row mt-4">
    <ul class="nav nav-tabs px-2">
      <li class="nav-item">
        <a class="nav-link active" aria-current="page">Cadastro de Cargo</a>
      </li>
    </ul>
    <div class="row mt-4">
      <!-- Mensagem de erro -->
      <app-alert
        [class]="'alert-danger'"
        [messages]="messagesErrorPosition"
      ></app-alert>
    </div>
    <div class="row">
      <!-- Descrição -->
      <div class="col-sm-6">
        <label class="form-label">Descrição</label>
        <input
          type="text"
          class="form-control"
          formControlName="name"
          autocomplete="off"
          maxlength="255"
        />
        <small
          class="form-text text-danger"
          *ngIf="
            !formPosition.get('name').valid && formPosition.get('name').touched
          "
          >Campo Obrigatório.</small
        >
      </div>
      <div class="buttons col-sm-4">
        <app-button
          [class]="'btn-logisoil-red'"
          [icon]="'fa fa-thin fa-xmark'"
          [label]="'Cancelar'"
          [type]="false"
          class="me-1"
          (click)="ctrlPosition = false"
        >
        </app-button>
        <app-button
          [class]="'btn-logisoil-green'"
          [icon]="'fa fa-thin fa-floppy-disk'"
          [label]="'Salvar'"
          [type]="false"
          class="me-1"
          [disabled]="!formPosition.valid"
        >
        </app-button>
      </div>
    </div>
  </div>
</form>
