import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  //Tela Dashboard
  getSectionMap(params: any = {}) {
    const url = `/dashboard/sections-map`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Tela Dashboard
  getSectionDXF(params: any = {}) {
    const url = `/dashboard/section-dxf`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Tela Dashboard
  getStabilityChart(params: any) {
    const url = `/dashboard/stability-chart`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Tela Dashboard
  getPercolationChart(params: any) {
    const url = `/dashboard/percolation-chart`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Tela Home
  getStructureMap(params: any = {}) {
    const url = `/dashboard/structures-map`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Tela Home - Instrumentos Ativos
  getInstrumentMetrics(params: any) {
    const url = `/dashboard/instrument-metrics`;
    return this.api.get<any>(url, params, false, 'client');
  }

  //Tela Home - Últimas atualizações
  getLatestUpdates(params: any) {
    const url = `/dashboard/latest-updates`;
    return this.api.get<any>(url, params, false, 'client');
  }
}
