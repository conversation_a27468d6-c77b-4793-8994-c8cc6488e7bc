// import { Injectable, Injector } from '@angular/core';
// import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpResponse, HttpErrorResponse } from '@angular/common/http';
// import { Observable, tap, throwError, catchError } from 'rxjs';
// import { AuthService } from './auth.service';
// import { ToastrService } from 'ngx-toastr';
// import { switchMap } from 'rxjs/operators';

import { Injectable, Injector } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { AuthService } from './auth.service';
import { ToastrService } from 'ngx-toastr';
import { catchError, switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthInterceptorService implements HttpInterceptor {
  constructor(private injector: Injector, private toastrService: ToastrService) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const authService = this.injector.get(AuthService);

    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          // Tentar renovar o token
          return authService.renewToken().pipe(
            switchMap((isAuthenticated: boolean) => {
              if (isAuthenticated) {
                const clonedRequest = request.clone({
                  setHeaders: {
                    Authorization: `Bearer ${authService.getToken()}`
                  }
                });
                return next.handle(clonedRequest);
              } else {
                this.toastrService.error('Não autorizado.', 'Erro 401:');
                return throwError(error);
              }
            }),
            catchError((renewalError: HttpErrorResponse) => {
              this.toastrService.error('Não autorizado.', 'Erro 401:');
              return throwError(renewalError);
            })
          );
        } else if (error.status === 403) {
          this.toastrService.warning('Acesso proibido.', 'Aviso:');
        } else if (error.status >= 500) {
          this.toastrService.error('Erro do Servidor. ', 'Erro 500');
        } else if (error.status === 0) {
          // Não notificar o usuário em caso de erro de status 0
        }
        return throwError(error);
      })

      // tap((event: HttpEvent<any>) => {
      //   if (event instanceof HttpResponse && event.status === 200) {
      //   }
      // }),
      // catchError((error: HttpErrorResponse) => {
      //   if (error.status === 401) {
      //     this.toastrService.error('Não autorizado.', 'Erro 401:');
      //   } else if (error.status === 403) {
      //     this.toastrService.warning('Acesso proibido.', 'Aviso:');
      //   } else if (error.status === 404) {
      //     // this.toastrService.error('Erro 404: Recurso não encontrado.', 'Erro');
      //   } else if (error.status >= 500) {
      //     this.toastrService.error('Erro do Servidor. ', 'Erro 500');
      //   } else if (error.status === 0) {
      //     // this.toastrService.error('Erro ' + error.status + ': Ocorreu um erro desconhecido.', 'Erro');
      //   }
      //   return throwError(error);
      // })
    );
  }
}
