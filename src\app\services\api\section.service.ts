import { Injectable } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class SectionsService {
  [x: string]: any;
  constructor(private api: ApiService) {}

  getSectionList(params: any = {}) {
    const url = '/sections';
    return this.api.get<any>(url, params, false, 'client');
  }

  // Cadastro de Secoes
  postSection(params: any) {
    const url = '/sections';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Retorna as secoes para uso em filtro
  postSectionsSearch(params: any = {}, qparams: any = {}) {
    const url = '/sections/search';
    return this.api.post<any>(url, params, qparams, 'client');
  }

  // Busca a Secao por ID
  getSectionById(id: string) {
    const url = `/sections/${id}`;
    return this.api.get<any>(url, null, false, 'client');
  }

  patchSections(id: string, params: any) {
    const url = `/sections/${id}`;
    return this.api.patch<any>(url, params, 'client');
  }

  putSections(id: string, params: any) {
    const url = `/sections/${id}`;
    return this.api.put<any>(url, params, 'client');
  }

  postSectionMaps(params: any) {
    const url = '/sections/maps/search';
    return this.api.post<any>(url, params, {}, 'client');
  }

  getSectionsReviewsLatestFile(id: string, params: any) {
    const url = `/sections/${id}/reviews/latest/file`;
    return this.api.get<any>(url, params, true, 'client');
  }

  //Busca a secao por ID - Tela de leituras
  getSectionByIdInstrument(id: string) {
    const url = `/sections/${id}/instruments`;
    return this.api.get<any>(url, null, false, 'client');
  }

  //Gráfico de Estabilidade
  patchSectionsChartLineColor(params: any) {
    const url = `/sections/chart-line-color`;
    return this.api.patch<any>(url, params, 'client');
  }

  postSectionsDXFTransform(params: any) {
    const url = '/sections/dxf/transform';
    return this.api.post<any>(url, params, {}, 'client');
  }

  postSectionsDXFTransformMaterial(params: any) {
    const url = '/sections/dxf/transform/material';
    return this.api.post<any>(url, params, {}, 'client');
  }

  // Histórico
  getSectionsHistory(id: string, params: any = {}) {
    const url = `/sections/${id}/history`;
    return this.api.get<any[]>(url, params, false, 'client');
  }

  // Lista as revisões e etapas de obra de uma seção
  getSectionsReviews(id: string) {
    const url = `/sections/${id}/reviews`;
    return this.api.get<any[]>(url, null, false, 'client');
  }
}
