import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ReloadTimerComponent } from './reload-timer.component';

describe('ReloadTimerComponent', () => {
  let component: ReloadTimerComponent;
  let fixture: ComponentFixture<ReloadTimerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ReloadTimerComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ReloadTimerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
