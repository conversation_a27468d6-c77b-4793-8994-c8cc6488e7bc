import { Component, OnInit, ViewEncapsulation } from '@angular/core';

import { MessagePadroes } from 'src/app/constants/message.constants';
import { NotificationTheme, NotificationThemeGroup } from 'src/app/constants/notifications.constants';

import { NotificationService } from '../../services/notification.service';

import * as moment from 'moment';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class NotificationsComponent implements OnInit {
  public dropdownNotification: boolean = false;

  public page: number = 1;
  public pageSize: number = 10;
  public collectionSize: number = 0;

  public message: any = { text: '', status: false, class: 'alert-success' };

  public notificationsData: any = [];

  public intervalNotifications: any = null;
  public selectedNotifications: any = [];

  public inputPage: number = 1;

  constructor(private notificationService: NotificationService) {}

  /**
   * Inicializa o componente de notificações.
   * Carrega as notificações não lidas do serviço, formata os dados e
   * restaura notificações previamente selecionadas do localStorage.
   */
  ngOnInit(): void {
    if (localStorage.getItem('selectedNotifications')) {
      this.selectedNotifications = JSON.parse(localStorage.getItem('selectedNotifications'));
    }

    this.notificationService.notifications$.subscribe(({ notifications, count }) => {
      this.notificationsData = notifications;
      this.collectionSize = count;
      if (this.notificationsData.length === 0 || this.collectionSize === 0) {
        this.message.text = MessagePadroes.NoNotifications;
        this.message.status = true;
        this.message.class = 'alert-warning';
      }
      this.formatData();
    });
  }

  /**
   * Marca como lidas as notificações previamente selecionadas.
   * Após a ação, reinicializa os dados das notificações.
   */
  readNotifications() {
    this.notificationService.markAsRead(this.selectedNotifications);
    this.resetNotifications();
  }

  /**
   * Marca todas as notificações como lidas.
   * Após a ação, reinicializa os dados das notificações.
   */
  readAllNotifications() {
    this.notificationService.markAllAsRead();
    this.resetNotifications();
  }

  /**
   * Formata os dados das notificações:
   * - Converte a data para o formato 'DD/MM/YYYY'
   * - Atualiza o estado de seleção das notificações com base nos itens salvos
   */
  formatData(): void {
    this.notificationsData = this.notificationsData.map((item: any) => {
      item.created_date = moment(item.created_date).format('DD/MM/YYYY');
      item.checked = this.selectedNotifications.includes(item.id);
      return item;
    });
  }

  /**
   * Adiciona ou remove uma notificação da lista de selecionadas.
   * Atualiza o localStorage para manter a seleção persistente.
   *
   * @param notification - ID da notificação
   * @param checked - Define se a notificação está selecionada ou não
   */
  changeNotification(notification: string, checked: boolean): void {
    clearInterval(this.intervalNotifications);
    if (checked) {
      this.selectedNotifications.push(notification);
    } else {
      const filtered = this.selectedNotifications.filter((item) => {
        return item !== notification;
      });
      this.selectedNotifications = filtered;
    }

    localStorage.setItem('selectedNotifications', JSON.stringify(this.selectedNotifications));
  }

  /**
   * Limpa a lista de notificações selecionadas e reseta a paginação.
   * Atualiza o localStorage com os dados limpos.
   */
  resetNotifications() {
    this.selectedNotifications = [];
    this.page = 1;
    this.pageSize = 10;
    localStorage.setItem('selectedNotifications', JSON.stringify(this.selectedNotifications));
  }

  /**
   * Controla o fechamento do dropdown de notificações ao clicar fora do componente.
   *
   * @param element - Identificador do elemento a ser fechado
   */
  onClickedOutside(element: string) {
    switch (element) {
      case 'notification':
        this.dropdownNotification = false;
        break;
    }
  }

  /**
   * Retorna o rótulo do tema da notificação com base no valor informado.
   *
   * @param themeValue - Código numérico do tema da notificação
   * @returns O texto do tema ou 'Tema desconhecido' se não encontrado
   */
  getThemeLabel(themeValue: number): string {
    const theme = NotificationThemeGroup.find((theme) => theme.value === themeValue);
    return theme?.label || 'Tema desconhecido';
  }

  /**
   * Gera a URL de histórico associada a uma notificação.
   *
   * @param notification - Objeto da notificação
   * @returns URL da página de histórico ou '#' se não aplicável
   */
  generateHistoryUrl(notification: any): string {
    let baseUrl = '';
    if (notification.theme === NotificationTheme.InstrumentUpdated) {
      let instrumentId = notification.message.split('@')[1];
      baseUrl = '/instruments/' + instrumentId + '/history';
    } else if (notification.theme === NotificationTheme.ReadingUpdated) {
      let readingId = notification.message.split('@')[1];
      baseUrl = '/readings/' + readingId + '/history';
    } else {
      baseUrl = '#';
    }
    return baseUrl;
  }

  /**
   * Altera a página atual da listagem de notificações e requisita os dados atualizados.
   *
   * @param selectPage - Número da página selecionada
   */
  loadPage(selectPage: number): void {
    this.page = selectPage;
    this.notificationService.fetchNotifications(2, this.page, this.pageSize);
  }

  /**
   * Formata o número total de notificações para uma exibição compacta e legível.
   * Utiliza notação K+ ou M+ conforme o volume.
   *
   * @returns Representação formatada do contador de notificações
   */
  getFormattedCollectionSize(): string {
    const count = this.collectionSize;

    if (count < 1000) {
      return count.toString();
    } else if (count < 10000) {
      return `${Math.floor(count / 1000)}K+`;
    } else if (count < 100000) {
      return `${Math.floor(count / 1000)}K+`;
    } else if (count < 1000000) {
      return `${Math.floor(count / 1000)}K+`;
    } else {
      return `${Math.floor(count / 1000000)}M+`;
    }
  }

  get totalPages(): number {
    return Math.ceil(this.collectionSize / this.pageSize);
  }

  jumpToPage(): void {
    if (this.inputPage >= 1 && this.inputPage <= this.totalPages) {
      this.page = this.inputPage;
      this.loadPage(this.page);
    }
  }
}
