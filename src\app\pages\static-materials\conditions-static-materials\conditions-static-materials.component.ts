import { Component, ElementRef, Input, OnInit, QueryList, SimpleChanges, ViewChildren } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { FormService } from 'src/app/services/form.service';

import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { MultiSelectDefault } from 'src/app/constants/app.constants';
import { constitutiveModel, waterSurface, hu } from 'src/app/constants/static-materials.constants';

import fn from 'src/app/utils/function.utils';

@Component({
  selector: 'app-conditions-static-materials',
  templateUrl: './conditions-static-materials.component.html',
  styleUrls: ['./conditions-static-materials.component.scss']
})
export class ConditionsStaticMaterialsComponent implements OnInit {
  @ViewChildren('mohrColoumbRef') mohrColoumbRef: QueryList<ElementRef>;
  @ViewChildren('undrainedRef') undrainedRef: QueryList<ElementRef>;
  @ViewChildren('infiniteStrengthRef')
  infiniteStrengthRef: QueryList<ElementRef>;
  @ViewChildren('shearNormalFunctionRef')
  shearNormalFunctionRef: QueryList<ElementRef>;
  @ViewChildren('hoekBrownRef') hoekBrownRef: QueryList<ElementRef>;
  @ViewChildren('generalizedHoekBrownRef')
  generalizedHoekBrownRef: QueryList<ElementRef>;
  @ViewChildren('verticalStressRatioRef')
  verticalStressRatioRef: QueryList<ElementRef>;
  @ViewChildren('shansepRef') shansepRef: QueryList<ElementRef>;

  @Input() public materialsList: any = [];
  @Input() public title: string = '';

  @Input() public data: any = null;
  @Input() public edit: boolean = false;
  @Input() public view: boolean = false;

  public formConditionStaticMaterials: FormGroup = new FormGroup({
    id: new FormControl(null),
    natural_specific_weight: new FormControl(null, [Validators.required]),
    is_saturated_specific_weight: new FormControl(false),
    saturated_specific_weight: new FormControl({ value: null, disabled: true }),
    color: new FormControl('#ffffff', [Validators.required]),

    constitutive_model: new FormControl([], [Validators.required]), //Modelo constitutivo
    water_surface: new FormControl([], [Validators.required]), // Parametros freaticos
    hu: new FormControl([], [Validators.required]), // Parametros freaticos
    custom_hu_value: new FormControl({ value: null, disabled: true }), // Parametros freaticos
    use_drained_resistance_over_water_surface: new FormControl(false) // Parametros freaticos
  });

  public showColorPicker: boolean = false;
  public selectedColor: string = '#ffffff';

  public dropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'name',
    unSelectAllText: 'Desmarcar seleção',
    searchPlaceholderText: 'Pesquisar...',
    itemsShowLimit: 10,
    allowSearchFilter: true,
    enableCheckAll: true,
    closeDropDownOnSelection: true,
    noFilteredDataAvailablePlaceholderText: 'Nenhum dado disponível.',
    noDataAvailablePlaceholderText: 'Nenhum dado disponível.'
  };

  public materialsListSettings = MultiSelectDefault.Single;

  public selectedConstitutiveModel = null;
  public selectedHu = null;

  public constitutiveModel = constitutiveModel;
  public waterSurface = waterSurface;
  public hu = hu;

  public dataElement: any = null;

  public func = fn;

  constructor(public formService: FormService) {}

  /**
   * Método de inicialização do componente. Se estiver em modo de visualização (`view` for `true`),
   * desabilita todos os campos do formulário `formConditionStaticMaterials` para evitar edições,
   * sem emitir eventos de alteração (`emitEvent: false`).
   */
  ngOnInit(): void {
    if (this.view) {
      this.formConditionStaticMaterials.disable({ emitEvent: false });
    }

    // Preencher "Superfície de água" com "Linha Freática" por padrão
    const waterSurfaceControl = this.formConditionStaticMaterials.get('water_surface');
    if (!this.edit && !this.view && waterSurfaceControl?.value?.length === 0) {
      const defaultOption = this.waterSurface.find((item) => item.name === 'Linha Freática');
      if (defaultOption) {
        waterSurfaceControl.setValue([defaultOption]);
      }
    }
  }

  /**
   * Detecta mudanças nos dados de entrada do componente. Quando `data` muda,
   * o método `splitData` é chamado para preencher o formulário.
   *
   * @param changes - Objeto contendo as alterações das propriedades do componente.
   */
  ngOnChanges(changes: SimpleChanges) {
    if (changes.data && changes.data.currentValue) {
      this.splitData(changes.data.currentValue);
    } else {
      this.splitData(null);
    }
  }

  /**
   * Evento disparado ao clicar fora de elementos interativos como o color picker.
   * Utilizado para ocultar o seletor de cor quando o usuário clica fora dele.
   *
   * @param element - Identificador do elemento onde ocorreu o clique externo.
   */
  onClickedOutside(element: string) {
    switch (element) {
      case 'colorPicker':
        this.showColorPicker = false;
        break;
    }
  }

  /**
   * Evento disparado ao concluir a seleção de uma cor no color picker.
   * Atualiza o valor do campo `color` no formulário com o valor selecionado.
   *
   * @param $event - Evento contendo a cor selecionada no formato hexadecimal.
   */
  changeComplete($event) {
    this.selectedColor = $event.color.hex;
    this.formConditionStaticMaterials.get('color').setValue(this.selectedColor);
  }

  /**
   * Manipula a seleção de itens específicos como modelo constitutivo ou HU (nível de água).
   * Atualiza as variáveis locais com base nas seleções feitas no formulário.
   *
   * @param item - Item selecionado (não utilizado diretamente neste contexto).
   * @param action - Ação executada (por padrão 'select').
   * @param subType - Tipo de item a ser tratado: 'constitutiveModel' ou 'hu'.
   */
  itemEvent(item: any, action: string = 'select', subType: string = '') {
    switch (subType) {
      case 'constitutiveModel':
        if (this.formConditionStaticMaterials.controls['constitutive_model'].value.length > 0) {
          this.selectedConstitutiveModel = this.formConditionStaticMaterials.controls['constitutive_model'].value[0].id;
        } else {
          this.selectedConstitutiveModel = null;
        }
        break;
      case 'hu':
        if (this.formConditionStaticMaterials.controls['hu'].value.length > 0) {
          this.selectedHu = this.formConditionStaticMaterials.controls['hu'].value[0].id;
        } else {
          this.selectedHu = null;
        }
        break;
      default:
        break;
    }
  }

  /**
   * Retorna a referência do componente filho correspondente ao modelo constitutivo selecionado.
   * Essa referência é utilizada para copiar dados entre componentes ou acessar seus formulários.
   *
   * @param element - Identificador do modelo constitutivo (como string ou número).
   * @returns A referência do componente correspondente ou `null` se não aplicável.
   */
  getConstitutiveModelElement(element: any) {
    let constitutiveModelElement = null;
    switch (element.toString()) {
      case '1': //mohrColoumbRef
        constitutiveModelElement = this.mohrColoumbRef.toArray()[0];
        break;
      case '2': //undrainedRef
        constitutiveModelElement = this.undrainedRef.toArray()[0];
        break;
      case '3': //noStrength
        break;
      case '4': //infiniteStrengthRef
        constitutiveModelElement = this.infiniteStrengthRef.toArray()[0];
        break;
      case '5': //shearNormalFunctionRef
        constitutiveModelElement = this.shearNormalFunctionRef.toArray()[0];
        break;
      case '6': //hoekBrownRef
        constitutiveModelElement = this.hoekBrownRef.toArray()[0];
        break;
      case '7': //generalizedHoekBrownRef
        constitutiveModelElement = this.generalizedHoekBrownRef.toArray()[0];
        break;
      case '8': //verticalStressRatioRef
        constitutiveModelElement = this.verticalStressRatioRef.toArray()[0];
        break;
      case '9': //shansepRef
        constitutiveModelElement = this.shansepRef.toArray()[0];
        break;
      default:
        constitutiveModelElement = null;
        break;
    }
    return constitutiveModelElement;
  }

  /**
   * Preenche o formulário `formConditionStaticMaterials` com os dados recebidos
   * e atualiza variáveis locais como `selectedColor` e `selectedConstitutiveModel`.
   * Também aplica validações e ativa/desativa campos conforme os dados.
   *
   * @param $dados - Objeto contendo os valores previamente salvos de uma condição estática.
   */
  splitData($dados) {
    if ($dados != null) {
      this.formConditionStaticMaterials.controls['color'].setValue($dados.color);
      this.selectedColor = $dados.color;

      this.formConditionStaticMaterials.controls['natural_specific_weight'].setValue($dados.natural_specific_weight);

      if ($dados.saturated_specific_weight != null) {
        this.formConditionStaticMaterials.controls['is_saturated_specific_weight'].setValue(true);
        this.formConditionStaticMaterials.controls['saturated_specific_weight'].setValue($dados.saturated_specific_weight);
        this.formService.checkboxControlValidate(this.formConditionStaticMaterials, 'saturated_specific_weight');
      }

      this.formConditionStaticMaterials.controls['constitutive_model'].setValue([
        fn.findIndexInArrayofObject(this.constitutiveModel, 'id', $dados.constitutive_model.toString(), 'name', true)
      ]);

      this.formConditionStaticMaterials.controls['water_surface'].setValue([
        fn.findIndexInArrayofObject(this.waterSurface, 'id', $dados.water_surface.toString(), 'name', true)
      ]);

      this.formConditionStaticMaterials.controls['hu'].setValue([fn.findIndexInArrayofObject(this.hu, 'id', $dados.hu.toString(), 'name', true)]);
      this.itemEvent(fn.findIndexInArrayofObject(this.hu, 'id', $dados.hu.toString(), 'name', true), 'select', 'hu');
      this.formService.dependencyControlValidate(this.formConditionStaticMaterials, 'hu', 'custom_hu_value', this.selectedHu == 1);
      this.formConditionStaticMaterials.controls['custom_hu_value'].setValue($dados.custom_hu_value);

      const isDrained = this.title === 'Condição Drenada';
      const useResistenceDrained = $dados.use_drained_resistance_over_water_surface ?? false;
      this.formConditionStaticMaterials.controls['use_drained_resistance_over_water_surface'].setValue(isDrained ? false : useResistenceDrained);

      this.formConditionStaticMaterials.controls['id'].setValue($dados.id);

      //Modelo Constitutivo:
      this.selectedConstitutiveModel = $dados.constitutive_model;
      this.dataElement = $dados;

      if (this.view) {
        this.formConditionStaticMaterials.disable({ emitEvent: false });
      }
    }
  }
}
