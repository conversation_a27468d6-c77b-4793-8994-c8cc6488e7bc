<ng-template #modalInstructions let-c="close" let-d="dismiss">
  <div class="modal-header">
    <h5 class="modal-title" id="modal-profile-title">
      <i class="fa fa-info-circle"></i>
      Instruções de preenchimento
    </h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="d('Cross click')"
    ></button>
  </div>
  <form>
    <div class="modal-body">
      <label
        >Preencha a planilha de acordo com o cabeçalho criado. Lembre-se de
        manter o formato “.csv” do arquivo!</label
      >
      <img src="assets/images/spreadsheetilustration.png" />
      <em>Importante: nunca altere a ordem das colunas!</em>
      <hr />
      <label
        >Preencha apenas um dos formatos de coordenadas para todos os
        instrumentos (UTM ou Geográficas).</label
      >
      <hr />
      <label
        >Para instrumentos com múltiplos pontos de medição, múltiplas células de
        pressão e múltiplos anéis magnéticos, manter o mesmo identificador e
        dados de cadastro do instrumento principal, alterar apenas a
        identificação e dados referentes aos próprios pontos de medição, células
        de pressão ou anéis magnéticos conforme exemplo. Nestes casos, a linha
        referente ao instrumento deve se repetir tantas vezes quantos pontos de
        medição, células de pressão ou anéis magnéticos existirem para ele.
      </label>
      <img src="assets/images/spreadsheetilustration-measuringpoint.png" />
    </div>
    <!-- Botões -->
    <div class="modal-footer">
      <app-button
        [class]="'btn-logisoil-green'"
        [label]="'Baixar arquivo-modelo'"
        [icon]="'fa fa-download'"
        [type]="false"
        (click)="downloadFile()"
      >
      </app-button>
      <app-button
        [class]="'btn-logisoil-red'"
        [label]="'Cancelar'"
        (click)="c('Close click')"
      >
      </app-button>
    </div>
  </form>
</ng-template>
