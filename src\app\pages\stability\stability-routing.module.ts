import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Rotas } from 'src/app/constants/rotas.constants';

import { ListStabilityComponent } from './list-stability/list-stability.component';
import { MapsStabilityComponent } from './maps-stability/maps-stability.component';
import { AnalysisStabilityComponent } from './analysis-stability/analysis-stability.component';
import { ChartStabilityComponent } from './chart-stability/chart-stability.component';
import { ListSimulationsComponent } from './simulations/list-simulations/list-simulations.component';
import { RegisterSimulationsComponent } from './simulations/register-simulations/register-simulations.component';
import { ResultSimulationsComponent } from './simulations/result-simulations/result-simulations.component';

import { AppGuard } from '../../guards/app.guard';

const routes: Routes = [
  {
    path: '',
    component: ListStabilityComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.MapaEstabilidade,
    component: MapsStabilityComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.FatoresDeSeguranca,
    component: AnalysisStabilityComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.GraficosEstabilidade,
    component: ChartStabilityComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.Simulador,
    component: ListSimulationsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.CriarSimulador,
    component: RegisterSimulationsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.EditarSimulacao,
    component: RegisterSimulationsComponent,
    canActivate: [AppGuard]
  },
  {
    path: Rotas.ResultadoSimulacao,
    component: ResultSimulationsComponent,
    canActivate: [AppGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StabilityRoutingModule {}
