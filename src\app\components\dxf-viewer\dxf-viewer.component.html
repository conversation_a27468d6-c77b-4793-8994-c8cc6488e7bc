<div class="list-content" [hidden]="!show">
  <div class="container-fluid border">
    <div class="row d-flex">
      <div class="canvasContainer col" [id]="idCanvas"></div>

      <div class="root-layers col-auto border">
        <div
          class="d-flex py-1 layer-item"
          *ngFor="let layer of layers"
          :key="layer.name"
          tag="label"
        >
          <input
            class="mx-2 form-check-input"
            type="checkbox"
            checked
            (input)="_ToggleLayer(layer, $event)"
          />
          <div class="me-2">
            <i
              [ngClass]="_GetIconClass(layer.name)"
              [style.color]="_GetCssColor(layer.color)"
            ></i>
          </div>
          <div>
            <div>
              <span [innerHTML]="layer.displayName"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
