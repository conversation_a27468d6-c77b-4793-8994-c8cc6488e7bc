import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class SharedService {
  public groupInstrumentsList: any = [];
  public structure: any = null;
  public clientUnit: any = null;
  public client: any = null;

  public subject = new Subject<any>();

  sendClickEvent(params) {
    this.subject.next(params);
  }

  getClickEvent(): Observable<any> {
    return this.subject.asObservable();
  }

  clear() {
    this.groupInstrumentsList = [];
    this.structure = null;
    this.clientUnit = null;
    this.client = null;
  }
}
